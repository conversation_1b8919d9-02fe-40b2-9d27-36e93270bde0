// نموذج الإعلان الشامل
class Ad {
  final String id;
  final String title;
  final String description;
  final String category;
  final String location;
  final String? price;
  final String? fileUrl;
  final DateTime createdAt;
  final bool isFeatured;
  int likes;
  bool isLiked;

  Ad({
    required this.id,
    required this.title,
    required this.description,
    required this.category,
    required this.location,
    this.price,
    this.fileUrl,
    required this.createdAt,
    this.isFeatured = false,
    this.likes = 0,
    this.isLiked = false,
  });

  // إنشاء نموذج من JSON
  factory Ad.fromJson(Map<String, dynamic> json) {
    return Ad(
      id: json['_id'] ?? json['id'] ?? '',
      title: json['title'] ?? 'بدون عنوان',
      description: json['description'] ?? 'بدون وصف',
      category: json['category'] ?? 'غير محدد',
      location: json['location'] ?? 'غير محدد',
      price: json['price']?.toString(),
      fileUrl: json['fileUrl'],
      createdAt: json['date'] != null 
          ? DateTime.parse(json['date'])
          : json['createdAt'] != null
              ? DateTime.parse(json['createdAt'])
              : DateTime.now(),
      isFeatured: json['isFeatured'] ?? false,
      likes: json['likes'] ?? 0,
      isLiked: json['isLiked'] ?? false,
    );
  }

  // تحويل النموذج إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'category': category,
      'location': location,
      'price': price,
      'fileUrl': fileUrl,
      'createdAt': createdAt.toIso8601String(),
      'isFeatured': isFeatured,
      'likes': likes,
      'isLiked': isLiked,
    };
  }
  
  // نسخ النموذج مع تحديث بعض الخصائص
  Ad copyWith({
    String? id,
    String? title,
    String? description,
    String? category,
    String? location,
    String? price,
    String? fileUrl,
    DateTime? createdAt,
    bool? isFeatured,
    int? likes,
    bool? isLiked,
  }) {
    return Ad(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      category: category ?? this.category,
      location: location ?? this.location,
      price: price ?? this.price,
      fileUrl: fileUrl ?? this.fileUrl,
      createdAt: createdAt ?? this.createdAt,
      isFeatured: isFeatured ?? this.isFeatured,
      likes: likes ?? this.likes,
      isLiked: isLiked ?? this.isLiked,
    );
  }
}
