# Dependency directories
node_modules/
npm-debug.log
yarn-error.log

# Environment variables
.env
.env.*
!.env.example

# Build output
/dist
/build
/coverage

# Debug logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# IDE and editor files
.idea/
.vscode/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Testing
/coverage
.nyc_output

# Misc
*.pem
*.p12
*.key
*.crt
*.cert
*.cer
*.p7b
*.p7c
*.p7s
*.crl
*.crl.srl
*.srl
*.srl.key
*.sig
*.csr
*.csr.pem
*.csr.der
*.csr.der.pem
*.key
*.key.pem
*.key.der
*.key.der.pem
*.p12
*.pfx
*.p12.pem
*.pfx.pem
*.p12.der
*.pfx.der
*.p12.der.pem
*.pfx.der.pem
*.p7b
*.p7c
*.p7s
*.p7m
*.spc
*.p7r
*.p7s.p7m
*.p7s.p7c
*.p7s.p7b
*.p7s.p7s
*.p7s.p7r

# Git
.git
.gitignore
.gitattributes
.github/
.gitlab/
.gitlab-ci.yml

# Documentation
/docs
/apidoc

# Local development
.local-*
*.local

# Logs
logs
*.log

# Environment files (except example)
!.env.example
