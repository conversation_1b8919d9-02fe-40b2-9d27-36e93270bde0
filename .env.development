# إعدادات التطبيق
NODE_ENV=development
PORT=3000
HOST=0.0.0.0

# إعدادات قاعدة البيانات
MONGODB_URI=mongodb://localhost:27017/fulk_dev

# إعدادات JWT
JWT_SECRET=dev_jwt_secret_key_replace_in_production
JWT_EXPIRES_IN=30d
JWT_COOKIE_EXPIRES_IN=30

# إعدادات CORS
CLIENT_URL=http://localhost:3000
FRONTEND_URL=http://localhost
ALLOWED_ORIGINS=http://localhost:3000,http://localhost

# إعدادات Supabase-
SUPABASE_URL=https://gfgdgbjkorzpjewrjnhc.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdmZ2RnYmprb3J6cGpld3JqbmhjIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcxNTk2ODQsImV4cCI6MjA2MjczNTY4NH0.Rr2y0rzdghPnHp0QAD-edhJLZ9EXHRKWL9wtLiCIHZs

# إعدادات Firebase (اختياري)
FIREBASE_API_KEY=your_dev_firebase_api_key
FIREBASE_AUTH_DOMAIN=your_dev_firebase_auth_domain
FIREBASE_PROJECT_ID=your_dev_firebase_project_id
FIREBASE_STORAGE_BUCKET=your_dev_firebase_storage_bucket
FIREBASE_MESSAGING_SENDER_ID=your_dev_firebase_messaging_sender_id
FIREBASE_APP_ID=your_dev_firebase_app_id

# إعدادات التخزين (اختياري)
AWS_ACCESS_KEY_ID=your_dev_aws_access_key_id
AWS_SECRET_ACCESS_KEY=your_dev_aws_secret_access_key
AWS_REGION=your_dev_aws_region
AWS_BUCKET_NAME=your_dev_aws_bucket_name

# إعدادات البريد الإلكتروني (اختياري)
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_EMAIL=<EMAIL>
SMTP_PASSWORD=your_dev_email_password
FROM_EMAIL=<EMAIL>
FROM_NAME=Fulk App (Dev)
