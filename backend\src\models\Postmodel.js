const mongoose = require('mongoose');

const PostSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  content: {
    type: String,
    required: [true, 'الرجاء إدخال محتوى المنشور'],
    maxlength: [1000, 'المحتوى لا يمكن أن يتجاوز 1000 حرف']
  },
  mediaUrl: {
    type: String
  },
  mediaType: {
    type: String,
    enum: ['image', 'video', null],
    default: null
  },
  likes: {
    type: Number,
    default: 0
  },
  loves: {
    type: Number,
    default: 0
  },
  haha: {
    type: Number,
    default: 0
  },
  comments: [
    {
      username: {
        type: String,
        required: true
      },
      content: {
        type: String,
        required: true
      },
      date: {
        type: Date,
        default: Date.now
      }
    }
  ],
  createdAt: {
    type: Date,
    default: Date.now
  },
  promoted: {
    type: Boolean,
    default: false
  },
  privacy: {
    type: String,
    enum: ['public', 'private', 'friends'],
    default: 'public'
  },
  latitude: {
    type: Number,
    default: null
  },
  longitude: {
    type: Number,
    default: null
  },
  hashtags: [{ type: String }],
}, {
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// الحصول على معلومات المؤلف
PostSchema.virtual('author', {
  ref: 'User',
  localField: 'userId',
  foreignField: '_id',
  justOne: true
});

module.exports = mongoose.model('Post', PostSchema);
