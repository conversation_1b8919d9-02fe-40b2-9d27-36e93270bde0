import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:animate_do/animate_do.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:http/http.dart' as http;
import 'package:image_picker/image_picker.dart';
import 'package:provider/provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:timeago/timeago.dart' as timeago;
import 'appstate.dart';
import 'repositories/city_repository.dart';

enum PostPrivacy {
  public('عام'),
  friends('الأصدقاء فقط'),
  specificFriends('أصدقاء محددين'),
  onlyMe('أنا فقط');

  final String label;
  const PostPrivacy(this.label);

  static PostPrivacy fromString(String value) {
    return PostPrivacy.values.firstWhere(
      (e) => e.name == value,
      orElse: () => PostPrivacy.public,
    );
  }
}

// تهيئة Timeago للغة العربية
void setupTimeago() => timeago.setLocaleMessages('ar', timeago.ArMessages());

// نماذج البيانات
class City {
  final String id;
  final String name;
  final String image;
  final List<Village> villages;

  City({
    required this.id,
    required this.name,
    required this.image,
    required this.villages,
  });

  factory City.fromJson(Map<String, dynamic> json) {
    return City(
      id: json['_id'] ?? '',
      name: json['name'] ?? 'بدون اسم',
      image: json['image'] ?? '',
      villages: (json['villages'] as List?)
          ?.map((v) => Village.fromJson(v))
          .toList() ??
          [],
    );
  }
}

class Village {
  final String name;
  final String image;
  final IconData icon;

  Village({required this.name, required this.image, required this.icon});

  factory Village.fromJson(Map<String, dynamic> json) {
    return Village(
      name: json['name'] ?? 'بدون اسم',
      image: json['image'] ?? '',
      icon: _iconFromString(json['icon']),
    );
  }

  static IconData _iconFromString(String? iconName) {
    return Icons.location_city; // قيمة افتراضية
  }
}

class Post {
  final String id;
  final String city;
  final String? village;
  final String user;
  final String content;
  final String? image;
  int likes;
  final List<String> comments;
  final int views;
  final DateTime createdAt;
  bool isLiked;
  final PostPrivacy privacy;
  final List<String>? visibleToUserIds;

  Post({
    required this.id,
    required this.city,
    this.village,
    required this.user,
    required this.content,
    this.image,
    required this.likes,
    required this.isLiked,
    required this.comments,
    required this.views,
    required this.createdAt,
    this.privacy = PostPrivacy.public,
    this.visibleToUserIds,
  });

  factory Post.fromJson(Map<String, dynamic> json) {
    return Post(
      id: json['_id'] ?? '',
      city: json['city'] ?? 'غير محدد',
      village: json['village'],
      user: json['user'] ?? 'مجهول',
      content: json['content'] ?? '',
      image: json['image'],
      likes: json['likes'] ?? 0,
      isLiked: json['isLiked'] ?? false,
      comments: List<String>.from(json['comments'] ?? []),
      views: json['views'] ?? 0,
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toString()),
      privacy: PostPrivacy.fromString(json['privacy'] ?? 'public'),
      visibleToUserIds: json['visibleToUserIds'] != null 
          ? List<String>.from(json['visibleToUserIds']) 
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'city': city,
      'village': village,
      'user': user,
      'content': content,
      'image': image,
      'likes': likes,
      'isLiked': isLiked,
      'comments': comments,
      'views': views,
      'createdAt': createdAt.toIso8601String(),
      'privacy': privacy.name,
      'visibleToUserIds': visibleToUserIds,
    };
  }
}

// خدمات البيانات
// قائمة المدن والقرى المصرية
final List<City> egyptCities = [
  City(
    id: '1',
    name: 'القاهرة',
    image: '',
    villages: [
      Village(name: 'المعادي', image: '', icon: Icons.location_city),
      Village(name: 'حلوان', image: '', icon: Icons.location_city),
      Village(name: 'شبرا', image: '', icon: Icons.location_city),
    ],
  ),
  City(
    id: '2',
    name: 'الجيزة',
    image: '',
    villages: [
      Village(name: 'العياط', image: '', icon: Icons.location_city),
      Village(name: 'البدرشين', image: '', icon: Icons.location_city),
      Village(name: 'أوسيم', image: '', icon: Icons.location_city),
    ],
  ),
  City(
    id: '3',
    name: 'الإسكندرية',
    image: '',
    villages: [
      Village(name: 'برج العرب', image: '', icon: Icons.location_city),
      Village(name: 'العجمي', image: '', icon: Icons.location_city),
      Village(name: 'سيدي بشر', image: '', icon: Icons.location_city),
    ],
  ),
  City(
    id: '4',
    name: 'المنصورة',
    image: '',
    villages: [
      Village(name: 'ميت غمر', image: '', icon: Icons.location_city),
      Village(name: 'طلخا', image: '', icon: Icons.location_city),
      Village(name: 'السنبلاوين', image: '', icon: Icons.location_city),
    ],
  ),
  City(
    id: '5',
    name: 'أسيوط',
    image: '',
    villages: [
      Village(name: 'ديروط', image: '', icon: Icons.location_city),
      Village(name: 'منفلوط', image: '', icon: Icons.location_city),
      Village(name: 'القوصية', image: '', icon: Icons.location_city),
    ],
  ),
  // أضف المزيد من المدن والقرى حسب الحاجة
];

class CityService {
  final String baseUrl;

  CityService() : baseUrl = AppState.getBackendUrl();

  Future<List<City>> fetchCities(String token) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/cities'),
        headers: {'x-auth-token': token},
      );
      if (response.statusCode == 200) {
        return (jsonDecode(response.body) as List)
            .map((json) => City.fromJson(json))
            .toList();
      }
      throw Exception('فشل في جلب المدن: ${response.body}');
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }
}

class PostService {
  final String baseUrl;

  PostService() : baseUrl = AppState.getBackendUrl();

  Future<Map<String, dynamic>> fetchPosts(
      String token, String city, String? village, int page, int limit, {String? currentUserId}) async {
    try {
      // Build the base URL with common parameters
      final params = {
        'city': city,
        'page': page.toString(),
        'limit': limit.toString(),
      };
      
      // Add village parameter if provided
      if (village != null) {
        params['village'] = village;
      }
      
      // Add current user ID for privacy filtering on the server side
      if (currentUserId != null) {
        params['currentUserId'] = currentUserId;
      }
      
      // Build the URI with parameters
      final uri = Uri.parse('$baseUrl/social-posts').replace(
        queryParameters: params,
      );
      
      final response = await http.get(uri, headers: {'x-auth-token': token});
      
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return {
          'posts': (data['posts'] as List)
              .map((json) => Post.fromJson(json))
              .toList(),
          'total': data['total'] as int,
        };
      }
      throw Exception('فشل في جلب المنشورات: ${response.body}');
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }

  Future<void> addPost(
    String token, 
    String city, 
    String? village,
    String content, 
    File? image, {
    PostPrivacy privacy = PostPrivacy.public,
    List<String>? visibleToUserIds,
  }) async {
    try {
      var request =
      http.MultipartRequest('POST', Uri.parse('$baseUrl/social-posts'));
      request.headers['x-auth-token'] = token;
      request.fields['city'] = city;
      if (village != null) request.fields['village'] = village;
      request.fields['userId'] = 'user1'; // TODO: Replace with actual user ID
      request.fields['content'] = content;
      request.fields['privacy'] = privacy.name;
      if (visibleToUserIds != null && visibleToUserIds.isNotEmpty) {
        request.fields['visibleToUserIds'] = jsonEncode(visibleToUserIds);
      }
      if (image != null) {
        if (await image.length() > 5 * 1024 * 1024) {
          throw Exception(
              'حجم الصورة كبير جدًا (يجب أن يكون أقل من 5 ميجابايت)');
        }
        request.files
            .add(await http.MultipartFile.fromPath('image', image.path));
      }
      final response = await request.send();
      if (response.statusCode != 201) {
        throw Exception('فشل في نشر المنشور: ${response.reasonPhrase}');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }

  Future<void> likePost(String token, String postId, bool isLiked) async {
    try {
      final response = await http.put(
        Uri.parse('$baseUrl/social-posts/$postId/like'),
        headers: {'x-auth-token': token, 'Content-Type': 'application/json'},
        body: jsonEncode({'isLiked': isLiked}),
      );
      if (response.statusCode != 200) {
        throw Exception('فشل في تحديث الإعجاب: ${response.body}');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }

  Future<void> commentPost(String token, String postId, String comment) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/social-posts/$postId/comment'),
        headers: {'Content-Type': 'application/json', 'x-auth-token': token},
        body: jsonEncode({'comment': comment}),
      );
      if (response.statusCode != 200) {
        throw Exception('فشل في إضافة التعليق: ${response.body}');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }

  Future<List<Map<String, dynamic>>> fetchNotifications(String token) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/notifications'),
        headers: {'x-auth-token': token},
      );
      if (response.statusCode == 200) {
        return List<Map<String, dynamic>>.from(jsonDecode(response.body));
      }
      throw Exception('فشل في جلب الإشعارات: ${response.body}');
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }

  Future<void> updateNotification(String token, String notificationId) async {
    try {
      final response = await http.put(
        Uri.parse('$baseUrl/notifications/$notificationId'),
        headers: {'x-auth-token': token},
      );
      if (response.statusCode != 200) {
        throw Exception('فشل في تحديث الإشعار: ${response.body}');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }
}

class Rayan11 extends StatefulWidget {
  const Rayan11({super.key});

  @override
  _Rayan11State createState() => _Rayan11State();
}

class _Rayan11State extends State<Rayan11> with SingleTickerProviderStateMixin {
  final CityService _cityService = CityService();
  late AnimationController _fabAnimationController;
  late Animation<double> _fabAnimation;
  final TextEditingController _searchController = TextEditingController();
  bool _isLoading = true;
  List<City> cities = [];
  List<Map<String, dynamic>> _notifications = [];

  // Mock search suggestions
  final List<String> _searchSuggestions = [
    'القاهرة',
    'الجيزة',
    'الإسكندرية',
    'المنصورة',
    'أسيوط',
  ];

  @override
  void initState() {
    super.initState();
    setupTimeago();
    _fabAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    _fabAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fabAnimationController,
      curve: Curves.easeOut,
    ));
    _fabAnimationController.forward();
    _fetchCities();
    _fetchNotifications();
    _searchController.addListener(_onSearchChanged);

    final appState = Provider.of<AppState>(context, listen: false);
    if (appState.socket != null) {
      appState.socket!.on('new_post', (data) {
        _fetchCities();
        setState(() {
          _notifications.add({
            'message': 'منشور جديد في ${data['city']}: ${data['content']}',
            'date': DateTime.now().toString(),
            'read': false,
            '_id': data['notificationId'] ?? DateTime.now().toString(),
          });
        });
      });
    }
  }

  Future<void> _fetchCities() async {
    setState(() => _isLoading = true);
    
    // استخدام CityRepository بدلاً من الاتصال المباشر بالخادم
    final cityRepo = CityRepository();
    final response = await cityRepo.getAll();
    
    if (response.isSuccess && response.data != null) {
      setState(() {
        cities = response.data!;
        _isLoading = false;
      });
    } else {
      // في حالة حدوث خطأ، يعرض رسالة للمستخدم
      // ملاحظة: CityRepository يستخدم البيانات المحلية تلقائياً في حالة فشل الاتصال
      if (response.isNoInternet) {
        _showSnackBar('لا يوجد اتصال بالإنترنت. تم استخدام البيانات المحلية.', Colors.orange);
      } else if (response.isError) {
        _showSnackBar('خطأ: ${response.message}. تم استخدام البيانات المحلية.', Colors.orange);
      }
      setState(() => _isLoading = false);
    }
  }

  Future<void> _fetchNotifications() async {
    final appState = Provider.of<AppState>(context, listen: false);
    try {
      _notifications =
      await PostService().fetchNotifications(appState.token ?? '');
      setState(() {});
    } catch (e) {
      _showSnackBar('خطأ في جلب الإشعارات: $e', Colors.red);
    }
  }

  void _onSearchChanged() {
    _debounce(() => setState(() {}));
  }

  Timer? _debounceTimer;
  void _debounce(VoidCallback callback) {
    const duration = Duration(milliseconds: 300);
    _debounceTimer?.cancel();
    _debounceTimer = Timer(duration, callback);
  }

  @override
  void dispose() {
    _fabAnimationController.dispose();
    _searchController.dispose();
    _debounceTimer?.cancel();
    super.dispose();
  }

  void _showSnackBar(String message,
      [Color? backgroundColor, SnackBarAction? action]) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: backgroundColor ?? Colors.teal,
        behavior: SnackBarBehavior.floating,
        action: action,
        elevation: 6,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final filteredCities = cities
        .where((city) => city.name
        .toLowerCase()
        .contains(_searchController.text.toLowerCase()))
        .toList();

    return Scaffold(
      body: _isLoading
          ? _buildSkeletonLoader()
          : RefreshIndicator(
        onRefresh: () async {
          await _fetchCities();
          await _fetchNotifications();
        },
        color: Colors.teal,
        child: CustomScrollView(
          slivers: [
            _buildSliverAppBar(isDarkMode),
            SliverToBoxAdapter(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: FadeInDown(
                  child: Text(
                    "المدن المصرية",
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: isDarkMode ? Colors.teal[300] : Colors.teal,
                    ),
                  ),
                ),
              ),
            ),
            SliverList(
              delegate: SliverChildBuilderDelegate(
                    (context, index) => FadeInUp(
                  child: _buildCityCard(context, filteredCities[index]),
                ),
                childCount: filteredCities.length,
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: ScaleTransition(
        scale: _fabAnimation,
        child: FloatingActionButton(
          onPressed: () => _showCreateCityDialog(context),
          backgroundColor: Colors.teal,
          tooltip: 'إضافة مدينة',
          elevation: 8,
          child: const Icon(Icons.add_location, size: 30, color: Colors.white),
        ),
      ),
    );
  }

  Widget _buildSkeletonLoader() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return CustomScrollView(
      slivers: [
        SliverAppBar(
          expandedHeight: 120,
          flexibleSpace: FlexibleSpaceBar(
            background: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: isDarkMode
                      ? [Colors.teal[900]!, Colors.teal[700]!]
                      : [Colors.teal, Colors.tealAccent],
                ),
              ),
              child: SafeArea(
                child: Padding(
                  padding:
                  const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Container(
                    height: 40,
                    decoration: BoxDecoration(
                      color: isDarkMode ? Colors.grey[800] : Colors.grey[300],
                      borderRadius: BorderRadius.circular(30),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
        SliverList(
          delegate: SliverChildBuilderDelegate(
                (context, index) => Pulse(
              child: Card(
                margin: const EdgeInsets.all(16),
                child: Padding(
                  padding: const EdgeInsets.all(12.0),
                  child: Column(
                    children: [
                      Container(
                          height: 20, width: 100, color: Colors.grey[300]),
                      const SizedBox(height: 10),
                      Container(height: 100, color: Colors.grey[300]),
                    ],
                  ),
                ),
              ),
            ),
            childCount: 5,
          ),
        ),
      ],
    );
  }

  SliverAppBar _buildSliverAppBar(bool isDarkMode) {
    return SliverAppBar(
      expandedHeight: 120,
      flexibleSpace: FlexibleSpaceBar(
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: isDarkMode
                  ? [Colors.teal[900]!, Colors.teal[700]!]
                  : [Colors.teal, Colors.tealAccent],
            ),
          ),
          child: SafeArea(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Column(
                children: [
                  TextField(
                    controller: _searchController,
                    decoration: InputDecoration(
                      hintText: 'بحث عن مدينة...',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(30),
                        borderSide: BorderSide.none,
                      ),
                      hintStyle:
                      TextStyle(color: isDarkMode ? Colors.white70 : Colors.white),
                      prefixIcon:
                      Icon(Icons.search, color: isDarkMode ? Colors.white : Colors.white),
                      suffixIcon: _searchController.text.isNotEmpty
                          ? IconButton(
                        icon: Icon(Icons.clear, color: isDarkMode ? Colors.white : Colors.white),
                        onPressed: () {
                          _searchController.clear();
                          setState(() {});
                        },
                      )
                          : null,
                      filled: true,
                      fillColor: isDarkMode ? Colors.white12 : Colors.white24,
                    ),
                    style: TextStyle(color: isDarkMode ? Colors.white : Colors.white),
                    onChanged: (value) {
                      if (value.isNotEmpty) {
                        _showSearchSuggestions(context, value);
                      }
                    },
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
      actions: [
        Stack(
          children: [
            IconButton(
              icon: const Icon(Icons.notifications, color: Colors.white),
              onPressed: () => _showNotifications(context),
            ),
            if (_notifications.isNotEmpty)
              Positioned(
                right: 8,
                top: 8,
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 300),
                  padding: const EdgeInsets.all(4),
                  decoration: const BoxDecoration(
                      color: Colors.red, shape: BoxShape.circle),
                  constraints: const BoxConstraints(minWidth: 16, minHeight: 16),
                  child: Text(
                    '${_notifications.length}',
                    style: const TextStyle(color: Colors.white, fontSize: 10),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
          ],
        ),
      ],
      pinned: true,
    );
  }

  void _showSearchSuggestions(BuildContext context, String query) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        height: 200,
        padding: const EdgeInsets.all(16),
        child: ListView(
          children: _searchSuggestions
              .where((suggestion) =>
              suggestion.toLowerCase().contains(query.toLowerCase()))
              .map((suggestion) => ListTile(
            title: Text(suggestion),
            onTap: () {
              _searchController.text = suggestion;
              setState(() {});
              Navigator.pop(context);
            },
          ))
              .toList(),
        ),
      ),
    );
  }

  Widget _buildCityCard(BuildContext context, City city) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return GestureDetector(
      onTap: () => Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => PostsPage(cityName: city.name),
        ),
      ),
      child: Card(
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        elevation: 6,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            image: DecorationImage(
              image: CachedNetworkImageProvider(
                city.image.isNotEmpty
                    ? '$_cityService.baseUrl${city.image}'
                    : 'https://via.placeholder.com/400x200',
              ),
              fit: BoxFit.cover,
              colorFilter: ColorFilter.mode(
                Colors.black.withOpacity(0.4),
                BlendMode.dstATop,
              ),
            ),
          ),
          padding: const EdgeInsets.all(12.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      const Icon(Icons.location_city,
                          color: Colors.white, size: 28),
                      const SizedBox(width: 8),
                      Text(
                        city.name,
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ],
                  ),
                  IconButton(
                    icon: const Icon(Icons.favorite_border, color: Colors.white),
                    onPressed: () => _addToFavorites(city),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              SizedBox(
                height: 120,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  itemCount: city.villages.length,
                  itemBuilder: (context, villageIndex) => FadeInRight(
                    child: _buildVillageCard(context, city.villages[villageIndex]),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildVillageCard(BuildContext context, Village village) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return GestureDetector(
      onTap: () => Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => PostsPage(
            cityName: _getCityName(village.name),
            villageName: village.name,
          ),
        ),
      ),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        width: 120,
        margin: const EdgeInsets.symmetric(horizontal: 4),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          image: DecorationImage(
            image: CachedNetworkImageProvider(
              village.image.isNotEmpty
                  ? '$_cityService.baseUrl${village.image}'
                  : 'https://via.placeholder.com/120x100',
            ),
            fit: BoxFit.cover,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(village.icon, color: Colors.white, size: 24),
            const SizedBox(height: 4),
            Text(
              village.name,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  String _getCityName(String villageName) {
    return cities
        .firstWhere(
          (city) => city.villages.any((v) => v.name == villageName),
      orElse: () =>
          City(id: '', name: 'غير محدد', image: '', villages: []),
    )
        .name;
  }

  void _showNotifications(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: isDarkMode ? Colors.grey[900] : Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.5,
        minChildSize: 0.3,
        maxChildSize: 0.8,
        builder: (context, scrollController) => Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Text(
                'الإشعارات',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: isDarkMode ? Colors.teal[300] : Colors.teal,
                ),
              ),
              Expanded(
                child: _notifications.isEmpty
                    ? Center(
                  child: Text(
                    'لا توجد إشعارات',
                    style: TextStyle(
                      color: isDarkMode ? Colors.grey[400] : Colors.grey,
                    ),
                  ),
                )
                    : ListView.builder(
                  controller: scrollController,
                  itemCount: _notifications.length,
                  itemBuilder: (context, index) => Dismissible(
                    key: Key(_notifications[index]['_id']),
                    background: Container(
                      color: Colors.red,
                      alignment: Alignment.centerRight,
                      padding: const EdgeInsets.only(right: 16),
                      child: const Icon(Icons.delete, color: Colors.white),
                    ),
                    onDismissed: (direction) async {
                      final notificationId = _notifications[index]['_id'];
                      final appState =
                      Provider.of<AppState>(context, listen: false);
                      try {
                        await PostService()
                            .updateNotification(appState.token ?? '', notificationId);
                        setState(() {
                          _notifications.removeAt(index);
                        });
                      } catch (e) {
                        _showSnackBar('خطأ في حذف الإشعار: $e', Colors.red);
                      }
                    },
                    child: ListTile(
                      leading: const Icon(Icons.notifications_active,
                          color: Colors.teal),
                      title: Text(
                        _notifications[index]['message'] ?? 'غير معروف',
                        style: TextStyle(
                          color: isDarkMode ? Colors.white : Colors.black87,
                        ),
                      ),
                      subtitle: Text(
                        timeago.format(
                          DateTime.parse(
                              _notifications[index]['date'] ??
                                  DateTime.now().toString()),
                          locale: 'ar',
                        ),
                        style: TextStyle(
                          color: isDarkMode ? Colors.grey[400] : Colors.grey,
                        ),
                      ),
                      trailing: _notifications[index]['read']
                          ? null
                          : const Icon(Icons.circle,
                          color: Colors.teal, size: 10),
                      onTap: () async {
                        if (!_notifications[index]['read']) {
                          final appState =
                          Provider.of<AppState>(context, listen: false);
                          try {
                            await PostService().updateNotification(
                                appState.token ?? '',
                                _notifications[index]['_id']);
                            setState(
                                    () => _notifications[index]['read'] = true);
                          } catch (e) {
                            _showSnackBar(
                                'خطأ في تحديث الإشعار: $e', Colors.red);
                          }
                        }
                      },
                    ),
                  ),
                ),
              ),
              ElevatedButton(
                onPressed: () {
                  setState(() => _notifications.clear());
                  Navigator.pop(context);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10)),
                ),
                child: const Text(
                  'مسح الكل',
                  style: TextStyle(color: Colors.white),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _addToFavorites(City city) {
    _showSnackBar(
      'تمت إضافة ${city.name} إلى المفضلة',
      Colors.green,
      SnackBarAction(
        label: 'عرض',
        textColor: Colors.white,
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => PostsPage(cityName: city.name),
            ),
          );
        },
      ),
    );
  }

  void _showCreateCityDialog(BuildContext context) {
    final appState = Provider.of<AppState>(context, listen: false);
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    if (appState.userRole != 'hokama') {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('عذراً، هذه الميزة متاحة فقط للحكماء'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }
  
  // متغيرات للنموذج
  final cityNameController = TextEditingController();
  final villageNameController = TextEditingController();
  final List<String> villageNames = [];
  
  showDialog(
    context: context,
    builder: (context) => StatefulBuilder(
      builder: (context, setState) => AlertDialog(
        title: const Text('إضافة مدينة/قرية جديدة'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: cityNameController,
                decoration: const InputDecoration(
                  labelText: 'اسم المدينة',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 16),
              const Text('إضافة قرى (اختياري)'),
              const SizedBox(height: 8),
              Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: villageNameController,
                      decoration: const InputDecoration(
                        labelText: 'اسم القرية',
                        border: OutlineInputBorder(),
                      ),
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.add),
                    onPressed: () {
                      if (villageNameController.text.isNotEmpty) {
                        setState(() {
                          villageNames.add(villageNameController.text);
                          villageNameController.clear();
                        });
                      }
                    },
                  ),
                ],
              ),
              const SizedBox(height: 8),
              if (villageNames.isNotEmpty) ...[  
                const Text('القرى المضافة:'),
                const SizedBox(height: 4),
                Container(
                  height: 100,
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: ListView.builder(
                    shrinkWrap: true,
                    itemCount: villageNames.length,
                    itemBuilder: (context, index) => ListTile(
                      dense: true,
                      title: Text(villageNames[index]),
                      trailing: IconButton(
                        icon: const Icon(Icons.delete, size: 18),
                        onPressed: () {
                          setState(() {
                            villageNames.removeAt(index);
                          });
                        },
                      ),
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'إلغاء',
              style: TextStyle(
                color: isDarkMode ? Colors.white70 : Colors.teal,
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              if (cityNameController.text.isEmpty) {
                _showSnackBar('يرجى إدخال اسم المدينة', Colors.orange);
                return;
              }
              
              // هنا يمكن إضافة كود لإرسال البيانات إلى الخادم
              // مثال:
              // _cityRepository.addCity(...);
              
              Navigator.pop(context);
              _showSnackBar('تم إضافة المدينة بنجاح', Colors.green);
              
              // إعادة تحميل البيانات
              _fetchCities();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.teal,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
            ),
            child: const Text(
              'إضافة',
              style: TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
    ),
  );
  }
}

class PostsPage extends StatefulWidget {
  final String cityName;
  final String? villageName;

  const PostsPage({super.key, required this.cityName, this.villageName});

  @override
  _PostsPageState createState() => _PostsPageState();
}

class _PostsPageState extends State<PostsPage> with SingleTickerProviderStateMixin {
  final PostService _postService = PostService();
  final ScrollController _scrollController = ScrollController();
  final List<String> _ads = [
    '${AppState.getBackendUrl()}/uploads/social-ads/ad1.jpg',
    '${AppState.getBackendUrl()}/uploads/social-ads/ad2.jpg',
    '${AppState.getBackendUrl()}/uploads/social-ads/ad3.jpg',
  ];
  late PageController _pageController;
  int _currentAdIndex = 0;
  List<Post> _posts = [];
  int _page = 1;
  int _totalPosts = 0;
  final int _limit = 10;
  bool _isLoading = true;
  bool _isLoadingMore = false;
  bool _isGridView = false;
  Timer? _timer;
  late AnimationController _fabAnimationController;
  late Animation<double> _fabAnimation;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    _scrollController.addListener(_scrollListener);
    _fetchPosts();
    _startAutoScroll();

    // Initialize FAB animation
    _fabAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 2),
    )..repeat(reverse: true);
    _fabAnimation = Tween<double>(begin: 1.0, end: 1.2).animate(
      CurvedAnimation(
        parent: _fabAnimationController,
        curve: Curves.easeInOut,
      ),
    );
  }

  void _startAutoScroll() {
    _timer = Timer.periodic(const Duration(seconds: 5), (Timer timer) {
      if (!mounted) return;
      setState(() {
        _currentAdIndex =
        (_currentAdIndex < _ads.length - 1) ? _currentAdIndex + 1 : 0;
        _pageController.animateToPage(
          _currentAdIndex,
          duration: const Duration(milliseconds: 500),
          curve: Curves.easeInOut,
        );
      });
    });
  }

  void _scrollListener() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 50 &&
        !_isLoadingMore &&
        _posts.length < _totalPosts) {
      _loadMorePosts();
    }
  }

  void _showSnackBar(String message,
      [Color? backgroundColor, SnackBarAction? action]) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: backgroundColor ?? Colors.teal,
        behavior: SnackBarBehavior.floating,
        action: action,
        elevation: 6,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      ),
    );
  }

  Future<void> _fetchPosts() async {
    setState(() => _isLoading = true);
    final appState = Provider.of<AppState>(context, listen: false);
    try {
      final data = await _postService.fetchPosts(
        appState.token ?? '', 
        widget.cityName, 
        widget.villageName, 
        _page, 
        _limit,
        currentUserId: appState.userId,
      );
      setState(() {
        _posts = data['posts'] as List<Post>;
        _totalPosts = data['total'] as int;
        _isLoading = false;
      });
    } catch (e) {
      _showSnackBar('خطأ في جلب المنشورات: $e', Colors.red);
      setState(() => _isLoading = false);
    }
  }

  Future<void> _loadMorePosts() async {
    if (_isLoadingMore) return;
    setState(() => _isLoadingMore = true);
    final appState = Provider.of<AppState>(context, listen: false);
    try {
      final data = await _postService.fetchPosts(
        appState.token ?? '', 
        widget.cityName, 
        widget.villageName, 
        _page + 1, 
        _limit,
        currentUserId: appState.userId,
      );
      setState(() {
        _posts.addAll(data['posts'] as List<Post>);
        _totalPosts = data['total'] as int;
        _page += 1;
        _isLoadingMore = false;
      });
    } catch (e) {
      _showSnackBar('خطأ في جلب المزيد من المنشورات: $e', Colors.red);
      setState(() => _isLoadingMore = false);
    }
  }

  @override
  void dispose() {
    _timer?.cancel();
    _pageController.dispose();
    _scrollController.dispose();
    _fabAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final title = widget.villageName != null
        ? "منشورات ${widget.villageName}"
        : "منشورات ${widget.cityName}";
    return Scaffold(
      body: _isLoading
          ? _buildSkeletonLoader()
          : RefreshIndicator(
        onRefresh: _fetchPosts,
        color: Colors.teal,
        child: CustomScrollView(
          controller: _scrollController,
          slivers: [
            _buildSliverAppBar(isDarkMode, title),
            SliverToBoxAdapter(child: _buildAdsSection()),
            SliverToBoxAdapter(child: _buildPostsSection()),
            if (_isLoadingMore)
              const SliverToBoxAdapter(
                child: Padding(
                  padding: EdgeInsets.all(16),
                  child: CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.teal),
                  ),
                ),
              ),
          ],
        ),
      ),
      floatingActionButton: ScaleTransition(
        scale: _fabAnimation,
        child: FloatingActionButton(
          onPressed: () => _showCreatePostDialog(context),
          backgroundColor: Colors.teal,
          tooltip: 'إضافة منشور',
          elevation: 8,
          child: const Icon(Icons.post_add, size: 30, color: Colors.white),
        ),
      ),
    );
  }

  SliverAppBar _buildSliverAppBar(bool isDarkMode, String title) {
    return SliverAppBar(
      expandedHeight: 100,
      flexibleSpace: FlexibleSpaceBar(
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: isDarkMode
                  ? [Colors.teal[900]!, Colors.teal[700]!]
                  : [Colors.teal, Colors.tealAccent],
            ),
          ),
          child: SafeArea(
            child: Center(
              child: Text(
                title,
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ),
          ),
        ),
      ),
      actions: [
        IconButton(
          icon: Icon(_isGridView ? Icons.list : Icons.grid_view, color: Colors.white),
          onPressed: () => setState(() => _isGridView = !_isGridView),
        ),
      ],
      pinned: true,
    );
  }

  Widget _buildSkeletonLoader() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return CustomScrollView(
      slivers: [
        SliverAppBar(
          expandedHeight: 100,
          flexibleSpace: FlexibleSpaceBar(
            background: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: isDarkMode
                      ? [Colors.teal[900]!, Colors.teal[700]!]
                      : [Colors.teal, Colors.tealAccent],
                ),
              ),
            ),
          ),
        ),
        SliverToBoxAdapter(
          child: Container(
            height: 150,
            margin: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: isDarkMode ? Colors.grey[800] : Colors.grey[300],
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        ),
        SliverList(
          delegate: SliverChildBuilderDelegate(
                (context, index) => Pulse(
              child: Card(
                margin: const EdgeInsets.all(16),
                child: Padding(
                  padding: const EdgeInsets.all(12.0),
                  child: Column(
                    children: [
                      Container(
                          height: 20, width: 100, color: Colors.grey[300]),
                      const SizedBox(height: 10),
                      Container(
                          height: 100,
                          width: double.infinity,
                          color: Colors.grey[300]),
                    ],
                  ),
                ),
              ),
            ),
            childCount: 5,
          ),
        ),
      ],
    );
  }

  Widget _buildAdsSection() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return Column(
      children: [
        SizedBox(
          height: 150,
          child: PageView.builder(
            controller: _pageController,
            itemCount: _ads.length,
            onPageChanged: (index) => setState(() => _currentAdIndex = index),
            itemBuilder: (context, index) => GestureDetector(
              onTap: () => _showSnackBar('تم النقر على الإعلان ${index + 1}'),
              child: Transform(
                transform: Matrix4.identity()
                  ..setEntry(3, 2, 0.001)
                  ..rotateX(0.1 * (_currentAdIndex - index)),
                alignment: Alignment.center,
                child: Container(
                  margin: const EdgeInsets.symmetric(horizontal: 8),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.2),
                        spreadRadius: 2,
                        blurRadius: 8,
                      ),
                    ],
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(12),
                    child: CachedNetworkImage(
                      imageUrl: _ads[index],
                      fit: BoxFit.cover,
                      placeholder: (context, url) => Container(
                        color: isDarkMode ? Colors.grey[800] : Colors.grey[300],
                        child: const Center(
                          child: CircularProgressIndicator(
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.teal),
                          ),
                        ),
                      ),
                      errorWidget: (context, url, error) => const Icon(
                        Icons.error,
                        color: Colors.teal,
                        size: 50,
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
        const SizedBox(height: 8),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: List.generate(
            _ads.length,
                (index) => AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              margin: const EdgeInsets.symmetric(horizontal: 4),
              width: _currentAdIndex == index ? 20 : 8,
              height: 8,
              decoration: BoxDecoration(
                color: _currentAdIndex == index
                    ? Colors.teal
                    : (isDarkMode ? Colors.grey[600] : Colors.grey),
                borderRadius: BorderRadius.circular(4),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPostsSection() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return _isGridView
        ? _buildGridPosts(isDarkMode)
        : _buildListPosts(isDarkMode);
  }

  Widget _buildGridPosts(bool isDarkMode) {
    return Padding(
      padding: const EdgeInsets.all(8),
      child: MasonryGridView.count(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        crossAxisCount: 2,
        mainAxisSpacing: 8,
        crossAxisSpacing: 8,
        itemCount: _posts.length,
        itemBuilder: (context, index) => FadeInUp(
          child: _buildPostCard(_posts[index], isGrid: true, isDarkMode: isDarkMode),
        ),
      ),
    );
  }

  Widget _buildListPosts(bool isDarkMode) {
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: _posts.length,
      itemBuilder: (context, index) => FadeInUp(
        child: _buildPostCard(_posts[index], isDarkMode: isDarkMode),
      ),
    );
  }

  Widget _buildPostCard(Post post, {bool isGrid = false, required bool isDarkMode}) {
    final TextEditingController commentController = TextEditingController();
    bool isCommentsExpanded = false;

    return GestureDetector(
      onDoubleTap: () => _likePost(post),
      child: Card(
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        elevation: 6,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        color: isDarkMode ? Colors.grey[850] : Colors.white,
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          transform: Matrix4.identity()..scale(post.isLiked ? 1.05 : 1.0),
          padding: const EdgeInsets.all(12.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  ElasticIn(
                    child: CircleAvatar(
                      radius: 20,
                      child: Text(post.user.isNotEmpty ? post.user[0] : 'U'),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          post.user,
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: isDarkMode ? Colors.white : Colors.black87,
                          ),
                        ),
                        Text(
                          timeago.format(post.createdAt, locale: 'ar'),
                          style: TextStyle(
                            fontSize: 12,
                            color: isDarkMode ? Colors.grey[400] : Colors.grey,
                          ),
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.more_vert,
                        color: Colors.teal),
                    onPressed: () => _showPostOptions(context, post),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              if (post.image != null)
                ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: CachedNetworkImage(
                    imageUrl: '$_postService.baseUrl${post.image!}',
                    height: isGrid ? 100 : 200,
                    width: double.infinity,
                    fit: BoxFit.cover,
                    placeholder: (context, url) => Container(
                      color: isDarkMode ? Colors.grey[800] : Colors.grey[300],
                      child: const Center(
                        child: CircularProgressIndicator(
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.teal),
                        ),
                      ),
                    ),
                    errorWidget: (context, url, error) => const Icon(
                      Icons.error,
                      color: Colors.teal,
                      size: 50,
                    ),
                  ),
                ),
              const SizedBox(height: 12),
              Text(
                post.content,
                style: TextStyle(
                  fontSize: 14,
                  color: isDarkMode ? Colors.white70 : Colors.black87,
                ),
              ),
              const SizedBox(height: 12),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      const Icon(Icons.visibility, size: 18, color: Colors.grey),
                      const SizedBox(width: 4),
                      Text(
                        '${post.views}',
                        style: TextStyle(
                          color: isDarkMode ? Colors.grey[400] : Colors.grey,
                        ),
                      ),
                    ],
                  ),
                  Row(
                    children: [
                      IconButton(
                        icon: AnimatedSwitcher(
                          duration: const Duration(milliseconds: 300),
                          child: Icon(
                            post.isLiked
                                ? Icons.thumb_up
                                : Icons.thumb_up_outlined,
                            key: ValueKey(post.isLiked),
                            color: post.isLiked ? Colors.teal : Colors.grey,
                          ),
                        ),
                        onPressed: () => _likePost(post),
                      ),
                      Text(
                        '${post.likes}',
                        style: TextStyle(
                          color: isDarkMode ? Colors.white70 : Colors.black87,
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.share, color: Colors.teal),
                        onPressed: () => _sharePost(post),
                      ),
                    ],
                  ),
                ],
              ),
              const Divider(),
              GestureDetector(
                onTap: () => setState(() => isCommentsExpanded = !isCommentsExpanded),
                child: Text(
                  "التعليقات (${post.comments.length})",
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: isDarkMode ? Colors.teal[300] : Colors.teal,
                  ),
                ),
              ),
              const SizedBox(height: 8),
              if (isCommentsExpanded || !isGrid)
                post.comments.isEmpty
                    ? Text(
                  'لا توجد تعليقات بعد',
                  style: TextStyle(
                    color: isDarkMode ? Colors.grey[400] : Colors.grey,
                  ),
                )
                    : AnimatedContainer(
                  duration: const Duration(milliseconds: 300),
                  constraints: BoxConstraints(
                    maxHeight: isCommentsExpanded ? 200 : 50,
                  ),
                  child: ListView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: isCommentsExpanded
                        ? post.comments.length
                        : (isGrid ? 1 : post.comments.length),
                    itemBuilder: (context, index) => Padding(
                      padding: const EdgeInsets.symmetric(vertical: 4.0),
                      child: Row(
                        children: [
                          const CircleAvatar(radius: 12, child: Text("U")),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              post.comments[index],
                              maxLines: isGrid ? 1 : null,
                              overflow: isGrid
                                  ? TextOverflow.ellipsis
                                  : null,
                              style: TextStyle(
                                color: isDarkMode
                                    ? Colors.white70
                                    : Colors.black87,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              if (isCommentsExpanded || !isGrid) const SizedBox(height: 8),
              Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: commentController,
                      decoration: InputDecoration(
                        hintText: "أضف تعليقك...",
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(20),
                        ),
                        filled: true,
                        fillColor: isDarkMode ? Colors.grey[800] : Colors.teal[50],
                      ),
                      style: TextStyle(
                        color: isDarkMode ? Colors.white : Colors.black87,
                      ),
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.send, color: Colors.teal),
                    onPressed: () async {
                      if (commentController.text.isNotEmpty) {
                        try {
                          await _postService.commentPost(
                            Provider.of<AppState>(context, listen: false).token ?? '',
                            post.id,
                            commentController.text,
                          );
                          await _fetchPosts();
                          commentController.clear();
                          setState(() => isCommentsExpanded = true);
                        } catch (e) {
                          _showSnackBar('خطأ في إضافة التعليق: $e', Colors.red);
                        }
                      }
                    },
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _likePost(Post post) async {
    final originalIsLiked = post.isLiked;
    final originalLikes = post.likes;
    try {
      setState(() {
        post.isLiked = !post.isLiked;
        post.likes += post.isLiked ? 1 : -1;
      });
      await _postService.likePost(
        Provider.of<AppState>(context, listen: false).token ?? '',
        post.id,
        post.isLiked,
      );
    } catch (e) {
      setState(() {
        post.isLiked = originalIsLiked;
        post.likes = originalLikes;
      });
      _showSnackBar('خطأ في تحديث الإعجاب: $e', Colors.red);
    }
  }

  void _showPostOptions(BuildContext context, Post post) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.report, color: Colors.red),
              title: const Text('الإبلاغ عن المنشور'),
              onTap: () {
                Navigator.pop(context);
                _showSnackBar('تم الإبلاغ عن المنشور', Colors.orange);
              },
            ),
            ListTile(
              leading: const Icon(Icons.bookmark_border, color: Colors.teal),
              title: const Text('حفظ المنشور'),
              onTap: () {
                Navigator.pop(context);
                _showSnackBar('تم حفظ المنشور', Colors.green);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showCreatePostDialog(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final TextEditingController contentController = TextEditingController();
    File? imageFile;
    String? selectedImageName;
    final appState = Provider.of<AppState>(context, listen: false);
    PostPrivacy selectedPrivacy = PostPrivacy.public;
    List<String> selectedUserIds = [];

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
          backgroundColor: isDarkMode ? Colors.grey[900] : Colors.white,
          title: Container(
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: isDarkMode
                    ? [Colors.teal[900]!, Colors.teal[700]!]
                    : [Colors.teal, Colors.tealAccent],
              ),
              borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
            ),
            child: const Text(
              "إضافة منشور جديد",
              style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
          ),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: contentController,
                  decoration: InputDecoration(
                    hintText: "اكتب منشورك هنا...",
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                    filled: true,
                    fillColor: isDarkMode ? Colors.grey[800] : Colors.grey[100],
                  ),
                  maxLines: 3,
                  style: TextStyle(
                    color: isDarkMode ? Colors.white : Colors.black87,
                  ),
                ),
                const SizedBox(height: 10),
                // Privacy selector
                DropdownButtonFormField<PostPrivacy>(
                  value: selectedPrivacy,
                  decoration: InputDecoration(
                    labelText: 'خصوصية المنشور',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                    filled: true,
                    fillColor: isDarkMode ? Colors.grey[800] : Colors.grey[100],
                  ),
                  dropdownColor: isDarkMode ? Colors.grey[900] : Colors.white,
                  style: TextStyle(
                    color: isDarkMode ? Colors.white : Colors.black87,
                  ),
                  items: PostPrivacy.values.map((privacy) {
                    return DropdownMenuItem(
                      value: privacy,
                      child: Text(
                        privacy.label,
                        style: TextStyle(
                          color: isDarkMode ? Colors.white : Colors.black87,
                        ),
                      ),
                    );
                  }).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      setDialogState(() {
                        selectedPrivacy = value;
                      });
                    }
                  },
                ),
                if (selectedPrivacy == PostPrivacy.specificFriends)
                  Padding(
                    padding: const EdgeInsets.only(top: 10),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'اختر الأصدقاء الذين يمكنهم رؤية المنشور',
                          style: TextStyle(
                            color: isDarkMode ? Colors.white70 : Colors.black87,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            border: Border.all(
                              color: isDarkMode ? Colors.grey[700]! : Colors.grey[400]!,
                            ),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: selectedUserIds.isEmpty
                              ? const Text('لم يتم اختيار أي أصدقاء بعد')
                              : Wrap(
                            spacing: 8,
                            runSpacing: 8,
                            children: selectedUserIds.map((userId) {
                              return Chip(
                                label: Text(userId),
                                onDeleted: () {
                                  setDialogState(() {
                                    selectedUserIds.remove(userId);
                                  });
                                },
                              );
                            }).toList(),
                          ),
                        ),
                        const SizedBox(height: 8),
                        TextButton.icon(
                          onPressed: () {
                            // TODO: Implement friend selection dialog
                            _showSnackBar('سيتم تنفيذ اختيار الأصدقاء قريبًا', Colors.blue);
                          },
                          icon: const Icon(Icons.person_add, size: 20),
                          label: const Text('اختر أصدقاء'),
                        ),
                      ],
                    ),
                  ),
                const SizedBox(height: 10),
                ElevatedButton.icon(
                  onPressed: () async {
                    final picker = ImagePicker();
                    final pickedFile =
                    await picker.pickImage(source: ImageSource.gallery);
                    if (pickedFile != null) {
                      final file = File(pickedFile.path);
                      if (await file.length() > 5 * 1024 * 1024) {
                        _showSnackBar(
                          'حجم الصورة كبير جدًا (يجب أن يكون أقل من 5 ميجابايت)',
                          Colors.orange,
                        );
                        return;
                      }
                      setDialogState(() {
                        imageFile = file;
                        selectedImageName = pickedFile.name;
                      });
                    }
                  },
                  icon: const Icon(Icons.photo, color: Colors.white),
                  label: Text(
                    selectedImageName ?? "إضافة صورة",
                    style: const TextStyle(color: Colors.white),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.teal,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                    elevation: 4,
                  ),
                ),
                if (imageFile != null)
                  Padding(
                    padding: const EdgeInsets.only(top: 8),
                    child: Text(
                      "تم اختيار صورة: $selectedImageName",
                      style: const TextStyle(color: Colors.green),
                    ),
                  ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(
                "إلغاء",
                style: TextStyle(
                  color: isDarkMode ? Colors.white70 : Colors.teal,
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () async {
                if (contentController.text.isEmpty && imageFile == null) {
                  _showSnackBar('الرجاء إدخال نص أو صورة', Colors.orange);
                  return;
                }

                if (selectedPrivacy == PostPrivacy.specificFriends && selectedUserIds.isEmpty) {
                  _showSnackBar('الرجاء اختيار الأصدقاء الذين يمكنهم رؤية المنشور', Colors.orange);
                  return;
                }

                try {
                  await _postService.addPost(
                    appState.token ?? '',
                    widget.cityName,
                    widget.villageName,
                    contentController.text,
                    imageFile,
                    privacy: selectedPrivacy,
                    visibleToUserIds: selectedPrivacy == PostPrivacy.specificFriends ? selectedUserIds : null,
                  );
                  await _fetchPosts();
                  _showSnackBar("تم النشر بنجاح", Colors.green);
                  if (mounted) {
                    Navigator.pop(context);
                  }
                } catch (e) {
                  _showSnackBar("خطأ في النشر: $e", Colors.red);
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.teal,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
              child: const Text(
                "نشر",
                style: TextStyle(color: Colors.white),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _sharePost(Post post) {
    final location = post.village ?? post.city;
    final shareText =
        'منشور من ${post.user} في $location: ${post.content}${post.image != null ? '\n$_postService.baseUrl${post.image}' : ''}';
    Share.share(shareText);
    _showSnackBar(
      "تمت المشاركة بنجاح",
      Colors.green,
      SnackBarAction(
        label: 'مشاركة مرة أخرى',
        textColor: Colors.white,
        onPressed: () => Share.share(shareText),
      ),
    );
  }
}