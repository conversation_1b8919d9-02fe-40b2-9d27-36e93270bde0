class NotificationModel {
  final String id;
  final String message;
  final String date;
  final bool isRead;
  final String? type;
  final String? relatedId;

  NotificationModel({
    required this.message,
    required this.date,
    this.id = '',
    this.isRead = false,
    this.type,
    this.relatedId,
  });

  factory NotificationModel.fromJson(Map<String, dynamic> json) {
    return NotificationModel(
      id: json['id'] ?? '',
      message: json['message'] ?? '',
      date: json['date'] ?? DateTime.now().toString(),
      isRead: json['isRead'] ?? false,
      type: json['type'],
      relatedId: json['relatedId'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'message': message,
      'date': date,
      'isRead': isRead,
      'type': type,
      'relatedId': relatedId,
    };
  }

  NotificationModel copyWith({
    String? id,
    String? message,
    String? date,
    bool? isRead,
    String? type,
    String? relatedId,
  }) {
    return NotificationModel(
      id: id ?? this.id,
      message: message ?? this.message,
      date: date ?? this.date,
      isRead: isRead ?? this.isRead,
      type: type ?? this.type,
      relatedId: relatedId ?? this.relatedId,
    );
  }
}
