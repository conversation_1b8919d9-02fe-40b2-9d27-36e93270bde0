import 'package:uuid/uuid.dart';

class Comment {
  final String id;
  final String userId;
  final String username;
  final String content;
  final DateTime createdAt;
  final String? userAvatar;
  final List<Comment> replies;
  final String? parentCommentId;
  final int likesCount;
  final List<String> likedBy;

  Comment({
    String? id,
    required this.userId,
    required this.username,
    required this.content,
    DateTime? createdAt,
    this.userAvatar,
    this.replies = const [],
    this.parentCommentId,
    this.likesCount = 0,
    this.likedBy = const [],
  })  : id = id ?? const Uuid().v4(),
        createdAt = createdAt ?? DateTime.now();

  factory Comment.fromJson(Map<String, dynamic> json) {
    final repliesList = json['replies'] as List<dynamic>?;
    final likedByList = json['likedBy'] as List<dynamic>?;

    return Comment(
      id: json['_id']?.toString() ?? json['id']?.toString() ?? const Uuid().v4(),
      userId: json['userId'] is Map ? json['userId']['_id']?.toString() ?? '' : json['userId']?.toString() ?? '',
      username: json['username']?.toString() ?? (json['user']?['name']?.toString() ?? 'مستخدم مجهول'),
      content: json['content']?.toString() ?? json['text']?.toString() ?? '',
      createdAt: json['createdAt'] is String 
          ? DateTime.tryParse(json['createdAt']) ?? DateTime.now()
          : (json['createdAt'] as Map?)?['\$date'] != null
              ? DateTime.fromMillisecondsSinceEpoch(
                  int.parse(json['createdAt']['\$date'].toString()))
              : DateTime.now(),
      userAvatar: json['userAvatar']?.toString() ?? json['user']?['avatar']?.toString(),
      replies: repliesList != null
          ? repliesList.map((r) => Comment.fromJson(r as Map<String, dynamic>)).toList()
          : [],
      parentCommentId: json['parentCommentId']?.toString(),
      likesCount: (json['likesCount'] as num?)?.toInt() ?? 0,
      likedBy: likedByList?.map((e) => e.toString()).toList() ?? [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'username': username,
      'content': content,
      'createdAt': createdAt.toIso8601String(),
      if (userAvatar != null) 'userAvatar': userAvatar,
      if (replies.isNotEmpty) 'replies': replies.map((r) => r.toJson()).toList(),
      if (parentCommentId != null) 'parentCommentId': parentCommentId,
      'likesCount': likesCount,
      'likedBy': likedBy,
    };
  }

  // Helper method to convert to the old format if needed
  String get date => createdAt.toIso8601String();
  
  String? get profileImagePath => userAvatar;
  
  // For backward compatibility
  Comment copyWith({
    String? id,
    String? userId,
    String? username,
    String? content,
    DateTime? createdAt,
    String? userAvatar,
    List<Comment>? replies,
    String? parentCommentId,
    int? likesCount,
    List<String>? likedBy,
  }) {
    return Comment(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      username: username ?? this.username,
      content: content ?? this.content,
      createdAt: createdAt ?? this.createdAt,
      userAvatar: userAvatar ?? this.userAvatar,
      replies: replies ?? this.replies,
      parentCommentId: parentCommentId ?? this.parentCommentId,
      likesCount: likesCount ?? this.likesCount,
      likedBy: likedBy ?? this.likedBy,
    );
  }
}
