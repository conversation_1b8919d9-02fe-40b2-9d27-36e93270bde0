import 'package:flutter/material.dart';
import 'package:latlong2/latlong.dart';
import 'ride_request.dart';

class RideState extends ChangeNotifier {
  LatLng? _currentLocation;
  List<RideRequest> _rideRequests = [];
  bool _isAvailable = true;
  Driver? _currentDriver;
  final List<Driver> _drivers = [];

  // Getters
  LatLng? get currentLocation => _currentLocation;
  List<RideRequest> get rideRequests => _rideRequests;
  bool get isAvailable => _isAvailable;
  Driver? get currentDriver => _currentDriver;
  List<Driver> get drivers => _drivers;

  // Setters
  set currentLocation(LatLng? location) {
    _currentLocation = location;
    notifyListeners();
  }

  set isAvailable(bool value) {
    _isAvailable = value;
    notifyListeners();
  }

  // Driver management
  void loginAsDriver(String name, String vehicleInfo) {
    _currentDriver = Driver(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      name: name,
      vehicleInfo: vehicleInfo,
      isAvailable: true,
    );
    notifyListeners();
  }

  void updateDriverAvailability(bool isAvailable) {
    if (_currentDriver != null) {
      _currentDriver = _currentDriver!.copyWith(isAvailable: isAvailable);
      _isAvailable = isAvailable;
      notifyListeners();
    }
  }

  // Ride request management
  void acceptRideRequest(String requestId, String driverId) {
    final index = _rideRequests.indexWhere((r) => r.id == requestId);
    if (index != -1) {
      _rideRequests[index] = _rideRequests[index].copyWith(
        status: 'Accepted',
        driverId: driverId,
      );
      notifyListeners();
    }
  }

  void updateRideStatus(String requestId, String status) {
    final index = _rideRequests.indexWhere((r) => r.id == requestId);
    if (index != -1) {
      _rideRequests[index] = _rideRequests[index].copyWith(status: status);
      notifyListeners();
    }
  }

  // Add test data (for development)
  void addTestData() {
    _rideRequests = [
      RideRequest(
        id: '1',
        passengerId: 'passenger1',
        pickup: const LatLng(33.5138, 36.2765),
        destination: 'Damascus',
        status: 'Pending',
        requestType: 'normal',
        timestamp: DateTime.now(),
      ),
    ];
    notifyListeners();
  }
}

class Driver {
  final String id;
  final String name;
  final String vehicleInfo;
  final bool isAvailable;

  Driver({
    required this.id,
    required this.name,
    required this.vehicleInfo,
    this.isAvailable = true,
  });

  Driver copyWith({
    String? id,
    String? name,
    String? vehicleInfo,
    bool? isAvailable,
  }) {
    return Driver(
      id: id ?? this.id,
      name: name ?? this.name,
      vehicleInfo: vehicleInfo ?? this.vehicleInfo,
      isAvailable: isAvailable ?? this.isAvailable,
    );
  }
}

// Provider for RideState
class RideStateProvider extends InheritedNotifier<RideState> {
  final RideState rideState;

  const RideStateProvider({
    super.key,
    required this.rideState,
    required super.child,
  }) : super(notifier: rideState);

  static RideState of(BuildContext context) {
    return context.dependOnInheritedWidgetOfExactType<RideStateProvider>()!.rideState;
  }
}
