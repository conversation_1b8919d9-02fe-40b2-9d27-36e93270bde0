import 'dart:async';
import 'dart:convert';
import 'dart:math';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:share_plus/share_plus.dart';
import 'package:animate_do/animate_do.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import 'package:video_player/video_player.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:lottie/lottie.dart';
import 'package:http/http.dart' as http;

// Data Models (Ingredient, PreparationMethod, Meal)
// ... (Keep existing data models)

class Ingredient {
  final String name;
  final Map<String, double> quantitiesBySize;
  final String? iconUrl; // Optional: Icon for the ingredient

  Ingredient({
    required this.name,
    required this.quantitiesBySize,
    this.iconUrl,
  });
}

class PreparationMethod {
  final List<String> ingredients;
  final List<String> steps;
  final String videoUrl;
  final String? imageUrl; // Optional: Image for the meal

  PreparationMethod({
    required this.ingredients,
    required this.steps,
    required this.videoUrl,
    this.imageUrl,
  });
}

class Meal {
  final String name;
  final String category;
  final List<Ingredient> ingredients;
  final PreparationMethod preparation;
  final String? mealIcon; // Optional: Icon for the meal category

  Meal({
    required this.name,
    required this.category,
    required this.ingredients,
    required this.preparation,
    this.mealIcon,
  });
}

// WeekSection Widget (Enhanced with animations and improved UI)
// WeekSection with proper BuildContext usage
class WeekSection extends StatelessWidget {
  // Add weeks list
  static const List<String> _weeks = [
    'الأسبوع الأول',
    'الأسبوع الثاني',
    'الأسبوع الثالث',
    'الأسبوع الرابع'
  ];

  final String memberId;
  final String week;
  final Map<String, Color> weekColors;
  final List<String> mealCategories;
  final List<String> days;
  final Map<String,
      Map<String, Map<String, Map<String, Map<String, String?>?>>>>
  familyMonthlyMeals;
  final Function(String, String, String) onClearMealCategory;
  final Function(String, String, String, String, Map<String, String?>?)
  buildItemCard;

  const WeekSection({
    Key? key,
    required this.memberId,
    required this.week,
    required this.weekColors,
    required this.mealCategories,
    required this.days,
    required this.familyMonthlyMeals,
    required this.onClearMealCategory,
    required this.buildItemCard,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AnimationConfiguration.staggeredList(
      position: _weeks.indexOf(week),
      duration: const Duration(milliseconds: 375),
      child: SlideAnimation(
        verticalOffset: 50.0,
        child: FadeInAnimation(
          child: Card(
            margin: const EdgeInsets.symmetric(vertical: 10, horizontal: 8),
            shape:
            RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
            elevation: 5,
            child: ExpansionTile(
              leading: CircleAvatar(
                backgroundColor: weekColors[week]!.withOpacity(0.2),
                child: Text(
                  week.substring(week.length - 1), // Week number
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: weekColors[week],
                  ),
                ),
              ),
              title: Text(
                week,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: weekColors[week],
                ),
              ),
              collapsedIconColor: weekColors[week],
              iconColor: weekColors[week],
              childrenPadding:
              const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              children: mealCategories.map((mealType) {
                return Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 8.0),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              mealType,
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: weekColors[week]!.withOpacity(0.8),
                              ),
                            ),
                            IconButton(
                              icon: Icon(Icons.delete_sweep_outlined,
                                  color: weekColors[week]),
                              onPressed: () =>
                                  onClearMealCategory(memberId, week, mealType),
                              tooltip: 'مسح اختيارات $mealType',
                            ),
                          ],
                        ),
                      ),
                      GridView.builder(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        gridDelegate:
                        const SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 3, // Adjusted for better layout
                          childAspectRatio: 0.75, // Adjusted for better layout
                          crossAxisSpacing: 8,
                          mainAxisSpacing: 8,
                        ),
                        itemCount: days.length,
                        itemBuilder: (context, index) {
                          String day = days[index];
                          var item = familyMonthlyMeals[memberId]![week]![
                          mealType]![day];
                          return AnimationConfiguration.staggeredGrid(
                            position: index,
                            duration: const Duration(milliseconds: 375),
                            columnCount: 3,
                            child: ScaleAnimation(
                              child: FadeInAnimation(
                                child: buildItemCard(
                                    memberId, week, mealType, day, item),
                              ),
                            ),
                          );
                        },
                      ),
                    ],
                  ),
                );
              }).toList(),
            ),
          ),
        ),
      ),
    );
  }
}

// MonthlyMealsScreen StatefulWidget (Main screen with all enhancements)
class MonthlyMealsScreen extends StatefulWidget {
  const MonthlyMealsScreen({super.key});

  @override
  _MonthlyMealsScreenState createState() => _MonthlyMealsScreenState();
}

class _MonthlyMealsScreenState extends State<MonthlyMealsScreen>
    with TickerProviderStateMixin {
  // ... (Keep existing state variables)
  final storage = const FlutterSecureStorage();
  final TextEditingController _foodNameController = TextEditingController();
  final TextEditingController _foodCategoryController = TextEditingController();
  final TextEditingController _foodDescriptionController =
  TextEditingController();
  List<Map<String, dynamic>> _foodTypes = [];
  bool _isLoadingFoodTypes = false;
  bool _isAddingFoodType = false;

  final int _batchSize = 10;
  int _currentBatch = 0;
  bool _isLoadingMore = false;
  bool _hasMoreData = true;

  static const List<String> weeks = [
    'الأسبوع 1',
    'الأسبوع 2',
    'الأسبوع 3',
    'الأسبوع 4'
  ];
  static const List<String> days = [
    'السبت',
    'الأحد',
    'الإثنين',
    'الثلاثاء',
    'الأربعاء',
    'الخميس',
    'الجمعة'
  ];
  static const List<String> portionSizes = ['كبير', 'متوسط', 'صغير'];
  static const List<String> mealCategories = [
    'الفطور',
    'الغداء',
    'العشاء',
    'التحلية اليومية',
    'المشروبات اليومية',
    'أطعمة العمل'
  ];

  final Map<String, Color> weekColors = {
    'الأسبوع 1': Colors.teal,
    'الأسبوع 2': Colors.indigo,
    'الأسبوع 3': Colors.amber.shade700, // Darker amber
    'الأسبوع 4': Colors.pink.shade400, // Lighter pink
  };

  // Enhanced ingredients with icons (example)
  final Map<String, Map<String, Map<String, double>>> ingredients = {
    'فول مدمس': {
      'فول': {'صغير': 0.1, 'متوسط': 0.2, 'كبير': 0.3},
      'زيت': {'صغير': 0.01, 'متوسط': 0.02, 'كبير': 0.03},
      'ملح': {'صغير': 0.005, 'متوسط': 0.01, 'كبير': 0.015},
    },
    // ... (Add more ingredients with optional icons)
  };

  // Enhanced preparationMethods with images (example)
  final Map<String, Map<String, dynamic>> preparationMethods = {
    'فول مدمس': {
      'ingredients': ['فول', 'زيت', 'ملح', 'كمون', 'ثوم'],
      'steps': [
        'انقع الفول في الماء طوال الليل ثم اغسله جيدًا.',
        'اسلق الفول في ماء نظيف لمدة 4-5 ساعات حتى يصبح طريًا.',
        'اهرس الفول قليلًا وأضف الزيت، الملح، الكمون والثوم حسب الرغبة.',
        'قدمه ساخنًا مع الخبز والسلطة.'
      ],
      'videoUrl': 'https://example.com/videos/foul.mp4',
      'imageUrl': 'https://example.com/images/foul.jpg', // Example image
    },
    // ... (Add more preparation methods with optional images)
  };

  final Map<String, Map<String, List<String>>> foodPreferences = {
    'الطعام المصري': {
      'الفطور': [
        'فول مدمس',
        'طعمية',
        'بيض بالبسطرمة',
        'جبنة بيضاء',
        'فطائر جبنة'
      ],
      'الغداء': ["كشري", "ملوخية", "محشي", "مسقعة البطاطس", "كبدة مشوية"],
      'العشاء': [
        "شاورما",
        "فتة",
        "مكرونة بالبشاميل",
        "بطاطس محمرة",
        "سلطة خضراء"
      ],
      'التحلية اليومية': ["بسبوسة", "كنافة", "أم علي", "مهلبية", "أرز باللبن"],
      'المشروبات اليومية': [
        "عرقسوس",
        "تمر هندي",
        "كركديه",
        "خروب",
        "تمر هندي باللبن"
      ],
      'أطعمة العمل': [
        "ساندويتش جبن",
        "ساندويتش بيض",
        "ساندويتش تونة",
        "ساندويتش رومي",
        "ساندويتش لانشون"
      ],
    },
    'المطبخ الإيطالي': {
      'الفطور': [
        'كرواسون بالشوكولاتة',
        'بان كيك بالتوت',
        'توست أفوكادو',
        'كابتشينو وكرواسون',
        'زبادي بالفواكه الطازجة'
      ],
      'الغداء': [
        'باستا كاربونارا',
        'بيتزا مارغريتا',
        'لازانيا باللحمة',
        'ريسوتو بالفطر',
        'بينيني بالجبن والبروسسيوتو'
      ],
      'العشاء': [
        'سلطة كابريزي',
        'ميني بيتزا',
        'بروشيتا',
        'باستا بومودورو',
        'جريتاتا بالخضروات'
      ],
      'التحلية اليومية': [
        'تيراميسو',
        'باناكوتا',
        'كانولي',
        'جاتوه الشوكولاتة الإيطالي',
        'سوفليه الليمون'
      ],
      'المشروبات اليومية': [
        'إسبريسو',
        'كابتشينو',
        'لاتيه',
        'ليموناتا',
        'مشروب اللوز'
      ],
      'أطعمة العمل': [
        'ساندويتش بانيني',
        'سلطة باستا',
        'كروكيت البطاطس',
        'ساندويتش بروسسيوتو',
        'سلطة كابريزي'
      ],
    },
    'المطبخ الهندي': {
      'الفطور': [
        'دوسا',
        'إدلي',
        'بوهاتورا',
        'أبام',
        'بونجال'
      ],
      'الغداء': [
        'برياني دجاج',
        'بتر تشيكن',
        'دجاج تندوري',
        'شاهي بانير',
        'دال ماخني'
      ],
      'العشاء': [
        'سمبوسة',
        'باكورا',
        'شول باتشر',
        'باني بورى',
        'تندورى روتي'
      ],
      'التحلية اليومية': [
        'جلاب جامون',
        'كولفي',
        'جاليبي',
        'كاجو كاتلي',
        'راسمالاي'
      ],
      'المشروبات اليومية': [
        'ماسالا تشاي',
        'لاسي',
        'ماء جوز الهند',
        'مشروب المانجو',
        'شاي الزنجبيل'
      ],
      'أطعمة العمل': [
        'ساموسا',
        'باتاتا فادا',
        'باف بهاجي',
        'تشولا باتورا',
        'ماسالا دوسا'
      ],
    },
    'النظام الصحي': {
      'الفطور': [
        'شوفان بالفواكه',
        'أفوكادو مع خبز توست',
        'عجة بالخضروات',
        'زبادي يوناني مع المكسرات',
        'سموثي أخضر'
      ],
      'الغداء': [
        'سلطة كينوا',
        'سمك مشوي مع خضار',
        'صدر دجاج مشوي مع بطاطا حلوة',
        'شوربة خضار',
        'سلطة التونة'
      ],
      'العشاء': [
        'سلمون مشوي مع بروكلي',
        'سلطة الكينوا والحمص',
        'شوربة العدس',
        'أومليت بالخضار',
        'سوتيه خضار مع أرز بني'
      ],
      'التحلية اليومية': [
        'سلطة فواكه',
        'شوكولاتة داكنة',
        'زبادي بالتوت',
        'تمر باللوز',
        'موس الشوكولاتة بالأفوكادو'
      ],
      'المشروبات اليومية': [
        'ماء بالليمون',
        'شاي أخضر',
        'عصير أخضر',
        'ماء جوز الهند',
        'شاي الزنجبيل'
      ],
      'أطعمة العمل': [
        'مكسرات مشكلة',
        'جزر وخيار',
        'تفاح مع زبدة الفول السوداني',
        'حبوب الكينوا',
        'سموثي بروتين'
      ],
    },
    'المطبخ العربي': {
      'الفطور': [
        'فول مدمس',
        'حمص',
        'فلافل',
        'مناقيش زعتر',
        'بيض بالبندورة'
      ],
      'الغداء': [
        'منسف',
        'كبسة',
        'مقلوبة',
        'مسخن',
        'مجبوس'
      ],
      'العشاء': [
        'شاورما',
        'شيش طاووق',
        'فتة',
        'سلطة فتوش',
        'تبولة'
      ],
      'التحلية اليومية': [
        'كنافة',
        'بقلاوة',
        'مهلبية',
        'قطايف',
        'حلاوة الجبن'
      ],
      'المشروبات اليومية': [
        'شاي بالنعناع',
        'قهوة عربية',
        'عيران',
        'تمر هندي',
        'سحلب'
      ],
      'أطعمة العمل': [
        'ساندويتش شاورما',
        'فطاير جبنة',
        'سندويشات فلافل',
        'صاجية دجاج',
        'سندويشات زعتر'
      ],
    },
    'المطبخ التركي': {
      'الفطور': [
        'سجق بالبيض',
        'جبنة بيضاء مع زيتون',
        'كروسان تركي',
        'بوريك بالجبنة',
        'كوكوريج'
      ],
      'الغداء': [
        'كباب مشوي',
        'دونر',
        'إسكندر كباب',
        'لحم عجل بالفرن',
        'يخنة الباذنجان'
      ],
      'العشاء': [
        'بيتزا تركية',
        'لحمة بعجين',
        'شاورما تركية',
        'سلطة تركية',
        'كفتة مشوية'
      ],
      'التحلية اليومية': [
        'باكلاوا',
        'كنافة تركي',
        'أرز بالحليب',
        'كادايف',
        'سوتلاك'
      ],
      'المشروبات اليومية': [
        'شاي تركي',
        'قهوة تركية',
        'عيران',
        'شراب الورد',
        'عصير الرمان'
      ],
      'أطعمة العمل': [
        'ساندويتش دونر',
        'سيميت',
        'بيدا',
        'بوريك باللحمة',
        'كفتة بالعجين'
      ],
    },
    'نظام السكري': {
      'الفطور': [
        'شوفان مع حليب خالي الدسم وتوت',
        'بيض مسلوق مع خبز قمح كامل',
        'جبنة قريش مع خيار وطماطم',
        'لبنة مع خبز أسمر',
        'أفوكادو مع بيض مسلوق'
      ],
      'الغداء': [
        'سمك مشوي مع خضار سوتيه',
        'صدر دجاج مشوي مع بروكلي',
        'شوربة خضار مع عدس',
        'سلطة سيزر مع صدر دجاج',
        'كوسة محشية بالأرز البني واللحمة'
      ],
      'العشاء': [
        'سلطة تونة مع خضار',
        'زبادي يوناني مع بذور الكتان',
        'شوربة كريمة بروكلي',
        'سلمون مشوي مع هليون',
        'بيض مسلوق مع سلطة خضراء'
      ],
      'التحلية اليومية': [
        'تفاح بالقرفة',
        'زبادي يوناني مع توت',
        'بذور شيا مع حليب لوز',
        'سموثي أخضر',
        'مكسرات نيئة'
      ],
      'المشروبات اليومية': [
        'شاي أخضر',
        'قرفة بالحليب خالي الدسم',
        'ماء بالليمون',
        'شاي أعشاب',
        'قهوة سوداء'
      ],
      'أطعمة العمل': [
        'جزر وخيار',
        'لوز نيء',
        'جبنة قليلة الدسم',
        'زبدة فول سوداني مع تفاح',
        'بيضة مسلوقة'
      ],
    },
    'نظام ضغط الدم': {
      'الفطور': [
        'شوفان مع موز وجوز',
        'زبادي مع توت وبذور كتان',
        'عصير أخضر مع سبانخ',
        'توست أفوكادو',
        'عجة بالخضار'
      ],
      'الغداء': [
        'سلطة سيزر مع سلمون',
        'شوربة خضار',
        'دجاج مشوي مع بطاطا حلوة',
        'سلطة كينوا مع خضار',
        'شوربة عدس'
      ],
      'العشاء': [
        'سمك مشوي مع بروكلي',
        'سلطة تونة',
        'دجاج بالفرن مع خضار',
        'شوربة خضار',
        'سلمون مع هليون'
      ],
      'التحلية اليومية': [
        'موز',
        'تفاح مع لوز',
        'زبادي بالتوت',
        'بذور شيا',
        'سموثي أخضر'
      ],
      'المشروبات اليومية': [
        'ماء بالليمون',
        'شاي أخضر',
        'عصير رمان',
        'شاي كركديه',
        'ماء جوز الهند'
      ],
      'أطعمة العمل': [
        'مكسرات غير مملحة',
        'جزر',
        'تفاح',
        'زبادي يوناني',
        'خضار مشوي'
      ],
    },
    'نظام القلب': {
      'الفطور': [
        'شوفان مع توت وجوز',
        'توست أفوكادو مع بيض',
        'سموثي أخضر',
        'زبادي مع بذور شيا',
        'عجة بالخضار'
      ],
      'الغداء': [
        'سلطة سلمون مع خضار',
        'شوربة عدس',
        'دجاج مشوي مع خضار',
        'كينوا مع خضار',
        'سمك مشوي مع بروكلي'
      ],
      'العشاء': [
        'سلطة تونة',
        'شوربة خضار',
        'دجاج بالفرن',
        'سمك سلمون مع هليون',
        'شوربة فاصوليا'
      ],
      'التحلية اليومية': [
        'فواكه طازجة',
        'زبادي مع عسل',
        'بذور شيا',
        'سموثي أخضر',
        'مكسرات نيئة'
      ],
      'المشروبات اليومية': [
        'شاي أخضر',
        'عصير رمان',
        'ماء بالليمون',
        'شاي كركديه',
        'ماء جوز الهند'
      ],
      'أطعمة العمل': [
        'جزر',
        'تفاح',
        'لوز',
        'زبادي يوناني',
        'خضار مشوي'
      ],
    },
    'نظام الكيتو': {
      'الفطور': [
        'بيض مقلي مع أفوكادو',
        'أومليت بالجبنة والخضار',
        'زبادي يوناني كامل الدسم مع بذور',
        'سموثي كيتو (سبانخ، أفوكادو، حليب لوز)',
        'بذور شيا مع حليب جوز الهند'
      ],
      'الغداء': [
        'دجاج بالزبدة مع خضار سوتيه',
        'ستيك لحم مع بروكلي وجبنة',
        'سلطة سيزر مع سلمون',
        'برجر لحم بدون خبز مع جبنة وخضار',
        'دجاج بالكاري مع زيت جوز الهند'
      ],
      'العشاء': [
        'سمك سلمون مع هليون',
        'لحم مفروم مع جبنة وخضار',
        'بيض مسلوق مع سلطة أفوكادو',
        'دجاج بالفرن مع خضار',
        'شوربة كريمة الدجاج'
      ],
      'التحلية اليومية': [
        'شوكولاتة داكنة 85%',
        'مكسرات مشكلة',
        'جبنة كريمية مع توت',
        'مهلبية كيتو',
        'آيس كريم كيتو'
      ],
      'المشروبات اليومية': [
        'قهوة بالزبدة',
        'شاي أخضر',
        'ماء بالليمون',
        'شاي أعشاب',
        'ماء فوار'
      ],
      'أطعمة العمل': [
        'جبنة شيدر',
        'لوز',
        'زيتون',
        'بيض مسلوق',
        'شرائح خيار مع جبنة كريمية'
      ],
    },
    'نظام نباتي': {
      'الفطور': [
        'شوفان مع حليب نباتي',
        'توست أفوكادو',
        'سموثي أخضر',
        'بذور شيا مع حليب لوز',
        'فطائر الشوفان'
      ],
      'الغداء': [
        'سلطة كينوا',
        'شوربة عدس',
        'برجر نباتي',
        'باستا بالخضار',
        'كاري الخضار'
      ],
      'العشاء': [
        'سلطة كينوا',
        'شوربة خضار',
        'برجر نباتي',
        'باستا بالخضار',
        'كاري الخضار'
      ],
      'التحلية اليومية': [
        'فواكه طازجة',
        'شوكولاتة نباتية',
        'مهلبية نباتية',
        'سموثي فواكه',
        'مكسرات وفواكه مجففة'
      ],
      'المشروبات اليومية': [
        'حليب لوز',
        'عصير أخضر',
        'شاي أعشاب',
        'قهوة مع حليب نباتي',
        'ماء منقوع بالفواكه'
      ],
      'أطعمة العمل': [
        'جزر',
        'تفاح',
        'لوز',
        'زبادي نباتي',
        'خضار مشوي'
      ],
    },
    'نظام البحر الأبيض المتوسط': {
      'الفطور': [
        'زبادي يوناني مع عسل وجوز',
        'توست مع زيت زيتون وطماطم',
        'عجة بالخضار وزيتون',
        'شوفان مع توت ولوز',
        'سلطة فواكه مع جبنة قريش'
      ],
      'الغداء': [
        'سلطة يونانية مع تونة',
        'سمك مشوي مع خضار',
        'دجاج بالليمون والأعشاب',
        'شوربة عدس',
        'باستا بالخضار وزيت الزيتون'
      ],
      'العشاء': [
        'سلمون مشوي مع هليون',
        'سلطة كينوا مع خضار',
        'شوربة خضار',
        'دجاج بالزعتر والليمون',
        'مشروم محشو بالخضار'
      ],
      'التحلية اليومية': [
        'فواكه طازجة',
        'زبادي يوناني مع عسل',
        'بذور شيا',
        'سموثي أخضر',
        'مكسرات وفواكه مجففة'
      ],
      'المشروبات اليومية': [
        'شاي أخضر',
        'عصير رمان',
        'ماء بالليمون',
        'شاي أعشاب',
        'نبيذ أحمر (باعتدال)'
      ],
      'أطعمة العمل': [
        'زيتون',
        'جبنة فيتا',
        'خضار طازج',
        'مكسرات',
        'فواكه طازجة'
      ],
    },
    'نظام خالي من الجلوتين': {
      'الفطور': [
        'شوفان خال من الجلوتين مع فواكه',
        'بيض مع خبز خال من الجلوتين',
        'سموثي أخضر مع حليب لوز',
        'فطائر خالية من الجلوتين',
        'زبادي مع فواكه وبذور'
      ],
      'الغداء': [
        'سلطة كينوا مع دجاج',
        'أرز بني مع خضار سوتيه',
        'بطاطا حلوة محشية',
        'سلطة تونة مع خضار',
        'برجر خال من الجلوتين مع سلطة'
      ],
      'العشاء': [
        'سمك مشوي مع خضار',
        'دجاج بالفرن مع بطاطا',
        'شوربة خضار',
        'باستا خالية من الجلوتين',
        'لحم مشوي مع خضار'
      ],
      'التحلية اليومية': [
        'فواكه طازجة',
        'شوكولاتة خالية من الجلوتين',
        'مهلبية خالية من الجلوتين',
        'سموثي فواكه',
        'مكسرات وفواكه مجففة'
      ],
      'المشروبات اليومية': [
        'شاي أعشاب',
        'عصير فواكه طازج',
        'ماء منقوع بالفواكه',
        'قهوة',
        'حليب خال من اللاكتوز'
      ],
      'أطعمة العمل': [
        'فواكه طازجة',
        'مكسرات',
        'زبادي خال من اللاكتوز',
        'خضار طازج',
        'رقائق خالية من الجلوتين'
      ],
    },
    'نظام باليو (Paleo)': {
      'الفطور': [
        'عجة بالخضار وزيت جوز الهند',
        'سموثي أخضر مع حليب لوز',
        'بيض مسلوق مع أفوكادو',
        'فطائر باليو (لوز ودقيق جوز الهند)',
        'فواكه مع مكسرات'
      ],
      'الغداء': [
        'سلطة سيزر مع دجاج',
        'ستيك لحم مع خضار مشوي',
        'سلمون مشوي مع هليون',
        'برجر لحم بقري مع خس',
        'دجاج بالكاري مع قرنبيط أرز'
      ],
      'العشاء': [
        'سمك مشوي مع بروكلي',
        'لحم بقري مشوي مع بطاطا حلوة',
        'شوربة دجاج منزلية',
        'جمبري مع صلصة الثوم',
        'يخنة لحم مع خضار'
      ],
      'التحلية اليومية': [
        'فواكه طازجة',
        'تمر مع زبدة لوز',
        'مكسرات مشكلة',
        'شوكولاتة داكنة 85%',
        'موز مجمد مع زبدة فول سوداني'
      ],
      'المشروبات اليومية': [
        'ماء',
        'شاي أخضر',
        'ماء جوز الهند',
        'شاي أعشاب',
        'قهوة سوداء'
      ],
      'أطعمة العمل': [
        'جزر',
        'كرفس',
        'مكسرات',
        'بيض مسلوق',
        'شرائح تفاح مع زبدة لوز'
      ],
    },
    'نظام الصيام المتقطع': {
      'الفطور': [
        'شوفان مع توت وجوز',
        'عجة بالخضار وجبنة فيتا',
        'سموثي بروتين مع سبانخ وتوت',
        'زبادي يوناني مع بذور شيا',
        'توست أفوكادو مع بيض'
      ],
      'الغداء': [
        'سلطة سيزر مع دجاج',
        'سمك سلمون مع كينوا',
        'برجر لحم مع بطاطا حلوة',
        'دجاج بالكاري مع أرز بني',
        'سلطة تونة مع خضار'
      ],
      'العشاء': [
        'سمك مشوي مع بروكلي',
        'دجاج بالفرن مع بطاطا',
        'شوربة عدس',
        'لحم بقري مع خضار سوتيه',
        'جمبري مع معكرونة كوسة'
      ],
      'التحلية اليومية': [
        'فواكه طازجة',
        'شوكولاتة داكنة',
        'زبادي يوناني مع عسل',
        'بذور شيا مع حليب لوز',
        'مكسرات'
      ],
      'المشروبات اليومية': [
        'ماء',
        'شاي أخضر',
        'قهوة سوداء',
        'شاي أعشاب',
        'ماء بالليمون'
      ],
      'أطعمة العمل': [
        'جزر',
        'تفاح',
        'لوز',
        'بيض مسلوق',
        'زبادي يوناني'
      ],
    },
    'نظام داش (DASH)': {
      'الفطور': [
        'شوفان مع موز وجوز',
        'زبادي مع توت وبذور كتان',
        'توست قمح كامل مع زبدة فول سوداني',
        'عجة بالخضار',
        'سموثي أخضر'
      ],
      'الغداء': [
        'سلطة سيزر مع سلمون',
        'شوربة عدس',
        'دجاج مشوي مع بطاطا حلوة',
        'كينوا مع خضار',
        'سلطة تونة مع خبز قمح كامل'
      ],
      'العشاء': [
        'سمك مشوي مع بروكلي',
        'دجاج بالفرن مع خضار',
        'شوربة خضار',
        'لحم بقري مع أرز بني',
        'باستا قمح كامل مع صلصة طماطم'
      ],
      'التحلية اليومية': [
        'فواكه طازجة',
        'زبادي مع عسل',
        'بذور شيا',
        'سموثي فواكه',
        'مكسرات غير مملحة'
      ],
      'المشروبات اليومية': [
        'ماء',
        'شاي أخضر',
        'حليب قليل الدسم',
        'عصير فواكه طازج',
        'شاي أعشاب'
      ],
      'أطعمة العمل': [
        'جزر',
        'تفاح',
        'لوز غير مملح',
        'زبادي قليل الدسم',
        'خضار مشوي'
      ],
    },
    'نظام فصائل الدم': {
      'الفطور': [
        'عصير أخضر',
        'بيض مع خبز قمح كامل',
        'شوفان مع حليب لوز',
        'زبادي مع فواكه',
        'سموثي بروتين'
      ],
      'الغداء': [
        'سلطة سيزر مع دجاج',
        'سمك سلمون مع كينوا',
        'برجر لحم مع بطاطا',
        'دجاج بالكاري مع أرز بني',
        'سلطة تونة مع خضار'
      ],
      'العشاء': [
        'سمك مشوي مع بروكلي',
        'دجاج بالفرن مع بطاطا',
        'شوربة عدس',
        'لحم بقري مع خضار سوتيه',
        'جمبري مع معكرونة كوسة'
      ],
      'التحلية اليومية': [
        'فواكه طازجة',
        'شوكولاتة داكنة',
        'زبادي مع عسل',
        'بذور شيا مع حليب لوز',
        'مكسرات'
      ],
      'المشروبات اليومية': [
        'ماء',
        'شاي أخضر',
        'قهوة سوداء',
        'شاي أعشاب',
        'ماء بالليمون'
      ],
      'أطعمة العمل': [
        'جزر',
        'تفاح',
        'لوز',
        'بيض مسلوق',
        'زبادي يوناني'
      ],
    },
    'النظام الآسيوي الصحي': {
      'الفطور': [
        'كونجي (أرز معمر) مع بيض',
        'شوربة ميسو مع توفو',
        'سموثي أخضر مع فواكه استوائية',
        'بان كيك الأرز مع فواكه',
        'شاي أخضر مع مكسرات'
      ],
      'الغداء': [
        'سوشي مع شوربة ميسو',
        'كاري أخضر تايلاندي مع أرز جاسمين',
        'تيمبور خضار مع أرز بني',
        'بوكيه باول (سلطة هاواي)',
        'يخنة التوفو مع الخضار'
      ],
      'العشاء': [
        'ساشيمي سلمون مع أرز بني',
        'دجاج بالزنجبيل مع بروكلي',
        'شوربة رامين بالدجاج',
        'خضار سوتيه مع صلصة صويا',
        'سمك مشوي مع صلصة الثوم'
      ],
      'التحلية اليومية': [
        'مانجو لبن',
        'موز بالكاري',
        'فواكه استوائية طازجة',
        'مهلبية جوز الهند',
        'شاي أخضر مثلج'
      ],
      'المشروبات اليومية': [
        'شاي أخضر',
        'شاي ياسمين',
        'ماء جوز الهند',
        'عصير ليمون بالنعناع',
        'شاي زنجبيل'
      ],
      'أطعمة العمل': [
        'أعواد خيار وجزر',
        'مكسرات مشكلة',
        'سوشي نباتي',
        'أرز مقرمش',
        'فواكه مجففة'
      ],
    },
    'النظام الخالي من الألبان': {
      'الفطور': [
        'شوفان مع حليب لوز وتوت',
        'توست أفوكادو مع بيض',
        'سموثي أخضر مع حليب جوز الهند',
        'فطائر بالشوفان والموز',
        'بودينج بذور شيا مع حليب لوز'
      ],
      'الغداء': [
        'سلطة كينوا مع خضار',
        'برجر نباتي مع بطاطا حلوة',
        'شوربة عدس',
        'دجاج مشوي مع أرز بني',
        'سلطة تونة مع خضار'
      ],
      'العشاء': [
        'سمك سلمون مع بروكلي',
        'دجاج بالفرن مع بطاطا',
        'شوربة خضار',
        'لحم بقري مع أرز بني',
        'باستا خالية من الجلوتين مع صلصة طماطم'
      ],
      'التحلية اليومية': [
        'فواكه طازجة',
        'شوكولاتة خالية من الألبان',
        'مهلبية جوز الهند',
        'سموثي فواكه',
        'مكسرات وفواكه مجففة'
      ],
      'المشروبات اليومية': [
        'حليب لوز',
        'شاي أعشاب',
        'عصير فواكه طازج',
        'قهوة مع حليب لوز',
        'ماء منقوع بالفواكه'
      ],
      'أطعمة العمل': [
        'جزر',
        'تفاح',
        'لوز',
        'خضار طازج',
        'رقائق خالية من الألبان'
      ],
    },
    'النظام منخفض الكربوهيدرات': {
      'الفطور': [
        'بيض مع أفوكادو',
        'زبادي يوناني مع بذور',
        'سموثي أخضر مع زبدة فول سوداني',
        'عجة بالخضار',
        'جبنة قريش مع خيار'
      ],
      'الغداء': [
        'سلطة سيزر مع دجاج',
        'برجر لحم مع خس',
        'دجاج بالفرن مع خضار',
        'سمك سلمون مع بروكلي',
        'سلطة تونة مع خضار'
      ],
      'العشاء': [
        'ستيك لحم مع فطر',
        'دجاج بالزبدة مع بروكلي',
        'شوربة كريمة الدجاج',
        'جمبري مع صلصة الثوم',
        'لحم مفروم مع جبنة وخضار'
      ],
      'التحلية اليومية': [
        'توت طازج',
        'شوكولاتة داكنة 85%',
        'زبادي يوناني مع قرفة',
        'مكسرات',
        'جبنة كريمية مع توت'
      ],
      'المشروبات اليومية': [
        'ماء',
        'شاي أخضر',
        'قهوة سوداء',
        'شاي أعشاب',
        'ماء فوار'
      ],
      'أطعمة العمل': [
        'جبنة شيدر',
        'بيض مسلوق',
        'زيتون',
        'خيار',
        'كرفس مع زبدة فول سوداني'
      ],
    },
    'النظام عالي البروتين': {
      'الفطور': [
        'عجة بيض مع جبنة وخضار',
        'شوفان مع بروتين مصل اللبن',
        'سموثي بروتين مع موز وزبدة فول سوداني',
        'بيض مسلوق مع أفوكادو',
        'لبنة مع خبز قمح كامل'
      ],
      'الغداء': [
        'صدر دجاج مشوي مع أرز بني',
        'ستيك لحم مع بطاطا حلوة',
        'تونة مع خبز قمح كامل',
        'برجر لحم مع بطاطا',
        'دجاج بالكاري مع أرز'
      ],
      'العشاء': [
        'سمك سلمون مع بروكلي',
        'دجاج بالفرن مع بطاطا',
        'لحم بقري مع أرز بني',
        'جمبري مع معكرونة كاملة',
        'ديك رومي مع بطاطا حلوة'
      ],
      'التحلية اليومية': [
        'زبادي يوناني مع عسل',
        'بروتين بار',
        'جبنة قريش مع فواكه',
        'سموثي بروتين',
        'بيض مسلوق'
      ],
      'المشروبات اليومية': [
        'حليب',
        'شاي أخضر',
        'قهوة',
        'عصير بروتين',
        'ماء'
      ],
      'أطعمة العمل': [
        'بيض مسلوق',
        'جبنة',
        'زبادي يوناني',
        'مكسرات',
        'شرائح ديك رومي'
      ],
    },
    'النظام النباتي الكامل (Plant-based)': {
      'الفطور': [
        'شوفان مع حليب نباتي',
        'توست أفوكادو',
        'سموثي أخضر',
        'بذور شيا مع حليب لوز',
        'فطائر الشوفان'
      ],
      'الغداء': [
        'سلطة كينوا',
        'شوربة عدس',
        'برجر نباتي',
        'باستا بالخضار',
        'كاري الخضار'
      ],
      'العشاء': [
        'سلطة كينوا',
        'شوربة خضار',
        'برجر نباتي',
        'باستا بالخضار',
        'كاري الخضار'
      ],
      'التحلية اليومية': [
        'فواكه طازجة',
        'شوكولاتة نباتية',
        'مهلبية نباتية',
        'سموثي فواكه',
        'مكسرات وفواكه مجففة'
      ],
      'المشروبات اليومية': [
        'حليب لوز',
        'عصير أخضر',
        'شاي أعشاب',
        'قهوة مع حليب نباتي',
        'ماء منقوع بالفواكه'
      ],
      'أطعمة العمل': [
        'جزر',
        'تفاح',
        'لوز',
        'زبادي نباتي',
        'خضار مشوي'
      ],
    },
    'نظام فودماب (لحساسية القولون)': {
      'الفطور': [
        'شوفان مع حليب خالي من اللاكتوز',
        'بيض مع خبز خال من الجلوتين',
        'سموثي مع موز وحليب لوز',
        'توست خال من الجلوتين مع زبدة الفول السوداني',
        'فواكه منخفضة الفودماب مع زبادي خالي من اللاكتوز'
      ],
      'الغداء': [
        'سلطة كينوا مع دجاج',
        'أرز بني مع خضار مسلوقة',
        'بطاطا حلوة محشية',
        'سمك مشوي مع جزر',
        'دجاج بالفرن مع بطاطا'
      ],
      'العشاء': [
        'سمك مشوي مع بروكلي',
        'دجاج مع أرز أبيض',
        'شوربة خضار',
        'لحم بقري مع بطاطا',
        'جمبري مع معكرونة خالية من الجلوتين'
      ],
      'التحلية اليومية': [
        'موز',
        'برتقال',
        'فراولة',
        'شوكولاتة داكنة',
        'بسكويت خال من الجلوتين'
      ],
      'المشروبات اليومية': [
        'شاي أخضر',
        'قهوة سوداء',
        'ماء',
        'شاي نعناع',
        'عصير برتقال طازج'
      ],
      'أطعمة العمل': [
        'موز',
        'برتقال',
        'بسكويت خال من الجلوتين',
        'جزر',
        'خيار'
      ],
    }
  };

  Map<String, Map<String, Map<String, Map<String, Map<String, String?>?>>>>
  familyMonthlyMeals = {};
  List<String> familyMembers = [
    'أحمد',
    'فاطمة',
    'محمد',
    'عائشة',
    'الجميع'
  ]; // Added 'الجميع'
  String selectedMember = 'أحمد';
  String selectedFoodCategory = 'الطعام المصري';
  int selectedTabIndex = 0;
  String? selectedWeek;
  String? selectedDay;
  String? selectedMealType;
  String? selectedMeal;
  String? selectedSize;
  bool isSelectionMode = false;
  VideoPlayerController? _videoController;
  bool _isVideoInitialized = false;

  // Search and Filter state variables
  TextEditingController _searchController = TextEditingController();
  String _searchTerm = '';
  String? _selectedFilterCategory; // For filtering by meal category

  // Animation Controllers
  late AnimationController _fabAnimationController;

  @override
  void initState() {
    super.initState();
    _initializeFamilyMeals();
    _loadFoodTypes();
    _loadUserPreferences();

    _searchController.addListener(() {
      setState(() {
        _searchTerm = _searchController.text;
      });
    });

    _fabAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
  }

  @override
  void dispose() {
    _videoController?.dispose();
    _foodNameController.dispose();
    _foodCategoryController.dispose();
    _foodDescriptionController.dispose();
    _searchController.dispose();
    _fabAnimationController.dispose();
    super.dispose();
  }

  // ... (Keep existing methods: _initializeFamilyMeals, _loadFoodTypes, _loadUserPreferences, _saveUserPreferences)
  // ... (Keep existing methods: _calculateTotalIngredientsForMember, _showPreparationDetails, _showPlanAndIngredients)
  // ... (Keep existing methods: _buildMonthlyPlanTab, _buildIngredientsSummaryTab, _buildIngredientsStatsTab)
  // ... (Keep existing methods: _buildStatItem, _getMostUsedIngredient, _getSelectedMealsCount, _getCompletionPercentage, _shareIngredientsList)
  // ... (Keep existing methods: _showErrorSnackBar, _showInfoSnackBar, _showSuccessSnackBar, _clearMealCategory)
  // ... (Keep existing methods: _selectMealForDay, _showSizeSelectionDialog, _buildSizeDescription, _getSizeColor, _getSizeIcon)
  // ... (Keep existing methods: _assignMealToDay, _removeMealFromDay, _addNewFoodType, _loadMoreFoodTypes)

  // Enhanced _buildItemCard with more details and animations
  Widget _buildItemCard(String memberId, String week, String mealType,
      String day, Map<String, String?>? item) {
    bool isSelected = item != null && item['meal'] != null;
    String mealName = isSelected ? item!['meal']! : '';
    String? size = isSelected ? item!['size'] : null;
    String? mealImageUrl =
    isSelected && preparationMethods.containsKey(mealName)
        ? preparationMethods[mealName]!['imageUrl']
        : null;

    return Card(
      elevation: isSelected ? 8 : 3,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: isSelected
              ? Theme.of(context).primaryColor
              : Colors.grey.shade300,
          width: isSelected ? 2.5 : 1.5,
        ),
      ),
      child: InkWell(
        onTap: () => _selectMealForDay(memberId, week, mealType, day),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    if (isSelected && mealImageUrl != null)
                      ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: Image.network(
                          mealImageUrl,
                          height: 50,
                          width: double.infinity,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) => Icon(
                              Icons.broken_image,
                              size: 40,
                              color: Colors.grey.shade400),
                        ),
                      )
                    else
                      Container(
                        padding: const EdgeInsets.all(10),
                        decoration: BoxDecoration(
                          color: isSelected
                              ? Theme.of(context).primaryColor.withOpacity(0.1)
                              : Colors.grey.shade200,
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          isSelected
                              ? Icons.restaurant_menu
                              : Icons.add_circle_outline,
                          color: isSelected
                              ? Theme.of(context).primaryColor
                              : Colors.grey.shade600,
                          size: 24,
                        ),
                      ),
                    const SizedBox(height: 6),
                    Text(
                      day,
                      style: TextStyle(
                        fontSize: 13,
                        fontWeight: FontWeight.bold,
                        color: isSelected
                            ? Theme.of(context).primaryColorDark
                            : Colors.grey.shade700,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    if (isSelected) ...[
                      const SizedBox(height: 4),
                      Text(
                        mealName,
                        style: const TextStyle(
                          fontSize: 11,
                          fontWeight: FontWeight.w500,
                        ),
                        textAlign: TextAlign.center,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      if (size != null) ...[
                        const SizedBox(height: 2),
                        Chip(
                          label: Text(size,
                              style:
                              TextStyle(fontSize: 9, color: Colors.white)),
                          backgroundColor: _getSizeColor(size),
                          padding: EdgeInsets.zero,
                          materialTapTargetSize:
                          MaterialTapTargetSize.shrinkWrap,
                        ),
                      ],
                    ],
                  ],
                ),
              ),
              if (isSelected)
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    IconButton(
                      icon: Icon(Icons.info_outline,
                          color: Colors.blue.shade700, size: 20),
                      onPressed: () =>
                          _showPreparationDetails(mealName, size ?? 'متوسط'),
                      tooltip: 'تفاصيل التحضير',
                    ),
                    IconButton(
                      icon: Icon(Icons.delete_outline,
                          color: Colors.red.shade700, size: 20),
                      onPressed: () =>
                          _removeMealFromDay(memberId, week, mealType, day),
                      tooltip: 'حذف الوجبة',
                    ),
                  ],
                ),
            ],
          ),
        ),
      ),
    );
  }

  // Enhanced _showMealSelectionDialog with search and filter
  void _showMealSelectionDialog(
      String memberId, String week, String mealType, String day) {
    List<String> allMealsForCategory =
        (foodPreferences[selectedFoodCategory]?[mealType] as List<dynamic>?)
            ?.cast<String>() ??
            [];
    List<String> filteredMeals = allMealsForCategory.where((meal) {
      final mealLower = meal.toLowerCase();
      final searchTermLower = _searchTerm.toLowerCase();
      return mealLower.contains(searchTermLower);
    }).toList();

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          // To update search results within the dialog
          builder: (context, setDialogState) {
            return Dialog(
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20)),
              insetPadding: const EdgeInsets.all(16),
              child: Container(
                padding: const EdgeInsets.all(16),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      'اختيار وجبة لـ $day - $mealType',
                      style: const TextStyle(
                          fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 16),
                    // Search Bar
                    TextField(
                      controller: _searchController,
                      decoration: InputDecoration(
                        hintText: 'ابحث عن وجبة...',
                        prefixIcon: const Icon(Icons.search),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        filled: true,
                        fillColor: Colors.grey.shade100,
                      ),
                      onChanged: (value) {
                        setDialogState(() {
                          _searchTerm = value;
                          filteredMeals = allMealsForCategory.where((meal) {
                            final mealLower = meal.toLowerCase();
                            final searchTermLower = _searchTerm.toLowerCase();
                            return mealLower.contains(searchTermLower);
                          }).toList();
                        });
                      },
                    ),
                    const SizedBox(height: 10),
                    // Filter (Example - can be expanded)
                    // DropdownButton<String>(...)
                    Expanded(
                      child: filteredMeals.isEmpty
                          ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Lottie.asset(
                                'assets/animations/not_found.json',
                                width: 150,
                                height: 150), // Add Lottie animation
                            const Text('لا توجد وجبات تطابق بحثك',
                                style: TextStyle(
                                    fontSize: 16, color: Colors.grey)),
                          ],
                        ),
                      )
                          : ListView.builder(
                        shrinkWrap: true,
                        itemCount: filteredMeals.length,
                        itemBuilder: (context, index) {
                          String meal = filteredMeals[index];
                          bool hasIngredients = ingredients[meal] != null;
                          String? mealImageUrl;
                          if (preparationMethods[meal]
                          is Map<String, dynamic>) {
                            mealImageUrl = (preparationMethods[meal]
                            as Map<String, dynamic>)['imageUrl']
                            as String?;
                          }

                          return Card(
                            elevation: 2,
                            margin:
                            const EdgeInsets.symmetric(vertical: 5),
                            shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(10)),
                            child: ListTile(
                              leading: mealImageUrl != null
                                  ? CircleAvatar(
                                  backgroundImage:
                                  NetworkImage(mealImageUrl))
                                  : CircleAvatar(
                                backgroundColor: hasIngredients
                                    ? Colors.green.shade100
                                    : Colors.grey.shade200,
                                child: Icon(
                                  hasIngredients
                                      ? Icons.restaurant
                                      : Icons.help_outline,
                                  color: hasIngredients
                                      ? Colors.green.shade700
                                      : Colors.grey.shade600,
                                ),
                              ),
                              title: Text(meal,
                                  style: const TextStyle(
                                      fontWeight: FontWeight.w500)),
                              subtitle: hasIngredients
                                  ? Text(
                                  '${ingredients[meal]?.length ?? 0} مكونات')
                                  : const Text('لا توجد تفاصيل مكونات'),
                              trailing: const Icon(
                                  Icons.arrow_forward_ios,
                                  size: 16),
                              onTap: () {
                                Navigator.of(context).pop();
                                _searchController
                                    .clear(); // Clear search term
                                _showSizeSelectionDialog(
                                    memberId, week, mealType, day, meal);
                              },
                            ),
                          );
                        },
                      ),
                    ),
                    TextButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                        _searchController.clear();
                      },
                      child: const Text('إلغاء',
                          style: TextStyle(color: Colors.redAccent)),
                    )
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  // Main build method with enhanced UI
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'مخطط الوجبات الشهري الذكي',
          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 22),
        ),
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                Theme.of(context).primaryColor,
                Theme.of(context).primaryColorDark
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
        ),
        elevation: 10,
        actions: [
          IconButton(
            icon: const Icon(Icons.insights, size: 28),
            onPressed: _showPlanAndIngredients,
            tooltip: 'عرض الخطة والمكونات',
          ),
          IconButton(
            icon: const Icon(Icons.ios_share, size: 28),
            onPressed: () {
              Map<String, double> totalIngredients =
              _calculateTotalIngredientsForMember(selectedMember);
              _shareIngredientsList(totalIngredients);
            },
            tooltip: 'مشاركة قائمة المكونات',
          ),
        ],
      ),
      body: Column(
        children: [
          _buildControlBar(),
          Expanded(
            child: AnimationLimiter(
              child: ListView.builder(
                padding: const EdgeInsets.all(8),
                itemCount: weeks.length,
                itemBuilder: (context, index) {
                  String week = weeks[index];
                  return WeekSection(
                    memberId: selectedMember,
                    week: week,
                    weekColors: weekColors,
                    mealCategories: mealCategories,
                    days: days,
                    familyMonthlyMeals: familyMonthlyMeals,
                    onClearMealCategory: _clearMealCategory,
                    buildItemCard: _buildItemCard,
                  );
                },
              ),
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _showPlanAndIngredients,
        backgroundColor: Theme.of(context).colorScheme.secondary,
        icon: const Icon(Icons.menu_book_outlined, color: Colors.white),
        label: const Text('عرض الخطة',
            style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold)),
        elevation: 8,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
    );
  }

  // Enhanced _buildControlBar
  Widget _buildControlBar() {
    return Material(
      elevation: 4,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        color: Colors.white,
        child: Column(
          children: [
            Row(
              children: [
                Icon(Icons.group_outlined,
                    color: Theme.of(context).primaryColor, size: 28),
                const SizedBox(width: 12),
                Expanded(
                  child: DropdownButtonFormField<String>(
                    value: selectedMember,
                    decoration: InputDecoration(
                      labelText: 'اختر العضو',
                      border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(10)),
                      contentPadding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 8),
                      filled: true,
                      fillColor: Colors.grey.shade50,
                    ),
                    items: familyMembers.map((member) {
                      return DropdownMenuItem(
                        value: member,
                        child:
                        Text(member, style: const TextStyle(fontSize: 15)),
                      );
                    }).toList(),
                    onChanged: (value) {
                      if (value != null) {
                        setState(() {
                          selectedMember = value;
                        });
                        _saveUserPreferences();
                      }
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Icon(Icons.restaurant_outlined,
                    color: Theme.of(context).primaryColor, size: 28),
                const SizedBox(width: 12),
                Expanded(
                  child: DropdownButtonFormField<String>(
                    value: selectedFoodCategory,
                    decoration: InputDecoration(
                      labelText: 'اختر نوع الطعام',
                      border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(10)),
                      contentPadding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 8),
                      filled: true,
                      fillColor: Colors.grey.shade50,
                    ),
                    items: foodPreferences.keys.map((category) {
                      return DropdownMenuItem(
                        value: category,
                        child: Text(category,
                            style: const TextStyle(fontSize: 14)),
                      );
                    }).toList(),
                    onChanged: (value) {
                      if (value != null) {
                        setState(() {
                          selectedFoodCategory = value;
                        });
                        _saveUserPreferences();
                      }
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // تهيئة وجبات العائلة
  void _initializeFamilyMeals() {
    for (String member in familyMembers) {
      familyMonthlyMeals[member] = {};
      for (String week in weeks) {
        familyMonthlyMeals[member]![week] = {};
        for (String mealType in mealCategories) {
          familyMonthlyMeals[member]![week]![mealType] = {};
          for (String day in days) {
            familyMonthlyMeals[member]![week]![mealType]![day] = null;
          }
        }
      }
    }
  }

  // تحميل أنواع الطعام
  Future<void> _loadFoodTypes() async {
    setState(() {
      _isLoadingFoodTypes = true;
    });

    try {
      await Future.delayed(const Duration(milliseconds: 500));

      List<Map<String, dynamic>> allFoodTypes = [];
      foodPreferences.forEach((category, meals) {
        meals.forEach((mealType, foodList) {
          for (String food in foodList) {
            allFoodTypes.add({
              'id': '${category}_${mealType}_$food',
              'name': food,
              'category': category,
              'mealType': mealType,
              'description': 'وصف $food',
              'isAvailable': true,
            });
          }
        });
      });

      setState(() {
        _foodTypes = allFoodTypes;
        _isLoadingFoodTypes = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingFoodTypes = false;
      });
      _showErrorSnackBar('خطأ في تحميل أنواع الطعام: $e');
    }
  }

  // تحميل تفضيلات المستخدم
  Future<void> _loadUserPreferences() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedMember = prefs.getString('selectedMember');
      final savedCategory = prefs.getString('selectedFoodCategory');

      if (savedMember != null && familyMembers.contains(savedMember)) {
        setState(() {
          selectedMember = savedMember;
        });
      }

      if (savedCategory != null && foodPreferences.containsKey(savedCategory)) {
        setState(() {
          selectedFoodCategory = savedCategory;
        });
      }
    } catch (e) {
      print('خطأ في تحميل التفضيلات: $e');
    }
  }

  // حفظ تفضيلات المستخدم
  Future<void> _saveUserPreferences() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('selectedMember', selectedMember);
      await prefs.setString('selectedFoodCategory', selectedFoodCategory);
    } catch (e) {
      print('خطأ في حفظ التفضيلات: $e');
    }
  }

  // دالة حساب إجمالي المكونات للشهر للعضو المحدد
  Map<String, double> _calculateTotalIngredientsForMember(String memberId) {
    Map<String, double> totalIngredients = {};

    // التكرار عبر جميع الأسابيع والأيام وأنواع الوجبات للعضو المحدد
    familyMonthlyMeals[memberId]?.forEach((week, weekData) {
      weekData.forEach((mealType, mealTypeData) {
        mealTypeData.forEach((day, mealData) {
          if (mealData != null &&
              mealData['meal'] != null &&
              mealData['size'] != null) {
            String mealName = mealData['meal']!;
            String size = mealData['size']!;

            // التحقق من وجود المكونات لهذه الوجبة
            if (ingredients.containsKey(mealName)) {
              ingredients[mealName]!.forEach((ingredientName, sizeQuantities) {
                if (sizeQuantities.containsKey(size)) {
                  double quantity = sizeQuantities[size]!;

                  // إضافة الكمية إلى الإجمالي
                  if (totalIngredients.containsKey(ingredientName)) {
                    totalIngredients[ingredientName] =
                        totalIngredients[ingredientName]! + quantity;
                  } else {
                    totalIngredients[ingredientName] = quantity;
                  }
                }
              });
            }
          }
        });
      });
    });

    return totalIngredients;
  }

  // دالة عرض تفاصيل التحضير مع المكونات والكميات
  void _showPreparationDetails(String mealName, String size) {
    final preparation = preparationMethods[mealName];
    final mealIngredientsData = ingredients[mealName]; // Renamed for clarity

    if (preparation == null) {
      _showErrorSnackBar('لا توجد تفاصيل تحضير لهذه الوجبة');
      return;
    }

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          shape:
          RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
          insetPadding: const EdgeInsets.all(16),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(20),
            child: Container(
              constraints: const BoxConstraints(maxHeight: 650, maxWidth: 550),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // رأس النافذة
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          Theme.of(context).primaryColor,
                          Theme.of(context).primaryColorDark
                        ],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                    ),
                    child: Row(
                      children: [
                        if (preparation['imageUrl'] != null)
                          Padding(
                            padding: const EdgeInsets.only(right: 12.0),
                            child: CircleAvatar(
                              radius: 24,
                              backgroundImage:
                              NetworkImage(preparation['imageUrl']!),
                            ),
                          ),
                        Expanded(
                          child: Text(
                            'طريقة تحضير $mealName',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        IconButton(
                          icon: const Icon(Icons.close,
                              color: Colors.white, size: 28),
                          onPressed: () => Navigator.of(context).pop(),
                        ),
                      ],
                    ),
                  ),

                  // محتوى النافذة
                  Expanded(
                    child: DefaultTabController(
                      length: 3,
                      child: Column(
                        children: [
                          Material(
                            color: Colors.white,
                            elevation: 2,
                            child: TabBar(
                              labelColor: Theme.of(context).primaryColor,
                              unselectedLabelColor: Colors.grey.shade600,
                              indicatorColor: Theme.of(context).primaryColor,
                              indicatorWeight: 3,
                              tabs: const [
                                Tab(
                                    icon: Icon(Icons.kitchen_outlined),
                                    text: 'المكونات'),
                                Tab(
                                    icon: Icon(Icons.list_alt_outlined),
                                    text: 'الخطوات'),
                                Tab(
                                    icon: Icon(Icons.play_circle_outline),
                                    text: 'فيديو'),
                              ],
                            ),
                          ),
                          Expanded(
                            child: TabBarView(
                              children: [
                                // تبويب المكونات مع الكميات
                                Padding(
                                  padding: const EdgeInsets.all(16),
                                  child: Column(
                                    crossAxisAlignment:
                                    CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        'المكونات المطلوبة (حجم $size):',
                                        style: TextStyle(
                                          fontSize: 17,
                                          fontWeight: FontWeight.bold,
                                          color: Theme.of(context)
                                              .primaryColorDark,
                                        ),
                                      ),
                                      const SizedBox(height: 12),
                                      Expanded(
                                        child: ListView.builder(
                                          itemCount:
                                          preparation['ingredients'].length,
                                          itemBuilder: (context, index) {
                                            String ingredientName =
                                            preparation['ingredients']
                                            [index];
                                            double? quantity;
                                            String unit = 'كجم';

                                            if (mealIngredientsData != null &&
                                                mealIngredientsData.containsKey(
                                                    ingredientName) &&
                                                mealIngredientsData[
                                                ingredientName]!
                                                    .containsKey(size)) {
                                              quantity = mealIngredientsData[
                                              ingredientName]![size];

                                              if (ingredientName == 'بيض') {
                                                unit = 'حبة';
                                              } else if (ingredientName
                                                  .contains('خبز') ||
                                                  ingredientName
                                                      .contains('رغيف')) {
                                                unit = 'رغيف';
                                              } else if (quantity! < 0.01 &&
                                                  quantity > 0) {
                                                unit = 'جرام';
                                                quantity = quantity * 1000;
                                              } else if (quantity < 1 &&
                                                  quantity > 0) {
                                                unit = 'جرام';
                                                quantity = quantity * 1000;
                                              }
                                            }

                                            return Card(
                                              elevation: 1,
                                              margin:
                                              const EdgeInsets.symmetric(
                                                  vertical: 5),
                                              shape: RoundedRectangleBorder(
                                                  borderRadius:
                                                  BorderRadius.circular(8)),
                                              child: ListTile(
                                                leading: CircleAvatar(
                                                  backgroundColor:
                                                  Theme.of(context)
                                                      .primaryColor
                                                      .withOpacity(0.1),
                                                  child: Text(
                                                    '${index + 1}',
                                                    style: TextStyle(
                                                      color: Theme.of(context)
                                                          .primaryColorDark,
                                                      fontWeight:
                                                      FontWeight.bold,
                                                    ),
                                                  ),
                                                ),
                                                title: Text(
                                                  ingredientName,
                                                  style: const TextStyle(
                                                      fontWeight:
                                                      FontWeight.w500,
                                                      fontSize: 15),
                                                ),
                                                trailing: quantity != null
                                                    ? Chip(
                                                  label: Text(
                                                    '${quantity.toStringAsFixed(quantity % 1 == 0 ? 0 : 1)} $unit',
                                                    style:
                                                    const TextStyle(
                                                        color: Colors
                                                            .white,
                                                        fontSize: 12),
                                                  ),
                                                  backgroundColor: Colors
                                                      .orange.shade700,
                                                  padding:
                                                  const EdgeInsets
                                                      .symmetric(
                                                      horizontal: 8,
                                                      vertical: 2),
                                                )
                                                    : null,
                                              ),
                                            );
                                          },
                                        ),
                                      ),
                                    ],
                                  ),
                                ),

                                // تبويب خطوات التحضير
                                Padding(
                                  padding: const EdgeInsets.all(16),
                                  child: Column(
                                    crossAxisAlignment:
                                    CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        'خطوات التحضير:',
                                        style: TextStyle(
                                          fontSize: 17,
                                          fontWeight: FontWeight.bold,
                                          color: Theme.of(context)
                                              .primaryColorDark,
                                        ),
                                      ),
                                      const SizedBox(height: 12),
                                      Expanded(
                                        child: ListView.builder(
                                          itemCount:
                                          preparation['steps'].length,
                                          itemBuilder: (context, index) {
                                            return Card(
                                              elevation: 1,
                                              margin:
                                              const EdgeInsets.symmetric(
                                                  vertical: 5),
                                              shape: RoundedRectangleBorder(
                                                  borderRadius:
                                                  BorderRadius.circular(8)),
                                              child: ListTile(
                                                leading: CircleAvatar(
                                                  backgroundColor:
                                                  Colors.blue.shade100,
                                                  child: Text(
                                                    '${index + 1}',
                                                    style: TextStyle(
                                                      color:
                                                      Colors.blue.shade700,
                                                      fontWeight:
                                                      FontWeight.bold,
                                                    ),
                                                  ),
                                                ),
                                                title: Text(
                                                  preparation['steps'][index],
                                                  style: const TextStyle(
                                                      fontSize: 14,
                                                      height: 1.4),
                                                ),
                                              ),
                                            );
                                          },
                                        ),
                                      ),
                                    ],
                                  ),
                                ),

                                // تبويب الفيديو
                                Center(
                                  child: Padding(
                                    padding: const EdgeInsets.all(20.0),
                                    child: Column(
                                      mainAxisAlignment:
                                      MainAxisAlignment.center,
                                      children: [
                                        Lottie.asset(
                                            'assets/animations/video_play.json',
                                            width: 200,
                                            height:
                                            200), // Add Lottie animation
                                        const SizedBox(height: 20),
                                        Text(
                                          'فيديو توضيحي لطريقة التحضير',
                                          style: TextStyle(
                                            fontSize: 18,
                                            color: Colors.grey.shade700,
                                            fontWeight: FontWeight.w600,
                                          ),
                                          textAlign: TextAlign.center,
                                        ),
                                        const SizedBox(height: 12),
                                        Text(
                                          preparation['videoUrl'],
                                          style: TextStyle(
                                            fontSize: 13,
                                            color: Colors.grey.shade500,
                                          ),
                                        ),
                                        const SizedBox(height: 24),
                                        ElevatedButton.icon(
                                          onPressed: () {
                                            _showInfoSnackBar(
                                                'سيتم إضافة تشغيل الفيديو قريباً');
                                            // Add video playing logic here (e.g., using url_launcher or an in-app video player)
                                          },
                                          icon: const Icon(
                                              Icons.play_arrow_rounded,
                                              size: 28),
                                          label: const Text('تشغيل الفيديو',
                                              style: TextStyle(fontSize: 16)),
                                          style: ElevatedButton.styleFrom(
                                            backgroundColor:
                                            Theme.of(context).primaryColor,
                                            foregroundColor: Colors.white,
                                            padding: const EdgeInsets.symmetric(
                                                horizontal: 30, vertical: 12),
                                            shape: RoundedRectangleBorder(
                                                borderRadius:
                                                BorderRadius.circular(12)),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  // عرض نافذة الخطة والمكونات
  void _showPlanAndIngredients() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          shape:
          RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
          insetPadding: const EdgeInsets.all(16),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(20),
            child: Container(
              constraints: const BoxConstraints(maxHeight: 700, maxWidth: 600),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // رأس النافذة
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          Theme.of(context).colorScheme.secondary,
                          Theme.of(context)
                              .colorScheme
                              .secondary
                              .withOpacity(0.8)
                        ],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                    ),
                    child: Row(
                      children: [
                        const Icon(Icons.menu_book_outlined,
                            color: Colors.white, size: 30),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Text(
                            'خطة الوجبات والمكونات - $selectedMember',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 19,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        IconButton(
                          icon: const Icon(Icons.close,
                              color: Colors.white, size: 28),
                          onPressed: () => Navigator.of(context).pop(),
                        ),
                      ],
                    ),
                  ),

                  // محتوى النافذة
                  Expanded(
                    child: DefaultTabController(
                      length: 3,
                      child: Column(
                        children: [
                          Material(
                            color: Colors.white,
                            elevation: 2,
                            child: TabBar(
                              labelColor:
                              Theme.of(context).colorScheme.secondary,
                              unselectedLabelColor: Colors.grey.shade600,
                              indicatorColor:
                              Theme.of(context).colorScheme.secondary,
                              indicatorWeight: 3,
                              tabs: const [
                                Tab(
                                    icon: Icon(Icons.calendar_today_outlined),
                                    text: 'الخطة'),
                                Tab(
                                    icon: Icon(Icons.shopping_basket_outlined),
                                    text: 'المكونات'),
                                Tab(
                                    icon: Icon(Icons.bar_chart_outlined),
                                    text: 'الإحصائيات'),
                              ],
                            ),
                          ),
                          Expanded(
                            child: TabBarView(
                              children: [
                                _buildMonthlyPlanTab(),
                                _buildIngredientsSummaryTab(),
                                _buildIngredientsStatsTab(),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  // بناء تبويب الخطة الشهرية
  Widget _buildMonthlyPlanTab() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'الخطة الشهرية للعضو: $selectedMember',
            style: TextStyle(
              fontSize: 17,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.secondary,
            ),
          ),
          const SizedBox(height: 12),
          Expanded(
            child: ListView.builder(
              itemCount: weeks.length,
              itemBuilder: (context, weekIndex) {
                String week = weeks[weekIndex];
                return Card(
                  elevation: 2,
                  margin: const EdgeInsets.symmetric(vertical: 6),
                  shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10)),
                  child: ExpansionTile(
                    leading: CircleAvatar(
                      backgroundColor: weekColors[week]!.withOpacity(0.2),
                      child: Text(week.substring(week.length - 1),
                          style: TextStyle(
                              color: weekColors[week],
                              fontWeight: FontWeight.bold)),
                    ),
                    title: Text(
                      week,
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: weekColors[week],
                        fontSize: 16,
                      ),
                    ),
                    childrenPadding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    children: mealCategories.map((mealType) {
                      return Padding(
                        padding: const EdgeInsets.symmetric(vertical: 6.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              mealType,
                              style: const TextStyle(
                                fontWeight: FontWeight.w600,
                                fontSize: 15,
                              ),
                            ),
                            const SizedBox(height: 6),
                            Wrap(
                              spacing: 8,
                              runSpacing: 6,
                              children: days.map((day) {
                                var mealData = familyMonthlyMeals[
                                selectedMember]![week]![mealType]![day];
                                String displayText =
                                    mealData?['meal'] ?? 'فارغ';
                                String size = mealData?['size'] ?? '';

                                return Chip(
                                  avatar: mealData != null
                                      ? Icon(Icons.check_circle,
                                      color: Colors.white, size: 16)
                                      : Icon(Icons.radio_button_unchecked,
                                      color: Colors.grey.shade700,
                                      size: 16),
                                  label: Text(
                                    '$day: $displayText${size.isNotEmpty ? " ($size)" : ""}',
                                    style: TextStyle(
                                      fontSize: 10,
                                      color: mealData != null
                                          ? Colors.white
                                          : Colors.grey.shade700,
                                    ),
                                  ),
                                  backgroundColor: mealData != null
                                      ? Colors.green.shade400
                                      : Colors.grey.shade200,
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 8, vertical: 4),
                                );
                              }).toList(),
                            ),
                            if (mealCategories.last != mealType)
                              const Divider(height: 20, thickness: 0.5),
                          ],
                        ),
                      );
                    }).toList(),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  // بناء تبويب ملخص المكونات
  Widget _buildIngredientsSummaryTab() {
    Map<String, double> totalIngredients =
    _calculateTotalIngredientsForMember(selectedMember);

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'إجمالي المكونات المطلوبة للشهر',
                style: TextStyle(
                  fontSize: 17,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).colorScheme.secondary,
                ),
              ),
              IconButton(
                icon: Icon(Icons.ios_share,
                    color: Theme.of(context).colorScheme.secondary),
                onPressed: () => _shareIngredientsList(totalIngredients),
                tooltip: 'مشاركة قائمة المكونات',
              ),
            ],
          ),
          const SizedBox(height: 12),
          if (totalIngredients.isEmpty)
            Expanded(
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Lottie.asset('assets/animations/empty_cart.json',
                        width: 200, height: 200), // Add Lottie animation
                    const SizedBox(height: 20),
                    const Text(
                      'لا توجد وجبات محددة بعد',
                      style: TextStyle(fontSize: 18, color: Colors.grey),
                    ),
                    const Text(
                      'قم بإضافة وجبات لرؤية قائمة المكونات المطلوبة',
                      style: TextStyle(fontSize: 14, color: Colors.grey),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            )
          else
            Expanded(
              child: ListView.builder(
                itemCount: totalIngredients.length,
                itemBuilder: (context, index) {
                  String ingredient = totalIngredients.keys.elementAt(index);
                  double quantity = totalIngredients[ingredient]!;

                  String unit = 'كجم';
                  double displayQuantity = quantity;

                  if (ingredient == 'بيض') {
                    unit = 'حبة';
                  } else if (ingredient.contains('خبز') ||
                      ingredient.contains('رغيف')) {
                    unit = 'رغيف';
                  } else if (quantity < 1 && quantity > 0) {
                    unit = 'جرام';
                    displayQuantity = quantity * 1000;
                  }

                  return Card(
                    elevation: 1.5,
                    margin: const EdgeInsets.symmetric(vertical: 5),
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8)),
                    child: ListTile(
                      leading: CircleAvatar(
                        backgroundColor: Colors.orange.shade100,
                        child: Text(
                          '${index + 1}',
                          style: TextStyle(
                            color: Colors.orange.shade800,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      title: Text(
                        ingredient,
                        style: const TextStyle(
                            fontWeight: FontWeight.w500, fontSize: 15),
                      ),
                      trailing: Chip(
                        label: Text(
                          '${displayQuantity.toStringAsFixed(displayQuantity % 1 == 0 ? 0 : 1)} $unit',
                          style: const TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              fontSize: 13),
                        ),
                        backgroundColor: Colors.green.shade600,
                        padding: const EdgeInsets.symmetric(
                            horizontal: 10, vertical: 4),
                      ),
                    ),
                  );
                },
              ),
            ),
        ],
      ),
    );
  }

  // بناء تبويب إحصائيات المكونات
  Widget _buildIngredientsStatsTab() {
    Map<String, double> totalIngredients =
    _calculateTotalIngredientsForMember(selectedMember);

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'إحصائيات المكونات والوجبات',
            style: TextStyle(
              fontSize: 17,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.secondary,
            ),
          ),
          const SizedBox(height: 12),
          if (totalIngredients.isEmpty)
            Expanded(
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Lottie.asset('assets/animations/no_data.json',
                        width: 180, height: 180), // Add Lottie animation
                    const SizedBox(height: 20),
                    const Text(
                      'لا توجد بيانات لعرض الإحصائيات حالياً',
                      style: TextStyle(fontSize: 16, color: Colors.grey),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            )
          else
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    GridView.count(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      crossAxisCount: 2,
                      crossAxisSpacing: 12,
                      mainAxisSpacing: 12,
                      childAspectRatio: 1.5, // Adjust for better layout
                      children: [
                        _buildStatItem(
                          'إجمالي المكونات',
                          totalIngredients.length.toString(),
                          Icons.inventory_2_outlined,
                          Colors.blue.shade700,
                        ),
                        _buildStatItem(
                          'أكثر المكونات طلباً',
                          _getMostUsedIngredient(totalIngredients),
                          Icons.star_outline_rounded,
                          Colors.amber.shade800,
                        ),
                        _buildStatItem(
                          'الوجبات المحددة',
                          _getSelectedMealsCount().toString(),
                          Icons.restaurant_menu_outlined,
                          Colors.green.shade700,
                        ),
                        _buildStatItem(
                          'نسبة إكمال الخطة',
                          '${_getCompletionPercentage().toStringAsFixed(1)}%',
                          Icons.pie_chart_outline_rounded,
                          Colors.purple.shade700,
                        ),
                      ],
                    ),
                    const SizedBox(height: 20),
                    Card(
                      elevation: 2,
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12)),
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'توزيع المكونات (الكميات النسبية)',
                              style: TextStyle(
                                fontSize: 15,
                                fontWeight: FontWeight.bold,
                                color: Theme.of(context).primaryColorDark,
                              ),
                            ),
                            const SizedBox(height: 12),
                            if (totalIngredients.length >
                                1) // Ensure there's data for chart
                              SizedBox(
                                height: 200, // Adjust height as needed
                                // Placeholder for a Pie Chart - Can use 'fl_chart' or similar
                                child: Center(
                                    child: Text(
                                        'سيتم إضافة رسم بياني دائري هنا قريباً',
                                        style: TextStyle(color: Colors.grey))),
                              )
                            else
                              const Text(
                                  'لا توجد بيانات كافية لعرض الرسم البياني.',
                                  style: TextStyle(color: Colors.grey)),
                            const SizedBox(height: 10),
                            // List view for detailed percentages if needed
                            ListView.builder(
                              shrinkWrap: true,
                              physics: const NeverScrollableScrollPhysics(),
                              itemCount: totalIngredients.length,
                              itemBuilder: (context, index) {
                                String ingredient =
                                totalIngredients.keys.elementAt(index);
                                double quantity = totalIngredients[ingredient]!;
                                double totalQuantitySum = totalIngredients
                                    .values
                                    .fold(0, (prev, element) => prev + element);
                                double percentage = totalQuantitySum > 0
                                    ? (quantity / totalQuantitySum) * 100
                                    : 0;

                                return Padding(
                                  padding:
                                  const EdgeInsets.symmetric(vertical: 6.0),
                                  child: Column(
                                    crossAxisAlignment:
                                    CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                        children: [
                                          Text(
                                            ingredient,
                                            style: const TextStyle(
                                                fontSize: 14,
                                                fontWeight: FontWeight.w500),
                                          ),
                                          Text(
                                            '${percentage.toStringAsFixed(1)}%',
                                            style: TextStyle(
                                              fontSize: 13,
                                              color: Colors.grey.shade700,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                        ],
                                      ),
                                      const SizedBox(height: 4),
                                      LinearProgressIndicator(
                                        value: percentage / 100,
                                        backgroundColor: Colors.grey.shade200,
                                        valueColor:
                                        AlwaysStoppedAnimation<Color>(
                                          Colors
                                              .primaries[index %
                                              Colors.primaries.length]
                                              .shade400,
                                        ),
                                        minHeight: 6,
                                        borderRadius: BorderRadius.circular(3),
                                      ),
                                    ],
                                  ),
                                );
                              },
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }

  // بناء عنصر إحصائي
  Widget _buildStatItem(
      String title, String value, IconData icon, Color color) {
    return Card(
      elevation: 3,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, color: color, size: 36),
            const SizedBox(height: 8),
            Text(
              value,
              style: TextStyle(
                fontSize: value.length > 10
                    ? 14
                    : 18, // Adjust font size for long text
                fontWeight: FontWeight.bold,
                color: color,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: TextStyle(
                fontSize: 11,
                color: Colors.grey.shade600,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  // الحصول على أكثر المكونات استخداماً
  String _getMostUsedIngredient(Map<String, double> totalIngredients) {
    if (totalIngredients.isEmpty) return 'لا يوجد';

    String mostUsed = totalIngredients.keys.first;
    double maxQuantity = totalIngredients.values.first;

    totalIngredients.forEach((ingredient, quantity) {
      if (quantity > maxQuantity) {
        maxQuantity = quantity;
        mostUsed = ingredient;
      }
    });

    return mostUsed;
  }

  // حساب عدد الوجبات المحددة
  int _getSelectedMealsCount() {
    int count = 0;
    familyMonthlyMeals[selectedMember]?.forEach((week, weekData) {
      weekData.forEach((mealType, mealTypeData) {
        mealTypeData.forEach((day, mealData) {
          if (mealData != null && mealData['meal'] != null) {
            count++;
          }
        });
      });
    });
    return count;
  }

  // حساب نسبة الإكمال
  double _getCompletionPercentage() {
    int totalSlots = weeks.length * days.length * mealCategories.length;
    if (totalSlots == 0) return 0.0; // Avoid division by zero
    int filledSlots = _getSelectedMealsCount();
    return (filledSlots / totalSlots) * 100;
  }

  // مشاركة قائمة المكونات
  void _shareIngredientsList(Map<String, double> totalIngredients) {
    if (totalIngredients.isEmpty) {
      _showInfoSnackBar('لا توجد مكونات لمشاركتها');
      return;
    }

    String shareText =
        '🛒 قائمة المكونات المطلوبة للشهر - $selectedMember 🛒\n';
    shareText += '------------------------------------\n';

    totalIngredients.forEach((ingredient, quantity) {
      String unit = 'كجم';
      double displayQuantity = quantity;

      if (ingredient == 'بيض') {
        unit = 'حبة';
      } else if (ingredient.contains('خبز') || ingredient.contains('رغيف')) {
        unit = 'رغيف';
      } else if (quantity < 1 && quantity > 0) {
        unit = 'جرام';
        displayQuantity = quantity * 1000;
      }

      shareText +=
      '✅ $ingredient: ${displayQuantity.toStringAsFixed(displayQuantity % 1 == 0 ? 0 : 1)} $unit\n';
    });

    shareText += '------------------------------------\n';
    shareText += '📅 تم إنشاؤها بواسطة تطبيق مخطط الوجبات الذكي';

    Share.share(shareText, subject: 'قائمة المكونات الشهرية - $selectedMember');
  }

  // عرض رسالة خطأ
  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error_outline, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.redAccent,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        margin: const EdgeInsets.all(10),
        action: SnackBarAction(
          label: 'إغلاق',
          textColor: Colors.white,
          onPressed: () {
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
          },
        ),
      ),
    );
  }

  // عرض رسالة معلومات
  void _showInfoSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.info_outline, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.blueAccent,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        margin: const EdgeInsets.all(10),
        action: SnackBarAction(
          label: 'إغلاق',
          textColor: Colors.white,
          onPressed: () {
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
          },
        ),
      ),
    );
  }

  // عرض رسالة نجاح
  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.check_circle_outline, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.green.shade600,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        margin: const EdgeInsets.all(10),
        action: SnackBarAction(
          label: 'إغلاق',
          textColor: Colors.white,
          onPressed: () {
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
          },
        ),
      ),
    );
  }

  // مسح اختيارات فئة وجبة معينة
  void _clearMealCategory(String memberId, String week, String mealType) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape:
          RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
          title: Row(
            children: [
              Icon(Icons.warning_amber_rounded, color: Colors.orange.shade700),
              const SizedBox(width: 10),
              const Text('تأكيد المسح'),
            ],
          ),
          content: Text(
              'هل أنت متأكد أنك تريد مسح جميع اختيارات $mealType في $week؟ هذا الإجراء لا يمكن التراجع عنه.'),
          actionsAlignment: MainAxisAlignment.spaceEvenly,
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء', style: TextStyle(color: Colors.grey)),
            ),
            ElevatedButton.icon(
              icon: const Icon(Icons.delete_forever_outlined,
                  color: Colors.white),
              label:
              const Text('مسح الكل', style: TextStyle(color: Colors.white)),
              onPressed: () {
                setState(() {
                  for (String day in days) {
                    familyMonthlyMeals[memberId]![week]![mealType]![day] = null;
                  }
                });
                Navigator.of(context).pop();
                _showSuccessSnackBar(
                    'تم مسح جميع اختيارات $mealType في $week بنجاح!');
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.redAccent,
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8)),
              ),
            ),
          ],
        );
      },
    );
  }

  // اختيار وجبة لليوم
  void _selectMealForDay(
      String memberId, String week, String mealType, String day) {
    setState(() {
      selectedWeek = week;
      selectedDay = day;
      selectedMealType = mealType;
      isSelectionMode = true; // This might not be needed anymore with dialogs
    });

    _showMealSelectionDialog(memberId, week, mealType, day);
  }

  // عرض نافذة اختيار الحجم
  void _showSizeSelectionDialog(
      String memberId, String week, String mealType, String day, String meal) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape:
          RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
          title: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('اختيار حجم الوجبة',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              Text(meal,
                  style: TextStyle(fontSize: 14, color: Colors.grey.shade600)),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: portionSizes.map((size) {
              return Card(
                elevation: 1.5,
                margin: const EdgeInsets.symmetric(vertical: 5),
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10)),
                child: ListTile(
                  leading: CircleAvatar(
                    backgroundColor: _getSizeColor(size).withOpacity(0.15),
                    child: Icon(
                      _getSizeIcon(size),
                      color: _getSizeColor(size),
                      size: 22,
                    ),
                  ),
                  title: Text(size,
                      style: const TextStyle(
                          fontSize: 16, fontWeight: FontWeight.w500)),
                  subtitle: _buildSizeDescription(meal, size),
                  onTap: () {
                    _assignMealToDay(memberId, week, mealType, day, meal, size);
                    Navigator.of(context).pop();
                  },
                  contentPadding:
                  const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                ),
              );
            }).toList(),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
          ],
        );
      },
    );
  }

  // بناء وصف الحجم مع الكميات
  Widget? _buildSizeDescription(String meal, String size) {
    if (!ingredients.containsKey(meal)) return null;

    var mealIngredients = ingredients[meal]!;
    List<String> descriptions = [];

    mealIngredients.forEach((ingredient, sizeQuantities) {
      if (sizeQuantities.containsKey(size)) {
        double quantity = sizeQuantities[size]!;
        String unit = 'كجم';

        if (ingredient == 'بيض') {
          unit = 'حبة';
        } else if (ingredient.contains('خبز')) {
          unit = 'رغيف';
        } else if (quantity < 1 && quantity > 0) {
          unit = 'جرام';
          quantity = quantity * 1000;
        }

        descriptions.add(
            '$ingredient: ${quantity.toStringAsFixed(quantity % 1 == 0 ? 0 : 1)} $unit');
      }
    });

    if (descriptions.isEmpty)
      return const Text('لا توجد تفاصيل كميات لهذا الحجم',
          style: TextStyle(fontSize: 11, color: Colors.grey));

    return Text(
      descriptions.take(2).join(' | '), // Show first 2 ingredients for brevity
      style: const TextStyle(fontSize: 11, color: Colors.black54),
      maxLines: 1,
      overflow: TextOverflow.ellipsis,
    );
  }

  // الحصول على لون الحجم
  Color _getSizeColor(String size) {
    switch (size) {
      case 'كبير':
        return Colors.red.shade600;
      case 'متوسط':
        return Colors.orange.shade700;
      case 'صغير':
        return Colors.green.shade600;
      default:
        return Colors.grey.shade600;
    }
  }

  // الحصول على أيقونة الحجم
  IconData _getSizeIcon(String size) {
    switch (size) {
      case 'كبير':
        return Icons.set_meal_outlined; // Changed icon
      case 'متوسط':
        return Icons.set_meal; // Changed icon
      case 'صغير':
        return Icons.set_meal_sharp; // Changed icon
      default:
        return Icons.help_outline;
    }
  }

  // تعيين وجبة لليوم
  void _assignMealToDay(String memberId, String week, String mealType,
      String day, String meal, String size) {
    setState(() {
      familyMonthlyMeals[memberId]![week]![mealType]![day] = {
        'meal': meal,
        'size': size,
      };
      // isSelectionMode = false; // Not needed if dialog handles its own state
    });

    _showSuccessSnackBar('تم إضافة $meal ($size) بنجاح ليوم $day!');
    // Log analytics event
    FirebaseAnalytics.instance.logEvent(
      name: 'meal_assigned',
      parameters: {
        'member_id': memberId,
        'week': week,
        'meal_type': mealType,
        'day': day,
        'meal_name': meal,
        'meal_size': size,
      },
    );
  }

  // إزالة وجبة من اليوم
  void _removeMealFromDay(
      String memberId, String week, String mealType, String day) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape:
          RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
          title: Row(
            children: [
              Icon(Icons.delete_sweep_outlined,
                  color: Colors.red.shade700, size: 28),
              const SizedBox(width: 10),
              const Text('تأكيد الحذف'),
            ],
          ),
          content:
          Text('هل أنت متأكد أنك تريد حذف الوجبة المحددة من يوم $day؟'),
          actionsAlignment: MainAxisAlignment.spaceEvenly,
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء', style: TextStyle(color: Colors.grey)),
            ),
            ElevatedButton.icon(
              icon: const Icon(Icons.check_circle_outline, color: Colors.white),
              label: const Text('نعم، احذف',
                  style: TextStyle(color: Colors.white)),
              onPressed: () {
                String? removedMealName =
                familyMonthlyMeals[memberId]![week]![mealType]![day]
                ?['meal'];
                setState(() {
                  familyMonthlyMeals[memberId]![week]![mealType]![day] = null;
                });
                Navigator.of(context).pop();
                _showSuccessSnackBar('تم حذف الوجبة من يوم $day بنجاح.');
                if (removedMealName != null) {
                  FirebaseAnalytics.instance.logEvent(
                    name: 'meal_removed',
                    parameters: {
                      'member_id': memberId,
                      'week': week,
                      'meal_type': mealType,
                      'day': day,
                      'meal_name': removedMealName,
                    },
                  );
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red.shade600,
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8)),
              ),
            ),
          ],
        );
      },
    );
  }

  // إضافة نوع طعام جديد (يمكن تحسين هذه الواجهة لاحقاً)
  void _addNewFoodType() {
    // This UI can be significantly improved with a dedicated screen or a more complex dialog
    if (_foodNameController.text.trim().isEmpty ||
        _foodCategoryController.text.trim().isEmpty) {
      _showErrorSnackBar('يرجى ملء اسم وفئة الطعام على الأقل');
      return;
    }

    setState(() {
      _isAddingFoodType = true;
    });

    Future.delayed(const Duration(milliseconds: 500), () {
      String newFoodName = _foodNameController.text.trim();
      String newFoodCat = _foodCategoryController.text.trim();
      String newFoodDesc = _foodDescriptionController.text.trim();

      // Add to _foodTypes (local list for selection)
      _foodTypes.add({
        'id': 'custom_${DateTime.now().millisecondsSinceEpoch}',
        'name': newFoodName,
        'category':
        newFoodCat, // This should ideally map to existing foodPreferences categories or a new one
        'mealType': 'وجبات مخصصة', // Or allow user to select
        'description': newFoodDesc,
        'isAvailable': true,
      });

      // Add to foodPreferences (for future selections)
      if (!foodPreferences.containsKey(newFoodCat)) {
        foodPreferences[newFoodCat] = {};
      }
      if (!foodPreferences[newFoodCat]!.containsKey('وجبات مخصصة')) {
        foodPreferences[newFoodCat]!['وجبات مخصصة'] = [];
      }
      foodPreferences[newFoodCat]!['وجبات مخصصة']!.add(newFoodName);

      // Potentially add to ingredients and preparationMethods if details are provided
      // For now, it's just added as a selectable name.

      setState(() {
        _isAddingFoodType = false;
      });

      _foodNameController.clear();
      _foodCategoryController.clear();
      _foodDescriptionController.clear();

      _showSuccessSnackBar(
          'تم إضافة "$newFoodName" إلى قائمة الوجبات المخصصة!');
      // Consider prompting user to add ingredients/preparation details for the new meal
    });
  }

  // تحميل المزيد من أنواع الطعام (Pagination example - can be implemented if _foodTypes is fetched from a backend)
  void _loadMoreFoodTypes() {
    if (_isLoadingMore || !_hasMoreData) return;

    setState(() {
      _isLoadingMore = true;
    });

    // Simulate API call
    Future.delayed(const Duration(seconds: 1), () {
      // Add more items to _foodTypes if paginating from a backend
      // For this local example, we'll just toggle the flags
      setState(() {
        _currentBatch++;
        _isLoadingMore = false;
        if (_currentBatch >= 2) {
          // Simulate no more data after 2 batches
          _hasMoreData = false;
        }
      });
    });
  }
}

// ===== Shopping List Management Classes =====

class ShoppingItem {
  final String id;
  final String name;
  final double quantity;
  final String unit;
  final bool isPurchased;
  final String category;
  final DateTime dateAdded;
  final double? estimatedPrice;

  ShoppingItem({
    required this.id,
    required this.name,
    required this.quantity,
    required this.unit,
    this.isPurchased = false,
    required this.category,
    required this.dateAdded,
    this.estimatedPrice,
  });

  ShoppingItem copyWith({
    String? id,
    String? name,
    double? quantity,
    String? unit,
    bool? isPurchased,
    String? category,
    DateTime? dateAdded,
    double? estimatedPrice,
  }) {
    return ShoppingItem(
      id: id ?? this.id,
      name: name ?? this.name,
      quantity: quantity ?? this.quantity,
      unit: unit ?? this.unit,
      isPurchased: isPurchased ?? this.isPurchased,
      category: category ?? this.category,
      dateAdded: dateAdded ?? this.dateAdded,
      estimatedPrice: estimatedPrice ?? this.estimatedPrice,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'quantity': quantity,
      'unit': unit,
      'isPurchased': isPurchased,
      'category': category,
      'dateAdded': dateAdded.toIso8601String(),
      'estimatedPrice': estimatedPrice,
    };
  }

  factory ShoppingItem.fromJson(Map<String, dynamic> json) {
    return ShoppingItem(
      id: json['id'],
      name: json['name'],
      quantity: json['quantity'].toDouble(),
      unit: json['unit'],
      isPurchased: json['isPurchased'] ?? false,
      category: json['category'],
      dateAdded: DateTime.parse(json['dateAdded']),
      estimatedPrice: json['estimatedPrice']?.toDouble(),
    );
  }
}

class ShoppingListManager {
  static const String _storageKey = 'shopping_list';
  final FlutterSecureStorage _storage = const FlutterSecureStorage();
  List<ShoppingItem> _items = [];

  List<ShoppingItem> get items => List.unmodifiable(_items);

  Future<void> loadShoppingList() async {
    try {
      final String? jsonString = await _storage.read(key: _storageKey);
      if (jsonString != null) {
        final List<dynamic> jsonList = json.decode(jsonString);
        _items = jsonList.map((json) => ShoppingItem.fromJson(json)).toList();
      }
    } catch (e) {
      print('Error loading shopping list: $e');
    }
  }

  Future<void> saveShoppingList() async {
    try {
      final String jsonString =
      json.encode(_items.map((item) => item.toJson()).toList());
      await _storage.write(key: _storageKey, value: jsonString);
    } catch (e) {
      print('Error saving shopping list: $e');
    }
  }

  Future<void> addItem(ShoppingItem item) async {
    // Check if item already exists and merge quantities
    final existingIndex = _items.indexWhere(
            (existing) => existing.name == item.name && existing.unit == item.unit);

    if (existingIndex != -1) {
      _items[existingIndex] = _items[existingIndex].copyWith(
        quantity: _items[existingIndex].quantity + item.quantity,
      );
    } else {
      _items.add(item);
    }
    await saveShoppingList();
  }

  Future<void> removeItem(String itemId) async {
    _items.removeWhere((item) => item.id == itemId);
    await saveShoppingList();
  }

  Future<void> togglePurchased(String itemId) async {
    final index = _items.indexWhere((item) => item.id == itemId);
    if (index != -1) {
      _items[index] =
          _items[index].copyWith(isPurchased: !_items[index].isPurchased);
      await saveShoppingList();
    }
  }

  Future<void> updateQuantity(String itemId, double newQuantity) async {
    final index = _items.indexWhere((item) => item.id == itemId);
    if (index != -1) {
      _items[index] = _items[index].copyWith(quantity: newQuantity);
      await saveShoppingList();
    }
  }

  Future<void> clearPurchasedItems() async {
    _items.removeWhere((item) => item.isPurchased);
    await saveShoppingList();
  }

  Future<void> generateFromIngredients(Map<String, double> ingredients) async {
    for (final entry in ingredients.entries) {
      final String ingredientName = entry.key;
      final double quantity = entry.value;

      String unit = 'كجم';
      double displayQuantity = quantity;

      if (ingredientName == 'بيض') {
        unit = 'حبة';
      } else if (ingredientName.contains('خبز') ||
          ingredientName.contains('رغيف')) {
        unit = 'رغيف';
      } else if (quantity < 1 && quantity > 0) {
        unit = 'جرام';
        displayQuantity = quantity * 1000;
      }

      final newItem = ShoppingItem(
        id: DateTime.now().millisecondsSinceEpoch.toString() +
            ingredientName.hashCode.toString(),
        name: ingredientName,
        quantity: displayQuantity,
        unit: unit,
        category: _getCategoryForIngredient(ingredientName),
        dateAdded: DateTime.now(),
        estimatedPrice:
        _getEstimatedPrice(ingredientName, displayQuantity, unit),
      );

      await addItem(newItem);
    }
  }

  String _getCategoryForIngredient(String ingredient) {
    if (ingredient.contains('خضار') ||
        ingredient.contains('طماطم') ||
        ingredient.contains('خيار')) {
      return 'خضروات';
    } else if (ingredient.contains('فاكهة') ||
        ingredient.contains('تفاح') ||
        ingredient.contains('موز')) {
      return 'فواكه';
    } else if (ingredient.contains('لحم') ||
        ingredient.contains('دجاج') ||
        ingredient.contains('سمك')) {
      return 'لحوم وأسماك';
    } else if (ingredient.contains('أرز') ||
        ingredient.contains('خبز') ||
        ingredient.contains('مكرونة')) {
      return 'حبوب ونشويات';
    } else if (ingredient.contains('لبن') ||
        ingredient.contains('جبن') ||
        ingredient.contains('زبدة')) {
      return 'ألبان ومشتقاتها';
    } else {
      return 'متنوعة';
    }
  }

  double? _getEstimatedPrice(String ingredient, double quantity, String unit) {
    // This is a simplified price estimation - in a real app, this could be fetched from an API
    final Map<String, double> pricePerUnit = {
      'أرز': 15.0, // per kg
      'خبز': 2.0, // per loaf
      'بيض': 1.5, // per piece
      'لحم': 120.0, // per kg
      'دجاج': 45.0, // per kg
      'طماطم': 8.0, // per kg
      'بصل': 6.0, // per kg
      'زيت': 25.0, // per kg
    };

    for (final entry in pricePerUnit.entries) {
      if (ingredient.contains(entry.key)) {
        if (unit == 'جرام') {
          return (entry.value * quantity) / 1000;
        } else {
          return entry.value * quantity;
        }
      }
    }
    return null;
  }

  double getTotalEstimatedCost() {
    return _items
        .where((item) => !item.isPurchased && item.estimatedPrice != null)
        .fold(0.0, (sum, item) => sum + item.estimatedPrice!);
  }

  Map<String, List<ShoppingItem>> getItemsByCategory() {
    final Map<String, List<ShoppingItem>> categorized = {};
    for (final item in _items) {
      if (!categorized.containsKey(item.category)) {
        categorized[item.category] = [];
      }
      categorized[item.category]!.add(item);
    }
    return categorized;
  }
}

// ===== Custom Meal Management Classes =====

class CustomMeal {
  final String id;
  final String name;
  final String category;
  final String description;
  final List<CustomIngredient> ingredients;
  final List<String> preparationSteps;
  final String? imageUrl;
  final String? videoUrl;
  final DateTime dateCreated;
  final String createdBy;
  final int difficulty; // 1-5 scale
  final int preparationTime; // in minutes
  final Map<String, int> nutritionInfo; // calories, protein, carbs, fat

  CustomMeal({
    required this.id,
    required this.name,
    required this.category,
    required this.description,
    required this.ingredients,
    required this.preparationSteps,
    this.imageUrl,
    this.videoUrl,
    required this.dateCreated,
    required this.createdBy,
    this.difficulty = 3,
    this.preparationTime = 30,
    this.nutritionInfo = const {},
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'category': category,
      'description': description,
      'ingredients': ingredients.map((i) => i.toJson()).toList(),
      'preparationSteps': preparationSteps,
      'imageUrl': imageUrl,
      'videoUrl': videoUrl,
      'dateCreated': dateCreated.toIso8601String(),
      'createdBy': createdBy,
      'difficulty': difficulty,
      'preparationTime': preparationTime,
      'nutritionInfo': nutritionInfo,
    };
  }

  factory CustomMeal.fromJson(Map<String, dynamic> json) {
    return CustomMeal(
      id: json['id'],
      name: json['name'],
      category: json['category'],
      description: json['description'],
      ingredients: (json['ingredients'] as List)
          .map((i) => CustomIngredient.fromJson(i))
          .toList(),
      preparationSteps: List<String>.from(json['preparationSteps']),
      imageUrl: json['imageUrl'],
      videoUrl: json['videoUrl'],
      dateCreated: DateTime.parse(json['dateCreated']),
      createdBy: json['createdBy'],
      difficulty: json['difficulty'] ?? 3,
      preparationTime: json['preparationTime'] ?? 30,
      nutritionInfo: Map<String, int>.from(json['nutritionInfo'] ?? {}),
    );
  }
}

class CustomIngredient {
  final String name;
  final Map<String, double> quantitiesBySize;
  final String unit;
  final bool isOptional;

  CustomIngredient({
    required this.name,
    required this.quantitiesBySize,
    required this.unit,
    this.isOptional = false,
  });

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'quantitiesBySize': quantitiesBySize,
      'unit': unit,
      'isOptional': isOptional,
    };
  }

  factory CustomIngredient.fromJson(Map<String, dynamic> json) {
    return CustomIngredient(
      name: json['name'],
      quantitiesBySize: Map<String, double>.from(json['quantitiesBySize']),
      unit: json['unit'],
      isOptional: json['isOptional'] ?? false,
    );
  }
}

class CustomMealManager {
  static const String _storageKey = 'custom_meals';
  final FlutterSecureStorage _storage = const FlutterSecureStorage();
  List<CustomMeal> _customMeals = [];

  List<CustomMeal> get customMeals => List.unmodifiable(_customMeals);

  Future<void> loadCustomMeals() async {
    try {
      final String? jsonString = await _storage.read(key: _storageKey);
      if (jsonString != null) {
        final List<dynamic> jsonList = json.decode(jsonString);
        _customMeals =
            jsonList.map((json) => CustomMeal.fromJson(json)).toList();
      }
    } catch (e) {
      print('Error loading custom meals: $e');
    }
  }

  Future<void> saveCustomMeals() async {
    try {
      final String jsonString =
      json.encode(_customMeals.map((meal) => meal.toJson()).toList());
      await _storage.write(key: _storageKey, value: jsonString);
    } catch (e) {
      print('Error saving custom meals: $e');
    }
  }

  Future<void> addCustomMeal(CustomMeal meal) async {
    _customMeals.add(meal);
    await saveCustomMeals();
  }

  Future<void> updateCustomMeal(CustomMeal updatedMeal) async {
    final index = _customMeals.indexWhere((meal) => meal.id == updatedMeal.id);
    if (index != -1) {
      _customMeals[index] = updatedMeal;
      await saveCustomMeals();
    }
  }

  Future<void> deleteCustomMeal(String mealId) async {
    _customMeals.removeWhere((meal) => meal.id == mealId);
    await saveCustomMeals();
  }

  List<CustomMeal> getMealsByCategory(String category) {
    return _customMeals.where((meal) => meal.category == category).toList();
  }

  List<CustomMeal> searchMeals(String query) {
    final lowerQuery = query.toLowerCase();
    return _customMeals
        .where((meal) =>
    meal.name.toLowerCase().contains(lowerQuery) ||
        meal.description.toLowerCase().contains(lowerQuery) ||
        meal.ingredients.any((ingredient) =>
            ingredient.name.toLowerCase().contains(lowerQuery)))
        .toList();
  }
}

// ===== Shopping List Screen =====

class ShoppingListScreen extends StatefulWidget {
  final ShoppingListManager shoppingListManager;

  const ShoppingListScreen({Key? key, required this.shoppingListManager})
      : super(key: key);

  @override
  _ShoppingListScreenState createState() => _ShoppingListScreenState();
}

class _ShoppingListScreenState extends State<ShoppingListScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  String _selectedCategory = 'الكل';
  bool _showPurchasedItems = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    widget.shoppingListManager.loadShoppingList();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('قائمة المشتريات الذكية',
            style: TextStyle(fontWeight: FontWeight.bold)),
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [Colors.green.shade600, Colors.green.shade800],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
        ),
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(icon: Icon(Icons.shopping_cart_outlined), text: 'قائمة التسوق'),
            Tab(icon: Icon(Icons.category_outlined), text: 'حسب الفئة'),
            Tab(icon: Icon(Icons.analytics_outlined), text: 'الإحصائيات'),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.clear_all),
            onPressed: _showClearPurchasedDialog,
            tooltip: 'مسح المشتريات المكتملة',
          ),
          IconButton(
            icon: const Icon(Icons.ios_share),
            onPressed: _shareShoppingList,
            tooltip: 'مشاركة قائمة المشتريات',
          ),
        ],
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildShoppingListTab(),
          _buildCategoryTab(),
          _buildStatisticsTab(),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _showAddItemDialog,
        backgroundColor: Colors.green.shade600,
        icon: const Icon(Icons.add_shopping_cart, color: Colors.white),
        label: const Text('إضافة عنصر', style: TextStyle(color: Colors.white)),
      ),
    );
  }

  Widget _buildShoppingListTab() {
    final items = widget.shoppingListManager.items;
    final filteredItems = _showPurchasedItems
        ? items
        : items.where((item) => !item.isPurchased).toList();

    if (filteredItems.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Lottie.asset('assets/animations/empty_shopping_cart.json',
                width: 200, height: 200),
            const SizedBox(height: 20),
            const Text('قائمة المشتريات فارغة',
                style: TextStyle(fontSize: 18, color: Colors.grey)),
            const SizedBox(height: 10),
            ElevatedButton.icon(
              onPressed: _showAddItemDialog,
              icon: const Icon(Icons.add),
              label: const Text('إضافة عنصر جديد'),
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'إجمالي العناصر: ${filteredItems.length}',
                style:
                const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              Row(
                children: [
                  const Text('إظهار المكتمل'),
                  Switch(
                    value: _showPurchasedItems,
                    onChanged: (value) {
                      setState(() {
                        _showPurchasedItems = value;
                      });
                    },
                    activeColor: Colors.green.shade600,
                  ),
                ],
              ),
            ],
          ),
        ),
        Expanded(
          child: AnimationLimiter(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: filteredItems.length,
              itemBuilder: (context, index) {
                final item = filteredItems[index];
                return AnimationConfiguration.staggeredList(
                  position: index,
                  duration: const Duration(milliseconds: 375),
                  child: SlideAnimation(
                    verticalOffset: 50.0,
                    child: FadeInAnimation(
                      child: _buildShoppingItemCard(item),
                    ),
                  ),
                );
              },
            ),
          ),
        ),
        if (widget.shoppingListManager.getTotalEstimatedCost() > 0)
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.green.shade50,
              border: Border(top: BorderSide(color: Colors.green.shade200)),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('التكلفة المقدرة:',
                    style:
                    TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
                Text(
                  '${widget.shoppingListManager.getTotalEstimatedCost().toStringAsFixed(2)} ج.م',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.green.shade700,
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildShoppingItemCard(ShoppingItem item) {
    return Card(
      elevation: 2,
      margin: const EdgeInsets.symmetric(vertical: 4),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: ListTile(
        leading: Checkbox(
          value: item.isPurchased,
          onChanged: (value) {
            widget.shoppingListManager.togglePurchased(item.id);
            setState(() {});
          },
          activeColor: Colors.green.shade600,
        ),
        title: Text(
          item.name,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            decoration: item.isPurchased ? TextDecoration.lineThrough : null,
            color: item.isPurchased ? Colors.grey : null,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
                '${item.quantity.toStringAsFixed(item.quantity % 1 == 0 ? 0 : 1)} ${item.unit}'),
            if (item.estimatedPrice != null)
              Text(
                'السعر المقدر: ${item.estimatedPrice!.toStringAsFixed(2)} ج.م',
                style: TextStyle(color: Colors.green.shade700, fontSize: 12),
              ),
          ],
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (value) {
            switch (value) {
              case 'edit':
                _showEditItemDialog(item);
                break;
              case 'delete':
                _deleteItem(item);
                break;
            }
          },
          itemBuilder: (context) => [
            const PopupMenuItem(value: 'edit', child: Text('تعديل')),
            const PopupMenuItem(value: 'delete', child: Text('حذف')),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryTab() {
    final categorizedItems = widget.shoppingListManager.getItemsByCategory();

    if (categorizedItems.isEmpty) {
      return const Center(
        child: Text('لا توجد عناصر لعرضها حسب الفئة',
            style: TextStyle(fontSize: 16, color: Colors.grey)),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: categorizedItems.keys.length,
      itemBuilder: (context, index) {
        final category = categorizedItems.keys.elementAt(index);
        final items = categorizedItems[category]!;
        final purchasedCount = items.where((item) => item.isPurchased).length;

        return Card(
          elevation: 2,
          margin: const EdgeInsets.symmetric(vertical: 8),
          shape:
          RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          child: ExpansionTile(
            leading: CircleAvatar(
              backgroundColor: Colors.green.shade100,
              child: Text(
                items.length.toString(),
                style: TextStyle(
                    color: Colors.green.shade700, fontWeight: FontWeight.bold),
              ),
            ),
            title: Text(category,
                style: const TextStyle(fontWeight: FontWeight.bold)),
            subtitle: Text('$purchasedCount من ${items.length} مكتمل'),
            children:
            items.map((item) => _buildShoppingItemCard(item)).toList(),
          ),
        );
      },
    );
  }

  Widget _buildStatisticsTab() {
    final items = widget.shoppingListManager.items;
    final totalItems = items.length;
    final purchasedItems = items.where((item) => item.isPurchased).length;
    final completionPercentage =
    totalItems > 0 ? (purchasedItems / totalItems) * 100 : 0.0;

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: 2,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            childAspectRatio: 1.2,
            children: [
              _buildStatCard('إجمالي العناصر', totalItems.toString(),
                  Icons.shopping_cart, Colors.blue),
              _buildStatCard('مكتمل', purchasedItems.toString(),
                  Icons.check_circle, Colors.green),
              _buildStatCard('متبقي', (totalItems - purchasedItems).toString(),
                  Icons.pending, Colors.orange),
              _buildStatCard(
                  'نسبة الإكمال',
                  '${completionPercentage.toStringAsFixed(1)}%',
                  Icons.pie_chart,
                  Colors.purple),
            ],
          ),
          const SizedBox(height: 20),
          if (widget.shoppingListManager.getTotalEstimatedCost() > 0) ...[
            Card(
              elevation: 2,
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12)),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('ملخص التكلفة',
                        style: TextStyle(
                            fontSize: 18, fontWeight: FontWeight.bold)),
                    const SizedBox(height: 12),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text('التكلفة الإجمالية المقدرة:'),
                        Text(
                          '${widget.shoppingListManager.getTotalEstimatedCost().toStringAsFixed(2)} ج.م',
                          style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.green.shade700),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildStatCard(
      String title, String value, IconData icon, Color color) {
    return Card(
      elevation: 3,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              value,
              style: TextStyle(
                  fontSize: 20, fontWeight: FontWeight.bold, color: color),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: const TextStyle(fontSize: 12, color: Colors.grey),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  void _showAddItemDialog() {
    final nameController = TextEditingController();
    final quantityController = TextEditingController();
    String selectedUnit = 'كجم';
    String selectedCategory = 'متنوعة';

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
        title: const Text('إضافة عنصر جديد'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: nameController,
              decoration: const InputDecoration(
                labelText: 'اسم العنصر',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  flex: 2,
                  child: TextField(
                    controller: quantityController,
                    keyboardType: TextInputType.number,
                    decoration: const InputDecoration(
                      labelText: 'الكمية',
                      border: OutlineInputBorder(),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: DropdownButtonFormField<String>(
                    value: selectedUnit,
                    decoration: const InputDecoration(
                      labelText: 'الوحدة',
                      border: OutlineInputBorder(),
                    ),
                    items: ['كجم', 'جرام', 'حبة', 'رغيف', 'لتر', 'كوب']
                        .map((unit) =>
                        DropdownMenuItem(value: unit, child: Text(unit)))
                        .toList(),
                    onChanged: (value) => selectedUnit = value!,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            DropdownButtonFormField<String>(
              value: selectedCategory,
              decoration: const InputDecoration(
                labelText: 'الفئة',
                border: OutlineInputBorder(),
              ),
              items: [
                'خضروات',
                'فواكه',
                'لحوم وأسماك',
                'حبوب ونشويات',
                'ألبان ومشتقاتها',
                'متنوعة'
              ]
                  .map((category) =>
                  DropdownMenuItem(value: category, child: Text(category)))
                  .toList(),
              onChanged: (value) => selectedCategory = value!,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              if (nameController.text.isNotEmpty &&
                  quantityController.text.isNotEmpty) {
                final newItem = ShoppingItem(
                  id: DateTime.now().millisecondsSinceEpoch.toString(),
                  name: nameController.text,
                  quantity: double.parse(quantityController.text),
                  unit: selectedUnit,
                  category: selectedCategory,
                  dateAdded: DateTime.now(),
                );
                await widget.shoppingListManager.addItem(newItem);
                setState(() {});
                Navigator.pop(context);
              }
            },
            child: const Text('إضافة'),
          ),
        ],
      ),
    );
  }

  void _showEditItemDialog(ShoppingItem item) {
    final quantityController =
    TextEditingController(text: item.quantity.toString());

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
        title: Text('تعديل ${item.name}'),
        content: TextField(
          controller: quantityController,
          keyboardType: TextInputType.number,
          decoration: InputDecoration(
            labelText: 'الكمية (${item.unit})',
            border: const OutlineInputBorder(),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              if (quantityController.text.isNotEmpty) {
                await widget.shoppingListManager.updateQuantity(
                  item.id,
                  double.parse(quantityController.text),
                );
                setState(() {});
                Navigator.pop(context);
              }
            },
            child: const Text('حفظ'),
          ),
        ],
      ),
    );
  }

  void _deleteItem(ShoppingItem item) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف "${item.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              await widget.shoppingListManager.removeItem(item.id);
              setState(() {});
              Navigator.pop(context);
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  void _showClearPurchasedDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
        title: const Text('مسح العناصر المكتملة'),
        content:
        const Text('هل تريد حذف جميع العناصر التي تم شراؤها من القائمة؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              await widget.shoppingListManager.clearPurchasedItems();
              setState(() {});
              Navigator.pop(context);
            },
            child: const Text('مسح'),
          ),
        ],
      ),
    );
  }

  void _shareShoppingList() {
    final items = widget.shoppingListManager.items
        .where((item) => !item.isPurchased)
        .toList();
    if (items.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('لا توجد عناصر لمشاركتها')),
      );
      return;
    }

    String shareText = '🛒 قائمة المشتريات 🛒\n';
    shareText += '--------------------\n';

    for (final item in items) {
      shareText +=
      '☐ ${item.name}: ${item.quantity.toStringAsFixed(item.quantity % 1 == 0 ? 0 : 1)} ${item.unit}\n';
    }

    shareText += '--------------------\n';
    shareText += 'تم إنشاؤها بواسطة تطبيق مخطط الوجبات الذكي';

    Share.share(shareText, subject: 'قائمة المشتريات');
  }
}

// ===== Custom Meal Creation Screen =====

class CustomMealCreationScreen extends StatefulWidget {
  final CustomMealManager customMealManager;
  final CustomMeal? mealToEdit;

  const CustomMealCreationScreen({
    Key? key,
    required this.customMealManager,
    this.mealToEdit,
  }) : super(key: key);

  @override
  _CustomMealCreationScreenState createState() =>
      _CustomMealCreationScreenState();
}

class _CustomMealCreationScreenState extends State<CustomMealCreationScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _imageUrlController = TextEditingController();
  final _videoUrlController = TextEditingController();

  String _selectedCategory = 'الفطور';
  int _difficulty = 3;
  int _preparationTime = 30;

  List<CustomIngredient> _ingredients = [];
  List<String> _preparationSteps = [];

  @override
  void initState() {
    super.initState();
    if (widget.mealToEdit != null) {
      _loadMealData(widget.mealToEdit!);
    }
  }

  void _loadMealData(CustomMeal meal) {
    _nameController.text = meal.name;
    _descriptionController.text = meal.description;
    _imageUrlController.text = meal.imageUrl ?? '';
    _videoUrlController.text = meal.videoUrl ?? '';
    _selectedCategory = meal.category;
    _difficulty = meal.difficulty;
    _preparationTime = meal.preparationTime;
    _ingredients = List.from(meal.ingredients);
    _preparationSteps = List.from(meal.preparationSteps);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
            widget.mealToEdit != null ? 'تعديل الوجبة' : 'إنشاء وجبة مخصصة'),
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [Colors.orange.shade600, Colors.orange.shade800],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: _saveMeal,
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildBasicInfoSection(),
              const SizedBox(height: 20),
              _buildIngredientsSection(),
              const SizedBox(height: 20),
              _buildPreparationStepsSection(),
              const SizedBox(height: 20),
              _buildAdditionalInfoSection(),
              const SizedBox(height: 30),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBasicInfoSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('المعلومات الأساسية',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
            const SizedBox(height: 16),
            TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'اسم الوجبة',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.restaurant),
              ),
              validator: (value) =>
              value?.isEmpty == true ? 'يرجى إدخال اسم الوجبة' : null,
            ),
            const SizedBox(height: 12),
            DropdownButtonFormField<String>(
              value: _selectedCategory,
              decoration: const InputDecoration(
                labelText: 'فئة الوجبة',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.category),
              ),
              items: [
                'الفطور',
                'الغداء',
                'العشاء',
                'التحلية اليومية',
                'المشروبات اليومية',
                'أطعمة العمل'
              ]
                  .map((category) =>
                  DropdownMenuItem(value: category, child: Text(category)))
                  .toList(),
              onChanged: (value) => setState(() => _selectedCategory = value!),
            ),
            const SizedBox(height: 12),
            TextFormField(
              controller: _descriptionController,
              decoration: const InputDecoration(
                labelText: 'وصف الوجبة',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.description),
              ),
              maxLines: 3,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildIngredientsSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('المكونات',
                    style:
                    TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                ElevatedButton.icon(
                  onPressed: _addIngredient,
                  icon: const Icon(Icons.add),
                  label: const Text('إضافة مكون'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (_ingredients.isEmpty)
              const Center(
                child: Text('لم يتم إضافة مكونات بعد',
                    style: TextStyle(color: Colors.grey)),
              )
            else
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _ingredients.length,
                itemBuilder: (context, index) {
                  final ingredient = _ingredients[index];
                  return Card(
                    margin: const EdgeInsets.symmetric(vertical: 4),
                    child: ListTile(
                      title: Text(ingredient.name),
                      subtitle: Text(
                          '${ingredient.unit} - ${ingredient.isOptional ? "اختياري" : "أساسي"}'),
                      trailing: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          IconButton(
                            icon: const Icon(Icons.edit),
                            onPressed: () => _editIngredient(index),
                          ),
                          IconButton(
                            icon: const Icon(Icons.delete),
                            onPressed: () => _removeIngredient(index),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildPreparationStepsSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('خطوات التحضير',
                    style:
                    TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                ElevatedButton.icon(
                  onPressed: _addPreparationStep,
                  icon: const Icon(Icons.add),
                  label: const Text('إضافة خطوة'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (_preparationSteps.isEmpty)
              const Center(
                child: Text('لم يتم إضافة خطوات تحضير بعد',
                    style: TextStyle(color: Colors.grey)),
              )
            else
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _preparationSteps.length,
                itemBuilder: (context, index) {
                  return Card(
                    margin: const EdgeInsets.symmetric(vertical: 4),
                    child: ListTile(
                      leading: CircleAvatar(
                        child: Text('${index + 1}'),
                        backgroundColor: Colors.orange.shade100,
                      ),
                      title: Text(_preparationSteps[index]),
                      trailing: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          IconButton(
                            icon: const Icon(Icons.edit),
                            onPressed: () => _editPreparationStep(index),
                          ),
                          IconButton(
                            icon: const Icon(Icons.delete),
                            onPressed: () => _removePreparationStep(index),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildAdditionalInfoSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('معلومات إضافية',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('مستوى الصعوبة: $_difficulty/5'),
                      Slider(
                        value: _difficulty.toDouble(),
                        min: 1,
                        max: 5,
                        divisions: 4,
                        onChanged: (value) =>
                            setState(() => _difficulty = value.round()),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    initialValue: _preparationTime.toString(),
                    decoration: const InputDecoration(
                      labelText: 'وقت التحضير (دقيقة)',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                    onChanged: (value) =>
                    _preparationTime = int.tryParse(value) ?? 30,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            TextFormField(
              controller: _imageUrlController,
              decoration: const InputDecoration(
                labelText: 'رابط الصورة (اختياري)',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.image),
              ),
            ),
            const SizedBox(height: 12),
            TextFormField(
              controller: _videoUrlController,
              decoration: const InputDecoration(
                labelText: 'رابط الفيديو (اختياري)',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.video_library),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _addIngredient() {
    _showIngredientDialog();
  }

  void _editIngredient(int index) {
    _showIngredientDialog(ingredient: _ingredients[index], index: index);
  }

  void _removeIngredient(int index) {
    setState(() {
      _ingredients.removeAt(index);
    });
  }

  void _showIngredientDialog({CustomIngredient? ingredient, int? index}) {
    final nameController = TextEditingController(text: ingredient?.name ?? '');
    final smallController = TextEditingController(
        text: ingredient?.quantitiesBySize['صغير']?.toString() ?? '');
    final mediumController = TextEditingController(
        text: ingredient?.quantitiesBySize['متوسط']?.toString() ?? '');
    final largeController = TextEditingController(
        text: ingredient?.quantitiesBySize['كبير']?.toString() ?? '');
    String selectedUnit = ingredient?.unit ?? 'كجم';
    bool isOptional = ingredient?.isOptional ?? false;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
        title: Text(ingredient != null ? 'تعديل المكون' : 'إضافة مكون جديد'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: nameController,
                decoration: const InputDecoration(
                  labelText: 'اسم المكون',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 12),
              DropdownButtonFormField<String>(
                value: selectedUnit,
                decoration: const InputDecoration(
                  labelText: 'الوحدة',
                  border: OutlineInputBorder(),
                ),
                items: ['كجم', 'جرام', 'حبة', 'رغيف', 'لتر', 'كوب', 'ملعقة']
                    .map((unit) =>
                    DropdownMenuItem(value: unit, child: Text(unit)))
                    .toList(),
                onChanged: (value) => selectedUnit = value!,
              ),
              const SizedBox(height: 12),
              const Text('الكميات حسب الحجم:',
                  style: TextStyle(fontWeight: FontWeight.bold)),
              const SizedBox(height: 8),
              Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: smallController,
                      decoration: const InputDecoration(
                        labelText: 'صغير',
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.number,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: TextField(
                      controller: mediumController,
                      decoration: const InputDecoration(
                        labelText: 'متوسط',
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.number,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: TextField(
                      controller: largeController,
                      decoration: const InputDecoration(
                        labelText: 'كبير',
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.number,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              CheckboxListTile(
                title: const Text('مكون اختياري'),
                value: isOptional,
                onChanged: (value) => isOptional = value ?? false,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              if (nameController.text.isNotEmpty) {
                final newIngredient = CustomIngredient(
                  name: nameController.text,
                  quantitiesBySize: {
                    'صغير': double.tryParse(smallController.text) ?? 0.0,
                    'متوسط': double.tryParse(mediumController.text) ?? 0.0,
                    'كبير': double.tryParse(largeController.text) ?? 0.0,
                  },
                  unit: selectedUnit,
                  isOptional: isOptional,
                );

                setState(() {
                  if (index != null) {
                    _ingredients[index] = newIngredient;
                  } else {
                    _ingredients.add(newIngredient);
                  }
                });
                Navigator.pop(context);
              }
            },
            child: const Text('حفظ'),
          ),
        ],
      ),
    );
  }

  void _addPreparationStep() {
    _showStepDialog();
  }

  void _editPreparationStep(int index) {
    _showStepDialog(step: _preparationSteps[index], index: index);
  }

  void _removePreparationStep(int index) {
    setState(() {
      _preparationSteps.removeAt(index);
    });
  }

  void _showStepDialog({String? step, int? index}) {
    final controller = TextEditingController(text: step ?? '');

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
        title: Text(step != null ? 'تعديل الخطوة' : 'إضافة خطوة جديدة'),
        content: TextField(
          controller: controller,
          decoration: const InputDecoration(
            labelText: 'وصف الخطوة',
            border: OutlineInputBorder(),
          ),
          maxLines: 3,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              if (controller.text.isNotEmpty) {
                setState(() {
                  if (index != null) {
                    _preparationSteps[index] = controller.text;
                  } else {
                    _preparationSteps.add(controller.text);
                  }
                });
                Navigator.pop(context);
              }
            },
            child: const Text('حفظ'),
          ),
        ],
      ),
    );
  }

  void _saveMeal() async {
    if (_formKey.currentState?.validate() != true) return;
    if (_ingredients.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى إضافة مكون واحد على الأقل')),
      );
      return;
    }
    if (_preparationSteps.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى إضافة خطوة تحضير واحدة على الأقل')),
      );
      return;
    }

    final meal = CustomMeal(
      id: widget.mealToEdit?.id ??
          DateTime.now().millisecondsSinceEpoch.toString(),
      name: _nameController.text,
      category: _selectedCategory,
      description: _descriptionController.text,
      ingredients: _ingredients,
      preparationSteps: _preparationSteps,
      imageUrl:
      _imageUrlController.text.isNotEmpty ? _imageUrlController.text : null,
      videoUrl:
      _videoUrlController.text.isNotEmpty ? _videoUrlController.text : null,
      dateCreated: widget.mealToEdit?.dateCreated ?? DateTime.now(),
      createdBy: 'المستخدم', // In a real app, this would be the current user
      difficulty: _difficulty,
      preparationTime: _preparationTime,
    );

    try {
      if (widget.mealToEdit != null) {
        await widget.customMealManager.updateCustomMeal(meal);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم تحديث الوجبة بنجاح')),
        );
      } else {
        await widget.customMealManager.addCustomMeal(meal);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم إنشاء الوجبة بنجاح')),
        );
      }
      Navigator.pop(context, true);
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('خطأ في حفظ الوجبة: $e')),
      );
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _imageUrlController.dispose();
    _videoUrlController.dispose();
    super.dispose();
  }
}

// ===== Member Management and Nutrition Tracking Classes =====

class MemberProfile {
  final String id;
  final String name;
  final int age;
  final String gender; // 'ذكر' or 'أنثى'
  final double weight; // in kg
  final double height; // in cm
  final String activityLevel; // 'قليل', 'متوسط', 'عالي', 'عالي جداً'
  final String
  goal; // 'إنقاص الوزن', 'زيادة الوزن', 'الحفاظ على الوزن', 'بناء العضلات'
  final List<String> allergies;
  final List<String> dislikedFoods;
  final List<String> preferredFoods;
  final Map<String, double>
  dailyNutritionGoals; // calories, protein, carbs, fat
  final DateTime dateCreated;
  final DateTime lastUpdated;

  MemberProfile({
    required this.id,
    required this.name,
    required this.age,
    required this.gender,
    required this.weight,
    required this.height,
    required this.activityLevel,
    required this.goal,
    this.allergies = const [],
    this.dislikedFoods = const [],
    this.preferredFoods = const [],
    this.dailyNutritionGoals = const {},
    required this.dateCreated,
    required this.lastUpdated,
  });

  double get bmi => weight / ((height / 100) * (height / 100));

  String get bmiCategory {
    if (bmi < 18.5) return 'نقص في الوزن';
    if (bmi < 25) return 'وزن طبيعي';
    if (bmi < 30) return 'زيادة في الوزن';
    return 'سمنة';
  }

  double get dailyCalorieNeeds {
    // Mifflin-St Jeor Equation
    double bmr;
    if (gender == 'ذكر') {
      bmr = 88.362 + (13.397 * weight) + (4.799 * height) - (5.677 * age);
    } else {
      bmr = 447.593 + (9.247 * weight) + (3.098 * height) - (4.330 * age);
    }

    double activityMultiplier;
    switch (activityLevel) {
      case 'قليل':
        activityMultiplier = 1.2;
        break;
      case 'متوسط':
        activityMultiplier = 1.55;
        break;
      case 'عالي':
        activityMultiplier = 1.725;
        break;
      case 'عالي جداً':
        activityMultiplier = 1.9;
        break;
      default:
        activityMultiplier = 1.55;
    }

    double calories = bmr * activityMultiplier;

    // Adjust based on goal
    switch (goal) {
      case 'إنقاص الوزن':
        calories -= 500; // 0.5 kg per week
        break;
      case 'زيادة الوزن':
        calories += 500; // 0.5 kg per week
        break;
      case 'بناء العضلات':
        calories += 300;
        break;
    }

    return calories;
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'age': age,
      'gender': gender,
      'weight': weight,
      'height': height,
      'activityLevel': activityLevel,
      'goal': goal,
      'allergies': allergies,
      'dislikedFoods': dislikedFoods,
      'preferredFoods': preferredFoods,
      'dailyNutritionGoals': dailyNutritionGoals,
      'dateCreated': dateCreated.toIso8601String(),
      'lastUpdated': lastUpdated.toIso8601String(),
    };
  }

  factory MemberProfile.fromJson(Map<String, dynamic> json) {
    return MemberProfile(
      id: json['id'],
      name: json['name'],
      age: json['age'],
      gender: json['gender'],
      weight: json['weight'].toDouble(),
      height: json['height'].toDouble(),
      activityLevel: json['activityLevel'],
      goal: json['goal'],
      allergies: List<String>.from(json['allergies'] ?? []),
      dislikedFoods: List<String>.from(json['dislikedFoods'] ?? []),
      preferredFoods: List<String>.from(json['preferredFoods'] ?? []),
      dailyNutritionGoals:
      Map<String, double>.from(json['dailyNutritionGoals'] ?? {}),
      dateCreated: DateTime.parse(json['dateCreated']),
      lastUpdated: DateTime.parse(json['lastUpdated']),
    );
  }

  MemberProfile copyWith({
    String? id,
    String? name,
    int? age,
    String? gender,
    double? weight,
    double? height,
    String? activityLevel,
    String? goal,
    List<String>? allergies,
    List<String>? dislikedFoods,
    List<String>? preferredFoods,
    Map<String, double>? dailyNutritionGoals,
    DateTime? dateCreated,
    DateTime? lastUpdated,
  }) {
    return MemberProfile(
      id: id ?? this.id,
      name: name ?? this.name,
      age: age ?? this.age,
      gender: gender ?? this.gender,
      weight: weight ?? this.weight,
      height: height ?? this.height,
      activityLevel: activityLevel ?? this.activityLevel,
      goal: goal ?? this.goal,
      allergies: allergies ?? this.allergies,
      dislikedFoods: dislikedFoods ?? this.dislikedFoods,
      preferredFoods: preferredFoods ?? this.preferredFoods,
      dailyNutritionGoals: dailyNutritionGoals ?? this.dailyNutritionGoals,
      dateCreated: dateCreated ?? this.dateCreated,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }
}

class NutritionInfo {
  final double calories;
  final double protein; // in grams
  final double carbs; // in grams
  final double fat; // in grams
  final double fiber; // in grams
  final double sugar; // in grams
  final double sodium; // in mg
  final Map<String, double> vitamins; // vitamin name -> amount
  final Map<String, double> minerals; // mineral name -> amount

  const NutritionInfo({
    required this.calories,
    required this.protein,
    required this.carbs,
    required this.fat,
    this.fiber = 0,
    this.sugar = 0,
    this.sodium = 0,
    this.vitamins = const {},
    this.minerals = const {},
  });

  NutritionInfo operator +(NutritionInfo other) {
    Map<String, double> combinedVitamins = Map.from(vitamins);
    other.vitamins.forEach((key, value) {
      combinedVitamins[key] = (combinedVitamins[key] ?? 0) + value;
    });

    Map<String, double> combinedMinerals = Map.from(minerals);
    other.minerals.forEach((key, value) {
      combinedMinerals[key] = (combinedMinerals[key] ?? 0) + value;
    });

    return NutritionInfo(
      calories: calories + other.calories,
      protein: protein + other.protein,
      carbs: carbs + other.carbs,
      fat: fat + other.fat,
      fiber: fiber + other.fiber,
      sugar: sugar + other.sugar,
      sodium: sodium + other.sodium,
      vitamins: combinedVitamins,
      minerals: combinedMinerals,
    );
  }

  NutritionInfo operator *(double multiplier) {
    return NutritionInfo(
      calories: calories * multiplier,
      protein: protein * multiplier,
      carbs: carbs * multiplier,
      fat: fat * multiplier,
      fiber: fiber * multiplier,
      sugar: sugar * multiplier,
      sodium: sodium * multiplier,
      vitamins: vitamins.map((key, value) => MapEntry(key, value * multiplier)),
      minerals: minerals.map((key, value) => MapEntry(key, value * multiplier)),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'calories': calories,
      'protein': protein,
      'carbs': carbs,
      'fat': fat,
      'fiber': fiber,
      'sugar': sugar,
      'sodium': sodium,
      'vitamins': vitamins,
      'minerals': minerals,
    };
  }

  factory NutritionInfo.fromJson(Map<String, dynamic> json) {
    return NutritionInfo(
      calories: json['calories'].toDouble(),
      protein: json['protein'].toDouble(),
      carbs: json['carbs'].toDouble(),
      fat: json['fat'].toDouble(),
      fiber: json['fiber']?.toDouble() ?? 0,
      sugar: json['sugar']?.toDouble() ?? 0,
      sodium: json['sodium']?.toDouble() ?? 0,
      vitamins: Map<String, double>.from(json['vitamins'] ?? {}),
      minerals: Map<String, double>.from(json['minerals'] ?? {}),
    );
  }
}

class MemberManager {
  static const String _storageKey = 'member_profiles';
  final FlutterSecureStorage _storage = const FlutterSecureStorage();
  List<MemberProfile> _members = [];

  List<MemberProfile> get members => List.unmodifiable(_members);

  Future<void> loadMembers() async {
    try {
      final String? jsonString = await _storage.read(key: _storageKey);
      if (jsonString != null) {
        final List<dynamic> jsonList = json.decode(jsonString);
        _members =
            jsonList.map((json) => MemberProfile.fromJson(json)).toList();
      }
    } catch (e) {
      print('Error loading members: $e');
    }
  }

  Future<void> saveMembers() async {
    try {
      final String jsonString =
      json.encode(_members.map((member) => member.toJson()).toList());
      await _storage.write(key: _storageKey, value: jsonString);
    } catch (e) {
      print('Error saving members: $e');
    }
  }

  Future<void> addMember(MemberProfile member) async {
    _members.add(member);
    await saveMembers();
  }

  Future<void> updateMember(MemberProfile updatedMember) async {
    final index =
    _members.indexWhere((member) => member.id == updatedMember.id);
    if (index != -1) {
      _members[index] = updatedMember;
      await saveMembers();
    }
  }

  Future<void> deleteMember(String memberId) async {
    _members.removeWhere((member) => member.id == memberId);
    await saveMembers();
  }

  MemberProfile? getMemberById(String id) {
    try {
      return _members.firstWhere((member) => member.id == id);
    } catch (e) {
      return null;
    }
  }

  List<String> getRecommendedMealsForMember(String memberId, String mealType,
      Map<String, Map<String, dynamic>> availableMeals) {
    final member = getMemberById(memberId);
    if (member == null) return [];

    List<String> recommendedMeals = [];

    availableMeals.forEach((mealName, mealData) {
      bool isRecommended = true;

      // Check allergies
      if (mealData['ingredients'] != null) {
        for (String ingredient in mealData['ingredients']) {
          if (member.allergies.any((allergy) => ingredient.contains(allergy))) {
            isRecommended = false;
            break;
          }
        }
      }

      // Check disliked foods
      if (isRecommended && member.dislikedFoods.contains(mealName)) {
        isRecommended = false;
      }

      // Prefer liked foods
      if (isRecommended && member.preferredFoods.contains(mealName)) {
        recommendedMeals.insert(0, mealName); // Add to beginning
      } else if (isRecommended) {
        recommendedMeals.add(mealName);
      }
    });

    return recommendedMeals;
  }
}

class NutritionTracker {
  static const String _storageKey = 'nutrition_tracking';
  final FlutterSecureStorage _storage = const FlutterSecureStorage();
  Map<String, Map<String, NutritionInfo>> _dailyNutrition =
  {}; // memberId -> date -> nutrition

  // Nutrition database for common foods (simplified)
  static const Map<String, NutritionInfo> _foodNutritionDatabase = const {
    'أرز':
    const NutritionInfo(calories: 130, protein: 2.7, carbs: 28, fat: 0.3),
    'خبز': const NutritionInfo(calories: 265, protein: 9, carbs: 49, fat: 3.2),
    'بيض': const NutritionInfo(calories: 155, protein: 13, carbs: 1.1, fat: 11),
    'دجاج': const NutritionInfo(calories: 239, protein: 27, carbs: 0, fat: 14),
    'لحم': const NutritionInfo(calories: 250, protein: 26, carbs: 0, fat: 15),
    'سمك': const NutritionInfo(calories: 206, protein: 22, carbs: 0, fat: 12),
    'طماطم':
    const NutritionInfo(calories: 18, protein: 0.9, carbs: 3.9, fat: 0.2),
    'خيار': const NutritionInfo(calories: 16, protein: 0.7, carbs: 4, fat: 0.1),
    'بصل':
    const NutritionInfo(calories: 40, protein: 1.1, carbs: 9.3, fat: 0.1),
    'جزر':
    const NutritionInfo(calories: 41, protein: 0.9, carbs: 9.6, fat: 0.2),
    'بطاطس': const NutritionInfo(calories: 77, protein: 2, carbs: 17, fat: 0.1),
    'موز': const NutritionInfo(calories: 89, protein: 1.1, carbs: 23, fat: 0.3),
    'تفاح':
    const NutritionInfo(calories: 52, protein: 0.3, carbs: 14, fat: 0.2),
    'برتقال':
    const NutritionInfo(calories: 47, protein: 0.9, carbs: 12, fat: 0.1),
    'لبن': const NutritionInfo(calories: 42, protein: 3.4, carbs: 5, fat: 1),
    'جبن': const NutritionInfo(calories: 113, protein: 7, carbs: 1, fat: 9),
    'زبدة':
    const NutritionInfo(calories: 717, protein: 0.9, carbs: 0.1, fat: 81),
    'زيت': const NutritionInfo(calories: 884, protein: 0, carbs: 0, fat: 100),
  };

  Future<void> loadNutritionData() async {
    try {
      final String? jsonString = await _storage.read(key: _storageKey);
      if (jsonString != null) {
        final Map<String, dynamic> jsonData = json.decode(jsonString);
        _dailyNutrition = jsonData.map((memberId, memberData) => MapEntry(
            memberId,
            (memberData as Map<String, dynamic>).map((date, nutritionData) =>
                MapEntry(date, NutritionInfo.fromJson(nutritionData)))));
      }
    } catch (e) {
      print('Error loading nutrition data: $e');
    }
  }

  Future<void> saveNutritionData() async {
    try {
      final Map<String, dynamic> jsonData = _dailyNutrition.map(
              (memberId, memberData) => MapEntry(
              memberId,
              memberData.map(
                      (date, nutrition) => MapEntry(date, nutrition.toJson()))));
      final String jsonString = json.encode(jsonData);
      await _storage.write(key: _storageKey, value: jsonString);
    } catch (e) {
      print('Error saving nutrition data: $e');
    }
  }

  NutritionInfo calculateMealNutrition(String mealName, String size,
      Map<String, Map<String, double>> ingredients) {
    if (!ingredients.containsKey(mealName)) {
      return const NutritionInfo(calories: 0, protein: 0, carbs: 0, fat: 0);
    }

    NutritionInfo totalNutrition =
    const NutritionInfo(calories: 0, protein: 0, carbs: 0, fat: 0);

    final mealIngredients = ingredients[mealName] ?? {};
    mealIngredients.forEach((ingredientName, dynamic sizeQuantities) {
      double quantity = 0.0;

      if (sizeQuantities is Map) {
        // Handle case where sizeQuantities is a map
        if (sizeQuantities.isNotEmpty && sizeQuantities.containsKey(size)) {
          final value = sizeQuantities[size];
          quantity = value is num ? value.toDouble() : 0.0;
        }
      } else if (sizeQuantities is num) {
        // Handle case where sizeQuantities is a direct number
        quantity = sizeQuantities.toDouble();
      }

      // Find nutrition info for this ingredient
      NutritionInfo? ingredientNutrition;
      for (String foodKey in _foodNutritionDatabase.keys) {
        if (ingredientName.contains(foodKey)) {
          ingredientNutrition = _foodNutritionDatabase[foodKey];
          break;
        }
      }

      if (ingredientNutrition != null) {
        // Multiply by quantity (assuming nutrition data is per 100g)
        double multiplier = quantity / 100.0;
        totalNutrition = NutritionInfo(
          calories: totalNutrition.calories + (ingredientNutrition.calories * multiplier),
          protein: totalNutrition.protein + (ingredientNutrition.protein * multiplier),
          carbs: totalNutrition.carbs + (ingredientNutrition.carbs * multiplier),
          fat: totalNutrition.fat + (ingredientNutrition.fat * multiplier),
        );
      }
    });

    return totalNutrition;
  }

  Future<void> addDailyNutrition(
      String memberId, DateTime date, NutritionInfo nutrition) async {
    final dateKey = date.toIso8601String().split('T')[0]; // YYYY-MM-DD format

    if (!_dailyNutrition.containsKey(memberId)) {
      _dailyNutrition[memberId] = {};
    }

    if (_dailyNutrition[memberId]!.containsKey(dateKey)) {
      _dailyNutrition[memberId]![dateKey] =
          _dailyNutrition[memberId]![dateKey]! + nutrition;
    } else {
      _dailyNutrition[memberId]![dateKey] = nutrition;
    }

    await saveNutritionData();
  }

  NutritionInfo? getDailyNutrition(String memberId, DateTime date) {
    final dateKey = date.toIso8601String().split('T')[0];
    return _dailyNutrition[memberId]?[dateKey];
  }

  Map<String, NutritionInfo> getWeeklyNutrition(
      String memberId, DateTime startDate) {
    Map<String, NutritionInfo> weeklyData = {};

    for (int i = 0; i < 7; i++) {
      DateTime date = startDate.add(Duration(days: i));
      String dateKey = date.toIso8601String().split('T')[0];
      NutritionInfo? dayNutrition = getDailyNutrition(memberId, date);

      if (dayNutrition != null) {
        weeklyData[dateKey] = dayNutrition;
      }
    }

    return weeklyData;
  }

  NutritionInfo calculateMonthlyNutritionFromMeals(
      String memberId,
      Map<String, Map<String, Map<String, Map<String, String?>?>>> familyMeals,
      Map<String, Map<String, double>> ingredients,
      ) {
    NutritionInfo totalNutrition =
    const NutritionInfo(calories: 0, protein: 0, carbs: 0, fat: 0);

    final memberMeals = familyMeals[memberId] ?? {};

    for (final weekEntry in memberMeals.entries) {
      final weekData = weekEntry.value;

      for (final mealTypeEntry in weekData.entries) {
        final mealTypeData = mealTypeEntry.value;

        mealTypeData?.forEach((day, dynamic mealData) {
          if (mealData != null && mealData is Map<String, dynamic>) {
            final mealName = mealData['meal']?.toString();
            final size = mealData['size']?.toString();

            if (mealName != null && size != null) {
              final mealNutrition =
              calculateMealNutrition(mealName, size, ingredients);
              totalNutrition = totalNutrition + mealNutrition;
            }
          }
        });
      }
    }

    return totalNutrition;
  }
}

// ===== Member Profile Screen =====

class MemberProfileScreen extends StatefulWidget {
  final MemberManager memberManager;
  final MemberProfile? memberToEdit;

  const MemberProfileScreen({
    Key? key,
    required this.memberManager,
    this.memberToEdit,
  }) : super(key: key);

  @override
  _MemberProfileScreenState createState() => _MemberProfileScreenState();
}

class _MemberProfileScreenState extends State<MemberProfileScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _ageController = TextEditingController();
  final _weightController = TextEditingController();
  final _heightController = TextEditingController();

  String _selectedGender = 'ذكر';
  String _selectedActivityLevel = 'متوسط';
  String _selectedGoal = 'الحفاظ على الوزن';

  List<String> _allergies = [];
  List<String> _dislikedFoods = [];
  List<String> _preferredFoods = [];

  final List<String> _commonAllergies = [
    'الفول السوداني',
    'البيض',
    'اللبن',
    'القمح',
    'الصويا',
    'السمك',
    'المحار',
    'المكسرات'
  ];

  final List<String> _commonFoods = [
    'أرز',
    'خبز',
    'مكرونة',
    'دجاج',
    'لحم',
    'سمك',
    'خضروات',
    'فواكه',
    'ألبان',
    'بقوليات'
  ];

  @override
  void initState() {
    super.initState();
    if (widget.memberToEdit != null) {
      _loadMemberData(widget.memberToEdit!);
    }
  }

  void _loadMemberData(MemberProfile member) {
    _nameController.text = member.name;
    _ageController.text = member.age.toString();
    _weightController.text = member.weight.toString();
    _heightController.text = member.height.toString();
    _selectedGender = member.gender;
    _selectedActivityLevel = member.activityLevel;
    _selectedGoal = member.goal;
    _allergies = List.from(member.allergies);
    _dislikedFoods = List.from(member.dislikedFoods);
    _preferredFoods = List.from(member.preferredFoods);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.memberToEdit != null
            ? 'تعديل الملف الشخصي'
            : 'إنشاء ملف شخصي جديد'),
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [Colors.purple.shade600, Colors.purple.shade800],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: _saveMember,
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              _buildBasicInfoCard(),
              const SizedBox(height: 16),
              _buildPhysicalInfoCard(),
              const SizedBox(height: 16),
              _buildGoalsCard(),
              const SizedBox(height: 16),
              _buildPreferencesCard(),
              const SizedBox(height: 30),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBasicInfoCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('المعلومات الأساسية',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
            const SizedBox(height: 16),
            TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'الاسم',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.person),
              ),
              validator: (value) =>
              value?.isEmpty == true ? 'يرجى إدخال الاسم' : null,
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _ageController,
                    decoration: const InputDecoration(
                      labelText: 'العمر',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.cake),
                    ),
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value?.isEmpty == true) return 'يرجى إدخال العمر';
                      final age = int.tryParse(value!);
                      if (age == null || age < 1 || age > 120)
                        return 'عمر غير صحيح';
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: DropdownButtonFormField<String>(
                    value: _selectedGender,
                    decoration: const InputDecoration(
                      labelText: 'الجنس',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.wc),
                    ),
                    items: ['ذكر', 'أنثى']
                        .map((gender) => DropdownMenuItem(
                        value: gender, child: Text(gender)))
                        .toList(),
                    onChanged: (value) =>
                        setState(() => _selectedGender = value!),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPhysicalInfoCard() {
    double? weight = double.tryParse(_weightController.text);
    double? height = double.tryParse(_heightController.text);
    double? bmi = (weight != null && height != null && height > 0)
        ? weight / ((height / 100) * (height / 100))
        : null;

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('المعلومات الجسدية',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _weightController,
                    decoration: const InputDecoration(
                      labelText: 'الوزن (كجم)',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.monitor_weight),
                    ),
                    keyboardType: TextInputType.number,
                    onChanged: (value) =>
                        setState(() {}), // Trigger BMI recalculation
                    validator: (value) {
                      if (value?.isEmpty == true) return 'يرجى إدخال الوزن';
                      final weight = double.tryParse(value!);
                      if (weight == null || weight < 20 || weight > 300)
                        return 'وزن غير صحيح';
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: TextFormField(
                    controller: _heightController,
                    decoration: const InputDecoration(
                      labelText: 'الطول (سم)',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.height),
                    ),
                    keyboardType: TextInputType.number,
                    onChanged: (value) =>
                        setState(() {}), // Trigger BMI recalculation
                    validator: (value) {
                      if (value?.isEmpty == true) return 'يرجى إدخال الطول';
                      final height = double.tryParse(value!);
                      if (height == null || height < 100 || height > 250)
                        return 'طول غير صحيح';
                      return null;
                    },
                  ),
                ),
              ],
            ),
            if (bmi != null) ...[
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: _getBmiColor(bmi).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: _getBmiColor(bmi)),
                ),
                child: Row(
                  children: [
                    Icon(Icons.analytics, color: _getBmiColor(bmi)),
                    const SizedBox(width: 8),
                    Text(
                      'مؤشر كتلة الجسم: ${bmi.toStringAsFixed(1)} (${_getBmiCategory(bmi)})',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: _getBmiColor(bmi),
                      ),
                    ),
                  ],
                ),
              ),
            ],
            const SizedBox(height: 12),
            DropdownButtonFormField<String>(
              value: _selectedActivityLevel,
              decoration: const InputDecoration(
                labelText: 'مستوى النشاط',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.fitness_center),
              ),
              items: ['قليل', 'متوسط', 'عالي', 'عالي جداً']
                  .map((level) =>
                  DropdownMenuItem(value: level, child: Text(level)))
                  .toList(),
              onChanged: (value) =>
                  setState(() => _selectedActivityLevel = value!),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGoalsCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('الأهداف',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
            const SizedBox(height: 16),
            DropdownButtonFormField<String>(
              value: _selectedGoal,
              decoration: const InputDecoration(
                labelText: 'الهدف الغذائي',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.flag),
              ),
              items: [
                'إنقاص الوزن',
                'زيادة الوزن',
                'الحفاظ على الوزن',
                'بناء العضلات'
              ]
                  .map((goal) =>
                  DropdownMenuItem(value: goal, child: Text(goal)))
                  .toList(),
              onChanged: (value) => setState(() => _selectedGoal = value!),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPreferencesCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('التفضيلات والحساسيات',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
            const SizedBox(height: 16),
            _buildChipSection(
                'الحساسيات الغذائية', _allergies, _commonAllergies, Colors.red),
            const SizedBox(height: 16),
            _buildChipSection('الأطعمة غير المفضلة', _dislikedFoods,
                _commonFoods, Colors.orange),
            const SizedBox(height: 16),
            _buildChipSection(
                'الأطعمة المفضلة', _preferredFoods, _commonFoods, Colors.green),
          ],
        ),
      ),
    );
  }

  Widget _buildChipSection(String title, List<String> selectedItems,
      List<String> availableItems, Color color) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(title,
                style:
                const TextStyle(fontSize: 16, fontWeight: FontWeight.w600)),
            IconButton(
              icon: Icon(Icons.add, color: color),
              onPressed: () => _showItemSelectionDialog(
                  title, selectedItems, availableItems, color),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 4,
          children: selectedItems.map((item) {
            return Chip(
              label: Text(item, style: const TextStyle(color: Colors.white)),
              backgroundColor: color,
              deleteIcon:
              const Icon(Icons.close, color: Colors.white, size: 18),
              onDeleted: () {
                setState(() {
                  selectedItems.remove(item);
                });
              },
            );
          }).toList(),
        ),
        if (selectedItems.isEmpty)
          Text('لم يتم تحديد أي عناصر',
              style: TextStyle(color: Colors.grey.shade600, fontSize: 14)),
      ],
    );
  }

  void _showItemSelectionDialog(String title, List<String> selectedItems,
      List<String> availableItems, Color color) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          shape:
          RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
          title: Text('اختيار $title'),
          content: SizedBox(
            width: double.maxFinite,
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: availableItems.length,
              itemBuilder: (context, index) {
                final item = availableItems[index];
                final isSelected = selectedItems.contains(item);

                return CheckboxListTile(
                  title: Text(item),
                  value: isSelected,
                  activeColor: color,
                  onChanged: (value) {
                    setState(() {
                      if (value == true) {
                        if (!selectedItems.contains(item)) {
                          selectedItems.add(item);
                        }
                      } else {
                        selectedItems.remove(item);
                      }
                    });
                    Navigator.pop(context);
                    _showItemSelectionDialog(
                        title, selectedItems, availableItems, color);
                  },
                );
              },
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('إغلاق'),
            ),
          ],
        );
      },
    );
  }

  Color _getBmiColor(double bmi) {
    if (bmi < 18.5) return Colors.blue;
    if (bmi < 25) return Colors.green;
    if (bmi < 30) return Colors.orange;
    return Colors.red;
  }

  String _getBmiCategory(double bmi) {
    if (bmi < 18.5) return 'نقص في الوزن';
    if (bmi < 25) return 'وزن طبيعي';
    if (bmi < 30) return 'زيادة في الوزن';
    return 'سمنة';
  }

  void _saveMember() async {
    if (_formKey.currentState?.validate() != true) return;

    final member = MemberProfile(
      id: widget.memberToEdit?.id ??
          DateTime.now().millisecondsSinceEpoch.toString(),
      name: _nameController.text,
      age: int.parse(_ageController.text),
      gender: _selectedGender,
      weight: double.parse(_weightController.text),
      height: double.parse(_heightController.text),
      activityLevel: _selectedActivityLevel,
      goal: _selectedGoal,
      allergies: _allergies,
      dislikedFoods: _dislikedFoods,
      preferredFoods: _preferredFoods,
      dateCreated: widget.memberToEdit?.dateCreated ?? DateTime.now(),
      lastUpdated: DateTime.now(),
    );

    try {
      if (widget.memberToEdit != null) {
        await widget.memberManager.updateMember(member);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم تحديث الملف الشخصي بنجاح')),
        );
      } else {
        await widget.memberManager.addMember(member);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم إنشاء الملف الشخصي بنجاح')),
        );
      }
      Navigator.pop(context, true);
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('خطأ في حفظ الملف الشخصي: $e')),
      );
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _ageController.dispose();
    _weightController.dispose();
    _heightController.dispose();
    super.dispose();
  }
}

// ===== External Services Integration and Performance Enhancements =====

// CloudSyncService class with http import
class CloudSyncService {
  static final http.Client _httpClient = http.Client();

  static const String _baseUrl = 'https://api.mealplanner.com'; // Example API
  final String _apiKey = 'your_api_key_here'; // In production, store securely

  Future<bool> syncMealPlans(Map<String, dynamic> mealPlansData) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/sync/meal-plans'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $_apiKey',
        },
        body: json.encode(mealPlansData),
      );

      return response.statusCode == 200;
    } catch (e) {
      print('Error syncing meal plans: $e');
      return false;
    }
  }

  Future<Map<String, dynamic>?> fetchMealPlans(String userId) async {
    try {
      final response = await _httpClient.get(
        Uri.parse('$_baseUrl/sync/meal-plans/$userId'),
        headers: {
          'Authorization': 'Bearer $_apiKey',
        },
      );

      if (response.statusCode == 200) {
        return json.decode(response.body);
      }
      return null;
    } catch (e) {
      print('Error fetching meal plans: $e');
      return null;
    }
  }

  Future<List<Map<String, dynamic>>> searchRecipes(String query) async {
    try {
      final response = await _httpClient.get(
        Uri.parse('$_baseUrl/recipes/search?q=${Uri.encodeComponent(query)}'),
        headers: {
          'Authorization': 'Bearer $_apiKey',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return List<Map<String, dynamic>>.from(data['recipes'] ?? []);
      }
      return [];
    } catch (e) {
      print('Error searching recipes: $e');
      return [];
    }
  }

  Future<Map<String, dynamic>?> getNutritionInfo(String foodName) async {
    try {
      final response = await _httpClient.get(
        Uri.parse('$_baseUrl/nutrition/${Uri.encodeComponent(foodName)}'),
        headers: {
          'Authorization': 'Bearer $_apiKey',
        },
      );

      if (response.statusCode == 200) {
        return json.decode(response.body);
      }
      return null;
    } catch (e) {
      print('Error getting nutrition info: $e');
      return null;
    }
  }
}

// GroceryDeliveryService class with http import
class GroceryDeliveryService {
  static final http.Client _httpClient = http.Client();

  static const String _baseUrl =
      'https://api.grocerydelivery.com'; // Example API
  final String _apiKey = 'your_grocery_api_key_here';

  Future<bool> createShoppingOrder(
      List<ShoppingItem> items, String deliveryAddress) async {
    try {
      final orderData = {
        'items': items
            .map((item) => {
          'name': item.name,
          'quantity': item.quantity,
          'unit': item.unit,
        })
            .toList(),
        'delivery_address': deliveryAddress,
        'delivery_time': 'ASAP',
      };

      final response = await _httpClient.post(
        Uri.parse('$_baseUrl/orders'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $_apiKey',
        },
        body: json.encode(orderData),
      );

      return response.statusCode == 201;
    } catch (e) {
      print('Error creating shopping order: $e');
      return false;
    }
  }

  Future<List<Map<String, dynamic>>> searchProducts(String query) async {
    try {
      final response = await _httpClient.get(
        Uri.parse('$_baseUrl/products/search?q=${Uri.encodeComponent(query)}'),
        headers: {
          'Authorization': 'Bearer $_apiKey',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return List<Map<String, dynamic>>.from(data['products'] ?? []);
      }
      return [];
    } catch (e) {
      print('Error searching products: $e');
      return [];
    }
  }

  Future<Map<String, double>?> getPrices(List<String> productNames) async {
    try {
      final response = await _httpClient.post(
        Uri.parse('$_baseUrl/prices'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $_apiKey',
        },
        body: json.encode({'products': productNames}),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return Map<String, double>.from(data['prices'] ?? {});
      }
      return null;
    } catch (e) {
      print('Error getting prices: $e');
      return null;
    }
  }
}

class CalendarIntegrationService {
  Future<bool> addMealToCalendar(
      String mealName, DateTime dateTime, String description) async {
    try {
      // This would integrate with device calendar
      // For now, we'll simulate the integration
      await Future.delayed(const Duration(milliseconds: 500));

      // In a real implementation, you would use calendar plugins like:
      // - device_calendar
      // - add_2_calendar

      print('Adding meal to calendar: $mealName at $dateTime');
      return true;
    } catch (e) {
      print('Error adding meal to calendar: $e');
      return false;
    }
  }

  Future<bool> createWeeklyMealReminders(
      Map<String, List<String>> weeklyMeals) async {
    try {
      for (final entry in weeklyMeals.entries) {
        final day = entry.key;
        final meals = entry.value;

        for (final meal in meals) {
          // Create reminder for each meal
          await addMealToCalendar(meal, _getDateForDay(day), 'وقت تحضير $meal');
        }
      }
      return true;
    } catch (e) {
      print('Error creating weekly meal reminders: $e');
      return false;
    }
  }

  DateTime _getDateForDay(String day) {
    final now = DateTime.now();
    final daysOfWeek = [
      'السبت',
      'الأحد',
      'الإثنين',
      'الثلاثاء',
      'الأربعاء',
      'الخميس',
      'الجمعة'
    ];
    final dayIndex = daysOfWeek.indexOf(day);
    final currentDayIndex = now.weekday % 7; // Convert to Saturday = 0 format

    int daysToAdd = dayIndex - currentDayIndex;
    if (daysToAdd <= 0) daysToAdd += 7; // Next week if day has passed

    return now.add(Duration(days: daysToAdd));
  }
}

class NotificationService {
  static const String _channelId = 'meal_planner_notifications';
  static const String _channelName = 'Meal Planner';
  static const String _channelDescription =
      'Notifications for meal planning and reminders';

  Future<void> initialize() async {
    // Initialize local notifications
    // In a real app, you would use flutter_local_notifications
    print('Initializing notification service...');
  }

  Future<void> scheduleMealReminder(
      String mealName, DateTime scheduledTime) async {
    try {
      // Schedule a local notification
      print('Scheduling reminder for $mealName at $scheduledTime');

      // In a real implementation:
      // await flutterLocalNotificationsPlugin.zonedSchedule(
      //   id,
      //   'وقت تحضير الوجبة',
      //   'حان وقت تحضير $mealName',
      //   tz.TZDateTime.from(scheduledTime, tz.local),
      //   notificationDetails,
      //   uiLocalNotificationDateInterpretation: UILocalNotificationDateInterpretation.absoluteTime,
      // );
    } catch (e) {
      print('Error scheduling meal reminder: $e');
    }
  }

  Future<void> scheduleShoppingReminder(
      DateTime scheduledTime, int itemCount) async {
    try {
      print(
          'Scheduling shopping reminder for $scheduledTime with $itemCount items');

      // Schedule notification for shopping
    } catch (e) {
      print('Error scheduling shopping reminder: $e');
    }
  }

  Future<void> showInstantNotification(String title, String body) async {
    try {
      print('Showing notification: $title - $body');

      // Show immediate notification
    } catch (e) {
      print('Error showing notification: $e');
    }
  }

  Future<void> cancelAllNotifications() async {
    try {
      print('Cancelling all notifications');

      // Cancel all scheduled notifications
    } catch (e) {
      print('Error cancelling notifications: $e');
    }
  }
}

class PerformanceOptimizer {
  static const int _maxCacheSize = 100;
  static final Map<String, dynamic> _cache = {};
  static final Map<String, DateTime> _cacheTimestamps = {};
  static const Duration _cacheExpiry = Duration(hours: 1);

  static T? getCachedData<T>(String key) {
    if (_cache.containsKey(key)) {
      final timestamp = _cacheTimestamps[key];
      if (timestamp != null &&
          DateTime.now().difference(timestamp) < _cacheExpiry) {
        return _cache[key] as T?;
      } else {
        // Remove expired cache
        _cache.remove(key);
        _cacheTimestamps.remove(key);
      }
    }
    return null;
  }

  static void setCachedData<T>(String key, T data) {
    // Implement LRU cache eviction if needed
    if (_cache.length >= _maxCacheSize) {
      final oldestKey = _cacheTimestamps.entries
          .reduce((a, b) => a.value.isBefore(b.value) ? a : b)
          .key;
      _cache.remove(oldestKey);
      _cacheTimestamps.remove(oldestKey);
    }

    _cache[key] = data;
    _cacheTimestamps[key] = DateTime.now();
  }

  static void clearCache() {
    _cache.clear();
    _cacheTimestamps.clear();
  }

  static Future<List<T>> loadDataInBatches<T>(
      Future<List<T>> Function(int offset, int limit) loadFunction,
      int batchSize,
      ) async {
    List<T> allData = [];
    int offset = 0;
    bool hasMore = true;

    while (hasMore) {
      final batch = await loadFunction(offset, batchSize);
      allData.addAll(batch);

      hasMore = batch.length == batchSize;
      offset += batchSize;

      // Add small delay to prevent overwhelming the system
      await Future.delayed(const Duration(milliseconds: 50));
    }

    return allData;
  }

  static Future<void> preloadImages(List<String> imageUrls) async {
    for (final url in imageUrls) {
      try {
        // Preload images for better performance
        // In a real app, you would use precacheImage or similar
        print('Preloading image: $url');
        await Future.delayed(const Duration(milliseconds: 10));
      } catch (e) {
        print('Error preloading image $url: $e');
      }
    }
  }
}

class DatabaseManager {
  static Database? _database;
  static const String _databaseName = 'meal_planner.db';
  static const int _databaseVersion = 1;

  static Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  static Future<Database> _initDatabase() async {
    final databasesPath = await getDatabasesPath();
    final dbPath = path.join(databasesPath, _databaseName);

    return await openDatabase(
      dbPath,
      version: _databaseVersion,
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
    );
  }

  static Future<void> _onCreate(Database db, int version) async {
    // Create tables for local storage
    await db.execute('''
      CREATE TABLE meal_plans (
        id TEXT PRIMARY KEY,
        member_id TEXT NOT NULL,
        week TEXT NOT NULL,
        meal_type TEXT NOT NULL,
        day TEXT NOT NULL,
        meal_name TEXT,
        meal_size TEXT,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL
      )
    ''');

    await db.execute('''
      CREATE TABLE custom_meals (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        category TEXT NOT NULL,
        description TEXT,
        ingredients TEXT NOT NULL,
        preparation_steps TEXT NOT NULL,
        image_url TEXT,
        video_url TEXT,
        difficulty INTEGER DEFAULT 3,
        preparation_time INTEGER DEFAULT 30,
        nutrition_info TEXT,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL
      )
    ''');

    await db.execute('''
      CREATE TABLE member_profiles (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        age INTEGER NOT NULL,
        gender TEXT NOT NULL,
        weight REAL NOT NULL,
        height REAL NOT NULL,
        activity_level TEXT NOT NULL,
        goal TEXT NOT NULL,
        allergies TEXT,
        disliked_foods TEXT,
        preferred_foods TEXT,
        daily_nutrition_goals TEXT,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL
      )
    ''');

    await db.execute('''
      CREATE TABLE shopping_items (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        quantity REAL NOT NULL,
        unit TEXT NOT NULL,
        category TEXT NOT NULL,
        is_purchased INTEGER DEFAULT 0,
        estimated_price REAL,
        created_at INTEGER NOT NULL
      )
    ''');

    await db.execute('''
      CREATE TABLE nutrition_tracking (
        id TEXT PRIMARY KEY,
        member_id TEXT NOT NULL,
        date TEXT NOT NULL,
        calories REAL NOT NULL,
        protein REAL NOT NULL,
        carbs REAL NOT NULL,
        fat REAL NOT NULL,
        fiber REAL DEFAULT 0,
        sugar REAL DEFAULT 0,
        sodium REAL DEFAULT 0,
        vitamins TEXT,
        minerals TEXT,
        created_at INTEGER NOT NULL
      )
    ''');

    // Create indexes for better performance
    await db.execute(
        'CREATE INDEX idx_meal_plans_member_week ON meal_plans(member_id, week)');
    await db.execute(
        'CREATE INDEX idx_nutrition_tracking_member_date ON nutrition_tracking(member_id, date)');
    await db.execute(
        'CREATE INDEX idx_shopping_items_category ON shopping_items(category)');
  }

  static Future<void> _onUpgrade(
      Database db, int oldVersion, int newVersion) async {
    // Handle database schema upgrades
    if (oldVersion < 2) {
      // Add new columns or tables for version 2
    }
  }

  static Future<void> insertMealPlan(Map<String, dynamic> mealPlan) async {
    final db = await database;
    await db.insert('meal_plans', mealPlan,
        conflictAlgorithm: ConflictAlgorithm.replace);
  }

  static Future<List<Map<String, dynamic>>> getMealPlans(
      String memberId) async {
    final db = await database;
    return await db.query(
      'meal_plans',
      where: 'member_id = ?',
      whereArgs: [memberId],
      orderBy: 'week, meal_type, day',
    );
  }

  static Future<void> insertCustomMeal(CustomMeal meal) async {
    final db = await database;
    final mealData = {
      'id': meal.id,
      'name': meal.name,
      'category': meal.category,
      'description': meal.description,
      'ingredients':
      json.encode(meal.ingredients.map((i) => i.toJson()).toList()),
      'preparation_steps': json.encode(meal.preparationSteps),
      'image_url': meal.imageUrl,
      'video_url': meal.videoUrl,
      'difficulty': meal.difficulty,
      'preparation_time': meal.preparationTime,
      'nutrition_info': json.encode(meal.nutritionInfo),
      'created_at': meal.dateCreated.millisecondsSinceEpoch,
      'updated_at': meal.dateCreated.millisecondsSinceEpoch,
    };

    await db.insert('custom_meals', mealData,
        conflictAlgorithm: ConflictAlgorithm.replace);
  }

  static Future<List<CustomMeal>> getCustomMeals() async {
    final db = await database;
    final maps = await db.query('custom_meals', orderBy: 'name');

    return maps.map((map) {
      return CustomMeal(
        id: map['id'] as String,
        name: map['name'] as String,
        category: map['category'] as String,
        description: map['description'] as String? ?? '',
        ingredients: (json.decode(map['ingredients'] as String) as List)
            .map((i) => CustomIngredient.fromJson(i))
            .toList(),
        preparationSteps:
        List<String>.from(json.decode(map['preparation_steps'] as String)),
        imageUrl: map['image_url'] as String?,
        videoUrl: map['video_url'] as String?,
        difficulty: map['difficulty'] as int? ?? 3,
        preparationTime: map['preparation_time'] as int? ?? 30,
        nutritionInfo: Map<String, int>.from(
            json.decode(map['nutrition_info'] as String? ?? '{}')),
        dateCreated:
        DateTime.fromMillisecondsSinceEpoch(map['created_at'] as int),
        createdBy: 'المستخدم',
      );
    }).toList();
  }

  static Future<void> deleteCustomMeal(String mealId) async {
    final db = await database;
    await db.delete('custom_meals', where: 'id = ?', whereArgs: [mealId]);
  }

  static Future<void> insertMemberProfile(MemberProfile member) async {
    final db = await database;
    final memberData = {
      'id': member.id,
      'name': member.name,
      'age': member.age,
      'gender': member.gender,
      'weight': member.weight,
      'height': member.height,
      'activity_level': member.activityLevel,
      'goal': member.goal,
      'allergies': json.encode(member.allergies),
      'disliked_foods': json.encode(member.dislikedFoods),
      'preferred_foods': json.encode(member.preferredFoods),
      'daily_nutrition_goals': json.encode(member.dailyNutritionGoals),
      'created_at': member.dateCreated.millisecondsSinceEpoch,
      'updated_at': member.lastUpdated.millisecondsSinceEpoch,
    };

    await db.insert('member_profiles', memberData,
        conflictAlgorithm: ConflictAlgorithm.replace);
  }

  static Future<List<MemberProfile>> getMemberProfiles() async {
    final db = await database;
    final maps = await db.query('member_profiles', orderBy: 'name');

    return maps.map((map) {
      return MemberProfile(
        id: map['id'] as String,
        name: map['name'] as String,
        age: map['age'] as int,
        gender: map['gender'] as String,
        weight: map['weight'] as double,
        height: map['height'] as double,
        activityLevel: map['activity_level'] as String,
        goal: map['goal'] as String,
        allergies:
        List<String>.from(json.decode(map['allergies'] as String? ?? '[]')),
        dislikedFoods: List<String>.from(
            json.decode(map['disliked_foods'] as String? ?? '[]')),
        preferredFoods: List<String>.from(
            json.decode(map['preferred_foods'] as String? ?? '[]')),
        dailyNutritionGoals: Map<String, double>.from(
            json.decode(map['daily_nutrition_goals'] as String? ?? '{}')),
        dateCreated:
        DateTime.fromMillisecondsSinceEpoch(map['created_at'] as int),
        lastUpdated:
        DateTime.fromMillisecondsSinceEpoch(map['updated_at'] as int),
      );
    }).toList();
  }

  static Future<void> deleteMemberProfile(String memberId) async {
    final db = await database;
    await db.delete('member_profiles', where: 'id = ?', whereArgs: [memberId]);
  }

  static Future<void> insertShoppingItem(ShoppingItem item) async {
    final db = await database;
    final itemData = {
      'id': item.id,
      'name': item.name,
      'quantity': item.quantity,
      'unit': item.unit,
      'category': item.category,
      'is_purchased': item.isPurchased ? 1 : 0,
      'estimated_price': item.estimatedPrice,
      'created_at': item.dateAdded.millisecondsSinceEpoch,
    };

    await db.insert('shopping_items', itemData,
        conflictAlgorithm: ConflictAlgorithm.replace);
  }

  static Future<List<ShoppingItem>> getShoppingItems() async {
    final db = await database;
    final maps = await db.query('shopping_items', orderBy: 'category, name');

    return maps.map((map) {
      return ShoppingItem(
        id: map['id'] as String,
        name: map['name'] as String,
        quantity: map['quantity'] as double,
        unit: map['unit'] as String,
        category: map['category'] as String,
        isPurchased: (map['is_purchased'] as int) == 1,
        estimatedPrice: map['estimated_price'] as double?,
        dateAdded:
        DateTime.fromMillisecondsSinceEpoch(map['created_at'] as int),
      );
    }).toList();
  }

  static Future<void> deleteShoppingItem(String itemId) async {
    final db = await database;
    await db.delete('shopping_items', where: 'id = ?', whereArgs: [itemId]);
  }

  static Future<void> updateShoppingItemPurchased(
      String itemId, bool isPurchased) async {
    final db = await database;
    await db.update(
      'shopping_items',
      {'is_purchased': isPurchased ? 1 : 0},
      where: 'id = ?',
      whereArgs: [itemId],
    );
  }

  static Future<void> insertNutritionTracking(
      String memberId, DateTime date, NutritionInfo nutrition) async {
    final db = await database;
    final nutritionData = {
      'id': '${memberId}_${date.toIso8601String().split('T')[0]}',
      'member_id': memberId,
      'date': date.toIso8601String().split('T')[0],
      'calories': nutrition.calories,
      'protein': nutrition.protein,
      'carbs': nutrition.carbs,
      'fat': nutrition.fat,
      'fiber': nutrition.fiber,
      'sugar': nutrition.sugar,
      'sodium': nutrition.sodium,
      'vitamins': json.encode(nutrition.vitamins),
      'minerals': json.encode(nutrition.minerals),
      'created_at': DateTime.now().millisecondsSinceEpoch,
    };

    await db.insert('nutrition_tracking', nutritionData,
        conflictAlgorithm: ConflictAlgorithm.replace);
  }

  static Future<NutritionInfo?> getNutritionTracking(
      String memberId, DateTime date) async {
    final db = await database;
    final dateKey = date.toIso8601String().split('T')[0];
    final maps = await db.query(
      'nutrition_tracking',
      where: 'member_id = ? AND date = ?',
      whereArgs: [memberId, dateKey],
    );

    if (maps.isNotEmpty) {
      final map = maps.first;
      return NutritionInfo(
        calories: map['calories'] as double,
        protein: map['protein'] as double,
        carbs: map['carbs'] as double,
        fat: map['fat'] as double,
        fiber: map['fiber'] as double? ?? 0,
        sugar: map['sugar'] as double? ?? 0,
        sodium: map['sodium'] as double? ?? 0,
        vitamins: Map<String, double>.from(
            json.decode(map['vitamins'] as String? ?? '{}')),
        minerals: Map<String, double>.from(
            json.decode(map['minerals'] as String? ?? '{}')),
      );
    }

    return null;
  }

  static Future<void> clearAllData() async {
    final db = await database;
    await db.delete('meal_plans');
    await db.delete('custom_meals');
    await db.delete('member_profiles');
    await db.delete('shopping_items');
    await db.delete('nutrition_tracking');
  }

  static Future<void> closeDatabase() async {
    final db = _database;
    if (db != null) {
      await db.close();
      _database = null;
    }
  }
}

// ===== AI-Powered Meal Recommendations =====

// AIRecommendationEngine class with http import
class AIRecommendationEngine {
  static final http.Client _httpClient = http.Client();

  static const String _openAIApiKey =
      'your_openai_api_key_here'; // Store securely in production
  static const String _baseUrl = 'https://api.openai.com/v1';

  Future<List<String>> generateMealRecommendations({
    required MemberProfile memberProfile,
    required List<String> availableMeals,
    required String mealType,
    required Map<String, int> recentMeals, // meal name -> times eaten this week
  }) async {
    try {
      final prompt = _buildRecommendationPrompt(
          memberProfile, availableMeals, mealType, recentMeals);

      final response = await _httpClient.post(
        Uri.parse('$_baseUrl/chat/completions'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $_openAIApiKey',
        },
        body: json.encode({
          'model': 'gpt-3.5-turbo',
          'messages': [
            {
              'role': 'system',
              'content':
              'أنت خبير تغذية متخصص في التخطيط للوجبات العربية والشرق أوسطية.'
            },
            {'role': 'user', 'content': prompt},
          ],
          'max_tokens': 500,
          'temperature': 0.7,
        }),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final content = data['choices'][0]['message']['content'] as String;

        // Parse the AI response to extract meal recommendations
        return _parseRecommendations(content, availableMeals);
      }

      return _getFallbackRecommendations(
          memberProfile, availableMeals, mealType);
    } catch (e) {
      print('Error generating AI recommendations: $e');
      return _getFallbackRecommendations(
          memberProfile, availableMeals, mealType);
    }
  }

  String _buildRecommendationPrompt(
      MemberProfile memberProfile,
      List<String> availableMeals,
      String mealType,
      Map<String, int> recentMeals,
      ) {
    final buffer = StringBuffer();

    buffer.writeln('أحتاج توصيات لوجبة $mealType لشخص بالمواصفات التالية:');
    buffer.writeln('- العمر: ${memberProfile.age} سنة');
    buffer.writeln('- الجنس: ${memberProfile.gender}');
    buffer.writeln('- الوزن: ${memberProfile.weight} كجم');
    buffer.writeln('- الطول: ${memberProfile.height} سم');
    buffer.writeln('- مستوى النشاط: ${memberProfile.activityLevel}');
    buffer.writeln('- الهدف: ${memberProfile.goal}');

    if (memberProfile.allergies.isNotEmpty) {
      buffer.writeln('- الحساسيات: ${memberProfile.allergies.join(', ')}');
    }

    if (memberProfile.dislikedFoods.isNotEmpty) {
      buffer.writeln(
          '- الأطعمة غير المفضلة: ${memberProfile.dislikedFoods.join(', ')}');
    }

    if (memberProfile.preferredFoods.isNotEmpty) {
      buffer.writeln(
          '- الأطعمة المفضلة: ${memberProfile.preferredFoods.join(', ')}');
    }

    buffer.writeln('\nالوجبات المتاحة: ${availableMeals.join(', ')}');

    if (recentMeals.isNotEmpty) {
      buffer.writeln('\nالوجبات التي تم تناولها مؤخراً:');
      recentMeals.forEach((meal, count) {
        buffer.writeln('- $meal: $count مرة');
      });
    }

    buffer.writeln(
        '\nيرجى ترشيح 5 وجبات مناسبة من القائمة المتاحة، مرتبة حسب الأولوية، مع تجنب التكرار المفرط.');
    buffer.writeln('اذكر أسماء الوجبات فقط، كل وجبة في سطر منفصل.');

    return buffer.toString();
  }

  List<String> _parseRecommendations(
      String aiResponse, List<String> availableMeals) {
    final lines = aiResponse.split('\n');
    final recommendations = <String>[];

    for (final line in lines) {
      final cleanLine = line.trim().replaceAll(RegExp(r'^[-•*]\s*'), '');

      // Find matching meal from available meals
      for (final meal in availableMeals) {
        if (cleanLine.contains(meal) || meal.contains(cleanLine)) {
          if (!recommendations.contains(meal)) {
            recommendations.add(meal);
          }
          break;
        }
      }

      if (recommendations.length >= 5) break;
    }

    return recommendations;
  }

  List<String> _getFallbackRecommendations(
      MemberProfile memberProfile,
      List<String> availableMeals,
      String mealType,
      ) {
    // Simple rule-based fallback recommendations
    final recommendations = <String>[];
    final shuffledMeals = List<String>.from(availableMeals)..shuffle();

    for (final meal in shuffledMeals) {
      bool isRecommended = true;

      // Check allergies
      for (final allergy in memberProfile.allergies) {
        if (meal.contains(allergy)) {
          isRecommended = false;
          break;
        }
      }

      // Check disliked foods
      if (isRecommended && memberProfile.dislikedFoods.contains(meal)) {
        isRecommended = false;
      }

      if (isRecommended) {
        recommendations.add(meal);
        if (recommendations.length >= 5) break;
      }
    }

    return recommendations;
  }

  Future<String> generateMealDescription(
      String mealName, List<String> ingredients) async {
    try {
      final prompt =
          'اكتب وصفاً مختصراً وجذاباً لوجبة "$mealName" التي تحتوي على المكونات التالية: ${ingredients.join(', ')}. الوصف يجب أن يكون في جملة أو جملتين فقط.';

      final response = await _httpClient.post(
        Uri.parse('$_baseUrl/chat/completions'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $_openAIApiKey',
        },
        body: json.encode({
          'model': 'gpt-3.5-turbo',
          'messages': [
            {
              'role': 'system',
              'content': 'أنت كاتب محتوى متخصص في وصف الأطعمة العربية.'
            },
            {'role': 'user', 'content': prompt},
          ],
          'max_tokens': 150,
          'temperature': 0.8,
        }),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return data['choices'][0]['message']['content'] as String;
      }

      return 'وجبة لذيذة ومغذية تحتوي على مكونات طازجة ومتنوعة.';
    } catch (e) {
      print('Error generating meal description: $e');
      return 'وجبة لذيذة ومغذية تحتوي على مكونات طازجة ومتنوعة.';
    }
  }
}

// ===== Smart Meal Planning Assistant =====

class SmartMealPlanningAssistant {
  final AIRecommendationEngine _aiEngine = AIRecommendationEngine();
  final MemberManager _memberManager;
  final Map<String, Map<String, dynamic>> _availableMeals;

  SmartMealPlanningAssistant(this._memberManager, this._availableMeals);

  Future<Map<String, List<String>>> generateWeeklyPlan(String memberId) async {
    final member = _memberManager.getMemberById(memberId);
    if (member == null) return {};

    final weeklyPlan = <String, List<String>>{};
    final days = [
      'السبت',
      'الأحد',
      'الإثنين',
      'الثلاثاء',
      'الأربعاء',
      'الخميس',
      'الجمعة'
    ];
    final mealTypes = ['الفطور', 'الغداء', 'العشاء'];

    final recentMeals = <String, int>{}; // Track meal frequency

    for (final day in days) {
      final dayMeals = <String>[];

      for (final mealType in mealTypes) {
        final availableMealsForType = _getAvailableMealsForType(mealType);

        final recommendations = await _aiEngine.generateMealRecommendations(
          memberProfile: member,
          availableMeals: availableMealsForType,
          mealType: mealType,
          recentMeals: recentMeals,
        );

        if (recommendations.isNotEmpty) {
          final selectedMeal = recommendations.first;
          dayMeals.add(selectedMeal);

          // Update recent meals tracking
          recentMeals[selectedMeal] = (recentMeals[selectedMeal] ?? 0) + 1;
        }
      }

      weeklyPlan[day] = dayMeals;
    }

    return weeklyPlan;
  }

  List<String> _getAvailableMealsForType(String mealType) {
    final meals = <String>[];

    _availableMeals.forEach((category, categoryMeals) {
      if (categoryMeals.containsKey(mealType)) {
        meals.addAll(List<String>.from(categoryMeals[mealType] ?? []));
      }
    });

    return meals;
  }

  Future<List<String>> getPersonalizedRecommendations(
      String memberId, String mealType) async {
    final member = _memberManager.getMemberById(memberId);
    if (member == null) return [];

    final availableMeals = _getAvailableMealsForType(mealType);

    return await _aiEngine.generateMealRecommendations(
      memberProfile: member,
      availableMeals: availableMeals,
      mealType: mealType,
      recentMeals: {},
    );
  }

  Future<Map<String, dynamic>> analyzeMealPlan(
      String memberId,
      Map<String, Map<String, Map<String, Map<String, String?>?>>> familyMeals,
      ) async {
    final member = _memberManager.getMemberById(memberId);
    if (member == null) return {};

    final analysis = <String, dynamic>{};

    // Calculate nutritional analysis
    final nutritionTracker = NutritionTracker();
    final totalNutrition = nutritionTracker.calculateMonthlyNutritionFromMeals(
      memberId,
      familyMeals,
      {}, // ingredients map would be passed here
    );

    // Calculate daily averages (assuming 30 days)
    final dailyCalories = totalNutrition.calories / 30;
    final targetCalories = member.dailyCalorieNeeds;

    analysis['nutrition'] = {
      'daily_calories': dailyCalories,
      'target_calories': targetCalories,
      'calorie_difference': dailyCalories - targetCalories,
      'daily_protein': totalNutrition.protein / 30,
      'daily_carbs': totalNutrition.carbs / 30,
      'daily_fat': totalNutrition.fat / 30,
    };

    // Analyze meal variety
    final mealCounts = <String, int>{};
    final memberMeals = familyMeals[memberId];
    if (memberMeals != null) {
      memberMeals.forEach((week, weekData) {
        if (weekData != null) {
          weekData.forEach((mealType, mealTypeData) {
            if (mealTypeData != null) {
              (mealTypeData as Map<String, dynamic>?)?.forEach((day, dynamic mealData) {
                if (mealData != null && mealData is Map<String, dynamic>) {
                  final mealName = mealData['meal']?.toString();
                  if (mealName != null && mealName.isNotEmpty) {
                    mealCounts[mealName] = (mealCounts[mealName] ?? 0) + 1;
                  }
                }
              });
            }
          });
        }
      });
    }

    analysis['variety'] = {
      'unique_meals': mealCounts.length,
      'most_frequent_meal': mealCounts.isNotEmpty
          ? mealCounts.entries.reduce((a, b) => a.value > b.value ? a : b).key
          : '',
      'meal_frequency': mealCounts,
    };

    // Generate recommendations
    final recommendations = <String>[];

    if (dailyCalories < targetCalories * 0.8) {
      recommendations.add(
          'السعرات الحرارية أقل من المطلوب. يُنصح بإضافة وجبات أكثر أو زيادة أحجام الوجبات.');
    } else if (dailyCalories > targetCalories * 1.2) {
      recommendations.add(
          'السعرات الحرارية أعلى من المطلوب. يُنصح بتقليل أحجام الوجبات أو اختيار وجبات أقل في السعرات.');
    }

    if (mealCounts.length < 10) {
      recommendations.add(
          'تنوع الوجبات قليل. حاول إضافة وجبات جديدة لتحسين التنوع الغذائي.');
    }

    if (totalNutrition.protein / 30 < member.weight * 0.8) {
      recommendations.add(
          'كمية البروتين قد تكون غير كافية. أضف المزيد من مصادر البروتين مثل اللحوم والبقوليات.');
    }

    analysis['recommendations'] = recommendations;

    return analysis;
  }
}

// ===== Advanced and Innovative Features =====

// Voice Assistant Integration
class VoiceAssistant {
  static const String _speechToTextLanguage = 'ar-SA'; // Arabic (Saudi Arabia)

  Future<void> initialize() async {
    // Initialize speech recognition and text-to-speech
    print('Initializing voice assistant...');
    // In a real app, you would use speech_to_text and flutter_tts packages
  }

  Future<String?> listenForCommand() async {
    try {
      // Listen for voice command
      print('Listening for voice command...');

      // Simulate voice recognition
      await Future.delayed(const Duration(seconds: 2));

      // In a real implementation:
      // final speechToText = SpeechToText();
      // final result = await speechToText.listen();
      // return result.recognizedWords;

      return 'أضف دجاج مشوي لوجبة الغداء يوم السبت';
    } catch (e) {
      print('Error listening for voice command: $e');
      return null;
    }
  }

  Future<void> speakResponse(String text) async {
    try {
      print('Speaking: $text');

      // In a real implementation:
      // final flutterTts = FlutterTts();
      // await flutterTts.setLanguage('ar-SA');
      // await flutterTts.speak(text);
    } catch (e) {
      print('Error speaking response: $e');
    }
  }

  Future<Map<String, dynamic>?> processVoiceCommand(String command) async {
    // Simple command parsing - in production, use NLP services
    final lowerCommand = command.toLowerCase();

    if (lowerCommand.contains('أضف') || lowerCommand.contains('اضافة')) {
      return _parseAddMealCommand(command);
    } else if (lowerCommand.contains('احذف') || lowerCommand.contains('حذف')) {
      return _parseDeleteMealCommand(command);
    } else if (lowerCommand.contains('عرض') || lowerCommand.contains('اظهر')) {
      return _parseShowCommand(command);
    } else if (lowerCommand.contains('قائمة المشتريات')) {
      return {'action': 'show_shopping_list'};
    } else if (lowerCommand.contains('توصيات') ||
        lowerCommand.contains('اقتراحات')) {
      return _parseRecommendationCommand(command);
    }

    return null;
  }

  Map<String, dynamic> _parseAddMealCommand(String command) {
    // Extract meal name, day, and meal type from command
    final days = [
      'السبت',
      'الأحد',
      'الإثنين',
      'الثلاثاء',
      'الأربعاء',
      'الخميس',
      'الجمعة'
    ];
    final mealTypes = ['الفطور', 'الغداء', 'العشاء'];

    String? day, mealType, mealName;

    for (final d in days) {
      if (command.contains(d)) {
        day = d;
        break;
      }
    }

    for (final mt in mealTypes) {
      if (command.contains(mt)) {
        mealType = mt;
        break;
      }
    }

    // Extract meal name (simplified)
    final words = command.split(' ');
    final mealWords = <String>[];
    bool foundAdd = false;

    for (final word in words) {
      if (word.contains('أضف') || word.contains('اضافة')) {
        foundAdd = true;
        continue;
      }
      if (foundAdd &&
          !days.contains(word) &&
          !mealTypes.contains(word) &&
          !['يوم', 'وجبة', 'لـ', 'في'].contains(word)) {
        mealWords.add(word);
      }
    }

    mealName = mealWords.join(' ');

    return {
      'action': 'add_meal',
      'meal_name': mealName,
      'day': day,
      'meal_type': mealType,
    };
  }

  Map<String, dynamic> _parseDeleteMealCommand(String command) {
    // Similar parsing logic for delete commands
    return {
      'action': 'delete_meal',
      'day': null, // Extract from command
      'meal_type': null, // Extract from command
    };
  }

  Map<String, dynamic> _parseShowCommand(String command) {
    if (command.contains('خطة') || command.contains('جدول')) {
      return {'action': 'show_plan'};
    } else if (command.contains('مكونات')) {
      return {'action': 'show_ingredients'};
    } else if (command.contains('إحصائيات')) {
      return {'action': 'show_statistics'};
    }

    return {'action': 'show_plan'};
  }

  Map<String, dynamic> _parseRecommendationCommand(String command) {
    final mealTypes = ['الفطور', 'الغداء', 'العشاء'];
    String? mealType;

    for (final mt in mealTypes) {
      if (command.contains(mt)) {
        mealType = mt;
        break;
      }
    }

    return {
      'action': 'get_recommendations',
      'meal_type': mealType ?? 'الغداء',
    };
  }
}

// Smart Home Integration
class SmartHomeIntegration {
  Future<void> addMealToSmartDisplay(
      String mealName, DateTime scheduledTime) async {
    try {
      // Integration with smart displays (Google Nest Hub, Amazon Echo Show, etc.)
      print(
          'Adding meal reminder to smart display: $mealName at $scheduledTime');

      // In a real implementation, you would use platform-specific APIs
      // or IFTTT/Zapier integrations
    } catch (e) {
      print('Error adding meal to smart display: $e');
    }
  }

  Future<void> setKitchenTimer(int minutes, String mealName) async {
    try {
      print('Setting kitchen timer for $minutes minutes for $mealName');

      // Integration with smart kitchen devices
    } catch (e) {
      print('Error setting kitchen timer: $e');
    }
  }

  Future<void> adjustSmartOvenSettings(
      String mealName, Map<String, dynamic> cookingSettings) async {
    try {
      print('Adjusting smart oven settings for $mealName: $cookingSettings');

      // Integration with smart ovens and cooking appliances
    } catch (e) {
      print('Error adjusting smart oven settings: $e');
    }
  }

  Future<void> updateSmartFridge(List<String> ingredients) async {
    try {
      print('Updating smart fridge with ingredients: $ingredients');

      // Integration with smart refrigerators to track ingredients
    } catch (e) {
      print('Error updating smart fridge: $e');
    }
  }
}

// Augmented Reality Meal Visualization
class ARMealVisualizer {
  Future<void> showMealInAR(String mealName, List<String> ingredients) async {
    try {
      print('Showing $mealName in AR with ingredients: $ingredients');

      // In a real implementation, you would use AR frameworks like:
      // - ARCore (Android)
      // - ARKit (iOS)
      // - Flutter AR plugins

      // This could show:
      // - 3D visualization of the meal
      // - Step-by-step cooking instructions overlaid on real kitchen
      // - Ingredient identification and measurement guides
    } catch (e) {
      print('Error showing meal in AR: $e');
    }
  }

  Future<void> scanIngredientWithAR() async {
    try {
      print('Scanning ingredient with AR camera...');

      // Use camera to identify ingredients and add to shopping list
      // Could integrate with Google Lens or similar services
    } catch (e) {
      print('Error scanning ingredient with AR: $e');
    }
  }
}

// Social Features and Community
class SocialFeatures {
  Future<void> shareMealPlan(
      String memberId, Map<String, dynamic> mealPlan) async {
    try {
      // Share meal plan with family members or community
      print('Sharing meal plan for member $memberId');

      // Could integrate with:
      // - Social media platforms
      // - Family sharing groups
      // - Community recipe exchanges
    } catch (e) {
      print('Error sharing meal plan: $e');
    }
  }

  Future<List<Map<String, dynamic>>> getCommunityRecommendations(
      String mealType) async {
    try {
      // Get meal recommendations from community
      print('Getting community recommendations for $mealType');

      return [
        {
          'meal_name': 'كبسة الدجاج المجتمعية',
          'rating': 4.8,
          'reviews': 156,
          'shared_by': 'أم أحمد',
          'difficulty': 3,
          'prep_time': 45,
        },
        {
          'meal_name': 'مندي اللحم التقليدي',
          'rating': 4.9,
          'reviews': 203,
          'shared_by': 'الشيف محمد',
          'difficulty': 4,
          'prep_time': 90,
        },
      ];
    } catch (e) {
      print('Error getting community recommendations: $e');
      return [];
    }
  }

  Future<void> rateMeal(String mealName, double rating, String review) async {
    try {
      print('Rating meal $mealName: $rating stars - $review');

      // Submit rating and review to community database
    } catch (e) {
      print('Error rating meal: $e');
    }
  }

  Future<void> followUser(String userId) async {
    try {
      print('Following user: $userId');

      // Follow another user to see their meal plans and recipes
    } catch (e) {
      print('Error following user: $e');
    }
  }
}

// Gamification System
class GamificationSystem {
  static const Map<String, int> _achievementPoints = {
    'first_meal_planned': 10,
    'week_completed': 50,
    'month_completed': 200,
    'variety_master': 100, // 20+ different meals in a month
    'healthy_eater': 150, // Meeting nutrition goals for a week
    'recipe_creator': 75, // Creating a custom recipe
    'community_contributor': 25, // Sharing a recipe
    'streak_7_days': 100,
    'streak_30_days': 500,
    'budget_saver': 80, // Staying under budget for a month
    'early_bird': 30, // Planning meals a week in advance
  };

  Future<Map<String, dynamic>> getUserProgress(String userId) async {
    try {
      // Get user's gamification progress
      return {
        'total_points': 1250,
        'level': 5,
        'current_streak': 12,
        'achievements': [
          'first_meal_planned',
          'week_completed',
          'recipe_creator',
          'streak_7_days',
        ],
        'next_achievement': {
          'name': 'variety_master',
          'description': 'خطط لـ 20 وجبة مختلفة في شهر واحد',
          'progress': 15,
          'target': 20,
          'points': 100,
        },
        'leaderboard_position': 23,
      };
    } catch (e) {
      print('Error getting user progress: $e');
      return {};
    }
  }

  Future<void> checkAndAwardAchievements(
      String userId, String action, Map<String, dynamic> context) async {
    try {
      print('Checking achievements for user $userId, action: $action');

      // Check if user earned any new achievements
      final newAchievements = <String>[];

      switch (action) {
        case 'meal_planned':
          if (context['is_first_meal'] == true) {
            newAchievements.add('first_meal_planned');
          }
          break;
        case 'week_completed':
          newAchievements.add('week_completed');
          if (context['streak_days'] == 7) {
            newAchievements.add('streak_7_days');
          } else if (context['streak_days'] == 30) {
            newAchievements.add('streak_30_days');
          }
          break;
        case 'custom_recipe_created':
          newAchievements.add('recipe_creator');
          break;
        case 'recipe_shared':
          newAchievements.add('community_contributor');
          break;
      }

      for (final achievement in newAchievements) {
        await _awardAchievement(userId, achievement);
      }
    } catch (e) {
      print('Error checking achievements: $e');
    }
  }

  Future<void> _awardAchievement(String userId, String achievementId) async {
    final points = _achievementPoints[achievementId] ?? 0;
    print(
        '🏆 Achievement unlocked for $userId: $achievementId (+$points points)');

    // Show achievement notification
    // Update user's total points
    // Trigger celebration animation
  }

  Future<List<Map<String, dynamic>>> getLeaderboard() async {
    try {
      return [
        {'rank': 1, 'name': 'أم فاطمة', 'points': 2850, 'level': 8},
        {'rank': 2, 'name': 'الشيف أحمد', 'points': 2650, 'level': 7},
        {'rank': 3, 'name': 'سارة المطبخ', 'points': 2400, 'level': 7},
        {'rank': 4, 'name': 'محمد الطباخ', 'points': 2200, 'level': 6},
        {'rank': 5, 'name': 'نورا الذواقة', 'points': 2100, 'level': 6},
      ];
    } catch (e) {
      print('Error getting leaderboard: $e');
      return [];
    }
  }
}

// Advanced Analytics and Insights
class AdvancedAnalytics {
  Future<Map<String, dynamic>> generateInsights(
      String userId, Map<String, dynamic> userData) async {
    try {
      final insights = <String, dynamic>{};

      // Meal pattern analysis
      insights['meal_patterns'] = await _analyzeMealPatterns(userData);

      // Nutrition trends
      insights['nutrition_trends'] = await _analyzeNutritionTrends(userData);

      // Cost analysis
      insights['cost_analysis'] = await _analyzeCosts(userData);

      // Seasonal preferences
      insights['seasonal_preferences'] =
      await _analyzeSeasonalPreferences(userData);

      // Predictive recommendations
      insights['predictions'] = await _generatePredictions(userData);

      return insights;
    } catch (e) {
      print('Error generating insights: $e');
      return {};
    }
  }

  Future<Map<String, dynamic>> _analyzeMealPatterns(
      Map<String, dynamic> userData) async {
    return {
      'most_active_meal_time': 'الغداء',
      'preferred_cooking_days': ['الجمعة', 'السبت'],
      'meal_complexity_trend': 'متزايد',
      'favorite_cuisines': ['مصري', 'شامي', 'خليجي'],
      'cooking_frequency': {
        'daily': 0.7,
        'weekly': 0.9,
        'monthly': 1.0,
      },
    };
  }

  Future<Map<String, dynamic>> _analyzeNutritionTrends(
      Map<String, dynamic> userData) async {
    return {
      'calorie_trend': 'مستقر',
      'protein_adequacy': 0.85, // 85% of recommended
      'carb_balance': 'متوازن',
      'fat_intake': 'ضمن الحدود الطبيعية',
      'vitamin_deficiencies': ['فيتامين د', 'فيتامين ب12'],
      'improvement_areas': [
        'زيادة تناول الخضروات الورقية',
        'تقليل السكريات المضافة',
        'زيادة مصادر الأوميجا 3',
      ],
    };
  }

  Future<Map<String, dynamic>> _analyzeCosts(
      Map<String, dynamic> userData) async {
    return {
      'monthly_average': 1200.0, // EGP
      'cost_per_meal': 15.5,
      'budget_efficiency': 0.92, // 92% efficient
      'expensive_ingredients': ['لحم البقر', 'الجمبري', 'المكسرات'],
      'cost_saving_suggestions': [
        'استبدال اللحم الأحمر بالدجاج مرتين أسبوعياً',
        'شراء الخضروات الموسمية',
        'الطبخ بكميات أكبر وحفظ الباقي',
      ],
      'seasonal_cost_variations': {
        'spring': 0.95,
        'summer': 1.1,
        'autumn': 0.9,
        'winter': 1.05,
      },
    };
  }

  Future<Map<String, dynamic>> _analyzeSeasonalPreferences(
      Map<String, dynamic> userData) async {
    return {
      'spring': ['سلطات', 'شوربات خفيفة', 'أسماك مشوية'],
      'summer': ['عصائر طبيعية', 'فواكه', 'وجبات باردة'],
      'autumn': ['شوربات دافئة', 'خضروات محشية', 'أطباق بالفرن'],
      'winter': ['وجبات دسمة', 'شوربات ثقيلة', 'حلويات دافئة'],
      'current_season_match': 0.78, // 78% match with current preferences
    };
  }

  Future<Map<String, dynamic>> _generatePredictions(
      Map<String, dynamic> userData) async {
    return {
      'next_week_preferences': [
        'كبسة الدجاج',
        'ملوخية',
        'مكرونة بالبشاميل',
      ],
      'shopping_list_prediction': [
        'دجاج - 2 كيلو',
        'أرز - 1 كيلو',
        'خضروات مشكلة - 500 جرام',
      ],
      'budget_forecast': {
        'next_week': 280.0,
        'next_month': 1150.0,
      },
      'health_goals_progress': {
        'weight_loss': 'على المسار الصحيح',
        'muscle_gain': 'يحتاج زيادة البروتين',
        'general_health': 'ممتاز',
      },
    };
  }
}

// Meal Planning Automation
class MealPlanningAutomation {
  final SmartMealPlanningAssistant _assistant;
  final GamificationSystem _gamification;
  final NotificationService _notifications;

  MealPlanningAutomation(
      this._assistant, this._gamification, this._notifications);

  Future<void> autoGenerateWeeklyPlan(String userId) async {
    try {
      print('Auto-generating weekly plan for user: $userId');

      // Generate AI-powered weekly meal plan
      final weeklyPlan = await _assistant.generateWeeklyPlan(userId);

      // Apply user preferences and constraints
      final optimizedPlan = await _optimizePlan(weeklyPlan, userId);

      // Generate shopping list
      final shoppingList = await _generateShoppingList(optimizedPlan);

      // Schedule notifications
      await _scheduleWeeklyNotifications(optimizedPlan);

      // Check for achievements
      await _gamification
          .checkAndAwardAchievements(userId, 'auto_plan_generated', {
        'meals_count': _countMealsInPlan(optimizedPlan),
        'variety_score': _calculateVarietyScore(optimizedPlan),
      });

      print('Weekly plan generated successfully');
    } catch (e) {
      print('Error auto-generating weekly plan: $e');
    }
  }

  Future<Map<String, List<String>>> _optimizePlan(
      Map<String, List<String>> plan, String userId) async {
    // Apply optimization based on:
    // - Budget constraints
    // - Nutritional goals
    // - Cooking time availability
    // - Ingredient availability
    // - Seasonal preferences

    return plan; // Simplified for now
  }

  Future<List<Map<String, dynamic>>> _generateShoppingList(
      Map<String, List<String>> plan) async {
    final shoppingList = <Map<String, dynamic>>[];

    // Extract ingredients from all meals in the plan
    // Group by category
    // Optimize quantities
    // Add estimated prices

    return shoppingList;
  }

  Future<void> _scheduleWeeklyNotifications(
      Map<String, List<String>> plan) async {
    plan.forEach((day, meals) async {
      for (int i = 0; i < meals.length; i++) {
        final meal = meals[i];
        final mealTime = _getMealTime(i); // Get appropriate time for meal type
        final scheduledTime =
        _getDateTimeForDay(day).add(Duration(hours: mealTime));

        await _notifications.scheduleMealReminder(meal, scheduledTime);
      }
    });
  }

  int _getMealTime(int mealIndex) {
    switch (mealIndex) {
      case 0:
        return 7; // Breakfast at 7 AM
      case 1:
        return 13; // Lunch at 1 PM
      case 2:
        return 19; // Dinner at 7 PM
      default:
        return 12;
    }
  }

  DateTime _getDateTimeForDay(String day) {
    final now = DateTime.now();
    final daysOfWeek = [
      'السبت',
      'الأحد',
      'الإثنين',
      'الثلاثاء',
      'الأربعاء',
      'الخميس',
      'الجمعة'
    ];
    final dayIndex = daysOfWeek.indexOf(day);
    final currentDayIndex = now.weekday % 7;

    int daysToAdd = dayIndex - currentDayIndex;
    if (daysToAdd <= 0) daysToAdd += 7;

    return now.add(Duration(days: daysToAdd));
  }

  int _countMealsInPlan(Map<String, List<String>> plan) {
    return plan.values.fold(0, (sum, meals) => sum + meals.length);
  }

  double _calculateVarietyScore(Map<String, List<String>> plan) {
    final allMeals = <String>[];
    plan.values.forEach((meals) => allMeals.addAll(meals));
    final uniqueMeals = allMeals.toSet();

    return uniqueMeals.length / allMeals.length; // Variety ratio
  }
}

// Health Integration
class HealthIntegration {
  Future<void> syncWithHealthApp(String userId) async {
    try {
      print('Syncing with health app for user: $userId');

      // In a real implementation, integrate with:
      // - Apple Health (iOS)
      // - Google Fit (Android)
      // - Samsung Health
      // - Fitbit
      // - Other fitness trackers

      // Sync data like:
      // - Daily calorie burn
      // - Exercise activities
      // - Sleep patterns
      // - Weight changes
      // - Heart rate data
    } catch (e) {
      print('Error syncing with health app: $e');
    }
  }

  Future<Map<String, dynamic>> getHealthMetrics(String userId) async {
    try {
      return {
        'daily_steps': 8500,
        'calories_burned': 2200,
        'active_minutes': 45,
        'sleep_hours': 7.5,
        'heart_rate_avg': 72,
        'weight_trend': 'stable',
        'hydration_level': 0.8, // 80% of daily goal
      };
    } catch (e) {
      print('Error getting health metrics: $e');
      return {};
    }
  }

  Future<List<String>> getHealthBasedRecommendations(String userId) async {
    try {
      final healthMetrics = await getHealthMetrics(userId);
      final recommendations = <String>[];

      if (healthMetrics['daily_steps'] < 8000) {
        recommendations.add('زد من نشاطك البدني - اهدف لـ 10,000 خطوة يومياً');
      }

      if (healthMetrics['hydration_level'] < 0.7) {
        recommendations.add('اشرب المزيد من الماء - على الأقل 8 أكواب يومياً');
      }

      if (healthMetrics['sleep_hours'] < 7) {
        recommendations.add('احرص على النوم 7-9 ساعات يومياً لصحة أفضل');
      }

      return recommendations;
    } catch (e) {
      print('Error getting health-based recommendations: $e');
      return [];
    }
  }
}

// Accessibility Features
class AccessibilityFeatures {
  static bool _isHighContrastEnabled = false;
  static bool _isLargeFontEnabled = false;
  static bool _isVoiceOverEnabled = false;
  static bool _isColorBlindFriendlyEnabled = false;

  static void enableHighContrast() {
    _isHighContrastEnabled = true;
    print('High contrast mode enabled');
  }

  static void enableLargeFont() {
    _isLargeFontEnabled = true;
    print('Large font mode enabled');
  }

  static void enableVoiceOver() {
    _isVoiceOverEnabled = true;
    print('Voice over mode enabled');
  }

  static void enableColorBlindFriendly() {
    _isColorBlindFriendlyEnabled = true;
    print('Color blind friendly mode enabled');
  }

  static TextStyle getAccessibleTextStyle(TextStyle baseStyle) {
    if (_isLargeFontEnabled) {
      return baseStyle.copyWith(fontSize: (baseStyle.fontSize ?? 14) * 1.3);
    }
    return baseStyle;
  }

  static Color getAccessibleColor(Color baseColor) {
    if (_isHighContrastEnabled) {
      // Return high contrast version of the color
      final luminance = baseColor.computeLuminance();
      return luminance > 0.5 ? Colors.black : Colors.white;
    }

    if (_isColorBlindFriendlyEnabled) {
      // Return color blind friendly version
      // This is a simplified implementation
      return baseColor;
    }

    return baseColor;
  }

  static String getVoiceOverText(String text, String context) {
    if (_isVoiceOverEnabled) {
      return '$context: $text';
    }
    return text;
  }
}

// Offline Mode Support
class OfflineSupport {
  static bool _isOfflineMode = false;
  static final Map<String, dynamic> _offlineCache = {};

  static bool get isOffline => _isOfflineMode;

  static Future<void> checkConnectivity() async {
    try {
      final connectivityResult = await Connectivity().checkConnectivity();
      _isOfflineMode = connectivityResult == ConnectivityResult.none;

      if (_isOfflineMode) {
        print('Device is offline - switching to offline mode');
      } else {
        print('Device is online - syncing cached data');
        await _syncCachedData();
      }
    } catch (e) {
      print('Error checking connectivity: $e');
      _isOfflineMode = true; // Assume offline on error
    }
  }

  static Future<void> cacheData(String key, dynamic data) async {
    _offlineCache[key] = {
      'data': data,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };

    // Save to local storage
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(
        'offline_cache_$key', json.encode(_offlineCache[key]));
  }

  static Future<T?> getCachedData<T>(String key) async {
    if (_offlineCache.containsKey(key)) {
      return _offlineCache[key]['data'] as T?;
    }

    // Try to load from local storage
    final prefs = await SharedPreferences.getInstance();
    final cachedString = prefs.getString('offline_cache_$key');

    if (cachedString != null) {
      final cachedData = json.decode(cachedString);
      _offlineCache[key] = cachedData;
      return cachedData['data'] as T?;
    }

    return null;
  }

  static Future<void> _syncCachedData() async {
    // Sync any cached changes when coming back online
    print('Syncing cached data...');

    // Implementation would sync:
    // - Meal plan changes
    // - Shopping list updates
    // - Custom recipes
    // - User preferences
  }

  static List<String> getOfflineFeatures() {
    return [
      'عرض خطط الوجبات المحفوظة',
      'إضافة وتعديل الوجبات',
      'إدارة قائمة المشتريات',
      'عرض الوصفات المحفوظة',
      'حساب المكونات والتغذية',
      'عرض الإحصائيات المحلية',
    ];
  }
}

// Multi-language Support
class LocalizationService {
  static const Map<String, Map<String, String>> _translations = {
    'ar': {
      'app_title': 'مخطط الوجبات الذكي',
      'breakfast': 'الفطور',
      'lunch': 'الغداء',
      'dinner': 'العشاء',
      'shopping_list': 'قائمة المشتريات',
      'add_meal': 'إضافة وجبة',
      'delete_meal': 'حذف وجبة',
      'meal_plan': 'خطة الوجبات',
      'ingredients': 'المكونات',
      'nutrition': 'التغذية',
      'calories': 'السعرات الحرارية',
      'protein': 'البروتين',
      'carbs': 'الكربوهيدرات',
      'fat': 'الدهون',
    },
    'en': {
      'app_title': 'Smart Meal Planner',
      'breakfast': 'Breakfast',
      'lunch': 'Lunch',
      'dinner': 'Dinner',
      'shopping_list': 'Shopping List',
      'add_meal': 'Add Meal',
      'delete_meal': 'Delete Meal',
      'meal_plan': 'Meal Plan',
      'ingredients': 'Ingredients',
      'nutrition': 'Nutrition',
      'calories': 'Calories',
      'protein': 'Protein',
      'carbs': 'Carbohydrates',
      'fat': 'Fat',
    },
    'fr': {
      'app_title': 'Planificateur de Repas Intelligent',
      'breakfast': 'Petit-déjeuner',
      'lunch': 'Déjeuner',
      'dinner': 'Dîner',
      'shopping_list': 'Liste de Courses',
      'add_meal': 'Ajouter un Repas',
      'delete_meal': 'Supprimer le Repas',
      'meal_plan': 'Plan de Repas',
      'ingredients': 'Ingrédients',
      'nutrition': 'Nutrition',
      'calories': 'Calories',
      'protein': 'Protéine',
      'carbs': 'Glucides',
      'fat': 'Graisse',
    },
  };

  static String _currentLanguage = 'ar';

  static String get currentLanguage => _currentLanguage;

  static void setLanguage(String languageCode) {
    if (_translations.containsKey(languageCode)) {
      _currentLanguage = languageCode;
      print('Language changed to: $languageCode');
    }
  }

  static String translate(String key) {
    return _translations[_currentLanguage]?[key] ?? key;
  }

  static List<String> getSupportedLanguages() {
    return _translations.keys.toList();
  }

  static Map<String, String> getLanguageNames() {
    return {
      'ar': 'العربية',
      'en': 'English',
      'fr': 'Français',
    };
  }
}
