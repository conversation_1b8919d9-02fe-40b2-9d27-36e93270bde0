import 'package:flutter/material.dart';
import 'package:untitled10/appstate.dart';

/// مساعد للتحقق من أدوار المستخدمين والصلاحيات
class RoleChecker {
  /// التحقق مما إذا كان المستخدم يملك دور "hokama"
  static bool isHokama(AppState appState) {
    // التحقق من الدور باستخدام كل من role.name و userType
    final roleName = appState.currentUser?.role.name.toLowerCase() ?? 'guest';
    final userType = (appState.userType ?? 'guest').toLowerCase();
    
    // طباعة قيم الدور للتصحيح (يمكن إزالتها في الإنتاج)
    debugPrint('Current role name: $roleName');
    debugPrint('Current userType: $userType');
    
    return roleName == 'hokama' || userType == 'hokama' || 
           roleName == 'admin' || userType == 'admin';
  }
  
  /// عرض رسالة عدم وجود صلاحية
  static void showUnauthorizedDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('غير مصرح'),
        content: const Text(
          'ليس لديك صلاحية للوصول إلى هذه الميزة. فقط المستخدمون بدور "hokama" يمكنهم الوصول.'
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('حسناً'),
          ),
        ],
      ),
    );
  }
  
  /// التحقق من الصلاحية وتنفيذ إجراء أو عرض رسالة عدم الصلاحية
  static void checkPermissionAndExecute(
    BuildContext context, 
    AppState appState, 
    VoidCallback onAuthorized
  ) {
    if (isHokama(appState)) {
      onAuthorized();
    } else {
      showUnauthorizedDialog(context);
    }
  }
}
