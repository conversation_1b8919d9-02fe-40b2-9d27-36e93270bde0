import 'package:uuid/uuid.dart';

class MedicalRecord {
  final String id;
  final String filePath;
  final String fileType;
  final String date;
  final String addedBy;
  final String? description;
  final String? category;

  MedicalRecord({
    required this.id,
    required this.filePath,
    required this.fileType,
    required this.date,
    required this.addedBy,
    this.description,
    this.category,
  });

  factory MedicalRecord.fromJson(Map<String, dynamic> json) {
    return MedicalRecord(
      id: json['_id'] ?? const Uuid().v4(),
      filePath: json['fileUrl'] ?? '',
      fileType: json['fileType'] ?? '',
      date: json['createdAt'] ?? DateTime.now().toString(),
      addedBy: json['addedBy'] ?? 'غير معروف',
      description: json['description'],
      category: json['category'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'filePath': filePath,
      'fileType': fileType,
      'date': date,
      'addedBy': addedBy,
      'description': description,
      'category': category,
    };
  }

  MedicalRecord copyWith({
    String? id,
    String? filePath,
    String? fileType,
    String? date,
    String? addedBy,
    String? description,
    String? category,
  }) {
    return MedicalRecord(
      id: id ?? this.id,
      filePath: filePath ?? this.filePath,
      fileType: fileType ?? this.fileType,
      date: date ?? this.date,
      addedBy: addedBy ?? this.addedBy,
      description: description ?? this.description,
      category: category ?? this.category,
    );
  }
}
