import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:audioplayers/audioplayers.dart'; // إضافة مكتبة audioplayers

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/foundation.dart' show kDebugMode, kIsWeb;
import 'package:flutter/material.dart';
import 'package:latlong2/latlong.dart';
import 'package:http/http.dart' as http;
import 'ride_request.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'models/user_model.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:socket_io_client/socket_io_client.dart' as io;
import 'package:uuid/uuid.dart';
import 'package:timezone/data/latest.dart' as tz;
import 'package:timezone/timezone.dart' as tz;

import 'models/comment_model.dart';
import 'models/group_task_model.dart';
import 'models/medical_record_model.dart';
import 'models/notification_model.dart';
import 'models/post_model.dart';
import 'models/simple_task_model.dart';
import 'models/story_model.dart';
import 'models/task_group_model.dart';
import 'models/video_model.dart';
import 'package:untitled10/elsaf7aelsha3sya.dart' hide Post;
import 'rewards_manager.dart';

// Global keys for navigation and SnackBars
const String defaultAuthToken = '**************:3000';
final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();
final GlobalKey<ScaffoldMessengerState> scaffoldMessengerKey =
    GlobalKey<ScaffoldMessengerState>();

class Driver {
  final String id;
  String name;
  String vehicleInfo;
  bool isAvailable;

  Driver({
    required this.id,
    required this.name,
    required this.vehicleInfo,
    this.isAvailable = true,
  });
}

const String kBaseUrl = 'http://localhost:3000/api';

class AppState extends ChangeNotifier {
  // Translation support
  Map<String, Map<String, String>> translations = {};
  String currentLanguage = 'en'; // Default language

  // Initialize translations - call this method during app startup
  Future<void> initializeTranslations() async {
    // Load translations from assets or API
    // Example:
    // translations = await loadTranslations();
    notifyListeners();
  }

  static String getBackendUrl() => kBaseUrl;
  final String _backendBaseUrl = kBaseUrl; // Added for API calls
  ProfileProvider? _profileProvider; // Reference to ProfileProvider
  String _userRole = 'hokama'; // Set initial role

  String get userRole => _userRole;

  void setUserRole(String role) {
    if (_userRole != role) {
      _userRole = role;
      notifyListeners();
    }
  }

  Future<void> addVideoContent({
    required String title,
    required String description,
    required String videoUrl,
    required String category,
  }) async {
    try {
      // Check if user has the required role
      if (_currentUser == null || userRole != 'hokama') {
        throw Exception('غير مصرح لك بإضافة فيديوهات');
      }

      final response = await http.post(
        Uri.parse('$_backendBaseUrl/videos'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $_token',
        },
        body: jsonEncode({
          'title': title,
          'description': description,
          'videoUrl': videoUrl,
          'category': category,
          'uploadedBy': _currentUser?.id,
          'uploadDate': DateTime.now().toIso8601String(),
        }),
      );

      if (response.statusCode == 201) {
        final newVideo = Video.fromJson(jsonDecode(response.body));
        videos.add(newVideo);
        notifyListeners();
      } else {
        throw Exception('Failed to add video: ${response.body}');
      }
    } catch (e) {
      throw Exception('Error adding video: $e');
    }
  }

  List<UserModel> friends = []; // List of user's friends

  // Method to update ProfileProvider, can be called after AppState is created
  void setProfileProvider(ProfileProvider provider) {
    _profileProvider = provider;
    // Optionally, if profile is already loaded in ProfileProvider, sync stars here
    if (_profileProvider != null &&
        (_profileProvider?.username.isNotEmpty ?? false) &&
        _currentUser != null) {
      // This check implies profile might have been loaded before AppState got provider
      // Or if ProfileProvider loads profile independently and AppState needs to sync later
      _stars = _profileProvider!
          .stars; // Sync stars from ProfileProvider if it's already loaded
      // تأخير notifyListeners لتجنب setState during build
      Future.microtask(() => notifyListeners());
    }
  }

  static Future<String> getAuthToken() async {
    // Simulate fetching dynamic token (replace with real auth system)
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('auth_token') ??
        'dynamic_token_${DateTime.now().millisecondsSinceEpoch}';
  }

  // خاص بـ id24
  String childAgeGroup = '3-5'; // خاص بـ id24
  // خاص بـ id24
  void setChildAgeGroup(String value) {
    childAgeGroup = value;
    notifyListeners();
  }

  // خاص بـ id24
  List<Map<String, String>> schedule = [];
  // خاص بـ id24
  void addSchedule(String task, String time) {
    schedule.add({'task': task, 'time': time});
    notifyListeners();
  }

  // خاص بـ id24
  void removeSchedule(int index) {
    schedule.removeAt(index);
    notifyListeners();
  }

  // خاص بـ id24
  List<Map<String, String>> rewards = [];
  // خاص بـ id24
  void addReward() {
    rewards.add({'reward': 'مكافأة جديدة'});
    notifyListeners();
  }

  // خاص بـ id24
  List<Map<String, String>> progress = [];
  // خاص بـ id24
  void loadProgress() {
    // مثال: تحميل إنجاز افتراضي
    progress = [
      {
        'milestone': 'مثال إنجاز',
        'date': DateTime.now().toString().substring(0, 10)
      }
    ];
    notifyListeners();
  }

  // خاص بـ id24
  void addProgress(String milestone, String date) {
    progress.add({'milestone': milestone, 'date': date});
    notifyListeners();
  }

  // خاص بـ id24
  List<Map<String, String>> communityMessages = [];
  // خاص بـ id24
  void addCommunityMessage(String message, String user) {
    communityMessages.add({'message': message, 'user': user});
    notifyListeners();
  }

  // Ride request state
  LatLng? _currentLocation;
  final List<RideRequest> _rideRequests = [];
  bool _isEligible = false;
  String? _rideUserId;
  String? _driverId;
  final List<Driver> _drivers = [];
  Driver? _currentDriver;

  LatLng? get currentLocation => _currentLocation;
  List<RideRequest> get rideRequests => List.unmodifiable(_rideRequests);
  bool get isEligible => _isEligible;
  String? get rideUserId => _rideUserId;
  String? get driverId => _driverId;
  List<Driver> get drivers => List.unmodifiable(_drivers);
  Driver? get currentDriver => _currentDriver;

  void setCurrentLocation(LatLng location) {
    _currentLocation = location;
    notifyListeners();
  }

  Future<void> addRideRequest(
      {required LatLng pickup,
      required String destination,
      LatLng? destinationLocation,
      required String requestType}) async {
    final response = await http.post(
      Uri.parse('$kBaseUrl/rides'),
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({
        'pickup': {'lat': pickup.latitude, 'lng': pickup.longitude},
        'destination': destination,
        'destinationLocation': destinationLocation != null
            ? {
                'lat': destinationLocation.latitude,
                'lng': destinationLocation.longitude
              }
            : null,
        'timestamp': DateTime.now().toIso8601String(),
      }),
    );
    if (response.statusCode == 201) {
      final data = jsonDecode(response.body);
      _rideRequests.add(RideRequest(
        id: data['_id'],
        pickup: LatLng(data['pickup']['lat'], data['pickup']['lng']),
        destination: data['destination'],
        destinationLocation: data['destinationLocation'] != null
            ? LatLng(data['destinationLocation']['lat'],
                data['destinationLocation']['lng'])
            : null,
        timestamp: DateTime.parse(data['timestamp']),
        status: data['status'],
        driverId: data['driverId'],
        requestType: requestType,
      ));
      notifyListeners();
    }
  }

  Future<void> cancelRideRequest(String requestId) async {
    final response = await http.put(
      Uri.parse('$kBaseUrl/rides/$requestId/status'),
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({'status': 'Cancelled'}),
    );
    if (response.statusCode == 200) {
      final request = _rideRequests.firstWhere((r) => r.id == requestId,
          orElse: () => throw Exception('RideRequest not found'));
      request.status = 'Cancelled';
      notifyListeners();
    }
  }

  Future<void> acceptRideRequest(String requestId, String driverId) async {
    final response = await http.put(
      Uri.parse('$kBaseUrl/rides/$requestId/status'),
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({'status': 'Accepted', 'driverId': driverId}),
    );
    if (response.statusCode == 200) {
      final request = _rideRequests.firstWhere((r) => r.id == requestId,
          orElse: () => throw Exception('RideRequest not found'));
      request.status = 'Accepted';
      request.driverId = driverId;
      notifyListeners();
    }
  }

  Future<void> updateRideStatus(String requestId, String status) async {
    final response = await http.put(
      Uri.parse('$kBaseUrl/rides/$requestId/status'),
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({'status': status}),
    );
    if (response.statusCode == 200) {
      final request = _rideRequests.firstWhere((r) => r.id == requestId,
          orElse: () => throw Exception('RideRequest not found'));
      request.status = status;
      notifyListeners();
    }
  }

  Future<void> loginAsDriver(String name, String vehicleInfo) async {
    final response = await http.post(
      Uri.parse('$kBaseUrl/drivers/login'),
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({'name': name, 'vehicleInfo': vehicleInfo}),
    );
    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      _driverId = data['_id'];
      _currentDriver = Driver(
          id: data['_id'],
          name: data['name'],
          vehicleInfo: data['vehicleInfo'],
          isAvailable: data['isAvailable'] ?? true);
      _drivers.add(_currentDriver!);
      notifyListeners();
    }
  }

  Future<void> updateDriverAvailability(bool isAvailable) async {
    if (_currentDriver != null) {
      final response = await http.put(
        Uri.parse('$kBaseUrl/drivers/${_currentDriver!.id}/availability'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({'isAvailable': isAvailable}),
      );
      if (response.statusCode == 200) {
        _currentDriver!.isAvailable = isAvailable;
        notifyListeners();
      }
    }
  }

  void verifyEligibility(String userId) {
    // محاكاة التحقق (يمكن استبدالها بـ API حقيقي)
    _rideUserId = userId;
    _isEligible = userId.length == 10; // مثال: رقم الهوية 10 أرقام
    notifyListeners();
  }

  // إعدادات الإشعارات
  String _notificationType = 'both'; // text, sound, both
  List<String> _selectedCategories = ['christian', 'islamic', 'wisdom'];
  bool _showInAppNotifications = true; // إشعارات داخل التطبيق
  bool _showSystemNotifications = true; // إشعارات النظام
  final AudioPlayer _audioPlayer = AudioPlayer(); // لتشغيل الصوت

  // Notification list
  final List<NotificationModel> _notifications = [];

  // Getters لإعدادات الإشعارات
  String get notificationType => _notificationType;
  List<String> get selectedCategories => _selectedCategories;
  bool get showInAppNotifications => _showInAppNotifications;
  bool get showSystemNotifications => _showSystemNotifications;
  List<NotificationModel> get notifications => _notifications;

  // إعدادات الإشعارات داخل التطبيق
  void setInAppNotifications(bool value) {
    _showInAppNotifications = value;
    notifyListeners();
  }

  // إعدادات إشعارات النظام
  void setSystemNotifications(bool value) {
    _showSystemNotifications = value;
    notifyListeners();
  }

  // Add a notification
  void addNotification(NotificationModel notification) {
    _notifications.add(notification);
    notifyListeners();
  }
  // Socket and state variables

  final GlobalKey<ScaffoldMessengerState> scaffoldMessengerKey =
      GlobalKey<ScaffoldMessengerState>();

  io.Socket? socket;
  bool _isDarkMode = false;
  bool _isLoggedIn = false;
  String? _token;
  String? _userId;
  String? _userType;
  String _username = "مستخدم";
  String _selectedTheme = 'Default';
  String? _profileImagePath;
  String? _currentPage;
  bool _isLoadingMore = false;
  UserModel? _currentUser;
  int _postPage = 1;
  int _videoPage = 1;
  bool _hasMoreVideos = true;
  bool _shareLocation = false; // Added shareLocation

  // Poll Comments
  List<Comment> get pollComments => _pollComments;

  void addPollComment(String content) {
    if (_currentUser == null) {
      // Optionally handle case where user is not logged in
      // // print("User not logged in, cannot add poll comment.");
      return;
    }
    final newComment = Comment(
      id: Uuid().v4(), // Generate a unique ID for the comment
      userId: _currentUser!.id, // Add current user's ID
      username: _currentUser!.name, // Use current user's name
      content: content,
      userAvatar: _currentUser!.avatarUrl, // Use current user's avatar
      // Other parameters use their default values
    );
    _pollComments.add(newComment);
    notifyListeners();
  }

  // Data collections
  final Map<String, dynamic> _sharedData = {};
  List<String> _favoritePages = [];
  List<String> _favoriteStores = [];
  // Removed duplicate: List<NotificationModel> _notifications = [];
  List<Post> _posts = [];
  List<Story> _stories = [];
  final List<Comment> _pollComments =
      []; // Using Comment model for richer poll comments
  List<Video> _videos = [];
  List<TaskGroup> _groups = [];
  Map<String, List<MedicalRecord>> _medicalRecords = {};
  List<Map<String, dynamic>> _professions = [];
  List<Map<String, dynamic>> _contents = [];
  final List<SimpleTask> tasks = [];
  final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();
  Map<String, Map<String, List<SimpleTask>>> _tasksByGroupAndCategory = {
    'default': {
      'العمل': [],
      'الدراسة': [],
      'الشخصية': [],
      'أخرى': [],
    }
  };

  // Child Care Videos State
  List<Video> _childCareVideos = [];
  bool _isLoadingChildCareVideos = false;
  String _childCareVideosError = '';

  List<Video> get childCareVideos => _childCareVideos;
  bool get isLoadingChildCareVideos => _isLoadingChildCareVideos;
  String get childCareVideosError => _childCareVideosError;

  Future<void> fetchChildCareVideos(String? token) async {
    if (token == null) {
      _childCareVideosError = 'Authentication token is missing.';
      notifyListeners();
      return;
    }
    _isLoadingChildCareVideos = true;
    _childCareVideosError = '';
    notifyListeners();

    try {
      final response = await http.get(
        Uri.parse(
            '$_backendBaseUrl/videos'), // Assuming _backendBaseUrl is defined
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        if (responseData['success'] == true && responseData['data'] != null) {
          final List<dynamic> videosData = responseData['data'];
          _childCareVideos = videosData
              .map((data) => Video.fromJson(data as Map<String, dynamic>))
              .toList();
        } else {
          _childCareVideosError =
              responseData['message'] ?? 'Failed to load child care videos.';
          _childCareVideos = [];
        }
      } else {
        _childCareVideosError = 'Error fetching videos: ${response.statusCode}';
        _childCareVideos = [];
      }
    } catch (e) {
      _childCareVideosError = 'An error occurred: ${e.toString()}';
      _childCareVideos = [];
    }

    _isLoadingChildCareVideos = false;
    notifyListeners();
  }

  // Getters
  String? get userId => _userId;
  bool get isDarkMode => _isDarkMode;
  bool get isLoggedIn => _isLoggedIn;
  String? get userType => _userType;
  String get username => _username;
  String get selectedTheme => _selectedTheme;
  String? get profileImagePath => _profileImagePath;
  String? get token => _token;
  String? get currentPage => _currentPage;
  bool get isLoadingMore => _isLoadingMore;
  String get baseUrl => kBaseUrl;
  String? get authToken => _token;
  UserModel? get currentUser => _currentUser;
  Map<String, dynamic> get sharedData => _sharedData;
  List<String> get favoritePages => _favoritePages;
  List<String> get favoriteStores => _favoriteStores;
  List<Post> get posts => _posts;
  List<Story> get stories => _stories;
  List<Video> get videos => _videos;
  List<TaskGroup> get groups => _groups;
  Map<String, List<MedicalRecord>> get medicalRecords => _medicalRecords;
  Map<String, Map<String, List<SimpleTask>>> get tasksByGroupAndCategory =>
      _tasksByGroupAndCategory;
  List<Map<String, dynamic>> get professions => _professions;
  List<Map<String, dynamic>> get contents => _contents;
  bool get shareLocation => _shareLocation; // Added getter for shareLocation

  // Toggle location sharing
  void toggleLocationSharing() async {
    _shareLocation = !_shareLocation;
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('shareLocation', _shareLocation);
    notifyListeners();
  }

  // نظام النجوم والمتجر والأفاتار
  int _stars = 0;
  final List<String> _purchasedItems = [];
  String _avatarId = '';

  int get stars => _stars;
  List<String> get purchasedItems => _purchasedItems;
  String get avatarId => _avatarId;

  void addStars(int n) async {
    final originalStars = _stars;
    _stars += n;
    notifyListeners(); // Optimistic update

    if (_profileProvider != null && _token != null) {
      try {
        await _profileProvider!
            .updateStarsOnBackend(n, _token!, _backendBaseUrl);
        // Backend call successful, _profileProvider.stars should be updated, sync it back
        _stars = _profileProvider!.stars;
      } catch (e) {
        _stars = originalStars; // Revert optimistic update on error
        showSnackBar('Failed to update stars on server: $e', Colors.red);
      }
    } else {
      _stars = originalStars; // Revert if no provider or token
      showSnackBar('ProfileProvider or token not available for star update.',
          Colors.orange);
    }
    notifyListeners(); // Notify final state
  }

  void spendStars(int n) async {
    if (_stars >= n) {
      final originalStars = _stars;
      _stars -= n;
      notifyListeners(); // Optimistic update

      if (_profileProvider != null && _token != null) {
        try {
          await _profileProvider!
              .updateStarsOnBackend(-n, _token!, _backendBaseUrl);
          _stars = _profileProvider!.stars; // Sync back from provider
        } catch (e) {
          _stars = originalStars; // Revert optimistic update
          showSnackBar('Failed to update stars on server: $e', Colors.red);
        }
      } else {
        _stars = originalStars; // Revert if no provider or token
        showSnackBar('ProfileProvider or token not available for star update.',
            Colors.orange);
      }
      notifyListeners(); // Notify final state
    } else {
      showSnackBar('Not enough stars to spend.', Colors.orange);
    }
  }

  void addPurchasedItem(String id) {
    if (!_purchasedItems.contains(id)) {
      _purchasedItems.add(id);
      notifyListeners();
    }
  }

  void setAvatar(String id) {
    _avatarId = id;
    notifyListeners();
  }
  // نهاية الإضافات

  // --- نظام النقاط والمكافآت الموحد ---
  int _points = 0;
  final List<Map<String, dynamic>> _rewardsHistory = [];

  int get points => _points;
  List<Map<String, dynamic>> get rewardsHistory => _rewardsHistory;
  String get rewardLevelName => RewardsManager.getLevel(_points).name;
  String get rewardBadgeAsset => RewardsManager.getLevel(_points).badgeAsset;

  void addPoints(RewardAction action, {String? note, int? customPoints}) {
    int pts = customPoints ?? RewardsManager.getPointsForAction(action);
    _points += pts;
    _rewardsHistory.add({
      'action': action.name,
      'points': pts,
      'note': note ?? '',
      'date': DateTime.now().toIso8601String(),
    });
    notifyListeners();
    showSnackBar('حصلت على $pts نقطة! مجموع نقاطك: $_points', Colors.teal);
  }

  void addCustomPoints(int pts, {String? note}) {
    addPoints(RewardAction.custom, note: note, customPoints: pts);
  }

  void resetPoints() {
    _points = 0;
    _rewardsHistory.clear();
    notifyListeners();
  }

  // --- نهاية نظام النقاط ---

  /// التوكن المركزي الجديد للتطبيق (base64_signer_key)
  static const String signerKey =
      '9ohgVYtBDx790tHxu6sVLpdeXk7aBV7VYakkQmySf/gurIpe17kUbIvFE8296vLuLwoDVshj8WaHsGaTcdd3iQ==';

  AppState() {
    tz.initializeTimeZones();
    _initializeNotifications();
    _loadTasks();
    _loadThemePreference();
    _loadFavoritePages();
    _loadFavoriteStores();
    _loadNotificationPreferences(); // تحميل إعدادات الإشعارات
    initSocket();
  }

  // Check internet connectivity
  Future<bool> _checkConnectivity() async {
    var connectivityResult = await Connectivity().checkConnectivity();
    return connectivityResult != ConnectivityResult.none;
  }

  // Set shared data for cross-page communication
  void setSharedData(String key, dynamic value) {
    _sharedData[key] = value;
    notifyListeners();
  }

  // Set current page for navigation tracking
  void setCurrentPage(String pageName) {
    _currentPage = pageName;
    notifyListeners();
  }

  // Send a notification to be displayed across pages
  void sendCrossPageNotification(String message) {
    _notifications.add(
        NotificationModel(message: message, date: DateTime.now().toString()));
    notifyListeners();
  }

  // Show a SnackBar with the given message
  void showSnackBar(String message, [Color? backgroundColor]) {
    final context = navigatorKey.currentContext;
    if (context != null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: backgroundColor ?? Colors.teal[700],
          behavior: SnackBarBehavior.floating,
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        ),
      );
    } else {
      scaffoldMessengerKey.currentState?.showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: backgroundColor ?? Colors.teal[700],
          behavior: SnackBarBehavior.floating,
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        ),
      );
    }
  }

  // Upload media file
  Future<String?> uploadMedia(File file, String endpoint) async {
    if (!await _checkConnectivity()) {
      showSnackBar('لا يوجد اتصال بالإنترنت', Colors.red);
      return null;
    }
    try {
      var request =
          http.MultipartRequest('POST', Uri.parse('$kBaseUrl$endpoint'));
      request.headers['Authorization'] = 'Bearer $_token';
      request.files.add(await http.MultipartFile.fromPath('file', file.path));
      final response =
          await request.send().timeout(const Duration(seconds: 15));
      final responseData = await http.Response.fromStream(response);
      if (response.statusCode == 200) {
        final data = jsonDecode(responseData.body);
        return data['url'];
      } else {
        throw Exception('فشل في رفع الملف: ${responseData.body}');
      }
    } catch (e) {
      showSnackBar('خطأ في رفع الملف: $e', Colors.red);
      return null;
    }
  }

  // Cancel notification
  Future<void> cancelNotification(int notificationId) async {
    await flutterLocalNotificationsPlugin.cancel(notificationId);
  }

  // Initialize Socket.IO connection
  void initSocket() {
    if (socket != null) socket!.disconnect();
    try {
      // ملاحظة: إذا كنت تعمل على محاكي أندرويد استخدم عنوان IP مثل '********' بدلاً من 'localhost' أو '127.0.0.1'
      // ويمكنك تعديل getBackendUrl() ليعيد العنوان المناسب حسب البيئة
      socket = io.io(kBaseUrl, <String, dynamic>{
        'transports': [
          'websocket',
          'polling'
        ], // إضافة polling كبديل إذا فشل websocket
        'autoConnect': false,
        'reconnection': true, // تفعيل إعادة الاتصال التلقائي
        'reconnectionAttempts': 5, // عدد محاولات إعادة الاتصال
        'reconnectionDelay': 5000, // 5 ثواني بين كل محاولة
        'timeout': 20000, // زيادة مهلة الاتصال إلى 20 ثانية
        'extraHeaders': {'Authorization': 'Bearer $_token'},
        'query': {'token': _token}
      });

      // محاولة الاتصال
      socket!.connect();

      // عند نجاح الاتصال
      socket!.onConnect((_) {
        if (kDebugMode) print('Connected to WebSocket');
        showSnackBar('تم الاتصال بالخادم', Colors.green);
      });

      // عند فشل الاتصال
      socket!.onConnectError((error) {
        if (kDebugMode) print('Connection Error: $error');
        // عدم إظهار رسالة خطأ لكل محاولة فاشلة لتجنب إزعاج المستخدم
        // showSnackBar('فشل الاتصال بالخادم: $error', Colors.red);
      });

      // عند قطع الاتصال
      socket!.onDisconnect((_) {
        if (kDebugMode) print('Disconnected from WebSocket');
      });

      // عند فشل المحاولات المتكررة
      socket!.onReconnectFailed((_) {
        if (kDebugMode) {
          print('WebSocket reconnection failed after multiple attempts');
        }
        showSnackBar(
            'فشل الاتصال بالخادم بعد عدة محاولات. سيعمل التطبيق بدون اتصال مباشر.',
            Colors.orange);
      });

      // عند محاولة إعادة الاتصال
      socket!.onReconnect((_) {
        if (kDebugMode) print('Reconnected to WebSocket');
        showSnackBar('تم إعادة الاتصال بالخادم', Colors.green);
      });

      // عند حدوث خطأ في الاتصال
      socket!.onError((error) {
        if (kDebugMode) print('Socket Error: $error');
      });
      socket!.on('notification', (data) {
        _notifications.add(NotificationModel(
          message: data['message'] ?? 'إشعار جديد',
          date: DateTime.now().toString(),
        ));
        showNotification('إشعار جديد', data['message'] ?? 'إشعار جديد');
        notifyListeners();
      });
      socket!.on('newPost', (data) {
        try {
          final post = Post.fromJson(data);
          if (!_posts.any((p) => p.id == post.id)) {
            _posts.insert(0, post);
            notifyListeners();
          }
        } catch (e) {
          // print('Error adding new post: $e');
        }
      });
      socket!.on('newComment', (data) {
        try {
          final postId = data['postId'];
          final comment = Comment.fromJson(data['comment']);
          final postIndex = _posts.indexWhere((p) => p.id == postId);
          if (postIndex != -1) {
            _posts[postIndex].comments.add(comment);
            _notifications.add(NotificationModel(
              id: const Uuid().v4(),
              message: 'تعليق جديد على منشورك من ${comment.username}',
              date: DateTime.now().toString(),
              isRead: false,
            ));
            notifyListeners();
          }
        } catch (e) {
          // print('Error adding new comment: $e');
        }
      });
      socket!.on('new_story', (data) {
        try {
          _stories.insert(0, Story.fromJson(data));
          notifyListeners();
        } catch (e) {
          // print('Error adding new story: $e');
        }
      });
      socket!.on('new_video', (data) {
        _videos.insert(0, Video.fromJson(data));
        _notifications.add(NotificationModel(
            message: 'تم إضافة فيديو جديد: ${data['title']}',
            date: DateTime.now().toString()));
        notifyListeners();
      });
      socket!.on('live_stream_started', (data) {
        _notifications.add(NotificationModel(
            message: 'بث مباشر بدأ: ${data['title']}',
            date: DateTime.now().toString()));
        notifyListeners();
      });
      socket!.on('chat_message_$_userId', (data) {
        _notifications.add(NotificationModel(
            message: 'رسالة جديدة من ${data['senderId']}',
            date: DateTime.now().toString()));
        notifyListeners();
      });
      socket!.on('group_message', (data) {
        _notifications.add(NotificationModel(
            message: 'رسالة جديدة في المجموعة: ${data['groupId']}',
            date: DateTime.now().toString()));
        notifyListeners();
      });
      socket!.on('new_vote', (data) {
        _notifications.add(NotificationModel(
            message: 'تصويت جديد في الاستطلاع: ${data['pollId']}',
            date: DateTime.now().toString()));
        notifyListeners();
      });
      socket!.on('new_meal_plan', (data) {
        _notifications.add(NotificationModel(
            message: 'تم تحديث خطة الوجبات لـ ${data['memberId']}',
            date: DateTime.now().toString()));
        showNotification(
            'خطة وجبات جديدة', 'تم تحديث خطة الوجبات لـ ${data['memberId']}');
        notifyListeners();
      });
      socket!.on('group_updated', (data) {
        try {
          final groupId = data['groupId'];
          final groupIndex = _groups.indexWhere((g) => g.id == groupId);
          if (groupIndex != -1) {
            _groups[groupIndex] = TaskGroup.fromJson(data);
            _notifications.add(NotificationModel(
              id: const Uuid().v4(),
              message: 'تم تحديث المجموعة: ${data['name']}',
              date: DateTime.now().toString(),
              isRead: false,
            ));
            notifyListeners();
          }
        } catch (e) {
          // print('Error updating group: $e');
        }
      });
      socket!.onError((err) => {} /* print('Socket error: $err') */);
    } catch (e) {
      showSnackBar('خطأ في تهيئة الاتصال: $e', Colors.red);
    }
  }

  // Disconnect Socket.IO
  void disconnectSocket() {
    socket?.disconnect();
    socket = null;
  }

  // Initialize local notifications
  Future<void> _initializeNotifications() async {
    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');
    const DarwinInitializationSettings initializationSettingsIOS =
        DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );
    const InitializationSettings initializationSettings =
        InitializationSettings(
      android: initializationSettingsAndroid,
      iOS: initializationSettingsIOS,
    );
    await flutterLocalNotificationsPlugin.initialize(initializationSettings);

    // طلب إذن الإشعارات على Android
    final androidPlugin =
        flutterLocalNotificationsPlugin.resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin>();
    await androidPlugin?.requestNotificationsPermission();

    // طلب إذن الإشعارات على iOS
    final iosPlugin =
        flutterLocalNotificationsPlugin.resolvePlatformSpecificImplementation<
            IOSFlutterLocalNotificationsPlugin>();
    await iosPlugin?.requestPermissions(
      alert: true,
      badge: true,
      sound: true,
    );
  }

  // Show a local notification
  Future<void> showNotification(String title, String body) async {
    // تشغيل الإشعار الكتابي إذا كان نوع الإشعار 'text' أو 'both'
    if (_notificationType == 'text' || _notificationType == 'both') {
      const AndroidNotificationDetails androidPlatformChannelSpecifics =
          AndroidNotificationDetails(
        'general_channel',
        'General Notifications',
        channelDescription: 'General notifications for the app',
        importance: Importance.max,
        priority: Priority.high,
      );
      const DarwinNotificationDetails iOSPlatformChannelSpecifics =
          DarwinNotificationDetails();
      const NotificationDetails platformChannelSpecifics = NotificationDetails(
        android: androidPlatformChannelSpecifics,
        iOS: iOSPlatformChannelSpecifics,
      );

      await flutterLocalNotificationsPlugin.show(
        DateTime.now().millisecondsSinceEpoch % 100000, // معرف فريد
        title,
        body,
        platformChannelSpecifics,
      );
    }

    // تشغيل الصوت إذا كان نوع الإشعار 'sound' أو 'both'
    if (_notificationType == 'sound' || _notificationType == 'both') {
      await _playNotificationSound();
    }
  }

  // تشغيل صوت الإشعار بناءً على الفئات المختارة
  Future<void> _playNotificationSound() async {
    String? soundPath;
    if (_selectedCategories.contains('christian')) {
      soundPath = 'sounds/christian_notification.mp3';
    } else if (_selectedCategories.contains('islamic')) {
      soundPath = 'sounds/islamic_notification.mp3';
    } else if (_selectedCategories.contains('wisdom')) {
      soundPath = 'sounds/wisdom_notification.mp3';
    }

    if (soundPath != null) {
      await _audioPlayer.play(AssetSource(soundPath));
    }
  }

  // تحميل إعدادات الإشعارات من SharedPreferences
  Future<void> _loadNotificationPreferences() async {
    final prefs = await SharedPreferences.getInstance();
    _notificationType = prefs.getString('notificationType') ?? 'both';
    _selectedCategories =
        (prefs.getStringList('selectedCategories') ?? ['islamic']).toList();
    notifyListeners();
  }

  // ضبط نوع الإشعار
  void setNotificationType(String type) async {
    _notificationType = type;
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('notificationType', type);
    notifyListeners();
  }

  // تبديل فئة الإشعارات
  void toggleCategory(String category) async {
    if (_selectedCategories.contains(category)) {
      _selectedCategories.remove(category);
    } else {
      _selectedCategories.add(category);
    }
    final prefs = await SharedPreferences.getInstance();
    await prefs.setStringList(
        'selectedCategories', _selectedCategories.toList());
    notifyListeners();
  }

  // Toggle theme mode
  void toggleTheme() {
    _isDarkMode = !_isDarkMode;
    _saveThemePreference();
    notifyListeners();
  }

  // Save theme preferences
  Future<void> _saveThemePreference() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('isDarkMode', _isDarkMode);
    await prefs.setString('selectedTheme', _selectedTheme);
  }

  // Load theme preferences
  Future<void> _loadThemePreference() async {
    final prefs = await SharedPreferences.getInstance();
    _isDarkMode = prefs.getBool('isDarkMode') ?? false;
    _selectedTheme = prefs.getString('selectedTheme') ?? 'Default';
    notifyListeners();
  }

  // Set theme by name
  void setTheme(String themeName) {
    _selectedTheme = themeName;
    _isDarkMode = false;
    _saveThemePreference();
    notifyListeners();
  }

  // Set token
  void setToken(String token) {
    _token = token;
    notifyListeners();
  }

  // Set username
  void setUsername(String username) {
    _username = username;
    notifyListeners();
  }

  // Set socket
  void setSocket(io.Socket socket) {
    this.socket = socket;
    notifyListeners();
  }

  // Login user
  Future<void> login(
      String phoneNumber, String password, String? userType) async {
    if (!await _checkConnectivity()) {
      showSnackBar('لا يوجد اتصال بالإنترنت', Colors.red);
      return;
    }
    try {
      final response = await http
          .post(
            Uri.parse('$kBaseUrl/api/auth/login'),
            headers: {'Content-Type': 'application/json'},
            body:
                jsonEncode({'phoneNumber': phoneNumber, 'password': password}),
          )
          .timeout(const Duration(seconds: 10));
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        _isLoggedIn = true;
        _token = data["token"];
        if (_token == null || _token!.isEmpty) {
          showSnackBar("فشل تسجيل الدخول: لم يتم استلام التوكن", Colors.red);
          _isLoggedIn = false;
          return;
        }
        _username = data['user']?['username'] ?? phoneNumber;
        _userId = data['user']?['id'] ?? const Uuid().v4();
        _userType = data['user']?['userType'] ?? userType ?? 'user';
        _profileImagePath = data['user']?['avatarUrl'];
        _currentUser = UserModel(
          id: _userId!,
          name: _username,
          email: data['user']?['email'] ?? '<EMAIL>',
          avatarUrl: _profileImagePath ?? '',
          role: UserRole.member,
          stars: data['user']?['stars'] as int? ??
              0, // Initialize stars from login data
        );
        _stars =
            _currentUser!.stars; // Sync AppState._stars with UserModel's stars

        // If ProfileProvider is available, ensure its data is also up-to-date
        // This assumes login might also update profile data centrally if ProfileProvider handles it
        _profileProvider?.setProfileData(data['user'] ?? {});

        socket?.emit('join', _username);
        initSocket();
        await Future.wait([
          fetchVideos(),
          fetchGroups(),
          fetchPosts(),
          fetchStories(),
          fetchProfessions(),
          fetchContents(),
          if (_userType == "medical") fetchMedicalRecords(),
          _loadTasks(),
        ]);
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('token', _token!);
        await prefs.setString('userId', _userId!);
        await prefs.setString('username', _username);
        await prefs.setString('userType', _userType!);
        notifyListeners();
        showSnackBar('تم تسجيل الدخول بنجاح', Colors.green);
      } else {
        String errorMessage = 'فشل تسجيل الدخول. حاول مرة أخرى.';
        try {
          final errorData = jsonDecode(response.body);
          errorMessage = errorData['error'] ?? errorMessage;
        } catch (_) {}
        if (kDebugMode) {
          print('Login failed: ${response.statusCode} - ${response.body}');
        }
        showSnackBar(errorMessage, Colors.red);
      }
    } catch (e) {
      showSnackBar('حدث خطأ: $e', Colors.red);
    }
  }

  // Login as guest
  Future<void> loginAsGuest() async {
    _isLoggedIn = true;
    _username = "زائر";
    _userType = "guest";
    _userId = const Uuid().v4();
    _profileImagePath = null;
    _currentUser = UserModel(
      id: _userId!,
      name: _username,
      email: '<EMAIL>',
      role: UserRole.guest,
      stars: 0, // Guests start with 0 stars
    );
    _stars = _currentUser!.stars; // Sync AppState._stars
    initSocket();
    await _loadTasks();
    notifyListeners();
    showSnackBar('تم تسجيل الدخول كزائر', Colors.green);
  }

  // تسجيل الدخول كأحد الحكماء (للاختبار فقط)
  Future<void> loginAsHokama() async {
    _isLoggedIn = true;
    _username = "حكيم محمد";
    _userType = "hokama";
    _userId = const Uuid().v4();
    _profileImagePath = null;
    _currentUser = UserModel(
      id: _userId!,
      name: _username,
      email: '<EMAIL>',
      role: UserRole.hokama,
      stars: 50, // الحكماء يبدأون بـ 50 نقطة
    );
    _stars = _currentUser!.stars; // Sync AppState._stars

    // حفظ بيانات المستخدم في التخزين المحلي
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('token', 'hokama_test_token');
    await prefs.setString('userId', _userId!);
    await prefs.setString('username', _username);
    await prefs.setString('userType', _userType!);

    initSocket();
    await Future.wait([
      fetchVideos(),
      fetchGroups(),
      fetchPosts(),
      fetchStories(),
      fetchProfessions(),
      fetchContents(),
      _loadTasks(),
    ]);
    notifyListeners();
    showSnackBar('تم تسجيل الدخول كحكيم (وضع تجريبي)', Colors.green);
  }

  // Register new user
  Future<void> register(
      String username,
      String password,
      String phoneNumber,
      String job,
      String? idCardImagePath,
      String? location,
      String role) async {
    if (!await _checkConnectivity()) {
      showSnackBar('لا يوجد اتصال بالإنترنت', Colors.red);
      return;
    }
    try {
      var request = http.MultipartRequest(
          'POST', Uri.parse('$kBaseUrl/api/auth/register'));
      request.fields['username'] = username;
      request.fields['password'] = password;
      request.fields['phoneNumber'] = phoneNumber;
      request.fields['job'] = job;
      request.fields['role'] = role;
      request.fields['userType'] = role;
      if (location != null) request.fields['location'] = location;
      if (idCardImagePath != null && !kIsWeb) {
        request.files.add(
            await http.MultipartFile.fromPath('idCardImage', idCardImagePath));
      }
      final response = await request.send();
      final responseData = await http.Response.fromStream(response);

      if (response.statusCode == 201) {
        final data = jsonDecode(responseData.body);
        _isLoggedIn = true;
        _username = username;
        _currentUser = UserModel.fromJson(data['user']);
        _userId = data['user']?['id'] ?? const Uuid().v4();
        _profileImagePath = data['user']?['avatarUrl'];
        _token = data['token'];
        _currentUser = UserModel(
          id: _userId!,
          name: _username,
          email: data['user']?['email'] ?? '$<EMAIL>',
          avatarUrl: _profileImagePath ?? '',
          role: UserRole.values.firstWhere(
            (r) => r.name == (data['user']?['role'] ?? role),
            orElse: () => UserRole.member,
          ),
          stars: data['user']?['stars'] as int? ??
              0, // Initialize stars from register data
        );
        _stars = _currentUser!.stars; // Sync AppState._stars

        // If ProfileProvider is available, ensure its data is also up-to-date
        _profileProvider?.setProfileData(data['user'] ?? {});

        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('token', _token!);
        await prefs.setString('userId', _userId!);
        await prefs.setString('username', _username);
        await prefs.setString('userType', _userType!);
        _notifications.add(NotificationModel(
            message: "تم إنشاء الحساب بنجاح", date: DateTime.now().toString()));
        initSocket();
        await _loadTasks();
        notifyListeners();
        showSnackBar('تم التسجيل بنجاح', Colors.green);
      } else {
        final errorData = jsonDecode(responseData.body);
        showSnackBar(errorData['error'] ?? 'فشل التسجيل', Colors.red);
      }
    } catch (e) {
      showSnackBar('خطأ في الاتصال: $e', Colors.red);
    }
  }

  // Logout user
  Future<void> logout() async {
    disconnectSocket();
    _isLoggedIn = false;
    _token = null;
    _userId = null;
    _userType = null;
    _username = "مستخدم";
    _profileImagePath = null;
    _currentUser = null;
    _notifications.clear();
    _posts.clear();
    _stories.clear();
    _videos.clear();
    _groups.clear();
    _medicalRecords.clear();
    _professions.clear();
    _contents.clear();
    _favoritePages.clear();
    _favoriteStores.clear();
    _tasksByGroupAndCategory.forEach((group, categories) =>
        categories.forEach((cat, tasks) => tasks.clear()));
    tasks.clear();

    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('token');
    await prefs.remove('userId');
    await prefs.remove('username');
    await prefs.remove('userType');
    await prefs.remove('favoritePages');
    await prefs.remove('favoriteStores');
    await prefs.remove('tasks_by_group_and_category');

    notifyListeners();
    showSnackBar('تم تسجيل الخروج بنجاح', Colors.green);
  }

  // Load saved login data
  Future<bool> loadSavedLoginData() async {
    final prefs = await SharedPreferences.getInstance();
    final savedToken = prefs.getString('token');

    if (savedToken != null && savedToken.isNotEmpty) {
      _token = savedToken;
      _userId = prefs.getString('userId');
      _username = prefs.getString('username') ?? 'مستخدم';
      _userType = prefs.getString('userType');
      _isLoggedIn = true;

      _currentUser = UserModel(
        id: _userId!,
        name: _username,
        email: '$<EMAIL>',
        role: UserRole.member,
      );

      initSocket();
      await Future.wait([
        fetchVideos(),
        fetchGroups(),
        fetchPosts(),
        fetchStories(),
        fetchProfessions(),
        fetchContents(),
        if (_userType == "medical") fetchMedicalRecords(),
        _loadTasks(),
      ]);

      notifyListeners();
      return true;
    }
    return false;
  }

  // Update profile image
  void updateProfileImage(String path) {
    _profileImagePath = path;
    notifyListeners();
  }

  // Update username
  void updateUsername(String name) {
    _username = name;
    notifyListeners();
  }

  // Add favorite page
  void addFavoritePage(String pageName) {
    if (!_favoritePages.contains(pageName)) {
      _favoritePages.add(pageName);
      _saveFavoritePages();
      notifyListeners();
    }
  }

  // Remove favorite page
  void removeFavoritePage(String pageName) {
    _favoritePages.remove(pageName);
    _saveFavoritePages();
    notifyListeners();
  }

  // Save favorite pages
  Future<void> _saveFavoritePages() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setStringList('favoritePages', _favoritePages);
  }

  // Load favorite pages
  Future<void> _loadFavoritePages() async {
    final prefs = await SharedPreferences.getInstance();
    _favoritePages = prefs.getStringList('favoritePages') ?? [];
    notifyListeners();
  }

  // Toggle favorite store
  void toggleFavoriteStore(String storeId) {
    try {
      if (_favoriteStores.contains(storeId)) {
        _favoriteStores.remove(storeId);
        showSnackBar('تمت إزالة المتجر من المفضلة', Colors.orange);
      } else {
        _favoriteStores.add(storeId);
        showSnackBar('تمت إضافة المتجر إلى المفضلة', Colors.green);
      }
      _saveFavoriteStores();
      notifyListeners();
    } catch (e) {
      // print('Error in toggleFavoriteStore: $e');
      showSnackBar('خطأ في تحديث المفضلة: $e', Colors.red);
    }
  }

  // Save favorite stores
  Future<void> _saveFavoriteStores() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setStringList('favoriteStores', _favoriteStores);
  }

  // Load favorite stores
  Future<void> _loadFavoriteStores() async {
    final prefs = await SharedPreferences.getInstance();
    _favoriteStores = prefs.getStringList('favoriteStores') ?? [];
    notifyListeners();
  }

  // Schedule a notification
  Future<void> scheduleNotification(
      String title, String body, DateTime scheduledDate) async {
    const AndroidNotificationDetails androidPlatformChannelSpecifics =
        AndroidNotificationDetails(
      'task_channel',
      'Task Reminders',
      channelDescription: 'Notifications for task reminders',
      importance: Importance.max,
      priority: Priority.high,
    );
    const DarwinNotificationDetails iOSPlatformChannelSpecifics =
        DarwinNotificationDetails();
    const NotificationDetails platformChannelSpecifics = NotificationDetails(
      android: androidPlatformChannelSpecifics,
      iOS: iOSPlatformChannelSpecifics,
    );

    await flutterLocalNotificationsPlugin.zonedSchedule(
      0,
      title,
      body,
      tz.TZDateTime.from(scheduledDate, tz.local),
      platformChannelSpecifics,
      androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
      uiLocalNotificationDateInterpretation:
          UILocalNotificationDateInterpretation.absoluteTime,
    );
  }

  // Add a task
  Future<void> addTask(String groupId, String task) async {
    if (!await _checkConnectivity()) {
      showSnackBar('لا يوجد اتصال بالإنترنت', Colors.red);
      return;
    }
    try {
      final response = await http
          .post(
            Uri.parse('$kBaseUrl/api/tasks'),
            headers: {
              'Authorization': 'Bearer $_token',
              'Content-Type': 'application/json',
            },
            body: jsonEncode({
              'groupId': groupId,
              'title': task,
            }),
          )
          .timeout(const Duration(seconds: 10));

      if (response.statusCode == 201) {
        final data = jsonDecode(response.body);
        final newTask = SimpleTask.fromJson(data);

        if (!_tasksByGroupAndCategory.containsKey(groupId)) {
          _tasksByGroupAndCategory[groupId] = {
            'العمل': [],
            'الدراسة': [],
            'الشخصية': [],
            'أخرى': [],
          };
        }
        _tasksByGroupAndCategory[groupId]!['أخرى']!.add(newTask);
        tasks.add(newTask);
        await _saveTasks();

        if (newTask.dueDate != null) {
          await scheduleNotification(
            'تذكير بالمهمة',
            'حان موعد المهمة: ${newTask.title}',
            newTask.dueDate!,
          );
        }

        notifyListeners();
        showSnackBar('تم إضافة المهمة بنجاح', Colors.green);
      } else {
        throw Exception('فشل في إضافة المهمة: ${response.body}');
      }
    } catch (e) {
      showSnackBar('خطأ في إضافة المهمة: $e', Colors.red);
    }
  }

  // Update task status
  void updateTaskStatus(String taskId, bool isDone,
      {String? group, String? category}) async {
    bool taskUpdated = false;
    if (group != null && category != null) {
      if (_tasksByGroupAndCategory.containsKey(group) &&
          _tasksByGroupAndCategory[group]!.containsKey(category)) {
        final taskIndex = _tasksByGroupAndCategory[group]![category]!
            .indexWhere((task) => task.id == taskId);
        if (taskIndex != -1) {
          _tasksByGroupAndCategory[group]![category]![taskIndex].isDone =
              isDone;
          taskUpdated = true;
          if (isDone) {
            await cancelNotification(taskId.hashCode);
          }
        }
      }
    } else {
      outerLoop:
      for (final groupEntry in _tasksByGroupAndCategory.entries) {
        for (final categoryEntry in groupEntry.value.entries) {
          final taskIndex =
              categoryEntry.value.indexWhere((task) => task.id == taskId);
          if (taskIndex != -1) {
            categoryEntry.value[taskIndex].isDone = isDone;
            taskUpdated = true;
            if (isDone) {
              await cancelNotification(taskId.hashCode);
            }
            break outerLoop;
          }
        }
      }
    }
    if (taskUpdated) {
      final taskIndex = tasks.indexWhere((task) => task.id == taskId);
      if (taskIndex != -1) {
        tasks[taskIndex].isDone = isDone;
      }
      await _saveTasks();
      notifyListeners();
    }
  }

  // Delete task
  void deleteTask(String taskId, {String? group, String? category}) async {
    bool taskRemoved = false;
    if (group != null && category != null) {
      if (_tasksByGroupAndCategory.containsKey(group) &&
          _tasksByGroupAndCategory[group]!.containsKey(category)) {
        final initialLength =
            _tasksByGroupAndCategory[group]![category]!.length;
        _tasksByGroupAndCategory[group]![category]!
            .removeWhere((task) => task.id == taskId);
        taskRemoved =
            _tasksByGroupAndCategory[group]![category]!.length < initialLength;
        if (taskRemoved) {
          await cancelNotification(taskId.hashCode);
        }
      }
    } else {
      outerLoop:
      for (final groupEntry in _tasksByGroupAndCategory.entries) {
        for (final categoryEntry in groupEntry.value.entries) {
          final initialLength = categoryEntry.value.length;
          categoryEntry.value.removeWhere((task) => task.id == taskId);
          if (categoryEntry.value.length < initialLength) {
            taskRemoved = true;
            await cancelNotification(taskId.hashCode);
            break outerLoop;
          }
        }
      }
    }
    if (taskRemoved) {
      tasks.removeWhere((task) => task.id == taskId);
      await _saveTasks();
      notifyListeners();
      showSnackBar('تم حذف المهمة بنجاح', Colors.green);
    }
  }

  // Save tasks to SharedPreferences
  Future<void> _saveTasks() async {
    final prefs = await SharedPreferences.getInstance();
    final tasksData = <String, dynamic>{};
    _tasksByGroupAndCategory.forEach((group, categories) {
      tasksData[group] = <String, dynamic>{};
      categories.forEach((category, tasks) {
        tasksData[group][category] =
            tasks.map((task) => task.toJson()).toList();
      });
    });
    await prefs.setString('tasks_by_group_and_category', jsonEncode(tasksData));
  }

  // Load tasks from SharedPreferences
  Future<void> _loadTasks() async {
    final prefs = await SharedPreferences.getInstance();
    final tasksJson = prefs.getString('tasks_by_group_and_category');
    if (tasksJson != null) {
      try {
        final tasksData = jsonDecode(tasksJson) as Map<String, dynamic>;
        _tasksByGroupAndCategory = {};
        tasksData.forEach((group, categories) {
          _tasksByGroupAndCategory[group] = {};
          (categories as Map<String, dynamic>).forEach((category, tasks) {
            _tasksByGroupAndCategory[group]![category] = (tasks as List)
                .map((taskJson) =>
                    SimpleTask.fromJson(taskJson as Map<String, dynamic>))
                .toList();
          });
        });
        tasks.clear();
        _tasksByGroupAndCategory.forEach((group, categories) {
          categories.forEach((category, taskList) {
            tasks.addAll(taskList);
          });
        });
        notifyListeners();
      } catch (e) {
        // print('Error loading tasks: $e');
        showSnackBar('خطأ في تحميل المهام: $e', Colors.red);
      }
    }
  }

  // Fetch professions
  Future<void> fetchProfessions() async {
    if (!await _checkConnectivity()) {
      showSnackBar('لا يوجد اتصال بالإنترنت', Colors.red);
      return;
    }
    try {
      final response = await http.get(
        Uri.parse('$kBaseUrl/professions'),
        headers: {'x-auth-token': _token ?? defaultAuthToken},
      ).timeout(const Duration(seconds: 10));
      if (response.statusCode == 200) {
        _professions =
            List<Map<String, dynamic>>.from(jsonDecode(response.body));
        notifyListeners();
      } else {
        throw Exception('فشل في جلب المهن: ${response.statusCode}');
      }
    } catch (e) {
      showSnackBar('خطأ في تحميل المهن: $e', Colors.red);
    }
  }

  // Submit profession
  Future<void> submitProfession(
      String title, String category, File image) async {
    if (!await _checkConnectivity()) {
      showSnackBar('لا يوجد اتصال بالإنترنت', Colors.red);
      return;
    }
    try {
      // Create form data for file upload
      var request = http.MultipartRequest(
          'POST', Uri.parse('$kBaseUrl/professions/submit'));
      request.headers['x-auth-token'] = _token ?? defaultAuthToken;
      request.fields['title'] = title;
      request.fields['category'] = category;
      request.files.add(await http.MultipartFile.fromPath('image', image.path));
      final response =
          await request.send().timeout(const Duration(seconds: 15));
      if (response.statusCode == 201) {
        showSnackBar('تم تقديم المهنة للمراجعة', Colors.green);
        await fetchProfessions();
      } else {
        final error = await response.stream.bytesToString();
        throw Exception('فشل في تقديم المهنة: $error');
      }
    } catch (e) {
      showSnackBar('خطأ في تقديم المهنة: $e', Colors.red);
    }
  }

  // Fetch contents
  Future<void> fetchContents() async {
    if (!await _checkConnectivity()) {
      showSnackBar('لا يوجد اتصال بالإنترنت', Colors.red);
      return;
    }
    try {
      final response = await http.get(
        Uri.parse('$kBaseUrl/contents'),
        headers: {'x-auth-token': _token ?? defaultAuthToken},
      ).timeout(const Duration(seconds: 10));
      if (response.statusCode == 200) {
        _contents = List<Map<String, dynamic>>.from(jsonDecode(response.body));
        notifyListeners();
      } else {
        throw Exception('فشل في جلب المحتويات');
      }
    } catch (e) {
      showSnackBar('خطأ في تحميل المحتويات: $e', Colors.red);
    }
  }

  // Add content
  Future<void> addContent(
      String title, String type, String description, File file) async {
    if (!await _checkConnectivity()) {
      showSnackBar('لا يوجد اتصال بالإنترنت', Colors.red);
      return;
    }
    try {
      var request =
          http.MultipartRequest('POST', Uri.parse('$kBaseUrl/contents'));
      request.headers['x-auth-token'] = _token ?? defaultAuthToken;
      request.fields['title'] = title;
      request.fields['type'] = type;
      request.fields['description'] = description;
      request.files.add(await http.MultipartFile.fromPath('file', file.path));
      final response =
          await request.send().timeout(const Duration(seconds: 15));
      if (response.statusCode == 201) {
        showSnackBar('تم إضافة المحتوى بنجاح', Colors.green);
        await fetchContents();
      } else {
        final error = await response.stream.bytesToString();
        throw Exception('فشل في إضافة المحتوى: $error');
      }
    } catch (e) {
      showSnackBar('خطأ في إضافة المحتوى: $e', Colors.red);
    }
  }

  // Fetch posts with pagination
  Future<void> fetchPosts({bool loadMore = false}) async {
    if (!await _checkConnectivity()) {
      showSnackBar('لا يوجد اتصال بالإنترنت', Colors.red);
      return;
    }
    if (_isLoadingMore || (!loadMore && _posts.isNotEmpty)) return;
    try {
      _isLoadingMore = true;
      notifyListeners();
      final response = await http.get(
        Uri.parse('$kBaseUrl/api/posts?page=$_postPage'),
        headers: {'Authorization': 'Bearer $_token'},
      ).timeout(const Duration(seconds: 10));
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final newPosts = List<Map<String, dynamic>>.from(data['posts'] ?? [])
            .map((post) => Post.fromJson(post))
            .toList();
        if (loadMore) {
          _posts.addAll(newPosts);
        } else {
          _posts = newPosts;
        }
        _postPage = data['nextPage'] ?? _postPage + 1;
        // _hasMorePosts = newPosts.isNotEmpty && data['nextPage'] != null;
        notifyListeners();
      } else {
        throw Exception('فشل في جلب المنشورات: ${response.statusCode}');
      }
    } catch (e) {
      showSnackBar('خطأ في تحميل المنشورات: $e', Colors.red);
    } finally {
      _isLoadingMore = false;
      notifyListeners();
    }
  }

  // Add a post
  Future<void> addPost(
      String content, String? mediaPath, String? mediaType) async {
    if (!await _checkConnectivity()) {
      showSnackBar('لا يوجد اتصال بالإنترنت', Colors.red);
      return;
    }
    try {
      String? mediaUrl;
      if (mediaPath != null && !kIsWeb) {
        mediaUrl = await uploadMedia(File(mediaPath), '/api/upload');
        if (mediaUrl == null) {
          throw Exception('فشل في رفع الوسائط');
        }
      }

      final post = Post(
        id: const Uuid().v4(),
        userId: _currentUser?.id ?? '',
        content: content,
        mediaUrl: mediaUrl,
        mediaType: mediaType,
        username: _username,
        avatar: _profileImagePath,
        likes: 0,
        loves: 0,
        haha: 0,
        isLiked: false,
        isSaved: false,
        comments: const [],
        hashtags: const [],
        promoted: false,
        visibility: PostVisibility.public,
      );

      final response = await http
          .post(
            Uri.parse('$kBaseUrl/api/posts'),
            headers: {
              'Authorization': 'Bearer $_token',
              'Content-Type': 'application/json',
            },
            body: jsonEncode(post.toJson()),
          )
          .timeout(const Duration(seconds: 10));

      if (response.statusCode == 201) {
        final data = jsonDecode(response.body);
        _posts.insert(0, Post.fromJson(data));
        socket?.emit('newPost', post.toJson());
        _notifications.add(NotificationModel(
          id: const Uuid().v4(),
          message: 'تم إضافة منشور جديد: $content',
          date: DateTime.now().toString(),
          isRead: false,
        ));
        await showNotification('منشور جديد', 'تم إضافة منشور جديد: $content');
        notifyListeners();
        showSnackBar('تم إضافة المنشور بنجاح', Colors.green);
      } else {
        throw Exception('فشل في إضافة المنشور: ${response.body}');
      }
    } catch (e) {
      showSnackBar('خطأ في إضافة المنشور: $e', Colors.red);
    }
  }

  // Edit a post
  Future<void> editPost(String postId, String newContent,
      {File? newImage}) async {
    if (!await _checkConnectivity()) {
      showSnackBar('لا يوجد اتصال بالإنترنت', Colors.red);
      return;
    }
    try {
      String? imageUrl;
      final postIndex = _posts.indexWhere((p) => p.id == postId);
      if (postIndex == -1) return;
      if (newImage != null && !kIsWeb) {
        imageUrl = await uploadMedia(newImage, '/api/upload');
        if (imageUrl == null) {
          throw Exception('فشل في رفع الصورة');
        }
      }
      final response = await http
          .put(
            Uri.parse('$kBaseUrl/api/posts/$postId'),
            headers: {
              'Authorization': 'Bearer $_token',
              'Content-Type': 'application/json',
            },
            body: jsonEncode({
              'content': newContent,
              if (imageUrl != null) 'image': imageUrl,
            }),
          )
          .timeout(const Duration(seconds: 10));
      if (response.statusCode == 200) {
        final post = _posts[postIndex];
        _posts[postIndex] = Post(
          id: post.id,
          userId: post.userId,
          content: newContent,
          mediaUrl: imageUrl ?? post.mediaUrl,
          mediaType: imageUrl != null ? 'image' : post.mediaType,
          username: post.username,
          avatar: post.avatar,
          likes: post.likes,
          loves: post.loves,
          haha: post.haha,
          comments: post.comments,
          isLiked: post.isLiked,
          isSaved: post.isSaved,
        );
        notifyListeners();
        showSnackBar('تم تعديل المنشور بنجاح', Colors.green);
      } else {
        throw Exception('فشل في تعديل المنشور: ${response.body}');
      }
    } catch (e) {
      showSnackBar('خطأ في تعديل المنشور: $e', Colors.red);
    }
  }

  // Delete a post
  Future<void> deletePost(String postId) async {
    if (!await _checkConnectivity()) {
      showSnackBar('لا يوجد اتصال بالإنترنت', Colors.red);
      return;
    }
    try {
      final response = await http.delete(
        Uri.parse('$kBaseUrl/api/posts/$postId'),
        headers: {
          'Authorization': 'Bearer $_token',
          'Content-Type': 'application/json',
        },
      ).timeout(const Duration(seconds: 10));
      if (response.statusCode == 200) {
        _posts.removeWhere((p) => p.id == postId);
        notifyListeners();
        _notifications.add(NotificationModel(
          id: const Uuid().v4(),
          message: 'تم حذف منشور',
          date: DateTime.now().toString(),
          isRead: false,
        ));
        showSnackBar('تم حذف المنشور بنجاح', Colors.green);
      } else {
        throw Exception('فشل في حذف المنشور: ${response.body}');
      }
    } catch (e) {
      showSnackBar('خطأ في حذف المنشور: $e', Colors.red);
    }
  }

  // React to a post
  Future<void> reactToPost(String postId, String reaction) async {
    if (!await _checkConnectivity()) {
      showSnackBar('لا يوجد اتصال بالإنترنت', Colors.red);
      return;
    }
    try {
      final response = await http
          .post(
            Uri.parse('$kBaseUrl/api/posts/$postId/like'),
            headers: {
              'Authorization': 'Bearer $_token',
              'Content-Type': 'application/json'
            },
            body: jsonEncode({'reaction': reaction}),
          )
          .timeout(const Duration(seconds: 10));
      if (response.statusCode == 200) {
        final postIndex = _posts.indexWhere((p) => p.id == postId);
        if (postIndex != -1) {
          final post = _posts[postIndex];
          final updatedReactions = Map<String, int>.from(post.reactions);
          String? updatedUserReaction = post.userReaction;

          if (post.userReaction != null) {
            updatedReactions[post.userReaction!] =
                (updatedReactions[post.userReaction!] ?? 0) - 1;
          }
          if (post.userReaction == reaction) {
            updatedUserReaction = null;
          } else {
            updatedReactions[reaction] = (updatedReactions[reaction] ?? 0) + 1;
            updatedUserReaction = reaction;
            _notifications.add(NotificationModel(
                message: "تمت إضافة رد فعل على منشور",
                date: DateTime.now().toString()));
          }

          _posts[postIndex] = Post(
            id: post.id,
            userId: post.userId,
            content: post.content,
            mediaUrl: post.mediaUrl,
            mediaType: post.mediaType,
            username: post.username,
            avatar: post.avatar,
            likes: updatedReactions['like'] ?? 0,
            loves: updatedReactions['love'] ?? 0,
            haha: updatedReactions['haha'] ?? 0,
            isLiked: updatedUserReaction == 'like',
            isSaved: post.isSaved,
            comments: post.comments,
          );
          notifyListeners();
        }
      } else {
        throw Exception('فشل في إضافة رد الفعل: ${response.body}');
      }
    } catch (e) {
      showSnackBar('خطأ في إضافة رد الفعل: $e', Colors.red);
    }
  }

  // Add comment to a post
  Future<void> addCommentToPost(String postId, String comment) async {
    if (!await _checkConnectivity()) {
      showSnackBar('لا يوجد اتصال بالإنترنت', Colors.red);
      return;
    }
    try {
      final response = await http
          .post(
            Uri.parse('$kBaseUrl/api/posts/$postId/comment'),
            headers: {
              'Authorization': 'Bearer $_token',
              'Content-Type': 'application/json',
            },
            body: jsonEncode({'text': comment}),
          )
          .timeout(const Duration(seconds: 10));
      if (response.statusCode == 201) {
        final postIndex = _posts.indexWhere((p) => p.id == postId);
        if (postIndex != -1) {
          final post = _posts[postIndex];
          final newComment = Comment(
            userId: _currentUser?.id ?? '',
            username: _username,
            content: comment,
            userAvatar: _profileImagePath,
          );
          final updatedComments = List<Comment>.from(post.comments)
            ..add(newComment);

          _posts[postIndex] = Post(
            id: post.id,
            userId: post.userId,
            content: post.content,
            mediaUrl: post.mediaUrl,
            mediaType: post.mediaType,
            username: post.username,
            avatar: post.avatar,
            likes: post.likes,
            loves: post.loves,
            haha: post.haha,
            isLiked: post.isLiked,
            isSaved: post.isSaved,
            comments: updatedComments,
          );
          _notifications.add(NotificationModel(
              message: "تم التعليق على منشور",
              date: DateTime.now().toString()));
          socket?.emit('newComment', {
            'postId': postId,
            'comment': {
              'username': _username,
              'content': comment,
              'date': DateTime.now().toString()
            }
          });
          notifyListeners();
          showSnackBar('تم إضافة التعليق بنجاح', Colors.green);
        }
      } else {
        throw Exception('فشل في إضافة التعليق: ${response.body}');
      }
    } catch (e) {
      showSnackBar('خطأ في إضافة التعليق: $e', Colors.red);
    }
  }

  // Fetch stories
  Future<void> fetchStories() async {
    if (!await _checkConnectivity()) {
      showSnackBar('لا يوجد اتصال بالإنترنت', Colors.red);
      return;
    }
    try {
      final response = await http.get(
        Uri.parse('$kBaseUrl/api/stories'),
        headers: {'Authorization': 'Bearer $_token'},
      ).timeout(const Duration(seconds: 10));
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        _stories = List<Map<String, dynamic>>.from(data)
            .map((story) => Story.fromJson(story))
            .toList();
        notifyListeners();
      } else {
        throw Exception('فشل في جلب القصص: ${response.body}');
      }
    } catch (e) {
      showSnackBar('خطأ في تحميل القصص: $e', Colors.red);
    }
  }

  // Add a story
  Future<void> addStory(String imagePath) async {
    if (!await _checkConnectivity()) {
      showSnackBar('لا يوجد اتصال بالإنترنت', Colors.red);
      return;
    }
    try {
      var request =
          http.MultipartRequest('POST', Uri.parse('$kBaseUrl/api/stories'));
      request.headers['Authorization'] = 'Bearer $_token';
      if (!kIsWeb) {
        request.files
            .add(await http.MultipartFile.fromPath('image', imagePath));
      }
      final response =
          await request.send().timeout(const Duration(seconds: 15));
      final responseData = await http.Response.fromStream(response);
      if (response.statusCode == 201) {
        final data = jsonDecode(responseData.body);
        _stories.insert(
          0,
          Story(
            id: data['_id'] ?? const Uuid().v4(),
            user: _username,
            image: data['image'] ?? '',
            date: data['createdAt'] ?? DateTime.now().toString(),
          ),
        );
        socket?.emit('new_story', data);
        notifyListeners();
        showSnackBar('تم إضافة القصة بنجاح', Colors.green);
      } else {
        throw Exception('فشل في إضافة القصة: ${responseData.body}');
      }
    } catch (e) {
      showSnackBar('خطأ في إضافة القصة: $e', Colors.red);
    }
  }

  // Remove a story
  void removeStory(String storyId) {
    _stories.removeWhere((story) => story.id == storyId);
    notifyListeners();
  }

  // Fetch videos with pagination and optional category
  Future<void> fetchVideos(
      {bool loadMore = false, String? category, bool refresh = false}) async {
    if (!await _checkConnectivity()) {
      showSnackBar('لا يوجد اتصال بالإنترنت', Colors.red);
      return;
    }
    if (_isLoadingMore || (!loadMore && _videos.isNotEmpty && !refresh)) return;
    try {
      _isLoadingMore = true;
      notifyListeners();
      final queryParams = {
        'page': _videoPage.toString(),
        if (category != null) 'category': category,
      };
      final uri = Uri.parse('$kBaseUrl/api/videos')
          .replace(queryParameters: queryParams);
      final response = await http.get(
        uri,
        headers: {'Authorization': 'Bearer $_token'},
      ).timeout(const Duration(seconds: 10));
      if (response.statusCode == 200) {
        final List<dynamic> videoData = jsonDecode(response.body);
        final newVideos = videoData
            .map((data) => Video.fromJson({
                  'id': data['_id'] ?? const Uuid().v4(),
                  'title': data['title'] ?? 'Untitled Video',
                  'description': data['description'] ?? '',
                  'videoUrl': data['videoUrl'].startsWith('http')
                      ? data['videoUrl']
                      : '$kBaseUrl${data['videoUrl']}',
                  'thumbnail': data['thumbnail'].startsWith('http')
                      ? data['thumbnail']
                      : '$kBaseUrl${data['thumbnail']}',
                  'isLive': data['isLive'] ?? false,
                  'reactions': {
                    'like': data['likes'] ?? 0,
                    'love': data['loves'] ?? 0,
                    'haha': data['haha'] ?? 0,
                  },
                  'userReaction': data['userReaction'],
                  'comments': (data['comments'] as List<dynamic>? ?? [])
                      .map((comment) => Comment(
                            id: comment['_id'] ?? const Uuid().v4(),
                            userId: comment['userId'] ?? '',
                            username: comment['username'] ?? 'Unknown',
                            content: comment['content'] ?? '',
                            userAvatar: comment['userAvatar'],
                            createdAt: comment['createdAt'] != null
                                ? DateTime.parse(comment['createdAt'])
                                : DateTime.now(),
                          ).toJson())
                      .toList(),
                }))
            .toList();
        if (loadMore) {
          _videos.addAll(newVideos);
        } else {
          _videos = newVideos;
        }
        _videoPage++;
        _hasMoreVideos = newVideos.isNotEmpty;
        notifyListeners();
      } else {
        throw Exception('فشل في جلب الفيديوهات: ${response.statusCode}');
      }
    } catch (e) {
      showSnackBar('خطأ في تحميل الفيديوهات: $e', Colors.red);
    } finally {
      _isLoadingMore = false;
      notifyListeners();
    }
  }

  // Load more videos with optional category
  Future<void> loadMoreVideos({String? category, bool refresh = false}) async {
    if (_hasMoreVideos && !_isLoadingMore) {
      await fetchVideos(loadMore: true, category: category, refresh: refresh);
    }
  }

  // Add a video - Only allowed for حكماء، علماء، مجتهدين
  Future<void> addVideo(String title, String videoPath) async {
    // bool isLoading = true; // Unused
    notifyListeners();

    try {
      // Check if user has the required role
      if (_currentUser == null ||
          ![
            UserRole.hokama,
            UserRole.olama,
            UserRole.mogtahdin,
          ].contains(_currentUser!.role)) {
        throw Exception(
            'غير مصرح لك بإضافة فيديوهات. يجب أن تكون من الحكماء أو العلماء أو المجتهدين');
      }

      // Create form data for file upload
      var request =
          http.MultipartRequest('POST', Uri.parse('$kBaseUrl/videos'));
      request.fields['title'] = title;
      request.fields['userId'] = _currentUser!.id;
      request.fields['addedBy'] = _currentUser!.name;
      request.fields['userRole'] = _currentUser!.role.name;

      // Add authorization header
      request.headers['Authorization'] = 'Bearer ${_currentUser!.id}';

      final response = await request.send();
      final responseData = await response.stream.bytesToString();

      if (response.statusCode == 201) {
        final videoData = jsonDecode(responseData);
        final newVideo = Video.fromJson(videoData);
        _videos.insert(0, newVideo);
        notifyListeners();
      } else {
        throw Exception('فشل رفع الفيديو: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error adding video: $e');
      rethrow;
    } finally {
      // isLoading = false; // Unused
      notifyListeners();
    }
  }

  // Toggle like on a video
  Future<void> toggleLikeVideo(String videoId, bool isLiked) async {
    if (!await _checkConnectivity()) {
      showSnackBar('لا يوجد اتصال بالإنترنت', Colors.red);
      return;
    }
    try {
      final response = await http
          .post(
            Uri.parse('$kBaseUrl/api/videos/$videoId/like'),
            headers: {
              'Authorization': 'Bearer $_token',
              'Content-Type': 'application/json'
            },
            body: jsonEncode({'isLiked': !isLiked}),
          )
          .timeout(const Duration(seconds: 10));
      if (response.statusCode == 200) {
        final videoIndex = _videos.indexWhere((v) => v.id == videoId);
        if (videoIndex != -1) {
          final video = _videos[videoIndex];
          final updatedReactions = Map<String, int>.from(video.reactions);
          String? updatedUserReaction = video.userReaction;

          if (isLiked) {
            updatedReactions['like'] = (updatedReactions['like'] ?? 0) - 1;
            updatedUserReaction = null;
          } else {
            updatedReactions['like'] = (updatedReactions['like'] ?? 0) + 1;
            updatedUserReaction = 'like';
          }

          _videos[videoIndex] = Video(
            id: video.id,
            title: video.title,
            description: video.description,
            videoUrl: video.videoUrl,
            thumbnailUrl: video.thumbnailUrl,
            isLive: video.isLive,
            reactions: updatedReactions,
            userReaction: updatedUserReaction,
            comments: video.comments,
            category: video.category, // Required category parameter
          );
          notifyListeners();
        }
      } else {
        throw Exception('فشل في تحديث الإعجاب: ${response.statusCode}');
      }
    } catch (e) {
      showSnackBar('خطأ في تحديث الإعجاب: $e', Colors.red);
    }
  }

  // Toggle like on a comment within a video
  Future<void> toggleLikeComment(
      String videoId, String commentId, String userId) async {
    if (_currentUser == null) {
      scaffoldMessengerKey.currentState?.showSnackBar(
        const SnackBar(
            content: Text('يجب تسجيل الدخول أولاً للإعجاب بالتعليقات')),
      );
      return;
    }

    final videoIndex = _videos.indexWhere((v) => v.id == videoId);
    if (videoIndex == -1) {
      // print('Video not found: $videoId');
      return;
    }

    Video targetVideo = _videos[videoIndex];
    Comment? targetComment;
    bool isReply = false;
    int parentCommentIndex = -1;
    int replyIndex = -1;

    // Find the target comment and its path if it's a reply
    for (int i = 0; i < targetVideo.comments.length; i++) {
      final c = targetVideo.comments[i];
      if (c.id == commentId) {
        targetComment = c;
        parentCommentIndex = i;
        break;
      }
      for (int j = 0; j < c.replies.length; j++) {
        final r = c.replies[j];
        if (r.id == commentId) {
          targetComment = r;
          parentCommentIndex = i; // Parent of the reply
          replyIndex = j; // Index of the reply itself
          isReply = true;
          break;
        }
      }
      if (targetComment != null) break;
    }

    if (targetComment == null) {
      // print('Comment not found: $commentId in video $videoId');
      return;
    }

    final bool currentlyLiked = targetComment.likedBy.contains(userId);
    List<String> updatedLikedBy = List.from(targetComment.likedBy);
    int updatedLikesCount = targetComment.likesCount;

    if (currentlyLiked) {
      updatedLikedBy.remove(userId);
      updatedLikesCount--;
    } else {
      updatedLikedBy.add(userId);
      updatedLikesCount++;
    }
    if (updatedLikesCount < 0)
      updatedLikesCount = 0; // Ensure likes don't go negative

    // Create the updated comment object
    final updatedComment = Comment(
      id: targetComment.id,
      userId: targetComment.userId,
      username: targetComment.username,
      content: targetComment.content,
      createdAt: targetComment.createdAt,
      userAvatar: targetComment.userAvatar,
      likesCount: updatedLikesCount,
      likedBy: updatedLikedBy,
      replies: targetComment.replies,
      parentCommentId: targetComment.parentCommentId,
    );

    // Update the comment in the local list
    if (isReply && parentCommentIndex != -1 && replyIndex != -1) {
      targetVideo.comments[parentCommentIndex].replies[replyIndex] =
          updatedComment;
    } else if (!isReply && parentCommentIndex != -1) {
      targetVideo.comments[parentCommentIndex] = updatedComment;
    }

    _videos[videoIndex] = targetVideo.copyWith(
        comments: List.from(targetVideo
            .comments)); // Ensure change notification for Video object
    notifyListeners();

    try {
      // API Call: Replace with your actual backend endpoint and logic
      final response = await http.post(
        Uri.parse(
            '$_backendBaseUrl/videos/$videoId/comments/$commentId/like'), // Placeholder endpoint
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $_token',
        },
        body: jsonEncode({
          'userId': userId,
          'liked': !currentlyLiked, // Send the new like status
        }),
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        // print('Comment like status updated successfully on the server.');
        // Optionally, re-fetch the video or comment to ensure sync, or trust local update
      } else {
        // print('Failed to update comment like status on server: ${response.body}');
        // Revert local changes if server update fails
        if (currentlyLiked) {
          updatedLikedBy.add(userId);
          updatedLikesCount++;
        } else {
          updatedLikedBy.remove(userId);
          updatedLikesCount--;
        }
        final revertedComment = Comment(
            id: targetComment.id,
            userId: targetComment.userId,
            username: targetComment.username,
            content: targetComment.content,
            createdAt: targetComment.createdAt,
            userAvatar: targetComment.userAvatar,
            likesCount: updatedLikesCount,
            likedBy: updatedLikedBy,
            replies: targetComment.replies,
            parentCommentId: targetComment.parentCommentId);

        if (isReply && parentCommentIndex != -1 && replyIndex != -1) {
          targetVideo.comments[parentCommentIndex].replies[replyIndex] =
              revertedComment;
        } else if (!isReply && parentCommentIndex != -1) {
          targetVideo.comments[parentCommentIndex] = revertedComment;
        }
        _videos[videoIndex] =
            targetVideo.copyWith(comments: List.from(targetVideo.comments));
        notifyListeners();
        scaffoldMessengerKey.currentState?.showSnackBar(
          SnackBar(content: Text('فشل تحديث الإعجاب: ${response.body}')),
        );
      }
    } catch (e) {
      // print('Error toggling comment like: $e');
      // Revert local changes on error
      if (currentlyLiked) {
        updatedLikedBy.add(userId);
        updatedLikesCount++;
      } else {
        updatedLikedBy.remove(userId);
        updatedLikesCount--;
      }
      final revertedComment = Comment(
          id: targetComment.id,
          userId: targetComment.userId,
          username: targetComment.username,
          content: targetComment.content,
          createdAt: targetComment.createdAt,
          userAvatar: targetComment.userAvatar,
          likesCount: updatedLikesCount,
          likedBy: updatedLikedBy,
          replies: targetComment.replies,
          parentCommentId: targetComment.parentCommentId);

      if (isReply && parentCommentIndex != -1 && replyIndex != -1) {
        targetVideo.comments[parentCommentIndex].replies[replyIndex] =
            revertedComment;
      } else if (!isReply && parentCommentIndex != -1) {
        targetVideo.comments[parentCommentIndex] = revertedComment;
      }
      _videos[videoIndex] =
          targetVideo.copyWith(comments: List.from(targetVideo.comments));
      notifyListeners();
      scaffoldMessengerKey.currentState?.showSnackBar(
        SnackBar(content: Text('خطأ في تحديث الإعجاب: $e')),
      );
    }
  }

  // Add comment to a video
  Future<void> addCommentToVideo(String videoId, String content) async {
    if (!await _checkConnectivity()) {
      showSnackBar('لا يوجد اتصال بالإنترنت', Colors.red);
      return;
    }
    try {
      final response = await http
          .post(
            Uri.parse('$kBaseUrl/api/videos/$videoId/comment'),
            headers: {
              'Authorization': 'Bearer $_token',
              'Content-Type': 'application/json'
            },
            body: jsonEncode({'content': content}),
          )
          .timeout(const Duration(seconds: 10));
      if (response.statusCode == 201) {
        final videoIndex = _videos.indexWhere((v) => v.id == videoId);
        if (videoIndex != -1) {
          final video = _videos[videoIndex];
          final updatedComments = List<Comment>.from(video.comments)
            ..add(Comment(
              id: const Uuid().v4(),
              userId: _currentUser?.id ?? '',
              username: _username,
              content: content,
              createdAt: DateTime.now(),
              userAvatar: _currentUser?.avatarUrl,
            ));
          _videos[videoIndex] = Video(
            id: video.id,
            title: video.title,
            description: video.description,
            videoUrl: video.videoUrl,
            thumbnailUrl: video.thumbnailUrl,
            isLive: video.isLive,
            reactions: video.reactions,
            userReaction: video.userReaction,
            comments: updatedComments,
            category: video.category, // Add required category parameter
          );
          notifyListeners();
          showSnackBar('تم إضافة التعليق بنجاح', Colors.green);
        }
      } else {
        throw Exception('فشل في إضافة التعليق: ${response.statusCode}');
      }
    } catch (e) {
      showSnackBar('خطأ في إضافة التعليق: $e', Colors.red);
    }
  }

  Future<void> addReplyToComment({
    required String videoId,
    required String commentId,
    required String content,
    required String
        userId, // This 'userId' is passed from the UI, usually _currentUser.id
  }) async {
    if (_currentUser == null) {
      // Consider showing a message to the user via ScaffoldMessenger if appropriate
      // For example:
      // scaffoldMessengerKey.currentState?.showSnackBar(
      //   const SnackBar(content: Text('يجب تسجيل الدخول للرد')),
      // );
      throw Exception('User not logged in. Cannot add reply.');
    }

    // Optional: Validate userId against _currentUser.id if necessary
    // if (userId != _currentUser!.id) {
    //   print('Warning: userId parameter ($userId) does not match _currentUser.id (${_currentUser!.id})');
    //   // Decide on handling: throw error, or proceed using _currentUser.id
    // }

    try {
      final response = await http.post(
        // Ensure this URI is correct based on your backend API
        Uri.parse(
            '$_backendBaseUrl/videos/$videoId/comments/$commentId/replies'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization':
              'Bearer $_token', // Ensure _token is managed and valid
        },
        body: jsonEncode({
          'content': content,
          'userId': _currentUser!.id, // Always use the app's current user
          'username': _currentUser!.name, // Use AppState's current user's name
          // 'timestamp': DateTime.now().toIso8601String(), // Timestamp usually set by backend
        }),
      );

      if (response.statusCode == 201) {
        // HTTP 201 Created
        final newReplyJson = jsonDecode(response.body);
        final newReply = Comment.fromJson(
            newReplyJson); // Assumes Comment.fromJson handles reply structure

        final video = videos.firstWhere(
          (v) => v.id == videoId,
          orElse: () => throw Exception(
              'Video not found (id: $videoId) when adding reply.'),
        );

        final parentComment = video.comments.firstWhere(
          (c) => c.id == commentId,
          orElse: () => throw Exception(
              'Parent comment not found (id: $commentId) when adding reply.'),
        );

        // Ensure replies list is initialized and mutable.
        // This depends on how Comment.replies is defined in comment_model.dart.
        // If it's `List<Comment> replies = const []` or `final List<Comment> replies` initialized with const/unmodifiable list,
        // this will need adjustment (e.g., re-assigning the comment or video object).
        // Assuming it's `List<Comment>? replies;` or `List<Comment> replies = [];`
        // parentComment.replies ??= []; // This line was incorrect as 'replies' is final.
        // The Comment.fromJson factory should ensure 'replies' is a mutable list.
        parentComment.replies.add(newReply);

        notifyListeners();
      } else {
        // Log detailed error information from the server
        if (kDebugMode) {
          print(
              'Failed to add reply: ${response.statusCode}, Body: ${response.body}');
        }
        throw Exception(
            'Failed to add reply: Server responded with ${response.statusCode}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Exception caught in addReplyToComment: $e');
      }
      // Rethrow or handle more gracefully, e.g., show a user-friendly message
      throw Exception('An error occurred while adding your reply: $e');
    }
  }

  // Remove a video
  void removeVideo(String videoId) {
    _videos.removeWhere((video) => video.id == videoId);
    notifyListeners();
  }

  // Add reaction to a video
  void addReactionToVideo(String videoId, String reactionType) {
    final videoIndex = _videos.indexWhere((video) => video.id == videoId);
    if (videoIndex != -1) {
      final video = _videos[videoIndex];
      final updatedReactions = Map<String, int>.from(video.reactions);
      String? updatedUserReaction = video.userReaction;

      if (video.userReaction != null) {
        updatedReactions[video.userReaction!] =
            (updatedReactions[video.userReaction!] ?? 0) - 1;
      }
      updatedUserReaction = reactionType;
      updatedReactions[reactionType] =
          (updatedReactions[reactionType] ?? 0) + 1;

      _videos[videoIndex] = Video(
        id: video.id,
        title: video.title,
        description: video.description,
        videoUrl: video.videoUrl,
        thumbnailUrl: video.thumbnailUrl,
        isLive: video.isLive,
        reactions: updatedReactions,
        userReaction: updatedUserReaction,
        comments: video.comments,
        category: video.category, // Add required category parameter
      );
      notifyListeners();
    }
  }

  // Fetch groups
  Future<void> fetchGroups({bool loadMore = false}) async {
    if (!await _checkConnectivity()) {
      showSnackBar('لا يوجد اتصال بالإنترنت', Colors.red);
      return;
    }
    try {
      _isLoadingMore = loadMore;
      notifyListeners();
      final response = await http.get(
        Uri.parse('$kBaseUrl/api/groups'),
        headers: {'x-auth-token': _token ?? ''},
      ).timeout(const Duration(seconds: 10));
      if (response.statusCode == 200) {
        final List<dynamic> groupData = jsonDecode(response.body);
        if (loadMore) {
          _groups.addAll(
              groupData.map((data) => TaskGroup.fromJson(data)).toList());
        } else {
          _groups = groupData.map((data) => TaskGroup.fromJson(data)).toList();
        }
        notifyListeners();
      } else {
        throw Exception('فشل في جلب المجموعات: ${response.statusCode}');
      }
    } catch (e) {
      showSnackBar('خطأ في جلب المجموعات: $e', Colors.red);
    } finally {
      _isLoadingMore = false;
      notifyListeners();
    }
  }

// Fetch leaderboard data
  Future<List<Map<String, dynamic>>> getLeaderboard() async {
    if (!await _checkConnectivity()) {
      showSnackBar(
          'لا يوجد اتصال بالإنترنت، استخدام البيانات المحلية', Colors.orange);
      return _getFallbackLeaderboard();
    }
    try {
      final response = await http.get(
        Uri.parse('$kBaseUrl/api/leaderboard'),
        headers: {'Authorization': 'Bearer $_token'},
      ).timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body) as List<dynamic>;
        return data.cast<Map<String, dynamic>>();
      } else {
        throw Exception('فشل في جلب لوحة المتصدرين: ${response.body}');
      }
    } catch (e) {
      showSnackBar(
          'خطأ في جلب لوحة المتصدرين، استخدام البيانات المحلية', Colors.orange);
      return _getFallbackLeaderboard();
    }
  }

  // Get local leaderboard data when offline
  List<Map<String, dynamic>> _getFallbackLeaderboard() {
    final sortedLeaderboard = List<Map<String, dynamic>>.from(_leaderboard);
    sortedLeaderboard
        .sort((a, b) => (b['points'] as int).compareTo(a['points'] as int));
    return sortedLeaderboard;
  }

  // Add a group
  Future<void> addGroup(TaskGroup group) async {
    if (!await _checkConnectivity()) {
      showSnackBar('لا يوجد اتصال بالإنترنت', Colors.red);
      return;
    }
    try {
      var request =
          http.MultipartRequest('POST', Uri.parse('$kBaseUrl/api/groups'));
      request.headers['x-auth-token'] = _token ?? '';
      request.fields['name'] = group.name;
      request.fields['description'] = group.description;
      request.fields['videoUrl'] = group.videoUrl;
      request.fields['type'] = group.type.name;
      request.fields['points'] = group.points.toString();
      request.fields['members'] =
          jsonEncode(group.members.map((m) => m.toJson()).toList());
      if (!kIsWeb) {
        for (var media in group.media) {
          if (!media['path']!.startsWith('http')) {
            request.files.add(
                await http.MultipartFile.fromPath('media', media['path']!));
          }
        }
      }
      final response =
          await request.send().timeout(const Duration(seconds: 15));
      if (response.statusCode == 201) {
        await fetchGroups();
        showSnackBar('تم إضافة المجموعة بنجاح', Colors.green);
      } else {
        final responseData = await http.Response.fromStream(response);
        throw Exception('فشل في إضافة المجموعة: ${responseData.body}');
      }
    } catch (e) {
      showSnackBar('خطأ في إضافة المجموعة: $e', Colors.red);
    }
  }

  // Update group
  Future<void> updateGroup(String groupId,
      {String? name, String? description, List<File>? newMedia}) async {
    if (!await _checkConnectivity()) {
      showSnackBar('لا يوجد اتصال بالإنترنت', Colors.red);
      return;
    }
    try {
      var request = http.MultipartRequest(
          'PUT', Uri.parse('$kBaseUrl/api/groups/$groupId'));
      request.headers['Authorization'] = 'Bearer $_token';
      if (name != null) request.fields['name'] = name;
      if (description != null) request.fields['description'] = description;
      if (newMedia != null && !kIsWeb) {
        for (var media in newMedia) {
          request.files
              .add(await http.MultipartFile.fromPath('media', media.path));
        }
      }
      final response =
          await request.send().timeout(const Duration(seconds: 15));
      final responseData = await http.Response.fromStream(response);
      if (response.statusCode == 200) {
        final data = jsonDecode(responseData.body);
        final groupIndex = _groups.indexWhere((g) => g.id == groupId);
        if (groupIndex != -1) {
          final updatedMedia = data['media'] != null
              ? List<Map<String, String>>.from(data['media'])
                  .map((m) => {'path': m['url']!, 'type': m['type']!})
                  .toList()
              : _groups[groupIndex].media;
          _groups[groupIndex] = TaskGroup(
            id: _groups[groupIndex].id,
            name: name ?? _groups[groupIndex].name,
            description: description ?? _groups[groupIndex].description,
            videoUrl: _groups[groupIndex].videoUrl,
            members: _groups[groupIndex].members,
            type: _groups[groupIndex].type,
            points: _groups[groupIndex].points,
            media: updatedMedia,
            tasksByUser: _groups[groupIndex].tasksByUser,
          );
          socket?.emit('group_updated', data);
          notifyListeners();
          showSnackBar('تم تحديث المجموعة بنجاح', Colors.green);
        }
      } else {
        throw Exception('فشل في تحديث المجموعة: ${responseData.body}');
      }
    } catch (e) {
      showSnackBar('خطأ في تحديث المجموعة: $e', Colors.red);
    }
  }

  // Join a group
  Future<void> joinGroup(String groupId) async {
    if (!await _checkConnectivity()) {
      showSnackBar('لا يوجد اتصال بالإنترنت', Colors.red);
      return;
    }
    try {
      final response = await http.post(
        Uri.parse('$kBaseUrl/api/groups/$groupId/join'),
        headers: {'x-auth-token': _token ?? ''},
      ).timeout(const Duration(seconds: 10));
      if (response.statusCode == 200) {
        await fetchGroups();
        showSnackBar('تم الانضمام إلى المجموعة بنجاح', Colors.green);
      } else {
        throw Exception('فشل الانضمام إلى المجموعة: ${response.body}');
      }
    } catch (e) {
      showSnackBar('خطأ في الانضمام إلى المجموعة: $e', Colors.red);
    }
  }

  // Leave a group
  Future<void> leaveGroup(String groupId) async {
    if (!await _checkConnectivity()) {
      showSnackBar('لا يوجد اتصال بالإنترنت', Colors.red);
      return;
    }
    try {
      final response = await http.delete(
        Uri.parse('$kBaseUrl/api/groups/$groupId/leave'),
        headers: {'x-auth-token': _token ?? ''},
      ).timeout(const Duration(seconds: 10));
      if (response.statusCode == 200) {
        _groups.removeWhere((group) => group.id == groupId);
        notifyListeners();
        showSnackBar('تم مغادرة المجموعة بنجاح', Colors.green);
      } else {
        throw Exception('فشل مغادرة المجموعة: ${response.body}');
      }
    } catch (e) {
      showSnackBar('خطأ في مغادرة المجموعة: $e', Colors.red);
    }
  }

  // Remove a group
  Future<void> removeGroup(String groupId) async {
    if (!await _checkConnectivity()) {
      showSnackBar('لا يوجد اتصال بالإنترنت', Colors.red);
      return;
    }
    try {
      final response = await http.delete(
        Uri.parse('$kBaseUrl/api/groups/$groupId'),
        headers: {'x-auth-token': _token ?? ''},
      ).timeout(const Duration(seconds: 10));
      if (response.statusCode == 200) {
        _groups.removeWhere((group) => group.id == groupId);
        notifyListeners();
        showSnackBar('تم حذف المجموعة بنجاح', Colors.green);
      } else {
        throw Exception('فشل حذف المجموعة: ${response.body}');
      }
    } catch (e) {
      showSnackBar('خطأ في حذف المجموعة: $e', Colors.red);
    }
  }

  // Complete a task
  Future<void> completeTask(
      String groupId, GroupTask task, String mediaPath) async {
    final groupIndex = _groups.indexWhere((g) => g.id == groupId);
    if (groupIndex == -1) return;
    final updatedTasksByUser =
        Map<String, List<GroupTask>>.from(_groups[groupIndex].tasksByUser);
    final userTasks = updatedTasksByUser[task.userId] ?? [];
    final taskIndex = userTasks.indexWhere((t) => t.id == task.id);
    if (taskIndex != -1) {
      userTasks[taskIndex] = GroupTask(
        id: task.id,
        title: task.title,
        groupId: task.groupId,
        userId: task.userId,
        category: task.category,
        points: task.points,
        startTime: task.startTime,
        duration: task.duration,
        isCompleted: true,
        completionMediaPath: mediaPath,
      );
      updatedTasksByUser[task.userId] = userTasks;
      _groups[groupIndex] = TaskGroup(
        id: _groups[groupIndex].id,
        name: _groups[groupIndex].name,
        description: _groups[groupIndex].description,
        videoUrl: _groups[groupIndex].videoUrl,
        members: _groups[groupIndex].members,
        type: _groups[groupIndex].type,
        points: _groups[groupIndex].points,
        media: _groups[groupIndex].media,
        tasksByUser: updatedTasksByUser,
      );
      notifyListeners();
    }
  }

  // Contribute to a task in a group
  Future<void> contributeToTask(
      String groupId, GroupTask task, String userId) async {
    if (!await _checkConnectivity()) {
      showSnackBar('لا يوجد اتصال بالإنترنت', Colors.red);
      return;
    }
    try {
      final response = await http
          .post(
            Uri.parse('$kBaseUrl/api/tasks/$groupId/contribute'),
            headers: {
              'Authorization': 'Bearer $_token',
              'Content-Type': 'application/json',
            },
            body: jsonEncode({
              'taskId': task.id,
              'userId': userId,
            }),
          )
          .timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        // Update local task state in _groups
        final groupIndex = _groups.indexWhere((g) => g.id == groupId);
        if (groupIndex != -1 &&
            _groups[groupIndex].tasksByUser.containsKey(userId)) {
          final taskIndex = _groups[groupIndex]
              .tasksByUser[userId]!
              .indexWhere((t) => t.id == task.id);
          if (taskIndex != -1) {
            _groups[groupIndex].tasksByUser[userId]![taskIndex] = task.copyWith(
              isCompleted: true,
              collaborators: [
                ...task.collaborators,
                userId
              ], // Add user to collaborators
            );
            notifyListeners();
            showSnackBar('تم المساهمة في المهمة بنجاح', Colors.green);
          }
        }
      } else {
        throw Exception('فشل في المساهمة في المهمة: ${response.body}');
      }
    } catch (e) {
      showSnackBar('خطأ في المساهمة في المهمة: $e', Colors.red);
    }
  }

  // Add member to a group
  void addMemberToGroup(String groupId, UserModel user) {
    final groupIndex = _groups.indexWhere((group) => group.id == groupId);
    if (groupIndex != -1) {
      _groups[groupIndex].members.add(user);
      notifyListeners();
    }
  }

  // Remove member from a group
  void removeMemberFromGroup(String groupId, String userId) {
    final groupIndex = _groups.indexWhere((group) => group.id == groupId);
    if (groupIndex != -1) {
      _groups[groupIndex].members.removeWhere((member) => member.id == userId);
      notifyListeners();
    }
  }

  // Add task to a group
  void addTaskToGroup(String groupId, String userId, GroupTask task) {
    final groupIndex = _groups.indexWhere((group) => group.id == groupId);
    if (groupIndex != -1) {
      if (!_groups[groupIndex].tasksByUser.containsKey(userId)) {
        _groups[groupIndex].tasksByUser[userId] = [];
      }
      _groups[groupIndex].tasksByUser[userId]!.add(task);
      notifyListeners();
    }
  }

  // Update group task status
  void updateGroupTaskStatus(
      String groupId, String userId, String taskId, bool isCompleted,
      {String? completionMediaPath}) {
    final groupIndex = _groups.indexWhere((group) => group.id == groupId);
    if (groupIndex != -1 &&
        _groups[groupIndex].tasksByUser.containsKey(userId)) {
      final taskIndex = _groups[groupIndex]
          .tasksByUser[userId]!
          .indexWhere((task) => task.id == taskId);
      if (taskIndex != -1) {
        final updatedTask = GroupTask(
          id: _groups[groupIndex].tasksByUser[userId]![taskIndex].id,
          title: _groups[groupIndex].tasksByUser[userId]![taskIndex].title,
          groupId: _groups[groupIndex].tasksByUser[userId]![taskIndex].groupId,
          userId: _groups[groupIndex].tasksByUser[userId]![taskIndex].userId,
          category:
              _groups[groupIndex].tasksByUser[userId]![taskIndex].category,
          points: _groups[groupIndex].tasksByUser[userId]![taskIndex].points,
          startTime:
              _groups[groupIndex].tasksByUser[userId]![taskIndex].startTime,
          duration:
              _groups[groupIndex].tasksByUser[userId]![taskIndex].duration,
          isCompleted: isCompleted,
          completionMediaPath: completionMediaPath ??
              _groups[groupIndex]
                  .tasksByUser[userId]![taskIndex]
                  .completionMediaPath,
        );
        _groups[groupIndex].tasksByUser[userId]![taskIndex] = updatedTask;
        notifyListeners();
      }
    }
  }

  // Fetch medical records
  Future<void> fetchMedicalRecords() async {
    if (!await _checkConnectivity()) {
      showSnackBar('لا يوجد اتصال بالإنترنت', Colors.red);
      return;
    }
    try {
      final response = await http.get(
        Uri.parse('$kBaseUrl/api/medicalRecords'),
        headers: {'Authorization': 'Bearer $_token'},
      ).timeout(const Duration(seconds: 10));
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        _medicalRecords = {};
        for (var record in data) {
          _medicalRecords.putIfAbsent(record['patientUsername'], () => []);
          _medicalRecords[record['patientUsername']]!
              .add(MedicalRecord.fromJson(record));
        }
        notifyListeners();
      } else {
        throw Exception('فشل في جلب السجلات الطبية: ${response.body}');
      }
    } catch (e) {
      showSnackBar('خطأ في تحميل السجلات الطبية: $e', Colors.red);
    }
  }

  // Add medical record
  Future<void> addMedicalRecord(
      String patientUsername, String filePath, String fileType) async {
    if (_userType != "medical" || !await _checkConnectivity()) {
      showSnackBar('لا يوجد اتصال بالإنترنت أو لست طبيبًا', Colors.red);
      return;
    }
    try {
      var request = http.MultipartRequest(
          'POST', Uri.parse('$kBaseUrl/api/medicalRecords'));
      request.headers['Authorization'] = 'Bearer $_token';
      request.fields['patientUsername'] = patientUsername;
      request.fields['fileType'] = fileType;
      if (!kIsWeb) {
        request.files.add(await http.MultipartFile.fromPath('file', filePath));
      }
      final response =
          await request.send().timeout(const Duration(seconds: 15));
      final responseData = await http.Response.fromStream(response);
      if (response.statusCode == 201) {
        final data = jsonDecode(responseData.body);
        final newRecord = MedicalRecord.fromJson(data);
        _medicalRecords.putIfAbsent(patientUsername, () => []);
        _medicalRecords[patientUsername]!.add(newRecord);
        _notifications.add(NotificationModel(
          message: 'تم إضافة سجل طبي جديد لـ $patientUsername',
          date: DateTime.now().toIso8601String(), // Fixed DateTime formatting
        ));
        notifyListeners();
        showSnackBar('تم إضافة السجل الطبي بنجاح', Colors.green);
      } else {
        throw Exception('فشل في إضافة السجل الطبي: ${responseData.body}');
      }
    } catch (e) {
      showSnackBar('خطأ في إضافة السجل الطبي: $e', Colors.red);
    }
  }

  // Update medical record
  Future<void> updateMedicalRecord(String recordId, String patientUsername,
      String? filePath, String? fileType) async {
    if (_userType != "medical" || !await _checkConnectivity()) {
      showSnackBar('لا يوجد اتصال بالإنترنت أو لست طبيبًا', Colors.red);
      return;
    }
    try {
      var request = http.MultipartRequest(
          'PUT', Uri.parse('$kBaseUrl/api/medicalRecords/$recordId'));
      request.headers['Authorization'] = 'Bearer $_token';
      request.fields['patientUsername'] = patientUsername;
      if (fileType != null) request.fields['fileType'] = fileType;
      if (filePath != null && !kIsWeb) {
        request.files.add(await http.MultipartFile.fromPath('file', filePath));
      }
      final response =
          await request.send().timeout(const Duration(seconds: 15));
      final responseData = await http.Response.fromStream(response);
      if (response.statusCode == 200) {
        final data = jsonDecode(responseData.body);
        final updatedRecord = MedicalRecord.fromJson(data);
        final records = _medicalRecords[patientUsername];
        if (records != null) {
          final index = records.indexWhere((r) => r.id == recordId);
          if (index != -1) {
            records[index] = updatedRecord;
            _notifications.add(NotificationModel(
              message: 'تم تحديث السجل الطبي لـ $patientUsername',
              date:
                  DateTime.now().toIso8601String(), // Fixed DateTime formatting
            ));
            notifyListeners();
            showSnackBar('تم تحديث السجل الطبي بنجاح', Colors.green);
          }
        }
      } else {
        throw Exception('فشل في تحديث السجل الطبي: ${responseData.body}');
      }
    } catch (e) {
      showSnackBar('خطأ في تحديث السجل الطبي: $e', Colors.red);
    }
  }

  // Delete medical record
  Future<void> deleteMedicalRecord(
      String recordId, String patientUsername) async {
    if (_userType != "medical" || !await _checkConnectivity()) {
      showSnackBar('لا يوجد اتصال بالإنترنت أو لست طبيبًا', Colors.red);
      return;
    }
    try {
      final response = await http.delete(
        Uri.parse('$kBaseUrl/api/medicalRecords/$recordId'),
        headers: {'Authorization': 'Bearer $_token'},
      ).timeout(const Duration(seconds: 10));
      if (response.statusCode == 200) {
        final records = _medicalRecords[patientUsername];
        if (records != null) {
          records.removeWhere((r) => r.id == recordId);
          if (records.isEmpty) {
            _medicalRecords.remove(patientUsername);
          }
          _notifications.add(NotificationModel(
            message: 'تم حذف السجل الطبي لـ $patientUsername',
            date: DateTime.now().toIso8601String(),
          ));
          notifyListeners();
          showSnackBar('تم حذف السجل الطبي بنجاح', Colors.green);
        }
      } else {
        throw Exception('فشل في حذف السجل الطبي: ${response.body}');
      }
    } catch (e) {
      showSnackBar('خطأ في حذف السجل الطبي: $e', Colors.red);
    }
  }

  // Dispose resources
  @override
  void dispose() {
    disconnectSocket();
    super.dispose();
  }

  // Helper method to reset pagination
  void resetPagination() {
    _postPage = 1;
    _videoPage = 1;
    // _hasMorePosts = true;
    _hasMoreVideos = true;
    _posts.clear();
    _videos.clear();
    notifyListeners();
  }

  // Refresh all data
  Future<void> refreshData() async {
    resetPagination();
    await Future.wait([
      fetchVideos(),
      fetchGroups(),
      fetchPosts(),
      fetchStories(),
      fetchProfessions(),
      fetchContents(),
      if (_userType == "medical") fetchMedicalRecords(),
      _loadTasks(),
    ]);
    notifyListeners();
  }

  // Update user profile
  Future<void> updateUserProfile(
      {String? username, String? email, File? avatar}) async {
    if (!await _checkConnectivity()) {
      showSnackBar('لا يوجد اتصال بالإنترنت', Colors.red);
      return;
    }
    try {
      var request = http.MultipartRequest(
          'PUT', Uri.parse('$kBaseUrl/api/users/$_userId'));
      request.headers['Authorization'] = 'Bearer $_token';
      if (username != null) request.fields['username'] = username;
      if (email != null) request.fields['email'] = email;
      if (avatar != null && !kIsWeb) {
        request.files
            .add(await http.MultipartFile.fromPath('avatar', avatar.path));
      }
      final response =
          await request.send().timeout(const Duration(seconds: 15));
      final responseData = await http.Response.fromStream(response);
      if (response.statusCode == 200) {
        final data = jsonDecode(responseData.body);
        if (username != null) {
          _username = username;
          _currentUser = _currentUser?.copyWith(name: username);
        }
        if (data['avatarUrl'] != null) {
          _profileImagePath = data['avatarUrl'];
          _currentUser = _currentUser?.copyWith(avatarUrl: _profileImagePath!);
        }
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('username', _username);
        notifyListeners();
        showSnackBar('تم تحديث الملف الشخصي بنجاح', Colors.green);
      } else {
        throw Exception('فشل في تحديث الملف الشخصي: ${responseData.body}');
      }
    } catch (e) {
      showSnackBar('خطأ في تحديث الملف الشخصي: $e', Colors.red);
    }
  }

  // Fetch user profile
  Future<void> fetchUserProfile() async {
    if (!await _checkConnectivity()) {
      showSnackBar('لا يوجد اتصال بالإنترنت', Colors.red);
      return;
    }
    try {
      final response = await http.get(
        Uri.parse('$kBaseUrl/api/users/$_userId'),
        headers: {'Authorization': 'Bearer $_token'},
      ).timeout(const Duration(seconds: 10));
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        _username = data['username'] ?? _username;
        _profileImagePath = data['avatarUrl'];
        _currentUser = UserModel(
          id: _userId!,
          name: _username,
          email: data['email'] ?? '$<EMAIL>',
          avatarUrl: _profileImagePath ?? '',
          role: UserRole.values.firstWhere(
            (role) =>
                role.toString().split('.').last == (data['role'] ?? 'member'),
            orElse: () => UserRole.member,
          ),
        );
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('username', _username);
        notifyListeners();
      } else {
        throw Exception('فشل في جلب الملف الشخصي: ${response.body}');
      }
    } catch (e) {
      showSnackBar('خطأ في جلب الملف الشخصي: $e', Colors.red);
    }
  }

  // Clear notifications
  void clearNotifications() {
    _notifications.clear();
    notifyListeners();
  }

  // Mark notification as read
  void markNotificationAsRead(String notificationId) {
    final index = _notifications.indexWhere((n) => n.id == notificationId);
    if (index != -1) {
      _notifications[index] = NotificationModel(
        id: _notifications[index].id,
        message: _notifications[index].message,
        date: _notifications[index].date,
        isRead: true,
      );
      notifyListeners();
    }
  }

  // Handle incoming chat message
  void handleChatMessage(String senderId, String message) {
    _notifications.add(NotificationModel(
      message: 'رسالة جديدة من $senderId: $message',
      date: DateTime.now().toIso8601String(),
    ));
    notifyListeners();
  }

  // Send chat message
  Future<void> sendChatMessage(String recipientId, String message) async {
    if (!await _checkConnectivity()) {
      showSnackBar('لا يوجد اتصال بالإنترنت', Colors.red);
      return;
    }
    try {
      final response = await http
          .post(
            Uri.parse('$kBaseUrl/api/messages'),
            headers: {
              'Authorization': 'Bearer $_token',
              'Content-Type': 'application/json',
            },
            body: jsonEncode({
              'recipientId': recipientId,
              'message': message,
            }),
          )
          .timeout(const Duration(seconds: 10));

      if (response.statusCode == 201) {
        socket?.emit('chat_message_$recipientId', {
          'senderId': _userId,
          'message': message,
          'date': DateTime.now().toIso8601String(),
        });
        showSnackBar('تم إرسال الرسالة بنجاح', Colors.green);
      } else {
        throw Exception('فشل في إرسال الرسالة: ${response.body}');
      }
    } catch (e) {
      showSnackBar('خطأ في إرسال الرسالة: $e', Colors.red);
    }
  }

  // Fetch group messages
  Future<List<Map<String, dynamic>>> fetchGroupMessages(String groupId) async {
    if (!await _checkConnectivity()) {
      showSnackBar('لا يوجد اتصال بالإنترنت', Colors.red);
      return [];
    }
    try {
      final response = await http.get(
        Uri.parse('$kBaseUrl/api/groupmessages/$groupId'),
        headers: {'Authorization': 'Bearer $_token'},
      ).timeout(const Duration(seconds: 10));
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['success'] == true && data['data'] != null) {
          return List<Map<String, dynamic>>.from(data['data']);
        } else {
          throw Exception('فشل في جلب رسائل المجموعة: ${response.body}');
        }
      } else {
        throw Exception('فشل في جلب رسائل المجموعة: ${response.body}');
      }
    } catch (e) {
      showSnackBar('خطأ في جلب رسائل المجموعة: $e', Colors.red);
      return [];
    }
  }

  // Poll Comments End

  // Send group message
  Future<bool> sendGroupMessage(
    String groupId, {
    required String content,
    String type = 'text',
    String? attachmentUrl,
  }) async {
    if (!await _checkConnectivity()) {
      showSnackBar('لا يوجد اتصال بالإنترنت', Colors.red);
      return false;
    }
    try {
      final response = await http
          .post(
            Uri.parse('$kBaseUrl/api/groupmessages/$groupId'),
            headers: {
              'Authorization': 'Bearer $_token',
              'Content-Type': 'application/json',
            },
            body: jsonEncode({
              'content': content,
              'type': type,
              'attachmentUrl': attachmentUrl,
            }),
          )
          .timeout(const Duration(seconds: 10));
      if (response.statusCode == 201) {
        showSnackBar('تم إرسال الرسالة بنجاح', Colors.green);
        return true;
      } else {
        throw Exception('فشل في إرسال رسالة المجموعة: ${response.body}');
      }
    } catch (e) {
      showSnackBar('خطأ في إرسال رسالة المجموعة: $e', Colors.red);
      return false;
    }
  }

  // User points and badges storage
  final Map<String, int> _userPoints = {};
  final Map<String, List<String>> _userBadges = {};
  final Set<String> _favorites = {};
  List<UserModel>? cachedFriends;

  Set<String> get favorites => _favorites;

  void addFavorite(String id) {
    if (!_favorites.contains(id)) {
      _favorites.add(id);
      notifyListeners();
    }
  }

  void removeFavorite(String id) {
    _favorites.remove(id);
    notifyListeners();
  }

  bool isFavorite(String id) {
    return _favorites.contains(id);
  }

  void toggleFavorite(String id) {
    if (_favorites.contains(id)) {
      removeFavorite(id);
    } else {
      addFavorite(id);
    }
  }

  void clearFavorites() {
    _favorites.clear();
    notifyListeners();
  }

  // Save favorites to SharedPreferences
  Future<void> saveFavorites() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setStringList('favorites', _favorites.toList());
  }

  // Load favorites from SharedPreferences
  Future<void> loadFavorites() async {
    final prefs = await SharedPreferences.getInstance();
    final List<String>? savedFavorites = prefs.getStringList('favorites');
    if (savedFavorites != null) {
      _favorites.addAll(savedFavorites);
      notifyListeners();
    }
  }

  // Initialize favorites when app starts
  Future<void> initFavorites() async {
    await loadFavorites();
  }

  // Save favorites when app closes
  Future<void> saveFavoritesOnDispose() async {
    await saveFavorites();
  }

  // Sample leaderboard data
  final List<Map<String, dynamic>> _leaderboard = [
    {
      'userId': 'user1',
      'points': 150,
      'badges': ['متميز']
    },
    {
      'userId': 'user2',
      'points': 120,
      'badges': ['مساهم نشط']
    },
    {'userId': 'user3', 'points': 100, 'badges': []},
  ];
  // Sample challenges data
  final List<Map<String, dynamic>> _challenges = [
    {
      'id': '1',
      'title': 'Complete 5 lessons',
      'description': 'Finish 5 different lessons',
      'points': 50,
      'completed': false,
      'progress': 2,
      'target': 5,
      'reward': 50
    },
    {
      'id': '2',
      'title': 'Share content',
      'description': 'Share app content with friends',
      'points': 30,
      'completed': false,
      'progress': 0,
      'target': 1,
      'reward': 30
    },
  ];

  void addUserPoints(String userId, int points) {
    if (userId.isEmpty) return;
    _userPoints[userId] = (_userPoints[userId] ?? 0) + points;
    // Check for badges related to points
    if ((_userPoints[userId] ?? 0) >= 100 &&
        !(_userBadges[userId]?.contains('متميز') ?? false)) {
      awardBadge(userId, 'متميز');
    }
    notifyListeners();
  }

  int getUserPoints(String userId) {
    if (userId.isEmpty) return 0;
    return _userPoints[userId] ?? 0;
  }

  // Driver points and statistics
  Map<String, int> driverPoints = {}; // نقاط كل سائق
  Map<String, Map<String, dynamic>> driverStats = {}; // إحصائيات السائق
  List<Map<String, dynamic>> leaderboard = []; // لوحة المتصدرين

  // تحديث نقاط السائق
  void addDriverPoints(String driverId, int points) {
    driverPoints[driverId] = (driverPoints[driverId] ?? 0) + points;
    notifyListeners();
  }

  // تحديث إحصائيات السائق
  void updateDriverStats(String driverId, int completedRides, double rating) {
    driverStats[driverId] = {
      'completedRides': completedRides,
      'rating': rating,
      'earnings': (driverStats[driverId]?['earnings'] ?? 0.0) +
          10.0, // مثال على الإيرادات
    };
    _updateLeaderboard();
    notifyListeners();
  }

  // تحديث لوحة المتصدرين
  void _updateLeaderboard() {
    leaderboard = driverStats.entries.map((entry) {
      return {
        'driverId': entry.key,
        'name': entry.value['name'] ?? 'غير معروف',
        'points': driverPoints[entry.key] ?? 0,
        'rating': entry.value['rating'] ?? 0.0,
      };
    }).toList()
      ..sort((a, b) => (b['points'] as int).compareTo(a['points'] as int));
    notifyListeners();
  }

  // إضافة مكافأة يومية
  bool checkDailyGoal(String driverId, int completedRides) {
    const dailyGoal = 5; // الهدف اليومي: 5 رحلات
    if (completedRides >= dailyGoal) {
      addDriverPoints(driverId, 50); // مكافأة 50 نقطة
      return true;
    }
    return false;
  }

  // User badges management
  void awardBadge(String userId, String badge) {
    if (userId.isEmpty || badge.isEmpty) return;
    _userBadges.putIfAbsent(userId, () => []);
    if (!_userBadges[userId]!.contains(badge)) {
      _userBadges[userId]!.add(badge);
      notifyListeners();
    }
  }

  List<String> getUserBadges(String userId) {
    if (userId.isEmpty) return [];
    return _userBadges[userId] ?? [];
  }

  // Challenges management
  Future<List<Map<String, dynamic>>> getChallenges() async {
    // Simulate API call or async data fetching
    await Future.delayed(const Duration(milliseconds: 100));
    return _challenges;
  }
}
