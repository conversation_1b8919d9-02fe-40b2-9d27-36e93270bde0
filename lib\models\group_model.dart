
class Group {
  final String id;
  final String name;
  final String iconUrl;
  final int membersCount;

  Group({
    required this.id,
    required this.name,
    required this.iconUrl,
    required this.membersCount,
  });

  factory Group.fromJson(Map<String, dynamic> json) {
    return Group(
      id: json['_id'] ?? json['id'] ?? '',
      name: json['name'] ?? 'مجموعة',
      iconUrl: json['iconUrl'] ?? 'https://via.placeholder.com/150',
      membersCount: json['membersCount'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'iconUrl': iconUrl,
      'membersCount': membersCount,
    };
  }
}