# Save exact version of packages
save-exact = true

# Set the package-lock to version 2
package-lock = true

# Enable package-lock.json creation
package-lock-only = false

# Set the package-lock.json version
lockfile-version = 2

# Set the engine-strict to true
engine-strict = true

# Set the progress to false for CI/CD
progress = false

# Set the fund to false to disable funding messages
fund = false

# Set the audit to true for security audits
audit = true

# Set the audit-level to high
audit-level = high

# Set the registry to use the official npm registry
registry = https://registry.npmjs.org/

# Set the cache settings
cache = .npm-cache
cache-min = 10
cache-max = 1000

# Set the loglevel
loglevel = warn

# Enable strict SSL
strict-ssl = true

# Set the save-prefix to empty to ensure exact versions
save-prefix =

# Disable package-lock.json updates on install
prefer-offline = true

# Set the tmp directory
tmp = .npm-tmp

# Set the init settings
init.author.name = "FULK Team"
init.author.email = "<EMAIL>"
init.license = "MIT"
init.version = "1.0.0"