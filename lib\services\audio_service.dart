import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:record/record.dart';

class AudioService {
  final Record _audioRecorder = Record();
  String? _recordingPath;
  bool _isRecording = false;
  Timer? _recordingTimer;
  int _recordingDuration = 0;

  bool get isRecording => _isRecording;
  int get recordingDuration => _recordingDuration;
  String? get recordingPath => _recordingPath;

  Future<bool> checkPermission() async {
    final status = await Permission.microphone.request();
    return status.isGranted;
  }

  Future<void> startRecording({Function(int)? onDurationChanged}) async {
    try {
      if (await checkPermission()) {
        final directory = await getTemporaryDirectory();
        final timestamp = DateTime.now().millisecondsSinceEpoch;
        _recordingPath = '${directory.path}/audio_$timestamp.aac';

        await _audioRecorder.start(
          path: _recordingPath!,
          encoder: AudioEncoder.aacLc,
          bitRate: 128000,
          samplingRate: 44100,
        );

        _isRecording = true;
        _recordingDuration = 0;

        // Start timer to track recording duration
        _recordingTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
          _recordingDuration++;
          if (onDurationChanged != null) {
            onDurationChanged(_recordingDuration);
          }
        });
      } else {
        throw Exception('لم يتم منح إذن الميكروفون');
      }
    } catch (e) {
      _isRecording = false;
      rethrow;
    }
  }

  Future<String?> stopRecording() async {
    try {
      if (_isRecording) {
        _recordingTimer?.cancel();
        _isRecording = false;
        await _audioRecorder.stop();
        return _recordingPath;
      }
      return null;
    } catch (e) {
      debugPrint('خطأ في إيقاف التسجيل: $e');
      return null;
    }
  }

  Future<void> cancelRecording() async {
    try {
      if (_isRecording) {
        _recordingTimer?.cancel();
        _isRecording = false;
        await _audioRecorder.stop();

        // Delete the recording file
        if (_recordingPath != null) {
          final file = File(_recordingPath!);
          if (await file.exists()) {
            await file.delete();
          }
        }
        _recordingPath = null;
      }
    } catch (e) {
      debugPrint('خطأ في إلغاء التسجيل: $e');
    }
  }

  void dispose() {
    _recordingTimer?.cancel();
    _audioRecorder.dispose();
  }
}
