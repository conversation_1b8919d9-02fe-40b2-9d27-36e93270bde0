const mongoose = require('mongoose');

const StorySchema = new mongoose.Schema({
user: {
type: mongoose.Schema.Types.ObjectId,
ref: 'User',
required: true
},
image: {
type: String,
required: [true, 'الرجاء إضافة صورة للقصة']
},
createdAt: {
type: Date,
default: Date.now,
expires: 86400 // تنتهي صلاحية القصة بعد 24 ساعة (86400 ثانية)
}
});

// الحصول على معلومات المستخدم
StorySchema.virtual('userDetails', {
ref: 'User',
localField: 'user',
foreignField: '_id',
justOne: true
});

module.exports = mongoose.model('Story', StorySchema);
