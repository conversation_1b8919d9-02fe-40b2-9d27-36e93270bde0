import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:http/http.dart' as http;
import 'package:provider/provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:socket_io_client/socket_io_client.dart' as io;
import 'package:timeago/timeago.dart' as timeago;
import 'appstate.dart';
import 'models/idea_model.dart';

// تهيئة Timeago للغة العربية
void setupTimeago() => timeago.setLocaleMessages('ar', timeago.ArMessages());

// خدمة إدارة الأفكار
class IdeaService {
  final String baseUrl;
  final String authToken;
  io.Socket? socket;

  IdeaService(
      {required this.baseUrl,
      required this.authToken}); // يجب تمرير appState.token عند الإنشاء

  Future<List<Idea>> fetchIdeas(
      {String search = '',
      String filter = 'all',
      String sort = 'newest',
      int skip = 0}) async {
    try {
      final response = await http.get(
        Uri.parse(
            '$baseUrl/ideas?search=${Uri.encodeQueryComponent(search)}&filter=$filter&sort=$sort&skip=$skip'),
        headers: {'Authorization': 'Bearer $authToken'},
      );
      if (response.statusCode == 200) {
        return (jsonDecode(response.body) as List)
            .map((json) => Idea.fromJson(json))
            .toList();
      } else {
        throw Exception('فشل في جلب الأفكار: ${response.body}');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }

  Future<List<Map<String, dynamic>>> fetchNotifications() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/notifications'),
        headers: {'Authorization': 'Bearer $authToken'},
      );
      if (response.statusCode == 200) {
        return List<Map<String, dynamic>>.from(jsonDecode(response.body));
      } else {
        throw Exception('فشل في جلب الإشعارات: ${response.body}');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }

  Future<void> updateNotification(String notificationId) async {
    try {
      final response = await http.put(
        Uri.parse('$baseUrl/notifications/$notificationId'),
        headers: {'Authorization': 'Bearer $authToken'},
      );
      if (response.statusCode != 200) {
        throw Exception('فشل في تحديث الإشعار: ${response.body}');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }

  Future<void> createIdea(String title, String description, String category,
      File? file, String creatorId) async {
    try {
      var request = http.MultipartRequest('POST', Uri.parse('$baseUrl/ideas'));
      request.headers['Authorization'] = 'Bearer $authToken';
      request.fields['title'] = title;
      request.fields['description'] = description;
      request.fields['category'] = category;
      request.fields['creatorId'] = creatorId;
      if (file != null) {
        if (await file.length() > 10 * 1024 * 1024) {
          throw Exception(
              'حجم الملف كبير جدًا (يجب أن يكون أقل من 10 ميجابايت)');
        }
        request.files.add(await http.MultipartFile.fromPath('file', file.path));
      }
      final response = await request.send();
      if (response.statusCode != 201) {
        throw Exception('فشل في إنشاء الفكرة: ${response.reasonPhrase}');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }

  Future<void> likeIdea(String ideaId, int newLikes, bool isLiked) async {
    try {
      final response = await http.put(
        Uri.parse('$baseUrl/ideas/$ideaId'),
        headers: {
          'Authorization': 'Bearer $authToken',
          'Content-Type': 'application/json'
        },
        body: jsonEncode({'likes': newLikes, 'isLiked': isLiked}),
      );
      if (response.statusCode != 200) {
        throw Exception('فشل في تحديث الإعجاب: ${response.body}');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }

  void setupSocketListeners(io.Socket socket, Function(dynamic) onNewIdea) {
    this.socket = socket;
    socket.on('new_idea', onNewIdea);
  }
}

class Rayan9 extends StatelessWidget {
  const Rayan9({super.key});

  @override
  Widget build(BuildContext context) {
    return const IdeasHomeScreen();
  }
}

class IdeasHomeScreen extends StatefulWidget {
  const IdeasHomeScreen({super.key});

  @override
  _IdeasHomeScreenState createState() => _IdeasHomeScreenState();
}

class _IdeasHomeScreenState extends State<IdeasHomeScreen>
    with SingleTickerProviderStateMixin {
  final PageController _adController = PageController();
  final ScrollController _scrollController = ScrollController();
  final TextEditingController _searchController = TextEditingController();
  final IdeaService _ideaService =
      IdeaService(baseUrl: AppState.getBackendUrl(), authToken: defaultAuthToken);

  int _currentAd = 0;
  int _selectedCategory = 0;
  String _selectedSort = 'newest';
  String _selectedFilter = 'all';
  Timer? _timer;
  bool _isLoadingMore = false;
  bool _isLoadingInitial = true;
  late AnimationController _fabAnimationController;
  late Animation<double> _fabAnimation;

  List<Idea> _ideas = [];
  List<Map<String, dynamic>> _notifications = [];
  final List<String> adImages = [
    '${AppState.getBackendUrl()}/uploads/ads/ad1.jpg',
    '${AppState.getBackendUrl()}/uploads/ads/ad2.jpg',
    '${AppState.getBackendUrl()}/uploads/ads/ad3.jpg',
  ];

  final List<Map<String, dynamic>> categories = const [
    {'icon': Icons.lightbulb, 'title': 'فكرة مشروع', 'filter': 'project_idea'},
    {'icon': Icons.group, 'title': 'فكرة للمجتمع', 'filter': 'community_idea'},
    {'icon': Icons.science, 'title': 'فكرة علمية', 'filter': 'scientific_idea'},
  ];

  // Mock search suggestions
  final List<String> _searchSuggestions = [
    'مشاريع تقنية',
    'أفكار مجتمعية',
    'ابتكارات علمية',
    'تطبيقات ذكية',
    'تصميم مستدام',
  ];

  @override
  void initState() {
    super.initState();
    setupTimeago();
    _startAutoScroll();
    _scrollController.addListener(_scrollListener);

    // Debounce search input
    Timer? debounce;
    _searchController.addListener(() {
      if (debounce?.isActive ?? false) debounce!.cancel();
      debounce = Timer(const Duration(milliseconds: 500), () {
        _loadInitialData();
      });
    });

    // Initialize FAB animation
    _fabAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 2),
    )..repeat(reverse: true);
    _fabAnimation = Tween<double>(begin: 1.0, end: 1.2).animate(
      CurvedAnimation(
        parent: _fabAnimationController,
        curve: Curves.easeInOut,
      ),
    );

    _loadInitialData();
    _fetchNotifications();
    final appState = Provider.of<AppState>(context, listen: false);
    if (appState.socket != null) {
      _ideaService.setupSocketListeners(appState.socket!, (data) {
        _loadInitialData();
        setState(() {
          _notifications.add({
            'message': 'فكرة جديدة: ${data['title']}',
            'date': DateTime.now().toString(),
            'read': false,
            '_id': data['notificationId'] ?? DateTime.now().toString(),
          });
        });
      });
    }
  }

  void _startAutoScroll() {
    _timer = Timer.periodic(const Duration(seconds: 5), (timer) {
      if (!mounted) return;
      setState(() {
        _currentAd = (_currentAd < adImages.length - 1) ? _currentAd + 1 : 0;
        _adController.animateToPage(_currentAd,
            duration: const Duration(milliseconds: 500),
            curve: Curves.easeInOut);
      });
    });
  }

  void _scrollListener() {
    if (_scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 50 &&
        !_isLoadingMore) {
      _loadMoreIdeas();
    }
  }

  Future<void> _loadInitialData() async {
    setState(() => _isLoadingInitial = true);
    try {
      _ideas = await _ideaService.fetchIdeas(
          search: _searchController.text,
          filter: _selectedFilter,
          sort: _selectedSort);
      setState(() => _isLoadingInitial = false);
    } catch (e) {
      _showSnackBar('خطأ في جلب الأفكار: $e', Colors.red);
      setState(() => _isLoadingInitial = false);
    }
  }

  Future<void> _loadMoreIdeas() async {
    if (_isLoadingMore) return;
    setState(() => _isLoadingMore = true);
    try {
      final newIdeas = await _ideaService.fetchIdeas(
        search: _searchController.text,
        filter: _selectedFilter,
        sort: _selectedSort,
        skip: _ideas.length,
      );
      setState(() {
        _ideas.addAll(newIdeas);
        _isLoadingMore = false;
      });
    } catch (e) {
      _showSnackBar('خطأ في جلب المزيد من الأفكار: $e', Colors.red);
      setState(() => _isLoadingMore = false);
    }
  }

  Future<void> _fetchNotifications() async {
    try {
      _notifications = await _ideaService.fetchNotifications();
      setState(() {});
    } catch (e) {
      _showSnackBar('خطأ في جلب الإشعارات: $e', Colors.red);
    }
  }

  Future<void> _refreshData() async {
    await Future.wait([_loadInitialData(), _fetchNotifications()]);
  }

  @override
  void dispose() {
    _timer?.cancel();
    _adController.dispose();
    _scrollController.dispose();
    _searchController.dispose();
    _fabAnimationController.dispose();
    super.dispose();
  }

  void _showSnackBar(String message,
      [Color? backgroundColor, SnackBarAction? action]) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: backgroundColor ?? Colors.teal,
        behavior: SnackBarBehavior.floating,
        action: action,
        elevation: 6,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return Scaffold(
      body: _isLoadingInitial
          ? _buildSkeletonLoader()
          : RefreshIndicator(
              onRefresh: _refreshData,
              color: Colors.teal,
              child: CustomScrollView(
                controller: _scrollController,
                slivers: [
                  _buildSliverAppBar(isDarkMode),
                  SliverToBoxAdapter(child: _buildFilterChips()),
                  SliverToBoxAdapter(child: _buildAdsCarousel()),
                  SliverPersistentHeader(
                    pinned: true,
                    delegate: _StickyHeaderDelegate(child: _buildCategories()),
                  ),
                  SliverToBoxAdapter(child: _buildIdeasList()),
                  if (_isLoadingMore)
                    const SliverToBoxAdapter(
                      child: Padding(
                        padding: EdgeInsets.all(16),
                        child: CircularProgressIndicator(
                            valueColor:
                                AlwaysStoppedAnimation<Color>(Colors.teal)),
                      ),
                    ),
                ],
              ),
            ),
      floatingActionButton: ScaleTransition(
        scale: _fabAnimation,
        child: FloatingActionButton(
          onPressed: () => _showCreateIdeaDialog(context),
          backgroundColor: Colors.teal,
          tooltip: 'إنشاء فكرة',
          elevation: 8,
          child: const Icon(Icons.post_add, size: 30, color: Colors.white),
        ),
      ),
    );
  }

  Widget _buildSkeletonLoader() {
    return SingleChildScrollView(
      child: Column(
        children: [
          Container(
            height: 200,
            margin: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(12),
            ),
          ),
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: 0.75,
              crossAxisSpacing: 8,
              mainAxisSpacing: 8,
            ),
            itemCount: 6,
            itemBuilder: (context, index) => Container(
              margin: const EdgeInsets.all(4),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.2),
                    spreadRadius: 1,
                    blurRadius: 4,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  SliverAppBar _buildSliverAppBar(bool isDarkMode) {
    return SliverAppBar(
      expandedHeight: 120,
      flexibleSpace: FlexibleSpaceBar(
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: isDarkMode
                  ? [Colors.teal[900]!, Colors.teal[700]!]
                  : [Colors.teal, Colors.tealAccent],
            ),
          ),
          child: SafeArea(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Column(
                children: [
                  TextField(
                    controller: _searchController,
                    decoration: InputDecoration(
                      hintText: 'ابحث عن فكرة...',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(30),
                        borderSide: BorderSide.none,
                      ),
                      hintStyle: TextStyle(
                          color: isDarkMode ? Colors.white70 : Colors.white),
                      prefixIcon: Icon(Icons.search,
                          color: isDarkMode ? Colors.white : Colors.white),
                      suffixIcon: _searchController.text.isNotEmpty
                          ? IconButton(
                              icon: Icon(Icons.clear,
                                  color:
                                      isDarkMode ? Colors.white : Colors.white),
                              onPressed: () {
                                _searchController.clear();
                                _loadInitialData();
                              },
                            )
                          : null,
                      filled: true,
                      fillColor: isDarkMode ? Colors.white12 : Colors.white24,
                    ),
                    style: TextStyle(
                        color: isDarkMode ? Colors.white : Colors.white),
                    onSubmitted: (_) => _loadInitialData(),
                    onChanged: (value) {
                      if (value.isNotEmpty) {
                        // Show suggestions (mock)
                        _showSearchSuggestions(context, value);
                      }
                    },
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
      actions: [
        Stack(
          children: [
            IconButton(
              icon: const Icon(Icons.notifications, color: Colors.white),
              onPressed: () => _showNotifications(context),
            ),
            if (_notifications.isNotEmpty)
              Positioned(
                right: 8,
                top: 8,
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 300),
                  padding: const EdgeInsets.all(4),
                  decoration: const BoxDecoration(
                      color: Colors.red, shape: BoxShape.circle),
                  constraints:
                      const BoxConstraints(minWidth: 16, minHeight: 16),
                  child: Text(
                    '${_notifications.length}',
                    style: const TextStyle(color: Colors.white, fontSize: 10),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
          ],
        ),
        PopupMenuButton<String>(
          color: isDarkMode ? Colors.grey[800] : Colors.white,
          onSelected: (value) => setState(() {
            _selectedSort = value;
            _loadInitialData();
          }),
          itemBuilder: (context) => const [
            PopupMenuItem(value: 'newest', child: Text('الأحدث')),
            PopupMenuItem(value: 'likes', child: Text('الأكثر إعجابًا')),
            PopupMenuItem(value: 'featured', child: Text('المميزة فقط')),
          ],
        ),
      ],
      pinned: true,
    );
  }

  void _showSearchSuggestions(BuildContext context, String query) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        height: 200,
        padding: const EdgeInsets.all(16),
        child: ListView(
          children: _searchSuggestions
              .where((suggestion) =>
                  suggestion.toLowerCase().contains(query.toLowerCase()))
              .map((suggestion) => ListTile(
                    title: Text(suggestion),
                    onTap: () {
                      _searchController.text = suggestion;
                      _loadInitialData();
                      Navigator.pop(context);
                    },
                  ))
              .toList(),
        ),
      ),
    );
  }

  Widget _buildFilterChips() {
    return Container(
      height: 50,
      padding: const EdgeInsets.symmetric(horizontal: 8),
      child: ListView(
        scrollDirection: Axis.horizontal,
        children: [
          _buildFilterChip('الكل', 'all'),
          ...categories
              .map((category) => _buildFilterChip(
                  category['title'] as String, category['filter'] as String))
              ,
        ],
      ),
    );
  }

  Widget _buildFilterChip(String label, String filter) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 4),
      child: GestureDetector(
        onHorizontalDragEnd: (details) {
          if (details.primaryVelocity! > 0) {
            // Swipe right
            setState(() {
              _selectedFilter = categories
                  .firstWhere((c) => c['filter'] == filter)['filter'] as String;
              _loadInitialData();
            });
          }
        },
        child: FilterChip(
          label: Text(label),
          selected: _selectedFilter == filter,
          onSelected: (v) => setState(() {
            _selectedFilter = filter;
            _loadInitialData();
          }),
          selectedColor: isDarkMode ? Colors.teal[700] : Colors.teal[100],
          checkmarkColor: isDarkMode ? Colors.teal[200] : Colors.teal[900],
          backgroundColor: isDarkMode ? Colors.grey[800] : Colors.grey[200],
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
          elevation: _selectedFilter == filter ? 4 : 0,
        ),
      ),
    );
  }

  Widget _buildAdsCarousel() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return Column(
      children: [
        SizedBox(
          height: 200,
          child: PageView.builder(
            controller: _adController,
            itemCount: adImages.length,
            onPageChanged: (index) => setState(() => _currentAd = index),
            itemBuilder: (context, index) => GestureDetector(
              onTap: () => _onAdTap(index),
              child: Transform(
                transform: Matrix4.identity()
                  ..setEntry(3, 2, 0.001)
                  ..rotateX(0.1 * (_currentAd - index)),
                alignment: Alignment.center,
                child: Container(
                  margin: const EdgeInsets.symmetric(horizontal: 8),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.2),
                        spreadRadius: 2,
                        blurRadius: 8,
                      ),
                    ],
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(12),
                    child: CachedNetworkImage(
                      imageUrl: adImages[index],
                      fit: BoxFit.cover,
                      placeholder: (context, url) => Container(
                        color: isDarkMode ? Colors.grey[800] : Colors.grey[300],
                        child: const Center(
                          child: CircularProgressIndicator(
                              valueColor:
                                  AlwaysStoppedAnimation<Color>(Colors.teal)),
                        ),
                      ),
                      errorWidget: (context, url, error) =>
                          const Icon(Icons.error, color: Colors.teal, size: 50),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
        const SizedBox(height: 8),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: List.generate(
            adImages.length,
            (index) => AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              margin: const EdgeInsets.symmetric(horizontal: 4),
              width: _currentAd == index ? 20 : 8,
              height: 8,
              decoration: BoxDecoration(
                color: _currentAd == index
                    ? Colors.teal
                    : (isDarkMode ? Colors.grey[600] : Colors.grey),
                borderRadius: BorderRadius.circular(4),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildCategories() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return Container(
      height: 120,
      padding: const EdgeInsets.symmetric(horizontal: 8),
      color: isDarkMode ? Colors.grey[900] : Colors.white,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: categories.length,
        itemBuilder: (context, index) => _buildCategoryItem(index),
      ),
    );
  }

  Widget _buildCategoryItem(int index) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final count = _ideas
        .where((idea) => idea.category == categories[index]['filter'])
        .length;
    return GestureDetector(
      onTap: () => setState(() {
        _selectedCategory = index;
        _selectedFilter = categories[index]['filter'] as String;
        _loadInitialData();
      }),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        width: 100,
        padding: const EdgeInsets.all(8),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: _selectedCategory == index
                    ? (isDarkMode ? Colors.teal[800] : Colors.teal[50])
                    : (isDarkMode ? Colors.grey[800] : Colors.grey[200]),
                borderRadius: BorderRadius.circular(15),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.2),
                    spreadRadius: 1,
                    blurRadius: 4,
                  ),
                ],
              ),
              child: Icon(categories[index]['icon'] as IconData,
                  color: _selectedCategory == index
                      ? Colors.teal
                      : (isDarkMode ? Colors.grey[400] : Colors.grey[700])),
            ),
            const SizedBox(height: 8),
            Text(
              '${categories[index]['title']} ($count)',
              style: TextStyle(
                color: _selectedCategory == index
                    ? Colors.teal
                    : (isDarkMode ? Colors.white70 : Colors.black),
                fontSize: 12,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildIdeasList() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    List<Idea> filteredIdeas = _ideas.where((idea) {
      final matchesFilter =
          _selectedFilter == 'all' || idea.category == _selectedFilter;
      final matchesSearch = idea.title
              .toLowerCase()
              .contains(_searchController.text.toLowerCase()) ||
          idea.description
              .toLowerCase()
              .contains(_searchController.text.toLowerCase());
      return matchesFilter && matchesSearch;
    }).toList();

    switch (_selectedSort) {
      case 'newest':
        filteredIdeas.sort((a, b) => b.createdAt.compareTo(a.createdAt));
        break;
      case 'likes':
        filteredIdeas.sort((a, b) => b.likes.compareTo(a.likes));
        break;
      case 'featured':
        filteredIdeas = filteredIdeas.where((idea) => idea.isFeatured).toList();
        break;
    }

    const visibleIdeasCount = 20;
    return Padding(
      padding: const EdgeInsets.all(8),
      child: filteredIdeas.isEmpty
          ? Center(
              child: Text(
                'لا توجد أفكار متاحة',
                style: TextStyle(
                    fontSize: 18,
                    color: isDarkMode ? Colors.grey[400] : Colors.grey),
              ),
            )
          : MasonryGridView.count(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisCount: 2,
              mainAxisSpacing: 8,
              crossAxisSpacing: 8,
              itemCount: filteredIdeas.length > visibleIdeasCount
                  ? visibleIdeasCount
                  : filteredIdeas.length,
              itemBuilder: (context, index) {
                return _buildIdeaItem(filteredIdeas[index]);
              },
            ),
    );
  }

  Widget _buildIdeaItem(Idea idea) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return GestureDetector(
      onTap: () => _showIdeaDetails(context, idea),
      onDoubleTap: () => _likeIdea(idea), // Double-tap to like
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        transform: Matrix4.identity()
          ..scale(idea.isLiked ? 1.05 : 1.0), // Scale on like
        child: Card(
          elevation: 5,
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          color: isDarkMode ? Colors.grey[850] : Colors.white,
          child: Stack(
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    child: ClipRRect(
                      borderRadius:
                          const BorderRadius.vertical(top: Radius.circular(16)),
                      child: CachedNetworkImage(
                        imageUrl: idea.filePath != null
                            ? '$_ideaService.baseUrl${idea.filePath}'
                            : 'https://via.placeholder.com/400x300',
                        fit: BoxFit.cover,
                        width: double.infinity,
                        placeholder: (context, url) => Container(
                          color:
                              isDarkMode ? Colors.grey[800] : Colors.grey[300],
                          child: const Center(
                            child: CircularProgressIndicator(
                                valueColor:
                                    AlwaysStoppedAnimation<Color>(Colors.teal)),
                          ),
                        ),
                        errorWidget: (context, url, error) => const Icon(
                            Icons.error,
                            color: Colors.teal,
                            size: 50),
                      ),
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.all(8),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          idea.title,
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: isDarkMode ? Colors.white : Colors.black,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 4),
                        Text(
                          timeago.format(idea.createdAt, locale: 'ar'),
                          style: TextStyle(
                            color: isDarkMode ? Colors.grey[400] : Colors.grey,
                            fontSize: 12,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            IconButton(
                              icon: AnimatedSwitcher(
                                duration: const Duration(milliseconds: 300),
                                child: Icon(
                                  idea.isLiked
                                      ? Icons.favorite
                                      : Icons.favorite_border,
                                  key: ValueKey(idea.isLiked),
                                  size: 20,
                                  color:
                                      idea.isLiked ? Colors.red : Colors.grey,
                                ),
                              ),
                              onPressed: () => _likeIdea(idea),
                            ),
                            Text(
                              '${idea.likes}',
                              style: TextStyle(
                                  color: isDarkMode
                                      ? Colors.white70
                                      : Colors.black87),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              if (idea.isFeatured)
                Positioned(
                  top: 8,
                  left: 8,
                  child: DottedBorder(
                    color: Colors.amber,
                    strokeWidth: 2,
                    dashPattern: const [4, 4],
                    borderType: BorderType.RRect,
                    radius: const Radius.circular(8),
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 6, vertical: 2),
                      color: Colors.amber.withOpacity(0.2),
                      child: const Text('مميز',
                          style: TextStyle(color: Colors.amber, fontSize: 12)),
                    ),
                  ),
                ),
              Positioned(
                top: 8,
                right: 8,
                child: IconButton(
                  icon: const Icon(Icons.share, size: 20, color: Colors.teal),
                  onPressed: () => _shareIdea(idea),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _likeIdea(Idea idea) async {
    final originalLikes = idea.likes;
    final originalIsLiked = idea.isLiked;
    try {
      setState(() {
        idea.isLiked = !idea.isLiked;
        idea.likes += idea.isLiked ? 1 : -1;
      });
      await _ideaService.likeIdea(idea.id, idea.likes, idea.isLiked);
    } catch (e) {
      setState(() {
        idea.isLiked = originalIsLiked;
        idea.likes = originalLikes;
      });
      _showSnackBar('خطأ في تحديث الإعجاب: $e', Colors.red);
    }
  }

  void _showCreateIdeaDialog(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final titleController = TextEditingController();
    final descriptionController = TextEditingController();
    String? selectedFileName;
    File? selectedFile;
    String selectedCategory = categories[0]['filter'] as String;
    final appState = Provider.of<AppState>(context, listen: false);
    final creatorId = appState.userId ?? 'user1';

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
          backgroundColor: isDarkMode ? Colors.grey[900] : Colors.white,
          title: Container(
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                  colors: isDarkMode
                      ? [Colors.teal[900]!, Colors.teal[700]!]
                      : [Colors.teal, Colors.tealAccent]),
              borderRadius:
                  const BorderRadius.vertical(top: Radius.circular(20)),
            ),
            child: const Text(
              "إنشاء فكرة جديدة",
              style:
                  TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
          ),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildDialogField(
                    "عنوان الفكرة:", titleController, "أدخل عنوان الفكرة..."),
                _buildDialogField(
                    "الوصف:", descriptionController, "أدخل وصف الفكرة...",
                    maxLines: 3),
                _buildCategoryDropdown(setDialogState, selectedCategory,
                    (value) => setDialogState(() => selectedCategory = value)),
                _buildFilePicker(
                    setDialogState,
                    selectedFileName,
                    (name, file) => setDialogState(() {
                          selectedFileName = name;
                          selectedFile = file;
                        })),
              ],
            ),
          ),
          actions: [
            TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text("إلغاء",
                    style: TextStyle(
                        color: isDarkMode ? Colors.white70 : Colors.teal))),
            ElevatedButton(
              onPressed: () => _submitIdea(
                  context,
                  titleController,
                  descriptionController,
                  selectedFile,
                  selectedFileName,
                  selectedCategory,
                  creatorId),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.teal,
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10)),
              ),
              child: const Text("إنشاء", style: TextStyle(color: Colors.white)),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _submitIdea(
    BuildContext context,
    TextEditingController title,
    TextEditingController desc,
    File? file,
    String? fileName,
    String category,
    String creatorId,
  ) async {
    if (title.text.isEmpty || desc.text.isEmpty) {
      _showSnackBar("يرجى ملء جميع الحقول الإلزامية", Colors.orange);
      return;
    }

    try {
      await _ideaService.createIdea(
          title.text, desc.text, category, file, creatorId);
      final newIdea = Idea(
        id: DateTime.now().toString(),
        title: title.text,
        description: desc.text,
        category: category,
        filePath: fileName,
        createdAt: DateTime.now(),
      );
      setState(() => _ideas.insert(0, newIdea));
      Navigator.pop(context);
      _showSnackBar(
        "تم إنشاء الفكرة: ${title.text}",
        Colors.green,
        SnackBarAction(
          label: 'عرض',
          textColor: Colors.white,
          onPressed: () => _showIdeaDetails(context, newIdea),
        ),
      );
    } catch (e) {
      _showSnackBar("خطأ في إنشاء الفكرة: $e", Colors.red);
    }
  }

  Widget _buildDialogField(
      String label, TextEditingController controller, String hint,
      {int maxLines = 1}) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(label,
              style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: isDarkMode ? Colors.teal[300] : Colors.teal)),
          const SizedBox(height: 8),
          TextField(
            controller: controller,
            maxLines: maxLines,
            decoration: InputDecoration(
              hintText: hint,
              filled: true,
              fillColor: isDarkMode ? Colors.grey[800] : Colors.grey[100],
              border:
                  OutlineInputBorder(borderRadius: BorderRadius.circular(10)),
              contentPadding:
                  const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            ),
            style: TextStyle(color: isDarkMode ? Colors.white : Colors.black),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryDropdown(void Function(void Function()) setDialogState,
      String selectedCategory, void Function(String) onChanged) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "الفئة:",
            style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: isDarkMode ? Colors.teal[300] : Colors.teal),
          ),
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12),
            decoration: BoxDecoration(
              color: isDarkMode ? Colors.grey[800] : Colors.grey[100],
              borderRadius: BorderRadius.circular(10),
              border: Border.all(color: Colors.teal),
            ),
            child: DropdownButton<String>(
              value: selectedCategory,
              items: categories
                  .map((category) => DropdownMenuItem<String>(
                        value: category['filter'] as String,
                        child: Text(category['title'] as String,
                            style: TextStyle(
                                color:
                                    isDarkMode ? Colors.white : Colors.black)),
                      ))
                  .toList(),
              onChanged: (value) => onChanged(value!),
              underline: const SizedBox(),
              isExpanded: true,
              icon: const Icon(Icons.arrow_drop_down, color: Colors.teal),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilePicker(void Function(void Function()) setDialogState,
      String? selectedFileName, void Function(String, File) onFilePicked) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "اختر ملف (صورة/فيديو/PDF):",
            style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: isDarkMode ? Colors.teal[300] : Colors.teal),
          ),
          const SizedBox(height: 8),
          ElevatedButton.icon(
            onPressed: () async {
              final result = await FilePicker.platform.pickFiles(
                type: FileType.custom,
                allowedExtensions: ['jpg', 'jpeg', 'png', 'mp4', 'mov', 'pdf'],
              );
              if (result != null && result.files.isNotEmpty) {
                final file = File(result.files.single.path!);
                if (await file.length() > 10 * 1024 * 1024) {
                  _showSnackBar(
                    'حجم الملف كبير جدًا (يجب أن يكون أقل من 10 ميجابايت)',
                    Colors.orange,
                  );
                  return;
                }
                setDialogState(
                    () => onFilePicked(result.files.single.name, file));
              }
            },
            icon: const Icon(Icons.upload_file, color: Colors.white),
            label: Text(
              selectedFileName ?? "اختيار ملف",
              style: const TextStyle(color: Colors.white),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.teal,
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10)),
              elevation: 4,
            ),
          ),
        ],
      ),
    );
  }

  void _showIdeaDetails(BuildContext context, Idea idea) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: isDarkMode ? Colors.grey[900] : Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.8,
        minChildSize: 0.5,
        maxChildSize: 0.9,
        expand: false,
        builder: (context, scrollController) => Container(
          padding: const EdgeInsets.all(16),
          child: SingleChildScrollView(
            controller: scrollController,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  idea.title,
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: isDarkMode ? Colors.teal[300] : Colors.teal,
                  ),
                ),
                const SizedBox(height: 16),
                if (idea.filePath != null)
                  ClipRRect(
                    borderRadius: BorderRadius.circular(12),
                    child: CachedNetworkImage(
                      imageUrl: '$_ideaService.baseUrl${idea.filePath}',
                      height: 200,
                      width: double.infinity,
                      fit: BoxFit.cover,
                      placeholder: (context, url) => Container(
                        color: isDarkMode ? Colors.grey[800] : Colors.grey[300],
                        child: const Center(
                          child: CircularProgressIndicator(
                              valueColor:
                                  AlwaysStoppedAnimation<Color>(Colors.teal)),
                        ),
                      ),
                      errorWidget: (context, url, error) =>
                          const Icon(Icons.error, color: Colors.teal, size: 50),
                    ),
                  ),
                const SizedBox(height: 16),
                Text(
                  idea.description,
                  style: TextStyle(
                      fontSize: 16,
                      color: isDarkMode ? Colors.white70 : Colors.black87),
                ),
                const SizedBox(height: 8),
                Text(
                  'تم النشر: ${timeago.format(idea.createdAt, locale: 'ar')}',
                  style: TextStyle(
                      color: isDarkMode ? Colors.grey[400] : Colors.grey),
                ),
                const SizedBox(height: 8),
                Text(
                  'الفئة: ${categories.firstWhere((c) => c['filter'] == idea.category)['title'] as String}',
                  style: TextStyle(
                      color: isDarkMode ? Colors.teal[300] : Colors.teal),
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    const Icon(Icons.favorite, color: Colors.red),
                    const SizedBox(width: 4),
                    Text(
                      '${idea.likes} إعجابات',
                      style: TextStyle(
                          color: isDarkMode ? Colors.teal[300] : Colors.teal),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () => Navigator.pop(context),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.teal,
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10)),
                    elevation: 4,
                  ),
                  child: const Text('إغلاق',
                      style: TextStyle(color: Colors.white)),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _showNotifications(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    showModalBottomSheet(
      context: context,
      backgroundColor: isDarkMode ? Colors.grey[900] : Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.5,
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Text(
              'الإشعارات',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: isDarkMode ? Colors.teal[300] : Colors.teal,
              ),
            ),
            const SizedBox(height: 10),
            Expanded(
              child: _notifications.isEmpty
                  ? Center(
                      child: Text(
                        'لا توجد إشعارات جديدة حالياً',
                        style: TextStyle(
                            color: isDarkMode ? Colors.grey[400] : Colors.grey),
                      ),
                    )
                  : ListView.builder(
                      itemCount: _notifications.length,
                      itemBuilder: (context, index) => Dismissible(
                        key: Key(_notifications[index]['_id']),
                        background: Container(
                          color: Colors.red,
                          alignment: Alignment.centerRight,
                          padding: const EdgeInsets.only(right: 16),
                          child: const Icon(Icons.delete, color: Colors.white),
                        ),
                        onDismissed: (direction) async {
                          final notificationId = _notifications[index]['_id'];
                          try {
                            await _ideaService
                                .updateNotification(notificationId);
                            setState(() {
                              _notifications.removeAt(index);
                            });
                          } catch (e) {
                            _showSnackBar('خطأ في حذف الإشعار: $e', Colors.red);
                          }
                        },
                        child: ListTile(
                          leading: const Icon(Icons.notifications_active,
                              color: Colors.teal),
                          title: Text(
                            _notifications[index]['message'] as String? ??
                                'غير معروف',
                            style: TextStyle(
                                color:
                                    isDarkMode ? Colors.white : Colors.black87),
                          ),
                          subtitle: Text(
                            timeago.format(
                                DateTime.parse(
                                    _notifications[index]['date'] as String),
                                locale: 'ar'),
                            style: TextStyle(
                                color: isDarkMode
                                    ? Colors.grey[400]
                                    : Colors.grey),
                          ),
                          trailing: _notifications[index]['read']
                              ? null
                              : const Icon(Icons.circle,
                                  color: Colors.teal, size: 10),
                          onTap: () async {
                            if (!_notifications[index]['read']) {
                              try {
                                await _ideaService.updateNotification(
                                    _notifications[index]['_id']);
                                setState(
                                    () => _notifications[index]['read'] = true);
                              } catch (e) {
                                _showSnackBar(
                                    'خطأ في تحديث الإشعار: $e', Colors.red);
                              }
                            }
                          },
                        ),
                      ),
                    ),
            ),
            ElevatedButton(
              onPressed: () {
                setState(() => _notifications.clear());
                Navigator.pop(context);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10)),
              ),
              child:
                  const Text('مسح الكل', style: TextStyle(color: Colors.white)),
            ),
          ],
        ),
      ),
    );
  }

  void _onAdTap(int index) {
    _showSnackBar('تم النقر على الإعلان ${index + 1}');
  }

  void _shareIdea(Idea idea) {
    final shareText =
        'فكرة مميزة: ${idea.title}\n${idea.description}${idea.filePath != null ? '\n$_ideaService.baseUrl${idea.filePath}' : ''}';
    Share.share(shareText);
    _showSnackBar("تمت المشاركة بنجاح", Colors.green);
  }
}

// Sticky header delegate for categories
class _StickyHeaderDelegate extends SliverPersistentHeaderDelegate {
  final Widget child;

  _StickyHeaderDelegate({required this.child});

  @override
  Widget build(
      BuildContext context, double shrinkOffset, bool overlapsContent) {
    return Container(
      color: Theme.of(context).brightness == Brightness.dark
          ? Colors.grey[900]
          : Colors.white,
      child: child,
    );
  }

  @override
  double get maxExtent => 120;

  @override
  double get minExtent => 120;

  @override
  bool shouldRebuild(covariant SliverPersistentHeaderDelegate oldDelegate) {
    return true;
  }
}
