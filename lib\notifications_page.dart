import 'package:flutter/material.dart';
import 'package:animate_do/animate_do.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:timezone/data/latest.dart' as tz;
import 'package:timezone/timezone.dart' as tz;
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import 'notifications_api.dart';

class NotificationProvider with ChangeNotifier {
    final List<Map<String, dynamic>> _notifications = [];
    final List<Map<String, dynamic>> _medications = [];
    final List<Map<String, dynamic>> _notesAndEvents = [];
    String _selectedNotificationType = 'both';
    final Set<String> _selectedCategories = {'islamic'};
    Map<String, dynamic> _mentalHealthAssessment = {};

    List<Map<String, dynamic>> get notifications => _notifications;
    List<Map<String, dynamic>> get medications => _medications;
    List<Map<String, dynamic>> get notesAndEvents => _notesAndEvents;
    String get selectedNotificationType => _selectedNotificationType;
    Set<String> get selectedCategories => _selectedCategories;
    Map<String, dynamic> get mentalHealthAssessment => _mentalHealthAssessment;

    void addNotification(Map<String, dynamic> notification) {
        _notifications.add(notification);
        notifyListeners();
    }

    void addMedication(Map<String, dynamic> medication) {
        _medications.add(medication);
        notifyListeners();
    }

    void addNoteOrEvent(Map<String, dynamic> noteOrEvent) {
        _notesAndEvents.add(noteOrEvent);
        notifyListeners();
    }

    void clearNotifications() {
        _notifications.clear();
        notifyListeners();
    }

    void setNotificationType(String type) {
        _selectedNotificationType = type;
        notifyListeners();
    }

    void toggleCategory(String category, bool selected) {
        if (selected) {
            _selectedCategories.add(category);
        } else {
            _selectedCategories.remove(category);
        }
        notifyListeners();
    }

    void updateMentalHealthAssessment(Map<String, dynamic> assessment) {
        _mentalHealthAssessment = assessment;
        notifyListeners();
    }
}

class NotificationScreen extends StatefulWidget {
    const NotificationScreen({super.key});

    @override
    _NotificationScreenState createState() => _NotificationScreenState();
}

class _NotificationScreenState extends State<NotificationScreen> {
    List<dynamic> _notifications = [];
    bool _isLoading = true;
    String? _error;

    @override
    void initState() {
        super.initState();
        _initializeNotifications();
        tz.initializeTimeZones();
        _fetchNotifications();
    }

    Future<void> _fetchNotifications() async {
        setState(() {
            _isLoading = true;
            _error = null;
        });
        try {
            final notifications = await NotificationsApi.fetchNotifications();
            setState(() {
                _notifications = notifications;
                _isLoading = false;
            });
        } catch (e) {
            setState(() {
                _error = e.toString();
                _isLoading = false;
            });
        }
    }

    Future<void> _addNotification(Map<String, dynamic> notification) async {
        final success = await NotificationsApi.addNotification(notification);
        if (success) {
            _fetchNotifications();
        } else {
            // يمكنك عرض رسالة خطأ هنا
        }
    }
    final FlutterLocalNotificationsPlugin _flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();
    bool _showSettings = false;
    bool _showMedicationForm = false;
    bool _showMentalHealthForm = false;
    bool _showNotesForm = false;

    final _medNameController = TextEditingController();
    final _medDosageController = TextEditingController();
    final _medTimeController = TextEditingController();
    final _noteTitleController = TextEditingController();
    final _noteContentController = TextEditingController();
    final _noteDateController = TextEditingController();
    final _noteUrlController = TextEditingController();

    int _optimismScore = 3;
    int _resilienceScore = 3;



    Future<void> _initializeNotifications() async {
        const AndroidInitializationSettings initializationSettingsAndroid = AndroidInitializationSettings('@mipmap/ic_launcher');
        const InitializationSettings initializationSettings = InitializationSettings(android: initializationSettingsAndroid);
        await _flutterLocalNotificationsPlugin.initialize(initializationSettings);
    }

    Future<void> _scheduleNotification({
        required String title,
        required String body,
        required DateTime scheduledTime,
        required int id,
    }) async {
        final tz.TZDateTime tzScheduledTime = tz.TZDateTime.from(scheduledTime, tz.local);
        await _flutterLocalNotificationsPlugin.zonedSchedule(
            id,
            title,
            body,
            tzScheduledTime,
            const NotificationDetails(
                android: AndroidNotificationDetails(
                    'channel_id',
                    'Notifications',
                    importance: Importance.max,
                    priority: Priority.high,
                ),
            ),
            androidAllowWhileIdle: true,
            uiLocalNotificationDateInterpretation: UILocalNotificationDateInterpretation.absoluteTime,
        );
    }

    @override
    void dispose() {
        _medNameController.dispose();
        _medDosageController.dispose();
        _medTimeController.dispose();
        _noteTitleController.dispose();
        _noteContentController.dispose();
        _noteDateController.dispose();
        _noteUrlController.dispose();
        super.dispose();
    }

    @override
    Widget build(BuildContext context) {
        return ChangeNotifierProvider(
            create: (_) => NotificationProvider(),
            child: Scaffold(
                body: Container(
                    width: double.infinity,
                    height: double.infinity,
                    decoration: BoxDecoration(
                        gradient: LinearGradient(
                            colors: [Colors.teal.shade100, Colors.teal.shade300],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                        ),
                    ),
                    child: SafeArea(
                        child: Column(
                            children: [
                                _buildAppBar(context),
                                Expanded(
                                    child: SingleChildScrollView(
                                        child: Column(
                                            mainAxisSize: MainAxisSize.min,
                                            children: [
                                                AnimatedCrossFade(
                                                    duration: const Duration(milliseconds: 300),
                                                    crossFadeState: _showSettings ? CrossFadeState.showSecond : CrossFadeState.showFirst,
                                                    firstChild: Container(),
                                                    secondChild: _buildSettingsSection(),
                                                ),
                                                if (_showMedicationForm) _buildMedicationForm(),
                                                if (_showMentalHealthForm) _buildMentalHealthForm(),
                                                if (_showNotesForm) _buildNotesForm(),
                                                _buildNotificationsList(),
                                            ],
                                        ),
                                    ),
                                ),
                            ],
                        ),
                    ),
                ),
            ),
        );
    }

    // Common styles
    TextStyle get _titleStyle => const TextStyle(
        fontFamily: 'Tajawal',
        fontSize: 18,
        fontWeight: FontWeight.bold,
        color: Colors.teal,
    );

    TextStyle get _labelStyle => const TextStyle(fontFamily: 'Tajawal', fontSize: 16);

    BoxDecoration get _formDecoration => BoxDecoration(
        color: Colors.white.withOpacity(0.9),
        borderRadius: BorderRadius.circular(15),
        boxShadow: const [
            BoxShadow(
                color: Colors.black12,
                blurRadius: 10,
                offset: Offset(0, 5),
            ),
        ],
    );

    ButtonStyle get _buttonStyle => ElevatedButton.styleFrom(
        backgroundColor: Colors.teal,
        foregroundColor: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
    );

    Widget _buildAppBar(BuildContext context) {
        return Consumer<NotificationProvider>(
            builder: (context, provider, _) => Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 10.0),
                child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                        const Text(
                            'الإشعارات',
                            style: TextStyle(
                                fontFamily: 'Tajawal',
                                fontSize: 24,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                            ),
                        ),
                        Row(
                            children: [
                                _buildIconButton(
                                    icon: Icons.medication,
                                    tooltip: 'إضافة تذكير دواء',
                                    onPressed: () => setState(() {
                                        _showMedicationForm = !_showMedicationForm;
                                        _showMentalHealthForm = false;
                                        _showNotesForm = false;
                                    }),
                                ),
                                _buildIconButton(
                                    icon: Icons.psychology,
                                    tooltip: 'استبيان الدعم النفسي',
                                    onPressed: () => setState(() {
                                        _showMentalHealthForm = !_showMentalHealthForm;
                                        _showMedicationForm = false;
                                        _showNotesForm = false;
                                    }),
                                ),
                                _buildIconButton(
                                    icon: Icons.event_note,
                                    tooltip: 'إضافة ملاحظة أو حدث',
                                    onPressed: () => setState(() {
                                        _showNotesForm = !_showNotesForm;
                                        _showMedicationForm = false;
                                        _showMentalHealthForm = false;
                                    }),
                                ),
                                _buildIconButton(
                                    icon: _showSettings ? Icons.close : Icons.settings,
                                    tooltip: _showSettings ? 'إغلاق الإعدادات' : 'إعدادات',
                                    onPressed: () => setState(() => _showSettings = !_showSettings),
                                    useBounce: true,
                                ),
                                _buildIconButton(
                                    icon: Icons.delete,
                                    tooltip: 'حذف الكل',
                                    onPressed: () => provider.clearNotifications(),
                                    useBounce: true,
                                ),
                            ],
                        ),
                    ],
                ),
            ),
        );
    }

    Widget _buildIconButton({
        required IconData icon,
        required String tooltip,
        required VoidCallback onPressed,
        bool useBounce = false,
    }) {
        return IconButton(
            icon: useBounce
                ? Bounce(
                duration: const Duration(milliseconds: 200),
                child: Icon(icon, color: Colors.white),
            )
                : ElasticIn(
                duration: const Duration(milliseconds: 300),
                child: Icon(icon, color: Colors.white),
            ),
            onPressed: onPressed,
            tooltip: tooltip,
        );
    }

    Widget _buildSettingsSection() {
        return Consumer<NotificationProvider>(
            builder: (context, provider, _) => FadeInUp(
                duration: const Duration(milliseconds: 300),
                child: Container(
                    padding: const EdgeInsets.all(16.0),
                    margin: const EdgeInsets.symmetric(horizontal: 16.0),
                    decoration: _formDecoration,
                    child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                            Text('إعدادات الإشعارات', style: _titleStyle),
                            const SizedBox(height: 10),
                            Text('نوع الإشعار', style: _labelStyle),
                            DropdownButton<String>(
                                value: provider.selectedNotificationType,
                                isExpanded: true,
                                items: [
                                    DropdownMenuItem(value: 'text', child: Text('كتابية', style: _labelStyle)),
                                    DropdownMenuItem(value: 'sound', child: Text('صوتية', style: _labelStyle)),
                                    DropdownMenuItem(value: 'both', child: Text('كلاهما', style: _labelStyle)),
                                ],
                                onChanged: (value) => provider.setNotificationType(value!),
                                style: const TextStyle(fontFamily: 'Tajawal', color: Colors.teal),
                                dropdownColor: Colors.white,
                                underline: Container(height: 2, color: Colors.teal),
                            ),
                            const SizedBox(height: 10),
                            Text('فئات الإشعارات', style: _labelStyle),
                            Wrap(
                                spacing: 8.0,
                                children: [
                                    _buildCategoryChip('مسيحية', 'christian', provider),
                                    _buildCategoryChip('إسلامية', 'islamic', provider),
                                    _buildCategoryChip('حكم عامة', 'wisdom', provider),
                                    _buildCategoryChip('الدعم النفسي', 'psychological_support', provider),
                                    _buildCategoryChip('الأدوية', 'medication', provider),
                                    _buildCategoryChip('الأحداث', 'events', provider),
                                ],
                            ),
                        ],
                    ),
                ),
            ),
        );
    }

    Widget _buildCategoryChip(String label, String value, NotificationProvider provider) {
        final isSelected = provider.selectedCategories.contains(value);
        return ChoiceChip(
            label: Text(
                label,
                style: TextStyle(
                    fontFamily: 'Tajawal',
                    color: isSelected ? Colors.white : Colors.teal,
                ),
            ),
            selected: isSelected,
            onSelected: (selected) => provider.toggleCategory(value, selected),
            selectedColor: Colors.teal,
            backgroundColor: Colors.white,
            shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
                side: const BorderSide(color: Colors.teal),
            ),
            elevation: isSelected ? 5 : 0,
            pressElevation: 10,
        );
    }

    Widget _buildMedicationForm() {
        return Consumer<NotificationProvider>(
            builder: (context, provider, _) => FadeInUp(
                duration: const Duration(milliseconds: 300),
                child: Container(
                    padding: const EdgeInsets.all(16.0),
                    margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 10.0),
                    decoration: _formDecoration,
                    child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                            Text('إضافة تذكير دواء', style: _titleStyle),
                            _buildTextField(_medNameController, 'اسم الدواء'),
                            const SizedBox(height: 10),
                            _buildTextField(_medDosageController, 'الجرعة'),
                            const SizedBox(height: 10),
                            _buildTextField(
                                _medTimeController,
                                'الوقت (ساعة:دقيقة)',
                                onTap: () async {
                                    final TimeOfDay? time = await showTimePicker(
                                        context: context,
                                        initialTime: TimeOfDay.now(),
                                    );
                                    if (time != null) {
                                        _medTimeController.text = time.format(context);
                                    }
                                },
                            ),
                            const SizedBox(height: 10),
                            ElevatedButton(
                                onPressed: () => _handleMedicationSubmission(provider),
                                style: _buttonStyle,
                                child: const Text('إضافة', style: TextStyle(fontFamily: 'Tajawal')),
                            ),
                        ],
                    ),
                ),
            ),
        );
    }

    Widget _buildMentalHealthForm() {
        return Consumer<NotificationProvider>(
            builder: (context, provider, _) => FadeInUp(
                duration: const Duration(milliseconds: 300),
                child: Container(
                    padding: const EdgeInsets.all(16.0),
                    margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 10.0),
                    decoration: _formDecoration,
                    child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                            Text('استبيان الدعم النفسي', style: _titleStyle),
                            const SizedBox(height: 10),
                            Text('مستوى التفاؤل (1-5)', style: _labelStyle),
                            Slider(
                                value: _optimismScore.toDouble(),
                                min: 1,
                                max: 5,
                                divisions: 4,
                                label: _optimismScore.toString(),
                                onChanged: (value) => setState(() => _optimismScore = value.round()),
                                activeColor: Colors.teal,
                            ),
                            Text('مستوى المرونة (1-5)', style: _labelStyle),
                            Slider(
                                value: _resilienceScore.toDouble(),
                                min: 1,
                                max: 5,
                                divisions: 4,
                                label: _resilienceScore.toString(),
                                onChanged: (value) => setState(() => _resilienceScore = value.round()),
                                activeColor: Colors.teal,
                            ),
                            const SizedBox(height: 10),
                            ElevatedButton(
                                onPressed: () => _handleMentalHealthSubmission(provider),
                                style: _buttonStyle,
                                child: const Text('إرسال', style: TextStyle(fontFamily: 'Tajawal')),
                            ),
                        ],
                    ),
                ),
            ),
        );
    }

    Widget _buildNotesForm() {
        return Consumer<NotificationProvider>(
            builder: (context, provider, _) => FadeInUp(
                duration: const Duration(milliseconds: 300),
                child: Container(
                    padding: const EdgeInsets.all(16.0),
                    margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 10.0),
                    decoration: _formDecoration,
                    child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                            Text('إضافة ملاحظة أو حدث', style: _titleStyle),
                            _buildTextField(_noteTitleController, 'العنوان'),
                            const SizedBox(height: 10),
                            _buildTextField(_noteContentController, 'المحتوى'),
                            const SizedBox(height: 10),
                            _buildTextField(
                                _noteDateController,
                                'التاريخ والوقت',
                                onTap: () async {
                                    final DateTime? date = await showDatePicker(
                                        context: context,
                                        initialDate: DateTime.now(),
                                        firstDate: DateTime.now(),
                                        lastDate: DateTime(2030),
                                    );
                                    if (date != null) {
                                        final TimeOfDay? time = await showTimePicker(
                                            context: context,
                                            initialTime: TimeOfDay.now(),
                                        );
                                        if (time != null) {
                                            final scheduledTime = DateTime(date.year, date.month, date.day, time.hour, time.minute);
                                            _noteDateController.text = DateFormat('yyyy-MM-dd HH:mm').format(scheduledTime);
                                        }
                                    }
                                },
                            ),
                            const SizedBox(height: 10),
                            _buildTextField(_noteUrlController, 'رابط (اختياري)'),
                            const SizedBox(height: 10),
                            ElevatedButton(
                                onPressed: () => _handleNoteSubmission(provider),
                                style: _buttonStyle,
                                child: const Text('إضافة', style: TextStyle(fontFamily: 'Tajawal')),
                            ),
                        ],
                    ),
                ),
            ),
        );
    }

    Widget _buildTextField(TextEditingController controller, String label, {VoidCallback? onTap}) {
        return TextField(
            controller: controller,
            decoration: InputDecoration(
                labelText: label,
                labelStyle: const TextStyle(fontFamily: 'Tajawal'),
                border: const OutlineInputBorder(),
            ),
            style: const TextStyle(fontFamily: 'Tajawal'),
            onTap: onTap,
        );
    }

    void _handleMedicationSubmission(NotificationProvider provider) {
        if (_medNameController.text.isNotEmpty &&
            _medDosageController.text.isNotEmpty &&
            _medTimeController.text.isNotEmpty) {
            final now = DateTime.now();
            final timeParts = _medTimeController.text.split(':');
            final scheduledTime = DateTime(
                now.year,
                now.month,
                now.day,
                int.parse(timeParts[0]),
                int.parse(timeParts[1].split(' ')[0]),
            );
            final med = {
                'name': _medNameController.text,
                'dosage': _medDosageController.text,
                'time': _medTimeController.text,
                'timestamp': DateFormat('yyyy-MM-dd HH:mm').format(now),
                'type': 'medication',
            };
            provider.addMedication(med);
            provider.addNotification({
                'message': 'تذكير: ${med['name']} - ${med['dosage']}',
                'timestamp': med['timestamp'],
                'type': 'medication',
            });
            _scheduleNotification(
                title: 'تذكير بالدواء',
                body: 'حان وقت تناول ${med['name']} - ${med['dosage']}',
                scheduledTime: scheduledTime,
                id: provider.notifications.length,
            );
            _medNameController.clear();
            _medDosageController.clear();
            _medTimeController.clear();
            setState(() => _showMedicationForm = false);
        }
    }

    void _handleMentalHealthSubmission(NotificationProvider provider) {
        final assessment = {
            'optimism': _optimismScore,
            'resilience': _resilienceScore,
            'timestamp': DateFormat('yyyy-MM-dd HH:mm').format(DateTime.now()),
        };
        provider.updateMentalHealthAssessment(assessment);
        String message = _optimismScore > 3
            ? 'أنت شخص متفائل! استمر في نشر الإيجابية.'
            : 'لا تقلق، كل يوم فرصة جديدة للتفاؤل!';
        provider.addNotification({
            'message': message,
            'timestamp': assessment['timestamp'],
            'type': 'psychological_support',
        });
        _scheduleNotification(
            title: 'رسالة دعم نفسي',
            body: message,
            scheduledTime: DateTime.now().add(const Duration(hours: 24)),
            id: provider.notifications.length,
        );
        setState(() => _showMentalHealthForm = false);
    }

    void _handleNoteSubmission(NotificationProvider provider) {
        if (_noteTitleController.text.isNotEmpty &&
            _noteContentController.text.isNotEmpty &&
            _noteDateController.text.isNotEmpty) {
            final note = {
                'title': _noteTitleController.text,
                'content': _noteContentController.text,
                'date': _noteDateController.text,
                'url': _noteUrlController.text,
                'timestamp': DateFormat('yyyy-MM-dd HH:mm').format(DateTime.now()),
                'type': 'events',
            };
            provider.addNoteOrEvent(note);
            provider.addNotification({
                'message': note['title'],
                'timestamp': note['timestamp'],
                'type': 'events',
            });
            final scheduledTime = DateTime.parse(_noteDateController.text);
            _scheduleNotification(
                title: 'تذكير بالحدث',
                body: note['title'] ?? '',
                scheduledTime: scheduledTime,
                id: provider.notifications.length,
            );
            _noteTitleController.clear();
            _noteContentController.clear();
            _noteDateController.clear();
            _noteUrlController.clear();
            setState(() => _showNotesForm = false);
        }
    }

    Widget _buildNotificationsList() {
        if (_isLoading) {
            return const SizedBox(
                height: 200,
                child: Center(child: CircularProgressIndicator()),
            );
        }
        if (_error != null) {
            return Container(
                height: 100,
                padding: const EdgeInsets.all(16),
                child: Center(
                    child: Text(
                        'حدث خطأ أثناء جلب الإشعارات: $_error',
                        textAlign: TextAlign.center,
                        style: const TextStyle(color: Colors.white),
                    ),
                ),
            );
        }
        if (_notifications.isEmpty) {
            return const SizedBox(
                height: 100,
                child: Center(
                    child: Text(
                        'لا توجد إشعارات حاليًا',
                        style: TextStyle(color: Colors.white70, fontSize: 16),
                    ),
                ),
            );
        }
        
        return Container(
            margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
            ),
            child: ListView.separated(
                physics: const NeverScrollableScrollPhysics(),
                shrinkWrap: true,
                padding: EdgeInsets.zero,
                itemCount: _notifications.length,
                separatorBuilder: (_, __) => const Divider(height: 1, color: Colors.white24),
                itemBuilder: (context, index) {
                    final notif = _notifications[index];
                    IconData icon;
                    switch (notif['type']) {
                        case 'medication':
                            icon = Icons.medication;
                            break;
                        case 'psychological_support':
                            icon = Icons.psychology;
                            break;
                        case 'events':
                            icon = Icons.event;
                            break;
                        default:
                            icon = Icons.notifications;
                    }
                    return ZoomIn(
                        duration: Duration(milliseconds: 300 + (index * 100)),
                        child: ListTile(
                            contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                            leading: Hero(
                                tag: 'notif_icon_$index',
                                child: Container(
                                    padding: const EdgeInsets.all(8),
                                    decoration: BoxDecoration(
                                        color: Colors.white.withOpacity(0.2),
                                        shape: BoxShape.circle,
                                    ),
                                    child: Icon(icon, color: Colors.white, size: 20),
                                ),
                            ),
                            title: Text(
                                notif['message'] ?? '',
                                style: const TextStyle(
                                    fontFamily: 'Tajawal',
                                    color: Colors.white,
                                    fontWeight: FontWeight.w500,
                                    fontSize: 14,
                                ),
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                            ),
                            subtitle: Padding(
                                padding: const EdgeInsets.only(top: 4.0),
                                child: Text(
                                    notif['timestamp'] ?? '',
                                    style: const TextStyle(
                                        fontFamily: 'Tajawal',
                                        color: Colors.white70,
                                        fontSize: 12,
                                    ),
                                ),
                            ),
                            onTap: () {},
                        ),
                    );
                },
            ),
        );
    }
}