import 'package:shared_preferences/shared_preferences.dart';

class FavoritesManager {
  static const String _favoritesKey = 'favorite_pages';
  
  // Add a page to favorites
  static Future<void> addFavorite(int index) async {
    final prefs = await SharedPreferences.getInstance();
    final favorites = await getFavorites();
    if (!favorites.contains(index)) {
      favorites.add(index);
      await prefs.setStringList(_favoritesKey, favorites.map((e) => e.toString()).toList());
    }
  }
  
  // Remove a page from favorites
  static Future<void> removeFavorite(int index) async {
    final prefs = await SharedPreferences.getInstance();
    final favorites = await getFavorites();
    favorites.remove(index);
    await prefs.setStringList(_favoritesKey, favorites.map((e) => e.toString()).toList());
  }
  
  // Get all favorite page indices
  static Future<List<int>> getFavorites() async {
    final prefs = await SharedPreferences.getInstance();
    final favorites = prefs.getStringList(_favoritesKey) ?? [];
    return favorites.map((e) => int.parse(e)).toList();
  }
  
  // Check if a page is in favorites
  static Future<bool> isFavorite(int index) async {
    final favorites = await getFavorites();
    return favorites.contains(index);
  }
  
  // Toggle favorite status
  static Future<void> toggleFavorite(int index) async {
    if (await isFavorite(index)) {
      await removeFavorite(index);
    } else {
      await addFavorite(index);
    }
  }
}
