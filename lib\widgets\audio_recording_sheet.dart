import 'dart:async';
import 'package:flutter/material.dart';
import '../services/audio_service.dart';

class AudioRecordingSheet extends StatefulWidget {
  final AudioService audioService;

  const AudioRecordingSheet({super.key, required this.audioService});

  @override
  State<AudioRecordingSheet> createState() => _AudioRecordingSheetState();
}

class _AudioRecordingSheetState extends State<AudioRecordingSheet> {
  bool _isRecording = false;
  int _recordingDuration = 0;
  Timer? _timer;

  @override
  void initState() {
    super.initState();
    _startRecording();
  }

  Future<void> _startRecording() async {
    try {
      await widget.audioService.startRecording(onDurationChanged: (duration) {
        setState(() {
          _recordingDuration = duration;
        });
      });
      setState(() {
        _isRecording = true;
      });
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('خطأ في بدء التسجيل: $e', style: const TextStyle(fontFamily: 'Tajawal'))),
      );
      Navigator.pop(context);
    }
  }

  Future<void> _stopRecording() async {
    if (_isRecording) {
      final path = await widget.audioService.stopRecording();
      Navigator.pop(context, path);
    }
  }

  Future<void> _cancelRecording() async {
    if (_isRecording) {
      await widget.audioService.cancelRecording();
      Navigator.pop(context);
    }
  }

  String _formatDuration(int seconds) {
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 200,
      decoration: BoxDecoration(
        color: Colors.grey[900],
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            _isRecording ? 'جاري التسجيل...' : 'اضغط للبدء',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontFamily: 'Tajawal',
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 10),
          Text(
            _formatDuration(_recordingDuration),
            style: const TextStyle(
              color: Colors.white,
              fontSize: 24,
              fontFamily: 'Tajawal',
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 20),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              // زر الإلغاء
              CircleAvatar(
                radius: 25,
                backgroundColor: Colors.red,
                child: IconButton(
                  icon: const Icon(Icons.close, color: Colors.white),
                  onPressed: _cancelRecording,
                ),
              ),
              // زر التسجيل/الإيقاف
              CircleAvatar(
                radius: 35,
                backgroundColor: _isRecording ? Colors.red : Colors.green,
                child: IconButton(
                  icon: Icon(
                    _isRecording ? Icons.stop : Icons.mic,
                    color: Colors.white,
                    size: 30,
                  ),
                  onPressed: _stopRecording,
                ),
              ),
              // زر الإرسال
              CircleAvatar(
                radius: 25,
                backgroundColor: Colors.green,
                child: IconButton(
                  icon: const Icon(Icons.send, color: Colors.white),
                  onPressed: _stopRecording,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }
}