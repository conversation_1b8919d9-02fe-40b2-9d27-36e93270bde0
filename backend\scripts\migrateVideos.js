const mongoose = require('mongoose');
require('dotenv').config({ path: '../.env' });
const Video = require('../src/models/Videomodel');

async function migrateVideos() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGO_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });

    console.log('Connected to MongoDB');

    // Update all existing videos with default values for new fields
    const result = await Video.updateMany(
      {},
      {
        $set: {
          category: 'تعليمي',
          ageGroup: '0-3',
          duration: 0
        }
      },
      { upsert: false, multi: true }
    );

    console.log(`Updated ${result.nModified} videos with default values`);
    console.log('Migration completed successfully');
    process.exit(0);
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  }
}

migrateVideos();