import React, { useEffect, useState } from "react";
import axios from "axios";

const FriendsPage = () => {
  const [friends, setFriends] = useState([]);
  useEffect(() => {
    axios.get("/api/users/me/friends").then(res => setFriends(res.data.data));
  }, []);
  return (
    <div style={{ padding: 32 }}>
      <h2>الأصدقاء (متابعين متبادلين)</h2>
      <ul>
        {friends.map(f => (
          <li key={f._id}>{f.name}</li>
        ))}
      </ul>
    </div>
  );
};
export default FriendsPage;
