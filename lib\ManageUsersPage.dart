import 'package:flutter/material.dart';

class ManageUsersPage extends StatelessWidget {
  const ManageUsersPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة المستخدمين', style: TextStyle(fontFamily: 'Tajawal')),
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          ListTile(
            leading: const Icon(Icons.person),
            title: const Text('أحمد', style: TextStyle(fontFamily: 'Tajawal')),
            subtitle: const Text('<EMAIL>'),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                PopupMenuButton<String>(
                  icon: const Icon(Icons.upgrade),
                  onSelected: (role) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('تمت ترقية المستخدم إلى: ${(role == 'mogtahdin' ? 'مجتهد' : role == 'olama' ? 'عالم' : 'حكيم')}',
                        ),
                      ),
                    );
                  },
                  itemBuilder: (context) => [
                    const PopupMenuItem(value: 'mogtahdin', child: Text('ترقية إلى مجتهد')),
                    const PopupMenuItem(value: 'olama', child: Text('ترقية إلى عالم')),
                    const PopupMenuItem(value: 'hokama', child: Text('ترقية إلى حكيم')),
                  ],
                ),
                IconButton(icon: const Icon(Icons.edit), onPressed: () {}),
                IconButton(icon: const Icon(Icons.delete), onPressed: () {}),
              ],
            ),
          ),
          ListTile(
            leading: const Icon(Icons.person),
            title: const Text('سارة', style: TextStyle(fontFamily: 'Tajawal')),
            subtitle: const Text('<EMAIL>'),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                PopupMenuButton<String>(
                  icon: const Icon(Icons.upgrade),
                  onSelected: (role) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('تمت ترقية المستخدم إلى: ${(role == 'mogtahdin' ? 'مجتهد' : role == 'olama' ? 'عالم' : 'حكيم')}',
                        ),
                      ),
                    );
                  },
                  itemBuilder: (context) => [
                    const PopupMenuItem(value: 'mogtahdin', child: Text('ترقية إلى مجتهد')),
                    const PopupMenuItem(value: 'olama', child: Text('ترقية إلى عالم')),
                    const PopupMenuItem(value: 'hokama', child: Text('ترقية إلى حكيم')),
                  ],
                ),
                IconButton(icon: const Icon(Icons.edit), onPressed: () {}),
                IconButton(icon: const Icon(Icons.delete), onPressed: () {}),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
