// نموذج البلاغ عن إساءة استخدام أو محتوى
class ReportModel {
  final String id;
  final String reporterId; // مُقدّم البلاغ
  final String reportedId; // المُبلغ عنه (مستخدم أو منشور)
  final String type; // نوع البلاغ: user/post/comment...
  final String reason; // سبب البلاغ
  final DateTime createdAt;
  final String? details;

  ReportModel({
    required this.id,
    required this.reporterId,
    required this.reportedId,
    required this.type,
    required this.reason,
    required this.createdAt,
    this.details,
  });

  factory ReportModel.fromJson(Map<String, dynamic> json) {
    return ReportModel(
      id: json['id'],
      reporterId: json['reporterId'],
      reportedId: json['reportedId'],
      type: json['type'],
      reason: json['reason'],
      createdAt: DateTime.parse(json['createdAt']),
      details: json['details'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'reporterId': reporterId,
      'reportedId': reportedId,
      'type': type,
      'reason': reason,
      'createdAt': createdAt.toIso8601String(),
      'details': details,
    };
  }
}
