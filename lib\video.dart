import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:animate_do/animate_do.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:lottie/lottie.dart';
import 'package:vibration/vibration.dart';
import 'package:share_plus/share_plus.dart';
import 'package:video_player/video_player.dart';
import 'package:chewie/chewie.dart';
import 'package:intl/intl.dart';
import 'package:timeago/timeago.dart' as timeago;
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'rewards_manager.dart';
import 'appstate.dart';
import 'models/video_model.dart';
import 'main.dart';
import 'models/comment_model.dart';

class RelatedVideosScreen extends StatefulWidget {
  const RelatedVideosScreen({super.key});

  @override
  State<RelatedVideosScreen> createState() => _RelatedVideosScreenState();
}

class _RelatedVideosScreenState extends State<RelatedVideosScreen>
    with TickerProviderStateMixin {
  final ScrollController _scrollController = ScrollController();
  String _selectedCategory = 'All';

  @override
  void initState() {
    super.initState();
    final appState = Provider.of<AppState>(context, listen: false);
    _scrollController.addListener(() {
      if (_scrollController.position.pixels >=
          _scrollController.position.maxScrollExtent - 200 &&
          !appState.isLoadingMore) {
        appState.loadMoreVideos(category: _selectedCategory);
      }
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _showAddVideoDialog(BuildContext context) async {
    final TextEditingController titleController = TextEditingController();
    final TextEditingController descriptionController = TextEditingController();
    final TextEditingController urlController = TextEditingController();
    final TextEditingController categoryController = TextEditingController();

    return showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'إضافة فيديو جديد',
          style: TextStyle(fontFamily: 'Tajawal'),
        ),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: titleController,
                decoration: const InputDecoration(
                  labelText: 'عنوان الفيديو',
                  hintText: 'أدخل عنوان الفيديو',
                ),
              ),
              TextField(
                controller: descriptionController,
                decoration: const InputDecoration(
                  labelText: 'وصف الفيديو',
                  hintText: 'أدخل وصف الفيديو',
                ),
                maxLines: 3,
              ),
              TextField(
                controller: urlController,
                decoration: const InputDecoration(
                  labelText: 'رابط الفيديو',
                  hintText: 'أدخل رابط الفيديو',
                ),
              ),
              TextField(
                controller: categoryController,
                decoration: const InputDecoration(
                  labelText: 'التصنيف',
                  hintText: 'أدخل تصنيف الفيديو',
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'إلغاء',
              style: TextStyle(fontFamily: 'Tajawal'),
            ),
          ),
          TextButton(
            onPressed: () async {
              if (titleController.text.isNotEmpty &&
                  urlController.text.isNotEmpty &&
                  categoryController.text.isNotEmpty) {
                try {
                  final appState = Provider.of<AppState>(context, listen: false);
                  await appState.addVideoContent(
                    title: titleController.text,
                    description: descriptionController.text,
                    videoUrl: urlController.text,
                    category: categoryController.text,
                  );
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('تم إضافة الفيديو بنجاح'),
                      backgroundColor: Colors.green,
                    ),
                  );
                } catch (e) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('حدث خطأ أثناء إضافة الفيديو: $e'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              } else {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('يرجى ملء جميع الحقول المطلوبة'),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            },
            child: Text(
              'إضافة',
              style: TextStyle(fontFamily: 'Tajawal'),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final appState = Provider.of<AppState>(context);
    return Theme(
      data: MyApp.themes[appState.selectedTheme]?['themeData'] ??
          MyApp.themes['Default']!['themeData'],
      child: Scaffold(
        floatingActionButton: Consumer<AppState>(
          builder: (context, appState, _) => appState.userRole == 'hokama'
              ? FloatingActionButton(
            onPressed: () => _showAddVideoDialog(context),
            tooltip: 'إضافة فيديو جديد',
            backgroundColor: Colors.deepPurple,
            child: Icon(Icons.add_to_queue, color: Colors.white),
          )
              : Container(),
        ),
        body: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Colors.black87,
                Colors.deepPurple.withOpacity(0.8),
              ],
            ),
          ),
          child: SafeArea(
            child: RefreshIndicator(
              onRefresh: () async {
                await appState.loadMoreVideos(
                    category: _selectedCategory, refresh: true);
              },
              color: Colors.purpleAccent,
              backgroundColor: Colors.black54,
              child: CustomScrollView(
                controller: _scrollController,
                slivers: [
                  _buildAppBar(),
                  _buildCategoryFilter(),
                  SliverPadding(
                    padding: const EdgeInsets.all(16),
                    sliver: SliverGrid(
                      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 2,
                        crossAxisSpacing: 16,
                        mainAxisSpacing: 16,
                        childAspectRatio: 0.75,
                      ),
                      delegate: SliverChildBuilderDelegate(
                            (context, index) {
                          if (index == appState.videos.length) {
                            return _buildLoadingIndicator();
                          }
                          final video = appState.videos[index];
                          return _buildVideoCard(video, index);
                        },
                        childCount: appState.videos.length +
                            (appState.isLoadingMore ? 1 : 0),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  SliverAppBar _buildAppBar() {
    return SliverAppBar(
      floating: true,
      pinned: true,
      backgroundColor: Colors.black.withOpacity(0.5),
      title: FadeInDown(
        child: Text(
          'استكشف الفيديوهات',
          style: TextStyle(
            fontFamily: 'Tajawal',
            fontWeight: FontWeight.bold,
            color: Colors.white,
            shadows: [
              Shadow(
                blurRadius: 10,
                color: Colors.purpleAccent,
                offset: Offset(0, 0),
              ),
            ],
          ),
        ),
      ),
      leading: IconButton(
        icon: Icon(Icons.arrow_back, color: Colors.white),
        onPressed: () => Navigator.pop(context),
      ),
      actions: [
        IconButton(
          icon: Icon(Icons.search, color: Colors.white),
          onPressed: () {
            showSearch(context: context, delegate: VideoSearchDelegate());
          },
        ),
      ],
    );
  }

  SliverToBoxAdapter _buildCategoryFilter() {
    final categories = [
      'All',
      'Trending',
      'New',
      'Recommended',
      'Music',
      'Gaming'
    ];
    return SliverToBoxAdapter(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            children: categories.map((category) {
              final isSelected = _selectedCategory == category;
              return Padding(
                padding: const EdgeInsets.symmetric(horizontal: 4),
                child: ZoomIn(
                  child: ChoiceChip(
                    label: Text(
                      category,
                      style: TextStyle(
                        fontFamily: 'Tajawal',
                        color: isSelected ? Colors.white : Colors.white70,
                      ),
                    ),
                    selected: isSelected,
                    selectedColor: Colors.purpleAccent,
                    backgroundColor: Colors.black.withOpacity(0.5),
                    onSelected: (selected) {
                      if (selected) {
                        setState(() => _selectedCategory = category);
                        context
                            .read<AppState>()
                            .loadMoreVideos(category: category, refresh: true);
                        Vibration.vibrate(duration: 50);
                      }
                    },
                  ),
                ),
              );
            }).toList(),
          ),
        ),
      ),
    );
  }

  Widget _buildLoadingIndicator() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: SpinKitFadingGrid(
        color: Colors.purpleAccent,
        size: 50,
        controller: AnimationController(
          vsync: this,
          duration: Duration(milliseconds: 1200),
        ),
      ),
    );
  }

  Widget _buildVideoCard(Video video, int index) {
    return FadeInUp(
      duration: Duration(milliseconds: 300 + (index * 100)),
      child: GestureDetector(
        onDoubleTap: () {
          context
              .read<AppState>()
              .toggleLikeVideo(video.id, video.userReaction == 'like');
          Vibration.vibrate(duration: 100);
        },
        child: Card(
          elevation: 8,
          shape:
          RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          color: Colors.black.withOpacity(0.3),
          margin: const EdgeInsets.symmetric(vertical: 8),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildVideoThumbnail(video),
              Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      video.title,
                      style: TextStyle(
                        fontFamily: 'Tajawal',
                        fontWeight: FontWeight.bold,
                        fontSize: 18,
                        color: Colors.white,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      video.description,
                      style: TextStyle(
                        fontFamily: 'Tajawal',
                        color: Colors.white.withOpacity(0.7),
                      ),
                      maxLines: 3,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 12),
                    _buildReactionBar(video),
                    const Divider(color: Colors.white24),
                    _buildCommentSection(video),
                    _buildShareButton(video),
                    const SizedBox(height: 8),
                    _buildVideoStats(video),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildVideoStats(Video video) {
    try {
      timeago.setLocaleMessages('ar', timeago.ArMessages());
    } catch (e) {
      // Locale already set, ignore
    }

    final String formattedViewCount = video.viewCount != null
        ? NumberFormat.compact().format(video.viewCount)
        : '0';
    final String timeAgo = video.publishDate != null
        ? timeago.format(video.publishDate!, locale: 'ar')
        : 'غير معروف';

    return Padding(
      padding: const EdgeInsets.only(top: 8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          if (video.authorName != null && video.authorName!.isNotEmpty)
            Expanded(
              child: Row(
                children: [
                  Icon(Icons.person_outline, color: Colors.white70, size: 16),
                  const SizedBox(width: 4),
                  Flexible(
                    child: Text(
                      video.authorName!,
                      style: TextStyle(
                        fontFamily: 'Tajawal',
                        color: Colors.white.withOpacity(0.7),
                        fontSize: 12,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),
          Row(
            children: [
              Icon(Icons.visibility_outlined, color: Colors.white70, size: 16),
              const SizedBox(width: 4),
              Text(
                '$formattedViewCount مشاهدة',
                style: TextStyle(
                  fontFamily: 'Tajawal',
                  color: Colors.white.withOpacity(0.7),
                  fontSize: 12,
                ),
              ),
              const SizedBox(width: 12),
              Icon(Icons.access_time_outlined, color: Colors.white70, size: 16),
              const SizedBox(width: 4),
              Text(
                timeAgo,
                style: TextStyle(
                  fontFamily: 'Tajawal',
                  color: Colors.white.withOpacity(0.7),
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildVideoThumbnail(Video video) {
    return Stack(
      alignment: Alignment.center,
      children: [
        ClipRRect(
          borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
          child: CachedNetworkImage(
            imageUrl: video.thumbnail ?? 'https://via.placeholder.com/300',
            height: 200,
            width: double.infinity,
            fit: BoxFit.cover,
            placeholder: (context, url) => Container(
              height: 200,
              color: Colors.grey,
              child: Center(
                  child: CircularProgressIndicator(color: Colors.purpleAccent)),
            ),
            errorWidget: (context, url, error) => Container(
              height: 200,
              color: Colors.grey,
              child: Icon(Icons.broken_image, color: Colors.white),
            ),
          ),
        ),
        Positioned.fill(
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.transparent,
                  Colors.black.withOpacity(0.5),
                ],
              ),
            ),
          ),
        ),
        ZoomIn(
          child: IconButton(
            icon: Icon(Icons.play_circle_fill, size: 60, color: Colors.white),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) =>
                      FullScreenVideoPlayer(videoUrl: video.videoUrl),
                ),
              );
              Vibration.vibrate(duration: 50);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildReactionBar(Video video) {
    return GestureDetector(
      onLongPress: () => _showReactionPicker(video),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _ReactionButton(
            icon: Icons.thumb_up,
            label: '${video.reactions['like'] ?? 0}',
            isActive: video.userReaction == 'like',
            lottieAsset: 'assets/animations/like.json',
            onPressed: () {
              context
                  .read<AppState>()
                  .toggleLikeVideo(video.id, video.userReaction == 'like');
              Vibration.vibrate(duration: 50);
            },
          ),
          _ReactionButton(
            icon: Icons.favorite,
            label: '${video.reactions['love'] ?? 0}',
            isActive: video.userReaction == 'love',
            lottieAsset: 'assets/animations/love.json',
            onPressed: () {
              context.read<AppState>().addReactionToVideo(video.id, 'love');
              Vibration.vibrate(duration: 50);
            },
          ),
          _ReactionButton(
            icon: Icons.tag_faces,
            label: '${video.reactions['haha'] ?? 0}',
            isActive: video.userReaction == 'haha',
            lottieAsset: 'assets/animations/haha.json',
            onPressed: () {
              context.read<AppState>().addReactionToVideo(video.id, 'haha');
              Vibration.vibrate(duration: 50);
            },
          ),
        ],
      ),
    );
  }

  void _showReactionPicker(Video video) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.black.withOpacity(0.8),
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        height: 100,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _ReactionOption(
              lottieAsset: 'assets/animations/like.json',
              onTap: () {
                context.read<AppState>().addReactionToVideo(video.id, 'like');
                Navigator.pop(context);
                Vibration.vibrate(duration: 100);
              },
            ),
            _ReactionOption(
              lottieAsset: 'assets/animations/love.json',
              onTap: () {
                context.read<AppState>().addReactionToVideo(video.id, 'love');
                Navigator.pop(context);
                Vibration.vibrate(duration: 100);
              },
            ),
            _ReactionOption(
              lottieAsset: 'assets/animations/haha.json',
              onTap: () {
                context.read<AppState>().addReactionToVideo(video.id, 'haha');
                Navigator.pop(context);
                Vibration.vibrate(duration: 100);
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCommentItem(Comment comment, Video video, BuildContext context,
      {bool isReply = false}) {
    final appState = Provider.of<AppState>(context, listen: false);
    final currentUser = appState.currentUser;
    final currentUserId = currentUser?.id ?? 'temp_user_id';
    final bool isLikedByCurrentUser = comment.likedBy.contains(currentUserId);

    return Padding(
      padding: EdgeInsets.only(left: isReply ? 30.0 : 0, top: 8.0, bottom: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CircleAvatar(
                backgroundImage: comment.profileImagePath != null
                    ? CachedNetworkImageProvider(comment.profileImagePath!)
                    : null,
                backgroundColor: Colors.grey.shade800,
                radius: isReply ? 16 : 20,
                child: comment.profileImagePath == null
                    ? Icon(Icons.person,
                    color: Colors.white70, size: isReply ? 18 : 24)
                    : null,
              ),
              SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      comment.username,
                      style: TextStyle(
                        fontFamily: 'Tajawal',
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                        fontSize: isReply ? 13 : 15,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      comment.content,
                      style: TextStyle(
                        fontFamily: 'Tajawal',
                        color: Colors.white70,
                        fontSize: isReply ? 12 : 14,
                      ),
                    ),
                    const SizedBox(height: 6),
                    Row(
                      children: [
                        Text(
                          timeago.format(
                              DateTime.tryParse(comment.date) ?? DateTime.now(),
                              locale: 'ar'),
                          style: TextStyle(
                            fontFamily: 'Tajawal',
                            color: Colors.white54,
                            fontSize: isReply ? 9 : 10,
                          ),
                        ),
                        const SizedBox(width: 12),
                        InkWell(
                          onTap: () {
                            if (currentUser != null) {
                              appState.toggleLikeComment(
                                  video.id, comment.id, currentUserId);
                            } else {
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                    content:
                                    Text('يرجى تسجيل الدخول للإعجاب بالتعليقات')),
                              );
                            }
                          },
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                isLikedByCurrentUser
                                    ? Icons.thumb_up_alt
                                    : Icons.thumb_up_alt_outlined,
                                color: isLikedByCurrentUser
                                    ? Colors.blueAccent
                                    : Colors.white54,
                                size: isReply ? 14 : 16,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                '${comment.likesCount}',
                                style: TextStyle(
                                  fontFamily: 'Tajawal',
                                  color: Colors.white54,
                                  fontSize: isReply ? 11 : 12,
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(width: 12),
                        if (!isReply)
                          InkWell(
                            onTap: () {
                              showModalBottomSheet(
                                context: context,
                                isScrollControlled: true,
                                backgroundColor: Colors.black.withOpacity(0.9),
                                builder: (context) {
                                  final TextEditingController replyController =
                                  TextEditingController();
                                  return Padding(
                                    padding: EdgeInsets.only(
                                        bottom: MediaQuery.of(context)
                                            .viewInsets
                                            .bottom),
                                    child: Container(
                                      padding: const EdgeInsets.all(16),
                                      child: Column(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Text(
                                            'الرد على ${comment.username}',
                                            style: TextStyle(
                                                fontFamily: 'Tajawal',
                                                color: Colors.white,
                                                fontWeight: FontWeight.bold),
                                          ),
                                          SizedBox(height: 16),
                                          TextField(
                                            controller: replyController,
                                            decoration: InputDecoration(
                                              hintText: 'اكتب ردك...',
                                              hintStyle: TextStyle(
                                                  fontFamily: 'Tajawal',
                                                  color: Colors.white
                                                      .withOpacity(0.6)),
                                              filled: true,
                                              fillColor: Colors.white
                                                  .withOpacity(0.1),
                                              border: OutlineInputBorder(
                                                borderRadius:
                                                BorderRadius.circular(12),
                                                borderSide: BorderSide.none,
                                              ),
                                            ),
                                            style: TextStyle(
                                                fontFamily: 'Tajawal',
                                                color: Colors.white),
                                          ),
                                          SizedBox(height: 8),
                                          ElevatedButton.icon(
                                            icon: Icon(Icons.send,
                                                color: Colors.white),
                                            label: Text('إرسال',
                                                style: TextStyle(
                                                    fontFamily: 'Tajawal',
                                                    color: Colors.white)),
                                            onPressed: () {
                                              if (replyController.text
                                                  .isNotEmpty) {
                                                final appState = Provider.of<
                                                    AppState>(context,
                                                    listen: false);
                                                final currentUser =
                                                    appState.currentUser;
                                                if (currentUser != null) {
                                                  appState.addReplyToComment(
                                                    videoId: video.id,
                                                    commentId: comment.id,
                                                    content:
                                                    replyController.text,
                                                    userId: currentUser.id,
                                                  );
                                                  Navigator.pop(context);
                                                  ScaffoldMessenger.of(context)
                                                      .showSnackBar(
                                                    SnackBar(
                                                        content: Text(
                                                            'تم إرسال الرد بنجاح')),
                                                  );
                                                } else {
                                                  ScaffoldMessenger.of(context)
                                                      .showSnackBar(
                                                    SnackBar(
                                                        content: Text(
                                                            'يرجى تسجيل الدخول للرد')),
                                                  );
                                                }
                                              } else {
                                                ScaffoldMessenger.of(context)
                                                    .showSnackBar(
                                                  SnackBar(
                                                      content: Text(
                                                          'لا يمكن إرسال رد فارغ')),
                                                );
                                              }
                                            },
                                            style: ElevatedButton.styleFrom(
                                              backgroundColor:
                                              Colors.purpleAccent,
                                              padding: EdgeInsets.symmetric(
                                                  horizontal: 20, vertical: 12),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  );
                                },
                              );
                            },
                            child: Text(
                              'رد',
                              style: TextStyle(
                                fontFamily: 'Tajawal',
                                color: Colors.blueAccent,
                                fontWeight: FontWeight.bold,
                                fontSize: isReply ? 11 : 12,
                              ),
                            ),
                          ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
          if (comment.replies.isNotEmpty)
            Padding(
              padding: const EdgeInsets.only(top: 8.0),
              child: Column(
                children: comment.replies
                    .map((reply) =>
                    _buildCommentItem(reply, video, context, isReply: true))
                    .toList(),
              ),
            ),
          if (!isReply) Divider(color: Colors.white12, height: 16),
        ],
      ),
    );
  }

  Widget _buildCommentSection(Video video) {
    return TextButton(
      onPressed: () {
        showModalBottomSheet(
          context: context,
          isScrollControlled: true,
          backgroundColor: Colors.black.withOpacity(0.9),
          builder: (context) {
            final TextEditingController commentController =
            TextEditingController();
            return DraggableScrollableSheet(
              initialChildSize: 0.9,
              builder: (context, scrollController) {
                return StatefulBuilder(
                  builder: (context, setModalState) {
                    return Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        children: [
                          Text(
                            'التعليقات (${video.comments.length})',
                            style: TextStyle(
                              fontFamily: 'Tajawal',
                              fontSize: 18,
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          TextField(
                            decoration: InputDecoration(
                              hintText: 'أضف تعليقًا...',
                              hintStyle: TextStyle(
                                fontFamily: 'Tajawal',
                                color: Colors.white.withOpacity(0.6),
                              ),
                              filled: true,
                              fillColor: Colors.white.withOpacity(0.1),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                                borderSide: BorderSide.none,
                              ),
                            ),
                            style: TextStyle(
                              fontFamily: 'Tajawal',
                              color: Colors.white,
                            ),
                            controller: commentController,
                          ),
                          const SizedBox(height: 8),
                          ElevatedButton.icon(
                            icon: const Icon(Icons.send, color: Colors.white),
                            label: const Text('إرسال',
                                style: TextStyle(
                                    fontFamily: 'Tajawal', color: Colors.white)),
                            onPressed: () {
                              if (commentController.text.isNotEmpty) {
                                final appState = Provider.of<AppState>(context,
                                    listen: false);
                                final currentUser = appState.currentUser;
                                if (currentUser != null) {
                                  print(
                                      'Video ID: ${video.id}, User ID: ${currentUser.id}, Comment: ${commentController.text}');
                                  commentController.clear();
                                } else {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(
                                        content:
                                        Text('يرجى تسجيل الدخول للتعليق')),
                                  );
                                }
                              } else {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                      content: Text('لا يمكن إرسال تعليق فارغ')),
                                );
                              }
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.purpleAccent,
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 20, vertical: 12),
                              textStyle:
                              const TextStyle(fontSize: 16, fontFamily: 'Tajawal'),
                            ),
                          ),
                          const SizedBox(height: 16),
                          Expanded(
                            child: video.comments.isEmpty
                                ? Center(
                              child: Text(
                                'لا توجد تعليقات حتى الآن. كن أول من يعلق!',
                                style: TextStyle(
                                    color: Colors.white70,
                                    fontFamily: 'Tajawal'),
                              ),
                            )
                                : ListView.builder(
                              controller: scrollController,
                              itemCount: video.comments
                                  .where((c) => c.parentCommentId == null)
                                  .length,
                              itemBuilder: (context, index) {
                                final topLevelComments = video.comments
                                    .where((c) => c.parentCommentId == null)
                                    .toList();
                                final comment = topLevelComments[index];
                                return _buildCommentItem(
                                    comment, video, context);
                              },
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                );
              },
            );
          },
        );
      },
      child: Text(
        'عرض التعليقات (${video.comments.length})',
        style: const TextStyle(
          fontFamily: 'Tajawal',
          color: Colors.purpleAccent,
        ),
      ),
    );
  }

  Widget _buildShareButton(Video video) {
    return Align(
      alignment: Alignment.centerRight,
      child: ZoomIn(
        child: TextButton.icon(
          icon: const Icon(Icons.share, color: Colors.white70),
          label: const Text(
            'مشاركة',
            style: TextStyle(
              fontFamily: 'Tajawal',
              color: Colors.white70,
            ),
          ),
          onPressed: () {
            Share.share('شاهد هذا الفيديو الرائع: ${video.videoUrl}');
            Vibration.vibrate(duration: 50);
            final appState = Provider.of<AppState>(context, listen: false);
            appState.addPoints(RewardAction.share);
          },
        ),
      ),
    );
  }
}

class _ReactionButton extends StatelessWidget {
  final IconData icon;
  final String label;
  final bool isActive;
  final String lottieAsset;
  final VoidCallback onPressed;

  const _ReactionButton({
    required this.icon,
    required this.label,
    required this.isActive,
    required this.lottieAsset,
    required this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        GestureDetector(
          onTap: onPressed,
          child: Stack(
            alignment: Alignment.center,
            children: [
              if (isActive)
                Lottie.asset(
                  lottieAsset,
                  height: 50,
                  width: 50,
                  repeat: false,
                ),
              Icon(
                icon,
                color: isActive ? Colors.purpleAccent : Colors.white70,
                size: 30,
              ),
            ],
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontFamily: 'Tajawal',
            color: isActive ? Colors.purpleAccent : Colors.white70,
          ),
        ),
      ],
    );
  }
}

class _ReactionOption extends StatelessWidget {
  final String lottieAsset;
  final VoidCallback onTap;

  const _ReactionOption({
    required this.lottieAsset,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Lottie.asset(
        lottieAsset,
        height: 60,
        width: 60,
      ),
    );
  }
}

class VideoSearchDelegate extends SearchDelegate {
  @override
  List<Widget> buildActions(BuildContext context) {
    return [
      IconButton(
        icon: Icon(Icons.clear),
        onPressed: () => query = '',
      ),
    ];
  }

  @override
  Widget buildLeading(BuildContext context) {
    return IconButton(
      icon: Icon(Icons.arrow_back),
      onPressed: () => close(context, null),
    );
  }

  @override
  Widget buildResults(BuildContext context) {
    final appState = Provider.of<AppState>(context);
    final results = appState.videos
        .where((video) =>
    video.title.toLowerCase().contains(query.toLowerCase()) ||
        video.description.toLowerCase().contains(query.toLowerCase()))
        .toList();

    return ListView.builder(
      itemCount: results.length,
      itemBuilder: (context, index) {
        final video = results[index];
        return ListTile(
          leading: CachedNetworkImage(
            imageUrl: video.thumbnail ?? 'https://via.placeholder.com/150',
            width: 50,
            height: 50,
            fit: BoxFit.cover,
          ),
          title: Text(video.title, style: TextStyle(fontFamily: 'Tajawal')),
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => FullScreenVideoPlayer(videoUrl: video.videoUrl),
              ),
            );
          },
        );
      },
    );
  }

  @override
  Widget buildSuggestions(BuildContext context) {
    return Container();
  }
}

class FullScreenVideoPlayer extends StatefulWidget {
  final String videoUrl;

  const FullScreenVideoPlayer({required this.videoUrl, super.key});

  @override
  State<FullScreenVideoPlayer> createState() => _FullScreenVideoPlayerState();
}

class _FullScreenVideoPlayerState extends State<FullScreenVideoPlayer> {
  late VideoPlayerController _controller;
  late ChewieController _chewieController;
  bool _isNetworkError = false;

  @override
  void initState() {
    super.initState();
    _initializeVideoPlayer();
  }

  Future<void> _initializeVideoPlayer() async {
    var connectivityResult = await Connectivity().checkConnectivity();
    if (connectivityResult == ConnectivityResult.none) {
      setState(() {
        _isNetworkError = true;
      });
      return;
    }

    _controller = VideoPlayerController.networkUrl(Uri.parse(widget.videoUrl));
    try {
      await _controller.initialize();
      _chewieController = ChewieController(
        videoPlayerController: _controller,
        autoPlay: true,
        looping: true,
        materialProgressColors: ChewieProgressColors(
          playedColor: Colors.purpleAccent,
          handleColor: Colors.purple,
          backgroundColor: Colors.black54,
          bufferedColor: Colors.white24,
        ),
        placeholder: Container(
          color: Colors.black,
          child: Center(child: CircularProgressIndicator(color: Colors.purpleAccent)),
        ),
        autoInitialize: true,
        errorBuilder: (context, errorMessage) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.error_outline, color: Colors.red, size: 48),
                const SizedBox(height: 16),
                Text(
                  'لا يمكن تشغيل الفيديو\n$errorMessage',
                  textAlign: TextAlign.center,
                  style: TextStyle(color: Colors.white, fontFamily: 'Tajawal'),
                ),
              ],
            ),
          );
        },
      );
      setState(() {});
    } catch (e) {
      setState(() {
        _isNetworkError = true;
      });
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    _chewieController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: _isNetworkError
          ? Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.wifi_off, color: Colors.red, size: 48),
            const SizedBox(height: 16),
            Text(
              'لا يوجد اتصال بالإنترنت',
              style: TextStyle(
                  color: Colors.white, fontFamily: 'Tajawal', fontSize: 18),
            ),
            TextButton(
              onPressed: () {
                setState(() {
                  _isNetworkError = false;
                  _initializeVideoPlayer();
                });
              },
              child: Text(
                'إعادة المحاولة',
                style: TextStyle(
                    fontFamily: 'Tajawal', color: Colors.purpleAccent),
              ),
            ),
          ],
        ),
      )
          : Chewie(controller: _chewieController),
    );
  }
}
