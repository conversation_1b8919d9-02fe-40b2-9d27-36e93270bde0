const AnalyticsEvent = require('../models/AnalyticsEvent.model');
const ShareBorrowItem = require('../models/ShareBorrowItem.model');

// Log an analytics event
exports.logEvent = async (req, res) => {
  try {
    const { type, itemId, userId, metadata } = req.body;
    
    // Validate event type
    const validTypes = ['view', 'borrow', 'share', 'favorite', 'verify'];
    if (!validTypes.includes(type)) {
      return res.status(400).json({ error: 'Invalid event type' });
    }
    
    // Verify item exists
    const item = await ShareBorrowItem.findById(itemId);
    if (!item) return res.status(404).json({ error: 'Item not found' });
    
    const newEvent = new AnalyticsEvent({
      type,
      itemId,
      userId,
      metadata: metadata || {}
    });
    
    await newEvent.save();
    res.status(201).json(newEvent);
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
};

// Get analytics for all items
exports.getAnalytics = async (req, res) => {
  try {
    // Get event counts by type
    const eventCounts = await AnalyticsEvent.aggregate([
      { $group: { _id: '$type', count: { $sum: 1 } } }
    ]);
    
    // Get most viewed items
    const mostViewed = await AnalyticsEvent.aggregate([
      { $match: { type: 'view' } },
      { $group: { _id: '$itemId', count: { $sum: 1 } } },
      { $sort: { count: -1 } },
      { $limit: 5 }
    ]);
    
    // Get most borrowed items
    const mostBorrowed = await AnalyticsEvent.aggregate([
      { $match: { type: 'borrow' } },
      { $group: { _id: '$itemId', count: { $sum: 1 } } },
      { $sort: { count: -1 } },
      { $limit: 5 }
    ]);
    
    // Get most active users
    const mostActiveUsers = await AnalyticsEvent.aggregate([
      { $group: { _id: '$userId', count: { $sum: 1 } } },
      { $sort: { count: -1 } },
      { $limit: 5 }
    ]);
    
    // Get events by date
    const eventsByDate = await AnalyticsEvent.aggregate([
      {
        $group: {
          _id: {
            year: { $year: '$timestamp' },
            month: { $month: '$timestamp' },
            day: { $dayOfMonth: '$timestamp' }
          },
          count: { $sum: 1 }
        }
      },
      { $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 } }
    ]);
    
    // Format the results
    const formattedEventsByDate = eventsByDate.map(item => ({
      date: `${item._id.year}-${item._id.month.toString().padStart(2, '0')}-${item._id.day.toString().padStart(2, '0')}`,
      count: item.count
    }));
    
    // Populate item details for most viewed and borrowed
    const populateItemDetails = async (items) => {
      const result = [];
      for (const item of items) {
        const itemDetails = await ShareBorrowItem.findById(item._id);
        if (itemDetails) {
          result.push({
            item: {
              id: itemDetails._id,
              title: itemDetails.title,
              category: itemDetails.category
            },
            count: item.count
          });
        }
      }
      return result;
    };
    
    const populatedMostViewed = await populateItemDetails(mostViewed);
    const populatedMostBorrowed = await populateItemDetails(mostBorrowed);
    
    res.json({
      eventCounts: eventCounts.reduce((acc, curr) => {
        acc[curr._id] = curr.count;
        return acc;
      }, {}),
      mostViewed: populatedMostViewed,
      mostBorrowed: populatedMostBorrowed,
      mostActiveUsers,
      eventsByDate: formattedEventsByDate
    });
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};

// Get analytics for a specific item
exports.getItemAnalytics = async (req, res) => {
  try {
    const { itemId } = req.params;
    
    // Verify item exists
    const item = await ShareBorrowItem.findById(itemId);
    if (!item) return res.status(404).json({ error: 'Item not found' });
    
    // Get event counts by type for this item
    const eventCounts = await AnalyticsEvent.aggregate([
      { $match: { itemId } },
      { $group: { _id: '$type', count: { $sum: 1 } } }
    ]);
    
    // Get events by date for this item
    const eventsByDate = await AnalyticsEvent.aggregate([
      { $match: { itemId } },
      {
        $group: {
          _id: {
            year: { $year: '$timestamp' },
            month: { $month: '$timestamp' },
            day: { $dayOfMonth: '$timestamp' }
          },
          count: { $sum: 1 }
        }
      },
      { $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 } }
    ]);
    
    // Format the results
    const formattedEventsByDate = eventsByDate.map(item => ({
      date: `${item._id.year}-${item._id.month.toString().padStart(2, '0')}-${item._id.day.toString().padStart(2, '0')}`,
      count: item.count
    }));
    
    res.json({
      item: {
        id: item._id,
        title: item.title,
        category: item.category
      },
      eventCounts: eventCounts.reduce((acc, curr) => {
        acc[curr._id] = curr.count;
        return acc;
      }, {}),
      eventsByDate: formattedEventsByDate
    });
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};
