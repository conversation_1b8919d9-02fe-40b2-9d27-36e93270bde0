const express = require('express');
const router = express.Router();
const { check } = require('express-validator');
const auth = require('../middleware/auth');
const {
  getProfessions,
  getProfessionById,
  getCommunityMessages,
  sendMessage,
  getContributions,
  addContribution,
  getChallenges,
  getLeaderboard,
  participateInChallenge,
  rateContribution,
  trackDownload,
  getUserPoints,
  updateUserPoints,
  createProfession,
  updateProfession,
  deleteProfession
} = require('../controllers/profession.controller');

// Public routes - Anyone can view professions and related content
router.get('/', getProfessions);
router.get('/:id', getProfessionById);
router.get('/:professionId/messages', getCommunityMessages);
router.get('/:professionId/contributions', getContributions);
router.get('/challenges/all', getChallenges);
router.get('/leaderboard', getLeaderboard);

// Protected routes for creating/updating professions
// Only hokama and olama can create/edit professions
router.post('/', [
  auth,
  auth.authorize('admin', 'hokama', 'olama'),
  check('title', 'Title is required').not().isEmpty(),
  check('description', 'Description is required').not().isEmpty(),
  check('category', 'Category is required').isIn(['technical', 'creative', 'service', 'health', 'education', 'social'])
], createProfession);

router.put('/:id', [
  auth,
  auth.authorize('admin', 'hokama', 'olama'),
  check('title', 'Title is required').not().isEmpty(),
  check('description', 'Description is required').not().isEmpty()
], updateProfession);

router.delete('/:id', [
  auth,
  auth.authorize('admin', 'hokama') // Only hokama and admin can delete professions
], deleteProfession);

// Protected routes (require authentication)
// Messages can be sent by all authenticated users except guests
router.post('/:professionId/messages', [
  auth,
  auth.authorize('admin', 'hokama', 'olama', 'mogtahdin', 'member'),
  check('content', 'Content is required').not().isEmpty()
], sendMessage);

// Contributions can be made by olama and higher roles
router.post('/:professionId/contributions', [
  auth,
  auth.authorize('admin', 'hokama', 'olama'),
  [
    check('title', 'Title is required').not().isEmpty(),
    check('description', 'Description is required').not().isEmpty(),
    check('type', 'Type is required').isIn(['link', 'document', 'video', 'other']),
    check('url', 'Valid URL is required').isURL()
  ]
], addContribution);

// Challenge participation open to all authenticated users
router.post('/challenges/:challengeId/participate', [
  auth,
  auth.authorize('admin', 'hokama', 'olama', 'mogtahdin', 'member'),
  check('progress', 'Progress is required').isNumeric()
], participateInChallenge);

// Contribution rating - can be done by all authenticated users except guests
router.post('/contributions/:contributionId/rate', [
  auth,
  auth.authorize('admin', 'hokama', 'olama', 'mogtahdin', 'member'),
  [
    check('rating', 'Rating is required').isFloat({ min: 1, max: 5 }),
    check('comment', 'Comment is required').optional().isString()
  ]
], rateContribution);

// Track download - can be done by all authenticated users except guests
router.post('/contributions/:contributionId/download', [
  auth,
  auth.authorize('admin', 'hokama', 'olama', 'mogtahdin', 'member')
], trackDownload);

// User points - viewing points is open to all authenticated users
router.get('/users/points', [
  auth,
  auth.authorize('admin', 'hokama', 'olama', 'mogtahdin', 'member', 'guest')
], getUserPoints);

// Updating points requires hokama or admin privileges
router.post('/users/points', [
  auth,
  auth.authorize('admin', 'hokama'),
  [
    check('points', 'Points are required').isInt(),
    check('reason', 'Reason is required').isString()
  ]
], updateUserPoints);

module.exports = router;
