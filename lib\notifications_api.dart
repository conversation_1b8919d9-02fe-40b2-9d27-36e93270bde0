import 'dart:convert';
import 'package:http/http.dart' as http;

class NotificationsApi {
  static const String baseUrl = 'http://localhost:3000/api/notifications';

  // جلب كل الإشعارات
  static Future<List<dynamic>> fetchNotifications() async {
    final response = await http.get(Uri.parse(baseUrl));
    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw Exception('فشل في جلب الإشعارات');
    }
  }

  // إضافة إشعار جديد
  static Future<bool> addNotification(Map<String, dynamic> notification) async {
    final response = await http.post(
      Uri.parse(baseUrl),
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode(notification),
    );
    return response.statusCode == 201;
  }
}
