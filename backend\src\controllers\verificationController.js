const asyncHandler = require('../middleware/asyncmiddleware');
const ErrorResponse = require('../utils/errorResponse');
const twilio = require('twilio');

// تهيئة عميل Twilio
const accountSid = process.env.TWILIO_ACCOUNT_SID || '**********************************';
const authToken = process.env.TWILIO_AUTH_TOKEN || '09237e2c808ca3ea14e555f308953cfc';
const client = twilio(accountSid, authToken);

// @desc    إرسال رمز التحقق
// @route   POST /api/auth/send-verification
// @access  Public
exports.sendVerification = asyncHandler(async (req, res, next) => {
  const { phoneNumber, serviceSid, channel = 'sms' } = req.body;

  // التحقق من وجود رقم الهاتف
  if (!phoneNumber) {
    return next(new ErrorResponse('يرجى إدخال رقم الهاتف', 400));
  }

  try {
    // إرسال رمز التحقق
    const verification = await client.verify.v2
      .services(serviceSid)
      .verifications.create({
        to: phoneNumber,
        channel: channel
      });

    res.status(200).json({
      success: true,
      data: {
        status: verification.status,
        to: verification.to
      }
    });
  } catch (error) {
    console.error('Twilio Error:', error);
    return next(new ErrorResponse(`فشل إرسال رمز التحقق: ${error.message}`, 500));
  }
});

// @desc    التحقق من الرمز
// @route   POST /api/auth/verify-code
// @access  Public
exports.verifyCode = asyncHandler(async (req, res, next) => {
  const { phoneNumber, code, serviceSid } = req.body;

  // التحقق من وجود رقم الهاتف والرمز
  if (!phoneNumber || !code) {
    return next(new ErrorResponse('يرجى إدخال رقم الهاتف ورمز التحقق', 400));
  }

  try {
    // التحقق من صحة الرمز
    const verificationCheck = await client.verify.v2
      .services(serviceSid)
      .verificationChecks.create({
        to: phoneNumber,
        code: code
      });

    if (verificationCheck.status === 'approved') {
      res.status(200).json({
        success: true,
        data: {
          status: verificationCheck.status,
          valid: true
        }
      });
    } else {
      return next(new ErrorResponse('رمز التحقق غير صحيح', 400));
    }
  } catch (error) {
    console.error('Twilio Error:', error);
    return next(new ErrorResponse(`فشل التحقق من الرمز: ${error.message}`, 500));
  }
});
