// نموذج الإشعار
const mongoose = require('mongoose');

const NotificationSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  type: {
    type: String, // مثل: like, comment, follow, mention, system
    required: true
  },
  message: {
    type: String,
    required: true
  },
  data: {
    type: Object // بيانات إضافية (مثل: postId, userId)
  },
  isRead: {
    type: Boolean,
    default: false
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
});

module.exports = mongoose.model('Notification', NotificationSchema);
