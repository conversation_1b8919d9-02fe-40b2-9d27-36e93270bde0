import 'services/api_service.dart';
import 'models/profession_model.dart';
import 'models/message_model.dart';
import 'models/contribution_model.dart';
import 'models/challenge_model.dart';

class ProfessionService {
  final ApiService _apiService = ApiService();
  ApiService get apiService => _apiService;
  final String baseUrl = 'https://api.example.com'; // Replace with your actual base URL

  // Professions
  Future<ApiResponse<List<Profession>>> getProfessions() async {
    return await _apiService.get(
      '/api/professions',
      parser: (data) => (data as List).map((item) => Profession.fromJson(item)).toList(),
    );
  }

  // Notifications
  Future<ApiResponse<bool>> markNotificationRead(String notificationId) async {
    return await _apiService.put(
      '/api/notifications/$notificationId/read',
      parser: (data) => true,
    );
  }

  // Community Messages
  Future<ApiResponse<List<Message>>> getCommunityMessages(String professionId) async {
    return await _apiService.get(
      '/api/professions/$professionId/messages',
      parser: (data) => (data as List).map((item) => Message.fromJson(item)).toList(),
    );
  }

  Future<ApiResponse<Message>> sendMessage({
    required String professionId,
    required String content,
  }) async {
    return await _apiService.post(
      '/api/professions/$professionId/messages',
      body: {'content': content},
      parser: (data) => Message.fromJson(data),
    );
  }

  // Contributions
  Future<ApiResponse<List<Contribution>>> getContributions(String professionId) async {
    return await _apiService.get(
      '/api/professions/$professionId/contributions',
      parser: (data) => (data as List).map((item) => Contribution.fromJson(item)).toList(),
    );
  }

  Future<ApiResponse<Contribution>> addContribution({
    required String professionId,
    required String title,
    required String description,
    required String type,
    required String url,
  }) async {
    return await _apiService.post(
      '/api/professions/$professionId/contributions',
      body: {
        'title': title,
        'description': description,
        'type': type,
        'url': url,
      },
      parser: (data) => Contribution.fromJson(data),
    );
  }

  // Challenges
  Future<ApiResponse<List<Challenge>>> getChallenges() async {
    return await _apiService.get(
      '/api/challenges',
      parser: (data) => (data as List).map((item) => Challenge.fromJson(item)).toList(),
    );
  }

  // Leaderboard
  Future<ApiResponse<List<Map<String, dynamic>>>> getLeaderboard() async {
    return await _apiService.get(
      '/api/leaderboard',
      parser: (data) => (data as List).cast<Map<String, dynamic>>(),
    );
  }
}
