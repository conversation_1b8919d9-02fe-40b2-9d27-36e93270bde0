# منصة فلك

تطبيق Flutter يساعد المستخدمين على استكشاف المهن والوظائف، تصفح تفاصيلها، معرفة المهارات المطلوبة، ومتابعة أحدث الفرص. التطبيق يستهدف المستخدمين الذين يرغبون في اكتشاف فرص العمل والمهن المتاحة.

## المتطلبات التقنية
- Flutter SDK 3.0 أو أحدث
- Dart SDK 3.0.0 أو أحدث
- Node.js 18+ للخادم الخلفي
- MongoDB للقاعدة البيانات
- اتصال إنترنت مستقر
- متغيرات بيئة (.env) محددة

## المتغيرات البيئية المطلوبة
قم بإنشاء ملف `.env` في الجذر مع المتغيرات التالية:
```
MONGODB_URI=your_mongodb_connection_string
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_supabase_anon_key
AGORA_APP_ID=your_agora_app_id
```

## التثبيت والتشغيل

### 1. تثبيت التبعيات
```bash
# تثبيت تبعيات Flutter
flutter pub get

# تثبيت تبعيات Node.js
npm install
```

### 2. تشغيل الخادم الخلفي
```bash
# تغيير المجلد إلى الخادم
cd backend

# تشغيل الخادم
npm start
```

### 3. تشغيل التطبيق
```bash
# تشغيل التطبيق في الوضع التجريبي
flutter run

# بناء نسخة الإصدار
flutter build apk --release
```

## التشغيل المحلي
```bash
flutter pub get
flutter run
```

## بناء نسخة APK أو Web
```bash
flutter build apk --release
# أو
flutter build web
```

## هيكل المشروع

- `lib/` : الكود الأساسي للتطبيق
  - `models/` : نماذج البيانات
  - `services/` : خدمات API وقاعدة البيانات
  - `screens/` : صفحات التطبيق
  - `widgets/` : مكونات UI قابلة لإعادة الاستخدام
  - `utils/` : أدوات مساعدة
- `backend/` : الخادم الخلفي
  - `src/` : الكود الأساسي
  - `routes/` : نقاط النهاية API
  - `models/` : نماذج MongoDB
- `test/` : اختبارات الوحدة والتكامل

## الميزات الرئيسية

### للمستخدمين العاديين
- تصفح المهن والوظائف
- متابعة التحديثات
- البحث عن فرص العمل
- مشاهدة التفاصيل والمهارات المطلوبة

### للهوكاما (المشرفين)
- إضافة أنواع جديدة من الحيوانات والطيور والأسماك
- إدارة المحتوى
- إضافة مدن وقرى جديدة
- إضافة أمراض جديدة
- إدارة الموارد التعليمية

## التوثيق API

API موثق باستخدام Swagger. يمكنك الوصول إلى وثائق API عبر:
```
http://localhost:3000/api-docs
```

## المساهمة
مرحبًا بأي مساهمة أو اقتراح! يمكنك التواصل عبر الإيميل أو فتح Issue على المستودع.

---
© 2025

## Getting Started

This project is a starting point for a Flutter application.

A few resources to get you started if this is your first Flutter project:

- [Lab: Write your first Flutter app](https://docs.flutter.dev/get-started/codelab)
- [Cookbook: Useful Flutter samples](https://docs.flutter.dev/cookbook)

For help getting started with Flutter development, view the
[online documentation](https://docs.flutter.dev/), which offers tutorials,
samples, guidance on mobile development, and a full API reference.
