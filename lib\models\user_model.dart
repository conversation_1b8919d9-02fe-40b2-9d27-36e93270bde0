import 'package:uuid/uuid.dart';

/// أدوار المستخدم في التطبيق
enum UserRole {
  admin('مدير'),
  ho<PERSON><PERSON>('حكماء'),
  <PERSON><PERSON><PERSON>('علماء'),
  m<PERSON><PERSON><PERSON>('مجتهدين'),
  medical('طبي'),
  member('عضو'),
  guest('ضيف');

  final String label;
  const UserRole(this.label);
  
  /// تحويل النص إلى UserRole
  static UserRole? fromString(String? value) {
    if (value == null) return null;
    try {
      return UserRole.values.firstWhere(
        (e) => e.name.toLowerCase() == value.toLowerCase() || 
              e.label == value,
      );
    } catch (e) {
      return null;
    }
  }
}

/// نموذج بيانات المستخدم
class UserModel {
  final String id;
  final String name;
  final String email;
  final String avatarUrl;
  final UserRole role;
  final double latitude;
  final double longitude;
  final String? phoneNumber;
  final String? bio;
  final DateTime? joinDate;
  final Map<String, dynamic>? preferences;
  final List<String>? interests;
  final bool isVerified;
  final String privacy; // public/private/friends
  final List<String> friends;
  final List<String> followers;
  final int stars;
  final Map<String, dynamic>? additionalData;
  final DateTime? updatedAt;

  UserModel({
    String? id,
    required this.name,
    required this.email,
    this.avatarUrl = '',
    UserRole? role,
    this.latitude = 0,
    this.longitude = 0,
    this.phoneNumber,
    this.bio,
    this.joinDate,
    this.preferences,
    this.interests,
    this.isVerified = false,
    this.privacy = 'public',
    List<String>? friends,
    List<String>? followers,
    this.stars = 0,
    this.additionalData,
    this.updatedAt,
  }) : 
    id = id ?? const Uuid().v4(),
    role = role ?? UserRole.member,
    friends = friends ?? const [],
    followers = followers ?? const [];

  /// إنشاء نموذج من JSON
  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['_id'] ?? json['id'],
      name: json['name'] ?? 'مستخدم بدون اسم',
      email: json['email'] ?? '',
      avatarUrl: json['avatarUrl'] ?? json['avatar'] ?? '',
      role: json['role'] is UserRole 
          ? json['role'] 
          : UserRole.fromString(json['role']) ?? UserRole.member,
      latitude: json['latitude'] is double 
          ? json['latitude'] 
          : (json['latitude'] is num ? (json['latitude'] as num).toDouble() : 0.0),
      longitude: json['longitude'] is double 
          ? json['longitude'] 
          : (json['longitude'] is num ? (json['longitude'] as num).toDouble() : 0.0),
      phoneNumber: json['phoneNumber'] ?? json['phone'],
      bio: json['bio'] ?? json['about'],
      joinDate: json['joinDate'] != null 
          ? DateTime.tryParse(json['joinDate']) 
          : json['createdAt'] != null 
              ? DateTime.tryParse(json['createdAt'])
              : null,
      preferences: json['preferences'] is Map 
          ? Map<String, dynamic>.from(json['preferences']) 
          : null,
      interests: json['interests'] is List 
          ? List<String>.from(json['interests']) 
          : json['interests'] is String 
              ? [json['interests']] 
              : null,
      isVerified: json['isVerified'] == true || json['verified'] == true,
      privacy: json['privacy'] ?? 'public',
      friends: json['friends'] is List ? List<String>.from(json['friends']) : [],
      followers: json['followers'] is List ? List<String>.from(json['followers']) : [],
      stars: (json['stars'] is int 
          ? json['stars'] 
          : (json['stars'] is num ? (json['stars'] as num).toInt() : 0)) ?? 0,
      additionalData: json['additionalData'] is Map 
          ? Map<String, dynamic>.from(json['additionalData']) 
          : null,
      updatedAt: json['updatedAt'] != null 
          ? DateTime.tryParse(json['updatedAt']) 
          : null,
    );
  }

  /// تحويل النموذج إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'avatarUrl': avatarUrl,
      'role': role.name,
      'latitude': latitude,
      'longitude': longitude,
      if (phoneNumber != null) 'phoneNumber': phoneNumber,
      if (bio != null) 'bio': bio,
      if (joinDate != null) 'joinDate': joinDate!.toIso8601String(),
      if (preferences != null) 'preferences': preferences,
      if (interests != null) 'interests': interests,
      'isVerified': isVerified,
      'privacy': privacy,
      'friends': friends,
      'followers': followers,
      'stars': stars,
      if (additionalData != null) ...additionalData!,
      'updatedAt': DateTime.now().toIso8601String(),
    };
  }
  
  /// نسخ النموذج مع تحديث بعض الخصائص
  UserModel copyWith({
    String? id,
    String? name,
    String? email,
    String? avatarUrl,
    UserRole? role,
    double? latitude,
    double? longitude,
    String? phoneNumber,
    String? bio,
    DateTime? joinDate,
    Map<String, dynamic>? preferences,
    List<String>? interests,
    bool? isVerified,
    String? privacy,
    List<String>? friends,
    List<String>? followers,
    int? stars,
    Map<String, dynamic>? additionalData,
    DateTime? updatedAt,
  }) {
    return UserModel(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      role: role ?? this.role,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      bio: bio ?? this.bio,
      joinDate: joinDate ?? this.joinDate,
      preferences: preferences ?? this.preferences,
      interests: interests ?? this.interests,
      isVerified: isVerified ?? this.isVerified,
      privacy: privacy ?? this.privacy,
      friends: friends ?? this.friends,
      followers: followers ?? this.followers,
      stars: stars ?? this.stars,
      additionalData: additionalData ?? this.additionalData,
      updatedAt: updatedAt ?? DateTime.now(),
    );
  }
  
  /// التحقق مما إذا كان المستخدم لديه صلاحية معينة
  bool hasRole(UserRole roleToCheck) {
    return role == roleToCheck;
  }
  
  /// التحقق مما إذا كان المستخدم لديه أي من الأدوار المحددة
  bool hasAnyRole(List<UserRole> rolesToCheck) {
    return rolesToCheck.contains(role);
  }
  
  /// التحقق مما إذا كان المستخدم مسؤول
  bool get isAdmin => role == UserRole.admin;
  
  /// التحقق مما إذا كان المستخدم من الحكماء
  bool get isHokama => role == UserRole.hokama;
  
  /// التحقق مما إذا كان المستخدم من العلماء
  bool get isOlama => role == UserRole.olama;
  
  /// التحقق مما إذا كان المستخدم من المجتهدين
  bool get isMogtahdin => role == UserRole.mogtahdin;
  
  /// التحقق مما إذا كان المستخدم من الفريق الطبي
  bool get isMedical => role == UserRole.medical;
  
  /// التحقق مما إذا كان المستخدم عضو عادي
  bool get isMember => role == UserRole.member;
  
  /// التحقق مما إذا كان المستخدم ضيف
  bool get isGuest => role == UserRole.guest;
  
  @override
  String toString() {
    return 'UserModel(id: $id, name: $name, email: $email, role: ${role.label})';
  }
}
