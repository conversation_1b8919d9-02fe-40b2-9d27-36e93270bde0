const express = require('express');
const router = express.Router();
const controller = require('../controllers/MaintenanceCategory.controller');

// Basic CRUD routes
router.get('/', controller.getAllCategories);
router.get('/:id', controller.getCategoryById);
router.post('/', controller.createCategory);
router.put('/:id', controller.updateCategory);
router.delete('/:id', controller.deleteCategory);

// Additional routes for tools
router.get('/:id/tools', controller.getToolsByCategoryId);
router.post('/:id/tools', controller.addToolToCategory);

module.exports = router;
