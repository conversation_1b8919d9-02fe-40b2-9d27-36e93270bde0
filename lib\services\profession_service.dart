import 'api_service.dart';

class ProfessionService {
  final String baseUrl = 'https://api.example.com'; // Replace with actual API base URL
  final ApiService _apiService = ApiService();

  Future<ApiResponse<bool>> markNotificationRead(String notificationId) async {
    try {
      final response = await _apiService.put(
        '$baseUrl/notifications/$notificationId/read',
        headers: {'Authorization': 'Bearer '}, // Get token from AppState or other source
        parser: (data) => true
      );
      return response;
    } catch (e) {
      print('Error marking notification as read: $e');
      return ApiResponse<bool>.error('Error marking notification as read: $e');
    }
  }
}