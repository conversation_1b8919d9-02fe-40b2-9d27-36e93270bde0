const MaintenanceCategory = require('../models/MaintenanceCategory.model');

// Get all maintenance categories
exports.getAllCategories = async (req, res) => {
  try {
    const categories = await MaintenanceCategory.find();
    res.json(categories);
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};

// Get single category by ID
exports.getCategoryById = async (req, res) => {
  try {
    const category = await MaintenanceCategory.findById(req.params.id);
    if (!category) return res.status(404).json({ error: 'Not found' });
    res.json(category);
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};

// Create new category
exports.createCategory = async (req, res) => {
  try {
    const newCategory = new MaintenanceCategory(req.body);
    await newCategory.save();
    res.status(201).json(newCategory);
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
};

// Update category
exports.updateCategory = async (req, res) => {
  try {
    const updated = await MaintenanceCategory.findByIdAndUpdate(req.params.id, req.body, { new: true });
    if (!updated) return res.status(404).json({ error: 'Not found' });
    res.json(updated);
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
};

// Delete category
exports.deleteCategory = async (req, res) => {
  try {
    const deleted = await MaintenanceCategory.findByIdAndDelete(req.params.id);
    if (!deleted) return res.status(404).json({ error: 'Not found' });
    res.json({ message: 'Deleted successfully' });
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
};

// Get tools by category ID
exports.getToolsByCategoryId = async (req, res) => {
  try {
    const category = await MaintenanceCategory.findById(req.params.id);
    if (!category) return res.status(404).json({ error: 'Category not found' });
    res.json(category.tools || []);
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};

// Add tool to category
exports.addToolToCategory = async (req, res) => {
  try {
    const category = await MaintenanceCategory.findById(req.params.id);
    if (!category) return res.status(404).json({ error: 'Category not found' });
    
    category.tools.push(req.body);
    await category.save();
    
    res.status(201).json(category);
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
};
