{"compilerOptions": {"target": "es2021", "module": "esnext", "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "baseUrl": ".", "paths": {"@/*": ["src/*"], "@config/*": ["src/config/*"], "@controllers/*": ["src/controllers/*"], "@middleware/*": ["src/middleware/*"], "@models/*": ["src/models/*"], "@routes/*": ["src/routes/*"], "@services/*": ["src/services/*"], "@utils/*": ["src/utils/*"], "@validators/*": ["src/validators/*"]}}, "include": ["src/**/*.js"], "exclude": ["node_modules", "**/node_modules/*", "dist"]}