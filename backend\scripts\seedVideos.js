const mongoose = require('mongoose');
const dotenv = require('dotenv');
const Video = require('../src/models/video.model');
const User = require('../src/models/User.model');

// Load environment variables
dotenv.config({ path: '../.env' });

// Sample video data
const sampleVideos = [
  {
    title: 'تعليم الألوان للأطفال',
    description: 'فيديو تعليمي ممتع لتعليم الأطفال الألوان الأساسية',
    url: 'https://www.youtube.com/watch?v=example1',
    thumbnail: 'https://i.ytimg.com/vi/example1/hqdefault.jpg',
    duration: 120,
    category: 'education',
    ageGroup: '1-3',
    tags: ['ألوان', 'تعليم', 'أطفال']
  },
  {
    title: 'الرعاية الصحية للرضع',
    description: 'نصائح مهمة للعناية بالرضع حديثي الولادة',
    url: 'https://www.youtube.com/watch?v=example2',
    thumbnail: 'https://i.ytimg.com/vi/example2/hqdefault.jpg',
    duration: 180,
    category: 'health',
    ageGroup: '0-1',
    tags: ['رعاية', 'رضع', 'صحة']
  },
  {
    title: 'أنشطة تعليمية للأطفال',
    description: 'مجموعة من الأنشطة التعليمية المسلية للأطفال',
    url: 'https://www.youtube.com/watch?v=example3',
    thumbnail: 'https://i.ytimg.com/vi/example3/hqdefault.jpg',
    duration: 150,
    category: 'activities',
    ageGroup: '3-6',
    tags: ['أنشطة', 'تعليم', 'مرح']
  },
  {
    title: 'نظام غذائي صحي للأطفال',
    description: 'نصائح لتغذية صحية ومتوازنة للأطفال',
    url: 'https://www.youtube.com/watch?v=example4',
    thumbnail: 'https://i.ytimg.com/vi/example4/hqdefault.jpg',
    duration: 200,
    category: 'nutrition',
    ageGroup: '1-3',
    tags: ['تغذية', 'صحة', 'أطفال']
  },
  {
    title: 'تعليم الحروف العربية',
    description: 'تعليم الحروف الهجائية للأطفال مع أمثلة',
    url: 'https://www.youtube.com/watch?v=example5',
    thumbnail: 'https://i.ytimg.com/vi/example5/hqdefault.jpg',
    duration: 300,
    category: 'education',
    ageGroup: '3-6',
    tags: ['حروف', 'عربية', 'تعليم']
  }
];

// Connect to MongoDB
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGO_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('MongoDB connected');
  } catch (err) {
    console.error('MongoDB connection error:', err);
    process.exit(1);
  }
};

// Seed the database
const seedVideos = async () => {
  try {
    // Connect to database
    await connectDB();
    
    // Clear existing videos
    await Video.deleteMany({});
    console.log('Cleared existing videos');
    
    // Get admin user (or create one if doesn't exist)
    let admin = await User.findOne({ email: '<EMAIL>' });
    
    if (!admin) {
      admin = new User({
        name: 'Admin User',
        email: '<EMAIL>',
        username: 'admin',
        password: 'admin123',
        role: 'admin',
        isVerified: true
      });
      await admin.save();
      console.log('Created admin user');
    }
    
    // Add videos with admin as the uploader
    const videosWithUser = sampleVideos.map(video => ({
      ...video,
      uploadedBy: admin._id,
      isApproved: true
    }));
    
    await Video.insertMany(videosWithUser);
    console.log(`Seeded ${sampleVideos.length} videos`);
    
    process.exit(0);
  } catch (err) {
    console.error('Error seeding videos:', err);
    process.exit(1);
  }
};

// Run the seeder
seedVideos();
