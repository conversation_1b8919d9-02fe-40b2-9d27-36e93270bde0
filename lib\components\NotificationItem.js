import React from "react";

const NotificationItem = ({ notif, onRead }) => (
  <li style={{ color: notif.isRead ? '#888' : '#000', fontWeight: notif.isRead ? 'normal' : 'bold', marginBottom: 8 }}>
    {notif.text}
    <span style={{ fontSize: 12, color: '#666', marginRight: 8 }}>({notif.relativeTime})</span>
    {!notif.isRead && (
      <button style={{ marginLeft: 8 }} onClick={() => onRead(notif._id)}>تعيين كمقروء</button>
    )}
  </li>
);
export default NotificationItem;
