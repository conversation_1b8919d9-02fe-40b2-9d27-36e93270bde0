// تحكم الحظر بين المستخدمين
const Block = require('../models/Blockmodel');

// إنشاء حظر جديد
exports.createBlock = async (req, res) => {
  try {
    const block = await Block.create(req.body);
    res.status(201).json(block);
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
};

// جلب الحظر بين مستخدمين محددين
exports.getBlock = async (req, res) => {
  try {
    const { blockerId, blockedId } = req.query;
    const block = await Block.findOne({ blockerId, blockedId });
    res.json(block);
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};

// إزالة الحظر
exports.deleteBlock = async (req, res) => {
  try {
    await Block.findByIdAndDelete(req.params.id);
    res.json({ message: 'تم إلغاء الحظر' });
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};
