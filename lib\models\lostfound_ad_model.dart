// نموذج الإعلان المفقود/الموجود
class LostFoundAd {
  final String id;
  final String title;
  final String description;
  final String location;
  final String category;
  final bool isLost;
  final String fileUrl;
  final DateTime date;
  final bool isFeatured;
  final String creatorId;
  final String status;
  bool isFavorite;

  LostFoundAd({
    required this.id,
    required this.title,
    required this.description,
    required this.location,
    required this.category,
    required this.isLost,
    required this.fileUrl,
    required this.date,
    required this.isFeatured,
    required this.creatorId,
    required this.status,
    this.isFavorite = false,
  });

  factory LostFoundAd.fromJson(Map<String, dynamic> json) {
    return LostFoundAd(
      id: json['_id'] ?? '',
      title: json['title'] ?? 'بدون عنوان',
      description: json['description'] ?? 'بدون وصف',
      location: json['location'] ?? 'غير محدد',
      category: json['category'] ?? 'غير مصنف',
      isLost: json['isLost'] ?? true,
      fileUrl: json['fileUrl'] ?? '',
      date: DateTime.parse(json['date'] ?? DateTime.now().toString()),
      isFeatured: json['isFeatured'] ?? false,
      creatorId: json['creatorId'] ?? '',
      status: json['status'] ?? 'pending',
    );
  }
}
