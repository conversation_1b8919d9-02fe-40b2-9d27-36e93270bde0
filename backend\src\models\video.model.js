const mongoose = require('mongoose');

const VideoSchema = new mongoose.Schema(
  {
    // المعلومات الأساسية
    title: {
      type: String,
      required: [true, 'الرجاء إدخال عنوان الفيديو'],
      trim: true,
      maxlength: [100, 'العنوان لا يمكن أن يتجاوز 100 حرف']
    },
    description: {
      type: String,
      required: [true, 'الرجاء إدخال وصف الفيديو'],
      trim: true,
      maxlength: [1000, 'الوصف لا يمكن أن يتجاوز 1000 حرف']
    },
    
    // المستخدم والتحميل
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: [true, 'يجب تحديد المستخدم']
    },
    
    // روابط الوسائط
    url: {
      type: String,
      required: [true, 'الرجاء إدخال رابط الفيديو'],
      trim: true
    },
    thumbnail: {
      type: String,
      default: '/uploads/thumbnails/default.jpg',
      trim: true
    },
    
    // التصنيفات والبيانات الوصفية
    category: {
      type: String,
      required: [true, 'الرجاء تحديد الفئة'],
      enum: {
        values: [
          'parenting', 'education', 'health', 'nutrition', 
          'development', 'activities', 'safety', 'other',
          'تعليمي', 'ترفيهي', 'رياضي', 'فني'
        ],
        message: 'فئة غير صالحة'
      },
      default: 'تعليمي'
    },
    ageGroup: {
      type: String,
      required: [true, 'الرجاء تحديد الفئة العمرية'],
      enum: {
        values: ['0-1', '1-3', '3-6', '6-9', '9-12', '12+', '0-3', '4-6', '7-12', '13+'],
        message: 'فئة عمرية غير صالحة'
      },
      default: '0-3'
    },
    tags: [{
      type: String,
      trim: true
    }],
    
    // الإحصائيات
    views: [{
      user: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
      },
      date: {
        type: Date,
        default: Date.now
      },
      duration: Number // مدة المشاهدة بالثواني
    }],
    viewCount: {
      type: Number,
      default: 0
    },
    likes: [{
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    }],
    likeCount: {
      type: Number,
      default: 0
    },
    comments: [{
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Comment'
    }],
    commentCount: {
      type: Number,
      default: 0
    },
    
    // خيارات الفيديو
    isPublic: {
      type: Boolean,
      default: true
    },
    isLive: {
      type: Boolean,
      default: false
    },
    isFeatured: {
      type: Boolean,
      default: false
    },
    isApproved: {
      type: Boolean,
      default: true
    },
    
    // مدة الفيديو وجودته
    duration: {
      type: Number, // بالثواني
      required: [true, 'الرجاء تحديد مدة الفيديو'],
      min: [1, 'يجب أن تكون مدة الفيديو ثانية واحدة على الأقل']
    },
    quality: {
      type: String,
      enum: ['144p', '240p', '360p', '480p', '720p', '1080p', '1440p', '2160p'],
      default: '480p'
    },
    
    // بيانات إضافية
    language: {
      type: String,
      default: 'ar',
      enum: ['ar', 'en', 'fr', 'es', 'other']
    },
    subtitles: [{
      language: String,
      url: String
    }],
    
    // التقييم
    rating: {
      average: {
        type: Number,
        min: 0,
        max: 5,
        default: 0
      },
      count: {
        type: Number,
        default: 0
      }
    },
    
    // الحالة
    status: {
      type: String,
      enum: ['draft', 'pending', 'published', 'rejected', 'archived'],
      default: 'published'
    },
    rejectionReason: String,
    
    // التواريخ المهمة
    scheduledPublish: Date,
    lastEdited: Date,
    
    // الحقول المحسوبة
    engagementRate: {
      type: Number,
      default: 0,
      min: 0,
      max: 100
    }
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  }
);

// فهرسة الحقول المستخدمة في البحث
VideoSchema.index({ title: 'text', description: 'text', tags: 'text' });

// تحديث عدد المشاهدات
VideoSchema.methods.addView = async function(userId, duration = 0) {
  // إضافة مشاهدة جديدة إذا لم يشاهد المستخدم الفيديو من قبل
  const existingView = this.views.find(view => view.user && view.user.toString() === userId.toString());
  
  if (!existingView) {
    this.views.push({ user: userId, duration });
    this.viewCount += 1;
  } else {
    // تحديث مدة المشاهدة إذا كان المستخدم قد شاهد الفيديو من قبل
    existingView.duration = duration;
    existingView.date = Date.now();
  }
  
  // حساب معدل المشاركة (نسبة المشاهدة إلى مدة الفيديو)
  if (duration > 0 && this.duration > 0) {
    const watchPercentage = (duration / this.duration) * 100;
    this.engagementRate = Math.min(100, Math.max(this.engagementRate || 0, watchPercentage));
  }
  
  await this.save();
  return this;
};

// التحقق مما إذا كان المستخدم قد أعجب بالفيديو
VideoSchema.methods.hasLiked = function(userId) {
  return this.likes.some(like => like.toString() === userId.toString());
};

// تبديل الإعجاب
VideoSchema.methods.toggleLike = async function(userId) {
  const index = this.likes.findIndex(like => like.toString() === userId.toString());
  
  if (index === -1) {
    // إضافة إعجاب
    this.likes.push(userId);
    this.likeCount += 1;
    
    // إشعار للمستخدم
    if (this.user.toString() !== userId.toString()) {
      // يمكنك إضافة إشعار هنا
    }
  } else {
    // إزالة الإعجاب
    this.likes.splice(index, 1);
    this.likeCount = Math.max(0, this.likeCount - 1);
  }
  
  await this.save();
  return this;
};

// الحصول على سجل المشاهدة للمستخدم
VideoSchema.statics.getUserWatchHistory = function(userId, limit = 10) {
  return this.aggregate([
    { $match: { 'views.user': mongoose.Types.ObjectId(userId) } },
    { $unwind: '$views' },
    { $match: { 'views.user': mongoose.Types.ObjectId(userId) } },
    { $sort: { 'views.date': -1 } },
    { $limit: limit },
    {
      $lookup: {
        from: 'videos',
        localField: '_id',
        foreignField: '_id',
        as: 'video'
      }
    },
    { $unwind: '$video' },
    {
      $project: {
        _id: '$video._id',
        title: '$video.title',
        thumbnail: '$video.thumbnail',
        duration: '$video.duration',
        viewCount: '$video.viewCount',
        watchedAt: '$views.date',
        watchedDuration: '$views.duration'
      }
    }
  ]);
};

// التصدير
module.exports = mongoose.model('Video', VideoSchema);
