const errorHandler = (err, req, res, next) => {
  let error = { ...err };
  error.message = err.message;

  // سجل الخطأ للتصحيح
  console.error(err);

  // خطأ Mongoose في معرف غير صالح
  if (err.name === 'CastError') {
    const message = `المورد غير موجود بالمعرف ${err.value}`;
    error = new ErrorResponse(message, 404);
  }

  // خطأ Mongoose في تكرار المفتاح
  if (err.code === 11000) {
    const message = 'تم إدخال قيمة مكررة';
    error = new ErrorResponse(message, 400);
  }

  // أخطاء التحقق من Mongoose
  if (err.name === 'ValidationError') {
    const message = Object.values(err.errors).map(val => val.message);
    error = new ErrorResponse(message, 400);
  }

  res.status(error.statusCode || 500).json({
    success: false,
    error: error.message || 'خطأ في الخادم'
  });
};

module.exports = errorHandler;
