const FishBreedingGroup = require('../models/FishBreedingGroup.model');

// Get all fish breeding groups
exports.getAllGroups = async (req, res) => {
  try {
    const groups = await FishBreedingGroup.find();
    res.json(groups);
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};

// Get single group by ID
exports.getGroupById = async (req, res) => {
  try {
    const group = await FishBreedingGroup.findById(req.params.id);
    if (!group) return res.status(404).json({ error: 'Not found' });
    res.json(group);
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};

// Create new group
exports.createGroup = async (req, res) => {
  try {
    const newGroup = new FishBreedingGroup(req.body);
    await newGroup.save();
    res.status(201).json(newGroup);
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
};

// Update group
exports.updateGroup = async (req, res) => {
  try {
    const updated = await FishBreedingGroup.findByIdAndUpdate(req.params.id, req.body, { new: true });
    if (!updated) return res.status(404).json({ error: 'Not found' });
    res.json(updated);
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
};

// Delete group
exports.deleteGroup = async (req, res) => {
  try {
    const deleted = await FishBreedingGroup.findByIdAndDelete(req.params.id);
    if (!deleted) return res.status(404).json({ error: 'Not found' });
    res.json({ message: 'Deleted successfully' });
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
};
