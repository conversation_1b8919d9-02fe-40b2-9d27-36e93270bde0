import 'dart:convert';
import 'package:http/http.dart' as http;

// API configuration
class ApiConfig {
  static const String baseUrl = 'http://localhost:3000/api';
}

class HomeFoodIndustryService {
  final String baseUrl = ApiConfig.baseUrl;

  // Get all home food industry groups
  Future<List<dynamic>> getAllGroups() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/home-food-industry-groups'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception('Failed to load home food industry groups: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error connecting to server: $e');
    }
  }

  // Get a single home food industry group by ID
  Future<dynamic> getGroupById(String id) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/home-food-industry-groups/$id'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception('Failed to load home food industry group: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error connecting to server: $e');
    }
  }

  // Create a new home food industry group
  Future<dynamic> createGroup(Map<String, dynamic> data) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/home-food-industry-groups'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(data),
      );

      if (response.statusCode == 201) {
        return jsonDecode(response.body);
      } else {
        throw Exception('Failed to create home food industry group: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error connecting to server: $e');
    }
  }

  // Update a home food industry group
  Future<dynamic> updateGroup(String id, Map<String, dynamic> data) async {
    try {
      final response = await http.put(
        Uri.parse('$baseUrl/home-food-industry-groups/$id'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(data),
      );

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception('Failed to update home food industry group: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error connecting to server: $e');
    }
  }

  // Delete a home food industry group
  Future<void> deleteGroup(String id) async {
    try {
      final response = await http.delete(
        Uri.parse('$baseUrl/home-food-industry-groups/$id'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode != 200) {
        throw Exception('Failed to delete home food industry group: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error connecting to server: $e');
    }
  }
}