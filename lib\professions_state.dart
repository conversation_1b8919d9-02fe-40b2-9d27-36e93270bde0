import 'package:flutter/material.dart';

class ProfessionsState extends ChangeNotifier {
  // Add any profession-specific state management here
  // For example:
  // - Selected filters
  // - Search queries
  // - UI state
  
  // Example state variable
  bool _isLoading = false;
  
  bool get isLoading => _isLoading;
  
  void setLoading(bool value) {
    _isLoading = value;
    notifyListeners();
  }
  
  // Add other profession-specific state management methods as needed
}
