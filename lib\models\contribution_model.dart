import 'package:json_annotation/json_annotation.dart';

part 'contribution_model.g.dart';

@JsonSerializable()
class Contribution {
  final String id;
  final String title;
  final String description;
  final String userId;
  final String? imageUrl;
  final String? videoUrl;
  final String? documentUrl;
  final String type; // 'image', 'video', 'document', 'text'
  final int likesCount;
  final int commentsCount;
  final bool isLiked;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String? professionId; // Optional, if contribution is related to a specific profession

  Contribution({
    required this.id,
    required this.title,
    required this.description,
    required this.userId,
    this.imageUrl,
    this.videoUrl,
    this.documentUrl,
    required this.type,
    this.likesCount = 0,
    this.commentsCount = 0,
    this.isLiked = false,
    DateTime? createdAt,
    DateTime? updatedAt,
    this.professionId,
  })  : createdAt = createdAt ?? DateTime.now(),
        updatedAt = updatedAt ?? DateTime.now();

  factory Contribution.fromJson(Map<String, dynamic> json) => _$ContributionFromJson(json);
  Map<String, dynamic> toJson() => _$ContributionToJson(this);

  Contribution copyWith({
    String? id,
    String? title,
    String? description,
    String? userId,
    String? imageUrl,
    String? videoUrl,
    String? documentUrl,
    String? type,
    int? likesCount,
    int? commentsCount,
    bool? isLiked,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? professionId,
  }) {
    return Contribution(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      userId: userId ?? this.userId,
      imageUrl: imageUrl ?? this.imageUrl,
      videoUrl: videoUrl ?? this.videoUrl,
      documentUrl: documentUrl ?? this.documentUrl,
      type: type ?? this.type,
      likesCount: likesCount ?? this.likesCount,
      commentsCount: commentsCount ?? this.commentsCount,
      isLiked: isLiked ?? this.isLiked,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      professionId: professionId ?? this.professionId,
    );
  }

  // Helper to get the main media URL based on type
  String? get mediaUrl {
    switch (type) {
      case 'image':
        return imageUrl;
      case 'video':
        return videoUrl;
      case 'document':
        return documentUrl;
      default:
        return null;
    }
  }

  // Helper to check if contribution has media
  bool get hasMedia => mediaUrl != null && mediaUrl!.isNotEmpty;
}
