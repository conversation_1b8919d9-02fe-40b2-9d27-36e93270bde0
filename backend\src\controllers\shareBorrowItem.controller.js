const ShareBorrowItem = require('../models/ShareBorrowItem.model');
const SharingGroup = require('../models/SharingGroup.model');
const AnalyticsEvent = require('../models/AnalyticsEvent.model');
const fs = require('fs');
const path = require('path');
const { v4: uuidv4 } = require('uuid');

// Get all items with filtering, sorting, and pagination
exports.getItems = async (req, res) => {
  try {
    const {
      search = '',
      filter = 'all',
      sort = 'newest',
      skip = 0,
      limit = 10,
      userLat,
      userLon
    } = req.query;

    // Build query
    let query = {};
    
    // Search filter
    if (search) {
      query.$or = [
        { title: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { category: { $regex: search, $options: 'i' } }
      ];
    }
    
    // Availability filter
    if (filter === 'available') {
      query.isAvailable = true;
    } else if (filter === 'borrowed') {
      query.isAvailable = false;
    }
    
    // Get items
    let items = await ShareBorrowItem.find(query)
      .sort(sort === 'newest' ? { date: -1 } : { date: 1 })
      .skip(parseInt(skip))
      .limit(parseInt(limit));
    
    // Calculate distance if user location is provided
    if (userLat && userLon) {
      items = items.map(item => {
        const distance = calculateDistance(
          parseFloat(userLat),
          parseFloat(userLon),
          item.latitude,
          item.longitude
        );
        return { ...item.toObject(), distance };
      });
      
      // Sort by distance if requested
      if (sort === 'nearest') {
        items.sort((a, b) => a.distance - b.distance);
      }
    }
    
    const total = await ShareBorrowItem.countDocuments(query);
    
    res.json({
      items,
      total,
      hasMore: total > parseInt(skip) + items.length
    });
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};

// Get item by ID
exports.getItemById = async (req, res) => {
  try {
    const item = await ShareBorrowItem.findById(req.params.id);
    if (!item) return res.status(404).json({ error: 'Item not found' });
    res.json(item);
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};

// Create new item
exports.createItem = async (req, res) => {
  try {
    const {
      title,
      description,
      location,
      category,
      condition,
      maxBorrowDays,
      latitude,
      longitude,
      creatorId
    } = req.body;
    
    // Handle file upload
    let fileUrl = '';
    if (req.file) {
      const filename = `${uuidv4()}${path.extname(req.file.originalname)}`;
      const uploadPath = path.join(__dirname, '../../uploads', filename);
      
      // Create directory if it doesn't exist
      if (!fs.existsSync(path.join(__dirname, '../../uploads'))) {
        fs.mkdirSync(path.join(__dirname, '../../uploads'), { recursive: true });
      }
      
      fs.writeFileSync(uploadPath, req.file.buffer);
      fileUrl = `/uploads/${filename}`;
    }
    
    const newItem = new ShareBorrowItem({
      title,
      description,
      location,
      category,
      fileUrl,
      condition,
      maxBorrowDays: parseInt(maxBorrowDays),
      latitude: parseFloat(latitude),
      longitude: parseFloat(longitude),
      creatorId
    });
    
    await newItem.save();
    
    // Log analytics event
    const analyticsEvent = new AnalyticsEvent({
      type: 'share',
      itemId: newItem._id,
      userId: creatorId
    });
    await analyticsEvent.save();
    
    res.status(201).json(newItem);
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
};

// Update item
exports.updateItem = async (req, res) => {
  try {
    const updated = await ShareBorrowItem.findByIdAndUpdate(
      req.params.id,
      req.body,
      { new: true }
    );
    
    if (!updated) return res.status(404).json({ error: 'Item not found' });
    res.json(updated);
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
};

// Delete item
exports.deleteItem = async (req, res) => {
  try {
    const item = await ShareBorrowItem.findById(req.params.id);
    if (!item) return res.status(404).json({ error: 'Item not found' });
    
    // Delete file if exists
    if (item.fileUrl) {
      const filePath = path.join(__dirname, '../..', item.fileUrl);
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }
    }
    
    await ShareBorrowItem.findByIdAndDelete(req.params.id);
    res.json({ message: 'Item deleted successfully' });
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
};

// Upload verification media
exports.uploadVerificationMedia = async (req, res) => {
  try {
    const { itemId, uploaderId } = req.body;
    
    const item = await ShareBorrowItem.findById(itemId);
    if (!item) return res.status(404).json({ error: 'Item not found' });
    
    // Handle file upload
    let fileUrl = '';
    if (req.file) {
      const filename = `verification_${uuidv4()}${path.extname(req.file.originalname)}`;
      const uploadPath = path.join(__dirname, '../../uploads', filename);
      
      // Create directory if it doesn't exist
      if (!fs.existsSync(path.join(__dirname, '../../uploads'))) {
        fs.mkdirSync(path.join(__dirname, '../../uploads'), { recursive: true });
      }
      
      fs.writeFileSync(uploadPath, req.file.buffer);
      fileUrl = `/uploads/${filename}`;
    }
    
    const verificationMedia = {
      url: fileUrl,
      type: req.file.mimetype,
      uploaderId
    };
    
    item.verificationMedia.push(verificationMedia);
    await item.save();
    
    // Log analytics event
    const analyticsEvent = new AnalyticsEvent({
      type: 'verify',
      itemId,
      userId: uploaderId
    });
    await analyticsEvent.save();
    
    res.status(201).json(item);
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
};

// Request to borrow an item
exports.requestBorrow = async (req, res) => {
  try {
    const { itemId, borrowerId, startDate, days } = req.body;
    
    const item = await ShareBorrowItem.findById(itemId);
    if (!item) return res.status(404).json({ error: 'Item not found' });
    
    if (!item.isAvailable) {
      return res.status(400).json({ error: 'Item is not available for borrowing' });
    }
    
    // Update item availability
    item.isAvailable = false;
    await item.save();
    
    // Log analytics event
    const analyticsEvent = new AnalyticsEvent({
      type: 'borrow',
      itemId,
      userId: borrowerId,
      metadata: {
        startDate,
        days
      }
    });
    await analyticsEvent.save();
    
    res.json({ message: 'Borrow request successful', item });
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
};

// Get borrow history for an item
exports.getBorrowHistory = async (req, res) => {
  try {
    const { itemId } = req.params;
    
    const borrowEvents = await AnalyticsEvent.find({
      type: 'borrow',
      itemId
    }).sort({ timestamp: -1 });
    
    res.json(borrowEvents);
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};

// Helper function to calculate distance between two coordinates
function calculateDistance(lat1, lon1, lat2, lon2) {
  const R = 6371; // Radius of the earth in km
  const dLat = deg2rad(lat2 - lat1);
  const dLon = deg2rad(lon2 - lon1);
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(deg2rad(lat1)) * Math.cos(deg2rad(lat2)) *
    Math.sin(dLon / 2) * Math.sin(dLon / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  const distance = R * c; // Distance in km
  return distance;
}

function deg2rad(deg) {
  return deg * (Math.PI / 180);
}
