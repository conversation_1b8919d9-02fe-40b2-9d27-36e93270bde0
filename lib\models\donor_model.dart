// نموذج المتبرع
class Donor {
  final String id;
  final double totalDonated;
  bool showHistory;

  <PERSON><PERSON>({required this.id, required this.totalDonated, required this.showHistory});

  factory Donor.fromJson(Map<String, dynamic> json) {
    return Donor(
      id: json['_id'] ?? '',
      totalDonated: (json['totalDonated'] as num?)?.toDouble() ?? 0.0,
      showHistory: json['showHistory'] ?? false,
    );
  }
}
