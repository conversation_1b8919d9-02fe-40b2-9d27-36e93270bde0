# FULK Backend API

Backend API for the FULK application, built with Node.js, Express, and MongoDB. This API provides a comprehensive set of endpoints for user management, content management, and various application features with role-based access control.

## ✨ Features

- 🔐 **Authentication & Authorization**
  - JWT-based authentication
  - Role-based access control (<PERSON><PERSON>, <PERSON><PERSON><PERSON>, User)
  - Secure password hashing with bcrypt

- 📱 **User Management**
  - User registration and profile management
  - Role assignment and permissions
  - Activity tracking and logging

- 🏛️ **Hokama System**
  - Specialized role for content management
  - Restricted access to critical operations
  - Content moderation capabilities

- 🎥 **Video Platform**
  - Video upload and streaming
  - Like, comment, and share functionality
  - Categories and tags

- 🏆 **Points & Rewards**
  - Activity-based point system
  - Achievement tracking
  - Leaderboards

- 🔔 **Real-time Features**
  - WebSocket integration with Socket.IO
  - Real-time notifications
  - Live updates

## 🚀 Getting Started

### Prerequisites

- Node.js v18 or later
- MongoDB v6.0 or later
- npm v9 or yarn v1.22+

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/yourusername/fulk-app.git
   cd fulk-app/backend
   ```

2. **Install dependencies**
   ```bash
   npm install
   # or
   yarn install
   ```

3. **Environment Setup**
   - Copy the example environment file:
     ```bash
     cp .env.example .env
     ```
   - Update the `.env` file with your configuration

4. **Database Setup**
   - Ensure MongoDB is running locally or update `MONGO_URI`
   - Run database migrations:
     ```bash
     npm run migrate
     ```

5. **Seed the database** (optional)
   ```bash
   npm run seed
   ```
   This will create initial admin and sample data.

## 🛠 Development

### Available Scripts

- `npm run dev` - Start development server with hot-reload
- `npm start` - Start production server
- `npm test` - Run tests
- `npm run lint` - Run ESLint
- `npm run format` - Format code with Prettier
- `npm run migrate` - Run database migrations
- `npm run seed` - Seed the database with sample data

### Project Structure

```
backend/
├── src/
│   ├── config/         # Configuration files
│   ├── controllers/     # Route controllers
│   ├── middleware/      # Custom middleware
│   ├── models/          # Database models
│   ├── routes/          # API routes
│   ├── services/        # Business logic
│   ├── utils/           # Utility functions
│   └── validators/      # Request validators
├── .env.example         # Environment variables example
├── .eslintrc.js         # ESLint config
├── .prettierrc          # Prettier config
├── app.js               # Express app setup
└── server.js            # Server entry point
```

## 🔒 Security

- Input validation and sanitization
- Rate limiting
- Helmet for secure HTTP headers
- CORS configuration
- JWT authentication
- Role-based access control
- Request logging

## 📚 API Documentation

API documentation is available at `/api-docs` when running in development mode.

## 🤝 Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- All contributors who have helped improve this project
- The open-source community for their invaluable tools and libraries
## Running the Application

### Development Mode

```bash
npm run dev
# or
yarn dev
```

The server will start on `http://localhost:5000` (or the port specified in your `.env` file).

### Production Mode

```bash
npm start
# or
yarn start
```

## API Documentation

API documentation is available at `/api-docs` when running in development mode.

### Authentication

Most endpoints require authentication. Include the JWT token in the `Authorization` header:

```
Authorization: Bearer <your_jwt_token>
```

### Endpoints

#### Authentication

- `POST /api/auth/register` - Register a new user
- `POST /api/auth/login` - Login user
- `GET /api/auth/me` - Get current user profile

#### Users

- `GET /api/users` - Get all users (admin only)
- `GET /api/users/:id` - Get user by ID
- `PUT /api/users/:id` - Update user
- `DELETE /api/users/:id` - Delete user (admin only)

#### Videos

- `GET /api/videos` - Get all videos
- `GET /api/videos/:id` - Get video by ID
- `POST /api/videos` - Create a new video
- `PUT /api/videos/:id` - Update video
- `DELETE /api/videos/:id` - Delete video
- `POST /api/videos/:videoId/watch` - Record video watch
- `POST /api/videos/:videoId/like` - Like/unlike video
- `POST /api/videos/:videoId/comment` - Add comment to video

#### Points

- `GET /api/points/summary` - Get user's points summary
- `GET /api/points/history` - Get user's points history
- `POST /api/points/redeem` - Redeem points for rewards

#### Notifications

- `GET /api/notifications` - Get user's notifications
- `PUT /api/notifications/:id/read` - Mark notification as read
- `PUT /api/notifications/read-all` - Mark all notifications as read

## Testing

To run tests:

```bash
npm test
# or
yarn test
```

## Environment Variables

- `PORT` - Port to run the server on (default: 5000)
- `NODE_ENV` - Environment (development, production)
- `MONGO_URI` - MongoDB connection string
- `JWT_SECRET` - Secret for JWT token generation
- `JWT_EXPIRE` - JWT expiration time (e.g., '7d' for 7 days)
- `JWT_COOKIE_EXPIRE` - JWT cookie expiration in days

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Contact

Your Name - <EMAIL>

Project Link: [https://github.com/yourusername/fulk-app](https://github.com/yourusername/fulk-app)
