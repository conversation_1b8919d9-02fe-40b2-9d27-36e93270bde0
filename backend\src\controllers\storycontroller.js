const Story = require('../models/Storymodel');
const User = require('../models/user.model.new');
const ErrorResponse = require('../utils/errorResponse');
const asyncHandler = require('../middleware/asyncmiddleware');
const path = require('path');
const fs = require('fs');
const multer = require('multer');

// إعداد تخزين الملفات
const storage = multer.diskStorage({
  destination: function(req, file, cb) {
    const uploadPath = './public/uploads/stories';
    if (!fs.existsSync(uploadPath)) {
      fs.mkdirSync(uploadPath, { recursive: true });
    }
    cb(null, uploadPath);
  },
  filename: function(req, file, cb) {
    cb(null, `story_${Date.now()}${path.extname(file.originalname)}`);
  }
});

// فلترة الملفات
const fileFilter = (req, file, cb) => {
  // قبول الصور فقط للقصص
  if (file.mimetype.startsWith('image')) {
    cb(null, true);
  } else {
    cb(new ErrorResponse('يرجى تحميل صورة فقط للقصة', 400), false);
  }
};

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 5000000 // 5 ميجابايت
  },
  fileFilter: fileFilter
});

// @desc    الحصول على جميع القصص
// @route   GET /api/stories
// @access  Private
exports.getStories = asyncHandler(async (req, res, next) => {
  const stories = await Story.find()
    .populate({
      path: 'user',
      select: 'name avatarUrl'
    })
    .sort({ createdAt: -1 });

  res.status(200).json({
    success: true,
    count: stories.length,
    data: stories
  });
});

// @desc    إنشاء قصة جديدة
// @route   POST /api/stories
// @access  Private
exports.createStory = asyncHandler(async (req, res, next) => {
  // التعامل مع تحميل الصورة
  const uploadMiddleware = upload.single('image');

  uploadMiddleware(req, res, async (err) => {
    if (err) {
      return next(new ErrorResponse(`خطأ في تحميل الصورة: ${err.message}`, 400));
    }

    // التحقق من وجود صورة
    if (!req.file) {
      return next(new ErrorResponse('الرجاء تحميل صورة للقصة', 400));
    }

    // إنشاء القصة
    const story = await Story.create({
      user: req.user.id,
      image: `/uploads/stories/${req.file.filename}`
    });

    // الحصول على القصة مع معلومات المستخدم
    const populatedStory = await Story.findById(story._id).populate({
      path: 'user',
      select: 'name avatarUrl'
    });

    res.status(201).json({
      success: true,
      data: populatedStory
    });
  });
});

// @desc    الحصول على قصة واحدة
// @route   GET /api/stories/:id
// @access  Private
exports.getStory = asyncHandler(async (req, res, next) => {
  const story = await Story.findById(req.params.id).populate({
    path: 'user',
    select: 'name avatarUrl'
  });

  if (!story) {
    return next(new ErrorResponse(`لم يتم العثور على قصة بالمعرف ${req.params.id}`, 404));
  }

  res.status(200).json({
    success: true,
    data: story
  });
});

// @desc    حذف قصة
// @route   DELETE /api/stories/:id
// @access  Private
exports.deleteStory = asyncHandler(async (req, res, next) => {
  const story = await Story.findById(req.params.id);

  if (!story) {
    return next(new ErrorResponse(`لم يتم العثور على قصة بالمعرف ${req.params.id}`, 404));
  }

  // التحقق من ملكية القصة
  if (story.user.toString() !== req.user.id && req.user.role !== 'admin') {
    return next(new ErrorResponse('غير مصرح لك بحذف هذه القصة', 401));
  }

  // حذف صورة القصة
  const filePath = path.join(__dirname, '../../public', story.image);
  if (fs.existsSync(filePath)) {
    fs.unlinkSync(filePath);
  }

  await story.remove();

  res.status(200).json({
    success: true,
    data: {}
  });
});

// @desc    الحصول على قصص مستخدم معين
// @route   GET /api/stories/user/:userId
// @access  Private
exports.getUserStories = asyncHandler(async (req, res, next) => {
  const stories = await Story.find({ user: req.params.userId })
    .populate({
      path: 'user',
      select: 'name avatarUrl'
    })
    .sort({ createdAt: -1 });

  res.status(200).json({
    success: true,
    count: stories.length,
    data: stories
  });
});
