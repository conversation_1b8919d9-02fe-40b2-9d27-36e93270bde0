const mongoose = require('mongoose');
const User = require('../models/User.model');
const PointsTransaction = require('../models/pointstransaction.model');
const Notification = require('../models/notification.model');
const { updateUserLevel } = require('./points.controller');

// Points configuration
const ACTIVITY_POINTS = {
  DAILY_LOGIN: 10,          // Points for daily login
  CREATE_POST: 20,         // Points for creating a post
  COMMENT: 5,              // Points for commenting
  LIKE: 1,                 // Points for receiving a like
  SHARE: 15,               // Points for sharing content
  COMPLETE_CHALLENGE: 50,  // Points for completing a challenge
  WATCH_VIDEO: 5,          // Points for watching an educational video
  UPLOAD_CONTENT: 30,      // Points for uploading educational content
  COMPLETE_LESSON: 25,     // Points for completing a lesson
  ACHIEVEMENT: 100,        // Points for earning an achievement
  REFERRAL: 200,           // Points for referring a friend
  PROFILE_COMPLETION: 50,  // Points for completing profile
  WELCOME: 100             // Welcome points for new users
};

/**
 * Award points for user activity
 * @param {string} userId - ID of the user to award points to
 * @param {string} activityType - Type of activity (must be a key in ACTIVITY_POINTS)
 * @param {object} options - Additional options
 * @param {string} options.referenceId - ID of the related document (post, comment, etc.)
 * @param {string} options.referenceType - Type of the reference
 * @param {string} options.description - Custom description for the transaction
 * @returns {Promise<object>} Result of the operation
 */
exports.awardPointsForActivity = async (userId, activityType, options = {}) => {
  const session = await mongoose.startSession();
  session.startTransaction();
  
  try {
    // Validate activity type
    if (!ACTIVITY_POINTS[activityType]) {
      throw new Error(`Invalid activity type: ${activityType}`);
    }
    
    const points = ACTIVITY_POINTS[activityType];
    
    // Update user's points
    const user = await User.findByIdAndUpdate(
      userId,
      { $inc: { points: points } },
      { new: true, session }
    );
    
    if (!user) {
      throw new Error('User not found');
    }
    
    // Log the points transaction
    const transaction = new PointsTransaction({
      userId,
      points,
      type: 'earned',
      reason: options.description || `${activityType.split('_').join(' ').toLowerCase()} activity`,
      referenceId: options.referenceId,
      referenceType: options.referenceType || activityType.toLowerCase()
    });
    
    await transaction.save({ session });
    
    // Create notification for significant point gains
    if (points >= 10) {
      const notification = new Notification({
        userId,
        title: '🎉 Points Earned!',
        message: `You've earned ${points} points for ${activityType.split('_').join(' ').toLowerCase()}`,
        type: 'points_earned',
        referenceId: options.referenceId,
        referenceType: options.referenceType || activityType.toLowerCase(),
        isRead: false
      });
      
      await notification.save({ session });
    }
    
    // Check for level up
    await updateUserLevel(user, points, session);
    
    await session.commitTransaction();
    
    return {
      success: true,
      points: user.points + points,
      pointsAwarded: points,
      newLevel: user.level
    };
  } catch (error) {
    await session.abortTransaction();
    console.error('Error awarding points:', error);
    return {
      success: false,
      error: error.message
    };
  } finally {
    session.endSession();
  }
};

/**
 * Deduct points from user
 * @param {string} userId - ID of the user
 * @param {number} points - Number of points to deduct
 * @param {string} reason - Reason for deduction
 * @param {object} options - Additional options
 * @returns {Promise<object>} Result of the operation
 */
exports.deductPoints = async (userId, points, reason, options = {}) => {
  const session = await mongoose.startSession();
  session.startTransaction();
  
  try {
    if (!points || points <= 0) {
      throw new Error('Points must be a positive number');
    }
    
    // Check if user has enough points
    const user = await User.findById(userId).session(session);
    if (!user) {
      throw new Error('User not found');
    }
    
    if (user.points < points) {
      throw new Error('Insufficient points');
    }
    
    // Deduct points
    user.points -= points;
    await user.save({ session });
    
    // Log the points transaction
    const transaction = new PointsTransaction({
      userId,
      points: -points,
      type: 'spent',
      reason: reason,
      referenceId: options.referenceId,
      referenceType: options.referenceType || 'deduction'
    });
    
    await transaction.save({ session });
    
    // Create notification for point deduction
    const notification = new Notification({
      userId,
      title: 'ℹ️ Points Deducted',
      message: `${points} points were deducted: ${reason}`,
      type: 'points_deducted',
      referenceId: options.referenceId,
      referenceType: options.referenceType || 'deduction',
      isRead: false
    });
    
    await notification.save({ session });
    
    await session.commitTransaction();
    
    return {
      success: true,
      points: user.points,
      pointsDeducted: points
    };
  } catch (error) {
    await session.abortTransaction();
    console.error('Error deducting points:', error);
    return {
      success: false,
      error: error.message
    };
  } finally {
    session.endSession();
  }
};

// Export points configuration
module.exports.ACTIVITY_POINTS = ACTIVITY_POINTS;
