import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:dio/dio.dart';

/// أنواع أخطاء API المختلفة
enum ApiErrorType {
  /// خطأ في الاتصال بالشبكة
  network,
  
  /// خطأ في المصادقة (401)
  authentication,
  
  /// خطأ في الصلاحيات (403)
  authorization,
  
  /// خطأ في البيانات المرسلة (400)
  badRequest,
  
  /// خطأ في الخادم (500)
  server,
  
  /// خطأ في العثور على المورد (404)
  notFound,
  
  /// خطأ في تنسيق البيانات
  formatting,
  
  /// خطأ غير معروف
  unknown
}

/// فئة خطأ API
class ApiError implements Exception {
  final String message;
  final int? statusCode;
  final ApiErrorType type;
  final dynamic rawError;
  final String? stackTrace;
  final Map<String, dynamic>? responseData;

  ApiError({
    required this.message,
    this.statusCode,
    required this.type,
    this.rawError,
    this.stackTrace,
    this.responseData,
  });

  /// إنشاء خطأ API من استثناء
  factory ApiError.fromException(dynamic exception, [StackTrace? trace]) {
    if (exception is ApiError) {
      return exception;
    }
    
    if (exception is SocketException || exception is TimeoutException) {
      return ApiError(
        message: 'فشل الاتصال بالخادم. يرجى التحقق من اتصالك بالإنترنت والمحاولة مرة أخرى.',
        type: ApiErrorType.network,
        rawError: exception,
        stackTrace: trace?.toString(),
      );
    }
    
    if (exception is FormatException) {
      return ApiError(
        message: 'حدث خطأ في معالجة البيانات من الخادم.',
        type: ApiErrorType.formatting,
        rawError: exception,
        stackTrace: trace?.toString(),
      );
    }

    // التعامل مع أخطاء Dio
    if (exception is DioException) {
      return _handleDioError(exception, trace);
    }
    
    // التعامل مع أخطاء Http
    if (exception is http.ClientException) {
      return ApiError(
        message: 'حدث خطأ في الاتصال بالخادم: ${exception.message}',
        type: ApiErrorType.network,
        rawError: exception,
        stackTrace: trace?.toString(),
      );
    }

    // خطأ غير معروف
    return ApiError(
      message: 'حدث خطأ غير متوقع: ${exception.toString()}',
      type: ApiErrorType.unknown,
      rawError: exception,
      stackTrace: trace?.toString(),
    );
  }

  /// التعامل مع أخطاء Dio
  static ApiError _handleDioError(DioException exception, [StackTrace? trace]) {
    switch (exception.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return ApiError(
          message: 'انتهت مهلة الاتصال بالخادم. يرجى المحاولة مرة أخرى.',
          type: ApiErrorType.network,
          statusCode: exception.response?.statusCode,
          rawError: exception,
          stackTrace: trace?.toString(),
        );

      case DioExceptionType.badCertificate:
        return ApiError(
          message: 'شهادة SSL غير صالحة. يرجى التحقق من اتصالك الآمن.',
          type: ApiErrorType.network,
          statusCode: exception.response?.statusCode,
          rawError: exception,
          stackTrace: trace?.toString(),
        );

      case DioExceptionType.badResponse:
        return _handleDioResponseError(exception, trace);

      case DioExceptionType.cancel:
        return ApiError(
          message: 'تم إلغاء الطلب.',
          type: ApiErrorType.unknown,
          statusCode: exception.response?.statusCode,
          rawError: exception,
          stackTrace: trace?.toString(),
        );

      case DioExceptionType.connectionError:
        return ApiError(
          message: 'فشل الاتصال بالخادم. يرجى التحقق من اتصالك بالإنترنت.',
          type: ApiErrorType.network,
          statusCode: exception.response?.statusCode,
          rawError: exception,
          stackTrace: trace?.toString(),
        );

      case DioExceptionType.unknown:
      default:
        if (exception.error is SocketException) {
          return ApiError(
            message: 'فشل الاتصال بالخادم. يرجى التحقق من اتصالك بالإنترنت.',
            type: ApiErrorType.network,
            statusCode: exception.response?.statusCode,
            rawError: exception,
            stackTrace: trace?.toString(),
          );
        }
        return ApiError(
          message: 'حدث خطأ غير متوقع: ${exception.message}',
          type: ApiErrorType.unknown,
          statusCode: exception.response?.statusCode,
          rawError: exception,
          stackTrace: trace?.toString(),
        );
    }
  }

  /// التعامل مع أخطاء استجابة Dio
  static ApiError _handleDioResponseError(DioException exception, [StackTrace? trace]) {
    final response = exception.response;
    final statusCode = response?.statusCode ?? 0;
    Map<String, dynamic>? responseData;
    
    try {
      if (response?.data != null) {
        if (response!.data is Map) {
          responseData = Map<String, dynamic>.from(response.data);
        } else if (response.data is String) {
          responseData = jsonDecode(response.data);
        }
      }
    } catch (e) {
      // فشل تحليل البيانات
    }

    String message = responseData?['message'] ?? 
                     responseData?['error'] ?? 
                     'حدث خطأ في الاتصال بالخادم';

    switch (statusCode) {
      case 400:
        return ApiError(
          message: message,
          statusCode: statusCode,
          type: ApiErrorType.badRequest,
          rawError: exception,
          stackTrace: trace?.toString(),
          responseData: responseData,
        );
      case 401:
        return ApiError(
          message: 'انتهت صلاحية الجلسة. يرجى تسجيل الدخول مرة أخرى.',
          statusCode: statusCode,
          type: ApiErrorType.authentication,
          rawError: exception,
          stackTrace: trace?.toString(),
          responseData: responseData,
        );
      case 403:
        return ApiError(
          message: 'ليس لديك صلاحية للوصول إلى هذا المورد.',
          statusCode: statusCode,
          type: ApiErrorType.authorization,
          rawError: exception,
          stackTrace: trace?.toString(),
          responseData: responseData,
        );
      case 404:
        return ApiError(
          message: 'المورد المطلوب غير موجود.',
          statusCode: statusCode,
          type: ApiErrorType.notFound,
          rawError: exception,
          stackTrace: trace?.toString(),
          responseData: responseData,
        );
      case 500:
      case 501:
      case 502:
      case 503:
        return ApiError(
          message: 'حدث خطأ في الخادم. يرجى المحاولة مرة أخرى لاحقًا.',
          statusCode: statusCode,
          type: ApiErrorType.server,
          rawError: exception,
          stackTrace: trace?.toString(),
          responseData: responseData,
        );
      default:
        return ApiError(
          message: message,
          statusCode: statusCode,
          type: ApiErrorType.unknown,
          rawError: exception,
          stackTrace: trace?.toString(),
          responseData: responseData,
        );
    }
  }

  @override
  String toString() {
    return 'ApiError: $message (Status: $statusCode, Type: $type)';
  }
}

/// مساعد للتعامل مع أخطاء API وعرضها للمستخدم
class ApiErrorHandler {
  /// عرض رسالة خطأ للمستخدم
  static void showErrorDialog(BuildContext context, ApiError error) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('خطأ'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(error.message),
              if (error.type == ApiErrorType.network)
                Padding(
                  padding: const EdgeInsets.only(top: 8.0),
                  child: Text(
                    'يرجى التحقق من اتصالك بالإنترنت والمحاولة مرة أخرى.',
                    style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                  ),
                ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('حسنًا'),
          ),
        ],
      ),
    );
  }

  /// عرض رسالة خطأ في شريط Snackbar
  static void showErrorSnackBar(BuildContext context, ApiError error) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(error.message),
        backgroundColor: Colors.red[700],
        duration: const Duration(seconds: 3),
        action: SnackBarAction(
          label: 'حسنًا',
          textColor: Colors.white,
          onPressed: () {
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
          },
        ),
      ),
    );
  }

  /// معالجة الخطأ وعرضه للمستخدم
  static void handleError(BuildContext context, dynamic error, {bool useDialog = false}) {
    final apiError = (error is ApiError) ? error : ApiError.fromException(error);
    
    // تسجيل الخطأ للتصحيح
    debugPrint('API Error: $apiError');
    if (apiError.stackTrace != null) {
      debugPrint('Stack Trace: ${apiError.stackTrace}');
    }
    
    // عرض الخطأ للمستخدم
    if (useDialog) {
      showErrorDialog(context, apiError);
    } else {
      showErrorSnackBar(context, apiError);
    }
    
    // معالجة خاصة لبعض أنواع الأخطاء
    if (apiError.type == ApiErrorType.authentication) {
      // تسجيل الخروج وإعادة التوجيه إلى صفحة تسجيل الدخول
      // يمكن إضافة المنطق هنا
    }
  }
}