import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:http/http.dart' as http;
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:shimmer/shimmer.dart';
import 'package:socket_io_client/socket_io_client.dart' as io;
import 'AppState.dart';
import 'models/classified_ad_model.dart';

// خدمة إدارة الإعلانات المبوبة
class ClassifiedService {
  final String baseUrl;
  final String authToken;
  io.Socket? socket;

  ClassifiedService({required this.baseUrl, required this.authToken}); // يجب تمرير appState.token عند الإنشاء

  Future<List<ClassifiedAd>> fetchClassifiedAds({
    String search = '',
    String filter = 'all',
    String sort = 'newest',
    int skip = 0,
    int limit = 10,
  }) async {
    try {
      final response = await http.get(
        Uri.parse(
            '$baseUrl/classifieds?search=$search&filter=$filter&sort=$sort&skip=$skip&limit=$limit'),
        headers: {'Authorization': 'Bearer $authToken'},
      );
      if (response.statusCode == 200) {
        return (jsonDecode(response.body) as List)
            .map((item) => ClassifiedAd.fromJson(item))
            .toList();
      } else {
        throw Exception('فشل في جلب الإعلانات: ${response.body}');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }

  Future<List<Map<String, dynamic>>> fetchNotifications() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/notifications'),
        headers: {'Authorization': 'Bearer $authToken'},
      );
      if (response.statusCode == 200) {
        return List<Map<String, dynamic>>.from(jsonDecode(response.body));
      } else {
        throw Exception('فشل في جلب الإشعارات: ${response.body}');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }

  Future<void> createClassifiedAd(
      String title,
      String description,
      String location,
      String price,
      String contact,
      String category,
      bool isNegotiable,
      File file,
      String creatorId) async {
    try {
      var request =
      http.MultipartRequest('POST', Uri.parse('$baseUrl/classifieds'));
      request.headers['Authorization'] = 'Bearer $authToken';
      request.fields['title'] = title;
      request.fields['description'] = description;
      request.fields['location'] = location;
      request.fields['price'] = price.isEmpty ? 'غير محدد' : price;
      request.fields['contact'] = contact;
      request.fields['category'] = category;
      request.fields['isNegotiable'] = isNegotiable.toString();
      request.fields['creatorId'] = creatorId;
      request.files.add(await http.MultipartFile.fromPath('file', file.path));
      final response = await request.send();
      if (response.statusCode != 201) {
        throw Exception('فشل في إنشاء الإعلان: ${response.reasonPhrase}');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }

  Future<void> markAsSold(String id) async {
    try {
      final response = await http.put(
        Uri.parse('$baseUrl/classifieds/$id'),
        headers: {
          'Authorization': 'Bearer $authToken',
          'Content-Type': 'application/json'
        },
        body: jsonEncode({'status': 'sold'}),
      );
      if (response.statusCode != 200) {
        throw Exception('فشل في تحديث الإعلان: ${response.body}');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }

  Future<void> deleteAd(String id) async {
    try {
      final response = await http.delete(
        Uri.parse('$baseUrl/classifieds/$id'),
        headers: {'Authorization': 'Bearer $authToken'},
      );
      if (response.statusCode != 200) {
        throw Exception('فشل في الحذف: ${response.body}');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }

  Future<void> requestContact(String adId, String requesterId) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/contact-requests'),
        headers: {
          'Authorization': 'Bearer $authToken',
          'Content-Type': 'application/json'
        },
        body: jsonEncode({'adId': adId, 'requesterId': requesterId}),
      );
      if (response.statusCode != 201) {
        throw Exception('فشل في إرسال طلب التواصل: ${response.body}');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }

  void setupSocketListeners(io.Socket socket, Function(dynamic) onNewAd) {
    this.socket = socket;
    socket.on('new_classified_ad', onNewAd);
  }
}

class Rayan7 extends StatelessWidget {
  const Rayan7({super.key});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider<AppState>(
      create: (_) => AppState(),
      child: const ClassifiedScreen(),
    );
  }
}

class ClassifiedScreen extends StatefulWidget {
  const ClassifiedScreen({super.key});

  @override
  _ClassifiedScreenState createState() => _ClassifiedScreenState();
}

class _ClassifiedScreenState extends State<ClassifiedScreen>
    with TickerProviderStateMixin {
  final PageController _adController = PageController();
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final ClassifiedService _classifiedService = ClassifiedService(
      baseUrl: AppState.getBackendUrl(), authToken: '');
  List<ClassifiedAd> _classifiedAds = [];
  List<Map<String, dynamic>> _notifications = [];
  int _currentAd = 0;
  int _selectedCategory = 0;
  int _visibleAds = 10;
  String _selectedSort = 'newest';
  String _selectedFilter = 'all';
  bool _isLoading = true;
  bool _isLoadingMore = false;
  bool _showScrollToTop = false;
  bool _showFeaturedOnly = false;
  Timer? _timer;
  final String _currentUserId = 'user1';

  final List<String> adImages = const [
    'https://via.placeholder.com/800x400',
    'https://via.placeholder.com/800x400',
    'https://via.placeholder.com/800x400',
  ];

  final List<Map<String, dynamic>> categories = const [
    {
      'icon': Icons.directions_car,
      'title': 'سيارات ومستلزماتها',
      'count': 45,
      'filter': 'cars'
    },
    {
      'icon': Icons.home,
      'title': 'عقارات ومستلزماتها',
      'count': 32,
      'filter': 'estate'
    },
    {
      'icon': Icons.phone_android,
      'title': 'إلكترونيات',
      'count': 67,
      'filter': 'electronics'
    },
    {'icon': Icons.chair, 'title': 'أثاث', 'count': 23, 'filter': 'furniture'},
    {
      'icon': Icons.fastfood,
      'title': 'منتجات غذائية',
      'count': 45,
      'filter': 'food'
    },
    {
      'icon': Icons.face,
      'title': 'الموضة والجمال',
      'count': 32,
      'filter': 'fashion_beauty'
    },
    {
      'icon': Icons.checkroom,
      'title': 'أزياء وملابس',
      'count': 67,
      'filter': 'clothing'
    },
    {'icon': Icons.toys, 'title': 'لعب أطفال', 'count': 23, 'filter': 'toys'},
    {
      'icon': Icons.weekend,
      'title': 'أثاث وديكور',
      'count': 67,
      'filter': 'furniture_decor'
    },
    {
      'icon': Icons.pets,
      'title': 'حيوانات أليفة',
      'count': 23,
      'filter': 'pets'
    },
    {'icon': Icons.category, 'title': 'أخرى', 'count': 67, 'filter': 'others'},
  ];

  @override
  void initState() {
    super.initState();
    _startAutoScroll();
    _scrollController.addListener(_scrollListener);
    _searchController.addListener(() => setState(() {}));
    final appState = Provider.of<AppState>(context, listen: false);
    if (appState.socket != null) {
      _classifiedService.setupSocketListeners(appState.socket!, (data) {
        _fetchClassifiedAds();
        setState(() {
          _notifications.add({
            'message': 'إعلان جديد: ${data['title']}',
            'date': DateTime.now().toString(),
            'read': false
          });
        });
      });
    }
    _fetchClassifiedAds();
    _fetchNotifications();
  }

  void _startAutoScroll() {
    _timer = Timer.periodic(const Duration(seconds: 5), (timer) {
      if (!mounted) return;
      setState(() => _currentAd = (_currentAd + 1) % adImages.length);
      _adController.animateToPage(_currentAd,
          duration: const Duration(milliseconds: 500), curve: Curves.easeIn);
    });
  }

  void _scrollListener() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 50 &&
        !_isLoadingMore) {
      _loadMoreAds();
    }
    setState(() {
      _showScrollToTop = _scrollController.position.pixels > 200;
    });
  }

  Future<void> _fetchClassifiedAds() async {
    setState(() => _isLoading = true);
    try {
      _classifiedAds = await _classifiedService.fetchClassifiedAds(
        search: _searchController.text,
        filter: _selectedFilter,
        sort: _selectedSort,
      );
      setState(() => _isLoading = false);
    } catch (e) {
      _showSnackBar('خطأ في جلب الإعلانات: $e', Colors.red);
      setState(() => _isLoading = false);
    }
  }

  Future<void> _fetchNotifications() async {
    try {
      _notifications = await _classifiedService.fetchNotifications();
      setState(() {});
    } catch (e) {
      _showSnackBar('خطأ في جلب الإشعارات: $e', Colors.red);
    }
  }

  Future<void> _loadMoreAds() async {
    setState(() => _isLoadingMore = true);
    try {
      final newAds = await _classifiedService.fetchClassifiedAds(
        search: _searchController.text,
        filter: _selectedFilter,
        sort: _selectedSort,
        skip: _visibleAds,
      );
      setState(() {
        _classifiedAds.addAll(newAds);
        _visibleAds += 10;
        _isLoadingMore = false;
      });
    } catch (e) {
      _showSnackBar('خطأ في تحميل المزيد: $e', Colors.red);
      setState(() => _isLoadingMore = false);
    }
  }

  Future<void> _refreshData() async {
    HapticFeedback.lightImpact();
    setState(() => _visibleAds = 10);
    await _fetchClassifiedAds();
    await _fetchNotifications();
  }

  @override
  void dispose() {
    _timer?.cancel();
    _adController.dispose();
    _scrollController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _showSnackBar(String message, [Color? backgroundColor]) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: backgroundColor ?? Colors.teal,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        elevation: 6,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return Scaffold(
      appBar: AppBar(
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: isDarkMode
                  ? [Colors.teal[900]!, Colors.orange[700]!]
                  : [Colors.teal, Colors.orangeAccent],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
        ),
        title: TextField(
          controller: _searchController,
          decoration: InputDecoration(
            hintText: 'ابحث عن إعلان...',
            border: InputBorder.none,
            hintStyle: const TextStyle(color: Colors.white70),
            filled: true,
            fillColor: Colors.white24,
            prefixIcon: const Icon(Icons.search, color: Colors.white),
            suffixIcon: _searchController.text.isNotEmpty
                ? IconButton(
                icon: const Icon(Icons.clear, color: Colors.white),
                onPressed: () {
                  setState(() => _searchController.clear());
                  HapticFeedback.selectionClick();
                })
                : null,
          ),
          style: const TextStyle(color: Colors.white),
          onSubmitted: (_) => _fetchClassifiedAds(),
        ),
        actions: [
          IconButton(
            icon: Stack(
              children: [
                const Icon(Icons.notifications, color: Colors.white),
                if (_notifications.isNotEmpty)
                  Positioned(
                    right: 0,
                    child: Container(
                      padding: const EdgeInsets.all(2),
                      decoration: const BoxDecoration(
                          color: Colors.red, shape: BoxShape.circle),
                      constraints: const BoxConstraints(minWidth: 12, minHeight: 12),
                      child: Text(
                        '${_notifications.length}',
                        style: const TextStyle(color: Colors.white, fontSize: 8),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
              ],
            ),
            onPressed: () {
              HapticFeedback.selectionClick();
              _showNotifications(context);
            },
          ),
          PopupMenuButton<String>(
            itemBuilder: (context) => [
              const PopupMenuItem(value: 'newest', child: Text('الأحدث')),
              const PopupMenuItem(value: 'price_asc', child: Text('السعر من الأقل')),
              const PopupMenuItem(value: 'price_desc', child: Text('السعر من الأعلى')),
            ],
            onSelected: (value) {
              HapticFeedback.selectionClick();
              setState(() {
                _selectedSort = value;
                _fetchClassifiedAds();
              });
            },
          ),
        ],
      ),
      body: Stack(
        children: [
          RefreshIndicator(
            onRefresh: _refreshData,
            color: Colors.teal,
            backgroundColor: isDarkMode ? Colors.grey[900] : Colors.white,
            child: SingleChildScrollView(
              controller: _scrollController,
              child: Column(
                children: [
                  _buildFilterChips(),
                  _buildAdsCarousel(),
                  _buildCategories(),
                  _buildFeaturedAds(),
                  if (_visibleAds < _classifiedAds.length) _buildLoadMoreButton(),
                  const SizedBox(height: 80),
                ],
              ),
            ),
          ),
          if (_isLoading) _buildSkeletonLoader(),
          if (_showScrollToTop)
            Positioned(
              bottom: 100,
              right: 16,
              child: FloatingActionButton(
                mini: true,
                backgroundColor: Colors.teal,
                onPressed: () {
                  HapticFeedback.mediumImpact();
                  _scrollController.animateTo(
                    0,
                    duration: const Duration(milliseconds: 500),
                    curve: Curves.easeInOut,
                  );
                },
                child: const Icon(Icons.arrow_upward, color: Colors.white),
              ),
            ),
        ],
      ),
      floatingActionButton: _buildFloatingActionButtons(),
    );
  }

  Widget _buildSkeletonLoader() {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: Column(
        children: [
          Container(
            height: 200,
            margin: const EdgeInsets.all(8),
            color: Colors.white,
          ),
          Container(
            height: 50,
            margin: const EdgeInsets.symmetric(horizontal: 8),
            color: Colors.white,
          ),
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: 0.65,
              crossAxisSpacing: 8,
              mainAxisSpacing: 8,
            ),
            itemCount: 6,
            itemBuilder: (context, index) => Container(
              margin: const EdgeInsets.all(8),
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChips() {
    return Container(
      height: 50,
      padding: const EdgeInsets.symmetric(horizontal: 8),
      child: Row(
        children: [
          AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: _showFeaturedOnly ? Colors.teal[100] : Colors.grey[200],
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.2),
                  spreadRadius: 1,
                  blurRadius: 3,
                ),
              ],
            ),
            child: GestureDetector(
              onTap: () {
                HapticFeedback.selectionClick();
                setState(() {
                  _showFeaturedOnly = !_showFeaturedOnly;
                  _fetchClassifiedAds();
                });
              },
              child: Row(
                children: [
                  Icon(
                    Icons.star,
                    size: 20,
                    color: _showFeaturedOnly ? Colors.teal[800] : Colors.grey,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    'مميز فقط',
                    style: TextStyle(
                      color: _showFeaturedOnly ? Colors.teal[800] : Colors.grey,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ),
          Expanded(
            child: ListView(
              scrollDirection: Axis.horizontal,
              children: [
                _buildFilterChip('الكل', 'all', Icons.all_inclusive),
                ...categories.map((category) => _buildFilterChip(
                    category['title'] as String,
                    category['filter'] as String,
                    category['icon'] as IconData)),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String label, String value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 4),
      child: FilterChip(
        label: Row(
          children: [
            Icon(icon,
                size: 16,
                color: _selectedFilter == value ? Colors.teal[800] : Colors.grey),
            const SizedBox(width: 4),
            Text(label),
          ],
        ),
        selected: _selectedFilter == value,
        onSelected: (v) {
          HapticFeedback.selectionClick();
          setState(() {
            _selectedFilter = value;
            _fetchClassifiedAds();
          });
        },
        selectedColor: Colors.teal[100],
        checkmarkColor: Colors.teal[800],
        elevation: _selectedFilter == value ? 4 : 1,
        pressElevation: 8,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      ),
    );
  }

  Widget _buildAdsCarousel() {
    return Column(
      children: [
        SizedBox(
          height: 200,
          child: PageView.builder(
            controller: _adController,
            itemCount: adImages.length,
            onPageChanged: (index) => setState(() => _currentAd = index),
            itemBuilder: (context, index) => GestureDetector(
              onTap: () {
                HapticFeedback.selectionClick();
                _showSnackBar('تم النقر على الإعلان الترويجي $index');
              },
              child: Transform(
                transform: Matrix4.identity()
                  ..setEntry(3, 2, 0.001)
                  ..rotateY(0.1 * (_currentAd - index)),
                alignment: Alignment.center,
                child: CachedNetworkImage(
                  imageUrl: adImages[index],
                  fit: BoxFit.cover,
                  placeholder: (context, url) => Shimmer.fromColors(
                    baseColor: Colors.grey[300]!,
                    highlightColor: Colors.grey[100]!,
                    child: Container(color: Colors.white),
                  ),
                  errorWidget: (context, url, error) =>
                      const Icon(Icons.error, color: Colors.grey),
                ),
              ),
            ),
          ),
        ),
        const SizedBox(height: 8),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: List.generate(
            adImages.length,
                (index) => AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              margin: const EdgeInsets.symmetric(horizontal: 4),
              width: _currentAd == index ? 20 : 8,
              height: 8,
              decoration: BoxDecoration(
                color: _currentAd == index ? Colors.teal : Colors.grey,
                borderRadius: BorderRadius.circular(4),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.2),
                    spreadRadius: 1,
                    blurRadius: 3,
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildCategories() {
    return Container(
      height: 120,
      padding: const EdgeInsets.symmetric(horizontal: 8),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: categories.length,
        itemBuilder: (context, index) => _buildCategoryItem(index),
      ),
    );
  }

  Widget _buildCategoryItem(int index) {
    return GestureDetector(
      onTap: () {
        HapticFeedback.selectionClick();
        setState(() => _selectedCategory = index);
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        width: 100,
        padding: const EdgeInsets.all(8),
        child: Column(
          children: [
            Container(
              decoration: BoxDecoration(
                color: _selectedCategory == index
                    ? Colors.teal[50]
                    : Colors.grey[200],
                borderRadius: BorderRadius.circular(15),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.2),
                    spreadRadius: 1,
                    blurRadius: 3,
                    offset: const Offset(2, 2),
                  ),
                  BoxShadow(
                    color: Colors.white.withOpacity(0.5),
                    spreadRadius: 1,
                    blurRadius: 3,
                    offset: const Offset(-2, -2),
                  ),
                ],
              ),
              padding: const EdgeInsets.all(12),
              child: Icon(
                categories[index]['icon'],
                color: _selectedCategory == index ? Colors.teal : Colors.grey[700],
                size: 30,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '${categories[index]['title']} (${categories[index]['count']})',
              style: TextStyle(
                color: _selectedCategory == index ? Colors.teal : Colors.black,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeaturedAds() {
    final filteredAds = _classifiedAds.where((ad) {
      final searchQuery = _searchController.text.toLowerCase();
      final matchesSearch = ad.title.toLowerCase().contains(searchQuery) ||
          ad.description.toLowerCase().contains(searchQuery);
      final matchesCategory = _selectedFilter == 'all' ||
          ad.category ==
              categories.firstWhere(
                      (cat) => cat['filter'] == _selectedFilter)['title'];
      final matchesFeatured = !_showFeaturedOnly || ad.isFeatured;
      return matchesSearch && matchesCategory && matchesFeatured;
    }).toList();

    filteredAds.sort((a, b) {
      if (_selectedSort == 'price_asc') {
        if (a.price == 'غير محدد' && b.price == 'غير محدد') return 0;
        if (a.price == 'غير محدد') return 1;
        if (b.price == 'غير محدد') return -1;
        return (double.tryParse(a.price) ?? 0)
            .compareTo(double.tryParse(b.price) ?? 0);
      } else if (_selectedSort == 'price_desc') {
        if (a.price == 'غير محدد' && b.price == 'غير محدد') return 0;
        if (a.price == 'غير محدد') return -1;
        if (b.price == 'غير محدد') return 1;
        return (double.tryParse(b.price) ?? 0)
            .compareTo(double.tryParse(a.price) ?? 0);
      }
      return b.date.compareTo(a.date); // newest
    });

    return Padding(
      padding: const EdgeInsets.all(8),
      child: filteredAds.isEmpty
          ? Center(
        child: Text(
          "لا توجد إعلانات متاحة",
          style: TextStyle(color: Colors.grey[600]),
        ),
      )
          : GridView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          childAspectRatio: 0.65,
          crossAxisSpacing: 8,
          mainAxisSpacing: 8,
        ),
        itemCount:
        filteredAds.length > _visibleAds ? _visibleAds : filteredAds.length,
        itemBuilder: (context, index) => FadeInAnimation(
          delay: index * 0.1,
          child: _buildAdItem(filteredAds[index]),
        ),
      ),
    );
  }

  Widget _buildAdItem(ClassifiedAd ad) {
    return Stack(
      children: [
        GestureDetector(
          onLongPress: () {
            HapticFeedback.selectionClick();
            _showAdPreview(context, ad);
          },
          child: Card(
            elevation: 0,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            child: InkWell(
              onTap: () {
                HapticFeedback.selectionClick();
                _showAdDetails(context, ad);
              },
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  color: Theme.of(context).cardColor,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withOpacity(0.2),
                      spreadRadius: 1,
                      blurRadius: 3,
                      offset: const Offset(2, 2),
                    ),
                    BoxShadow(
                      color: Colors.white.withOpacity(0.5),
                      spreadRadius: 1,
                      blurRadius: 3,
                      offset: const Offset(-2, -2),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: ClipRRect(
                        borderRadius:
                        const BorderRadius.vertical(top: Radius.circular(12)),
                        child: CachedNetworkImage(
                          imageUrl: '${_classifiedService.baseUrl}${ad.fileUrl}',
                          fit: BoxFit.cover,
                          width: double.infinity,
                          placeholder: (context, url) => Shimmer.fromColors(
                            baseColor: Colors.grey[300]!,
                            highlightColor: Colors.grey[100]!,
                            child: Container(color: Colors.white),
                          ),
                          errorWidget: (context, url, error) =>
                              const Icon(Icons.error, color: Colors.grey),
                        ),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.all(8),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            ad.title,
                            style: const TextStyle(fontWeight: FontWeight.bold),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 4),
                          Text(
                            ad.price == 'غير محدد'
                                ? 'غير محدد${ad.isNegotiable ? ' (قابل للتفاوض)' : ''}'
                                : '\$${ad.price}${ad.isNegotiable ? ' (قابل للتفاوض)' : ''}',
                            style: const TextStyle(color: Colors.teal, fontSize: 14),
                          ),
                          const SizedBox(height: 4),
                          Row(
                            children: [
                              const Icon(Icons.location_on,
                                  size: 16, color: Colors.grey),
                              Flexible(
                                child: Text(
                                  ad.location,
                                  style: const TextStyle(color: Colors.grey),
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 4),
                          Text(
                            DateFormat('dd/MM/yyyy').format(ad.date),
                            style: const TextStyle(color: Colors.grey, fontSize: 12),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            ad.status == 'sold' ? 'مباع' : 'نشط',
                            style: TextStyle(
                              color: ad.status == 'sold' ? Colors.red : Colors.green,
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
        if (ad.isFeatured)
          Positioned(
            top: 8,
            left: 8,
            child: DottedBorder(
              color: Colors.amber,
              strokeWidth: 2,
              dashPattern: const [4, 4],
              borderType: BorderType.RRect,
              radius: const Radius.circular(8),
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                color: Colors.amber.withOpacity(0.2),
                child: const Text(
                  'مميز',
                  style: TextStyle(color: Colors.amber, fontSize: 12),
                ),
              ),
            ),
          ),
        Positioned(
          top: 8,
          right: 8,
          child: Row(
            children: [
              IconButton(
                icon: Icon(
                  ad.isFavorite ? Icons.star : Icons.star_border,
                  size: 20,
                  color: ad.isFavorite ? Colors.yellow[700] : Colors.teal,
                ),
                onPressed: () {
                  HapticFeedback.selectionClick();
                  setState(() {
                    ad.isFavorite = !ad.isFavorite;
                  });
                  _showSnackBar(
                    ad.isFavorite
                        ? 'تمت الإضافة إلى المفضلة'
                        : 'تمت الإزالة من المفضلة',
                    Colors.teal,
                  );
                },
              ),
              IconButton(
                icon: const Icon(Icons.share, size: 20, color: Colors.teal),
                onPressed: () {
                  HapticFeedback.selectionClick();
                  Share.share('${ad.title} - ${ad.price} - ${ad.location}');
                },
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildLoadMoreButton() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: ScaleTransitionButton(
        onPressed: _isLoadingMore ? null : _loadMoreAds,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.teal,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
          padding: const EdgeInsets.symmetric(horizontal: 40, vertical: 12),
          elevation: 4,
        ),
        child: _isLoadingMore
            ? const CircularProgressIndicator(color: Colors.white)
            : const Text('تحميل المزيد', style: TextStyle(color: Colors.white)),
      ),
    );
  }

  Widget _buildFloatingActionButtons() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        Tooltip(
          message: 'إعلاناتي',
          child: ScaleTransitionButton(
            onPressed: () {
              HapticFeedback.selectionClick();
              _showMyAds(context);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.teal,
              elevation: 4,
              shape: const CircleBorder(),
              padding: const EdgeInsets.all(16),
            ),
            child: const Icon(Icons.list_alt, color: Colors.white),
          ),
        ),
        const SizedBox(height: 16),
        Tooltip(
          message: 'إنشاء إعلان',
          child: ScaleTransitionButton(
            onPressed: () {
              HapticFeedback.selectionClick();
              _showCreateAdDialog(context);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.teal,
              elevation: 4,
              shape: const CircleBorder(),
              padding: const EdgeInsets.all(16),
            ),
            child: const Icon(Icons.post_add, color: Colors.white),
          ),
        ),
      ],
    );
  }

  void _showCreateAdDialog(BuildContext context) {
    final titleController = TextEditingController();
    final descriptionController = TextEditingController();
    final locationController = TextEditingController();
    final priceController = TextEditingController();
    final contactController = TextEditingController();
    String selectedCategory = categories[0]['title'];
    bool isNegotiable = false;
    File? selectedFile;

    showGeneralDialog(
      context: context,
      barrierDismissible: true,
      barrierLabel: MaterialLocalizations.of(context).modalBarrierDismissLabel,
      barrierColor: Colors.black54,
      transitionDuration: const Duration(milliseconds: 300),
      pageBuilder: (context, anim1, anim2) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        title: Container(
          padding: const EdgeInsets.all(10),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [Colors.teal[700]!, Colors.orange[400]!],
            ),
            borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: const Text(
            "إنشاء إعلان جديد",
            style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
            textAlign: TextAlign.center,
          ),
        ),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDialogTextField(
                label: "عنوان الإعلان:",
                controller: titleController,
                hint: "أدخل عنوان الإعلان...",
              ),
              _buildDialogTextField(
                label: "الوصف:",
                controller: descriptionController,
                hint: "أدخل وصف الإعلان...",
                maxLines: 3,
              ),
              _buildCategoryDropdown(
                label: "الفئة:",
                selectedCategory: selectedCategory,
                onChanged: (value) => setState(() => selectedCategory = value!),
              ),
              _buildDialogTextField(
                label: "الموقع:",
                controller: locationController,
                hint: "أدخل الموقع...",
              ),
              _buildDialogTextField(
                label: "السعر:",
                controller: priceController,
                hint: "أدخل السعر (اختياري)...",
                keyboardType: TextInputType.number,
              ),
              Row(
                children: [
                  Checkbox(
                    value: isNegotiable,
                    onChanged: (value) => setState(() => isNegotiable = value!),
                  ),
                  const Text("السعر قابل للتفاوض"),
                ],
              ),
              _buildDialogTextField(
                label: "رقم التواصل:",
                controller: contactController,
                hint: "أدخل رقم التواصل...",
                keyboardType: TextInputType.phone,
              ),
              const SizedBox(height: 10),
              _buildFilePickerButton(
                onPressed: () async {
                  FilePickerResult? result = await FilePicker.platform.pickFiles(
                    type: FileType.custom,
                    allowedExtensions: ['jpg', 'jpeg', 'png', 'mp4', 'mov', 'pdf'],
                  );
                  if (result != null) {
                    setState(() => selectedFile = File(result.files.single.path!));
                  }
                },
                selectedFileName: selectedFile?.path.split('/').last,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text("إلغاء"),
          ),
          ScaleTransitionButton(
            onPressed: () => _createClassifiedAd(
                context,
                titleController.text,
                descriptionController.text,
                locationController.text,
                priceController.text,
                contactController.text,
                selectedCategory,
                isNegotiable,
                selectedFile),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.teal,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
            ),
            child: const Text("إنشاء", style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
      transitionBuilder: (context, anim1, anim2, child) => FadeTransition(
        opacity: CurvedAnimation(parent: anim1, curve: Curves.easeInOut),
        child: ScaleTransition(
          scale: CurvedAnimation(parent: anim1, curve: Curves.easeInOut),
          child: child,
        ),
      ),
    );
  }

  Widget _buildDialogTextField({
    required String label,
    required TextEditingController controller,
    required String hint,
    int maxLines = 1,
    TextInputType? keyboardType,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.teal[800],
          ),
        ),
        const SizedBox(height: 8),
        TextField(
          controller: controller,
          maxLines: maxLines,
          keyboardType: keyboardType,
          decoration: InputDecoration(
            hintText: hint,
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(10)),
            filled: true,
            fillColor: Colors.grey[100],
            contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
          ),
        ),
        const SizedBox(height: 10),
      ],
    );
  }

  Widget _buildCategoryDropdown({
    required String label,
    required String selectedCategory,
    required Function(String?) onChanged,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.teal[800],
          ),
        ),
        const SizedBox(height: 8),
        DropdownButton<String>(
          value: selectedCategory,
          items: categories
              .map((category) => DropdownMenuItem<String>(
            value: category['title'] as String,
            child: Text(category['title'] as String),
          ))
              .toList(),
          onChanged: onChanged,
          isExpanded: true,
          borderRadius: BorderRadius.circular(10),
          underline: Container(),
        ),
        const SizedBox(height: 10),
      ],
    );
  }

  Widget _buildFilePickerButton({
    required VoidCallback onPressed,
    required String? selectedFileName,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "اختر ملف (صورة/فيديو/PDF):",
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.teal[800],
          ),
        ),
        const SizedBox(height: 8),
        ScaleTransitionButton(
          onPressed: onPressed,
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.teal,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(Icons.upload_file, color: Colors.white),
              const SizedBox(width: 8),
              Text(
                selectedFileName ?? "اختيار ملف",
                style: const TextStyle(color: Colors.white),
              ),
            ],
          ),
        ),
        const SizedBox(height: 10),
      ],
    );
  }

  Future<void> _createClassifiedAd(
      BuildContext context,
      String title,
      String description,
      String location,
      String price,
      String contact,
      String category,
      bool isNegotiable,
      File? file) async {
    if (title.isEmpty ||
        description.isEmpty ||
        location.isEmpty ||
        contact.isEmpty ||
        file == null) {
      _showSnackBar("يرجى ملء جميع الحقول المطلوبة", Colors.orange);
      return;
    }
    if (!RegExp(r'^\+?\d{8,15}$').hasMatch(contact)) {
      _showSnackBar("رقم التواصل غير صالح", Colors.orange);
      return;
    }
    try {
      await _classifiedService.createClassifiedAd(
        title,
        description,
        location,
        price,
        contact,
        category,
        isNegotiable,
        file,
        _currentUserId,
      );
      Navigator.pop(context);
      _fetchClassifiedAds();
      _fetchNotifications();
      _showSnackBar("تم إنشاء الإعلان بنجاح!", Colors.green);
      // Show creation animation
      showDialog(
        context: context,
        builder: (context) => const CheckmarkPulseAnimation(),
      );
      await Future.delayed(const Duration(seconds: 1));
      Navigator.pop(context);
    } catch (e) {
      _showSnackBar("خطأ في إنشاء الإعلان: $e", Colors.red);
    }
  }

  void _showAdPreview(BuildContext context, ClassifiedAd ad) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CachedNetworkImage(
              imageUrl: '${_classifiedService.baseUrl}${ad.fileUrl}',
              width: 100,
              height: 100,
              fit: BoxFit.cover,
              placeholder: (context, url) => Shimmer.fromColors(
                baseColor: Colors.grey[300]!,
                highlightColor: Colors.grey[100]!,
                child: Container(color: Colors.white),
              ),
              errorWidget: (context, url, error) => const Icon(Icons.error),
            ),
            const SizedBox(height: 8),
            Text(
              ad.title,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            Text(ad.price == 'غير محدد' ? 'غير محدد' : '\$${ad.price}'),
            Text('الموقع: ${ad.location}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  void _showAdDetails(BuildContext context, ClassifiedAd ad) {
    showGeneralDialog(
      context: context,
      barrierDismissible: true,
      barrierLabel: MaterialLocalizations.of(context).modalBarrierDismissLabel,
      barrierColor: Colors.black54,
      transitionDuration: const Duration(milliseconds: 300),
      pageBuilder: (context, anim1, anim2) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        title: Text(ad.title, style: const TextStyle(fontWeight: FontWeight.bold)),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              CachedNetworkImage(
                imageUrl: '${_classifiedService.baseUrl}${ad.fileUrl}',
                width: double.infinity,
                height: 200,
                fit: BoxFit.cover,
                placeholder: (context, url) => Shimmer.fromColors(
                  baseColor: Colors.grey[300]!,
                  highlightColor: Colors.grey[100]!,
                  child: Container(color: Colors.white),
                ),
                errorWidget: (context, url, error) => const Icon(Icons.error),
              ),
              const SizedBox(height: 16),
              Text('الوصف: ${ad.description}'),
              const SizedBox(height: 8),
              Text(
                'السعر: ${ad.price == 'غير محدد' ? 'غير محدد' : '\$${ad.price}'}${ad.isNegotiable ? ' (قابل للتفاوض)' : ''}',
                style: const TextStyle(color: Colors.teal),
              ),
              const SizedBox(height: 8),
              Text('الموقع: ${ad.location}'),
              const SizedBox(height: 8),
              TextButton.icon(
                onPressed: () {
                  _showSnackBar('سيتم إضافة خريطة في الإصدار القادم', Colors.teal);
                },
                icon: const Icon(Icons.map, color: Colors.teal),
                label:
                const Text('عرض على الخريطة', style: TextStyle(color: Colors.teal)),
              ),
              const SizedBox(height: 8),
              Text('الفئة: ${ad.category}'),
              const SizedBox(height: 8),
              Text('رقم التواصل: ${ad.contact}'),
              const SizedBox(height: 8),
              Text(
                  'التاريخ: ${DateFormat('dd/MM/yyyy HH:mm').format(ad.date)}'),
              const SizedBox(height: 8),
              Text(
                'الحالة: ${ad.status == 'sold' ? 'مباع' : 'نشط'}',
                style: TextStyle(
                    color: ad.status == 'sold' ? Colors.red : Colors.green),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
          ScaleTransitionButton(
            onPressed: () => _requestContact(context, ad.id, ad.title, ad.contact),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.teal,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
            ),
            child: const Text('تواصل الآن', style: TextStyle(color: Colors.white)),
          ),
          if (ad.creatorId == _currentUserId && ad.status != 'sold')
            ScaleTransitionButton(
              onPressed: () => _markAsSold(ad.id),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
              ),
              child: const Text('تم البيع', style: TextStyle(color: Colors.white)),
            ),
        ],
      ),
      transitionBuilder: (context, anim1, anim2, child) => FadeTransition(
        opacity: CurvedAnimation(parent: anim1, curve: Curves.easeInOut),
        child: ScaleTransition(
          scale: CurvedAnimation(parent: anim1, curve: Curves.easeInOut),
          child: child,
        ),
      ),
    );
  }

  Future<void> _requestContact(
      BuildContext context, String adId, String title, String contact) async {
    try {
      await _classifiedService.requestContact(adId, _currentUserId);
      Navigator.pop(context);
      _showSnackBar('تم إرسال طلب التواصل على $contact بشأن $title', Colors.green);
      await _fetchNotifications();
      // Show contact request animation
      showDialog(
        context: context,
        builder: (context) => const CheckmarkPulseAnimation(),
      );
      await Future.delayed(const Duration(seconds: 1));
      Navigator.pop(context);
    } catch (e) {
      _showSnackBar('خطأ في إرسال طلب التواصل: $e', Colors.red);
    }
  }

  Future<void> _markAsSold(String id) async {
    try {
      await _classifiedService.markAsSold(id);
      _fetchClassifiedAds();
      _fetchNotifications();
      Navigator.pop(context);
      _showSnackBar('تم تعليم الإعلان كمباع', Colors.green);
      // Show sold animation
      showDialog(
        context: context,
        builder: (context) => const CheckmarkPulseAnimation(),
      );
      await Future.delayed(const Duration(seconds: 1));
      Navigator.pop(context);
    } catch (e) {
      _showSnackBar('خطأ في تحديث الإعلان: $e', Colors.red);
    }
  }

  Future<void> _deleteAd(String id) async {
    try {
      await _classifiedService.deleteAd(id);
      _fetchClassifiedAds();
      _fetchNotifications();
      Navigator.pop(context);
      _showSnackBar('تم حذف الإعلان', Colors.green);
    } catch (e) {
      _showSnackBar('خطأ في الحذف: $e', Colors.red);
    }
  }

  void _showMyAds(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => SizedBox(
        height: MediaQuery.of(context).size.height * 0.8,
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.all(16),
              child: Text(
                'إعلاناتي',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.teal[900],
                ),
              ),
            ),
            Expanded(
              child: _classifiedAds
                  .where((ad) => ad.creatorId == _currentUserId)
                  .isEmpty
                  ? Center(
                child: Text(
                  'لم تقم بإنشاء إعلانات بعد',
                  style: TextStyle(color: Colors.grey[600]),
                ),
              )
                  : ListView.builder(
                itemCount: _classifiedAds
                    .where((ad) => ad.creatorId == _currentUserId)
                    .length,
                itemBuilder: (context, index) {
                  final myAds = _classifiedAds
                      .where((ad) => ad.creatorId == _currentUserId)
                      .toList();
                  final ad = myAds[index];
                  return FadeInAnimation(
                    delay: index * 0.1,
                    child: ListTile(
                      title: Text(ad.title),
                      subtitle: Text(
                          '${ad.price} - ${ad.location} - ${ad.status}'),
                      trailing: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          if (ad.status != 'sold')
                            IconButton(
                              icon: const Icon(Icons.check, color: Colors.green),
                              onPressed: () => _markAsSold(ad.id),
                            ),
                          IconButton(
                            icon: const Icon(Icons.delete, color: Colors.red),
                            onPressed: () => _deleteAd(ad.id),
                          ),
                        ],
                      ),
                      onTap: () => _showAdDetails(context, ad),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showNotifications(BuildContext context) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.5,
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Text(
              'الإشعارات',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.teal[900],
              ),
            ),
            const SizedBox(height: 10),
            Expanded(
              child: _notifications.isEmpty
                  ? Center(
                child: Text(
                  'لا توجد إشعارات جديدة حالياً',
                  style: TextStyle(color: Colors.grey[600]),
                ),
              )
                  : ListView.builder(
                itemCount: _notifications.length,
                itemBuilder: (context, index) => FadeInAnimation(
                  delay: index * 0.1,
                  child: ListTile(
                    leading:
                    const Icon(Icons.notifications_active, color: Colors.teal),
                    title: Text(_notifications[index]['message']),
                    subtitle: Text(
                      DateFormat('dd/MM/yyyy HH:mm').format(
                          DateTime.parse(_notifications[index]['date'])),
                    ),
                    trailing: _notifications[index]['read']
                        ? null
                        : const Icon(Icons.circle, color: Colors.teal, size: 10),
                    onTap: () => setState(
                            () => _notifications[index]['read'] = true),
                  ),
                ),
              ),
            ),
            ScaleTransitionButton(
              onPressed: () {
                setState(() => _notifications.clear());
                Navigator.pop(context);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
              child: const Text('مسح الكل', style: TextStyle(color: Colors.white)),
            ),
          ],
        ),
      ),
    );
  }
}

// Custom Animation Widgets
class FadeInAnimation extends StatefulWidget {
  final Widget child;
  final double delay;

  const FadeInAnimation({super.key, required this.child, this.delay = 0});

  @override
  _FadeInAnimationState createState() => _FadeInAnimationState();
}

class _FadeInAnimationState extends State<FadeInAnimation>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _animation = CurvedAnimation(parent: _controller, curve: Curves.easeIn);
    Future.delayed(Duration(milliseconds: (widget.delay * 1000).toInt()), () {
      if (mounted) _controller.forward();
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return FadeTransition(
      opacity: _animation,
      child: widget.child,
    );
  }
}

class ScaleTransitionButton extends StatelessWidget {
  final VoidCallback? onPressed;
  final Widget child;
  final ButtonStyle? style;

  const ScaleTransitionButton({
    super.key,
    required this.onPressed,
    required this.child,
    this.style,
  });

  @override
  Widget build(BuildContext context) {
    return _ScaleTransitionButton(
      onPressed: onPressed,
      style: style,
      child: child,
    );
  }
}

class _ScaleTransitionButton extends StatefulWidget {
  final VoidCallback? onPressed;
  final Widget child;
  final ButtonStyle? style;

  const _ScaleTransitionButton({
    required this.onPressed,
    required this.child,
    this.style,
  });

  @override
  _ScaleTransitionButtonState createState() => _ScaleTransitionButtonState();
}

class _ScaleTransitionButtonState extends State<_ScaleTransitionButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 100),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.95).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: (_) => _controller.forward(),
      onTapUp: (_) {
        _controller.reverse();
        widget.onPressed?.call();
      },
      onTapCancel: () => _controller.reverse(),
      child: ScaleTransition(
        scale: _scaleAnimation,
        child: ElevatedButton(
          onPressed: widget.onPressed,
          style: widget.style,
          child: widget.child,
        ),
      ),
    );
  }
}

class CheckmarkPulseAnimation extends StatefulWidget {
  const CheckmarkPulseAnimation({super.key});

  @override
  _CheckmarkPulseAnimationState createState() => _CheckmarkPulseAnimationState();
}

class _CheckmarkPulseAnimationState extends State<CheckmarkPulseAnimation>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _animation = Tween<double>(begin: 0.8, end: 1.2).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );
    _controller.repeat(reverse: true);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: ScaleTransition(
        scale: _animation,
        child: const Icon(
          Icons.check_circle,
          color: Colors.green,
          size: 100,
        ),
      ),
    );
  }
}