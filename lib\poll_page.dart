import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:share_plus/share_plus.dart';
import 'package:lottie/lottie.dart';
import 'package:haptic_feedback/haptic_feedback.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'dart:async';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:percent_indicator/circular_percent_indicator.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'appstate.dart';
import 'package:timeago/timeago.dart' as timeago;

class PollModel {
  final String id;
  final String question;
  final List<String> options;
  final List<int> votes;
  final DateTime endTime;

  PollModel({
    required this.id,
    required this.question,
    required this.options,
    required this.votes,
    required this.endTime,
  });

  factory PollModel.fromJson(Map<String, dynamic> json) {
    return PollModel(
      id: json['id'],
      question: json['question'],
      options: List<String>.from(json['options']),
      votes: List<int>.from(json['votes']),
      endTime: DateTime.parse(json['endTime']),
    );
  }
}

class PollPage extends StatefulWidget {
  const PollPage({super.key});

  @override
  _PollPageState createState() => _PollPageState();
}

class _PollPageState extends State<PollPage> with SingleTickerProviderStateMixin {
  PollModel _poll = PollModel(
    id: 'poll_1',
    question: 'ما رأيك في الموضوع؟', // سيتم استبداله بـ AppLocalizations لاحقًا
    options: ["الخيار الأول", "الخيار الثاني", "الخيار الثالث"],
    votes: [0, 0, 0],
    endTime: DateTime(2025, 3, 25, 12, 0),
  );
  int selectedOption = -1;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Timer _timer;
  String _remainingTime = '';
  bool _isLoading = false;

  int get totalVotes => _poll.votes.reduce((a, b) => a + b);

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.forward();
    _startCountdown();
    _fetchVotes();
  }

  void _startCountdown() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (!mounted) {
        timer.cancel();
        return;
      }
      final now = DateTime.now();
      final difference = _poll.endTime.difference(now);
      if (difference.isNegative) {
        setState(() => _remainingTime = 'انتهى الاستطلاع');
        timer.cancel();
      } else {
        setState(() {
          _remainingTime =
          '${difference.inDays} أيام ${difference.inHours % 24} ساعات ${difference.inMinutes % 60} دقائق';
        });
      }
    });
  }

  double _calculateTimeProgress() {
    final now = DateTime.now();
    final totalDuration = _poll.endTime.difference(DateTime(2025, 3, 1)).inSeconds;
    final remainingDuration = _poll.endTime.difference(now).inSeconds;
    if (remainingDuration <= 0) return 1.0;
    return 1 - (remainingDuration / totalDuration).clamp(0.0, 1.0);
  }

  Future<void> _fetchVotes() async {
    setState(() => _isLoading = true);
    var connectivityResult = await Connectivity().checkConnectivity();
    if (connectivityResult == ConnectivityResult.none) {
      _showSnackBar('لا يوجد اتصال بالإنترنت');
      setState(() => _isLoading = false);
      return;
    }

    final appState = Provider.of<AppState>(context, listen: false);
    try {
      final response = await http.get(
        Uri.parse('${AppState.getBackendUrl()}/api/votes'),
        headers: {'Authorization': 'Bearer ${appState.token ?? defaultAuthToken}'},
      );
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (mounted) {
          setState(() {
            _poll = PollModel(
              id: _poll.id,
              question: _poll.question,
              options: data['options'] != null ? List<String>.from(data['options']) : _poll.options,
              votes: List<int>.from(data['votes'] ?? [0, 0, 0]),
              endTime: _poll.endTime,
            );
            _isLoading = false;
          });
        }
      } else {
        _showSnackBar('فشل في جلب الأصوات: ${response.body}');
      }
    } catch (e) {
      _showSnackBar('خطأ في جلب الأصوات: $e');
    } finally {
      if (mounted) setState(() => _isLoading = false);
    }
  }

  Future<void> _sendVoteToServer(int optionIndex) async {
    await Haptics.vibrate(HapticsType.success);
    FirebaseAnalytics.instance.logEvent(
      name: 'vote_poll',
      parameters: {'poll_id': _poll.id, 'option_index': optionIndex},
    );
    var connectivityResult = await Connectivity().checkConnectivity();
    if (connectivityResult == ConnectivityResult.none) {
      _showSnackBar('لا يوجد اتصال بالإنترنت');
      return;
    }

    final appState = Provider.of<AppState>(context, listen: false);
    final url = Uri.parse('${AppState.getBackendUrl()}/api/vote');
    final headers = {
      'Authorization': 'Bearer ${appState.token ?? defaultAuthToken}',
      'Content-Type': 'application/json',
    };
    final body = jsonEncode({
      'option': optionIndex,
      'timestamp': DateTime.now().toIso8601String(),
    });

    try {
      final response = await http.post(url, headers: headers, body: body);
      if (response.statusCode == 200) {
        _showVoteAnimation();
        _showSnackBar('تم تسجيل صوتك بنجاح!');
        await _fetchVotes();
        if (mounted) {
          setState(() {
            selectedOption = optionIndex;
            _animationController.forward(from: 0);
          });
        }
      } else {
        await Haptics.vibrate(HapticsType.error);
        _showSnackBar('فشل في تسجيل الصوت: ${response.statusCode}');
      }
    } catch (e) {
      await Haptics.vibrate(HapticsType.error);
      _showSnackBar('خطأ في الاتصال: $e');
    }
  }

  void _showVoteAnimation() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        child: Lottie.network(
          'https://assets.lottiefiles.com/packages/lf20_jcikwtgl.json',
          width: 200,
          height: 200,
          repeat: false,
          onLoaded: (composition) {
            Future.delayed(composition.duration, () {
              if (mounted) Navigator.pop(context);
            });
          },
        ),
      ),
    );
  }

  Future<void> _createNewPoll() async {
    final questionController = TextEditingController();
    final optionsController = TextEditingController();
    DateTime? selectedEndTime;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
          title: const Text('إنشاء استطلاع جديد',
              style: TextStyle(color: Colors.teal, fontWeight: FontWeight.bold)),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: questionController,
                  decoration: InputDecoration(
                    hintText: 'السؤال',
                    filled: true,
                    fillColor: Colors.grey[100],
                    border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: BorderSide.none),
                  ),
                ),
                const SizedBox(height: 12),
                TextField(
                  controller: optionsController,
                  decoration: InputDecoration(
                    hintText: 'الخيارات (مفصولة بفواصل)',
                    filled: true,
                    fillColor: Colors.grey[100],
                    border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: BorderSide.none),
                  ),
                  maxLines: 3,
                ),
                const SizedBox(height: 12),
                ElevatedButton(
                  onPressed: () async {
                    final pickedDateTime = await showDateTimePicker(context);
                    setDialogState(() => selectedEndTime = pickedDateTime);
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.teal[700],
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12)),
                  ),
                  child: Text(selectedEndTime == null
                      ? 'اختيار وقت الانتهاء'
                      : 'تم اختيار: ${selectedEndTime.toString().substring(0, 16)}'),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('إلغاء', style: TextStyle(color: Colors.grey)),
            ),
            ElevatedButton(
              onPressed: () async {
                if (questionController.text.isNotEmpty &&
                    optionsController.text.isNotEmpty &&
                    selectedEndTime != null) {
                  final appState = Provider.of<AppState>(context, listen: false);
                  final url = Uri.parse('${AppState.getBackendUrl()}/api/polls');
                  final headers = {
                    'Authorization': 'Bearer ${appState.token ?? ''}',
                    'Content-Type': 'application/json',
                  };
                  final body = jsonEncode({
                    'question': questionController.text,
                    'options': optionsController.text
                        .split(',')
                        .map((e) => e.trim())
                        .toList(),
                    'endTime': selectedEndTime!.toIso8601String(),
                  });

                  var connectivityResult = await Connectivity().checkConnectivity();
                  if (connectivityResult == ConnectivityResult.none) {
                    _showSnackBar('لا يوجد اتصال بالإنترنت');
                    return;
                  }

                  try {
                    final response =
                    await http.post(url, headers: headers, body: body);
                    if (response.statusCode == 201) {
                      FirebaseAnalytics.instance.logEvent(
                        name: 'create_poll',
                        parameters: {'poll_id': _poll.id},
                      );
                      _showSnackBar('تم إرسال الاستطلاع للموافقة!');
                      Navigator.pop(context);
                    } else {
                      _showSnackBar(
                          'فشل في إنشاء الاستطلاع: ${response.statusCode}');
                    }
                  } catch (e) {
                    _showSnackBar('خطأ في الاتصال: $e');
                  }
                } else {
                  _showSnackBar('يرجى ملء جميع الحقول');
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.teal[700],
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12)),
              ),
              child: const Text('إنشاء', style: TextStyle(color: Colors.white)),
            ),
          ],
        ),
      ),
    );
  }

  void _showCommentsSection() {
    final TextEditingController commentController = TextEditingController();
    final appState = Provider.of<AppState>(context, listen: false);
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.9,
        builder: (context, scrollController) {
          return StatefulBuilder(
            builder: (context, setModalState) {
              return Padding(
                padding: EdgeInsets.all(16),
                child: Column(
                  children: [
                    Text(
                      'التعليقات (${appState.pollComments.length})',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: appState.isDarkMode ? Colors.teal[200] : Colors.teal[900],
                      ),
                    ),
                    SizedBox(height: 16),
                    TextField(
                      controller: commentController,
                      decoration: InputDecoration(
                        hintText: 'أضف تعليقًا...',
                        filled: true,
                        fillColor: appState.isDarkMode ? Colors.grey[800] : Colors.grey[100],
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide.none,
                        ),
                      ),
                      style: TextStyle(
                        color: appState.isDarkMode ? Colors.white : Colors.black,
                      ),
                    ),
                    SizedBox(height: 8),
                    ElevatedButton(
                      onPressed: () {
                        if (commentController.text.isNotEmpty) {
                          FirebaseAnalytics.instance.logEvent(
                            name: 'add_comment',
                            parameters: {'poll_id': _poll.id},
                          );
                          appState.addPollComment(commentController.text);
                          commentController.clear();
                          setModalState(() {});
                        } else {
                          _showSnackBar('لا يمكن إرسال تعليق فارغ');
                        }
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.teal[700],
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12)),
                      ),
                      child: Text('إرسال', style: TextStyle(color: Colors.white)),
                    ),
                    SizedBox(height: 16),
                    Expanded(
                      child: appState.pollComments.isEmpty
                          ? Center(
                        child: Text('لا توجد تعليقات بعد، كن الأول!',
                            style: TextStyle(
                                color: appState.isDarkMode
                                    ? Colors.teal[300]
                                    : Colors.teal[600])),
                      )
                          : ListView.builder(
                        controller: scrollController,
                        itemCount: appState.pollComments.length,
                        itemBuilder: (context, index) {
                          final comment = appState.pollComments[index];
                          return Card(
                            color: appState.isDarkMode ? Colors.grey[850] : Colors.white,
                            margin: EdgeInsets.symmetric(vertical: 4, horizontal: 0),
                            elevation: 1,
                            child: ListTile(
                              leading: CircleAvatar(
                                backgroundColor: Colors.teal[700],
                                backgroundImage: comment.userAvatar != null && comment.userAvatar!.isNotEmpty
                                    ? NetworkImage(comment.userAvatar!)
                                    : null,
                                child: comment.userAvatar == null || comment.userAvatar!.isEmpty
                                    ? Text(
                                  comment.username.isNotEmpty ? comment.username[0].toUpperCase() : 'U',
                                  style: TextStyle(color: Colors.white),
                                )
                                    : null,
                              ),
                              title: Text(comment.content,
                                  style: TextStyle(
                                      color: appState.isDarkMode
                                          ? Colors.white
                                          : Colors.black)),
                              subtitle: Text(
                                '${comment.username} • ${timeago.format(comment.createdAt, locale: 'ar')}',
                                style: TextStyle(
                                    fontSize: 12,
                                    color: appState.isDarkMode
                                        ? Colors.grey[400]
                                        : Colors.grey[600]),
                              ),
                              // Add like button or other actions if needed in the future
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              );
            },
          );
        },
      ),
    );
  }

  Future<DateTime?> showDateTimePicker(BuildContext context) async {
    final date = await showDatePicker(
      context: context,
      initialDate: DateTime.now().add(const Duration(days: 1)),
      firstDate: DateTime.now(),
      lastDate: DateTime(2030),
    );
    if (date == null) return null;
    final time = await showTimePicker(
      context: context,
      initialTime: TimeOfDay.now(),
    );
    if (time == null) return null;
    return DateTime(date.year, date.month, date.day, time.hour, time.minute);
  }

  void _sharePoll() {
    Haptics.vibrate(HapticsType.light);
    FirebaseAnalytics.instance.logEvent(
      name: 'share_poll',
      parameters: {'poll_id': _poll.id},
    );
    Share.share(
      'شارك في استطلاع الرأي: ${_poll.question}\nالخيارات: ${_poll.options.join(', ')}\nينتهي في: ${_poll.endTime.toString().substring(0, 16)}',
      subject: 'استطلاع رأي جديد',
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    _timer.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return Scaffold(
      body: CustomScrollView(
        slivers: [
          SliverAppBar(
            expandedHeight: 250.0,
            floating: false,
            pinned: true,
            flexibleSpace: FlexibleSpaceBar(
              background: Stack(
                fit: StackFit.expand,
                children: [
                  CachedNetworkImage(
                    imageUrl: 'https://via.placeholder.com/300',
                    fit: BoxFit.cover,
                    color: isDarkMode
                        ? Colors.black.withOpacity(0.3)
                        : Colors.white.withOpacity(0.3),
                    colorBlendMode: BlendMode.darken,
                    placeholder: (context, url) => Container(
                      color: Colors.grey,
                      child: Center(
                          child: CircularProgressIndicator(color: Colors.teal)),
                    ),
                    errorWidget: (context, url, error) => Container(
                      color: Colors.grey,
                      child: Icon(Icons.broken_image, color: Colors.white),
                    ),
                  ),
                  Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          Colors.teal[900]!.withOpacity(0.5),
                          Colors.transparent,
                        ],
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          SliverToBoxAdapter(
            child: FutureBuilder(
              future: _fetchVotes(),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return Center(
                      child: CircularProgressIndicator(color: Colors.teal));
                } else if (snapshot.hasError) {
                  return Center(
                    child: Column(
                      children: [
                        Icon(Icons.error_outline, color: Colors.red, size: 48),
                        SizedBox(height: 16),
                        Text(
                          'حدث خطأ أثناء تحميل الأصوات: ${snapshot.error}',
                          style: TextStyle(color: isDarkMode ? Colors.white : Colors.black),
                          textAlign: TextAlign.center,
                        ),
                        TextButton(
                          onPressed: () => setState(() {}),
                          child: Text('إعادة المحاولة',
                              style: TextStyle(color: Colors.teal)),
                        ),
                      ],
                    ),
                  );
                } else {
                  return FadeTransition(
                    opacity: _fadeAnimation,
                    child: Padding(
                      padding: const EdgeInsets.all(20.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            _poll.question, // يمكن استبداله بـ AppLocalizations.of(context)!.pollQuestion
                            style: TextStyle(
                              fontSize: 28,
                              fontWeight: FontWeight.bold,
                              color:
                              isDarkMode ? Colors.teal[200] : Colors.teal[900],
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'يرجى اختيار خيار واحد من الخيارات التالية:', // يمكن استبداله بـ AppLocalizations
                            style: TextStyle(
                              fontSize: 16,
                              color:
                              isDarkMode ? Colors.teal[300] : Colors.teal[600],
                            ),
                          ),
                          const SizedBox(height: 20),
                          Column(
                            children: List.generate(_poll.options.length, (index) {
                              double votePercentage = totalVotes > 0
                                  ? (_poll.votes[index] / totalVotes) * 100
                                  : 0;
                              return AnimatedScale(
                                scale: selectedOption == index ? 1.05 : 1.0,
                                duration: const Duration(milliseconds: 300),
                                child: AnimatedContainer(
                                  duration: const Duration(milliseconds: 300),
                                  margin: const EdgeInsets.symmetric(vertical: 10),
                                  child: Card(
                                    elevation: 6,
                                    shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(12)),
                                    color: selectedOption == index
                                        ? isDarkMode
                                        ? Colors.teal[900]
                                        : Colors.teal[100]
                                        : isDarkMode
                                        ? Colors.grey[800]
                                        : Colors.white,
                                    child: Padding(
                                      padding: const EdgeInsets.all(10),
                                      child: Column(
                                        children: [
                                          Semantics(
                                            label:
                                            'خيار الاستطلاع: ${_poll.options[index]}، النسبة: ${votePercentage.toStringAsFixed(1)}%',
                                            child: RadioListTile<int>(
                                              title: Text(
                                                _poll.options[index],
                                                style: TextStyle(
                                                    fontSize: 18,
                                                    fontWeight: FontWeight.bold,
                                                    color: isDarkMode
                                                        ? Colors.white
                                                        : Colors.black),
                                              ),
                                              value: index,
                                              groupValue: selectedOption,
                                              activeColor: Colors.teal[700],
                                              onChanged: selectedOption == -1 &&
                                                  _poll.endTime
                                                      .isAfter(DateTime.now())
                                                  ? (value) =>
                                                  _sendVoteToServer(index)
                                                  : null,
                                            ),
                                          ),
                                          if (selectedOption != -1)
                                            Padding(
                                              padding: const EdgeInsets.symmetric(
                                                  horizontal: 16),
                                              child: AnimatedContainer(
                                                duration: const Duration(
                                                    milliseconds: 500),
                                                height: 8,
                                                decoration: BoxDecoration(
                                                  gradient: LinearGradient(
                                                    colors: [
                                                      Colors.teal[700]!,
                                                      Colors.teal[300]!,
                                                    ],
                                                  ),
                                                  borderRadius:
                                                  BorderRadius.circular(4),
                                                ),
                                                child: LinearProgressIndicator(
                                                  value: votePercentage / 100,
                                                  backgroundColor: Colors.grey[200]!
                                                      .withOpacity(0.3),
                                                  valueColor:
                                                  const AlwaysStoppedAnimation(
                                                      Colors.transparent),
                                                  minHeight: 8,
                                                ),
                                              ),
                                            ),
                                          if (selectedOption != -1)
                                            Padding(
                                              padding:
                                              const EdgeInsets.only(top: 8),
                                              child: Text(
                                                '${votePercentage.toStringAsFixed(1)}% (${_poll.votes[index]} صوت)',
                                                style: TextStyle(
                                                    fontSize: 14,
                                                    color: isDarkMode
                                                        ? Colors.grey[400]
                                                        : Colors.grey[600]),
                                              ),
                                            ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              );
                            }),
                          ),
                          const SizedBox(height: 24),
                          Container(
                            padding: const EdgeInsets.symmetric(
                                vertical: 12, horizontal: 16),
                            decoration: BoxDecoration(
                              color: isDarkMode
                                  ? Colors.red[900]!.withOpacity(0.2)
                                  : Colors.red[50],
                              borderRadius: BorderRadius.circular(10),
                              border: Border.all(
                                  color: (isDarkMode
                                      ? Colors.grey[700]
                                      : Colors.grey[200]) ?? Colors.grey), // Added ?? Colors.grey as fallback
                            ),
                            child: CircularPercentIndicator(
                              radius: 60.0,
                              lineWidth: 10.0,
                              percent: _calculateTimeProgress(),
                              center: Text(
                                _remainingTime,
                                style: TextStyle(
                                  color:
                                  isDarkMode ? Colors.red[400] : Colors.red[800],
                                  fontWeight: FontWeight.bold,
                                  fontSize: 12,
                                ),
                                textAlign: TextAlign.center,
                              ),
                              progressColor: Colors.red[700],
                              backgroundColor:
                              Colors.grey[300]!.withOpacity(0.3),
                              circularStrokeCap: CircularStrokeCap.round,
                            ),
                          ),
                          const SizedBox(height: 24),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                            children: [
                              _buildActionButton(
                                icon: Icons.share,
                                label: "مشاركة",
                                color:
                                isDarkMode ? Colors.teal[300]! : Colors.teal[700]!,
                                onPressed: _sharePoll,
                              ),
                              _buildActionButton(
                                icon: Icons.comment,
                                label: "تعليق",
                                color:
                                isDarkMode ? Colors.teal[300]! : Colors.teal[700]!,
                                onPressed: _showCommentsSection,
                              ),
                              _buildActionButton(
                                icon: Icons.how_to_vote,
                                label: "الأصوات: $totalVotes",
                                color:
                                isDarkMode ? Colors.teal[300]! : Colors.teal[700]!,
                                onPressed: null,
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  );
                }
              },
            ),
          ),
        ],
      ),
      floatingActionButton: Consumer<AppState>(
        builder: (context, appState, _) => appState.userRole == 'admin'
            ? FloatingActionButton.extended(
          heroTag: 'poll_create_fab',
          onPressed: () {
            Haptics.vibrate(HapticsType.light);
            _createNewPoll();
          },
          label: const Text("نشر استطلاع",
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
          icon: const Icon(Icons.add_circle),
          backgroundColor: isDarkMode ? Colors.teal[900] : Colors.teal[700],
          foregroundColor: Colors.white,
          elevation: 6,
          shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12)),
          tooltip: "إضافة استطلاع جديد",
        )
            : Container(),
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback? onPressed,
  }) {
    return Column(
      children: [
        Semantics(
          button: true,
          label: label,
          enabled: onPressed != null,
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: color.withOpacity(0.1),
              boxShadow: [
                BoxShadow(
                  color: color.withOpacity(0.3),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: IconButton(
              icon: FaIcon(icon, color: color),
              onPressed: () {
                if (onPressed != null) {
                  Haptics.vibrate(HapticsType.light);
                  onPressed();
                }
              },
              tooltip: label,
              padding: const EdgeInsets.all(12),
            ),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(fontSize: 12, color: color),
        ),
      ],
    );
  }

  void _showSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Theme.of(context).brightness == Brightness.dark
              ? Colors.teal[900]
              : Colors.teal[700],
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        ),
      );
    }
  }
}
