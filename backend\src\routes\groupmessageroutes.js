const express = require('express');
const router = express.Router();
const { getGroupMessages, sendGroupMessage, markGroupMessagesAsRead } = require('../controllers/groupmessagecontroller');
const { protect } = require('../middleware/authmiddleware');

// جلب رسائل مجموعة
router.get('/:groupId', protect, getGroupMessages);
// إرسال رسالة إلى مجموعة
router.post('/:groupId', protect, sendGroupMessage);
// وضع علامة مقروء لجميع رسائل المجموعة
router.put('/:groupId/read', protect, markGroupMessagesAsRead);

module.exports = router;
