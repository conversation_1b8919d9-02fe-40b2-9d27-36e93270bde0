import 'package:flutter/material.dart';

class RewardsBadge extends StatelessWidget {
  final String badgeAsset;
  final String levelName;
  final int points;
  const RewardsBadge({required this.badgeAsset, required this.levelName, required this.points, super.key});
  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        CircleAvatar(
          radius: 36,
          backgroundColor: Colors.teal[50],
          backgroundImage: AssetImage(badgeAsset),
        ),
        const SizedBox(height: 8),
        Text(levelName, style: const TextStyle(fontWeight: FontWeight.bold)),
        Text('$points نقطة', style: const TextStyle(color: Colors.teal)),
      ],
    );
  }
}
