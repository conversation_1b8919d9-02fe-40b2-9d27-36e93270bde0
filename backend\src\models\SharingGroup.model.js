const mongoose = require('mongoose');

const SharingGroupSchema = new mongoose.Schema({
  name: { type: String, required: true },
  description: { type: String, required: true },
  location: { type: String, required: true },
  creatorId: { type: String, required: true },
  members: [{ type: String }],
  createdAt: { type: Date, default: Date.now },
  items: [{ type: mongoose.Schema.Types.ObjectId, ref: 'ShareBorrowItem' }]
});

module.exports = mongoose.model('SharingGroup', SharingGroupSchema);
