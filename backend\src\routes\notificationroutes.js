const express = require('express');
const router = express.Router();
const { createNotification, getNotifications, markAsRead } = require('../controllers/notificationcontroller');
const { protect } = require('../middleware/authmiddleware');

// إنشاء إشعار جديد
router.post('/', protect, createNotification);
// جلب جميع الإشعارات للمستخدم الحالي
router.get('/', protect, getNotifications);
// وضع علامة مقروء لإشعار
router.put('/:id/read', protect, markAsRead);

module.exports = router;
