import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:http/http.dart' as http;
import 'package:intl/intl.dart';
import 'package:animate_do/animate_do.dart';
import 'appstate.dart';
import 'appstate_provider.dart';

// ===============================
// نماذج البيانات - Data Models
// ===============================

/// نموذج التقييم
class Rating {
  final String id;
  final String adId;
  final String donorId;
  final String donorName;
  final String? donorAvatarUrl;
  final double value;
  final String? comment;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final bool isVerified;
  final int helpfulCount;

  Rating({
    required this.id,
    required this.adId,
    required this.donorId,
    required this.donorName,
    this.donorAvatarUrl,
    required this.value,
    this.comment,
    required this.createdAt,
    this.updatedAt,
    this.isVerified = false,
    this.helpfulCount = 0,
  });

  factory Rating.fromJson(Map<String, dynamic> json) {
    return Rating(
      id: json['_id'] ?? '',
      adId: json['adId'] ?? '',
      donorId: json['donorId'] ?? '',
      donorName: json['donorName'] ?? 'متبرع مجهول',
      donorAvatarUrl: json['donorAvatarUrl'],
      value: (json['value'] as num?)?.toDouble() ?? 0.0,
      comment: json['comment'],
      createdAt: DateTime.tryParse(json['createdAt'] ?? '') ?? DateTime.now(),
      updatedAt: json['updatedAt'] != null
          ? DateTime.tryParse(json['updatedAt'])
          : null,
      isVerified: json['isVerified'] ?? false,
      helpfulCount: json['helpfulCount'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'adId': adId,
      'donorId': donorId,
      'donorName': donorName,
      'donorAvatarUrl': donorAvatarUrl,
      'value': value,
      'comment': comment,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'isVerified': isVerified,
      'helpfulCount': helpfulCount,
    };
  }

  /// نسخة محدثة من التقييم
  Rating copyWith({
    String? id,
    String? adId,
    String? donorId,
    String? donorName,
    String? donorAvatarUrl,
    double? value,
    String? comment,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isVerified,
    int? helpfulCount,
  }) {
    return Rating(
      id: id ?? this.id,
      adId: adId ?? this.adId,
      donorId: donorId ?? this.donorId,
      donorName: donorName ?? this.donorName,
      donorAvatarUrl: donorAvatarUrl ?? this.donorAvatarUrl,
      value: value ?? this.value,
      comment: comment ?? this.comment,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isVerified: isVerified ?? this.isVerified,
      helpfulCount: helpfulCount ?? this.helpfulCount,
    );
  }
}

/// إحصائيات التقييم
class RatingStats {
  final double averageRating;
  final int totalRatings;
  final Map<int, int> ratingDistribution; // توزيع التقييمات (1-5 نجوم)
  final int totalComments;

  RatingStats({
    required this.averageRating,
    required this.totalRatings,
    required this.ratingDistribution,
    required this.totalComments,
  });

  factory RatingStats.fromJson(Map<String, dynamic> json) {
    return RatingStats(
      averageRating: (json['averageRating'] as num?)?.toDouble() ?? 0.0,
      totalRatings: json['totalRatings'] ?? 0,
      ratingDistribution: Map<int, int>.from(json['ratingDistribution'] ?? {}),
      totalComments: json['totalComments'] ?? 0,
    );
  }

  /// الحصول على نسبة تقييم معين
  double getRatingPercentage(int rating) {
    if (totalRatings == 0) return 0.0;
    return (ratingDistribution[rating] ?? 0) / totalRatings;
  }
}

// ===============================
// خدمة التقييمات - Rating Service
// ===============================

class RatingService {
  final String baseUrl;
  final String authToken;

  RatingService({required this.baseUrl, required this.authToken});

  /// جلب تقييمات إعلان معين
  Future<List<Rating>> fetchRatings(
    String adId, {
    int page = 1,
    int limit = 10,
    String sortBy = 'newest',
  }) async {
    try {
      final response = await http.get(
        Uri.parse(
            '$baseUrl/ratings/$adId?page=$page&limit=$limit&sortBy=$sortBy'),
        headers: {
          'Authorization': 'Bearer $authToken',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return (data['ratings'] as List)
            .map((item) => Rating.fromJson(item))
            .toList();
      } else {
        throw Exception('فشل في جلب التقييمات: ${response.body}');
      }
    } catch (e) {
      throw Exception('خطأ في الشبكة: $e');
    }
  }

  /// جلب إحصائيات التقييم لإعلان معين
  Future<RatingStats> fetchRatingStats(String adId) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/ratings/$adId/stats'),
        headers: {
          'Authorization': 'Bearer $authToken',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        return RatingStats.fromJson(jsonDecode(response.body));
      } else {
        throw Exception('فشل في جلب إحصائيات التقييم: ${response.body}');
      }
    } catch (e) {
      throw Exception('خطأ في الشبكة: $e');
    }
  }

  /// إضافة تقييم جديد
  Future<Rating> addRating({
    required String adId,
    required String donorId,
    required double value,
    String? comment,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/ratings'),
        headers: {
          'Authorization': 'Bearer $authToken',
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'adId': adId,
          'donorId': donorId,
          'value': value,
          'comment': comment,
        }),
      );

      if (response.statusCode == 201) {
        return Rating.fromJson(jsonDecode(response.body));
      } else {
        throw Exception('فشل في إضافة التقييم: ${response.body}');
      }
    } catch (e) {
      throw Exception('خطأ في الشبكة: $e');
    }
  }

  /// تحديث تقييم موجود
  Future<Rating> updateRating({
    required String ratingId,
    required double value,
    String? comment,
  }) async {
    try {
      final response = await http.put(
        Uri.parse('$baseUrl/ratings/$ratingId'),
        headers: {
          'Authorization': 'Bearer $authToken',
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'value': value,
          'comment': comment,
        }),
      );

      if (response.statusCode == 200) {
        return Rating.fromJson(jsonDecode(response.body));
      } else {
        throw Exception('فشل في تحديث التقييم: ${response.body}');
      }
    } catch (e) {
      throw Exception('خطأ في الشبكة: $e');
    }
  }

  /// حذف تقييم
  Future<void> deleteRating(String ratingId) async {
    try {
      final response = await http.delete(
        Uri.parse('$baseUrl/ratings/$ratingId'),
        headers: {
          'Authorization': 'Bearer $authToken',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode != 200) {
        throw Exception('فشل في حذف التقييم: ${response.body}');
      }
    } catch (e) {
      throw Exception('خطأ في الشبكة: $e');
    }
  }

  /// تسجيل أن التقييم مفيد
  Future<void> markRatingHelpful(String ratingId) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/ratings/$ratingId/helpful'),
        headers: {
          'Authorization': 'Bearer $authToken',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode != 200) {
        throw Exception('فشل في تسجيل التقييم كمفيد: ${response.body}');
      }
    } catch (e) {
      throw Exception('خطأ في الشبكة: $e');
    }
  }
}

// ===============================
// مكونات واجهة المستخدم - UI Components
// ===============================

/// مكون عرض النجوم للتقييم
class StarRating extends StatelessWidget {
  final double rating;
  final int starCount;
  final double size;
  final Color color;
  final Color unratedColor;
  final bool allowHalfRating;
  final Function(double)? onRatingChanged;

  const StarRating({
    Key? key,
    required this.rating,
    this.starCount = 5,
    this.size = 24.0,
    this.color = Colors.amber,
    this.unratedColor = Colors.grey,
    this.allowHalfRating = true,
    this.onRatingChanged,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(starCount, (index) {
        return GestureDetector(
          onTap: onRatingChanged != null
              ? () => onRatingChanged!(index + 1.0)
              : null,
          child: Icon(
            _getStarIcon(index),
            size: size,
            color: _getStarColor(index),
          ),
        );
      }),
    );
  }

  IconData _getStarIcon(int index) {
    double difference = rating - index;
    if (difference >= 1.0) {
      return Icons.star;
    } else if (difference >= 0.5 && allowHalfRating) {
      return Icons.star_half;
    } else {
      return Icons.star_border;
    }
  }

  Color _getStarColor(int index) {
    double difference = rating - index;
    if (difference >= 0.5) {
      return color;
    } else {
      return unratedColor;
    }
  }
}

/// مكون عرض التقييم مع التفاصيل
class RatingDisplay extends StatelessWidget {
  final double rating;
  final int totalRatings;
  final bool showText;
  final double starSize;

  const RatingDisplay({
    Key? key,
    required this.rating,
    required this.totalRatings,
    this.showText = true,
    this.starSize = 16.0,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        StarRating(
          rating: rating,
          size: starSize,
          onRatingChanged: null,
        ),
        if (showText) ...[
          const SizedBox(width: 8),
          Text(
            '${rating.toStringAsFixed(1)} ($totalRatings)',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                ),
          ),
        ],
      ],
    );
  }
}

/// مكون إضافة تقييم جديد
class AddRatingWidget extends StatefulWidget {
  final String adId;
  final String donorId;
  final Function(Rating) onRatingAdded;

  const AddRatingWidget({
    Key? key,
    required this.adId,
    required this.donorId,
    required this.onRatingAdded,
  }) : super(key: key);

  @override
  State<AddRatingWidget> createState() => _AddRatingWidgetState();
}

class _AddRatingWidgetState extends State<AddRatingWidget> {
  double _rating = 0.0;
  final TextEditingController _commentController = TextEditingController();
  bool _isSubmitting = false;
  late final RatingService _ratingService;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final appState = AppStateProvider.of(context);
      _ratingService = RatingService(
        baseUrl: '${AppState.getBackendUrl()}/api',
        authToken: AppState.signerKey,
      );
    });
  }

  @override
  void dispose() {
    _commentController.dispose();
    super.dispose();
  }

  Future<void> _submitRating() async {
    if (_rating == 0.0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى اختيار تقييم')),
      );
      return;
    }

    setState(() => _isSubmitting = true);

    try {
      final rating = await _ratingService.addRating(
        adId: widget.adId,
        donorId: widget.donorId,
        value: _rating,
        comment: _commentController.text.trim().isEmpty
            ? null
            : _commentController.text.trim(),
      );

      widget.onRatingAdded(rating);

      // Reset form
      setState(() {
        _rating = 0.0;
        _commentController.clear();
      });

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('تم إضافة التقييم بنجاح')),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('خطأ في إضافة التقييم: $e')),
      );
    } finally {
      setState(() => _isSubmitting = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إضافة تقييم',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 16),

            // Star rating selector
            Row(
              children: [
                const Text('التقييم: '),
                const SizedBox(width: 8),
                StarRating(
                  rating: _rating,
                  size: 32,
                  onRatingChanged: (rating) {
                    setState(() => _rating = rating);
                    HapticFeedback.selectionClick();
                  },
                ),
                const SizedBox(width: 8),
                Text(
                  _getRatingText(_rating),
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: _getRatingColor(_rating),
                        fontWeight: FontWeight.w500,
                      ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Comment field
            TextField(
              controller: _commentController,
              maxLines: 3,
              decoration: const InputDecoration(
                labelText: 'تعليق (اختياري)',
                hintText: 'شاركنا رأيك حول هذا الإعلان...',
                border: OutlineInputBorder(),
              ),
            ),

            const SizedBox(height: 16),

            // Submit button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _isSubmitting ? null : _submitRating,
                child: _isSubmitting
                    ? const SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Text('إضافة التقييم'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _getRatingText(double rating) {
    if (rating == 0) return '';
    if (rating <= 1) return 'ضعيف';
    if (rating <= 2) return 'مقبول';
    if (rating <= 3) return 'جيد';
    if (rating <= 4) return 'جيد جداً';
    return 'ممتاز';
  }

  Color _getRatingColor(double rating) {
    if (rating == 0) return Colors.grey;
    if (rating <= 2) return Colors.red;
    if (rating <= 3) return Colors.orange;
    if (rating <= 4) return Colors.blue;
    return Colors.green;
  }
}

/// مكون عرض قائمة التقييمات
class RatingsList extends StatefulWidget {
  final String adId;

  const RatingsList({
    Key? key,
    required this.adId,
  }) : super(key: key);

  @override
  State<RatingsList> createState() => _RatingsListState();
}

class _RatingsListState extends State<RatingsList> {
  late final RatingService _ratingService;

  List<Rating> _ratings = [];
  RatingStats? _stats;
  bool _isLoading = true;
  String _sortBy = 'newest';

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final appState = AppStateProvider.of(context);
      _ratingService = RatingService(
        baseUrl: '${AppState.getBackendUrl()}/api',
        authToken: AppState.signerKey,
      );
      _loadRatings();
      _loadStats();
    });
  }

  Future<void> _loadRatings() async {
    try {
      final ratings = await _ratingService.fetchRatings(
        widget.adId,
        sortBy: _sortBy,
      );
      setState(() {
        _ratings = ratings;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('خطأ في جلب التقييمات: $e')),
      );
    }
  }

  Future<void> _loadStats() async {
    try {
      final stats = await _ratingService.fetchRatingStats(widget.adId);
      setState(() => _stats = stats);
    } catch (e) {
      // Handle error silently for stats
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Rating statistics
        if (_stats != null) _buildRatingStats(),

        const SizedBox(height: 16),

        // Sort options
        _buildSortOptions(),

        const SizedBox(height: 16),

        // Ratings list
        if (_ratings.isEmpty)
          _buildEmptyState()
        else
          ..._ratings.map((rating) => _buildRatingItem(rating)).toList(),
      ],
    );
  }

  Widget _buildRatingStats() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _stats!.averageRating.toStringAsFixed(1),
                      style:
                          Theme.of(context).textTheme.headlineMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                    ),
                    StarRating(
                      rating: _stats!.averageRating,
                      size: 20,
                    ),
                    Text(
                      '${_stats!.totalRatings} تقييم',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                  ],
                ),
                const SizedBox(width: 24),
                Expanded(
                  child: Column(
                    children: List.generate(5, (index) {
                      final starCount = 5 - index;
                      final percentage = _stats!.getRatingPercentage(starCount);
                      return Padding(
                        padding: const EdgeInsets.symmetric(vertical: 2),
                        child: Row(
                          children: [
                            Text('$starCount'),
                            const SizedBox(width: 4),
                            const Icon(Icons.star,
                                size: 16, color: Colors.amber),
                            const SizedBox(width: 8),
                            Expanded(
                              child: LinearProgressIndicator(
                                value: percentage,
                                backgroundColor: Colors.grey[300],
                                valueColor: const AlwaysStoppedAnimation<Color>(
                                    Colors.amber),
                              ),
                            ),
                            const SizedBox(width: 8),
                            Text(
                              '${(percentage * 100).toInt()}%',
                              style: Theme.of(context).textTheme.bodySmall,
                            ),
                          ],
                        ),
                      );
                    }),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSortOptions() {
    return Row(
      children: [
        const Text('ترتيب حسب: '),
        const SizedBox(width: 8),
        DropdownButton<String>(
          value: _sortBy,
          items: const [
            DropdownMenuItem(value: 'newest', child: Text('الأحدث')),
            DropdownMenuItem(value: 'oldest', child: Text('الأقدم')),
            DropdownMenuItem(value: 'highest', child: Text('التقييم الأعلى')),
            DropdownMenuItem(value: 'lowest', child: Text('التقييم الأقل')),
            DropdownMenuItem(value: 'helpful', child: Text('الأكثر فائدة')),
          ],
          onChanged: (value) {
            if (value != null) {
              setState(() => _sortBy = value);
              _loadRatings();
            }
          },
        ),
      ],
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          children: [
            Icon(
              Icons.star_border,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد تقييمات بعد',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: Colors.grey[600],
                  ),
            ),
            const SizedBox(height: 8),
            Text(
              'كن أول من يقيم هذا الإعلان',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[500],
                  ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRatingItem(Rating rating) {
    return FadeInUp(
      duration: const Duration(milliseconds: 300),
      child: Card(
        margin: const EdgeInsets.only(bottom: 8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  CircleAvatar(
                    radius: 20,
                    backgroundImage: rating.donorAvatarUrl != null
                        ? NetworkImage(rating.donorAvatarUrl!)
                        : null,
                    child: rating.donorAvatarUrl == null
                        ? Text(rating.donorName[0].toUpperCase())
                        : null,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Text(
                              rating.donorName,
                              style: Theme.of(context)
                                  .textTheme
                                  .titleSmall
                                  ?.copyWith(
                                    fontWeight: FontWeight.w500,
                                  ),
                            ),
                            if (rating.isVerified) ...[
                              const SizedBox(width: 4),
                              const Icon(
                                Icons.verified,
                                size: 16,
                                color: Colors.blue,
                              ),
                            ],
                          ],
                        ),
                        Row(
                          children: [
                            StarRating(
                              rating: rating.value,
                              size: 16,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              DateFormat('dd/MM/yyyy').format(rating.createdAt),
                              style: Theme.of(context)
                                  .textTheme
                                  .bodySmall
                                  ?.copyWith(
                                    color: Colors.grey[600],
                                  ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              if (rating.comment != null) ...[
                const SizedBox(height: 12),
                Text(
                  rating.comment!,
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
              ],
              const SizedBox(height: 8),
              Row(
                children: [
                  TextButton.icon(
                    onPressed: () => _markHelpful(rating),
                    icon: const Icon(Icons.thumb_up, size: 16),
                    label: Text('مفيد (${rating.helpfulCount})'),
                    style: TextButton.styleFrom(
                      foregroundColor: Colors.grey[600],
                    ),
                  ),
                  const Spacer(),
                  if (rating.updatedAt != null)
                    Text(
                      'تم التحديث',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Colors.grey[500],
                            fontStyle: FontStyle.italic,
                          ),
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _markHelpful(Rating rating) async {
    try {
      await _ratingService.markRatingHelpful(rating.id);

      // Update local state
      setState(() {
        final index = _ratings.indexWhere((r) => r.id == rating.id);
        if (index != -1) {
          _ratings[index] = rating.copyWith(
            helpfulCount: rating.helpfulCount + 1,
          );
        }
      });

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('شكراً لك على تقييم هذا التعليق')),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('خطأ: $e')),
      );
    }
  }
}

/// شاشة التقييمات الكاملة
class RatingsScreen extends StatefulWidget {
  final String adId;
  final String adTitle;
  final String currentUserId;

  const RatingsScreen({
    Key? key,
    required this.adId,
    required this.adTitle,
    required this.currentUserId,
  }) : super(key: key);

  @override
  State<RatingsScreen> createState() => _RatingsScreenState();
}

class _RatingsScreenState extends State<RatingsScreen> {
  bool _showAddRating = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('تقييمات ${widget.adTitle}'),
        actions: [
          IconButton(
            onPressed: () {
              setState(() => _showAddRating = !_showAddRating);
            },
            icon: Icon(_showAddRating ? Icons.close : Icons.add),
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            if (_showAddRating)
              AddRatingWidget(
                adId: widget.adId,
                donorId: widget.currentUserId,
                onRatingAdded: (rating) {
                  setState(() => _showAddRating = false);
                  // Refresh ratings list
                },
              ),
            Padding(
              padding: const EdgeInsets.all(16),
              child: RatingsList(adId: widget.adId),
            ),
          ],
        ),
      ),
    );
  }
}

/// مكون عرض التقييم المختصر
class RatingSummary extends StatefulWidget {
  final String adId;
  final double? initialRating;
  final int? initialCount;

  const RatingSummary({
    Key? key,
    required this.adId,
    this.initialRating,
    this.initialCount,
  }) : super(key: key);

  @override
  State<RatingSummary> createState() => _RatingSummaryState();
}

class _RatingSummaryState extends State<RatingSummary> {
  double? _rating;
  int? _count;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _rating = widget.initialRating;
    _count = widget.initialCount;

    // إذا لم يتم تمرير البيانات الأولية، قم بتحميلها
    if (_rating == null || _count == null) {
      _loadRatingStats();
    } else {
      _isLoading = false;
    }
  }

  Future<void> _loadRatingStats() async {
    try {
      final appState = AppStateProvider.of(context);
      final ratingService = RatingService(
        baseUrl: '${AppState.getBackendUrl()}/api',
        authToken: AppState.signerKey,
      );

      final stats = await ratingService.fetchRatingStats(widget.adId);
      setState(() {
        _rating = stats.averageRating;
        _count = stats.totalRatings;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _rating = 0.0;
        _count = 0;
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const SizedBox(
        width: 16,
        height: 16,
        child: CircularProgressIndicator(strokeWidth: 2),
      );
    }

    if (_rating == null || _count == null || _count == 0) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(Icons.star_border, size: 16, color: Colors.grey[400]),
          const SizedBox(width: 4),
          Text(
            'لا توجد تقييمات',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey[500],
                ),
          ),
        ],
      );
    }

    return GestureDetector(
      onTap: () => _showRatingsScreen(),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(Icons.star, size: 16, color: Colors.amber),
          const SizedBox(width: 4),
          Text(
            '${_rating!.toStringAsFixed(1)} ($_count)',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.amber[700],
                  fontWeight: FontWeight.w500,
                ),
          ),
        ],
      ),
    );
  }

  void _showRatingsScreen() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => RatingsScreen(
          adId: widget.adId,
          adTitle: 'إعلان التبرع',
          currentUserId: 'current_user_id', // يجب تمرير ID المستخدم الحالي
        ),
      ),
    );
  }
}
