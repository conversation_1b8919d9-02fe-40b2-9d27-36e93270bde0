import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:share_plus/share_plus.dart';
import 'package:animated_text_kit/animated_text_kit.dart';
import 'package:confetti/confetti.dart';
import 'package:intl/intl.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:geolocator/geolocator.dart';
import 'package:image_picker/image_picker.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:provider/provider.dart';
import 'appstate.dart';

class AgriculturalPlantGroup {
  final String id;
  final String name;
  final String imageUrl;
  final List<Video> videos;
  final List<Article> articles;
  final List<FAQ> faqs;
  final String nutritionGuide;
  final List<String> galleryImages;

  AgriculturalPlantGroup({
    required this.id,
    required this.name,
    required this.imageUrl,
    required this.videos,
    required this.articles,
    required this.faqs,
    required this.nutritionGuide,
    required this.galleryImages,
  });
}

class Video {
  final String title;
  final String url;

  Video({required this.title, required this.url});
}

class Article {
  final String title;
  final String content;

  Article({required this.title, required this.content});
}

class FAQ {
  final String question;
  final String answer;

  FAQ({required this.question, required this.answer});
}

class Post {
  final String id;
  final String user;
  final String content;
  final String? imageUrl;
  int likes;
  List<String> comments;

  Post({
    required this.id,
    required this.user,
    required this.content,
    this.imageUrl,
    this.likes = 0,
    this.comments = const [],
  });
}

class ChatMessage {
  final String userId;
  final String userName;
  final String text;

  ChatMessage({required this.userId, required this.userName, required this.text});
}

class NotificationItem {
  final String title;
  final String description;
  final DateTime date;

  NotificationItem({
    required this.title,
    required this.description,
    required this.date,
  });
}

class MarketItem {
  final String id;
  final String user;
  final String type;
  final List<String> imageUrls;
  final double quantity;
  final double price;
  final String address;
  final DateTime date;
  final String validity;
  final String contactNumber;
  final String details;
  final String? applicantStatus;
  final String? registrationType;
  final String? description;
  final String? economicActivity;
  final String? economicActivityDetails;
  final String? waterSource;
  final String? soilType;
  final String? electricitySource;
  final String? structures;
  final String? structuresDetails;

  MarketItem({
    required this.id,
    required this.user,
    required this.type,
    required this.imageUrls,
    required this.quantity,
    required this.price,
    required this.address,
    required this.date,
    required this.validity,
    required this.contactNumber,
    required this.details,
    this.applicantStatus,
    this.registrationType,
    this.description,
    this.economicActivity,
    this.economicActivityDetails,
    this.waterSource,
    this.soilType,
    this.electricitySource,
    this.structures,
    this.structuresDetails,
  });
}

class AgriculturalPlantsPage extends StatefulWidget {
  const AgriculturalPlantsPage({super.key});

  @override
  State<AgriculturalPlantsPage> createState() => _AgriculturalPlantsPageState();
}

class _AgriculturalPlantsPageState extends State<AgriculturalPlantsPage> with SingleTickerProviderStateMixin {
  late ConfettiController _confettiController;
  final List<AgriculturalPlantGroup> plants = [
    AgriculturalPlantGroup(
      id: '1',
      name: 'القمح',
      imageUrl: 'https://cdn-icons-png.flaticon.com/512/2927/2927027.png',
      videos: [
        Video(title: 'زراعة القمح', url: 'https://www.youtube.com/watch?v=wheat1'),
        Video(title: 'أمراض القمح الشائعة', url: 'https://www.youtube.com/watch?v=wheat2'),
      ],
      articles: [
        Article(title: 'دليل زراعة القمح', content: 'كل ما تحتاجه لزراعة القمح...'),
      ],
      faqs: [
        FAQ(question: 'ما هي أفضل الترب لزراعة القمح؟', answer: 'التربة الطينية الخصبة...'),
      ],
      nutritionGuide: 'معلومات التغذية للقمح: الأسمدة العضوية، الري المنتظم...',
      galleryImages: ['https://cdn-icons-png.flaticon.com/512/2927/2927027.png'],
    ),
    AgriculturalPlantGroup(
      id: '2',
      name: 'الأرز',
      imageUrl: 'https://cdn-icons-png.flaticon.com/512/2927/2927030.png',
      videos: [
        Video(title: 'زراعة الأرز', url: 'https://www.youtube.com/watch?v=rice1'),
      ],
      articles: [
        Article(title: 'صحة الأرز', content: 'نصائح للحفاظ على صحة الأرز...'),
      ],
      faqs: [
        FAQ(question: 'كيف أعرف إذا كان الأرز مصابًا؟', answer: 'راقب لون الأوراق ونموها...'),
      ],
      nutritionGuide: 'معلومات التغذية للأرز: الأسمدة الكيميائية، الري بالغمر...',
      galleryImages: ['https://cdn-icons-png.flaticon.com/512/2927/2927030.png'],
    ),
  ];

  final List<Post> posts = [
    Post(
      id: '1',
      user: 'علي',
      content: 'زرعت القمح بنجاح!',
      imageUrl: 'https://cdn-icons-png.flaticon.com/512/2927/2927027.png',
    ),
    Post(
      id: '2',
      user: 'سارة',
      content: 'نصائح لزراعة الأرز.',
    ),
  ];

  final List<NotificationItem> notifications = [
    NotificationItem(
      title: 'تذكير بالري',
      description: 'موعد ري القمح غداً',
      date: DateTime.now().add(const Duration(days: 1)),
    ),
  ];

  final List<MarketItem> cropItems = [
    MarketItem(
      id: '1',
      user: 'محمد',
      type: 'قمح',
      imageUrls: ['https://cdn-icons-png.flaticon.com/512/2927/2927027.png'],
      quantity: 100.0,
      price: 500.0,
      address: 'الرياض',
      date: DateTime.now().subtract(const Duration(days: 1)),
      validity: 'شهر',
      contactNumber: '1234567890',
      details: 'قمح عالي الجودة',
    ),
  ];

  final List<MarketItem> landItems = [
    MarketItem(
      id: '2',
      user: 'خالد',
      type: 'للبيع',
      imageUrls: ['https://cdn-icons-png.flaticon.com/512/3097/3097973.png'],
      quantity: 1000.0,
      price: 100000.0,
      address: 'الدمام',
      date: DateTime.now().subtract(const Duration(days: 3)),
      validity: 'شهر',
      contactNumber: '0987654321',
      details: 'أرض زراعية خصبة',
      applicantStatus: 'مالك',
      registrationType: 'شهر عقاري',
      description: 'أرض ممتازة للزراعة',
      economicActivity: 'نعم',
      economicActivityDetails: 'إنتاج زراعي',
      waterSource: 'آبار',
      soilType: 'طينية',
      electricitySource: 'طاقة شمسية',
      structures: 'نعم',
      structuresDetails: 'مستودع صغير',
    ),
  ];

  final List<MarketItem> supplyItems = [
    MarketItem(
      id: '3',
      user: 'يوسف',
      type: 'أسمدة',
      imageUrls: ['https://cdn-icons-png.flaticon.com/512/3082/3082872.png'],
      quantity: 200.0,
      price: 2000.0,
      address: 'مكة',
      date: DateTime.now().subtract(const Duration(days: 1)),
      validity: 'شهر',
      contactNumber: '1122334455',
      details: 'أسمدة عضوية عالية الجودة',
    ),
  ];

  final List<String> cropTypes = [
    'قمح',
    'أرز',
    'ذرة',
    'شعير',
    'فول الصويا',
    'قطن',
    'شوفان',
    'بنجر السكر',
    'بطاطس',
    'طماطم',
  ];

  final List<String> soilTypes = [
    'طينية',
    'رملية',
    'طميية',
    'جيرية',
    'عضوية',
  ];

  final TextEditingController _postController = TextEditingController();
  final TextEditingController _searchController = TextEditingController();
  final TextEditingController _healthCheckController = TextEditingController();

  late AnimationController _fabAnimationController;
  late Animation<double> _fabScaleAnimation;

  int userPoints = 100;

  @override
  void initState() {
    super.initState();
    _confettiController = ConfettiController(duration: const Duration(seconds: 1));
    _fabAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    _fabScaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fabAnimationController,
      curve: Curves.easeOut,
    ));
    _fabAnimationController.forward();
  }

  @override
  void dispose() {
    _confettiController.dispose();
    _fabAnimationController.dispose();
    _postController.dispose();
    _searchController.dispose();
    _healthCheckController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Scaffold(
          appBar: AppBar(
            title: AnimatedTextKit(
              animatedTexts: [
                TypewriterAnimatedText(
                  'موسوعة زراعة النباتات',
                  textStyle: const TextStyle(fontSize: 22, fontWeight: FontWeight.bold, color: Colors.white),
                  speed: const Duration(milliseconds: 100),
                ),
              ],
              totalRepeatCount: 1,
            ),
            backgroundColor: Colors.green.shade700,
            actions: [
              IconButton(
                icon: const Icon(Icons.search, color: Colors.white),
                onPressed: () => _showSearchDialog(context),
                tooltip: 'بحث',
              ),
            ],
            flexibleSpace: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [Colors.green.shade700, Colors.green.shade300],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
              ),
            ),
            elevation: 4,
          ),
          body: RefreshIndicator(
            onRefresh: () async {
              await Future.delayed(const Duration(seconds: 1));
              setState(() {});
              _confettiController.play();
              Fluttertoast.showToast(msg: 'تم تحديث الصفحة', backgroundColor: Colors.green.shade700);
            },
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: AnimationLimiter(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: AnimationConfiguration.toStaggeredList(
                    duration: const Duration(milliseconds: 500),
                    childAnimationBuilder: (widget) => SlideAnimation(
                      verticalOffset: 50.0,
                      child: FadeInAnimation(child: widget),
                    ),
                    children: [
                      _buildSectionTitle('أنواع النباتات'),
                      const SizedBox(height: 16),
                      _buildPlantCards(),
                      const SizedBox(height: 24),
                      _buildSectionTitle('السوق الزراعي'),
                      const SizedBox(height: 12),
                      _buildMarketplaceSection(),
                      const SizedBox(height: 24),
                      _buildSectionTitle('منشورات المجتمع'),
                      const SizedBox(height: 12),
                      _buildPostInput(),
                      const SizedBox(height: 12),
                      _buildPostsList(),
                    ],
                  ),
                ),
              ),
            ),
          ),
          floatingActionButton: ScaleTransition(
            scale: _fabScaleAnimation,
            child: FloatingActionButton(
              onPressed: () => _showQuickActions(context),
              tooltip: 'إجراءات سريعة',
              backgroundColor: Colors.green.shade700,
              elevation: 6,
              child: const Icon(Icons.add, color: Colors.white),
            ),
          ),
        ),
        Align(
          alignment: Alignment.topCenter,
          child: ConfettiWidget(
            confettiController: _confettiController,
            blastDirectionality: BlastDirectionality.explosive,
            particleDrag: 0.05,
            emissionFrequency: 0.05,
            numberOfParticles: 50,
            gravity: 0.05,
            colors: const [Colors.green, Colors.blue, Colors.yellow, Colors.red],
          ),
        ),
      ],
    );
  }

  Widget _buildSectionTitle(String title) {
    return AnimatedTextKit(
      animatedTexts: [
        FadeAnimatedText(
          title,
          textStyle: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: Colors.green.shade700,
          ),
        ),
      ],
      totalRepeatCount: 1,
    );
  }

  Widget _buildPlantCards() {
    return SizedBox(
      height: 220,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: plants.length,
        itemBuilder: (context, index) {
          final plant = plants[index];
          return AnimationConfiguration.staggeredList(
            position: index,
            duration: const Duration(milliseconds: 600),
            child: SlideAnimation(
              horizontalOffset: 50.0,
              child: FadeInAnimation(
                child: GestureDetector(
                  onTap: () => _showPlantDetails(context, plant),
                  child: Padding(
                    padding: const EdgeInsets.only(right: 16.0, left: 8.0),
                    child: Card(
                      elevation: 8,
                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
                      child: Container(
                        width: 160,
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [Colors.green.shade200, Colors.green.shade400],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ),
                          borderRadius: BorderRadius.circular(16),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.green.withOpacity(0.3),
                              blurRadius: 10,
                              offset: const Offset(0, 5),
                            ),
                          ],
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            ClipRRect(
                              borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
                              child: CachedNetworkImage(
                                imageUrl: plant.imageUrl,
                                height: 100,
                                width: double.infinity,
                                fit: BoxFit.cover,
                                placeholder: (context, url) => _buildShimmerPlaceholder(),
                                errorWidget: (context, url, error) => const Icon(Icons.error),
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: AnimatedTextKit(
                                animatedTexts: [
                                  TypewriterAnimatedText(
                                    plant.name,
                                    textStyle: const TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.white,
                                    ),
                                    speed: const Duration(milliseconds: 100),
                                  ),
                                ],
                                totalRepeatCount: 1,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildMarketplaceSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildMarketplaceCategory('سوق المحاصيل', cropItems, _showAddCropDialog, Icons.grass),
        const SizedBox(height: 24),
        _buildMarketplaceCategory('سوق الأراضي', landItems, _showAddLandDialog, Icons.landscape),
        const SizedBox(height: 24),
        _buildMarketplaceCategory('سوق المستلزمات الزراعية', supplyItems, _showAddSupplyDialog, Icons.agriculture),
      ],
    );
  }

  Widget _buildMarketplaceCategory(String title, List<MarketItem> items, Function(BuildContext) addDialog, IconData icon) {
    return Stack(
      children: [
        Card(
          elevation: 6,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.green.shade100, Colors.green.shade50],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(icon, color: Colors.green.shade700, size: 30),
                      const SizedBox(width: 8),
                      Text(
                        title,
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(color: Colors.green.shade700),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  if (title == 'سوق المستلزمات الزراعية') ...[
                    Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children: [
                        Chip(label: const Text('معالجة الملوحة'), backgroundColor: Colors.green.shade100),
                        Chip(label: const Text('شبكات الري'), backgroundColor: Colors.green.shade100),
                        Chip(label: const Text('تقاوي وشتلات وبذور'), backgroundColor: Colors.green.shade100),
                        Chip(label: const Text('تعبئة وتغليف'), backgroundColor: Colors.green.shade100),
                        Chip(label: const Text('آلات ومعدات زراعية'), backgroundColor: Colors.green.shade100),
                        Chip(label: const Text('أسمدة ومبيدات'), backgroundColor: Colors.green.shade100),
                        Chip(label: const Text('زراعات مائية'), backgroundColor: Colors.green.shade100),
                        Chip(label: const Text('طاقة شمسية'), backgroundColor: Colors.green.shade100),
                      ],
                    ),
                    const SizedBox(height: 12),
                  ],
                  SizedBox(
                    height: 300,
                    child: ListView.builder(
                      scrollDirection: Axis.horizontal,
                      itemCount: items.length,
                      itemBuilder: (context, index) {
                        final item = items[index];
                        return AnimationConfiguration.staggeredList(
                          position: index,
                          duration: const Duration(milliseconds: 600),
                          child: SlideAnimation(
                            horizontalOffset: 50.0,
                            child: FadeInAnimation(
                              child: _buildMarketCard(item),
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        Positioned(
          bottom: 16,
          right: 16,
          child: ScaleTransition(
            scale: _fabScaleAnimation,
            child: FloatingActionButton(
              onPressed: () => addDialog(context),
              backgroundColor: Colors.green.shade700,
              tooltip: 'إضافة إلى $title',
              elevation: 6,
              child: const Icon(Icons.add, color: Colors.white),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildMarketCard(MarketItem item) {
    return Padding(
      padding: const EdgeInsets.only(right: 12.0),
      child: MouseRegion(
        onEnter: (_) => setState(() {}),
        onExit: (_) => setState(() {}),
        child: Card(
          elevation: 6,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          child: Container(
            width: 200,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.green.withOpacity(0.2),
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ClipRRect(
                  borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
                  child: item.imageUrls.isNotEmpty
                      ? CachedNetworkImage(
                    imageUrl: item.imageUrls.first,
                    width: 200,
                    height: 120,
                    fit: BoxFit.cover,
                    placeholder: (context, url) => _buildShimmerPlaceholder(),
                    errorWidget: (context, url, error) => const Icon(Icons.error),
                  )
                      : Container(
                    width: 200,
                    height: 120,
                    color: Colors.grey.shade300,
                    child: Icon(Icons.image, color: Colors.grey.shade600),
                  ),
                ),
                Flexible(
                  child: SingleChildScrollView(
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(item.type, style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16)),
                          Text('الكمية: ${item.quantity} ${item.applicantStatus != null ? 'متر مربع' : 'كجم'}'),
                          Text('السعر: ${item.price} ريال'),
                          Text('العنوان: ${item.address}'),
                          Text('رقم التواصل: ${item.contactNumber}'),
                          Text('التفاصيل: ${item.details}'),
                          Text(
                            'التاريخ: ${DateFormat('dd/MM/yyyy').format(item.date)}',
                            style: const TextStyle(fontSize: 12, color: Colors.grey),
                          ),
                          if (item.applicantStatus != null) ...[
                            Text('الحالة: ${item.applicantStatus}'),
                            Text('التسجيل: ${item.registrationType}'),
                            Text('مصدر المياه: ${item.waterSource}'),
                            Text('نوع التربة: ${item.soilType}'),
                            Text('الكهرباء: ${item.electricitySource}'),
                            Text('المنشآت: ${item.structures}'),
                          ],
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildShimmerPlaceholder() {
    return Container(
      width: double.infinity,
      height: 120,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.grey.shade300, Colors.grey.shade100, Colors.grey.shade300],
          stops: const [0.1, 0.3, 0.4],
          begin: const Alignment(-1.0, -0.3),
          end: const Alignment(1.0, 0.3),
          tileMode: TileMode.repeated,
        ),
      ),
    );
  }

  void _showAddCropDialog(BuildContext context) {
    final quantityController = TextEditingController();
    final priceController = TextEditingController();
    final addressController = TextEditingController();
    final contactController = TextEditingController();
    final detailsController = TextEditingController();
    String selectedCropType = cropTypes.first;
    String selectedValidity = 'أسبوع';
    List<String> imageUrls = [];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('إضافة محصول', style: TextStyle(color: Colors.green.shade700)),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              DropdownButton<String>(
                value: selectedCropType,
                isExpanded: true,
                items: cropTypes.map((String crop) {
                  return DropdownMenuItem<String>(
                    value: crop,
                    child: Text(crop),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  setState(() {
                    selectedCropType = newValue!;
                  });
                },
                dropdownColor: Colors.green.shade50,
              ),
              const SizedBox(height: 8),
              TextField(
                controller: quantityController,
                decoration: InputDecoration(
                  hintText: 'الكمية (كجم)',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.green.shade50,
                ),
                keyboardType: TextInputType.number,
              ),
              const SizedBox(height: 8),
              TextField(
                controller: priceController,
                decoration: InputDecoration(
                  hintText: 'السعر (ريال)',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.green.shade50,
                ),
                keyboardType: TextInputType.number,
              ),
              const SizedBox(height: 8),
              TextField(
                controller: addressController,
                decoration: InputDecoration(
                  hintText: 'العنوان',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.green.shade50,
                  suffixIcon: IconButton(
                    icon: Icon(Icons.location_on, color: Colors.green.shade700),
                    onPressed: () async {
                      Position position = await Geolocator.getCurrentPosition();
                      addressController.text = 'موقع جغرافي: ${position.latitude}, ${position.longitude}';
                    },
                  ),
                ),
              ),
              const SizedBox(height: 8),
              DropdownButton<String>(
                value: selectedValidity,
                isExpanded: true,
                items: ['أسبوع', 'أسبوعان', 'شهر'].map((String validity) {
                  return DropdownMenuItem<String>(
                    value: validity,
                    child: Text(validity),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  setState(() {
                    selectedValidity = newValue!;
                  });
                },
                dropdownColor: Colors.green.shade50,
              ),
              const SizedBox(height: 8),
              TextField(
                controller: contactController,
                decoration: InputDecoration(
                  hintText: 'رقم التواصل',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.green.shade50,
                ),
                keyboardType: TextInputType.phone,
              ),
              const SizedBox(height: 8),
              TextField(
                controller: detailsController,
                decoration: InputDecoration(
                  hintText: 'تفاصيل المحصول',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.green.shade50,
                ),
                maxLines: 3,
              ),
              const SizedBox(height: 8),
              ElevatedButton(
                onPressed: () {
                  if (imageUrls.length < 4) {
                    imageUrls.addAll(List.generate(4 - imageUrls.length, (index) => 'https://cdn-icons-png.flaticon.com/512/2927/2927027.png'));
                  }
                  Fluttertoast.showToast(msg: 'تم رفع ${imageUrls.length} صور');
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green.shade700,
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                ),
                child: Text('رفع الصور (${imageUrls.length}/4)'),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              if (quantityController.text.isNotEmpty &&
                  priceController.text.isNotEmpty &&
                  addressController.text.isNotEmpty &&
                  contactController.text.isNotEmpty &&
                  detailsController.text.isNotEmpty &&
                  imageUrls.length >= 4) {
                setState(() {
                  cropItems.add(MarketItem(
                    id: DateTime.now().millisecondsSinceEpoch.toString(),
                    user: 'Current User',
                    type: selectedCropType,
                    imageUrls: imageUrls,
                    quantity: double.parse(quantityController.text),
                    price: double.parse(priceController.text),
                    address: addressController.text,
                    date: DateTime.now(),
                    validity: selectedValidity,
                    contactNumber: contactController.text,
                    details: detailsController.text,
                  ));
                });
                _confettiController.play();
                Fluttertoast.showToast(msg: 'تم إضافة المحصول بنجاح', backgroundColor: Colors.green.shade700);
                Navigator.pop(context);
              } else {
                Fluttertoast.showToast(msg: 'يرجى ملء جميع الحقول ورفع 4 صور', backgroundColor: Colors.red);
              }
            },
            child: const Text('إضافة'),
          ),
        ],
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        backgroundColor: Colors.green.shade50,
      ),
    );
  }

  void _showAddLandDialog(BuildContext context) {
    final quantityController = TextEditingController();
    final priceController = TextEditingController();
    final addressController = TextEditingController();
    final contactController = TextEditingController();
    final descriptionController = TextEditingController();
    final economicDetailsController = TextEditingController();
    final structuresDetailsController = TextEditingController();
    String selectedUnit = 'متر مربع';
    String selectedApplicantStatus = 'مالك';
    String selectedRegistrationType = 'شهر عقاري';
    String selectedEconomicActivity = 'نعم';
    String selectedWaterSource = 'آبار';
    String selectedSoilType = soilTypes.first;
    String selectedElectricitySource = 'خاص';
    String selectedStructures = 'نعم';
    String selectedSaleOrRent = 'للبيع';
    List<String> imageUrls = [];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('إضافة أرض', style: TextStyle(color: Colors.green.shade700)),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              DropdownButton<String>(
                value: selectedSaleOrRent,
                isExpanded: true,
                items: ['للبيع', 'للإيجار'].map((String value) {
                  return DropdownMenuItem<String>(
                    value: value,
                    child: Text(value),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  setState(() {
                    selectedSaleOrRent = newValue!;
                  });
                },
                dropdownColor: Colors.green.shade50,
              ),
              const SizedBox(height: 8),
              DropdownButton<String>(
                value: selectedUnit,
                isExpanded: true,
                items: ['فدان', 'قيراط', 'متر مربع'].map((String unit) {
                  return DropdownMenuItem<String>(
                    value: unit,
                    child: Text(unit),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  setState(() {
                    selectedUnit = newValue!;
                  });
                },
                dropdownColor: Colors.green.shade50,
              ),
              const SizedBox(height: 8),
              TextField(
                controller: quantityController,
                decoration: InputDecoration(
                  hintText: 'المساحة',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.green.shade50,
                ),
                keyboardType: TextInputType.number,
              ),
              const SizedBox(height: 8),
              TextField(
                controller: priceController,
                decoration: InputDecoration(
                  hintText: 'السعر (ريال)',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.green.shade50,
                ),
                keyboardType: TextInputType.number,
              ),
              const SizedBox(height: 8),
              TextField(
                controller: addressController,
                decoration: InputDecoration(
                  hintText: 'العنوان',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.green.shade50,
                  suffixIcon: IconButton(
                    icon: Icon(Icons.location_on, color: Colors.green.shade700),
                    onPressed: () async {
                      Position position = await Geolocator.getCurrentPosition();
                      addressController.text = 'موقع جغرافي: ${position.latitude}, ${position.longitude}';
                    },
                  ),
                ),
              ),
              const SizedBox(height: 8),
              TextField(
                controller: contactController,
                decoration: InputDecoration(
                  hintText: 'رقم التواصل',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.green.shade50,
                ),
                keyboardType: TextInputType.phone,
              ),
              const SizedBox(height: 8),
              DropdownButton<String>(
                value: selectedApplicantStatus,
                isExpanded: true,
                items: ['مالك', 'وكيل'].map((String status) {
                  return DropdownMenuItem<String>(
                    value: status,
                    child: Text(status),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  setState(() {
                    selectedApplicantStatus = newValue!;
                  });
                },
                dropdownColor: Colors.green.shade50,
              ),
              const SizedBox(height: 8),
              DropdownButton<String>(
                value: selectedRegistrationType,
                isExpanded: true,
                items: ['شهر عقاري', 'أملاك دولة', 'هيئة تعمير', 'جمعيات زراعية', 'شركات'].map((String type) {
                  return DropdownMenuItem<String>(
                    value: type,
                    child: Text(type),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  setState(() {
                    selectedRegistrationType = newValue!;
                  });
                },
                dropdownColor: Colors.green.shade50,
              ),
              const SizedBox(height: 8),
              TextField(
                controller: descriptionController,
                decoration: InputDecoration(
                  hintText: 'الوصف',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.green.shade50,
                ),
                maxLines: 3,
              ),
              const SizedBox(height: 8),
              DropdownButton<String>(
                value: selectedEconomicActivity,
                isExpanded: true,
                items: ['نعم', 'لا'].map((String value) {
                  return DropdownMenuItem<String>(
                    value: value,
                    child: Text(value),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  setState(() {
                    selectedEconomicActivity = newValue!;
                  });
                },
                dropdownColor: Colors.green.shade50,
              ),
              if (selectedEconomicActivity == 'نعم') ...[
                const SizedBox(height: 8),
                TextField(
                  controller: economicDetailsController,
                  decoration: InputDecoration(
                    hintText: 'تفاصيل الأنشطة الاقتصادية',
                    border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                    filled: true,
                    fillColor: Colors.green.shade50,
                  ),
                  maxLines: 2,
                ),
              ],
              const SizedBox(height: 8),
              DropdownButton<String>(
                value: selectedWaterSource,
                isExpanded: true,
                items: ['آبار', 'بحاري'].map((String source) {
                  return DropdownMenuItem<String>(
                    value: source,
                    child: Text(source),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  setState(() {
                    selectedWaterSource = newValue!;
                  });
                },
                dropdownColor: Colors.green.shade50,
              ),
              const SizedBox(height: 8),
              DropdownButton<String>(
                value: selectedSoilType,
                isExpanded: true,
                items: soilTypes.map((String soil) {
                  return DropdownMenuItem<String>(
                    value: soil,
                    child: Text(soil),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  setState(() {
                    selectedSoilType = newValue!;
                  });
                },
                dropdownColor: Colors.green.shade50,
              ),
              const SizedBox(height: 8),
              DropdownButton<String>(
                value: selectedElectricitySource,
                isExpanded: true,
                items: ['خاص', 'عام', 'طاقة شمسية', 'ديزل'].map((String source) {
                  return DropdownMenuItem<String>(
                    value: source,
                    child: Text(source),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  setState(() {
                    selectedElectricitySource = newValue!;
                  });
                },
                dropdownColor: Colors.green.shade50,
              ),
              const SizedBox(height: 8),
              DropdownButton<String>(
                value: selectedStructures,
                isExpanded: true,
                items: ['نعم', 'لا'].map((String value) {
                  return DropdownMenuItem<String>(
                    value: value,
                    child: Text(value),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  setState(() {
                    selectedStructures = newValue!;
                  });
                },
                dropdownColor: Colors.green.shade50,
              ),
              if (selectedStructures == 'نعم') ...[
                const SizedBox(height: 8),
                TextField(
                  controller: structuresDetailsController,
                  decoration: InputDecoration(
                    hintText: 'تفاصيل المنشآت',
                    border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                    filled: true,
                    fillColor: Colors.green.shade50,
                  ),
                  maxLines: 2,
                ),
              ],
              const SizedBox(height: 8),
              ElevatedButton(
                onPressed: () {
                  if (imageUrls.length < 4) {
                    imageUrls.addAll(List.generate(4 - imageUrls.length, (index) => 'https://cdn-icons-png.flaticon.com/512/3097/3097973.png'));
                  }
                  Fluttertoast.showToast(msg: 'تم رفع ${imageUrls.length} صور');
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green.shade700,
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                ),
                child: Text('رفع الصور (${imageUrls.length}/4)'),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              if (quantityController.text.isNotEmpty &&
                  priceController.text.isNotEmpty &&
                  addressController.text.isNotEmpty &&
                  contactController.text.isNotEmpty &&
                  descriptionController.text.isNotEmpty &&
                  imageUrls.length >= 4) {
                setState(() {
                  landItems.add(MarketItem(
                    id: DateTime.now().millisecondsSinceEpoch.toString(),
                    user: 'Current User',
                    type: selectedSaleOrRent,
                    imageUrls: imageUrls,
                    quantity: double.parse(quantityController.text),
                    price: double.parse(priceController.text),
                    address: addressController.text,
                    date: DateTime.now(),
                    validity: 'شهر',
                    contactNumber: contactController.text,
                    details: descriptionController.text,
                    applicantStatus: selectedApplicantStatus,
                    registrationType: selectedRegistrationType,
                    description: descriptionController.text,
                    economicActivity: selectedEconomicActivity,
                    economicActivityDetails: economicDetailsController.text,
                    waterSource: selectedWaterSource,
                    soilType: selectedSoilType,
                    electricitySource: selectedElectricitySource,
                    structures: selectedStructures,
                    structuresDetails: structuresDetailsController.text,
                  ));
                });
                _confettiController.play();
                Fluttertoast.showToast(msg: 'تم إضافة الأرض بنجاح', backgroundColor: Colors.green.shade700);
                Navigator.pop(context);
              } else {
                Fluttertoast.showToast(msg: 'يرجى ملء جميع الحقول ورفع 4 صور', backgroundColor: Colors.red);
              }
            },
            child: const Text('إضافة'),
          ),
        ],
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        backgroundColor: Colors.green.shade50,
      ),
    );
  }

  void _showAddSupplyDialog(BuildContext context) {
    final quantityController = TextEditingController();
    final priceController = TextEditingController();
    final addressController = TextEditingController();
    final contactController = TextEditingController();
    final detailsController = TextEditingController();
    String selectedSupplyType = 'أسمدة ومبيدات';
    String selectedValidity = 'أسبوع';
    List<String> imageUrls = [];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('إضافة مستلزمات زراعية', style: TextStyle(color: Colors.green.shade700)),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              DropdownButton<String>(
                value: selectedSupplyType,
                isExpanded: true,
                items: [
                  'معالجة الملوحة',
                  'شبكات الري',
                  'تقاوي وشتلات وبذور',
                  'تعبئة وتغليف',
                  'آلات ومعدات زراعية',
                  'أسمدة ومبيدات',
                  'زراعات مائية',
                  'طاقة شمسية',
                ].map((String type) {
                  return DropdownMenuItem<String>(
                    value: type,
                    child: Text(type),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  setState(() {
                    selectedSupplyType = newValue!;
                  });
                },
                dropdownColor: Colors.green.shade50,
              ),
              const SizedBox(height: 8),
              TextField(
                controller: quantityController,
                decoration: InputDecoration(
                  hintText: 'الكمية',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.green.shade50,
                ),
                keyboardType: TextInputType.number,
              ),
              const SizedBox(height: 8),
              TextField(
                controller: priceController,
                decoration: InputDecoration(
                  hintText: 'السعر (ريال)',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.green.shade50,
                ),
                keyboardType: TextInputType.number,
              ),
              const SizedBox(height: 8),
              TextField(
                controller: addressController,
                decoration: InputDecoration(
                  hintText: 'العنوان',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.green.shade50,
                  suffixIcon: IconButton(
                    icon: Icon(Icons.location_on, color: Colors.green.shade700),
                    onPressed: () async {
                      Position position = await Geolocator.getCurrentPosition();
                      addressController.text = 'موقع جغرافي: ${position.latitude}, ${position.longitude}';
                    },
                  ),
                ),
              ),
              const SizedBox(height: 8),
              DropdownButton<String>(
                value: selectedValidity,
                isExpanded: true,
                items: ['أسبوع', 'أسبوعان', 'شهر'].map((String validity) {
                  return DropdownMenuItem<String>(
                    value: validity,
                    child: Text(validity),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  setState(() {
                    selectedValidity = newValue!;
                  });
                },
                dropdownColor: Colors.green.shade50,
              ),
              const SizedBox(height: 8),
              TextField(
                controller: contactController,
                decoration: InputDecoration(
                  hintText: 'رقم التواصل',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.green.shade50,
                ),
                keyboardType: TextInputType.phone,
              ),
              const SizedBox(height: 8),
              TextField(
                controller: detailsController,
                decoration: InputDecoration(
                  hintText: 'تفاصيل المستلزمات',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.green.shade50,
                ),
                maxLines: 3,
              ),
              const SizedBox(height: 8),
              ElevatedButton(
                onPressed: () {
                  if (imageUrls.length < 4) {
                    imageUrls.addAll(List.generate(4 - imageUrls.length, (index) => 'https://cdn-icons-png.flaticon.com/512/3082/3082872.png'));
                  }
                  Fluttertoast.showToast(msg: 'تم رفع ${imageUrls.length} صور');
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green.shade700,
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                ),
                child: Text('رفع الصور (${imageUrls.length}/4)'),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              if (quantityController.text.isNotEmpty &&
                  priceController.text.isNotEmpty &&
                  addressController.text.isNotEmpty &&
                  contactController.text.isNotEmpty &&
                  detailsController.text.isNotEmpty &&
                  imageUrls.length >= 4) {
                setState(() {
                  supplyItems.add(MarketItem(
                    id: DateTime.now().millisecondsSinceEpoch.toString(),
                    user: 'Current User',
                    type: selectedSupplyType,
                    imageUrls: imageUrls,
                    quantity: double.parse(quantityController.text),
                    price: double.parse(priceController.text),
                    address: addressController.text,
                    date: DateTime.now(),
                    validity: selectedValidity,
                    contactNumber: contactController.text,
                    details: detailsController.text,
                  ));
                });
                _confettiController.play();
                Fluttertoast.showToast(msg: 'تم إضافة المستلزمات بنجاح', backgroundColor: Colors.green.shade700);
                Navigator.pop(context);
              } else {
                Fluttertoast.showToast(msg: 'يرجى ملء جميع الحقول ورفع 4 صور', backgroundColor: Colors.red);
              }
            },
            child: const Text('إضافة'),
          ),
        ],
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        backgroundColor: Colors.green.shade50,
      ),
    );
  }

  void _showAddNewPlantTypeDialog(BuildContext context) {
    final nameController = TextEditingController();
    final imageUrlController = TextEditingController();
    final videoTitleController = TextEditingController();
    final videoUrlController = TextEditingController();
    final articleTitleController = TextEditingController();
    final articleContentController = TextEditingController();
    final faqQuestionController = TextEditingController();
    final faqAnswerController = TextEditingController();
    final nutritionGuideController = TextEditingController();
    final galleryImageController = TextEditingController();
    List<String> galleryImages = [];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('إضافة نوع نبات جديد', style: TextStyle(color: Colors.green.shade700)),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: nameController,
                decoration: InputDecoration(
                  hintText: 'اسم النبات',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.green.shade50,
                ),
              ),
              const SizedBox(height: 8),
              TextField(
                controller: imageUrlController,
                decoration: InputDecoration(
                  hintText: 'رابط صورة النبات',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.green.shade50,
                ),
              ),
              const SizedBox(height: 8),
              TextField(
                controller: videoTitleController,
                decoration: InputDecoration(
                  hintText: 'عنوان الفيديو',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.green.shade50,
                ),
              ),
              const SizedBox(height: 8),
              TextField(
                controller: videoUrlController,
                decoration: InputDecoration(
                  hintText: 'رابط الفيديو',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.green.shade50,
                ),
              ),
              const SizedBox(height: 8),
              TextField(
                controller: articleTitleController,
                decoration: InputDecoration(
                  hintText: 'عنوان المقالة',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.green.shade50,
                ),
              ),
              const SizedBox(height: 8),
              TextField(
                controller: articleContentController,
                decoration: InputDecoration(
                  hintText: 'محتوى المقالة',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.green.shade50,
                ),
                maxLines: 3,
              ),
              const SizedBox(height: 8),
              TextField(
                controller: faqQuestionController,
                decoration: InputDecoration(
                  hintText: 'السؤال',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.green.shade50,
                ),
              ),
              const SizedBox(height: 8),
              TextField(
                controller: faqAnswerController,
                decoration: InputDecoration(
                  hintText: 'الإجابة',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.green.shade50,
                ),
                maxLines: 3,
              ),
              const SizedBox(height: 8),
              TextField(
                controller: nutritionGuideController,
                decoration: InputDecoration(
                  hintText: 'دليل التغذية والري',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.green.shade50,
                ),
                maxLines: 3,
              ),
              const SizedBox(height: 8),
              TextField(
                controller: galleryImageController,
                decoration: InputDecoration(
                  hintText: 'رابط صورة المعرض',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.green.shade50,
                ),
              ),
              const SizedBox(height: 8),
              ElevatedButton(
                onPressed: () {
                  if (galleryImageController.text.isNotEmpty) {
                    galleryImages.add(galleryImageController.text);
                    galleryImageController.clear();
                    Fluttertoast.showToast(msg: 'تم إضافة صورة إلى المعرض (${galleryImages.length})');
                  }
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green.shade700,
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                ),
                child: Text('إضافة صورة إلى المعرض (${galleryImages.length})'),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              if (nameController.text.isNotEmpty &&
                  imageUrlController.text.isNotEmpty &&
                  videoTitleController.text.isNotEmpty &&
                  videoUrlController.text.isNotEmpty &&
                  articleTitleController.text.isNotEmpty &&
                  articleContentController.text.isNotEmpty &&
                  faqQuestionController.text.isNotEmpty &&
                  faqAnswerController.text.isNotEmpty &&
                  nutritionGuideController.text.isNotEmpty &&
                  galleryImages.isNotEmpty) {
                setState(() {
                  plants.add(AgriculturalPlantGroup(
                    id: DateTime.now().millisecondsSinceEpoch.toString(),
                    name: nameController.text,
                    imageUrl: imageUrlController.text,
                    videos: [Video(title: videoTitleController.text, url: videoUrlController.text)],
                    articles: [Article(title: articleTitleController.text, content: articleContentController.text)],
                    faqs: [FAQ(question: faqQuestionController.text, answer: faqAnswerController.text)],
                    nutritionGuide: nutritionGuideController.text,
                    galleryImages: galleryImages,
                  ));
                });
                _confettiController.play();
                Fluttertoast.showToast(msg: 'تم إضافة نوع النبات بنجاح', backgroundColor: Colors.green.shade700);
                Navigator.pop(context);
              } else {
                Fluttertoast.showToast(msg: 'يرجى ملء جميع الحقول وإضافة صورة واحدة على الأقل', backgroundColor: Colors.red);
              }
            },
            child: const Text('إضافة'),
          ),
        ],
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        backgroundColor: Colors.green.shade50,
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
  }) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, size: 20),
      label: Text(label, style: const TextStyle(fontSize: 14)),
      style: ElevatedButton.styleFrom(
        foregroundColor: Colors.green.shade700, 
        backgroundColor: Colors.green.shade50,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),
    );
  }

  Widget _buildPostInput() {
    return Card(
      elevation: 8,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            TextField(
              controller: _postController,
              decoration: InputDecoration(
                hintText: 'ما الذي تفكر به؟',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: Theme.of(context).scaffoldBackgroundColor,
                hintStyle: const TextStyle(fontFamily: 'Tajawal'),
              ),
              maxLines: 3,
              onSubmitted: (value) {
                if (value.isNotEmpty) {
                  setState(() {
                    posts.add(Post(
                      id: DateTime.now().millisecondsSinceEpoch.toString(),
                      user: 'Current User',
                      content: value,
                    ));
                    _postController.clear();
                    userPoints += 10;
                    _confettiController.play();
                  });
                  Fluttertoast.showToast(
                    msg: 'تم نشر المنشور',
                    backgroundColor: Colors.green.shade700,
                  );
                }
              },
            ),
            const SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildActionButton(
                  icon: Icons.image,
                  label: 'صورة',
                  onPressed: () async {
                    final picker = ImagePicker();
                    final image = await picker.pickImage(source: ImageSource.gallery);
                    if (image != null) {
                      setState(() {
                        posts.add(Post(
                          id: DateTime.now().millisecondsSinceEpoch.toString(),
                          user: 'Current User',
                          content: 'منشور مع صورة',
                          imageUrl: image.path,
                        ));
                        userPoints += 15;
                        _confettiController.play();
                      });
                      Fluttertoast.showToast(
                        msg: 'تم نشر الصورة',
                        backgroundColor: Colors.green.shade700,
                      );
                    }
                  },
                ),
                _buildActionButton(
                  icon: Icons.videocam,
                  label: 'فيديو',
                  onPressed: () async {
                    final connectivityResult = await Connectivity().checkConnectivity();
                    if (connectivityResult == ConnectivityResult.none) {
                      Fluttertoast.showToast(
                        msg: 'لا يوجد اتصال بالإنترنت',
                        backgroundColor: Colors.red,
                      );
                      return;
                    }
                    final picker = ImagePicker();
                    final video = await picker.pickVideo(source: ImageSource.gallery);
                    if (video != null) {
                      // Handle video post
                      Fluttertoast.showToast(
                        msg: 'تم رفع الفيديو',
                        backgroundColor: Colors.green.shade700,
                      );
                    }
                  },
                ),
                _buildActionButton(
                  icon: Icons.camera_alt,
                  label: 'قصة',
                  onPressed: () async {
                    final picker = ImagePicker();
                    final image = await picker.pickImage(source: ImageSource.camera);
                    if (image != null) {
                      // Handle story
                      Fluttertoast.showToast(
                        msg: 'تم إنشاء قصة جديدة',
                        backgroundColor: Colors.green.shade700,
                      );
                    }
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPostsList() {
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: posts.length,
      itemBuilder: (context, index) {
        final post = posts[index];
        final commentController = TextEditingController();
        return AnimationConfiguration.staggeredList(
          position: index,
          duration: const Duration(milliseconds: 600),
          child: SlideAnimation(
            verticalOffset: 50.0,
            child: FadeInAnimation(
              child: Card(
                elevation: 4,
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          CircleAvatar(
                            backgroundColor: Colors.green.shade700,
                            child: Text(post.user[0], style: const TextStyle(color: Colors.white)),
                          ),
                          const SizedBox(width: 8),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(post.user, style: const TextStyle(fontWeight: FontWeight.bold)),
                              Text(
                                DateFormat('dd/MM/yyyy HH:mm').format(DateTime.now()),
                                style: const TextStyle(fontSize: 12, color: Colors.grey),
                              ),
                            ],
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(post.content),
                      if (post.imageUrl != null) ...[
                        const SizedBox(height: 8),
                        CachedNetworkImage(
                          imageUrl: post.imageUrl!,
                          width: double.infinity,
                          height: 200,
                          fit: BoxFit.cover,
                          placeholder: (context, url) => _buildShimmerPlaceholder(),
                          errorWidget: (context, url, error) => const Icon(Icons.error),
                        ),
                      ],
                      const Divider(),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Row(
                            children: [
                              IconButton(
                                icon: Icon(
                                  Icons.thumb_up,
                                  color: post.likes > 0 ? Colors.blue : Colors.grey,
                                ),
                                onPressed: () {
                                  setState(() {
                                    post.likes++;
                                  });
                                  _confettiController.play();
                                  Fluttertoast.showToast(msg: 'تم الإعجاب بالمنشور');
                                },
                              ),
                              Text('${post.likes} إعجاب'),
                            ],
                          ),
                          Row(
                            children: [
                              IconButton(
                                icon: const Icon(Icons.comment, color: Colors.grey),
                                onPressed: () {
                                  showDialog(
                                    context: context,
                                    builder: (context) => AlertDialog(
                                      title: const Text('إضافة تعليق'),
                                      content: TextField(
                                        controller: commentController,
                                        decoration: InputDecoration(
                                          hintText: 'اكتب تعليقك...',
                                          border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                                          filled: true,
                                          fillColor: Colors.green.shade50,
                                        ),
                                      ),
                                      actions: [
                                        TextButton(
                                          onPressed: () => Navigator.pop(context),
                                          child: const Text('إلغاء'),
                                        ),
                                        TextButton(
                                          onPressed: () {
                                            if (commentController.text.isNotEmpty) {
                                              setState(() {
                                                post.comments.add(commentController.text);
                                              });
                                              commentController.clear();
                                              Navigator.pop(context);
                                              Fluttertoast.showToast(msg: 'تم إضافة التعليق');
                                            }
                                          },
                                          child: const Text('إضافة'),
                                        ),
                                      ],
                                    ),
                                  );
                                },
                              ),
                              Text('${post.comments.length} تعليق'),
                            ],
                          ),
                          IconButton(
                            icon: Icon(Icons.share, color: Colors.green.shade700),
                            onPressed: () {
                              Share.share('${post.user}: ${post.content} ${post.imageUrl ?? ''}');
                              _confettiController.play();
                            },
                          ),
                        ],
                      ),
                      if (post.comments.isNotEmpty) ...[
                        const Divider(),
                        Text('التعليقات', style: Theme.of(context).textTheme.titleMedium),
                        ListView.builder(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          itemCount: post.comments.length,
                          itemBuilder: (context, commentIndex) {
                            return ListTile(
                              leading: const CircleAvatar(
                                backgroundColor: Colors.grey,
                                child: Icon(Icons.person, color: Colors.white),
                              ),
                              title: Text('مستخدم ${commentIndex + 1}'),
                              subtitle: Text(post.comments[commentIndex]),
                            );
                          },
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  void _showPlantDetails(BuildContext context, AgriculturalPlantGroup plant) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.9,
        minChildSize: 0.5,
        maxChildSize: 0.95,
        builder: (context, scrollController) => Container(
          decoration: BoxDecoration(
            color: Theme.of(context).scaffoldBackgroundColor,
            borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
            boxShadow: [
              BoxShadow(
                color: Colors.green.withOpacity(0.3),
                blurRadius: 10,
                offset: const Offset(0, -5),
              ),
            ],
          ),
          child: DefaultTabController(
            length: 6,
            child: Column(
              children: [
                Container(
                  margin: const EdgeInsets.symmetric(vertical: 8),
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: Colors.grey,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                TabBar(
                  isScrollable: true,
                  tabs: const [
                    Tab(text: 'نظرة عامة'),
                    Tab(text: 'الفيديوهات'),
                    Tab(text: 'المقالات'),
                    Tab(text: 'الأسئلة الشائعة'),
                    Tab(text: 'دليل التغذية'),
                    Tab(text: 'معرض الصور'),
                  ],
                  labelColor: Colors.green.shade700,
                  unselectedLabelColor: Colors.grey,
                  indicatorColor: Colors.green.shade700,
                ),
                Expanded(
                  child: TabBarView(
                    children: [
                      _buildOverviewTab(plant),
                      _buildVideosTab(plant.videos),
                      _buildArticlesTab(plant.articles),
                      _buildFAQsTab(plant.faqs),
                      _buildNutritionGuideTab(plant.nutritionGuide),
                      _buildGalleryTab(plant.galleryImages),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildOverviewTab(AgriculturalPlantGroup plant) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(12),
            child: CachedNetworkImage(
              imageUrl: plant.imageUrl,
              height: 200,
              width: double.infinity,
              fit: BoxFit.cover,
              placeholder: (context, url) => _buildShimmerPlaceholder(),
              errorWidget: (context, url, error) => const Icon(Icons.error),
            ),
          ),
          const SizedBox(height: 16),
          Text(
            plant.name,
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: Colors.green.shade700,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'معلومات شاملة عن ${plant.name}',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: 8),
          Text('عدد الفيديوهات: ${plant.videos.length}'),
          Text('عدد المقالات: ${plant.articles.length}'),
          Text('عدد الأسئلة الشائعة: ${plant.faqs.length}'),
          Text('دليل التغذية: متوفر'),
          Text('صور المعرض: ${plant.galleryImages.length}'),
        ],
      ),
    );
  }

  Widget _buildVideosTab(List<Video> videos) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: videos.length,
      itemBuilder: (context, index) {
        final video = videos[index];
        return AnimationConfiguration.staggeredList(
          position: index,
          duration: const Duration(milliseconds: 600),
          child: SlideAnimation(
            verticalOffset: 50.0,
            child: FadeInAnimation(
              child: ListTile(
                leading: Icon(Icons.videocam, color: Colors.green.shade700),
                title: Text(video.title),
                onTap: () {
                  ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('Playing ${video.title}')));
                  _confettiController.play();
                },
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildArticlesTab(List<Article> articles) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: articles.length,
      itemBuilder: (context, index) {
        final article = articles[index];
        return AnimationConfiguration.staggeredList(
          position: index,
          duration: const Duration(milliseconds: 600),
          child: SlideAnimation(
            verticalOffset: 50.0,
            child: FadeInAnimation(
              child: ExpansionTile(
                leading: Icon(Icons.article, color: Colors.green.shade700),
                title: Text(article.title),
                tilePadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                collapsedBackgroundColor: Colors.green.shade50,
                backgroundColor: Colors.green.shade100,
                children: [
                  Padding(
                    padding: const EdgeInsets.all(16),
                    child: Text(article.content),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildFAQsTab(List<FAQ> faqs) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: faqs.length,
      itemBuilder: (context, index) {
        final faq = faqs[index];
        return AnimationConfiguration.staggeredList(
          position: index,
          duration: const Duration(milliseconds: 600),
          child: SlideAnimation(
            verticalOffset: 50.0,
            child: FadeInAnimation(
              child: ExpansionTile(
                leading: Icon(Icons.question_answer, color: Colors.green.shade700),
                title: Text(faq.question),
                tilePadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                collapsedBackgroundColor: Colors.green.shade50,
                backgroundColor: Colors.green.shade100,
                children: [
                  Padding(
                    padding: const EdgeInsets.all(16),
                    child: Text(faq.answer),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildNutritionGuideTab(String nutritionGuide) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'دليل التغذية والري',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 12),
          Text(nutritionGuide),
        ],
      ),
    );
  }

  Widget _buildGalleryTab(List<String> galleryImages) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: galleryImages.length,
      itemBuilder: (context, index) {
        final imageUrl = galleryImages[index];
        return AnimationConfiguration.staggeredList(
          position: index,
          duration: const Duration(milliseconds: 600),
          child: SlideAnimation(
            verticalOffset: 50.0,
            child: FadeInAnimation(
              child: Padding(
                padding: const EdgeInsets.only(bottom: 12.0),
                child: GestureDetector(
                  onTap: () {
                    Share.share('تحقق من هذه الصورة: $imageUrl');
                    _confettiController.play();
                  },
                  child: CachedNetworkImage(
                    imageUrl: imageUrl,
                    width: double.infinity,
                    height: 150,
                    fit: BoxFit.cover,
                    placeholder: (context, url) => _buildShimmerPlaceholder(),
                    errorWidget: (context, url, error) => const Icon(Icons.error),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  void _showQuickActions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      backgroundColor: Colors.green.shade50,
      builder: (context) => Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: Icon(Icons.add_circle, color: Colors.green.shade700),
              title: const Text('إضافة نوع نبات جديد'),
              onTap: () {
                Navigator.pop(context);
                _showAddNewPlantTypeDialog(context);
              },
            ),
            ListTile(
              leading: Icon(Icons.post_add, color: Colors.green.shade700),
              title: const Text('إضافة منشور'),
              onTap: () {
                Navigator.pop(context);
                _showAddPostDialog(context);
              },
            ),
            ListTile(
              leading: Icon(Icons.add_shopping_cart, color: Colors.green.shade700),
              title: const Text('إضافة إلى السوق'),
              onTap: () {
                Navigator.pop(context);
                showModalBottomSheet(
                  context: context,
                  shape: const RoundedRectangleBorder(
                    borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
                  ),
                  backgroundColor: Colors.green.shade50,
                  builder: (context) => Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        ListTile(
                          leading: Icon(Icons.grass, color: Colors.green.shade700),
                          title: const Text('إضافة محصول'),
                          onTap: () {
                            Navigator.pop(context);
                            _showAddCropDialog(context);
                          },
                        ),
                        ListTile(
                          leading: Icon(Icons.landscape, color: Colors.green.shade700),
                          title: const Text('إضافة أرض'),
                          onTap: () {
                            Navigator.pop(context);
                            _showAddLandDialog(context);
                          },
                        ),
                        ListTile(
                          leading: Icon(Icons.agriculture, color: Colors.green.shade700),
                          title: const Text('إضافة مستلزمات زراعية'),
                          onTap: () {
                            Navigator.pop(context);
                            _showAddSupplyDialog(context);
                          },
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showAddPostDialog(BuildContext context) {
    final postImageController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('إضافة منشور جديد', style: TextStyle(color: Colors.green.shade700)),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: _postController,
                decoration: InputDecoration(
                  hintText: 'اكتب منشورك...',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.green.shade50,
                ),
                maxLines: 3,
              ),
              const SizedBox(height: 8),
              TextField(
                controller: postImageController,
                decoration: InputDecoration(
                  hintText: 'رابط الصورة (اختياري)',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.green.shade50,
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              if (_postController.text.isNotEmpty) {
                setState(() {
                  posts.add(Post(
                    id: DateTime.now().millisecondsSinceEpoch.toString(),
                    user: 'Current User',
                    content: _postController.text,
                    imageUrl: postImageController.text.isNotEmpty ? postImageController.text : null,
                  ));
                  _postController.clear();
                  postImageController.clear();
                });
                _confettiController.play();
                Fluttertoast.showToast(msg: 'تم إضافة المنشور بنجاح', backgroundColor: Colors.green.shade700);
                Navigator.pop(context);
              } else {
                Fluttertoast.showToast(msg: 'يرجى كتابة محتوى المنشور', backgroundColor: Colors.red);
              }
            },
            child: const Text('نشر'),
          ),
        ],
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        backgroundColor: Colors.green.shade50,
      ),
    );
  }

  void _showSearchDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('بحث', style: TextStyle(color: Colors.green.shade700)),
        content: TextField(
          controller: _searchController,
          decoration: InputDecoration(
            hintText: 'ابحث عن نبات أو موضوع...',
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
            filled: true,
            fillColor: Colors.green.shade50,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              if (_searchController.text.isNotEmpty) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('نتائج البحث عن: ${_searchController.text}')),
                );
                _searchController.clear();
                Navigator.pop(context);
              }
    },
    child: const Text('بحث'),
    ),
    ],
    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
    backgroundColor: Colors.green.shade50,
    ),
    );
  }
}
