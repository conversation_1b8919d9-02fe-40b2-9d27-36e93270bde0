class Message {
  final String id;
  final String senderId;
  final String content;
  final DateTime timestamp;
  final List<String> readBy;

  Message({
    required this.id,
    required this.senderId,
    required this.content,
    required this.timestamp,
    required this.readBy,
  });

  factory Message.fromJson(Map<String, dynamic> json) {
    return Message(
      id: json['id'] as String,
      senderId: json['senderId'] as String,
      content: json['content'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
      readBy: List<String>.from(json['readBy'] ?? []), // تمت إضافة هذا السطر
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'senderId': senderId,
      'content': content,
      'timestamp': timestamp.toIso8601String(),
      'readBy': readBy, // تمت إضافة هذا السطر
    };
  }

  @override
  String toString() {
    return 'Message{id: $id, senderId: $senderId, content: $content, timestamp: $timestamp, readBy: $readBy}';
  }

  Message copyWith({
    String? id,
    String? senderId,
    String? content,
    DateTime? timestamp,
    List<String>? readBy,
  }) {
    return Message(
      id: id ?? this.id,
      senderId: senderId ?? this.senderId,
      content: content ?? this.content,
      timestamp: timestamp ?? this.timestamp,
      readBy: readBy ?? this.readBy, // تمت إضافة هذا السطر
    );
  }
}
