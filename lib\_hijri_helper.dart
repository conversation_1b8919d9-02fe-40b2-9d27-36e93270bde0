import 'dart:convert';
import 'package:http/http.dart' as http;

Future<String?> fetchHijriDate(DateTime date) async {
  final url = Uri.parse('https://api.islamicdevelopers.com/v1/calendar?calendar=gregorian&year=${date.year}&month=${date.month}&day=${date.day}');
  final response = await http.get(url);
  if (response.statusCode == 200) {
    final data = response.body;
    try {
      final decoded = jsonDecode(data);
      final hijri = decoded[0]['calendar']['hijri'];
      final year = hijri['numeric']['year'];
      final monthName = hijri['names']['month']['arabic']['native'];
      final day = hijri['numeric']['day'];
      return '$day $monthName $year هـ';
    } catch (e) {
      throw Exception('خطأ في قراءة التاريخ الهجري');
    }
  } else {
    throw Exception('فشل الاتصال بخادم التاريخ الهجري');
  }
}
