import 'package:flutter/material.dart';
import 'package:flame/game.dart';
import 'package:flame/components.dart';

class DominoGamePage extends StatefulWidget {
  const DominoGamePage({super.key});

  @override
  State<DominoGamePage> createState() => _DominoGamePageState();
}

class _DominoGamePageState extends State<DominoGamePage> {
  final DominoGame _game = DominoGame();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('لعبة الدومينو', 
          style: TextStyle(fontFamily: 'Tajawal', color: Colors.white)),
        backgroundColor: Theme.of(context).primaryColor,
        centerTitle: true,
      ),
      body: LayoutBuilder(
        builder: (context, constraints) {
          return GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTapDown: (details) {
              final box = context.findRenderObject() as RenderBox;
              final position = box.globalToLocal(details.globalPosition);
              _game.addTile(position);
            },
            child: GameWidget(
              game: _game,
            ),
          );
        },
      ),
    );
  }
}

class DominoGame extends FlameGame {
  @override
  Color backgroundColor() => const Color(0xFF2D2D2D);

  @override
  Future<void> onLoad() async {
    await super.onLoad();
    addTile(Offset(size.x / 2, size.y / 2));
  }

  void addTile(Offset position) {
    add(
      DominoTile(
        position: Vector2(position.dx, position.dy),
        value: '3|4',
      ),
    );
  }
}

class DominoTile extends PositionComponent {
  final String value;
  
  DominoTile({
    required super.position,
    required this.value,
  }) : super(size: Vector2(100, 50), anchor: Anchor.center);

  @override
  Future<void> onLoad() async {
    await super.onLoad();
  }

  @override
  void render(Canvas canvas) {
    // Draw domino background
    final backgroundPaint = Paint()..color = Colors.white;
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(0, 0, width, height),
        const Radius.circular(8),
      ),
      backgroundPaint,
    );

    // Draw border
    final borderPaint = Paint()
      ..color = Colors.black
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(0, 0, width, height),
        const Radius.circular(8),
      ),
      borderPaint,
    );

    // Draw divider line
    final linePaint = Paint()
      ..color = Colors.black
      ..strokeWidth = 2;
    canvas.drawLine(
      Offset(width / 2, 0),
      Offset(width / 2, height),
      linePaint,
    );

    // Draw text
    final textPainter = TextPainter(
      text: TextSpan(
        text: value,
        style: const TextStyle(
          color: Colors.black,
          fontSize: 20,
          fontWeight: FontWeight.bold,
          fontFamily: 'Tajawal',
        ),
      ),
      textDirection: TextDirection.ltr,
    );
    
    textPainter.layout();
    textPainter.paint(
      canvas,
      Offset((width - textPainter.width) / 2, (height - textPainter.height) / 2),
    );
  }
}
