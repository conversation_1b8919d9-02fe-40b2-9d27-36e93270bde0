const mongoose = require('mongoose');

// إنشاء بيانات اختبار للمستخدمين
const createTestUsers = async () => {
  const User = require('./src/models/Usermodel');

  // التحقق من وجود مستخدمين
  const userCount = await User.countDocuments();
  if (userCount > 0) {
    console.log('المستخدمون موجودون بالفعل، تخطي إنشاء بيانات اختبار للمستخدمين');
    return;
  }

  // إنشاء مستخدمين للاختبار
  const users = [
    {
      name: 'أحمد محمد',
      email: '<EMAIL>',
      password: 'password123',
      role: 'admin',
      userType: 'regular',
      avatarUrl: '/uploads/default-avatar.png',
      job: 'مطور برمجيات'
    },
    {
      name: 'سارة علي',
      email: '<EMAIL>',
      password: 'password123',
      role: 'member',
      userType: 'medical',
      avatarUrl: '/uploads/default-avatar.png',
      job: 'طبيبة'
    },
    {
      name: 'محمد خالد',
      email: '<EMAIL>',
      password: 'password123',
      role: 'member',
      userType: 'regular',
      avatarUrl: '/uploads/default-avatar.png',
      job: 'مهندس'
    }
  ];

  try {
    await User.create(users);
    console.log('تم إنشاء بيانات اختبار للمستخدمين بنجاح');
  } catch (error) {
    console.error('خطأ في إنشاء بيانات اختبار للمستخدمين:', error);
  }
};

// إنشاء بيانات اختبار للمنشورات
const createTestPosts = async () => {
  const Post = require('./src/models/Postmodel');
  const User = require('./src/models/Usermodel');

  // التحقق من وجود منشورات
  const postCount = await Post.countDocuments();
  if (postCount > 0) {
    console.log('المنشورات موجودة بالفعل، تخطي إنشاء بيانات اختبار للمنشورات');
    return;
  }

  // الحصول على المستخدمين
  const users = await User.find();
  if (users.length === 0) {
    console.log('لا يوجد مستخدمين، يرجى إنشاء مستخدمين أولاً');
    return;
  }

  // إنشاء منشورات للاختبار
  const posts = [
    {
      userId: users[0]._id,
      content: 'مرحباً بالجميع في تطبيقنا الجديد!',
      createdAt: new Date()
    },
    {
      userId: users[1]._id,
      content: 'نصائح طبية: تناول الفواكه والخضروات يومياً للحفاظ على صحتك',
      createdAt: new Date(Date.now() - 86400000) // بالأمس
    },
    {
      userId: users[2]._id,
      content: 'أفضل الممارسات الهندسية للمشاريع الناجحة',
      createdAt: new Date(Date.now() - 172800000) // قبل يومين
    }
  ];

  try {
    await Post.create(posts);
    console.log('تم إنشاء بيانات اختبار للمنشورات بنجاح');
  } catch (error) {
    console.error('خطأ في إنشاء بيانات اختبار للمنشورات:', error);
  }
};

// إنشاء بيانات اختبار للمجموعات
const createTestGroups = async () => {
  const Group = require('./src/models/Groupmodel');
  const User = require('./src/models/Usermodel');

  // التحقق من وجود مجموعات
  const groupCount = await Group.countDocuments();
  if (groupCount > 0) {
    console.log('المجموعات موجودة بالفعل، تخطي إنشاء بيانات اختبار للمجموعات');
    return;
  }

  // الحصول على المستخدمين
  const users = await User.find();
  if (users.length === 0) {
    console.log('لا يوجد مستخدمين، يرجى إنشاء مستخدمين أولاً');
    return;
  }

  // إنشاء مجموعات للاختبار
  const groups = [
    {
      name: 'مجموعة الطبخ',
      description: 'مجموعة لتبادل وصفات الطعام والنصائح',
      creatorId: users[0]._id,
      type: 'cooking',
      members: [users[0]._id, users[1]._id],
      createdAt: new Date()
    },
    {
      name: 'مجموعة التكنولوجيا',
      description: 'مناقشة أحدث التقنيات والابتكارات',
      creatorId: users[2]._id,
      type: 'technology',
      members: [users[2]._id, users[0]._id],
      createdAt: new Date(Date.now() - 86400000) // بالأمس
    }
  ];

  try {
    await Group.create(groups);
    console.log('تم إنشاء بيانات اختبار للمجموعات بنجاح');
  } catch (error) {
    console.error('خطأ في إنشاء بيانات اختبار للمجموعات:', error);
  }
};

// الدالة الرئيسية لإنشاء بيانات الاختبار
const seedDatabase = async () => {
  try {
    // الاتصال بقاعدة البيانات
    const dotenv = require('dotenv');
    dotenv.config();

    await mongoose.connect(process.env.MONGO_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });

    console.log('تم الاتصال بقاعدة البيانات');

    // إنشاء بيانات الاختبار
    await createTestUsers();
    await createTestPosts();
    await createTestGroups();

    console.log('تم إنشاء بيانات الاختبار بنجاح');
    process.exit(0);
  } catch (error) {
    console.error('خطأ في إنشاء بيانات الاختبار:', error);
    process.exit(1);
  }
};

// تنفيذ الدالة الرئيسية
seedDatabase();
