const Driver = require('../models/driver.model');

exports.loginDriver = async (req, res) => {
  try {
    const { name, vehicleInfo } = req.body;
    let driver = await Driver.findOne({ name, vehicleInfo });
    if (!driver) {
      driver = await Driver.create({ name, vehicleInfo });
    }
    res.status(200).json(driver);
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
};

exports.updateAvailability = async (req, res) => {
  try {
    const { isAvailable } = req.body;
    const driver = await Driver.findByIdAndUpdate(
      req.params.id,
      { isAvailable },
      { new: true }
    );
    res.json(driver);
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
};

exports.getAllDrivers = async (req, res) => {
  try {
    const drivers = await Driver.find();
    res.json(drivers);
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};
