// نموذج البث المباشر
const mongoose = require('mongoose');

const LivestreamSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  title: {
    type: String,
    required: true
  },
  description: {
    type: String
  },
  streamUrl: {
    type: String, // رابط البث (WebRTC/YouTube/...)
    required: true
  },
  isLive: {
    type: Boolean,
    default: true
  },
  startedAt: {
    type: Date,
    default: Date.now
  },
  endedAt: {
    type: Date
  }
});

module.exports = mongoose.model('Livestream', LivestreamSchema);
