import 'dart:async';
import 'dart:convert';
import 'dart:io';

// Flutter packages
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';

// State management
import 'package:provider/provider.dart';

// UI components
import 'package:cached_network_image/cached_network_image.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:shimmer/shimmer.dart';
import 'package:confetti/confetti.dart';
import 'package:animate_do/animate_do.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

// Network & storage
import 'package:http/http.dart' as http;
import 'package:dio/dio.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:retry/retry.dart';

// Location & maps
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:geolocator/geolocator.dart';

// File handling
import 'package:file_picker/file_picker.dart';

// Localization
import 'package:intl/intl.dart';
import 'package:easy_localization/easy_localization.dart';

// Utilities
import 'package:uuid/uuid.dart';
import 'package:socket_io_client/socket_io_client.dart' as io;
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter_offline/flutter_offline.dart';
import 'package:share_plus/share_plus.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:provider/provider.dart';

// App modules
import 'appstate.dart';
import 'donation_ads_screen.dart';
import 'donation_features_screen.dart';
import 'maps_system.dart';
import 'rating_system.dart';
import 'badges_system.dart';

// ===============================
// Constants and Configuration
// ===============================

/// Animation constants for consistent UI behavior
const Duration _animationDuration = Duration(milliseconds: 300);
const Duration _fastAnimationDuration = Duration(milliseconds: 150);
const Duration _slowAnimationDuration = Duration(milliseconds: 500);
const double _scaleFactor = 0.95;
const double _borderRadius = 12.0;
const double _cardElevation = 4.0;

/// Error message constants for better user experience
const String networkError = 'خطأ في الشبكة، يرجى التحقق من الاتصال';
const String loadingError = 'خطأ في تحميل البيانات';
const String authError = 'فشل في المصادقة، يرجى تسجيل الدخول مرة أخرى';
const String serverError = 'خطأ في الخادم، يرجى المحاولة لاحقاً';
const String offlineMessage = 'أنت غير متصل بالإنترنت';

/// Retry configuration for network requests
final RetryOptions _retryOptions = RetryOptions(
  maxAttempts: 3,
  delayFactor: Duration(seconds: 2),
);

/// Theme colors for consistent design
class AppColors {
  static const Color primary = Color(0xFF00695C);
  static const Color primaryLight = Color(0xFF4DB6AC);
  static const Color primaryDark = Color(0xFF004D40);
  static const Color accent = Color(0xFF26A69A);
  static const Color success = Color(0xFF4CAF50);
  static const Color warning = Color(0xFFFF9800);
  static const Color error = Color(0xFFF44336);
  static const Color background = Color(0xFFF5F5F5);
  static const Color surface = Colors.white;
  static const Color onSurface = Color(0xFF212121);
  static const Color onSurfaceVariant = Color(0xFF757575);
}

// ===============================
// Enhanced Error Handling
// ===============================

/// Enhanced error handler with better user feedback
Future<T> handleError<T>(Future<T> Function() future, {String? customMessage}) async {
  try {
    return await future();
  } catch (e) {
    String errorMessage = customMessage ?? 'حدث خطأ غير متوقع';

    if (e is SocketException) {
      errorMessage = 'خطأ في الاتصال بالشبكة';
    } else if (e is TimeoutException) {
      errorMessage = 'انتهت مهلة الانتظار';
    } else if (e is DioError) {
      switch (e.response?.statusCode) {
        case 401:
          errorMessage = 'غير مصرح';
          break;
        case 404:
          errorMessage = 'البيانات المطلوبة غير موجودة';
          break;
        case 500:
          errorMessage = 'خطأ في الخادم';
          break;
        default:
          errorMessage = 'خطأ في الخادم: ${e.response?.statusCode}';
      }
    }

    throw errorMessage;
  }
}

// ===============================
// Enhanced Data Models
// ===============================

/// Enhanced DonationAd model with additional features
class DonationAd {
  // إضافة نموذج الموقع مع الإحداثيات
  static const String defaultImageUrl = 'https://via.placeholder.com/400x300?text=No+Image';
  static const List<String> defaultCategories = [
    'تعليم',
    'صحة',
    'إغاثة',
    'أيتام',
    'أسر محتاجة',
    'أضاحي',
    'إفطار صائم',
    'كفالة يتيم',
    'مشاريع مياه',
    'أخرى'
  ];

  // إضافة نموذج الوسوم
  static const List<String> defaultTags = [
    'طارئ',
    'إغاثة عاجلة',
    'رمضان',
    'الأضحى',
    'شتاء',
    'تعليمي',
    'صحي',
    'إنساني',
    'تنموي',
    'إطعام'
  ];
  final String id;
  final String title;
  final String description;
  final String location;
  final double? latitude;
  final double? longitude;
  final double? rating; // تقييم الإعلان من 0 إلى 5
  final int? ratingCount; // عدد التقييمات
  final String amount;
  final String category;
  final List<String> imageUrls;
  final String? videoUrl;
  final bool isCompleted;
  final String creatorId;
  final String donationStatus;
  final double raisedAmount;
  final double targetAmount;
  final DateTime date;
  final DateTime requestDate;
  final DateTime? completedDate;
  final DateTime? endDate;
  final bool isFeatured;
  bool isFavorite;
  final List<ImpactMedia> impactMedia;
  final int donorCount;
  final double urgencyLevel;
  final List<String> tags;
  final Map<String, dynamic> metadata;

  // دالة مساعدة لإنشاء معرف فريد
  static String generateId() {
    return const Uuid().v4();
  }

  // دالة لإنشاء إعلان جديد مع القيم الافتراضية
  factory DonationAd.createNew({
    required String title,
    required String description,
    required String location,
    required double targetAmount,
    required String category,
    required String creatorId,
    List<String>? imageUrls,
    String? videoUrl,
    double? latitude,
    double? longitude,
    double? rating,
    int? ratingCount,
    DateTime? endDate,
    List<String>? tags,
  }) {
    final now = DateTime.now();
    return DonationAd(
      id: generateId(),
      title: title,
      description: description,
      location: location,
      latitude: latitude,
      longitude: longitude,
      rating: rating,
      ratingCount: ratingCount ?? 0,
      amount: targetAmount.toString(),
      category: category,
      imageUrls: imageUrls ?? [],
      videoUrl: videoUrl,
      isCompleted: false,
      creatorId: creatorId,
      donationStatus: 'active',
      raisedAmount: 0,
      targetAmount: targetAmount,
      date: now,
      requestDate: now,
      completedDate: null,
      endDate: endDate,
      isFeatured: false,
      isFavorite: false,
      impactMedia: [],
      donorCount: 0,
      urgencyLevel: 0.0,
      tags: tags ?? [],
      metadata: {},
    );
  }

  DonationAd({
    required this.id,
    required this.title,
    required this.description,
    required this.location,
    this.latitude,
    this.longitude,
    this.rating,
    this.ratingCount = 0,
    required this.amount,
    required this.category,
    this.imageUrls = const [],
    this.videoUrl,
    required this.isCompleted,
    required this.creatorId,
    required this.donationStatus,
    required this.raisedAmount,
    required this.targetAmount,
    required this.date,
    required this.requestDate,
    this.completedDate,
    this.endDate,
    required this.isFeatured,
    this.isFavorite = false,
    this.impactMedia = const [],
    this.donorCount = 0,
    this.urgencyLevel = 0.0,
    this.tags = const [],
    this.metadata = const {},
  });

  /// Calculate donation progress percentage
  double get progressPercentage {
    if (targetAmount <= 0) return 0.0;
    return (raisedAmount / targetAmount).clamp(0.0, 1.0);
  }

  /// Get remaining amount needed
  double get remainingAmount {
    return (targetAmount - raisedAmount).clamp(0.0, double.infinity);
  }

  /// Check if donation is urgent
  bool get isUrgent => urgencyLevel > 0.7;

  /// Check if donation is nearly complete
  bool get isNearlyComplete => progressPercentage > 0.8;

  // نسخة معدلة مع الحفاظ على القيم الحالية
  DonationAd copyWith({
    String? id,
    String? title,
    String? description,
    String? location,
    double? latitude,
    double? longitude,
    double? rating,
    int? ratingCount,
    String? amount,
    String? category,
    List<String>? imageUrls,
    String? videoUrl,
    bool? isCompleted,
    String? creatorId,
    String? donationStatus,
    double? raisedAmount,
    double? targetAmount,
    DateTime? date,
    DateTime? requestDate,
    DateTime? completedDate,
    DateTime? endDate,
    bool? isFeatured,
    bool? isFavorite,
    List<ImpactMedia>? impactMedia,
    int? donorCount,
    double? urgencyLevel,
    List<String>? tags,
    Map<String, dynamic>? metadata,
  }) {
    return DonationAd(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      location: location ?? this.location,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      rating: rating ?? this.rating,
      ratingCount: ratingCount ?? this.ratingCount,
      amount: amount ?? this.amount,
      category: category ?? this.category,
      imageUrls: imageUrls ?? this.imageUrls,
      videoUrl: videoUrl ?? this.videoUrl,
      isCompleted: isCompleted ?? this.isCompleted,
      creatorId: creatorId ?? this.creatorId,
      donationStatus: donationStatus ?? this.donationStatus,
      raisedAmount: raisedAmount ?? this.raisedAmount,
      targetAmount: targetAmount ?? this.targetAmount,
      date: date ?? this.date,
      requestDate: requestDate ?? this.requestDate,
      completedDate: completedDate ?? this.completedDate,
      endDate: endDate ?? this.endDate,
      isFeatured: isFeatured ?? this.isFeatured,
      isFavorite: isFavorite ?? this.isFavorite,
      impactMedia: impactMedia ?? this.impactMedia,
      donorCount: donorCount ?? this.donorCount,
      urgencyLevel: urgencyLevel ?? this.urgencyLevel,
      tags: tags ?? this.tags,
      metadata: metadata ?? this.metadata,
    );
  }

  // إضافة دالة للتحقق من صحة البيانات
  bool get isValid {
    return title.isNotEmpty &&
           description.length >= 30 &&
           location.isNotEmpty &&
           (latitude != null && longitude != null) &&
           targetAmount > 0 &&
           category.isNotEmpty &&
           (imageUrls.isNotEmpty || videoUrl != null);
  }

  // حساب الأيام المتبقية
  int? get daysRemaining {
    if (endDate == null) return null;
    final now = DateTime.now();
    return endDate!.difference(now).inDays;
  }

  // حساب النسبة المئوية للتبرع
  double get donationPercentage {
    if (targetAmount <= 0) return 0.0;
    return (raisedAmount / targetAmount).clamp(0.0, 1.0);
  }

  // الحصول على الصورة الأولى أو صورة افتراضية
  String get firstImageUrl => imageUrls.isNotEmpty
      ? imageUrls.first
      : videoUrl ?? defaultImageUrl;

  // التحقق من انتهاء الصلاحية
  bool get isExpired => endDate != null && endDate!.isBefore(DateTime.now());

  // الحصول على حالة الحملة
  String get statusText {
    if (isCompleted) return 'مكتمل';
    if (isExpired) return 'منتهي';
    if (donationPercentage >= 1.0) return 'تم التمويل بالكامل';
    return 'نشط';
  }

  factory DonationAd.fromJson(Map<String, dynamic> json) {
    return DonationAd(
      id: json['_id'] ?? const Uuid().v4(),
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      location: json['location'] ?? '',
      latitude: (json['latitude'] as num?)?.toDouble(),
      longitude: (json['longitude'] as num?)?.toDouble(),
      rating: (json['rating'] as num?)?.toDouble(),
      ratingCount: json['ratingCount'] as int?,
      amount: json['amount'] ?? '0',
      category: json['category'] ?? 'أخرى',
      imageUrls: List<String>.from(json['imageUrls'] ?? []),
      videoUrl: json['videoUrl'],
      isCompleted: json['isCompleted'] ?? false,
      creatorId: json['creatorId'] ?? '',
      donationStatus: json['donationStatus'] ?? 'active',
      raisedAmount: (json['raisedAmount'] as num?)?.toDouble() ?? 0.0,
      targetAmount: (json['targetAmount'] as num?)?.toDouble() ?? 0.0,
      date: json['date'] != null
          ? DateTime.tryParse(json['date']) ?? DateTime.now()
          : DateTime.now(),
      requestDate: json['requestDate'] != null
          ? DateTime.tryParse(json['requestDate']) ?? DateTime.now()
          : DateTime.now(),
      completedDate: json['completedDate'] != null
          ? DateTime.tryParse(json['completedDate'])
          : null,
      endDate: json['endDate'] != null
          ? DateTime.tryParse(json['endDate'])
          : null,
      isFeatured: json['isFeatured'] ?? false,
      isFavorite: json['isFavorite'] ?? false,
      impactMedia: (json['impactMedia'] as List? ?? [])
          .map((item) => ImpactMedia.fromJson(item))
          .toList(),
      donorCount: json['donorCount'] ?? 0,
      urgencyLevel: (json['urgencyLevel'] as num?)?.toDouble() ?? 0.0,
      tags: List<String>.from(json['tags'] ?? []),
      metadata: Map<String, dynamic>.from(json['metadata'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'title': title,
      'description': description,
      'location': location,
      'latitude': latitude,
      'longitude': longitude,
      'rating': rating,
      'ratingCount': ratingCount,
      'amount': amount,
      'category': category,
      'imageUrls': imageUrls,
      'videoUrl': videoUrl,
      'isCompleted': isCompleted,
      'creatorId': creatorId,
      'donationStatus': donationStatus,
      'raisedAmount': raisedAmount,
      'targetAmount': targetAmount,
      'date': date.toIso8601String(),
      'requestDate': requestDate.toIso8601String(),
      'completedDate': completedDate?.toIso8601String(),
      'endDate': endDate?.toIso8601String(),
      'isFeatured': isFeatured,
      'isFavorite': isFavorite,
      'impactMedia': impactMedia.map((item) => item.toJson()).toList(),
      'donorCount': donorCount,
      'urgencyLevel': urgencyLevel,
      'tags': tags,
      'metadata': metadata,
    };
  }
}

/// Enhanced ImpactMedia model for donation impact tracking
class ImpactMedia {
  final String id;
  final String url;
  final String type;
  final DateTime uploadedAt;
  final String description;
  final String? thumbnailUrl;
  final Map<String, dynamic> metadata;

  ImpactMedia({
    required this.id,
    required this.url,
    required this.type,
    required this.uploadedAt,
    required this.description,
    this.thumbnailUrl,
    this.metadata = const {},
  });

  factory ImpactMedia.fromJson(Map<String, dynamic> json) {
    return ImpactMedia(
      id: json['_id'] ?? '',
      url: json['url'] ?? '',
      type: json['type'] ?? 'image',
      uploadedAt: DateTime.tryParse(json['uploadedAt'] ?? '') ?? DateTime.now(),
      description: json['description'] ?? '',
      thumbnailUrl: json['thumbnailUrl'],
      metadata: Map<String, dynamic>.from(json['metadata'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'url': url,
      'type': type,
      'uploadedAt': uploadedAt.toIso8601String(),
      'description': description,
      'thumbnailUrl': thumbnailUrl,
      'metadata': metadata,
    };
  }
}

/// Enhanced Donor model with additional features
class Donor {
  final String id;
  final String name;
  final String? avatarUrl;
  final double totalDonated;
  final int donationCount;
  final DateTime joinDate;
  final DateTime? lastDonationDate;
  bool showHistory;
  final List<String> badges;
  final Map<String, dynamic> preferences;

  Donor({
    required this.id,
    required this.name,
    this.avatarUrl,
    required this.totalDonated,
    required this.donationCount,
    required this.joinDate,
    this.lastDonationDate,
    required this.showHistory,
    this.badges = const [],
    this.preferences = const {},
  });

  /// Get donor level based on total donated amount
  String get donorLevel {
    if (totalDonated >= 10000) return 'ذهبي';
    if (totalDonated >= 5000) return 'فضي';
    if (totalDonated >= 1000) return 'برونزي';
    return 'مبتدئ';
  }

  /// Get donor level color
  Color get donorLevelColor {
    switch (donorLevel) {
      case 'ذهبي':
        return Colors.amber;
      case 'فضي':
        return Colors.grey[400]!;
      case 'برونزي':
        return Colors.brown;
      default:
        return Colors.blue;
    }
  }

  factory Donor.fromJson(Map<String, dynamic> json) {
    return Donor(
      id: json['_id'] ?? '',
      name: json['name'] ?? 'متبرع مجهول',
      avatarUrl: json['avatarUrl'],
      totalDonated: (json['totalDonated'] as num?)?.toDouble() ?? 0.0,
      donationCount: json['donationCount'] ?? 0,
      joinDate: DateTime.tryParse(json['joinDate'] ?? '') ?? DateTime.now(),
      lastDonationDate: json['lastDonationDate'] != null
          ? DateTime.tryParse(json['lastDonationDate'])
          : null,
      showHistory: json['showHistory'] ?? false,
      badges: List<String>.from(json['badges'] ?? []),
      preferences: Map<String, dynamic>.from(json['preferences'] ?? {}),
    );
  }
}

// ===============================
// Enhanced Notification System
// ===============================

/// Enhanced notification model
class AppNotification {
  final String id;
  final String title;
  final String message;
  final String type;
  final DateTime createdAt;
  final bool isRead;
  final Map<String, dynamic> data;
  final String? actionUrl;
  final String? imageUrl;

  AppNotification({
    required this.id,
    required this.title,
    required this.message,
    required this.type,
    required this.createdAt,
    this.isRead = false,
    this.data = const {},
    this.actionUrl,
    this.imageUrl,
  });

  /// Get notification icon based on type
  IconData get icon {
    switch (type) {
      case 'donation':
        return Icons.volunteer_activism;
      case 'completion':
        return Icons.check_circle;
      case 'urgent':
        return Icons.warning;
      case 'update':
        return Icons.info;
      default:
        return Icons.notifications;
    }
  }

  /// Get notification color based on type
  Color get color {
    switch (type) {
      case 'donation':
        return AppColors.success;
      case 'completion':
        return AppColors.primary;
      case 'urgent':
        return AppColors.warning;
      case 'update':
        return AppColors.accent;
      default:
        return AppColors.onSurfaceVariant;
    }
  }

  factory AppNotification.fromJson(Map<String, dynamic> json) {
    return AppNotification(
      id: json['_id'] ?? '',
      title: json['title'] ?? '',
      message: json['message'] ?? '',
      type: json['type'] ?? 'general',
      createdAt: DateTime.tryParse(json['createdAt'] ?? '') ?? DateTime.now(),
      isRead: json['isRead'] ?? false,
      data: Map<String, dynamic>.from(json['data'] ?? {}),
      actionUrl: json['actionUrl'],
      imageUrl: json['imageUrl'],
    );
  }
}

// ===============================
// Enhanced Service Layer
// ===============================

/// Enhanced DonationAdService with better error handling and caching
/// Donation model for tracking user donations
class Donation {
  final String id;
  final String campaignId;
  final String campaignName;
  final double amount;
  final DateTime date;
  final String status; // 'مكتمل', 'معلق', 'ملغي'
  final String? transactionId;

  Donation({
    required this.id,
    required this.campaignId,
    required this.campaignName,
    required this.amount,
    required this.date,
    required this.status,
    this.transactionId,
  });

  factory Donation.fromJson(Map<String, dynamic> json) {
    return Donation(
      id: json['id'] ?? '',
      campaignId: json['campaignId'] ?? '',
      campaignName: json['campaignName'] ?? 'حملة مجهولة',
      amount: (json['amount'] as num?)?.toDouble() ?? 0.0,
      date: DateTime.parse(json['date'] ?? DateTime.now().toIso8601String()),
      status: json['status'] ?? 'معلق',
      transactionId: json['transactionId'],
    );
  }
}

class DonationAdService {
  final String baseUrl;
  final String authToken;
  final Dio _dio;
  io.Socket? socket;

  // Cache for better performance
  final Map<String, List<DonationAd>> _adsCache = {};
  final Map<String, DateTime> _cacheTimestamps = {};
  static const Duration _cacheExpiry = Duration(minutes: 5);

  DonationAdService({required this.baseUrl, required this.authToken})
      : _dio = Dio(BaseOptions(
          baseUrl: baseUrl,
          connectTimeout: const Duration(seconds: 10),
          receiveTimeout: const Duration(seconds: 10),
          headers: {'Authorization': 'Bearer $authToken'},
        )) {
    _setupInterceptors();
  }

  /// Setup Dio interceptors for better error handling and logging
  void _setupInterceptors() {
    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) {
        print('🚀 Request: ${options.method} ${options.path}');
        handler.next(options);
      },
      onResponse: (response, handler) {
        print(
            '✅ Response: ${response.statusCode} ${response.requestOptions.path}');
        handler.next(response);
      },
      onError: (error, handler) {
        print('❌ Error: ${error.message}');
        handler.next(error);
      },
    ));
  }

  /// Check if cache is valid
  bool _isCacheValid(String key) {
    final timestamp = _cacheTimestamps[key];
    if (timestamp == null) return false;
    return DateTime.now().difference(timestamp) < _cacheExpiry;
  }

  /// Fetch ads with enhanced caching and error handling
  Future<List<DonationAd>> fetchAds({
    String search = '',
    String filter = 'all',
    String sort = 'newest',
    int skip = 0,
    int limit = 10,
    bool forceRefresh = false,
  }) async {
    final cacheKey = 'ads_${search}_${filter}_${sort}_${skip}_$limit';

    // Return cached data if valid and not forcing refresh
    if (!forceRefresh &&
        _isCacheValid(cacheKey) &&
        _adsCache.containsKey(cacheKey)) {
      return Future.value(_adsCache[cacheKey]!);
    }

    return handleError(() => _dio.get('/donation_ads', queryParameters: {
          'search': search,
          'filter': filter,
          'sort': sort,
          'skip': skip,
          'limit': limit,
        }).then((response) {
          final ads = (response.data as List)
              .map((item) => DonationAd.fromJson(item))
              .toList();

          // Cache the results
          _adsCache[cacheKey] = ads;
          _cacheTimestamps[cacheKey] = DateTime.now();

          return ads;
        }),
        customMessage: 'فشل في جلب الإعلانات');
  }

  /// Fetch donation history with enhanced error handling
  Future<List<Map<String, dynamic>>> fetchDonationHistory(String adId) {
    return handleError(
        () => _dio
            .get('/donation_ads/$adId/donations')
            .then((response) => List<Map<String, dynamic>>.from(response.data)),
        customMessage: 'فشل في جلب سجل التبرعات');
  }

  /// Fetch received donations with enhanced error handling
  Future<List<Map<String, dynamic>>> fetchReceivedDonations(String creatorId) {
    return handleError(
        () => _dio
            .get('/donation_ads/received/$creatorId')
            .then((response) => List<Map<String, dynamic>>.from(response.data)),
        customMessage: 'فشل في جلب التبرعات المستلمة');
  }

  /// Fetch donor information with enhanced error handling
  Future<Donor?> fetchDonor(String donorId) {
    return handleError(
        () => _dio
            .get('/donors/$donorId')
            .then((response) => Donor.fromJson(response.data)),
        customMessage: 'فشل في جلب بيانات المتبرع');
  }

  /// Fetch notifications with enhanced error handling
  Future<List<AppNotification>> fetchNotifications() async {
    return handleError(() async {
      final response = await _dio.get('/notifications');
      return (response.data as List)
          .map((item) => AppNotification.fromJson(item))
          .toList();
    }, customMessage: 'فشل في جلب الإشعارات');
  }

  /// Create new donation ad with enhanced error handling
  Future<void> createAd({
    required String title,
    required String description,
    required String location,
    required String amount,
    required String category,
    required File file,
    required bool isCompleted,
    required String creatorId,
    List<String> tags = const [],
    double urgencyLevel = 0.0,
  }) async {
    return handleError(() async {
      final formData = FormData.fromMap({
        'title': title,
        'description': description,
        'location': location,
        'amount': amount.isEmpty ? 'غير محدد' : amount,
        'category': category,
        'isCompleted': isCompleted,
        'creatorId': creatorId,
        'requestDate': DateTime.now().toIso8601String(),
        'tags': tags.join(','),
        'urgencyLevel': urgencyLevel,
        'file': await MultipartFile.fromFile(file.path),
      });

      await _dio.post('/donation_ads', data: formData);

      // Clear cache after creating new ad
      _adsCache.clear();
      _cacheTimestamps.clear();
    }, customMessage: 'فشل في إنشاء الإعلان');
  }

  /// Upload impact media with enhanced error handling
  Future<void> uploadImpactMedia({
    required String adId,
    required File file,
    required String description,
    required String type,
  }) async {
    return handleError(
      () async {
        final formData = FormData.fromMap({
          'adId': adId,
          'description': description,
          'type': type,
          'file': await MultipartFile.fromFile(file.path),
        });

        await _dio.post('/donation_ads/impact-media', data: formData);
      },
      customMessage: 'فشل في رفع الوسائط',
    );
  }

  /// Delete donation ad with enhanced error handling
  Future<void> deleteAd(String id) async {
    return handleError(
      () async {
        await _dio.delete('/donation_ads/$id');

        // Clear cache after deletion
        _adsCache.clear();
        _cacheTimestamps.clear();
      },
      customMessage: 'فشل في حذف الإعلان',
    );
  }

  /// Make donation with enhanced error handling
  Future<void> donate({
    required String adId,
    required String donorId,
    required double amount,
    String? message,
  }) async {
    return handleError(
      () async {
        await _dio.post('/donation_ads/donations', data: {
          'adId': adId,
          'donorId': donorId,
          'amount': amount,
          'message': message,
          'timestamp': DateTime.now().toIso8601String(),
        });

        // Clear cache after donation
        _adsCache.clear();
        _cacheTimestamps.clear();
      },
      customMessage: 'فشل في التبرع',
    );
  }

  /// Update ad status with enhanced error handling
  Future<void> updateAdStatus(String adId, String status) async {
    return handleError(
      () async {
        await _dio.put('/donation_ads/$adId/status', data: {'status': status});

        // Clear cache after status update
        _adsCache.clear();
        _cacheTimestamps.clear();
      },
      customMessage: 'فشل في تحديث حالة الإعلان',
    );
  }

  /// Update donor history visibility
  Future<void> updateDonorHistoryVisibility(
      String donorId, bool showHistory) async {
    return handleError(
      () async {
        await _dio.put('/donors/$donorId/visibility',
            data: {'showHistory': showHistory});
      },
      customMessage: 'فشل في تحديث إعدادات الخصوصية',
    );
  }

  /// Mark notification as read
  Future<void> markNotificationAsRead(String notificationId) async {
    return handleError(
      () async {
        await _dio.put('/notifications/$notificationId/read');
      },
      customMessage: 'فشل في تحديث حالة الإشعار',
    );
  }

  /// Setup enhanced socket listeners
  void setupSocketListeners(
    io.Socket socket, {
    Function(dynamic)? onNewAd,
    Function(dynamic)? onAdUpdate,
    Function(dynamic)? onNewDonation,
    Function(dynamic)? onNewNotification,
  }) {
    this.socket = socket;

    socket.on('new_donation_ad', (data) {
      _adsCache.clear();
      _cacheTimestamps.clear();
      onNewAd?.call(data);
    });

    socket.on('ad_updated', (data) {
      _adsCache.clear();
      _cacheTimestamps.clear();
      onAdUpdate?.call(data);
    });

    socket.on('new_donation', (data) {
      _adsCache.clear();
      _cacheTimestamps.clear();
      onNewDonation?.call(data);
    });

    socket.on('new_notification', (data) {
      onNewNotification?.call(data);
    });

    socket.on('new_impact_media', (data) {
      // Handle new impact media notification
    });
  }

  /// Clear all caches
  void clearCache() {
    _adsCache.clear();
    _cacheTimestamps.clear();
  }

  /// Dispose resources
  void dispose() {
    _dio.close();
    socket?.disconnect();
  }
}

// ===============================
// Enhanced Theme Provider
// ===============================

/// Theme provider for dark mode support
class ThemeProvider extends ChangeNotifier {
  bool _isDarkMode = false;
  late SharedPreferences _prefs;

  bool get isDarkMode => _isDarkMode;

  ThemeProvider() {
    _loadThemePreference();
  }

  /// Load theme preference from storage
  Future<void> _loadThemePreference() async {
    _prefs = await SharedPreferences.getInstance();
    _isDarkMode = _prefs.getBool('isDarkMode') ?? false;
    notifyListeners();
  }

  /// Toggle theme mode
  Future<void> toggleTheme() async {
    _isDarkMode = !_isDarkMode;
    await _prefs.setBool('isDarkMode', _isDarkMode);
    notifyListeners();
  }

  /// Get current theme data
  ThemeData get themeData {
    return _isDarkMode ? _darkTheme : _lightTheme;
  }

  /// Light theme configuration
  static final ThemeData _lightTheme = ThemeData(
    useMaterial3: true,
    brightness: Brightness.light,
    colorScheme: ColorScheme.fromSeed(
      seedColor: AppColors.primary,
      brightness: Brightness.light,
    ),
    appBarTheme: AppBarTheme(
      backgroundColor: AppColors.primary,
      foregroundColor: Colors.white,
      elevation: 0,
      centerTitle: true,
    ),
    cardTheme: CardThemeData(
      elevation: _cardElevation,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(_borderRadius),
      ),
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      clipBehavior: Clip.antiAlias,
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(_borderRadius),
        ),
      ),
    ),
  );

  /// Dark theme configuration
  static final ThemeData _darkTheme = ThemeData(
    useMaterial3: true,
    brightness: Brightness.dark,
    colorScheme: ColorScheme.fromSeed(
      seedColor: AppColors.primary,
      brightness: Brightness.dark,
    ),
    appBarTheme: AppBarTheme(
      backgroundColor: AppColors.primaryDark,
      foregroundColor: Colors.white,
      elevation: 0,
      centerTitle: true,
    ),
    cardTheme: CardThemeData(
      elevation: _cardElevation,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(_borderRadius),
      ),
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      clipBehavior: Clip.antiAlias,
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.primaryLight,
        foregroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(_borderRadius),
        ),
      ),
    ),
  );
}

// ===============================
// Main Application Widget
// ===============================

/// Enhanced main application widget
class Rayan4 extends StatelessWidget {
  const Rayan4({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => ThemeProvider()),
        ChangeNotifierProvider(create: (_) => AppState()),
        ChangeNotifierProvider(
          create: (context) => BadgesService(
            baseUrl: 'https://your-api-url.com/api',
            authToken: context.read<AppState>().authToken ?? '',
          ),
        ),
      ],
      child: Consumer3<ThemeProvider, AppState, BadgesService>(
        builder: (context, themeProvider, appState, badgesService, _) {
          // Initialize badges service if needed
          WidgetsBinding.instance.addPostFrameCallback((_) {
            final donorId = appState.currentUser?.id;
            if (donorId != null) {
              badgesService.fetchEarnedBadges(donorId);
              badgesService.fetchDonorStats(donorId);
              badgesService.fetchBadgeProgress(donorId);
            }
          });

          return MaterialApp(
            title: 'منصة التبرعات المحسنة',
            theme: themeProvider.themeData,
            home: const DonationAdsScreen(),
            debugShowCheckedModeBanner: false,
            routes: {
              '/badges': (context) => BadgesScreen(
                donorId: appState.currentUser?.id ?? '',
              ),
            },
          );
        },
      ),
    );
  }
}
// ===============================
// Enhanced Main Screen
// ===============================

/// Enhanced donation ads screen with improved UI and features
class DonationAdsScreen extends StatefulWidget {
  const DonationAdsScreen({super.key});

  @override
  State<DonationAdsScreen> createState() => _DonationAdsScreenState();
}

class _DonationAdsScreenState extends State<DonationAdsScreen>
    with TickerProviderStateMixin, AutomaticKeepAliveClientMixin {
  // Settings state
  bool _notificationEnabled = true;
  bool _donationHistoryVisible = true;
  String _selectedLanguage = 'ar';

  // ===============================
  // Controllers and Services
  // ===============================

  final PageController _adController = PageController();
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final RefreshController _refreshController =
      RefreshController(initialRefresh: false);

  late final DonationAdService _adService;
  late final AnimationController _fabAnimationController;
  late final AnimationController _searchAnimationController;
  late final ConfettiController _confettiController;

  final Uuid _uuid = const Uuid();

  // ===============================
  // State Variables
  // ===============================

  List<DonationAd> _donationAds = [];
  List<DonationAd> _filteredAds = [];
  List<AppNotification> _notifications = [];
  final Map<String, Donor> _donors = {};
  final Map<String, List<Map<String, dynamic>>> _donationHistory = {};
  final Map<String, List<Map<String, dynamic>>> _receivedDonations = {};

  int _currentAd = 0;
  int _selectedCategoryIndex = 0;
  int _visibleAds = 10;
  String _selectedSort = 'newest';
  String _selectedFilter = 'all';
  bool _isLoading = true;
  bool _isLoadingMore = false;
  bool _showScrollToTop = false;
  bool _showFeaturedOnly = false;
  bool _isSearchExpanded = false;
  bool _isOffline = false;

  Timer? _timer;
  Timer? _searchDebounceTimer;

  final String _currentUserId = 'user1';
  final bool _isAdmin = false;

  // ===============================
  // Configuration Data
  // ===============================

  final List<String> adImages = const [
    'https://images.unsplash.com/photo-1559027615-cd4628902d4a?w=800&h=400&fit=crop',
    'https://images.unsplash.com/photo-1488521787991-ed7bbaae773c?w=800&h=400&fit=crop',
    'https://images.unsplash.com/photo-*************-0ba58a3f068b?w=800&h=400&fit=crop',
  ];

  final List<Map<String, dynamic>> categories = const [
    {
      'icon': Icons.family_restroom,
      'title': 'كفالة أبنائنا',
      'count': 45,
      'filter': 'orphans',
      'color': Colors.blue
    },
    {
      'icon': Icons.local_hospital,
      'title': 'علاجية',
      'count': 32,
      'filter': 'medical',
      'color': Colors.red
    },
    {
      'icon': Icons.account_balance,
      'title': 'غارمين',
      'count': 67,
      'filter': 'debtors',
      'color': Colors.orange
    },
    {
      'icon': Icons.fastfood,
      'title': 'غذائية',
      'count': 23,
      'filter': 'food',
      'color': Colors.green
    },
    {
      'icon': Icons.home,
      'title': 'المسكن',
      'count': 23,
      'filter': 'housing',
      'color': Colors.brown
    },
    {
      'icon': Icons.school,
      'title': 'التعليم',
      'count': 23,
      'filter': 'education',
      'color': Colors.purple
    },
    {
      'icon': Icons.money,
      'title': 'قرض حسن',
      'count': 23,
      'filter': 'loan',
      'color': Colors.teal
    },
    {
      'icon': Icons.category,
      'title': 'أخرى',
      'count': 23,
      'filter': 'others',
      'color': Colors.grey
    },
  ];

  final List<Map<String, String>> sortOptions = const [
    {'value': 'newest', 'label': 'الأحدث'},
    {'value': 'oldest', 'label': 'الأقدم'},
    {'value': 'amount_high', 'label': 'المبلغ (الأعلى)'},
    {'value': 'amount_low', 'label': 'المبلغ (الأقل)'},
    {'value': 'progress', 'label': 'نسبة الإنجاز'},
    {'value': 'urgent', 'label': 'الأكثر إلحاحاً'},
  ];

  // ===============================
  // Lifecycle Methods
  // ===============================

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _initializeServices();
    _initializeAnimations();
    _setupListeners();
    _loadInitialData();
  }

  /// Initialize services and controllers
  void _initializeServices() {
    _adService = DonationAdService(
      baseUrl: AppState.getBackendUrl(),
      authToken: AppState.signerKey,
    );
  }

  /// Initialize animation controllers
  void _initializeAnimations() {
    _fabAnimationController = AnimationController(
      duration: _animationDuration,
      vsync: this,
    );

    _searchAnimationController = AnimationController(
      duration: _animationDuration,
      vsync: this,
    );

    _confettiController = ConfettiController(
      duration: const Duration(seconds: 3),
    );
  }

  /// Setup various listeners
  void _setupListeners() {
    _startAutoScroll();
    _scrollController.addListener(_scrollListener);
    _searchController.addListener(_onSearchChanged);

    // Setup connectivity listener
    Connectivity().onConnectivityChanged.listen((ConnectivityResult result) {
      setState(() {
        _isOffline = result == ConnectivityResult.none;
      });

      if (!_isOffline) {
        _refreshData();
      }
    });

    // Setup socket listeners
    final appState = Provider.of<AppState>(context, listen: false);
    if (appState.socket != null) {
      _adService.setupSocketListeners(
        appState.socket!,
        onNewAd: _handleNewAd,
        onAdUpdate: _handleAdUpdate,
        onNewDonation: _handleNewDonation,
        onNewNotification: _handleNewNotification,
      );
    }
  }

  /// Load initial data
  Future<void> _loadInitialData() async {
    await Future.wait([
      _fetchAds(),
      _fetchNotifications(),
    ]);
  }

  @override
  void dispose() {
    _timer?.cancel();
    _searchDebounceTimer?.cancel();
    _adController.dispose();
    _scrollController.dispose();
    _searchController.dispose();
    _refreshController.dispose();
    _fabAnimationController.dispose();
    _searchAnimationController.dispose();
    _confettiController.dispose();
    _adService.dispose();
    super.dispose();
  }

  // ===============================
  // Event Handlers
  // ===============================

  /// Handle new ad from socket
  void _handleNewAd(dynamic data) {
    _fetchAds();
    _showNotificationSnackBar(
      'إعلان تبرع جديد: ${data['title']}',
      AppColors.success,
    );
    _confettiController.play();
  }

  /// Handle ad update from socket
  void _handleAdUpdate(dynamic data) {
    _fetchAds();
  }

  /// Handle new donation from socket
  void _handleNewDonation(dynamic data) {
    _fetchAds();
    _showNotificationSnackBar(
      'تبرع جديد بمبلغ ${data['amount']} ريال',
      AppColors.primary,
    );
  }

  /// Handle new notification from socket
  void _handleNewNotification(dynamic data) {
    setState(() {
      _notifications.insert(0, AppNotification.fromJson(data));
    });
    _showNotificationSnackBar(
      data['message'],
      AppColors.accent,
    );
  }

  /// Handle search text changes with debouncing
  void _onSearchChanged() {
    _searchDebounceTimer?.cancel();
    _searchDebounceTimer = Timer(const Duration(milliseconds: 500), () {
      _filterAds();
    });
  }

  /// Handle scroll events
  void _scrollListener() {
    if (_scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 200 &&
        !_isLoadingMore) {
      _loadMoreAds();
    }

    final showScrollToTop = _scrollController.position.pixels > 200;
    if (showScrollToTop != _showScrollToTop) {
      setState(() => _showScrollToTop = showScrollToTop);

      if (showScrollToTop) {
        _fabAnimationController.forward();
      } else {
        _fabAnimationController.reverse();
      }
    }
  }

  // ===============================
  // Data Fetching Methods
  // ===============================

  /// Fetch donation ads with error handling
  Future<void> _fetchAds({bool forceRefresh = false}) async {
    if (!forceRefresh && _isOffline) return;

    setState(() => _isLoading = true);

    try {
      final ads = await _adService.fetchAds(
        search: _searchController.text,
        filter: _selectedFilter,
        sort: _selectedSort,
        forceRefresh: forceRefresh,
      );

      setState(() {
        _donationAds = ads;
        _isFormLoading = false;
      });

      _filterAds();
    } catch (e) {
      _showErrorSnackBar('خطأ في جلب الإعلانات: $e');
      setState(() => _isLoading = false);
    }
  }

  /// Load more ads for pagination
  Future<void> _loadMoreAds() async {
    if (_isOffline) return;

    setState(() => _isLoadingMore = true);

    try {
      final newAds = await _adService.fetchAds(
        search: _searchController.text,
        filter: _selectedFilter,
        sort: _selectedSort,
        skip: _visibleAds,
      );

      setState(() {
        _donationAds.addAll(newAds);
        _visibleAds += 10;
        _isLoadingMore = false;
      });

      _filterAds();
    } catch (e) {
      _showErrorSnackBar('خطأ في تحميل المزيد: $e');
      setState(() => _isLoadingMore = false);
    }
  }

  /// Fetch notifications
  Future<void> _fetchNotifications() async {
    if (_isOffline) return;

    try {
      final notifications = await _adService.fetchNotifications();
      setState(() => _notifications = notifications);
    } catch (e) {
      _showErrorSnackBar('خطأ في جلب الإشعارات: $e');
    }
  }

  /// Fetch donation history for specific ad
  Future<void> _fetchDonationHistory(String adId) async {
    if (_isOffline) return;

    try {
      final history = await _adService.fetchDonationHistory(adId);
      setState(() => _donationHistory[adId] = history);
    } catch (e) {
      _showErrorSnackBar('خطأ في جلب سجل التبرعات: $e');
    }
  }

  /// Fetch received donations for creator
  Future<void> _fetchReceivedDonations(String creatorId) async {
    if (_isOffline) return;

    try {
      final donations = await _adService.fetchReceivedDonations(creatorId);
      setState(() => _receivedDonations[creatorId] = donations);
    } catch (e) {
      _showErrorSnackBar('خطأ في جلب التبرعات المستلمة: $e');
    }
  }

  /// Fetch donor information
  Future<void> _fetchDonor(String donorId) async {
    if (_isOffline || _donors.containsKey(donorId)) return;

    try {
      final donor = await _adService.fetchDonor(donorId);
      if (donor != null) {
        setState(() => _donors[donorId] = donor);
      }
    } catch (e) {
      // Silently handle donor fetch errors
    }
  }

  // ===============================
  // UI Helper Methods
  // ===============================

  /// Filter ads based on search and category
  void _filterAds() {
    List<DonationAd> filtered = List.from(_donationAds);

    // Apply search filter
    if (_searchController.text.isNotEmpty) {
      final searchTerm = _searchController.text.toLowerCase();
      filtered = filtered
          .where((ad) =>
              ad.title.toLowerCase().contains(searchTerm) ||
              ad.description.toLowerCase().contains(searchTerm) ||
              ad.location.toLowerCase().contains(searchTerm) ||
              ad.tags.any((tag) => tag.toLowerCase().contains(searchTerm)))
          .toList();
    }

    // Apply category filter
    if (_selectedCategoryIndex > 0) {
      final categoryFilter = categories[_selectedCategoryIndex - 1]['filter'];
      filtered = filtered.where((ad) => ad.category == categoryFilter).toList();
    }

    // Apply featured filter
    if (_showFeaturedOnly) {
      filtered = filtered.where((ad) => ad.isFeatured).toList();
    }

    setState(() => _filteredAds = filtered);
  }

  /// Refresh all data
  Future<void> _refreshData() async {
    HapticFeedback.lightImpact();
    setState(() => _visibleAds = 10);

    await Future.wait([
      _fetchAds(forceRefresh: true),
      _fetchNotifications(),
    ]);

    _refreshController.refreshCompleted();
  }

  /// Start auto-scroll for banner ads
  void _startAutoScroll() {
    _timer = Timer.periodic(const Duration(seconds: 5), (timer) {
      if (!mounted || adImages.isEmpty) return;

      setState(() => _currentAd = (_currentAd + 1) % adImages.length);

      if (_adController.hasClients) {
        _adController.animateToPage(
          _currentAd,
          duration: _slowAnimationDuration,
          curve: Curves.easeInOut,
        );
      }
    });
  }

  /// Show success snack bar
  void _showSuccessSnackBar(String message) {
    _showNotificationSnackBar(message, AppColors.success);
  }

  /// Show error snack bar
  void _showErrorSnackBar(String message) {
    _showNotificationSnackBar(message, AppColors.error);
  }

  /// Show notification snack bar with custom styling
  void _showNotificationSnackBar(String message, Color backgroundColor) {
    if (!mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(
              backgroundColor == AppColors.error
                  ? Icons.error
                  : Icons.check_circle,
              color: Colors.white,
              size: 20,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                message,
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
        backgroundColor: backgroundColor,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(_borderRadius),
        ),
        elevation: 6,
        margin: const EdgeInsets.all(16),
        duration: const Duration(seconds: 4),
        action: SnackBarAction(
          label: 'إغلاق',
          textColor: Colors.white,
          onPressed: () {
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
          },
        ),
      ),
    );
  }

  // ===============================
  // UI Building Methods
  // ===============================

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      body: OfflineBuilder(
        connectivityBuilder: (context, connectivity, child) {
          final connected = connectivity != ConnectivityResult.none;

          if (!connected) {
            return _buildOfflineWidget();
          }

          return child!;
        },
        child: Stack(
          children: [
            _buildMainContent(isDarkMode),
            _buildFloatingElements(),
            _buildConfetti(),
          ],
        ),
      ),
    );
  }

  /// Build main content
  Widget _buildMainContent(bool isDarkMode) {
    return NestedScrollView(
      headerSliverBuilder: (context, innerBoxIsScrolled) => [
        _buildSliverAppBar(isDarkMode),
      ],
      body: SmartRefresher(
        controller: _refreshController,
        onRefresh: _refreshData,
        header: WaterDropMaterialHeader(
          backgroundColor: AppColors.primary,
          color: Colors.white,
        ),
        child: CustomScrollView(
          controller: _scrollController,
          slivers: [
            _buildBannerSection(),
            _buildCategoriesSection(),
            _buildFiltersSection(),
            _buildAdsSection(),
            _buildLoadingMoreSection(),
          ],
        ),
      ),
    );
  }

  /// Build sliver app bar with enhanced design
  Widget _buildSliverAppBar(bool isDarkMode) {
    return SliverAppBar(
      expandedHeight: 120,
      floating: true,
      pinned: true,
      snap: true,
      flexibleSpace: FlexibleSpaceBar(
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: isDarkMode
                  ? [AppColors.primaryDark, AppColors.primary]
                  : [AppColors.primary, AppColors.primaryLight],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
        ),
        title: AnimatedSwitcher(
          duration: _animationDuration,
          child: _isSearchExpanded
              ? _buildSearchField()
              : const Text(
                  'منصة التبرعات',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
        ),
      ),
      actions: [
        _buildSearchToggleButton(),
        _buildNotificationButton(),
        _buildThemeToggleButton(),
        _buildMenuButton(),
      ],
    );
  }

  /// Build search field
  Widget _buildSearchField() {
    return Container(
      height: 40,
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.2),
        borderRadius: BorderRadius.circular(_borderRadius),
      ),
      child: TextField(
        controller: _searchController,
        style: const TextStyle(color: Colors.white),
        decoration: InputDecoration(
          hintText: 'ابحث عن إعلان تبرع...',
          hintStyle: const TextStyle(color: Colors.white70),
          border: InputBorder.none,
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          prefixIcon: const Icon(Icons.search, color: Colors.white70),
          suffixIcon: _searchController.text.isNotEmpty
              ? IconButton(
                  icon: const Icon(Icons.clear, color: Colors.white70),
                  onPressed: () {
                    _searchController.clear();
                    HapticFeedback.selectionClick();
                  },
                )
              : null,
        ),
        onSubmitted: (_) => _fetchAds(),
      ),
    );
  }

  /// Build search toggle button
  Widget _buildSearchToggleButton() {
    return IconButton(
      icon: AnimatedRotation(
        turns: _isSearchExpanded ? 0.5 : 0,
        duration: _animationDuration,
        child: Icon(
          _isSearchExpanded ? Icons.close : Icons.search,
          color: Colors.white,
        ),
      ),
      onPressed: () {
        setState(() => _isSearchExpanded = !_isSearchExpanded);
        HapticFeedback.selectionClick();

        if (_isSearchExpanded) {
          _searchAnimationController.forward();
        } else {
          _searchAnimationController.reverse();
          _searchController.clear();
        }
      },
    );
  }

  /// Build notification button with badge
  Widget _buildNotificationButton() {
    final unreadCount = _notifications.where((n) => !n.isRead).length;

    return Stack(
      children: [
        IconButton(
          icon: const Icon(Icons.notifications, color: Colors.white),
          onPressed: () {
            HapticFeedback.selectionClick();
            _showNotificationsBottomSheet();
          },
        ),
        if (unreadCount > 0)
          Positioned(
            right: 8,
            top: 8,
            child: Container(
              padding: const EdgeInsets.all(2),
              decoration: BoxDecoration(
                color: AppColors.error,
                borderRadius: BorderRadius.circular(10),
              ),
              constraints: const BoxConstraints(
                minWidth: 16,
                minHeight: 16,
              ),
              child: Text(
                unreadCount > 99 ? '99+' : unreadCount.toString(),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
      ],
    );
  }

  /// Build theme toggle button
  Widget _buildThemeToggleButton() {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return IconButton(
          icon: AnimatedSwitcher(
            duration: _animationDuration,
            child: Icon(
              themeProvider.isDarkMode ? Icons.light_mode : Icons.dark_mode,
              key: ValueKey(themeProvider.isDarkMode),
              color: Colors.white,
            ),
          ),
          onPressed: () {
            HapticFeedback.selectionClick();
            themeProvider.toggleTheme();
          },
        );
      },
    );
  }

  /// Build menu button
  Widget _buildMenuButton() {
    return PopupMenuButton<String>(
      icon: const Icon(Icons.more_vert, color: Colors.white),
      onSelected: _handleMenuSelection,
      itemBuilder: (context) => [
        const PopupMenuItem(
          value: 'history',
          child: ListTile(
            leading: Icon(Icons.history),
            title: Text('سجل التبرعات'),
            contentPadding: EdgeInsets.zero,
          ),
        ),
        const PopupMenuItem(
          value: 'favorites',
          child: ListTile(
            leading: Icon(Icons.favorite),
            title: Text('المفضلة'),
            contentPadding: EdgeInsets.zero,
          ),
        ),
        const PopupMenuItem(
          value: 'settings',
          child: ListTile(
            leading: Icon(Icons.settings),
            title: Text('الإعدادات'),
            contentPadding: EdgeInsets.zero,
          ),
        ),
        const PopupMenuItem(
          value: 'about',
          child: ListTile(
            leading: Icon(Icons.info),
            title: Text('حول التطبيق'),
            contentPadding: EdgeInsets.zero,
          ),
        ),
      ],
    );
  }

  /// Handle menu selection
  void _handleMenuSelection(String value) {
    HapticFeedback.selectionClick();

    switch (value) {
      case 'history':
        // Show a message that donor history is available per ad
        _showSuccessSnackBar('الرجاء اختيار إعلان لعرض سجل التبرعات الخاصة به');
        break;
      case 'favorites':
        _showFavoriteAds();
        break;
      case 'settings':
        _showSettingsDialog();
        break;
      case 'about':
        _showAboutDialog();
        break;
    }
  }

  /// Build banner section with enhanced carousel
  Widget _buildBannerSection() {
    return SliverToBoxAdapter(
      child: Container(
        height: 200,
        margin: const EdgeInsets.all(16),
        child: Stack(
          children: [
            PageView.builder(
              controller: _adController,
              onPageChanged: (index) => setState(() => _currentAd = index),
              itemCount: adImages.length,
              itemBuilder: (context, index) =>
                  _buildBannerCard(adImages[index]),
            ),
            _buildBannerIndicators(),
          ],
        ),
      ),
    );
  }

  /// Build individual banner card
  Widget _buildBannerCard(String imageUrl) {
    return FadeInUp(
      duration: _slowAnimationDuration,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(_borderRadius),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 10,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(_borderRadius),
          child: Stack(
            fit: StackFit.expand,
            children: [
              CachedNetworkImage(
                imageUrl: imageUrl,
                fit: BoxFit.cover,
                placeholder: (context, url) => Shimmer.fromColors(
                  baseColor: Colors.grey[300]!,
                  highlightColor: Colors.grey[100]!,
                  child: Container(color: Colors.white),
                ),
                errorWidget: (context, url, error) => Container(
                  color: Colors.grey[300],
                  child: const Icon(Icons.error, size: 50),
                ),
              ),
              Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.transparent,
                      Colors.black.withOpacity(0.7),
                    ],
                  ),
                ),
              ),
              Positioned(
                bottom: 16,
                left: 16,
                right: 16,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'ساعد في تغيير حياة الآخرين',
                      style:
                          Theme.of(context).textTheme.headlineSmall?.copyWith(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'انضم إلى مجتمع المتبرعين وكن جزءاً من التغيير الإيجابي',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Colors.white70,
                          ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Build banner indicators
  Widget _buildBannerIndicators() {
    return Positioned(
      bottom: 16,
      right: 16,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: List.generate(
          adImages.length,
          (index) => AnimatedContainer(
            duration: _animationDuration,
            margin: const EdgeInsets.symmetric(horizontal: 2),
            width: _currentAd == index ? 20 : 8,
            height: 8,
            decoration: BoxDecoration(
              color: _currentAd == index
                  ? Colors.white
                  : Colors.white.withOpacity(0.5),
              borderRadius: BorderRadius.circular(4),
            ),
          ),
        ),
      ),
    );
  }

  /// Build categories section with enhanced design
  Widget _buildCategoriesSection() {
    return SliverToBoxAdapter(
      child: Container(
        height: 120,
        margin: const EdgeInsets.symmetric(vertical: 8),
        child: AnimationLimiter(
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: categories.length + 1, // +1 for "All" category
            itemBuilder: (context, index) {
              return AnimationConfiguration.staggeredList(
                position: index,
                duration: _animationDuration,
                child: SlideAnimation(
                  horizontalOffset: 50.0,
                  child: FadeInAnimation(
                    child: _buildCategoryCard(index),
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  /// Build individual category card
  Widget _buildCategoryCard(int index) {
    final isSelected = _selectedCategoryIndex == index;
    final isAllCategory = index == 0;

    final category = isAllCategory
        ? {
            'icon': Icons.all_inclusive,
            'title': 'الكل',
            'count': _donationAds.length,
            'color': AppColors.primary
          }
        : categories[index - 1];

    return GestureDetector(
      onTap: () {
        setState(() => _selectedCategoryIndex = index);
        HapticFeedback.selectionClick();
        _filterAds();
      },
      child: AnimatedContainer(
        duration: _animationDuration,
        width: 100,
        margin: const EdgeInsets.only(right: 12),
        decoration: BoxDecoration(
          color: isSelected
              ? (category['color'] as Color).withOpacity(0.1)
              : Theme.of(context).cardColor,
          borderRadius: BorderRadius.circular(_borderRadius),
          border: Border.all(
            color:
                isSelected ? (category['color'] as Color) : Colors.transparent,
            width: 2,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            AnimatedScale(
              scale: isSelected ? 1.2 : 1.0,
              duration: _animationDuration,
              child: Icon(
                category['icon'] as IconData,
                size: 32,
                color: isSelected
                    ? (category['color'] as Color)
                    : Theme.of(context).iconTheme.color,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              category['title'] as String,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    fontWeight:
                        isSelected ? FontWeight.bold : FontWeight.normal,
                    color: isSelected ? (category['color'] as Color) : null,
                  ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 4),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: (category['color'] as Color).withOpacity(0.1),
                borderRadius: BorderRadius.circular(10),
              ),
              child: Text(
                '${category['count']}',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: category['color'] as Color,
                      fontWeight: FontWeight.bold,
                      fontSize: 10,
                    ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build filters section
  Widget _buildFiltersSection() {
    return SliverToBoxAdapter(
      child: Container(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Expanded(
              child: _buildSortDropdown(),
            ),
            const SizedBox(width: 12),
            _buildFeaturedToggle(),
            const SizedBox(width: 12),
            _buildFilterButton(),
          ],
        ),
      ),
    );
  }

  /// Build sort dropdown
  Widget _buildSortDropdown() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(_borderRadius),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String>(
          value: _selectedSort,
          isExpanded: true,
          icon: const Icon(Icons.arrow_drop_down),
          items: sortOptions.map((option) {
            return DropdownMenuItem<String>(
              value: option['value'],
              child: Text(option['label']!),
            );
          }).toList(),
          onChanged: (value) {
            if (value != null) {
              setState(() => _selectedSort = value);
              HapticFeedback.selectionClick();
              _fetchAds();
            }
          },
        ),
      ),
    );
  }

  /// Build featured toggle
  Widget _buildFeaturedToggle() {
    return FilterChip(
      label: const Text('مميز'),
      selected: _showFeaturedOnly,
      onSelected: (selected) {
        setState(() => _showFeaturedOnly = selected);
        HapticFeedback.selectionClick();
        _filterAds();
      },
      selectedColor: AppColors.primary.withOpacity(0.2),
      checkmarkColor: AppColors.primary,
    );
  }

  /// Build filter button
  Widget _buildFilterButton() {
    return IconButton(
      icon: const Icon(Icons.filter_list),
      onPressed: () {
        HapticFeedback.selectionClick();
        _showAdvancedFiltersBottomSheet();
      },
    );
  }

  /// Build ads section with enhanced layout
  Widget _buildAdsSection() {
    if (_isLoading) {
      return _buildLoadingSection();
    }

    if (_filteredAds.isEmpty) {
      return _buildEmptySection();
    }

    return SliverPadding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      sliver: SliverList(
        delegate: SliverChildBuilderDelegate(
          (context, index) {
            if (index >= _filteredAds.length) return null;

            return AnimationConfiguration.staggeredList(
              position: index,
              duration: _animationDuration,
              child: SlideAnimation(
                verticalOffset: 50.0,
                child: FadeInAnimation(
                  child: _buildAdCard(_filteredAds[index], index),
                ),
              ),
            );
          },
          childCount: _filteredAds.length,
        ),
      ),
    );
  }

  /// Build loading section
  Widget _buildLoadingSection() {
    return SliverPadding(
      padding: const EdgeInsets.all(16),
      sliver: SliverList(
        delegate: SliverChildBuilderDelegate(
          (context, index) => _buildLoadingCard(),
          childCount: 5,
        ),
      ),
    );
  }

  /// Build loading card with shimmer effect
  Widget _buildLoadingCard() {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Shimmer.fromColors(
        baseColor: Colors.grey[300]!,
        highlightColor: Colors.grey[100]!,
        child: Card(
          child: Container(
            height: 200,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(_borderRadius),
            ),
          ),
        ),
      ),
    );
  }

  /// Build empty section
  Widget _buildEmptySection() {
    return SliverFillRemaining(
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 80,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد إعلانات تبرع',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    color: Colors.grey[600],
                  ),
            ),
            const SizedBox(height: 8),
            Text(
              'جرب تغيير معايير البحث أو الفلترة',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[500],
                  ),
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () {
                _searchController.clear();
                setState(() {
                  _selectedCategoryIndex = 0;
                  _selectedFilter = 'all';
                  _showFeaturedOnly = false;
                });
                _fetchAds();
              },
              icon: const Icon(Icons.refresh),
              label: const Text('إعادة تحميل'),
            ),
          ],
        ),
      ),
    );
  }

  /// Build enhanced ad card
  Widget _buildAdCard(DonationAd ad, int index) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Card(
        elevation: _cardElevation,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(_borderRadius),
        ),
        child: InkWell(
          borderRadius: BorderRadius.circular(_borderRadius),
          onTap: () => _showAdDetails(ad),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildAdImage(ad),
              _buildAdContent(ad),
              _buildAdActions(ad),
            ],
          ),
        ),
      ),
    );
  }

  /// Build ad image section
  Widget _buildAdImage(DonationAd ad) {
    return Stack(
      children: [
        ClipRRect(
          borderRadius: const BorderRadius.vertical(
            top: Radius.circular(_borderRadius),
          ),
          child: AspectRatio(
            aspectRatio: 16 / 9,
            child: CachedNetworkImage(
              imageUrl: ad.firstImageUrl,
              fit: BoxFit.cover,
              placeholder: (context, url) => Shimmer.fromColors(
                baseColor: Colors.grey[300]!,
                highlightColor: Colors.grey[100]!,
                child: Container(color: Colors.white),
              ),
              errorWidget: (context, url, error) => Container(
                color: Colors.grey[300],
                child: const Icon(Icons.image_not_supported, size: 50),
              ),
            ),
          ),
        ),
        _buildAdBadges(ad),
        _buildFavoriteButton(ad),
      ],
    );
  }

  /// Build ad badges (featured, urgent, etc.)
  Widget _buildAdBadges(DonationAd ad) {
    return Positioned(
      top: 8,
      left: 8,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (ad.isFeatured)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: AppColors.warning,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(Icons.star, color: Colors.white, size: 12),
                  const SizedBox(width: 4),
                  Text(
                    'مميز',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                ],
              ),
            ),
          if (ad.isUrgent)
            Container(
              margin: const EdgeInsets.only(top: 4),
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: AppColors.error,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(Icons.warning, color: Colors.white, size: 12),
                  const SizedBox(width: 4),
                  Text(
                    'عاجل',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  /// Build favorite button
  Widget _buildFavoriteButton(DonationAd ad) {
    return Positioned(
      top: 8,
      right: 8,
      child: GestureDetector(
        onTap: () => _toggleFavorite(ad),
        child: AnimatedScale(
          scale: ad.isFavorite ? 1.2 : 1.0,
          duration: _animationDuration,
          child: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.9),
              shape: BoxShape.circle,
            ),
            child: Icon(
              ad.isFavorite ? Icons.favorite : Icons.favorite_border,
              color: ad.isFavorite ? AppColors.error : Colors.grey,
              size: 20,
            ),
          ),
        ),
      ),
    );
  }

  /// Build ad content section
  Widget _buildAdContent(DonationAd ad) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildAdHeader(ad),
          const SizedBox(height: 8),
          _buildAdDescription(ad),
          const SizedBox(height: 12),
          _buildAdProgress(ad),
          const SizedBox(height: 8),
          _buildAdMetadata(ad),
        ],
      ),
    );
  }

  /// Build ad header with title and category
  Widget _buildAdHeader(DonationAd ad) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: Text(
            ad.title,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ),
        const SizedBox(width: 8),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: _getCategoryColor(ad.category).withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Text(
            _getCategoryTitle(ad.category),
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: _getCategoryColor(ad.category),
                  fontWeight: FontWeight.w500,
                ),
          ),
        ),
      ],
    );
  }

  /// Build ad description
  Widget _buildAdDescription(DonationAd ad) {
    return Text(
      ad.description,
      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: Colors.grey[600],
          ),
      maxLines: 3,
      overflow: TextOverflow.ellipsis,
    );
  }

  /// Build ad progress section
  Widget _buildAdProgress(DonationAd ad) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'تم جمع: ${NumberFormat('#,###').format(ad.raisedAmount)} ريال',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
            ),
            Text(
              'الهدف: ${NumberFormat('#,###').format(ad.targetAmount)} ريال',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey[600],
                  ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        LinearProgressIndicator(
          value: ad.progressPercentage,
          backgroundColor: Colors.grey[300],
          valueColor: AlwaysStoppedAnimation<Color>(
            ad.isNearlyComplete ? AppColors.success : AppColors.primary,
          ),
        ),
        const SizedBox(height: 4),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              '${(ad.progressPercentage * 100).toStringAsFixed(1)}%',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: ad.isNearlyComplete
                        ? AppColors.success
                        : AppColors.primary,
                  ),
            ),
            Text(
              'متبقي: ${NumberFormat('#,###').format(ad.remainingAmount)} ريال',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey[600],
                  ),
            ),
          ],
        ),
      ],
    );
  }

  /// Build ad metadata (location, date, donors)
  Widget _buildAdMetadata(DonationAd ad) {
    return Row(
      children: [
        Icon(Icons.location_on, size: 16, color: Colors.grey[600]),
        const SizedBox(width: 4),
        Expanded(
          child: Text(
            ad.location,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                ),
            overflow: TextOverflow.ellipsis,
          ),
        ),
        const SizedBox(width: 16),
        Icon(Icons.people, size: 16, color: Colors.grey[600]),
        const SizedBox(width: 4),
        Text(
          '${ad.donorCount} متبرع',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
              ),
        ),
        const SizedBox(width: 16),
        // تحميل مكون RatingSummary بشكل كسول
        if (ad.rating != null && (ad.ratingCount ?? 0) > 0) ...[
          GestureDetector(
            onTap: () => _showRatingsScreen(ad),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.star, size: 16, color: Colors.amber),
                const SizedBox(width: 2),
                Text(
                  ad.rating!.toStringAsFixed(1),
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.amber[700],
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(width: 2),
                Text(
                  '(${ad.ratingCount})',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(width: 16),
        ] else const SizedBox.shrink(),
        const SizedBox(width: 16),
        Icon(Icons.access_time, size: 16, color: Colors.grey[600]),
        const SizedBox(width: 4),
        Text(
          _formatDate(ad.date),
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
              ),
        ),
      ],
    );
  }

  /// Build ad actions section
  Widget _buildAdActions(DonationAd ad) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: const BorderRadius.vertical(
          bottom: Radius.circular(_borderRadius),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: ElevatedButton.icon(
              onPressed: () => _showDonationDialog(ad),
              icon: const Icon(Icons.volunteer_activism, size: 18),
              label: const Text('تبرع الآن'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
              ),
            ),
          ),
          const SizedBox(width: 12),
          IconButton(
            onPressed: () => _showRatingsScreen(ad),
            icon: const Icon(Icons.star, color: Colors.amber),
            tooltip: 'التقييمات',
          ),
          IconButton(
            onPressed: _navigateToMapScreen,
            icon: const Icon(Icons.map, color: Colors.blue),
            tooltip: 'عرض الخريطة',
          ),
          IconButton(
            onPressed: () => _shareAd(ad),
            icon: const Icon(Icons.share),
            tooltip: 'مشاركة',
          ),
          IconButton(
            onPressed: () => _showAdDetails(ad),
            icon: const Icon(Icons.info_outline),
            tooltip: 'التفاصيل',
          ),
        ],
      ),
    );
  }

  /// Build loading more section
  Widget _buildLoadingMoreSection() {
    if (!_isLoadingMore) {
      return const SliverToBoxAdapter(child: SizedBox.shrink());
    }

    return SliverToBoxAdapter(
      child: Container(
        padding: const EdgeInsets.all(16),
        child: const Center(
          child: CircularProgressIndicator(),
        ),
      ),
    );
  }

  /// Build floating elements (FAB, scroll to top)
  Widget _buildFloatingElements() {
    return Stack(
      children: [
        // Scroll to top button
        Positioned(
          right: 20,
          bottom: 20,
          child: AnimatedOpacity(
            opacity: _showScrollToTop ? 1.0 : 0.0,
            duration: const Duration(milliseconds: 300),
            child: FloatingActionButton(
              heroTag: 'scroll_to_top',
              onPressed: _scrollToTop,
              backgroundColor: Theme.of(context).primaryColor,
              child: const Icon(Icons.arrow_upward, color: Colors.white),
              elevation: 4.0,
            ),
          ),
        ),
        // Donation features button
        Positioned(
          right: 20,
          bottom: 160,
          child: FloatingActionButton.extended(
            heroTag: 'donation_features',
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const DonationFeaturesScreen(),
                ),
              );
            },
            backgroundColor: Colors.green,
            icon: const Icon(Icons.volunteer_activism, color: Colors.white),
            label: const Text('ميزات التبرع', style: TextStyle(color: Colors.white)),
            elevation: 4.0,
          ),
        ),
        // Add new ad button
        Positioned(
          right: 20,
          bottom: 90,
          child: FloatingActionButton.extended(
            heroTag: 'add_ad',
            onPressed: _showCreateAdDialog,
            backgroundColor: Theme.of(context).primaryColor,
            icon: const Icon(Icons.add, color: Colors.white),
            label: const Text('إعلان جديد', style: TextStyle(color: Colors.white)),
            elevation: 4.0,
          ),
        ),
      ],
    );
  }

  /// Build confetti widget
  Widget _buildConfetti() {
    return Align(
      alignment: Alignment.topCenter,
      child: ConfettiWidget(
        confettiController: _confettiController,
        blastDirectionality: BlastDirectionality.explosive,
        shouldLoop: false,
        colors: const [
          AppColors.primary,
          AppColors.accent,
          AppColors.success,
          AppColors.warning,
        ],
      ),
    );
  }

  /// Build offline widget
  Widget _buildOfflineWidget() {
    return Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.wifi_off,
              size: 80,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'لا يوجد اتصال بالإنترنت',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    color: Colors.grey[600],
                  ),
            ),
            const SizedBox(height: 8),
            Text(
              'يرجى التحقق من اتصالك بالإنترنت والمحاولة مرة أخرى',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[500],
                  ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _refreshData,
              icon: const Icon(Icons.refresh),
              label: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      ),
    );
  }

  // ===============================
  // Dialog and Bottom Sheet Methods
  // ===============================

  /// Show notifications bottom sheet
  void _showNotificationsBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius:
            BorderRadius.vertical(top: Radius.circular(_borderRadius)),
      ),
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        maxChildSize: 0.9,
        minChildSize: 0.5,
        builder: (context, scrollController) => Column(
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor,
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(_borderRadius),
                ),
              ),
              child: Row(
                children: [
                  const Icon(Icons.notifications, color: Colors.white),
                  const SizedBox(width: 8),
                  const Expanded(
                    child: Text(
                      'الإشعارات',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  TextButton(
                    onPressed: _markAllNotificationsAsRead,
                    child: const Text(
                      'قراءة الكل',
                      style: TextStyle(color: Colors.white),
                    ),
                  ),
                ],
              ),
            ),
            Expanded(
              child: _notifications.isEmpty
                  ? const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.notifications_none,
                              size: 64, color: Colors.grey),
                          SizedBox(height: 16),
                          Text('لا توجد إشعارات'),
                        ],
                      ),
                    )
                  : ListView.builder(
                      controller: scrollController,
                      itemCount: _notifications.length,
                      itemBuilder: (context, index) {
                        final notification = _notifications[index];
                        return _buildNotificationItem(notification);
                      },
                    ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build notification item
  Widget _buildNotificationItem(AppNotification notification) {
    return ListTile(
      leading: CircleAvatar(
        backgroundColor: notification.color.withOpacity(0.1),
        child: Icon(notification.icon, color: notification.color),
      ),
      title: Text(
        notification.title,
        style: TextStyle(
          fontWeight: notification.isRead ? FontWeight.normal : FontWeight.bold,
        ),
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(notification.message),
          const SizedBox(height: 4),
          Text(
            _formatDate(notification.createdAt),
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                ),
          ),
        ],
      ),
      trailing: notification.isRead
          ? null
          : Container(
              width: 8,
              height: 8,
              decoration: const BoxDecoration(
                color: AppColors.primary,
                shape: BoxShape.circle,
              ),
            ),
      onTap: () => _handleNotificationTap(notification),
    );
  }

  /// Handle notification tap
  void _handleNotificationTap(AppNotification notification) {
    if (!notification.isRead) {
      _adService.markNotificationAsRead(notification.id);
      setState(() {
        final index = _notifications.indexOf(notification);
        if (index != -1) {
          _notifications[index] = AppNotification(
            id: notification.id,
            title: notification.title,
            message: notification.message,
            type: notification.type,
            createdAt: notification.createdAt,
            isRead: true,
            data: notification.data,
            actionUrl: notification.actionUrl,
            imageUrl: notification.imageUrl,
          );
        }
      });
    }

    Navigator.pop(context);

    // Handle notification action
    if (notification.actionUrl != null) {
      // Navigate to specific screen based on action URL
    }
  }

  /// Mark all notifications as read
  void _markAllNotificationsAsRead() {
    for (final notification in _notifications) {
      if (!notification.isRead) {
        _adService.markNotificationAsRead(notification.id);
      }
    }

    setState(() {
      _notifications = _notifications
          .map((n) => AppNotification(
                id: n.id,
                title: n.title,
                message: n.message,
                type: n.type,
                createdAt: n.createdAt,
                isRead: true,
                data: n.data,
                actionUrl: n.actionUrl,
                imageUrl: n.imageUrl,
              ))
          .toList();
    });
  }

  /// Show advanced filters bottom sheet
  void _showAdvancedFiltersBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius:
            BorderRadius.vertical(top: Radius.circular(_borderRadius)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'فلاتر متقدمة',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 16),
            // Add advanced filter options here
            const Text('قريباً: فلاتر متقدمة للبحث والتصفية'),
            const SizedBox(height: 24),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => Navigator.pop(context),
                    child: const Text('إلغاء'),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      Navigator.pop(context);
                      _fetchAds();
                    },
                    child: const Text('تطبيق'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Show donation dialog
  void _showDonationDialog(DonationAd ad) {
    final amountController = TextEditingController();
    final messageController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('التبرع لـ ${ad.title}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: amountController,
              keyboardType: TextInputType.number,
              decoration: const InputDecoration(
                labelText: 'مبلغ التبرع (ريال)',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.attach_money),
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: messageController,
              maxLines: 3,
              decoration: const InputDecoration(
                labelText: 'رسالة (اختيارية)',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.message),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              final amount = double.tryParse(amountController.text);
              if (amount != null && amount > 0) {
                try {
                  await _adService.donate(
                    adId: ad.id,
                    donorId: _currentUserId,
                    amount: amount,
                    message: messageController.text.trim().isEmpty
                        ? null
                        : messageController.text.trim(),
                  );

                  Navigator.pop(context);
                  _showSuccessSnackBar('تم التبرع بنجاح!');
                  _confettiController.play();
                  _fetchAds();
                } catch (e) {
                  _showErrorSnackBar('فشل في التبرع: $e');
                }
              } else {
                _showErrorSnackBar('يرجى إدخال مبلغ صحيح');
              }
            },
            child: const Text('تبرع'),
          ),
        ],
      ),
    );
  }

  /// Convert DonationAd to DonationAdWithLocation
  DonationAdWithLocation _convertToAdWithLocation(DonationAd ad) {
    // Try to parse location from ad.location string or use latitude/longitude if available
    LatLng? location;
    if (ad.latitude != null && ad.longitude != null) {
      location = LatLng(ad.latitude!, ad.longitude!);
    } else if (ad.location.isNotEmpty) {
      try {
        final parts = ad.location.split(',');
        if (parts.length == 2) {
          final lat = double.tryParse(parts[0].trim());
          final lng = double.tryParse(parts[1].trim());
          if (lat != null && lng != null) {
            location = LatLng(lat, lng);
          }
        }
      } catch (e) {
        debugPrint('Error parsing location: $e');
      }
    }

    return DonationAdWithLocation(
      id: ad.id,
      title: ad.title,
      description: ad.description,
      category: ad.category,
      targetAmount: ad.targetAmount,
      raisedAmount: ad.raisedAmount,
      imageUrl: ad.firstImageUrl,
      location: location != null
          ? LocationData(
              latitude: location.latitude,
              longitude: location.longitude,
              address: ad.location,
            )
          : null,
      createdAt: ad.date,
      isUrgent: ad.isUrgent,
      isFeatured: ad.isFeatured,
      averageRating: 0.0, // You might want to set this from ratings
      ratingCount: 0,     // You might want to set this from ratings
    );
  }


  /// Show ad details dialog with map view
  void _showAdDetails(DonationAd ad) {
    final adWithLocation = _convertToAdWithLocation(ad);
    int _currentTabIndex = 0;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) {
          return Dialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(_borderRadius),
            ),
            child: Container(
              constraints: const BoxConstraints(maxHeight: 700, maxWidth: 500),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Tabs
                  Container(
                    decoration: BoxDecoration(
                      border: Border(
                        bottom: BorderSide(
                          color: Theme.of(context).dividerColor,
                          width: 1,
                        ),
                      ),
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          child: InkWell(
                            onTap: () => setState(() => _currentTabIndex = 0),
                            child: Container(
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              decoration: BoxDecoration(
                                border: Border(
                                  bottom: BorderSide(
                                    color: _currentTabIndex == 0
                                        ? Theme.of(context).primaryColor
                                        : Colors.transparent,
                                    width: 2,
                                  ),
                                ),
                              ),
                              child: Text(
                                'التفاصيل',
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  fontWeight: _currentTabIndex == 0
                                      ? FontWeight.bold
                                      : FontWeight.normal,
                                  color: _currentTabIndex == 0
                                      ? Theme.of(context).primaryColor
                                      : null,
                                ),
                              ),
                            ),
                          ),
                        ),
                        Expanded(
                          child: InkWell(
                            onTap: () => setState(() => _currentTabIndex = 1),
                            child: Container(
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              decoration: BoxDecoration(
                                border: Border(
                                  bottom: BorderSide(
                                    color: _currentTabIndex == 1
                                        ? Theme.of(context).primaryColor
                                        : Colors.transparent,
                                    width: 2,
                                  ),
                                ),
                              ),
                              child: Text(
                                'الموقع على الخريطة',
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  fontWeight: _currentTabIndex == 1
                                      ? FontWeight.bold
                                      : FontWeight.normal,
                                  color: _currentTabIndex == 1
                                      ? Theme.of(context).primaryColor
                                      : null,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Tab content
                  Expanded(
                    child: IndexedStack(
                      index: _currentTabIndex,
                      children: [
                        // Details Tab
                        SingleChildScrollView(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Header with image
                              ClipRRect(
                                borderRadius: const BorderRadius.vertical(
                                  top: Radius.circular(_borderRadius),
                                ),
                                child: AspectRatio(
                                  aspectRatio: 16 / 9,
                                  child: CachedNetworkImage(
                                    imageUrl: ad.firstImageUrl,
                                    fit: BoxFit.cover,
                                    placeholder: (context, url) => Shimmer.fromColors(
                                      baseColor: Colors.grey[300]!,
                                      highlightColor: Colors.grey[100]!,
                                      child: Container(color: Colors.white),
                                    ),
                                  ),
                                ),
                              ),
                              // Content
                              Padding(
                                padding: const EdgeInsets.all(16),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      ad.title,
                                      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    const SizedBox(height: 8),
                                    Text(
                                      ad.description,
                                      style: Theme.of(context).textTheme.bodyMedium,
                                    ),
                                    const SizedBox(height: 16),
                                    _buildAdProgress(ad),
                                    const SizedBox(height: 16),
                                    _buildRatingDisplay(ad),
                                    const SizedBox(height: 16),
                                    _buildDetailRow('الموقع', ad.location, Icons.location_on),
                                    _buildDetailRow('التاريخ', _formatDate(ad.date), Icons.calendar_today),
                                    _buildDetailRow('عدد المتبرعين', '${ad.donorCount}', Icons.people),
                                    if (ad.tags.isNotEmpty)
                                      _buildDetailRow('العلامات', ad.tags.join(', '), Icons.tag),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),

                        // Map Tab
                        adWithLocation.location != null
                            ? InteractiveMap(
                                ads: [adWithLocation],
                                initialCenter: LatLng(
                                  adWithLocation.location!.latitude,
                                  adWithLocation.location!.longitude,
                                ),
                                onAdSelected: (ad) {},
                                onLocationSelected: (location) {},
                              )
                            : const Center(
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(Icons.location_off, size: 48, color: Colors.grey),
                                    SizedBox(height: 16),
                                    Text('لا يتوفر موقع لهذه الحالة'),
                                  ],
                                ),
                              ),
                      ],
                    ),
                  ),

                  // Actions
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Theme.of(context).cardColor,
                      borderRadius: const BorderRadius.vertical(
                        bottom: Radius.circular(_borderRadius),
                      ),
                    ),
                    child: Column(
                      children: [
                        Row(
                          children: [
                            if (adWithLocation.location != null)
                              Expanded(
                                child: OutlinedButton.icon(
                                  onPressed: () {
                                    Navigator.push(
                                      context,
                                      MaterialPageRoute(
                                        builder: (context) => const MapsScreen(),
                                      ),
                                    );
                                  },
                                  icon: const Icon(Icons.map),
                                  label: const Text('عرض الخريطة الكاملة'),
                                ),
                              ),
                            if (adWithLocation.location != null) const SizedBox(width: 12),
                            Expanded(
                              child: OutlinedButton(
                                onPressed: () => Navigator.pop(context),
                                child: const Text('إغلاق'),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 12),
                        SizedBox(
                          width: double.infinity,
                          child: ElevatedButton(
                            onPressed: () {
                              Navigator.pop(context);
                              _showDonationDialog(ad);
                            },
                            child: const Text('تبرع الآن'),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  /// Build detail row for ad details
  Widget _buildDetailRow(String label, String value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(icon, size: 16, color: Colors.grey[600]),
          const SizedBox(width: 8),
          Text(
            '$label: ',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          Expanded(
            child: Text(
              value,
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ),
        ],
      ),
    );
  }

  // Form key and controllers for create ad form
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _locationController = TextEditingController();
  final _targetAmountController = TextEditingController();
  final _categoryController = TextEditingController();
  File? _selectedImage;
  bool _isFormLoading = false;
  String? _selectedCategoryName;
  final List<String> _categories = [
    'تعليم',
    'صحة',
    'إغاثة',
    'أيتام',
    'أسر محتاجة',
    'مساجد',
    'أخرى'
  ];

  /// Show create ad dialog
  Future<void> _showCreateAdDialog() async {
    final currentUser = Provider.of<AppState>(context, listen: false).currentUser;
    if (currentUser == null) {
      _showErrorSnackBar('يجب تسجيل الدخول أولاً');
      return;
    }

    // Reset form
    _titleController.clear();
    _descriptionController.clear();
    _locationController.clear();
    _targetAmountController.clear();
    _categoryController.clear();
    _selectedImage = null;
    _selectedCategoryIndex = 0;

    await showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => StatefulBuilder(
        builder: (context, setState) {
          return SingleChildScrollView(
            padding: EdgeInsets.only(
              bottom: MediaQuery.of(context).viewInsets.bottom,
              left: 16,
              right: 16,
              top: 16,
            ),
            child: Form(
              key: _formKey,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'إنشاء إعلان تبرع جديد',
                        style: Theme.of(context).textTheme.headlineSmall,
                      ),
                      IconButton(
                        icon: const Icon(Icons.close),
                        onPressed: () => Navigator.pop(context),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // Image Picker
                  GestureDetector(
                    onTap: _pickImage,
                    child: DottedBorder(
                      borderType: BorderType.RRect,
                      radius: const Radius.circular(12),
                      dashPattern: const [8, 4],
                      color: Colors.grey[400]!,
                      child: Container(
                        width: double.infinity,
                        height: 150,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(12),
                          color: Colors.grey[100],
                        ),
                        child: _selectedImage != null
                            ? ClipRRect(
                                borderRadius: BorderRadius.circular(12),
                                child: Image.file(
                                  _selectedImage!,
                                  fit: BoxFit.cover,
                                ),
                              )
                            : Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  const Icon(Icons.add_photo_alternate_outlined,
                                      size: 40, color: Colors.grey),
                                  const SizedBox(height: 8),
                                  Text(
                                    'إضافة صورة رئيسية',
                                    style: TextStyle(color: Colors.grey[600]),
                                  ),
                                ],
                              ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Title Field
                  TextFormField(
                    controller: _titleController,
                    decoration: const InputDecoration(
                      labelText: 'عنوان الإعلان',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.title),
                    ),
                    textInputAction: TextInputAction.next,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'الرجاء إدخال عنوان للإعلان';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),

                  // Description Field
                  TextFormField(
                    controller: _descriptionController,
                    decoration: const InputDecoration(
                      labelText: 'تفاصيل الإعلان',
                      border: OutlineInputBorder(),
                      alignLabelWithHint: true,
                    ),
                    maxLines: 3,
                    textInputAction: TextInputAction.newline,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'الرجاء إدخال تفاصيل الإعلان';
                      }
                      if (value.length < 30) {
                        return 'الرجاء إدخال وصف مفصل أكثر';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),

                  // Location Field
                  TextFormField(
                    controller: _locationController,
                    decoration: const InputDecoration(
                      labelText: 'الموقع',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.location_on),
                    ),
                    textInputAction: TextInputAction.next,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'الرجاء إدخال الموقع';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),

                  // Target Amount Field
                  TextFormField(
                    controller: _targetAmountController,
                    decoration: const InputDecoration(
                      labelText: 'المبلغ المستهدف',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.attach_money),
                      suffixText: 'ر.س',
                    ),
                    keyboardType: TextInputType.number,
                    textInputAction: TextInputAction.next,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'الرجاء إدخال المبلغ المستهدف';
                      }
                      if (double.tryParse(value) == null) {
                        return 'الرجاء إدخال رقم صحيح';
                      }
                      if (double.parse(value) <= 0) {
                        return 'يجب أن يكون المبلغ أكبر من صفر';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),

                  // Category Dropdown
                  DropdownButtonFormField<String>(
                    value: _categories[_selectedCategoryIndex],
                    decoration: const InputDecoration(
                      labelText: 'التصنيف',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.category),
                    ),
                    items: _categories.map((String category) {
                      return DropdownMenuItem<String>(
                        value: category,
                        child: Text(category),
                      );
                    }).toList(),
                    onChanged: (String? newValue) {
                      setState(() {
                        _selectedCategoryIndex = _categories.indexOf(newValue!);
                      });
                    },
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'الرجاء اختيار التصنيف';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 24),

                  // Submit Button
                  ElevatedButton(
                    onPressed: _isLoading
                        ? null
                        : () async {
                            if (_formKey.currentState!.validate() &&
                                _selectedImage != null) {
                              await _submitDonationAd(currentUser.id);
                            } else if (_selectedImage == null) {
                              _showErrorSnackBar('الرجاء إضافة صورة للإعلان');
                            }
                          },
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: _isLoading
                        ? const SizedBox(
                            width: 24,
                            height: 24,
                            child: CircularProgressIndicator(
                              color: Colors.white,
                              strokeWidth: 2,
                            ),
                          )
                        : const Text(
                            'نشر الإعلان',
                            style: TextStyle(fontSize: 16),
                          ),
                  ),
                  const SizedBox(height: 16),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  /// Pick image from device
  Future<void> _pickImage() async {
    final pickedFile = await FilePicker.platform.pickFiles(
      type: FileType.image,
      allowMultiple: false,
    );

    if (pickedFile != null) {
      setState(() {
        _selectedImage = File(pickedFile.files.single.path!);
      });
    }
  }

  /// Submit donation ad to the server
  Future<void> _submitDonationAd(String userId) async {
    if (_formKey.currentState!.validate() && _selectedImage != null) {
      setState(() => _isLoading = true);

      try {
        // Create form data
        final formData = FormData.fromMap({
          'title': _titleController.text,
          'description': _descriptionController.text,
          'location': _locationController.text,
          'targetAmount': _targetAmountController.text,
          'category': _selectedCategoryIndex,
          'creatorId': userId,
          'file': await MultipartFile.fromFile(
            _selectedImage!.path,
            filename: 'donation_${DateTime.now().millisecondsSinceEpoch}.jpg',
          ),
        });

        // Call API to create donation ad
        final response = await Dio().post(
          'YOUR_API_ENDPOINT/donations',
          data: formData,
          options: Options(
            headers: {
              'Content-Type': 'multipart/form-data',
              'Authorization': 'Bearer YOUR_AUTH_TOKEN',
            },
          ),
        );

        if (response.statusCode == 201) {
          // Success
          if (mounted) {
            Navigator.pop(context);
            _showSuccessSnackBar('تم نشر إعلان التبرع بنجاح');
            _refreshData(); // Refresh the list
          }
        } else {
          // Handle API error
          if (mounted) {
            _showErrorSnackBar('حدث خطأ أثناء محاولة نشر الإعلان');
          }
        }
      } catch (e) {
        if (mounted) {
          _showErrorSnackBar('حدث خطأ في الاتصال بالخادم');
        }
      } finally {
        if (mounted) {
          setState(() => _isFormLoading = false);
        }
      }
    }
  }

  /// Navigate to full map screen
  void _navigateToMapScreen() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const MapsScreen(),
      ),
    );
  }

  /// Show donor history for a specific donation ad
  void _showDonorHistory(DonationAd ad) async {
    try {
      // Show loading indicator
      if (!mounted) return;
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(
          child: CircularProgressIndicator(),
        ),
      );

      // Fetch donation history for the ad
      final donationHistory = await _adService.fetchDonationHistory(ad.id);
      
      // Close loading dialog
      if (!mounted) return;
      Navigator.pop(context);

      // Show donor history in a dialog
      if (!mounted) return;
      showDialog(
        context: context,
        builder: (context) {
          return AlertDialog(
            title: const Text('سجل المتبرعين'),
            content: SizedBox(
              width: double.maxFinite,
              child: donationHistory.isEmpty
                  ? const Text('لا توجد تبرعات حتى الآن')
                  : ListView.builder(
                      shrinkWrap: true,
                      itemCount: donationHistory.length,
                      itemBuilder: (context, index) {
                        final donation = donationHistory[index];
                        return ListTile(
                          leading: CircleAvatar(
                            backgroundColor: Theme.of(context).primaryColor,
                            child: Text(
                              '${index + 1}',
                              style: const TextStyle(color: Colors.white),
                            ),
                          ),
                          title: Text(
                            '${donation['donorName'] ?? 'متبرع مجهول'}',
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                          subtitle: Text(
                            '${donation['amount']} ر.س',
                            style: TextStyle(
                              color: Theme.of(context).primaryColor,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          trailing: Text(
                            _formatDate(DateTime.parse(donation['createdAt'])),
                            style: const TextStyle(fontSize: 12),
                          ),
                        );
                      },
                    ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إغلاق'),
              ),
            ],
          );
        },
      );
    } catch (e) {
      if (mounted) {
        Navigator.pop(context); // Close loading dialog if still open
        _showErrorSnackBar('حدث خطأ أثناء جلب سجل التبرعات');
      }
    }
  }

  /// Show favorite ads
  void _showFavoriteAds() {
    final favoriteAds = _donationAds.where((ad) => ad.isFavorite).toList();

    if (favoriteAds.isEmpty) {
      _showErrorSnackBar('لا توجد إعلانات مفضلة');
      return;
    }

    // Show favorite ads in a dialog or navigate to a new screen
    _showSuccessSnackBar('عرض ${favoriteAds.length} إعلان مفضل');
  }

  /// Show settings dialog
  void _showSettingsDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('الإعدادات', textAlign: TextAlign.center, style: TextStyle(fontWeight: FontWeight.bold)),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildSettingsDivider('إعدادات التطبيق'),
              _buildNotificationSettingsTile(),
              _buildPrivacySettingsTile(),
              _buildLanguageSettingsTile(),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  /// Build a divider with text for settings sections
  Widget _buildSettingsDivider(String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        children: [
          const Expanded(child: Divider()),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8.0),
            child: Text(
              text,
              style: const TextStyle(
                fontSize: 12,
                color: Colors.grey,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          const Expanded(child: Divider()),
        ],
      ),
    );
  }

  /// Build notification settings tile
  Widget _buildNotificationSettingsTile() {
    return StatefulBuilder(
      builder: (context, setState) {
        return SwitchListTile(
          title: const Text('تفعيل الإشعارات'),
          subtitle: const Text('استقبال الإشعارات والتحديثات'),
          secondary: const Icon(Icons.notifications_active),
          value: _notificationEnabled,
          onChanged: (bool value) {
            setState(() {
              _notificationEnabled = value;
              // Save notification preference
              _saveNotificationPreference(value);
            });
            _showSuccessSnackBar(
              value ? 'تم تفعيل الإشعارات' : 'تم إيقاف الإشعارات',
            );
          },
        );
      },
    );
  }

  /// Build privacy settings tile
  Widget _buildPrivacySettingsTile() {
    return StatefulBuilder(
      builder: (context, setState) {
        return SwitchListTile(
          title: const Text('إظهار سجل التبرعات'),
          subtitle: const Text('إظهار تبرعاتك للآخرين'),
          secondary: const Icon(Icons.visibility),
          value: _donationHistoryVisible,
          onChanged: (bool value) {
            setState(() {
              _donationHistoryVisible = value;
              // Save privacy preference
              _savePrivacyPreference(value);
            });
            _showSuccessSnackBar(
              value ? 'سجل التبرعات ظاهر للآخرين' : 'تم إخفاء سجل التبرعات',
            );
          },
        );
      },
    );
  }

  /// Build language settings tile
  Widget _buildLanguageSettingsTile() {
    return ListTile(
      leading: const Icon(Icons.language),
      title: const Text('تغيير اللغة'),
      subtitle: const Text('اختر لغة التطبيق المفضلة'),
      onTap: () {
        Navigator.pop(context);
        _showLanguageSelectionDialog();
      },
    );
  }

  /// Show language selection dialog
  void _showLanguageSelectionDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختر اللغة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              title: const Text('العربية'),
              leading: const Icon(Icons.language),
              onTap: () => _changeLanguage('ar'),
            ),
            const Divider(),
            ListTile(
              title: const Text('English'),
              leading: const Icon(Icons.language),
              onTap: () => _changeLanguage('en'),
            ),
          ],
        ),
      ),
    );
  }

  /// Change app language
  void _changeLanguage(String languageCode) async {
    // Save language preference
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('language', languageCode);
    
    // Update app language
    if (mounted) {
      // You'll need to implement your language change logic here
      // For example, using EasyLocalization:
      // context.setLocale(Locale(languageCode));
      _showSuccessSnackBar('تم تغيير اللغة بنجاح');
      Navigator.pop(context);
    }
  }

  /// Save notification preference
  void _saveNotificationPreference(bool enabled) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('notifications_enabled', enabled);
  }

  /// Save privacy preference
  void _savePrivacyPreference(bool visible) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('donation_history_visible', visible);
    
    // Update privacy setting on server if user is logged in
    final currentUser = Provider.of<AppState>(context, listen: false).currentUser;
    if (currentUser != null) {
      try {
        await _adService.updateDonorHistoryVisibility(
          currentUser.id,
          visible,
        );
      } catch (e) {
        if (mounted) {
          _showErrorSnackBar('حدث خطأ في تحديث إعدادات الخصوصية');
        }
      }
    }
  }

  /// Show about dialog
  void _showAboutDialog() {
    showAboutDialog(
      context: context,
      applicationName: 'منصة التبرعات المحسنة',
      applicationVersion: '2.0.0',
      applicationIcon: const Icon(Icons.volunteer_activism, size: 48),
      children: [
        const Text(
            'منصة شاملة للتبرعات الخيرية مع ميزات متقدمة وتجربة مستخدم محسنة.'),
        const SizedBox(height: 16),
        const Text('تم تطويرها بتقنية Flutter مع دعم كامل للغة العربية.'),
      ],
    );
  }

  // ===============================
  // Utility Methods
  // ===============================

  /// Toggle favorite status for an ad
  void _toggleFavorite(DonationAd ad) {
    setState(() {
      ad.isFavorite = !ad.isFavorite;
    });

    HapticFeedback.lightImpact();

    final message = ad.isFavorite
        ? 'تم إضافة الإعلان للمفضلة'
        : 'تم إزالة الإعلان من المفضلة';

    _showSuccessSnackBar(message);
  }

  /// Share ad
  void _shareAd(DonationAd ad) {
    final shareText = '''
${ad.title}

${ad.description}

الموقع: ${ad.location}
تم جمع: ${NumberFormat('#,###').format(ad.raisedAmount)} ريال من أصل ${NumberFormat('#,###').format(ad.targetAmount)} ريال

شارك في التبرع وكن جزءاً من التغيير الإيجابي!
''';

    Share.share(shareText);
    HapticFeedback.selectionClick();
  }

  /// Scroll to top
  void _scrollToTop() {
    _scrollController.animateTo(
      0,
      duration: _slowAnimationDuration,
      curve: Curves.easeInOut,
    );
    HapticFeedback.lightImpact();
  }

  /// Get category color
  Color _getCategoryColor(String category) {
    final categoryData = categories.firstWhere(
      (cat) => cat['filter'] == category,
      orElse: () => categories.last,
    );
    return categoryData['color'] as Color;
  }

  /// Get category title
  String _getCategoryTitle(String category) {
    final categoryData = categories.firstWhere(
      (cat) => cat['filter'] == category,
      orElse: () => categories.last,
    );
    return categoryData['title'] as String;
  }

  /// Format date for display
  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'اليوم';
    } else if (difference.inDays == 1) {
      return 'أمس';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} أيام';
    } else if (difference.inDays < 30) {
      return 'منذ ${(difference.inDays / 7).floor()} أسابيع';
    } else {
      return DateFormat('dd/MM/yyyy').format(date);
    }
  }

  /// Show ratings screen for the ad
  void _showRatingsScreen(DonationAd ad) {
    // Get current user ID from app state or provider
    final appState = Provider.of<AppState>(context, listen: false);
    final currentUser = appState.currentUser;

    if (currentUser == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يجب تسجيل الدخول لعرض التقييمات')),
      );
      return;
    }

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => Scaffold(
          appBar: AppBar(
            title: Text('تقييمات ${ad.title}'),
            centerTitle: true,
          ),
          body: RatingsScreen(
            adId: ad.id,
            adTitle: ad.title,
            currentUserId: currentUser.id,
          ),
        ),
      ),
    );
  }

  /// Build rating display for ad details
  Widget _buildRatingDisplay(DonationAd ad) {
    final rating = ad.rating ?? 0.0;
    final ratingCount = ad.ratingCount ?? 0;

    return GestureDetector(
      onTap: () => _showRatingsScreen(ad),
      child: Row(
        children: [
          // Simple rating display
          Row(
            children: [
              const Icon(Icons.star, color: Colors.amber, size: 20),
              const SizedBox(width: 4),
              Text(
                rating.toStringAsFixed(1),
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(width: 8),
              Text(
                '($ratingCount تقييم${ratingCount != 1 ? 'ات' : ''})',
                style: Theme.of(context).textTheme.bodySmall,
              ),
            ],
          ),
          const SizedBox(width: 8),
          IconButton(
            onPressed: () => _showRatingsScreen(ad),
            icon: const Icon(Icons.arrow_forward_ios, size: 16),
            tooltip: 'عرض جميع التقييمات',
          ),
        ],
      ),
    );
  }
}
