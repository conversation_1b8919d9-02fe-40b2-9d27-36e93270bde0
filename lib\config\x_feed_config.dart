// تكوين X Feed للتكامل مع الباك إند

class XFeedConfig {
  // URLs الأساسية
  static const String defaultBackendUrl = 'http://localhost:3000';
  static const String defaultSocketUrl = 'http://localhost:3000';
  
  // إعدادات API
  static const int defaultPageSize = 10;
  static const int maxRetries = 3;
  static const Duration requestTimeout = Duration(seconds: 30);
  static const Duration socketTimeout = Duration(seconds: 10);
  
  // إعدادات التخزين المؤقت
  static const Duration cacheExpiry = Duration(minutes: 5);
  static const int maxCachedPosts = 100;
  
  // إعدادات الملفات
  static const int maxFileSize = 10 * 1024 * 1024; // 10 MB
  static const List<String> allowedImageTypes = [
    'image/jpeg',
    'image/png',
    'image/gif',
    'image/webp'
  ];
  static const List<String> allowedVideoTypes = [
    'video/mp4',
    'video/mov',
    'video/avi'
  ];
  
  // إعدادات المحتوى
  static const int maxPostLength = 1000;
  static const int maxCommentLength = 500;
  static const int maxHashtags = 10;
  
  // إعدادات Socket.IO
  static const Map<String, dynamic> socketOptions = {
    'transports': ['websocket'],
    'autoConnect': false,
    'timeout': 10000,
    'forceNew': true,
  };
  
  // رسائل الأخطاء
  static const Map<String, String> errorMessages = {
    'network_error': 'خطأ في الاتصال بالشبكة',
    'server_error': 'خطأ في الخادم',
    'auth_error': 'خطأ في المصادقة',
    'validation_error': 'بيانات غير صحيحة',
    'file_too_large': 'حجم الملف كبير جداً',
    'unsupported_file': 'نوع الملف غير مدعوم',
    'post_too_long': 'المنشور طويل جداً',
    'comment_too_long': 'التعليق طويل جداً',
    'no_internet': 'لا يوجد اتصال بالإنترنت',
    'socket_disconnected': 'انقطع الاتصال المباشر',
  };
  
  // إعدادات الواجهة
  static const Map<String, dynamic> uiSettings = {
    'animation_duration': 300,
    'refresh_threshold': 0.8,
    'auto_refresh_interval': 30000, // 30 seconds
    'max_visible_posts': 50,
    'scroll_threshold': 200,
  };
  
  // أنواع التفاعلات
  static const Map<String, String> reactionTypes = {
    'like': 'إعجاب',
    'love': 'حب',
    'haha': 'ضحك',
    'angry': 'غضب',
    'sad': 'حزن',
    'wow': 'تعجب',
  };
  
  // أولويات المنشورات
  static const Map<String, int> postPriorities = {
    'promoted': 100,
    'pinned': 90,
    'trending': 80,
    'recent': 70,
    'normal': 50,
  };
  
  // إعدادات الإشعارات
  static const Map<String, bool> defaultNotificationSettings = {
    'new_post': true,
    'post_liked': true,
    'post_commented': true,
    'mentioned': true,
    'followed': true,
    'trending': false,
  };
  
  // دوال مساعدة للتحقق من صحة البيانات
  static bool isValidPostContent(String content) {
    return content.trim().isNotEmpty && content.length <= maxPostLength;
  }
  
  static bool isValidCommentContent(String content) {
    return content.trim().isNotEmpty && content.length <= maxCommentLength;
  }
  
  static bool isValidFileSize(int fileSize) {
    return fileSize <= maxFileSize;
  }
  
  static bool isValidImageType(String mimeType) {
    return allowedImageTypes.contains(mimeType.toLowerCase());
  }
  
  static bool isValidVideoType(String mimeType) {
    return allowedVideoTypes.contains(mimeType.toLowerCase());
  }
  
  static bool isValidMediaType(String mimeType) {
    return isValidImageType(mimeType) || isValidVideoType(mimeType);
  }
  
  // استخراج الوسوم من النص
  static List<String> extractHashtags(String content) {
    final regex = RegExp(r'#\w+');
    final matches = regex.allMatches(content);
    final hashtags = matches
        .map((match) => match.group(0)!.substring(1)) // إزالة #
        .take(maxHashtags)
        .toList();
    return hashtags;
  }
  
  // استخراج الإشارات من النص
  static List<String> extractMentions(String content) {
    final regex = RegExp(r'@\w+');
    final matches = regex.allMatches(content);
    final mentions = matches
        .map((match) => match.group(0)!.substring(1)) // إزالة @
        .toList();
    return mentions;
  }
  
  // تنسيق حجم الملف
  static String formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }
  
  // تنسيق الوقت النسبي
  static String formatRelativeTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);
    
    if (difference.inSeconds < 60) {
      return 'الآن';
    } else if (difference.inMinutes < 60) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inHours < 24) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} يوم';
    } else if (difference.inDays < 30) {
      final weeks = (difference.inDays / 7).floor();
      return 'منذ $weeks أسبوع';
    } else if (difference.inDays < 365) {
      final months = (difference.inDays / 30).floor();
      return 'منذ $months شهر';
    } else {
      final years = (difference.inDays / 365).floor();
      return 'منذ $years سنة';
    }
  }
  
  // تنسيق عدد التفاعلات
  static String formatInteractionCount(int count) {
    if (count < 1000) return count.toString();
    if (count < 1000000) return '${(count / 1000).toStringAsFixed(1)}K';
    return '${(count / 1000000).toStringAsFixed(1)}M';
  }
  
  // الحصول على رسالة خطأ مناسبة
  static String getErrorMessage(String errorType, [String? customMessage]) {
    return customMessage ?? errorMessages[errorType] ?? 'خطأ غير معروف';
  }
  
  // التحقق من حالة الشبكة
  static bool isNetworkError(dynamic error) {
    final errorString = error.toString().toLowerCase();
    return errorString.contains('network') ||
           errorString.contains('connection') ||
           errorString.contains('timeout') ||
           errorString.contains('unreachable');
  }
  
  // التحقق من خطأ المصادقة
  static bool isAuthError(dynamic error) {
    final errorString = error.toString().toLowerCase();
    return errorString.contains('401') ||
           errorString.contains('unauthorized') ||
           errorString.contains('authentication') ||
           errorString.contains('token');
  }
  
  // التحقق من خطأ الخادم
  static bool isServerError(dynamic error) {
    final errorString = error.toString().toLowerCase();
    return errorString.contains('500') ||
           errorString.contains('502') ||
           errorString.contains('503') ||
           errorString.contains('504') ||
           errorString.contains('server error');
  }
}
