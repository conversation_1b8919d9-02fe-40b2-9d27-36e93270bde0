import 'package:uuid/uuid.dart';

const Uuid _uuid = Uuid();

/// مستوى الطلب على المهنة
enum DemandLevel {
  high('مرتفع'),
  medium('متوسط'),
  low('منخفض');

  final String label;
  const DemandLevel(this.label);
  
  /// تحويل النص إلى DemandLevel
  static DemandLevel? fromString(String? value) {
    if (value == null) return null;
    try {
      return DemandLevel.values.firstWhere(
        (e) => e.name.toLowerCase() == value.toLowerCase() || 
              e.label == value,
      );
    } catch (e) {
      return null;
    }
  }
}

/// مستوى الصعوبة
enum DifficultyLevel {
  beginner('مبتدئ'),
  intermediate('متوسط'),
  advanced('متقدم'),
  expert('خبير');

  final String label;
  const DifficultyLevel(this.label);
  
  /// تحويل النص إلى DifficultyLevel
  static DifficultyLevel? fromString(String? value) {
    if (value == null) return null;
    try {
      return DifficultyLevel.values.firstWhere(
        (e) => e.name.toLowerCase() == value.toLowerCase() || 
              e.label == value,
      );
    } catch (e) {
      return null;
    }
  }
}

/// نموذج بيانات المهنة
class Profession {
  final String id;
  final String title;
  final String description;
  final String image;
  final bool isLocal;
  final int averageSalary;
  final DemandLevel demand;
  final DifficultyLevel difficulty;
  final int courseDuration; // المدة بالأشهر
  final List<String> requiredSkills;
  final String? category;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  Profession({
    String? id,
    required this.title,
    required this.description,
    this.image = '',
    this.isLocal = false,
    this.averageSalary = 0,
    DemandLevel? demand,
    DifficultyLevel? difficulty,
    this.courseDuration = 1,
    List<String>? requiredSkills,
    this.category,
    this.createdAt,
    this.updatedAt,
  }) : 
    id = id ?? _uuid.v4(),
    demand = demand ?? DemandLevel.medium,
    difficulty = difficulty ?? DifficultyLevel.beginner,
    requiredSkills = requiredSkills ?? [];

  /// إنشاء نموذج من JSON
  factory Profession.fromJson(Map<String, dynamic> json) {
    return Profession(
      id: json['_id'] ?? json['id'],
      title: json['title'] ?? 'بدون عنوان',
      description: json['description'] ?? 'لا يوجد وصف',
      image: json['image'] ?? '',
      isLocal: json['isLocal'] == true,
      averageSalary: json['averageSalary'] is int 
          ? json['averageSalary'] 
          : (json['averageSalary'] is num ? (json['averageSalary'] as num).toInt() : 0),
      demand: json['demand'] is DemandLevel 
          ? json['demand'] 
          : DemandLevel.fromString(json['demand']) ?? DemandLevel.medium,
      difficulty: json['difficulty'] is DifficultyLevel
          ? json['difficulty']
          : DifficultyLevel.fromString(json['difficulty']) ?? DifficultyLevel.beginner,
      courseDuration: json['courseDuration'] is int 
          ? json['courseDuration'] 
          : (json['courseDuration'] is num ? (json['courseDuration'] as num).toInt() : 1),
      requiredSkills: json['requiredSkills'] is List 
          ? List<String>.from(json['requiredSkills']) 
          : [],
      category: json['category'],
      createdAt: json['createdAt'] != null 
          ? DateTime.tryParse(json['createdAt']) 
          : null,
      updatedAt: json['updatedAt'] != null 
          ? DateTime.tryParse(json['updatedAt']) 
          : null,
    );
  }

  /// تحويل النموذج إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'image': image,
      'isLocal': isLocal,
      'averageSalary': averageSalary,
      'demand': demand.name,
      'difficulty': difficulty.name,
      'courseDuration': courseDuration,
      'requiredSkills': requiredSkills,
      if (category != null) 'category': category,
      if (createdAt != null) 'createdAt': createdAt!.toIso8601String(),
      if (updatedAt != null) 'updatedAt': updatedAt!.toIso8601String(),
    };
  }
  
  /// نسخ النموذج مع تحديث بعض الخصائص
  Profession copyWith({
    String? id,
    String? title,
    String? description,
    String? image,
    bool? isLocal,
    int? averageSalary,
    DemandLevel? demand,
    DifficultyLevel? difficulty,
    int? courseDuration,
    List<String>? requiredSkills,
    String? category,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Profession(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      image: image ?? this.image,
      isLocal: isLocal ?? this.isLocal,
      averageSalary: averageSalary ?? this.averageSalary,
      demand: demand ?? this.demand,
      difficulty: difficulty ?? this.difficulty,
      courseDuration: courseDuration ?? this.courseDuration,
      requiredSkills: requiredSkills ?? this.requiredSkills,
      category: category ?? this.category,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
    );
  }
  
  /// الحصول على وصف مختصر للمهنة
  String get summary {
    final salary = averageSalary > 0 
        ? '\n- الراتب المتوسط: $averageSalary' 
        : '';
    return '${difficulty.label} - ${demand.label} الطلب$salary';
  }
  
  /// التحقق مما إذا كانت المهارات المطلوبة متوفرة
  bool hasRequiredSkills(List<String> userSkills) {
    if (requiredSkills.isEmpty) return true;
    return requiredSkills.every((skill) => userSkills.contains(skill));
  }
  
  @override
  String toString() {
    return 'Profession(id: $id, title: $title, demand: ${demand.label})';
  }
  
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Profession &&
          runtimeType == other.runtimeType &&
          id == other.id &&
          title == other.title;

  @override
  int get hashCode => id.hashCode ^ title.hashCode;
}