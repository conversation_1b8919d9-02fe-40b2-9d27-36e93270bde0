import 'dart:async';
import 'dart:convert';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:confetti/confetti.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:http/http.dart' as http;
import 'package:provider/provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:shimmer/shimmer.dart';
import 'package:socket_io_client/socket_io_client.dart' as io;
import 'package:timeago/timeago.dart' as timeago;
import 'appstate.dart';
import 'models/hobby_content_model.dart';

// إعداد Timeago للغة العربية
void setupTimeago() => timeago.setLocaleMessages('ar', timeago.ArMessages());

// ==== Animation Widgets ==== //

class FadeInAnimation extends StatefulWidget {
  final double delay;
  final Widget child;

  const FadeInAnimation({
    super.key,
    required this.delay,
    required this.child,
  });

  @override
  _FadeInAnimationState createState() => _FadeInAnimationState();
}

class _FadeInAnimationState extends State<FadeInAnimation> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _animation = CurvedAnimation(parent: _controller, curve: Curves.easeIn);
    Future.delayed(Duration(milliseconds: (widget.delay * 1000).toInt()), () {
      if (mounted) _controller.forward();
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return FadeTransition(
      opacity: _animation,
      child: widget.child,
    );
  }
}

class ScaleTransitionButton extends StatefulWidget {
  final VoidCallback onPressed;
  final Widget child;
  final ButtonStyle? style;

  const ScaleTransitionButton({
    super.key,
    required this.onPressed,
    required this.child,
    this.style,
  });

  @override
  _ScaleTransitionButtonState createState() => _ScaleTransitionButtonState();
}

class _ScaleTransitionButtonState extends State<ScaleTransitionButton> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 100),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.9).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: (_) => _controller.forward(),
      onTapUp: (_) {
        _controller.reverse();
        widget.onPressed();
      },
      onTapCancel: () => _controller.reverse(),
      child: ScaleTransition(
        scale: _scaleAnimation,
        child: ElevatedButton(
          onPressed: null, // Handled by GestureDetector
          style: widget.style,
          child: widget.child,
        ),
      ),
    );
  }
}

class HeartPulseAnimation extends StatefulWidget {
  const HeartPulseAnimation({super.key});

  @override
  _HeartPulseAnimationState createState() => _HeartPulseAnimationState();
}

class _HeartPulseAnimationState extends State<HeartPulseAnimation> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _animation = Tween<double>(begin: 0.8, end: 1.2).animate(
      CurvedAnimation(parent: _controller, curve: Curves.elasticOut),
    );
    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: ScaleTransition(
        scale: _animation,
        child: const Icon(
          Icons.favorite,
          color: Colors.red,
          size: 100,
        ),
      ),
    );
  }
}

class CheckmarkPulseAnimation extends StatefulWidget {
  const CheckmarkPulseAnimation({super.key});

  @override
  _CheckmarkPulseAnimationState createState() => _CheckmarkPulseAnimationState();
}

class _CheckmarkPulseAnimationState extends State<CheckmarkPulseAnimation> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _animation = Tween<double>(begin: 0.8, end: 1.2).animate(
      CurvedAnimation(parent: _controller, curve: Curves.elasticOut),
    );
    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: ScaleTransition(
        scale: _animation,
        child: const Icon(
          Icons.check_circle,
          color: Colors.green,
          size: 100,
        ),
      ),
    );
  }
}

// خدمة إدارة الهوايات
class HobbyService {
  final String baseUrl;
  final String authToken;
  io.Socket? socket;

  HobbyService({required this.baseUrl, required this.authToken});

  Future<List<HobbyContent>> fetchContents() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/contents'),
        headers: {'Authorization': 'Bearer $authToken'},
      );
      if (response.statusCode == 200) {
        return (jsonDecode(response.body) as List)
            .map((json) => HobbyContent.fromJson(json))
            .toList();
      } else {
        throw Exception('فشل في جلب المحتويات: ${response.body}');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }

  Future<List<Map<String, dynamic>>> fetchNotifications() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/notifications'),
        headers: {'Authorization': 'Bearer $authToken'},
      );
      if (response.statusCode == 200) {
        return List<Map<String, dynamic>>.from(jsonDecode(response.body));
      } else {
        throw Exception('فشل في جلب الإشعارات: ${response.body}');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }

  Future<List<Map<String, dynamic>>> fetchPosts() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/posts'),
        headers: {'Authorization': 'Bearer $authToken'},
      );
      if (response.statusCode == 200) {
        return (jsonDecode(response.body) as List)
            .map((json) => {
          '_id': json['_id'],
          'memberName': json['memberName'],
          'postContent': json['postContent'],
          'attachedFile': json['attachedFile'],
          'fileType': json['fileType'],
          'likes': json['likes'] ?? 0,
          'views': json['views'] ?? 0,
          'comments': List<String>.from(json['comments'] ?? []),
          'points': json['points'] ?? 0,
          'postsToday': json['postsToday'] ?? 0,
          'isLiked': false,
          'isFavorite': false,
          'selectedForChat': false,
          'creatorId': json['creatorId'],
          'createdAt': DateTime.parse(json['createdAt'] ?? DateTime.now().toString()),
        })
            .toList();
      } else {
        throw Exception('فشل في جلب المنشورات: ${response.body}');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }

  Future<List<Map<String, dynamic>>> fetchTasks() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/tasks'),
        headers: {'Authorization': 'Bearer $authToken'},
      );
      if (response.statusCode == 200) {
        return (jsonDecode(response.body) as List)
            .map((json) => {
          '_id': json['_id'],
          'task': json['task'],
          'points': json['points'],
          'completed': json['completed'],
          'creatorId': json['creatorId'],
        })
            .toList();
      } else {
        throw Exception('فشل في جلب المهام: ${response.body}');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }

  Future<List<Map<String, dynamic>>> fetchChallenges() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/challenges'),
        headers: {'Authorization': 'Bearer $authToken'},
      );
      if (response.statusCode == 200) {
        return (jsonDecode(response.body) as List)
            .map((json) => {
          '_id': json['_id'],
          'name': json['name'],
          'description': json['description'],
          'points': json['points'],
          'creatorId': json['creatorId'],
        })
            .toList();
      } else {
        throw Exception('فشل في جلب التحديات: ${response.body}');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }

  Future<List<Member>> fetchMembers() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/members'),
        headers: {'Authorization': 'Bearer $authToken'},
      );
      if (response.statusCode == 200) {
        return (jsonDecode(response.body) as List)
            .map((json) => Member.fromJson(json))
            .toList();
      } else {
        throw Exception('فشل في جلب الأعضاء: ${response.body}');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }

  Future<void> createContent(
      String title, String type, String? description, PlatformFile? file, String creatorId) async {
    try {
      var request = http.MultipartRequest('POST', Uri.parse('$baseUrl/contents'));
      request.headers['Authorization'] = 'Bearer $authToken';
      request.fields['title'] = title;
      request.fields['type'] = type;
      request.fields['description'] = description ?? '';
      request.fields['creatorId'] = creatorId;
      if (file != null) {
        if (file.size > 10 * 1024 * 1024) {
          throw Exception('حجم الملف كبير جدًا (يجب أن يكون أقل من 10 ميجابايت)');
        }
        request.files.add(http.MultipartFile.fromBytes(
          'file',
          file.bytes!,
          filename: file.name,
        ));
      }
      final response = await request.send();
      if (response.statusCode != 201) {
        throw Exception('فشل في إضافة المحتوى: ${response.reasonPhrase}');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }

  Future<void> createPost(
      String memberName, String postContent, PlatformFile? file, String creatorId) async {
    try {
      var request = http.MultipartRequest('POST', Uri.parse('$baseUrl/posts'));
      request.headers['Authorization'] = 'Bearer $authToken';
      request.fields['memberName'] = memberName;
      request.fields['postContent'] = postContent;
      request.fields['creatorId'] = creatorId;
      if (file != null) {
        if (file.size > 10 * 1024 * 1024) {
          throw Exception('حجم الملف كبير جدًا (يجب أن يكون أقل من 10 ميجابايت)');
        }
        request.files.add(http.MultipartFile.fromBytes(
          'file',
          file.bytes!,
          filename: file.name,
        ));
      }
      final response = await request.send();
      if (response.statusCode != 201) {
        throw Exception('فشل في النشر: ${response.reasonPhrase}');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }

  Future<void> updatePost(String postId, Map<String, dynamic> post) async {
    try {
      final response = await http.put(
        Uri.parse('$baseUrl/posts/$postId'),
        headers: {
          'Authorization': 'Bearer $authToken',
          'Content-Type': 'application/json',
        },
        body: jsonEncode({'likes': post['likes'], 'comments': post['comments']}),
      );
      if (response.statusCode != 200) {
        throw Exception('فشل في تحديث المنشور: ${response.body}');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }

  Future<void> completeTask(String taskId) async {
    try {
      final response = await http.put(
        Uri.parse('$baseUrl/tasks/$taskId'),
        headers: {'Authorization': 'Bearer $authToken'},
      );
      if (response.statusCode != 200) {
        throw Exception('فشل في إكمال المهمة: ${response.body}');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }

  void setupSocketListeners(io.Socket socket, Function(dynamic) onNewContent) {
    this.socket = socket;
    socket.on('new_content', onNewContent);
  }
}

class Rayan8 extends StatelessWidget {
  const Rayan8({super.key});

  @override
  Widget build(BuildContext context) {
    return const HobbyHomeScreen();
  }
}

class HobbyHomeScreen extends StatefulWidget {
  const HobbyHomeScreen({super.key});

  @override
  _HobbyHomeScreenState createState() => _HobbyHomeScreenState();
}

class _HobbyHomeScreenState extends State<HobbyHomeScreen> with SingleTickerProviderStateMixin {
  late AnimationController _fabAnimationController;
  late Animation<double> _fabAnimation;
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final HobbyService _hobbyService = HobbyService(
    baseUrl: AppState.getBackendUrl(),
    authToken: defaultAuthToken,
  );
  List<HobbyContent> _contents = [];
  List<Map<String, dynamic>> _notifications = [];
  List<Member> _members = [];
  bool _isLoading = true;
  bool _showScrollToTop = false;
  bool _showTrendingOnly = false;

  @override
  void initState() {
    super.initState();
    setupTimeago();
    _fabAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    _fabAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fabAnimationController,
      curve: Curves.easeOut,
    ));
    _fabAnimationController.forward();
    _scrollController.addListener(_scrollListener);
    _searchController.addListener(() => setState(() {}));
    final appState = Provider.of<AppState>(context, listen: false);
    if (appState.socket != null) {
      _hobbyService.setupSocketListeners(appState.socket!, (data) {
        _fetchContents();
        setState(() {
          _notifications.add({
            'message': 'محتوى جديد: ${data['title']}',
            'date': DateTime.now().toString(),
            'read': false,
          });
        });
      });
    }
    _fetchData();
  }

  void _scrollListener() {
    setState(() {
      _showScrollToTop = _scrollController.position.pixels > 200;
    });
  }

  Future<void> _fetchData() async {
    setState(() => _isLoading = true);
    try {
      await Future.wait([
        _fetchContents(),
        _fetchNotifications(),
        _fetchMembers(),
      ]);
      setState(() => _isLoading = false);
    } catch (e) {
      _showSnackBar('خطأ في جلب البيانات: $e', Colors.red);
      setState(() => _isLoading = false);
    }
  }

  Future<void> _fetchContents() async {
    try {
      _contents = await _hobbyService.fetchContents();
      setState(() {});
    } catch (e) {
      throw Exception('خطأ في جلب المحتويات: $e');
    }
  }

  Future<void> _fetchNotifications() async {
    try {
      _notifications = await _hobbyService.fetchNotifications();
      setState(() {});
    } catch (e) {
      throw Exception('خطأ في جلب الإشعارات: $e');
    }
  }

  Future<void> _fetchMembers() async {
    try {
      _members = await _hobbyService.fetchMembers();
      setState(() {});
    } catch (e) {
      _members = [
        Member(id: 'user1', name: 'أحمد'),
        Member(id: 'user2', name: 'سارة'),
        Member(id: 'user3', name: 'محمد'),
      ];
      throw Exception('خطأ في جلب الأعضاء: $e');
    }
  }

  Future<void> _refreshData() async {
    HapticFeedback.lightImpact();
    await _fetchData();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _searchController.dispose();
    _fabAnimationController.dispose();
    super.dispose();
  }

  void _showSnackBar(String message, [Color? backgroundColor]) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: backgroundColor ?? Colors.teal,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        elevation: 6,
      ),
    );
  }
  
  void _showAddHobbyDialog(BuildContext context) {
    final appState = Provider.of<AppState>(context, listen: false);
    final roleName = appState.currentUser?.role.name ?? 'guest';
    final userType = appState.userType ?? 'guest';
    
    // التحقق من أن المستخدم لديه دور "hokama"
    if (!(roleName == 'hokama' || userType == 'hokama')) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('غير مصرح'),
          content: const Text('هذه الميزة متاحة فقط للحكماء'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('إغلاق'),
            ),
          ],
        ),
      );
      return;
    }
    
    // متغيرات للنموذج
    final titleController = TextEditingController();
    final descriptionController = TextEditingController();
    String selectedType = 'هواية عامة';
    final types = ['هواية عامة', 'هواية رياضية', 'هواية فنية', 'هواية ثقافية', 'هواية اجتماعية'];
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إضافة هواية جديدة'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: titleController,
                decoration: const InputDecoration(
                  labelText: 'اسم الهواية',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 10),
              DropdownButtonFormField<String>(
                value: selectedType,
                decoration: const InputDecoration(
                  labelText: 'نوع الهواية',
                  border: OutlineInputBorder(),
                ),
                items: types.map((type) => DropdownMenuItem(
                  value: type,
                  child: Text(type),
                )).toList(),
                onChanged: (value) {
                  selectedType = value!;
                },
              ),
              const SizedBox(height: 10),
              TextField(
                controller: descriptionController,
                decoration: const InputDecoration(
                  labelText: 'وصف الهواية',
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              // التحقق من البيانات
              if (titleController.text.isEmpty) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('يرجى إدخال اسم الهواية')),
                );
                return;
              }
              
              // هنا يمكن إضافة كود لإرسال البيانات إلى الخادم
              // مثال:
              // _hobbyService.createContent(
              //   titleController.text,
              //   selectedType,
              //   descriptionController.text,
              //   null,
              //   appState.currentUser?.id ?? 'unknown',
              // );
              
              Navigator.pop(context);
              _showSnackBar('تم إضافة الهواية بنجاح', Colors.green);
              
              // إعادة تحميل البيانات
              _fetchContents();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.teal,
            ),
            child: const Text('إضافة'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return Scaffold(
      appBar: AppBar(
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: isDarkMode
                  ? [Colors.teal[900]!, Colors.purple[700]!]
                  : [Colors.teal, Colors.purpleAccent],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
        ),
        title: TextField(
          controller: _searchController,
          decoration: InputDecoration(
            hintText: 'ابحث عن هواية...',
            border: InputBorder.none,
            hintStyle: const TextStyle(color: Colors.white70),
            filled: true,
            fillColor: Colors.white24,
            prefixIcon: const Icon(Icons.search, color: Colors.white),
            suffixIcon: _searchController.text.isNotEmpty
                ? IconButton(
              icon: const Icon(Icons.clear, color: Colors.white),
              onPressed: () {
                setState(() => _searchController.clear());
                HapticFeedback.selectionClick();
              },
            )
                : null,
          ),
          style: const TextStyle(color: Colors.white),
        ),
        actions: [
          IconButton(
            icon: Stack(
              children: [
                const Icon(Icons.notifications, color: Colors.white),
                if (_notifications.isNotEmpty)
                  Positioned(
                    right: 0,
                    child: Container(
                      padding: const EdgeInsets.all(2),
                      decoration: const BoxDecoration(
                        color: Colors.red,
                        shape: BoxShape.circle,
                      ),
                      constraints: const BoxConstraints(minWidth: 12, minHeight: 12),
                      child: Text(
                        '${_notifications.length}',
                        style: const TextStyle(color: Colors.white, fontSize: 8),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
              ],
            ),
            onPressed: () {
              HapticFeedback.selectionClick();
              _showNotifications(context);
            },
          ),
        ],
      ),
      body: Stack(
        children: [
          RefreshIndicator(
            onRefresh: _refreshData,
            color: Colors.teal,
            backgroundColor: isDarkMode ? Colors.grey[900] : Colors.white,
            child: SingleChildScrollView(
              controller: _scrollController,
              physics: const AlwaysScrollableScrollPhysics(),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildFilterChips(),
                  _buildCategorySection(
                    context,
                    "هوايات فنية",
                    [
                      {"image": "images/art.png", "title": "الرسم", "icon": Icons.brush},
                      {"image": "images/music.png", "title": "العزف الموسيقي", "icon": Icons.music_note},
                      {"image": "images/photography.png", "title": "التصوير الفوتوغرافي", "icon": Icons.camera_alt},
                      {"image": "images/craft.png", "title": "الأشغال اليدوية", "icon": Icons.handyman},
                      {"image": "images/dance.png", "title": "الرقص", "icon": Icons.directions_run},
                    ],
                  ),
                  _buildCategorySection(
                    context,
                    "هوايات رياضية",
                    [
                      {"image": "images/soccer.png", "title": "كرة القدم", "icon": Icons.sports_soccer},
                      {"image": "images/basketball.png", "title": "كرة السلة", "icon": Icons.sports_basketball},
                      {"image": "images/tennis.png", "title": "التنس", "icon": Icons.sports_tennis},
                      {"image": "images/swimming.png", "title": "السباحة", "icon": Icons.pool},
                      {"image": "images/volleyball.png", "title": "كرة الطائرة", "icon": Icons.sports_volleyball},
                    ],
                  ),
                  _buildCategorySection(
                    context,
                    "هوايات دينية",
                    [
                      {"image": "images/quran.png", "title": "ترتيل القرآن", "icon": Icons.book},
                      {"image": "images/hymns.png", "title": "ترانيم", "icon": Icons.music_note},
                    ],
                  ),
                  _buildCategorySection(
                    context,
                    "هوايات تقنية",
                    [
                      {"image": "images/coding.png", "title": "البرمجة", "icon": Icons.code},
                      {"image": "images/gaming.png", "title": "الألعاب الإلكترونية", "icon": Icons.videogame_asset},
                    ],
                  ),
                  _buildCategorySection(
                    context,
                    "هوايات ثقافية",
                    [
                      {"image": "images/reading.png", "title": "القراءة", "icon": Icons.book},
                      {"image": "images/writing.png", "title": "الكتابة", "icon": Icons.edit},
                    ],
                  ),
                  const SizedBox(height: 80),
                ],
              ),
            ),
          ),
          if (_isLoading) _buildSkeletonLoader(),
          if (_showScrollToTop)
            Positioned(
              bottom: 100,
              right: 16,
              child: FloatingActionButton(
                mini: true,
                backgroundColor: Colors.teal,
                onPressed: () {
                  HapticFeedback.mediumImpact();
                  _scrollController.animateTo(
                    0,
                    duration: const Duration(milliseconds: 500),
                    curve: Curves.easeInOut,
                  );
                },
                child: const Icon(Icons.arrow_upward, color: Colors.white),
              ),
            ),
          Positioned(
            bottom: 16,
            right: 16,
            child: ScaleTransition(
              scale: _fabAnimation,
              child: FloatingActionButton(
                onPressed: () {
                  _showAddHobbyDialog(context);
                },
                backgroundColor: Colors.teal,
                child: const Icon(Icons.add, color: Colors.white),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSkeletonLoader() {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: Column(
        children: [
          Container(
            height: 50,
            margin: const EdgeInsets.symmetric(horizontal: 8),
            color: Colors.white,
          ),
          ...List.generate(
            5,
                (index) => Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  height: 30,
                  width: 150,
                  margin: const EdgeInsets.all(8),
                  color: Colors.white,
                ),
                Container(
                  height: 200,
                  margin: const EdgeInsets.symmetric(horizontal: 8),
                  color: Colors.white,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChips() {
    return Container(
      height: 50,
      padding: const EdgeInsets.symmetric(horizontal: 8),
      child: Row(
        children: [
          AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: _showTrendingOnly ? Colors.teal[100] : Colors.grey[200],
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.2),
                  spreadRadius: 1,
                  blurRadius: 3,
                ),
              ],
            ),
            child: GestureDetector(
              onTap: () {
                HapticFeedback.selectionClick();
                setState(() => _showTrendingOnly = !_showTrendingOnly);
              },
              child: Row(
                children: [
                  Icon(
                    Icons.trending_up,
                    size: 20,
                    color: _showTrendingOnly ? Colors.teal[800] : Colors.grey,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    'رائج فقط',
                    style: TextStyle(
                      color: _showTrendingOnly ? Colors.teal[800] : Colors.grey,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategorySection(
      BuildContext context, String categoryName, List<Map<String, dynamic>> items) {
    final filteredItems = items
        .where((item) =>
        item["title"]!.toLowerCase().contains(_searchController.text.toLowerCase()))
        .toList();
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Text(
              categoryName,
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.teal[900],
              ),
            ),
          ),
          SizedBox(
            height: 200,
            child: filteredItems.isEmpty
                ? Center(
              child: Text(
                "لا توجد هوايات مطابقة",
                style: TextStyle(color: Colors.grey[600]),
              ),
            )
                : ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: filteredItems.length,
              itemBuilder: (context, index) => FadeInAnimation(
                delay: index * 0.1,
                child: GestureDetector(
                  onTap: () {
                    HapticFeedback.selectionClick();
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => CourseDetailsPage(
                          courseTitle: filteredItems[index]["title"]!,
                          contents: _contents,
                          members: _members,
                        ),
                      ),
                    );
                  },
                  child: Container(
                    width: 150,
                    margin: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.grey.withOpacity(0.2),
                          spreadRadius: 1,
                          blurRadius: 3,
                          offset: const Offset(2, 2),
                        ),
                        BoxShadow(
                          color: Colors.white.withOpacity(0.5),
                          spreadRadius: 1,
                          blurRadius: 3,
                          offset: const Offset(-2, -2),
                        ),
                      ],
                    ),
                    child: Stack(
                      children: [
                        ClipRRect(
                          borderRadius: BorderRadius.circular(12),
                          child: Image.asset(
                            filteredItems[index]["image"]!,
                            fit: BoxFit.cover,
                            width: double.infinity,
                            height: double.infinity,
                            errorBuilder: (context, error, stackTrace) =>
                                Image.asset('images/placeholder.png'),
                          ),
                        ),
                        Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(12),
                            gradient: const LinearGradient(
                              colors: [Colors.black54, Colors.transparent],
                              begin: Alignment.bottomCenter,
                              end: Alignment.topCenter,
                            ),
                          ),
                        ),
                        Align(
                          alignment: Alignment.topLeft,
                          child: Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: Icon(
                              filteredItems[index]["icon"],
                              color: Colors.teal,
                              size: 24,
                            ),
                          ),
                        ),
                        Align(
                          alignment: Alignment.bottomCenter,
                          child: Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: Text(
                              filteredItems[index]["title"]!,
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showNotifications(BuildContext context) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.5,
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Text(
              'الإشعارات',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.teal[900],
              ),
            ),
            const SizedBox(height: 10),
            Expanded(
              child: _notifications.isEmpty
                  ? Center(
                child: Text(
                  'لا توجد إشعارات جديدة حالياً',
                  style: TextStyle(color: Colors.grey[600]),
                ),
              )
                  : ListView.builder(
                itemCount: _notifications.length,
                itemBuilder: (context, index) => FadeInAnimation(
                  delay: index * 0.1,
                  child: ListTile(
                    leading: const Icon(Icons.notifications_active, color: Colors.teal),
                    title: Text(_notifications[index]['message']),
                    subtitle: Text(
                      timeago.format(
                        DateTime.parse(_notifications[index]['date']),
                        locale: 'ar',
                      ),
                    ),
                    trailing: _notifications[index]['read']
                        ? null
                        : const Icon(Icons.circle, color: Colors.teal, size: 10),
                    onTap: () {
                      HapticFeedback.selectionClick();
                      setState(() => _notifications[index]['read'] = true);
                    },
                  ),
                ),
              ),
            ),
            ScaleTransitionButton(
              onPressed: () {
                HapticFeedback.selectionClick();
                setState(() => _notifications.clear());
                Navigator.pop(context);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
              child: const Text('مسح الكل', style: TextStyle(color: Colors.white)),
            ),
          ],
        ),
      ),
    );
  }
}

class CourseDetailsPage extends StatefulWidget {
  final String courseTitle;
  final List<HobbyContent> contents;
  final List<Member> members;

  const CourseDetailsPage({
    super.key,
    required this.courseTitle,
    required this.contents,
    required this.members,
  });

  @override
  _CourseDetailsPageState createState() => _CourseDetailsPageState();
}

class _CourseDetailsPageState extends State<CourseDetailsPage> with TickerProviderStateMixin {
  final TextEditingController _newPostController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final HobbyService _hobbyService = HobbyService(
    baseUrl: AppState.getBackendUrl(),
    authToken: defaultAuthToken,
  );
  String? _attachedFileName;
  String? _attachedFileType;
  PlatformFile? _attachedFile;
  List<Map<String, dynamic>> _memberPosts = [];
  List<Map<String, dynamic>> _hobbyTasks = [];
  List<Map<String, dynamic>> _hobbyChallenges = [];
  String _selectedMemberId = 'user1';
  bool _isLoading = false;
  bool _showScrollToTop = false;
  late ConfettiController _confettiController;

  @override
  void initState() {
    super.initState();
    _confettiController = ConfettiController(duration: const Duration(seconds: 3));
    _scrollController.addListener(_scrollListener);
    _fetchData();
  }

  void _scrollListener() {
    setState(() {
      _showScrollToTop = _scrollController.position.pixels > 200;
    });
  }

  Future<void> _fetchData() async {
    setState(() => _isLoading = true);
    try {
      await Future.wait([
        _fetchPosts(),
        _fetchTasks(),
        _fetchChallenges(),
      ]);
      setState(() => _isLoading = false);
    } catch (e) {
      _showSnackBar('خطأ في جلب البيانات: $e', Colors.red);
      setState(() => _isLoading = false);
    }
  }

  Future<void> _fetchPosts() async {
    try {
      _memberPosts = await _hobbyService.fetchPosts();
      setState(() {});
    } catch (e) {
      throw Exception('خطأ في جلب المنشورات: $e');
    }
  }

  Future<void> _fetchTasks() async {
    try {
      _hobbyTasks = await _hobbyService.fetchTasks();
      setState(() {});
    } catch (e) {
      throw Exception('خطأ في جلب المهام: $e');
    }
  }

  Future<void> _fetchChallenges() async {
    try {
      _hobbyChallenges = await _hobbyService.fetchChallenges();
      setState(() {});
    } catch (e) {
      throw Exception('خطأ في جلب التحديات: $e');
    }
  }

  Future<void> _refreshData() async {
    HapticFeedback.lightImpact();
    await _fetchData();
  }

  Future<void> _postNewContent() async {
    if (_newPostController.text.isNotEmpty || _attachedFile != null) {
      try {
        final memberName = widget.members
            .firstWhere(
              (m) => m.id == _selectedMemberId,
          orElse: () => Member(id: _selectedMemberId, name: 'غير معروف'),
        )
            .name;
        await _hobbyService.createPost(
          memberName,
          _newPostController.text.isNotEmpty ? _newPostController.text : "منشور بدون نص",
          _attachedFile,
          _selectedMemberId,
        );
        _newPostController.clear();
        setState(() {
          _attachedFileName = null;
          _attachedFileType = null;
          _attachedFile = null;
        });
        await _fetchPosts();
        _showSnackBar("تم نشر المنشور بنجاح!", Colors.green);
        showDialog(
          context: context,
          builder: (context) => const CheckmarkPulseAnimation(),
        );
        await Future.delayed(const Duration(seconds: 1));
        Navigator.pop(context);
      } catch (e) {
        _showSnackBar("فشل في النشر: $e", Colors.red);
      }
    } else {
      _showSnackBar("يرجى كتابة نص أو إرفاق ملف", Colors.orange);
    }
  }

  Future<void> _attachFile(String type) async {
    FilePickerResult? result = await FilePicker.platform.pickFiles(
      type: type == 'image' ? FileType.image : FileType.video,
      allowCompression: true,
    );
    if (result != null) {
      final file = result.files.single;
      if (file.size > 10 * 1024 * 1024) {
        _showSnackBar(
          'حجم الملف كبير جدًا (يجب أن يكون أقل من 10 ميجابايت)',
          Colors.orange,
        );
        return;
      }
      setState(() {
        _attachedFileName = file.name;
        _attachedFileType = type;
        _attachedFile = file;
      });
    }
  }

  void _removeAttachment() => setState(() {
    _attachedFileName = null;
    _attachedFileType = null;
    _attachedFile = null;
  });

  void _showSnackBar(String message, [Color? backgroundColor]) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: backgroundColor ?? Colors.teal,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        elevation: 6,
      ),
    );
  }
  
  void _showAddHobbyDialog(BuildContext context) {
    final appState = Provider.of<AppState>(context, listen: false);
    final roleName = appState.currentUser?.role.name ?? 'guest';
    final userType = appState.userType ?? 'guest';
    
    // التحقق من أن المستخدم لديه دور "hokama"
    if (!(roleName == 'hokama' || userType == 'hokama')) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('غير مصرح'),
          content: const Text('هذه الميزة متاحة فقط للحكماء'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('إغلاق'),
            ),
          ],
        ),
      );
      return;
    }
    
    // متغيرات للنموذج
    final titleController = TextEditingController();
    final descriptionController = TextEditingController();
    String selectedType = 'هواية عامة';
    final types = ['هواية عامة', 'هواية رياضية', 'هواية فنية', 'هواية ثقافية', 'هواية اجتماعية'];
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إضافة هواية جديدة'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: titleController,
                decoration: const InputDecoration(
                  labelText: 'اسم الهواية',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 10),
              DropdownButtonFormField<String>(
                value: selectedType,
                decoration: const InputDecoration(
                  labelText: 'نوع الهواية',
                  border: OutlineInputBorder(),
                ),
                items: types.map((type) => DropdownMenuItem(
                  value: type,
                  child: Text(type),
                )).toList(),
                onChanged: (value) {
                  selectedType = value!;
                },
              ),
              const SizedBox(height: 10),
              TextField(
                controller: descriptionController,
                decoration: const InputDecoration(
                  labelText: 'وصف الهواية',
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              // التحقق من البيانات
              if (titleController.text.isEmpty) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('يرجى إدخال اسم الهواية')),
                );
                return;
              }
              
              // هنا يمكن إضافة كود لإرسال البيانات إلى الخادم
              // مثال:
              // _hobbyService.createContent(
              //   titleController.text,
              //   selectedType,
              //   descriptionController.text,
              //   null,
              //   appState.currentUser?.id ?? 'unknown',
              // );
              
              Navigator.pop(context);
              _showSnackBar('تم إضافة الهواية بنجاح', Colors.green);
              
              // إعادة تحميل البيانات
              _fetchContents();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.teal,
            ),
            child: const Text('إضافة'),
          ),
        ],
      ),
    );
  }

  Future<void> _fetchContents() async {
    setState(() {
      _isLoading = true;
    });
    
    try {
      // Assuming you have a hobbyService and contents variable in this class
      final result = await _hobbyService.fetchContents();
      if (mounted) {
        setState(() {
          // Update your contents variable here
          // _contents = result;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        _showSnackBar('حدث خطأ أثناء تحميل المحتويات: $e', Colors.red);
      }
    }
  }

  @override
  void dispose() {
    _newPostController.dispose();
    _scrollController.dispose();
    _confettiController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final videos = widget.contents.where((content) => content.type == "فيديو").toList();
    final guides = widget.contents.where((content) => content.type == "دليل").toList();
    final articles = widget.contents.where((content) => content.type == "مقالة").toList();
    final images = widget.contents.where((content) => content.type == "صورة").toList();
    final liveStreams = widget.contents.where((content) => content.type == "بث مباشر").toList();

    return Scaffold(
      appBar: AppBar(
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: isDarkMode
                  ? [Colors.teal[900]!, Colors.purple[700]!]
                  : [Colors.teal, Colors.purpleAccent],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
        ),
        title: Text(
          widget.courseTitle,
          style: const TextStyle(fontWeight: FontWeight.bold, color: Colors.white),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.bookmark_border, color: Colors.white),
            onPressed: () {
              HapticFeedback.selectionClick();
              _showSnackBar("تمت إضافة الهواية إلى المفضلة", Colors.green);
            },
          ),
        ],
      ),
      body: Stack(
        children: [
          RefreshIndicator(
            onRefresh: _refreshData,
            color: Colors.teal,
            backgroundColor: isDarkMode ? Colors.grey[900] : Colors.white,
            child: SingleChildScrollView(
              controller: _scrollController,
              physics: const AlwaysScrollableScrollPhysics(),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildNewPostSection(),
                  _buildSection("فيديوهات تعليمية", videos, _buildVideoCard),
                  _buildSection("أدلة وكتيبات", guides, _buildGuideCard),
                  _buildSection("مقالات", articles, _buildArticleCard),
                  _buildSection("صور الأعضاء", images, _buildImageCard),
                  _buildSection("بث مباشر", liveStreams, _buildLiveStreamCard),
                  _buildChallengeSection(),
                  _buildTasksSection(),
                  _buildChallengesSection(),
                  _buildPostsSection(),
                  const SizedBox(height: 80),
                ],
              ),
            ),
          ),
          if (_isLoading) _buildSkeletonLoader(),
          if (_showScrollToTop)
            Positioned(
              bottom: 100,
              right: 16,
              child: FloatingActionButton(
                mini: true,
                backgroundColor: Colors.teal,
                onPressed: () {
                  HapticFeedback.mediumImpact();
                  _scrollController.animateTo(
                    0,
                    duration: const Duration(milliseconds: 500),
                    curve: Curves.easeInOut,
                  );
                },
                child: const Icon(Icons.arrow_upward, color: Colors.white),
              ),
            ),
          ConfettiWidget(
            confettiController: _confettiController,
            blastDirectionality: BlastDirectionality.explosive,
            shouldLoop: false,
            colors: const [Colors.teal, Colors.purple, Colors.yellow],
          ),
        ],
      ),
      floatingActionButton: _buildFloatingActionButtons(),
    );
  }

  Widget _buildSkeletonLoader() {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: Column(
        children: [
          Container(
            height: 150,
            margin: const EdgeInsets.all(8),
            color: Colors.white,
          ),
          ...List.generate(
            5,
                (index) => Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  height: 30,
                  width: 150,
                  margin: const EdgeInsets.all(8),
                  color: Colors.white,
                ),
                Container(
                  height: 180,
                  margin: const EdgeInsets.symmetric(horizontal: 8),
                  color: Colors.white,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNewPostSection() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Card(
        elevation: 0,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: Theme.of(context).cardColor,
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.2),
                spreadRadius: 1,
                blurRadius: 3,
                offset: const Offset(2, 2),
              ),
              BoxShadow(
                color: Colors.white.withOpacity(0.5),
                spreadRadius: 1,
                blurRadius: 3,
                offset: const Offset(-2, -2),
              ),
            ],
          ),
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      "اكتب منشورًا جديدًا",
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.teal[900],
                      ),
                    ),
                  ),
                  DropdownButton<String>(
                    value: _selectedMemberId,
                    items: widget.members
                        .map((m) => DropdownMenuItem(value: m.id, child: Text(m.name)))
                        .toList(),
                    onChanged: (value) {
                      HapticFeedback.selectionClick();
                      setState(() => _selectedMemberId = value!);
                    },
                    borderRadius: BorderRadius.circular(10),
                    underline: Container(),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              TextField(
                controller: _newPostController,
                maxLines: 3,
                decoration: InputDecoration(
                  hintText: "ما الذي تريد مشاركته؟",
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.grey[100],
                ),
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  IconButton(
                    icon: const Icon(Icons.photo, color: Colors.teal),
                    onPressed: () {
                      HapticFeedback.selectionClick();
                      _attachFile('image');
                    },
                    tooltip: "إرفاق صورة",
                  ),
                  IconButton(
                    icon: const Icon(Icons.videocam, color: Colors.teal),
                    onPressed: () {
                      HapticFeedback.selectionClick();
                      _attachFile('video');
                    },
                    tooltip: "إرفاق فيديو",
                  ),
                  const SizedBox(width: 8),
                  if (_attachedFileName != null)
                    Row(
                      children: [
                        Icon(
                          _attachedFileType == "image" ? Icons.image : Icons.video_library,
                          color: Colors.teal[700],
                        ),
                        const SizedBox(width: 4),
                        Text(
                          _attachedFileName!,
                          style: TextStyle(color: Colors.teal[700]),
                        ),
                        IconButton(
                          icon: const Icon(Icons.close, color: Colors.red),
                          onPressed: () {
                            HapticFeedback.selectionClick();
                            _removeAttachment();
                          },
                          tooltip: "إزالة المرفق",
                        ),
                      ],
                    ),
                  const Spacer(),
                  ScaleTransitionButton(
                    onPressed: _postNewContent,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.teal,
                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
                    ),
                    child: const Text("نشر", style: TextStyle(color: Colors.white)),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSection(String title, List<HobbyContent> items, Widget Function(HobbyContent) builder) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          child: Text(
            title,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.teal[900],
            ),
          ),
        ),
        SizedBox(
          height: 180,
          child: items.isEmpty
              ? Center(
            child: Text(
              "لا يوجد محتوى متاح",
              style: TextStyle(color: Colors.grey[600]),
            ),
          )
              : ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: items.length,
            itemBuilder: (context, index) =>
                FadeInAnimation(delay: index * 0.1, child: builder(items[index])),
          ),
        ),
      ],
    );
  }

  Widget _buildVideoCard(HobbyContent content) {
    return GestureDetector(
      onLongPress: () {
        HapticFeedback.selectionClick();
        _showContentPreview(context, content);
      },
      child: Card(
        elevation: 0,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: InkWell(
          onTap: () {
            HapticFeedback.selectionClick();
            _viewContent(content.filePath);
          },
          child: Container(
            width: 220,
            margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              color: Theme.of(context).cardColor,
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.2),
                  spreadRadius: 1,
                  blurRadius: 3,
                  offset: const Offset(2, 2),
                ),
                BoxShadow(
                  color: Colors.white.withOpacity(0.5),
                  spreadRadius: 1,
                  blurRadius: 3,
                  offset: const Offset(-2, -2),
                ),
              ],
            ),
            child: Stack(
              children: [
                Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(Icons.play_circle_fill, size: 50, color: Colors.teal),
                    const SizedBox(height: 8),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                      child: Text(
                        "${content.title} - ${widget.members.firstWhere((m) => m.id == content.creatorId, orElse: () => Member(id: content.creatorId, name: 'غير معروف')).name}",
                        style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                        textAlign: TextAlign.center,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      timeago.format(content.createdAt, locale: 'ar'),
                      style: const TextStyle(color: Colors.grey, fontSize: 12),
                    ),
                    const SizedBox(height: 8),
                    _buildRatingStars(content),
                  ],
                ),
                Positioned(
                  top: 8,
                  right: 8,
                  child: IconButton(
                    icon: Icon(
                      content.isFavorite ? Icons.star : Icons.star_border,
                      color: content.isFavorite ? Colors.yellow[700] : Colors.teal,
                      size: 20,
                    ),
                    onPressed: () {
                      HapticFeedback.selectionClick();
                      setState(() {
                        content.isFavorite = !content.isFavorite;
                      });
                      _showSnackBar(
                        content.isFavorite ? 'تمت الإضافة إلى المفضلة' : 'تمت الإزالة من المفضلة',
                        Colors.teal,
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildGuideCard(HobbyContent content) {
    return GestureDetector(
      onLongPress: () {
        HapticFeedback.selectionClick();
        _showContentPreview(context, content);
      },
      child: Card(
        elevation: 0,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: InkWell(
          onTap: () {
            HapticFeedback.selectionClick();
            _downloadFile(content.filePath);
          },
          child: Container(
            width: 140,
            margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              color: Theme.of(context).cardColor,
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.2),
                  spreadRadius: 1,
                  blurRadius: 3,
                  offset: const Offset(2, 2),
                ),
                BoxShadow(
                  color: Colors.white.withOpacity(0.5),
                  spreadRadius: 1,
                  blurRadius: 3,
                  offset: const Offset(-2, -2),
                ),
              ],
            ),
            child: Stack(
              children: [
                Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(Icons.book, size: 40, color: Colors.teal),
                    const SizedBox(height: 8),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                      child: Text(
                        content.title,
                        style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                        textAlign: TextAlign.center,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      timeago.format(content.createdAt, locale: 'ar'),
                      style: const TextStyle(color: Colors.grey, fontSize: 12),
                    ),
                    const SizedBox(height: 8),
                    _buildRatingStars(content),
                  ],
                ),
                Positioned(
                  top: 8,
                  right: 8,
                  child: IconButton(
                    icon: Icon(
                      content.isFavorite ? Icons.star : Icons.star_border,
                      color: content.isFavorite ? Colors.yellow[700] : Colors.teal,
                      size: 20,
                    ),
                    onPressed: () {
                      HapticFeedback.selectionClick();
                      setState(() {
                        content.isFavorite = !content.isFavorite;
                      });
                      _showSnackBar(
                        content.isFavorite ? 'تمت الإضافة إلى المفضلة' : 'تمت الإزالة من المفضلة',
                        Colors.teal,
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildArticleCard(HobbyContent content) {
    return GestureDetector(
      onLongPress: () {
        HapticFeedback.selectionClick();
        _showContentPreview(context, content);
      },
      child: Card(
        elevation: 0,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: InkWell(
          onTap: () {
            HapticFeedback.selectionClick();
            _viewContent(content.filePath);
          },
          child: Container(
            width: 220,
            margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              color: Theme.of(context).cardColor,
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.2),
                  spreadRadius: 1,
                  blurRadius: 3,
                  offset: const Offset(2, 2),
                ),
                BoxShadow(
                  color: Colors.white.withOpacity(0.5),
                  spreadRadius: 1,
                  blurRadius: 3,
                  offset: const Offset(-2, -2),
                ),
              ],
            ),
            child: Stack(
              children: [
                Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(Icons.article, size: 40, color: Colors.teal),
                    const SizedBox(height: 8),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                      child: Text(
                        content.title,
                        style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                        textAlign: TextAlign.center,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      timeago.format(content.createdAt, locale: 'ar'),
                      style: const TextStyle(color: Colors.grey, fontSize: 12),
                    ),
                    const SizedBox(height: 8),
                    _buildRatingStars(content),
                  ],
                ),
                Positioned(
                  top: 8,
                  right: 8,
                  child: IconButton(
                    icon: Icon(
                      content.isFavorite ? Icons.star : Icons.star_border,
                      color: content.isFavorite ? Colors.yellow[700] : Colors.teal,
                      size: 20,
                    ),
                    onPressed: () {
                      HapticFeedback.selectionClick();
                      setState(() {
                        content.isFavorite = !content.isFavorite;
                      });
                      _showSnackBar(
                        content.isFavorite ? 'تمت الإضافة إلى المفضلة' : 'تمت الإزالة من المفضلة',
                        Colors.teal,
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildImageCard(HobbyContent content) {
    return GestureDetector(
      onLongPress: () {
        HapticFeedback.selectionClick();
        _showContentPreview(context, content);
      },
      child: Card(
        elevation: 0,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: InkWell(
          onTap: () {
            HapticFeedback.selectionClick();
            _viewContent(content.filePath);
          },
          child: Container(
            width: 140,
            margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              color: Theme.of(context).cardColor,
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.2),
                  spreadRadius: 1,
                  blurRadius: 3,
                  offset: const Offset(2, 2),
                ),
                BoxShadow(
                  color: Colors.white.withOpacity(0.5),
                  spreadRadius: 1,
                  blurRadius: 3,
                  offset: const Offset(-2, -2),
                ),
              ],
            ),
            child: Stack(
              children: [
                content.filePath != null
                    ? ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: CachedNetworkImage(
                    imageUrl: '${_hobbyService.baseUrl}${content.filePath}',
                    fit: BoxFit.cover,
                    width: double.infinity,
                    height: double.infinity,
                    placeholder: (context, url) => Shimmer.fromColors(
                      baseColor: Colors.grey[300]!,
                      highlightColor: Colors.grey[100]!,
                      child: Container(color: Colors.white),
                    ),
                    errorWidget: (context, url, error) =>
                    const Icon(Icons.error, color: Colors.grey),
                  ),
                )
                    : const Icon(Icons.image, size: 40, color: Colors.teal),
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    gradient: const LinearGradient(
                      colors: [Colors.black54, Colors.transparent],
                      begin: Alignment.bottomCenter,
                      end: Alignment.topCenter,
                    ),
                  ),
                ),
                Column(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(8),
                      child: Text(
                        content.title,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                        ),
                        textAlign: TextAlign.center,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
                Positioned(
                  top: 8,
                  right: 8,
                  child: IconButton(
                    icon: Icon(
                      content.isFavorite ? Icons.star : Icons.star_border,
                      color: content.isFavorite ? Colors.yellow[700] : Colors.teal,
                      size: 20,
                    ),
                    onPressed: () {
                      HapticFeedback.selectionClick();
                      setState(() {
                        content.isFavorite = !content.isFavorite;
                      });
                      _showSnackBar(
                        content.isFavorite ? 'تمت الإضافة إلى المفضلة' : 'تمت الإزالة من المفضلة',
                        Colors.teal,
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLiveStreamCard(HobbyContent content) {
    return GestureDetector(
      onLongPress: () {
        HapticFeedback.selectionClick();
        _showContentPreview(context, content);
      },
      child: Card(
        elevation: 0,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: InkWell(
          onTap: () {
            HapticFeedback.selectionClick();
            _viewContent(content.filePath);
          },
          child: Container(
            width: 220,
            margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              color: Theme.of(context).cardColor,
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.2),
                  spreadRadius: 1,
                  blurRadius: 3,
                  offset: const Offset(2, 2),
                ),
                BoxShadow(
                  color: Colors.white.withOpacity(0.5),
                  spreadRadius: 1,
                  blurRadius: 3,
                  offset: const Offset(-2, -2),
                ),
              ],
            ),
            child: Stack(
              children: [
                Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(Icons.live_tv, size: 50, color: Colors.red),
                    const SizedBox(height: 8),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                      child: Text(
                        content.title,
                        style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                        textAlign: TextAlign.center,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      timeago.format(content.createdAt, locale: 'ar'),
                      style: const TextStyle(color: Colors.grey, fontSize: 12),
                    ),
                    const SizedBox(height: 8),
                    ScaleTransitionButton(
                      onPressed: () => _viewContent(content.filePath),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
                      ),
                      child: const Text("مشاهدة", style: TextStyle(color: Colors.white)),
                    ),
                  ],
                ),
                Positioned(
                  top: 8,
                  left: 8,
                  child: DottedBorder(
                    color: Colors.red,
                    strokeWidth: 2,
                    dashPattern: const [4, 4],
                    borderType: BorderType.RRect,
                    radius: const Radius.circular(8),
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      color: Colors.red.withOpacity(0.2),
                      child: const Text(
                        'مباشر',
                        style: TextStyle(color: Colors.red, fontSize: 12),
                      ),
                    ),
                  ),
                ),
                Positioned(
                  top: 8,
                  right: 8,
                  child: IconButton(
                    icon: Icon(
                      content.isFavorite ? Icons.star : Icons.star_border,
                      color: content.isFavorite ? Colors.yellow[700] : Colors.teal,
                      size: 20,
                    ),
                    onPressed: () {
                      HapticFeedback.selectionClick();
                      setState(() {
                        content.isFavorite = !content.isFavorite;
                      });
                      _showSnackBar(
                        content.isFavorite ? 'تمت الإضافة إلى المفضلة' : 'تمت الإزالة من المفضلة',
                        Colors.teal,
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildRatingStars(HobbyContent content) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(5, (index) {
        return IconButton(
          icon: Icon(
            index < content.rating ? Icons.star : Icons.star_border,
            color: Colors.yellow[700],
            size: 16,
          ),
          onPressed: () {
            HapticFeedback.selectionClick();
            setState(() {
              content.rating = index + 1;
            });
            _showSnackBar('تم تقييم المحتوى بـ ${content.rating} نجوم', Colors.teal);
          },
        );
      }),
    );
  }

  Widget _buildChallengeSection() {
    final postsToday = _memberPosts
        .where((post) =>
    post['creatorId'] == _selectedMemberId &&
        post['createdAt'].day == DateTime.now().day)
        .fold(0, (sum, post) => sum + (post['postsToday'] as int));
    final progress = postsToday / 5;
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Card(
        elevation: 0,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: Theme.of(context).cardColor,
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.2),
                spreadRadius: 1,
                blurRadius: 3,
                offset: const Offset(2, 2),
              ),
              BoxShadow(
                color: Colors.white.withOpacity(0.5),
                spreadRadius: 1,
                blurRadius: 3,
                offset: const Offset(-2, -2),
              ),
            ],
          ),
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "تحدي النشاط",
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.teal[900],
                ),
              ),
              const SizedBox(height: 8),
              Text(
                "التقدم في تحدي النشاط (5 منشورات يومية)",
                style: TextStyle(fontSize: 16, color: Colors.teal[700]),
              ),
              const SizedBox(height: 8),
              Stack(
                alignment: Alignment.center,
                children: [
                  SizedBox(
                    height: 80,
                    width: 80,
                    child: CircularProgressIndicator(
                      value: progress,
                      backgroundColor: Colors.grey[300],
                      color: Colors.teal,
                      strokeWidth: 8,
                    ),
                  ),
                  Text(
                    "${(progress * 100).toInt()}%",
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.teal[900],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                "المنشورات اليومية: $postsToday / 5",
                style: TextStyle(color: Colors.teal[700]),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTasksSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          child: Text(
            "مهام الهواية اليومية",
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.teal[900],
            ),
          ),
        ),
        SizedBox(
          height: 200,
          child: _hobbyTasks.isEmpty
              ? Center(
            child: Text(
              "لا توجد مهام متاحة",
              style: TextStyle(color: Colors.grey[600]),
            ),
          )
              : ListView.builder(
            itemCount: _hobbyTasks.length,
            itemBuilder: (context, index) => FadeInAnimation(
              delay: index * 0.1,
              child: Card(
                elevation: 0,
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    color: Theme.of(context).cardColor,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey.withOpacity(0.2),
                        spreadRadius: 1,
                        blurRadius: 3,
                        offset: const Offset(2, 2),
                      ),
                      BoxShadow(
                        color: Colors.white.withOpacity(0.5),
                        spreadRadius: 1,
                        blurRadius: 3,
                        offset: const Offset(-2, -2),
                      ),
                    ],
                  ),
                  child: ListTile(
                    leading: Icon(
                      _hobbyTasks[index]['completed']
                          ? Icons.check_circle
                          : Icons.circle_outlined,
                      color: _hobbyTasks[index]['completed'] ? Colors.green : Colors.grey,
                    ),
                    title: Text(_hobbyTasks[index]['task']),
                    trailing: Text(
                      "+${_hobbyTasks[index]['points']} نقاط",
                      style: TextStyle(color: Colors.teal[700]),
                    ),
                    onTap: () => _completeTask(_hobbyTasks[index]['_id']),
                  ),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Future<void> _completeTask(String taskId) async {
    try {
      await _hobbyService.completeTask(taskId);
      await _fetchTasks();
      _showSnackBar("تم إكمال المهمة", Colors.green);
      _confettiController.play();
      showDialog(
        context: context,
        builder: (context) => const CheckmarkPulseAnimation(),
      );
      await Future.delayed(const Duration(seconds: 1));
      Navigator.pop(context);
    } catch (e) {
      _showSnackBar("خطأ في إكمال المهمة: $e", Colors.red);
    }
  }

  Widget _buildChallengesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          child: Text(
            "تحديات الهواية",
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.teal[900],
            ),
          ),
        ),
        SizedBox(
          height: 200,
          child: _hobbyChallenges.isEmpty
              ? Center(
            child: Text(
              "لا توجد تحديات متاحة",
              style: TextStyle(color: Colors.grey[600]),
            ),
          )
              : ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: _hobbyChallenges.length,
            itemBuilder: (context, index) => FadeInAnimation(
              delay: index * 0.1,
              child: Card(
                elevation: 0,
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    color: Theme.of(context).cardColor,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey.withOpacity(0.2),
                        spreadRadius: 1,
                        blurRadius: 3,
                        offset: const Offset(2, 2),
                      ),
                      BoxShadow(
                        color: Colors.white.withOpacity(0.5),
                        spreadRadius: 1,
                        blurRadius: 3,
                        offset: const Offset(-2, -2),
                      ),
                    ],
                  ),
                  child: ListTile(
                    leading: const Icon(Icons.star, color: Colors.yellow, size: 20),
                    title: Text(_hobbyChallenges[index]['name']),
                    subtitle: Text(_hobbyChallenges[index]['description']),
                    trailing: Text(
                      "${_hobbyChallenges[index]['points']} نقطة",
                      style: TextStyle(fontWeight: FontWeight.bold, color: Colors.teal[700]),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPostsSection() {
    final filteredPosts = _memberPosts; // Placeholder for filtering logic if needed
    return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
    Padding(
    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
    child: Text(
    "منشورات الأعضاء",
    style: TextStyle(
    fontSize: 20,
    fontWeight: FontWeight.bold,
    color: Colors.teal[900],
    ),
    ),
    ),
    filteredPosts.isEmpty
    ? Center(
    child: Text(
    "لا توجد منشورات متاحة",
    style: TextStyle(color: Colors.grey[600]),
    ),
    )
        : ListView.builder(
    shrinkWrap: true,
    physics: const NeverScrollableScrollPhysics(),
    itemCount: filteredPosts.length,
      itemBuilder: (context, index) => FadeInAnimation(
        delay: index * 0.1,
        child: _buildPostCard(filteredPosts[index], index),
      ),
    ),
        ],
    );
  }

  Widget _buildPostCard(Map<String, dynamic> post, int index) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: Theme.of(context).cardColor,
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.2),
              spreadRadius: 1,
              blurRadius: 3,
              offset: const Offset(2, 2),
            ),
            BoxShadow(
              color: Colors.white.withOpacity(0.5),
              spreadRadius: 1,
              blurRadius: 3,
              offset: const Offset(-2, -2),
            ),
          ],
        ),
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  backgroundColor: Colors.teal,
                  child: Text(
                    post['memberName'][0],
                    style: const TextStyle(color: Colors.white),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        post['memberName'],
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.teal[900],
                        ),
                      ),
                      Text(
                        timeago.format(post['createdAt'], locale: 'ar'),
                        style: TextStyle(color: Colors.grey[600], fontSize: 12),
                      ),
                    ],
                  ),
                ),
                IconButton(
                  icon: Icon(
                    post['isFavorite'] ? Icons.bookmark : Icons.bookmark_border,
                    color: post['isFavorite'] ? Colors.teal : Colors.grey,
                  ),
                  onPressed: () {
                    HapticFeedback.selectionClick();
                    setState(() {
                      post['isFavorite'] = !post['isFavorite'];
                    });
                    _showSnackBar(
                      post['isFavorite']
                          ? 'تمت إضافة المنشور إلى المفضلة'
                          : 'تمت إزالة المنشور من المفضلة',
                      Colors.teal,
                    );
                  },
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              post['postContent'],
              style: TextStyle(
                fontSize: 16,
                color: isDarkMode ? Colors.white : Colors.black87,
              ),
            ),
            if (post['attachedFile'] != null) ...[
              const SizedBox(height: 8),
              GestureDetector(
                onTap: () {
                  HapticFeedback.selectionClick();
                  _viewContent(post['attachedFile']);
                },
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: post['fileType'] == 'image'
                      ? CachedNetworkImage(
                    imageUrl: '${_hobbyService.baseUrl}${post['attachedFile']}',
                    fit: BoxFit.cover,
                    width: double.infinity,
                    height: 200,
                    placeholder: (context, url) => Shimmer.fromColors(
                      baseColor: Colors.grey[300]!,
                      highlightColor: Colors.grey[100]!,
                      child: Container(color: Colors.white),
                    ),
                    errorWidget: (context, url, error) =>
                    const Icon(Icons.error, color: Colors.grey),
                  )
                      : Stack(
                    alignment: Alignment.center,
                    children: [
                      Container(
                        width: double.infinity,
                        height: 200,
                        color: Colors.grey[300],
                      ),
                      const Icon(Icons.play_circle_fill, size: 50, color: Colors.teal),
                    ],
                  ),
                ),
              ),
            ],
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    IconButton(
                      icon: Icon(
                        post['isLiked'] ? Icons.favorite : Icons.favorite_border,
                        color: post['isLiked'] ? Colors.red : Colors.grey,
                      ),
                      onPressed: () {
                        HapticFeedback.selectionClick();
                        setState(() {
                          post['isLiked'] = !post['isLiked'];
                          post['likes'] = post['isLiked']
                              ? (post['likes'] as int) + 1
                              : (post['likes'] as int) - 1;
                        });
                        _hobbyService.updatePost(post['_id'], post);
                        if (post['isLiked']) {
                          showDialog(
                            context: context,
                            builder: (context) => const HeartPulseAnimation(),
                          );
                          Future.delayed(const Duration(seconds: 1), () {
                            Navigator.pop(context);
                          });
                        }
                      },
                    ),
                    Text('${post['likes']}'),
                    const SizedBox(width: 16),
                    IconButton(
                      icon: const Icon(Icons.comment, color: Colors.grey),
                      onPressed: () {
                        HapticFeedback.selectionClick();
                        _showCommentDialog(post, index);
                      },
                    ),
                    Text('${post['comments'].length}'),
                  ],
                ),
                IconButton(
                  icon: const Icon(Icons.share, color: Colors.grey),
                  onPressed: () {
                    HapticFeedback.selectionClick();
                    Share.share(
                      '${post['postContent']}\n${post['attachedFile'] != null ? "${_hobbyService.baseUrl}${post['attachedFile']}" : ""}',
                      subject: 'منشور من ${post['memberName']}',
                    );
                  },
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(Icons.visibility, color: Colors.grey[600], size: 16),
                const SizedBox(width: 4),
                Text(
                  '${post['views']} مشاهدة',
                  style: TextStyle(color: Colors.grey[600], fontSize: 12),
                ),
                const SizedBox(width: 16),
                Icon(Icons.star, color: Colors.grey[600], size: 16),
                const SizedBox(width: 4),
                Text(
                  '${post['points']} نقطة',
                  style: TextStyle(color: Colors.grey[600], fontSize: 12),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _showCommentDialog(Map<String, dynamic> post, int index) {
    final TextEditingController commentController = TextEditingController();
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        title: const Text('التعليقات'),
        content: SizedBox(
          width: double.maxFinite,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Flexible(
                child: SingleChildScrollView(
                  child: Column(
                    children: post['comments'].asMap().entries.map((entry) {
                      final commentIndex = entry.key;
                      final comment = entry.value;
                      return ListTile(
                        leading: const Icon(Icons.person, color: Colors.teal),
                        title: Text(comment),
                        trailing: IconButton(
                          icon: const Icon(Icons.delete, color: Colors.red),
                          onPressed: () {
                            HapticFeedback.selectionClick();
                            setState(() {
                              post['comments'].removeAt(commentIndex);
                            });
                            _hobbyService.updatePost(post['_id'], post);
                            Navigator.pop(context);
                            _showCommentDialog(post, index);
                          },
                        ),
                      );
                    }).toList(),
                  ),
                ),
              ),
              const SizedBox(height: 8),
              TextField(
                controller: commentController,
                decoration: InputDecoration(
                  hintText: 'أضف تعليقًا...',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.grey[100],
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء', style: TextStyle(color: Colors.grey)),
          ),
          ScaleTransitionButton(
            onPressed: () {
              if (commentController.text.isNotEmpty) {
                HapticFeedback.selectionClick();
                setState(() {
                  post['comments'].add(commentController.text);
                });
                _hobbyService.updatePost(post['_id'], post);
                Navigator.pop(context);
                _showSnackBar('تم إضافة التعليق', Colors.teal);
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.teal,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
            ),
            child: const Text('إرسال', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  Widget _buildFloatingActionButtons() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Tooltip(
          message: 'إضافة محتوى جديد',
          child: ScaleTransitionButton(
            onPressed: () {
              HapticFeedback.selectionClick();
              _showAddContentDialog();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.teal,
              shape: const CircleBorder(),
              padding: const EdgeInsets.all(16),
            ),
            child: const Icon(Icons.add, color: Colors.white),
          ),
        ),
        const SizedBox(height: 8),
        Tooltip(
          message: 'عرض الدردشة',
          child: ScaleTransitionButton(
            onPressed: () {
              HapticFeedback.selectionClick();
              _showChatDialog();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.purple,
              shape: const CircleBorder(),
              padding: const EdgeInsets.all(16),
            ),
            child: const Icon(Icons.chat, color: Colors.white),
          ),
        ),
      ],
    );
  }

  void _showAddContentDialog() {
    final TextEditingController titleController = TextEditingController();
    final TextEditingController descriptionController = TextEditingController();
    String contentType = 'فيديو';
    PlatformFile? selectedFile;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        title: const Text('إضافة محتوى جديد'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: titleController,
                decoration: InputDecoration(
                  hintText: 'عنوان المحتوى',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.grey[100],
                ),
              ),
              const SizedBox(height: 8),
              DropdownButton(
                value: contentType,
                isExpanded: true,
                items: ['فيديو', 'دليل', 'مقالة', 'صورة', 'بث مباشر']
                    .map((type) => DropdownMenuItem(value: type, child: Text(type)))
                    .toList(),
                onChanged: (value) {
                  HapticFeedback.selectionClick();
                  setState(() {
                    contentType = value!;
                  });
                },
                borderRadius: BorderRadius.circular(10),
                underline: Container(),
              ),
              const SizedBox(height: 8),
              TextField(
                controller: descriptionController,
                maxLines: 3,
                decoration: InputDecoration(
                  hintText: 'وصف المحتوى (اختياري)',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.grey[100],
                ),
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  ScaleTransitionButton(
                    onPressed: () async {
                      HapticFeedback.selectionClick();
                      FilePickerResult? result = await FilePicker.platform.pickFiles(
                        type: contentType == 'صورة'
                            ? FileType.image
                            : contentType == 'فيديو' || contentType == 'بث مباشر'
                            ? FileType.video
                            : FileType.any,
                        allowCompression: true,
                      );
                      if (result != null) {
                        final file = result.files.single;
                        if (file.size > 10 * 1024 * 1024) {
                          _showSnackBar(
                            'حجم الملف كبير جدًا (يجب أن يكون أقل من 10 ميجابايت)',
                            Colors.orange,
                          );
                          return;
                        }
                        setState(() {
                          selectedFile = file;
                        });
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.teal,
                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
                    ),
                    child: const Text('إرفاق ملف', style: TextStyle(color: Colors.white)),
                  ),
                  if (selectedFile != null) ...[
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        selectedFile!.name,
                        overflow: TextOverflow.ellipsis,
                        style: const TextStyle(color: Colors.teal),
                      ),
                    ),
                  ],
                ],
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء', style: TextStyle(color: Colors.grey)),
          ),
          ScaleTransitionButton(
            onPressed: () async {
              if (titleController.text.isNotEmpty) {
                try {
                  await _hobbyService.createContent(
                    titleController.text,
                    contentType,
                    descriptionController.text,
                    selectedFile,
                    _selectedMemberId,
                  );
                  Navigator.pop(context);
                  _showSnackBar('تم إضافة المحتوى بنجاح', Colors.green);
                  showDialog(
                    context: context,
                    builder: (context) => const CheckmarkPulseAnimation(),
                  );
                  await Future.delayed(const Duration(seconds: 1));
                  Navigator.pop(context);
                  await _fetchData();
                } catch (e) {
                  _showSnackBar('فشل في إضافة المحتوى: $e', Colors.red);
                }
              } else {
                _showSnackBar('يرجى إدخال عنوان المحتوى', Colors.orange);
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.teal,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
            ),
            child: const Text('إضافة', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  void _showChatDialog() {
    final TextEditingController chatController = TextEditingController();
    final selectedPosts = _memberPosts.where((post) => post['selectedForChat']).toList();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        title: const Text('الدردشة حول المنشورات'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (selectedPosts.isEmpty)
                const Text('لم يتم تحديد منشورات للدردشة', style: TextStyle(color: Colors.grey)),
              ...selectedPosts.map((post) => ListTile(
                leading: const Icon(Icons.post_add, color: Colors.teal),
                title: Text(post['postContent']),
                subtitle: Text('بواسطة: ${post['memberName']}'),
                trailing: IconButton(
                  icon: const Icon(Icons.remove_circle, color: Colors.red),
                  onPressed: () {
                    HapticFeedback.selectionClick();
                    setState(() {
                      post['selectedForChat'] = false;
                    });
                    Navigator.pop(context);
                    _showChatDialog();
                  },
                ),
              )),
              const SizedBox(height: 8),
              TextField(
                controller: chatController,
                decoration: InputDecoration(
                  hintText: 'اكتب رسالتك...',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.grey[100],
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء', style: TextStyle(color: Colors.grey)),
          ),
          ScaleTransitionButton(
            onPressed: () {
              if (chatController.text.isNotEmpty) {
                HapticFeedback.selectionClick();
                _showSnackBar('تم إرسال الرسالة', Colors.teal);
                Navigator.pop(context);
              } else {
                _showSnackBar('يرجى كتابة رسالة', Colors.orange);
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.teal,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
            ),
            child: const Text('إرسال', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  void _viewContent(String? filePath) {
    if (filePath != null) {
      _showSnackBar('يتم فتح المحتوى: $filePath', Colors.teal);
// Placeholder for actual content viewing logic (e.g., open video player, image viewer, etc.)
    } else {
      _showSnackBar('المحتوى غير متاح', Colors.red);
    }
  }

  void _downloadFile(String? filePath) {
    if (filePath != null) {
      _showSnackBar('يتم تحميل الملف: $filePath', Colors.teal);
// Placeholder for actual file download logic
    } else {
      _showSnackBar('الملف غير متاح', Colors.red);
    }
  }

  void _showContentPreview(BuildContext context, HobbyContent content) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        title: Text(content.title),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (content.filePath != null)
                content.type == 'صورة'
                    ? CachedNetworkImage(
                  imageUrl: '${_hobbyService.baseUrl}${content.filePath}',
                  fit: BoxFit.cover,
                  width: double.infinity,
                  height: 200,
                  placeholder: (context, url) => Shimmer.fromColors(
                    baseColor: Colors.grey[300]!,
                    highlightColor: Colors.grey[100]!,
                    child: Container(color: Colors.white),
                  ),
                  errorWidget: (context, url, error) =>
                  const Icon(Icons.error, color: Colors.grey),
                )
                    : content.type == 'فيديو' || content.type == 'بث مباشر'
                    ? Stack(
                  alignment: Alignment.center,
                  children: [
                    Container(
                      width: double.infinity,
                      height: 200,
                      color: Colors.grey[300],
                    ),
                    const Icon(Icons.play_circle_fill, size: 50, color: Colors.teal),
                  ],
                )
                    : const Icon(Icons.insert_drive_file, size: 50, color: Colors.teal),
              const SizedBox(height: 8),
              Text(
                content.description ?? 'لا يوجد وصف',
                style: TextStyle(color: Colors.grey[600]),
              ),
              const SizedBox(height: 8),
              Text(
                'بواسطة: ${widget.members.firstWhere((m) => m.id == content.creatorId, orElse: () => Member(id: content.creatorId, name: 'غير معروف')).name}',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              Text(
                timeago.format(content.createdAt, locale: 'ar'),
                style: TextStyle(color: Colors.grey[600]),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق', style: TextStyle(color: Colors.grey)),
          ),
          ScaleTransitionButton(
            onPressed: () {
              Navigator.pop(context);
              _viewContent(content.filePath);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.teal,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
            ),
            child: const Text('عرض', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }
}