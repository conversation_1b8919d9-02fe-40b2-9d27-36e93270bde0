const { S3Client, ListBucketsCommand } = require("@aws-sdk/client-s3");
const { getSignedUrl } = require('@aws-sdk/s3-request-presigner');
require('dotenv').config();

const s3 = new S3Client({
  region: process.env.AWS_REGION || 'us-east-1',
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID || 'default-key',
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || 'default-secret',
  },
});

// اختبار الاتصال
const testConnection = async () => {
  try {
    const command = new ListBucketsCommand({});
    const data = await s3Client.send(command);
    console.log('S3 connected successfully');
    console.log('Buckets:', data.Buckets);
  } catch (err) {
    console.error('Error connecting to S3:', err);
  }
};
testConnection();

const uploadFile = async (file, key) => {
  const params = {
    Bucket: process.env.S3_BUCKET_NAME,
    Key: key,
    Body: file,
    ACL: 'public-read',
  };
  const command = new PutObjectCommand(params);
  await s3Client.send(command);
  return `https://${process.env.S3_BUCKET_NAME}.s3.${process.env.AWS_REGION}.amazonaws.com/${key}`;
};

const getFileUrl = async (key) => {
  const params = {
    Bucket: process.env.S3_BUCKET_NAME,
    Key: key,
  };
  const command = new GetObjectCommand(params);
  return await getSignedUrl(s3Client, command, { expiresIn: 3600 });
};

module.exports = { s3: s3Client, uploadFile, getFileUrl };

console.log('Access Key:', process.env.AWS_ACCESS_KEY_ID);
console.log('Secret Key:', process.env.AWS_SECRET_ACCESS_KEY);
console.log('Region:', process.env.AWS_REGION);
console.log('Bucket:', process.env.S3_BUCKET_NAME);