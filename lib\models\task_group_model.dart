import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';
import 'user_model.dart';
import 'group_task_model.dart';

enum GroupType {
  cooking('الطبخ', Icons.food_bank, Colors.orange),
  shopping('المبيعات', Icons.shopping_cart, Colors.green),
  sports('الرياضة', Icons.sports, Colors.blue),
  mentalSupport('الدعم النفسي', Icons.favorite, Colors.pink),
  study('الدراسة', Icons.book, Colors.purple),
  entertainment('الترفيه', Icons.tv, Colors.green),
  technology('التكنولوجيا', Icons.laptop, Colors.blue),
  travel('السفر', Icons.airplane_ticket, Colors.cyan),
  work('العمل', Icons.work, Colors.yellow),
  health('الصحة', Icons.health_and_safety, Colors.red),
  homework('الأعمال المنزلية', Icons.home, Colors.teal),
  photography('التصوير', Icons.camera_alt, Colors.grey),
  art('الفن', Icons.brush, Colors.red),
  music('ترتيل', Icons.music_note, Colors.yellow),
  gaming('الألعاب', Icons.videogame_asset, Colors.indigo),
  other('أخرى', Icons.category, Colors.teal);

  final String label;
  final IconData icon;
  final Color color;

  const GroupType(this.label, this.icon, this.color);
}

class TaskGroup {
  final String id;
  final String name;
  final String description;
  final String videoUrl;
  final List<UserModel> members;
  final GroupType type;
  final int points;
  final List<Map<String, String>> media;
  final Map<String, List<GroupTask>> tasksByUser;
  final DateTime? createdAt;
  final String? createdBy;
  final String? coverImage;
  final bool isPrivate;

  TaskGroup({
    required this.id,
    required this.name,
    required this.description,
    this.videoUrl = '',
    required this.members,
    required this.type,
    this.points = 0,
    this.media = const [],
    this.tasksByUser = const {},
    this.createdAt,
    this.createdBy,
    this.coverImage,
    this.isPrivate = false,
  });

  factory TaskGroup.fromJson(Map<String, dynamic> json) {
    return TaskGroup(
      id: json['id'] ?? const Uuid().v4(),
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      videoUrl: json['videoUrl'] ?? '',
      members: (json['members'] as List<dynamic>?)
              ?.map((m) => UserModel.fromJson(m as Map<String, dynamic>))
              .toList() ??
          [],
      type: GroupType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => GroupType.other,
      ),
      points: json['points']?.toInt() ?? 0,
      media: (json['media'] as List<dynamic>?)
              ?.map((m) => Map<String, String>.from(m))
              .toList() ??
          [],
      tasksByUser: (json['tasksByUser'] as Map<String, dynamic>?)?.map(
            (key, value) => MapEntry(
              key,
              (value as List<dynamic>)
                  .map((t) => GroupTask.fromJson(t as Map<String, dynamic>))
                  .toList(),
            ),
          ) ??
          {},
      createdAt:
          json['createdAt'] != null ? DateTime.parse(json['createdAt']) : null,
      createdBy: json['createdBy'],
      coverImage: json['coverImage'],
      isPrivate: json['isPrivate'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'videoUrl': videoUrl,
      'members': members.map((m) => m.toJson()).toList(),
      'type': type.name,
      'points': points,
      'media': media,
      'tasksByUser': tasksByUser.map(
        (key, value) => MapEntry(key, value.map((t) => t.toJson()).toList()),
      ),
      'createdAt': createdAt?.toIso8601String(),
      'createdBy': createdBy,
      'coverImage': coverImage,
      'isPrivate': isPrivate,
    };
  }

  static TaskGroup fromMap(Map<String, dynamic> map) {
    return TaskGroup.fromJson(map);
  }

  TaskGroup copyWith({
    String? id,
    String? name,
    String? description,
    String? videoUrl,
    List<UserModel>? members,
    GroupType? type,
    int? points,
    List<Map<String, String>>? media,
    Map<String, List<GroupTask>>? tasksByUser,
    DateTime? createdAt,
    String? createdBy,
    String? coverImage,
    bool? isPrivate,
  }) {
    return TaskGroup(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      videoUrl: videoUrl ?? this.videoUrl,
      members: members ?? this.members,
      type: type ?? this.type,
      points: points ?? this.points,
      media: media ?? this.media,
      tasksByUser: tasksByUser ?? this.tasksByUser,
      createdAt: createdAt ?? this.createdAt,
      createdBy: createdBy ?? this.createdBy,
      coverImage: coverImage ?? this.coverImage,
      isPrivate: isPrivate ?? this.isPrivate,
    );
  }
}
