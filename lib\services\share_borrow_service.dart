import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:http_parser/http_parser.dart';
import 'package:socket_io_client/socket_io_client.dart' as io;

// API configuration
class ApiConfig {
  static const String baseUrl = 'http://localhost:3000/api';
  static const String socketUrl = 'http://localhost:3000';
}

class ShareBorrowService {
  final String baseUrl = ApiConfig.baseUrl;
  
  // Fetch items with filtering, sorting, and pagination
  Future<Map<String, dynamic>> fetchItems({
    String search = '',
    String filter = 'all',
    String sort = 'newest',
    int skip = 0,
    int limit = 10,
    double? userLat,
    double? userLon,
  }) async {
    try {
      final queryParams = {
        'search': search,
        'filter': filter,
        'sort': sort,
        'skip': skip.toString(),
        'limit': limit.toString(),
      };
      
      if (userLat != null && userLon != null) {
        queryParams['userLat'] = userLat.toString();
        queryParams['userLon'] = userLon.toString();
      }
      
      final uri = Uri.parse('$baseUrl/share-borrow-items').replace(
        queryParameters: queryParams,
      );
      
      final response = await http.get(
        uri,
        headers: {'Content-Type': 'application/json'},
      );
      
      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception('Failed to load items: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error connecting to server: $e');
    }
  }
  
  // Fetch borrow history for an item
  Future<List<dynamic>> fetchBorrowHistory(String itemId) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/share-borrow-items/$itemId/borrow-history'),
        headers: {'Content-Type': 'application/json'},
      );
      
      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception('Failed to load borrow history: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error connecting to server: $e');
    }
  }
  
  // Fetch user details
  Future<dynamic> fetchUser(String userId) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/users/$userId'),
        headers: {'Content-Type': 'application/json'},
      );
      
      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception('Failed to load user: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error connecting to server: $e');
    }
  }
  
  // Create a new item
  Future<dynamic> createItem(
    String title,
    String description,
    String location,
    String category,
    File file,
    String condition,
    int maxBorrowDays,
    double latitude,
    double longitude,
    String creatorId,
  ) async {
    try {
      var request = http.MultipartRequest(
        'POST',
        Uri.parse('$baseUrl/share-borrow-items'),
      );
      
      // Add text fields
      request.fields['title'] = title;
      request.fields['description'] = description;
      request.fields['location'] = location;
      request.fields['category'] = category;
      request.fields['condition'] = condition;
      request.fields['maxBorrowDays'] = maxBorrowDays.toString();
      request.fields['latitude'] = latitude.toString();
      request.fields['longitude'] = longitude.toString();
      request.fields['creatorId'] = creatorId;
      
      // Add file
      String fileExtension = file.path.split('.').last;
      request.files.add(
        await http.MultipartFile.fromPath(
          'file',
          file.path,
          contentType: MediaType('image', fileExtension),
        ),
      );
      
      var streamedResponse = await request.send();
      var response = await http.Response.fromStream(streamedResponse);
      
      if (response.statusCode == 201) {
        return jsonDecode(response.body);
      } else {
        throw Exception('Failed to create item: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error connecting to server: $e');
    }
  }
  
  // Upload verification media
  Future<dynamic> uploadVerificationMedia(
    String itemId,
    File file,
    String uploaderId,
  ) async {
    try {
      var request = http.MultipartRequest(
        'POST',
        Uri.parse('$baseUrl/share-borrow-items/verification-media'),
      );
      
      // Add text fields
      request.fields['itemId'] = itemId;
      request.fields['uploaderId'] = uploaderId;
      
      // Add file
      String fileExtension = file.path.split('.').last;
      request.files.add(
        await http.MultipartFile.fromPath(
          'file',
          file.path,
          contentType: MediaType('image', fileExtension),
        ),
      );
      
      var streamedResponse = await request.send();
      var response = await http.Response.fromStream(streamedResponse);
      
      if (response.statusCode == 201) {
        return jsonDecode(response.body);
      } else {
        throw Exception('Failed to upload verification media: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error connecting to server: $e');
    }
  }
  
  // Create a sharing group
  Future<dynamic> createSharingGroup(
    String name,
    String description,
    String location,
    String creatorId,
  ) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/sharing-groups'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'name': name,
          'description': description,
          'location': location,
          'creatorId': creatorId,
        }),
      );
      
      if (response.statusCode == 201) {
        return jsonDecode(response.body);
      } else {
        throw Exception('Failed to create sharing group: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error connecting to server: $e');
    }
  }
  
  // Fetch sharing groups
  Future<List<dynamic>> fetchSharingGroups() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/sharing-groups'),
        headers: {'Content-Type': 'application/json'},
      );
      
      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception('Failed to load sharing groups: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error connecting to server: $e');
    }
  }
  
  // Log analytics event
  Future<dynamic> logAnalyticsEvent(
    String type,
    String itemId,
    String userId,
  ) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/analytics'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'type': type,
          'itemId': itemId,
          'userId': userId,
        }),
      );
      
      if (response.statusCode == 201) {
        return jsonDecode(response.body);
      } else {
        throw Exception('Failed to log analytics event: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error connecting to server: $e');
    }
  }
  
  // Fetch analytics
  Future<dynamic> fetchAnalytics() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/analytics'),
        headers: {'Content-Type': 'application/json'},
      );
      
      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception('Failed to load analytics: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error connecting to server: $e');
    }
  }
  
  // Request to borrow an item
  Future<dynamic> requestBorrow(
    String itemId,
    String borrowerId,
    DateTime startDate,
    int days,
  ) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/share-borrow-items/borrow-request'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'itemId': itemId,
          'borrowerId': borrowerId,
          'startDate': startDate.toIso8601String(),
          'days': days,
        }),
      );
      
      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception('Failed to request borrow: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error connecting to server: $e');
    }
  }
  
  // Rate a user
  Future<dynamic> rateUser(String userId, double rating) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/users/$userId/rate'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'rating': rating,
        }),
      );
      
      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception('Failed to rate user: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error connecting to server: $e');
    }
  }
  
  // Delete an item
  Future<void> deleteItem(String id) async {
    try {
      final response = await http.delete(
        Uri.parse('$baseUrl/share-borrow-items/$id'),
        headers: {'Content-Type': 'application/json'},
      );
      
      if (response.statusCode != 200) {
        throw Exception('Failed to delete item: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error connecting to server: $e');
    }
  }
  
  // Setup socket listeners
  void setupSocketListeners(io.Socket socket, Function(dynamic) onNewItem) {
    socket.on('newItem', (data) {
      onNewItem(data);
    });
  }
}
