import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../appstate.dart';
import '../models/post_model.dart';
import '../models/user_model.dart';

class PostPrivacySelector extends StatefulWidget {
  final PostVisibility initialVisibility;
  final Function(PostVisibility) onVisibilityChanged;
  final List<String>? selectedUsers;
  final Function(List<String>)? onUsersSelected;
  final bool showLabel;

  const PostPrivacySelector({
    super.key,
    this.initialVisibility = PostVisibility.public,
    required this.onVisibilityChanged,
    this.selectedUsers,
    this.onUsersSelected,
    this.showLabel = true,
  });

  @override
  _PostPrivacySelectorState createState() => _PostPrivacySelectorState();
}

class _PostPrivacySelectorState extends State<PostPrivacySelector> {
  late PostVisibility _selectedVisibility;
  List<String> _selectedUserIds = [];

  @override
  void initState() {
    super.initState();
    _selectedVisibility = widget.initialVisibility;
    _selectedUserIds = widget.selectedUsers ?? [];
  }

  @override
  void didUpdateWidget(PostPrivacySelector oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.initialVisibility != widget.initialVisibility) {
      setState(() {
        _selectedVisibility = widget.initialVisibility;
      });
    }
    if (oldWidget.selectedUsers != widget.selectedUsers) {
      setState(() {
        _selectedUserIds = widget.selectedUsers ?? [];
      });
    }
  }

  Future<void> _selectUsers() async {
    final appState = Provider.of<AppState>(context, listen: false);
    final result = await showDialog<List<String>>(
      context: context,
      builder: (context) => SelectUsersDialog(
        initialSelection: _selectedUserIds,
        friends: appState.friends,
      ),
    );

    if (result != null) {
      setState(() {
        _selectedUserIds = result;
      });
      widget.onUsersSelected?.call(_selectedUserIds);
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.showLabel) ...[
          Text(
            'الخصوصية',
            style: theme.textTheme.titleMedium,
          ),
          const SizedBox(height: 8),
        ],
        DropdownButtonFormField<PostVisibility>(
          value: _selectedVisibility,
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          ),
          items: PostVisibility.values.map((visibility) {
            return DropdownMenuItem<PostVisibility>(
              value: visibility,
              child: Row(
                children: [
                  _getVisibilityIcon(visibility),
                  const SizedBox(width: 8),
                  Text(visibility.label),
                ],
              ),
            );
          }).toList(),
          onChanged: (value) {
            if (value != null) {
              setState(() {
                _selectedVisibility = value;
              });
              widget.onVisibilityChanged(value);
            }
          },
        ),
        if (_selectedVisibility == PostVisibility.specificFriends) ...[
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: _selectUsers,
                  icon: const Icon(Icons.people_outline),
                  label: Text(
                    _selectedUserIds.isEmpty
                        ? 'اختر الأصدقاء'
                        : '${_selectedUserIds.length} أصدقاء محددين',
                  ),
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  Widget _getVisibilityIcon(PostVisibility visibility) {
    switch (visibility) {
      case PostVisibility.public:
        return const Icon(Icons.public, size: 20);
      case PostVisibility.friends:
        return const Icon(Icons.people, size: 20);
      case PostVisibility.specificFriends:
        return const Icon(Icons.people_alt_outlined, size: 20);
      case PostVisibility.onlyMe:
        return const Icon(Icons.lock_outline, size: 20);
      case PostVisibility.custom:
        return const Icon(Icons.settings, size: 20);
    }
  }
}

class SelectUsersDialog extends StatefulWidget {
  final List<String> initialSelection;
  final List<UserModel> friends;

  const SelectUsersDialog({
    super.key,
    required this.initialSelection,
    required this.friends,
  });

  @override
  _SelectUsersDialogState createState() => _SelectUsersDialogState();
}

class _SelectUsersDialogState extends State<SelectUsersDialog> {
  late List<String> _selectedUserIds;

  @override
  void initState() {
    super.initState();
    _selectedUserIds = List.from(widget.initialSelection);
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('اختر الأصدقاء'),
      content: SizedBox(
        width: double.maxFinite,
        child: ListView.builder(
          shrinkWrap: true,
          itemCount: widget.friends.length,
          itemBuilder: (context, index) {
            final friend = widget.friends[index];
            final isSelected = _selectedUserIds.contains(friend.id);
            return CheckboxListTile(
              title: Text(friend.name),
              value: isSelected,
              onChanged: (value) {
                setState(() {
                  if (value == true) {
                    _selectedUserIds.add(friend.id);
                  } else {
                    _selectedUserIds.remove(friend.id);
                  }
                });
              },
            );
          },
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('إلغاء'),
        ),
        TextButton(
          onPressed: () => Navigator.pop(context, _selectedUserIds),
          child: const Text('حفظ'),
        ),
      ],
    );
  }
}