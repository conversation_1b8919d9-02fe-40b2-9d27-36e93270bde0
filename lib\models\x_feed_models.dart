// نماذج البيانات لصفحة X Feed المتكاملة مع الباك إند

// نموذج المستخدم
class XPostUser {
  final String id;
  final String name;
  final String? avatarUrl;
  final String? handle;

  XPostUser({
    required this.id,
    required this.name,
    this.avatarUrl,
    this.handle,
  });

  factory XPostUser.fromJson(Map<String, dynamic> json) {
    return XPostUser(
      id: json['_id'] ?? json['id'] ?? '',
      name: json['name'] ?? 'مستخدم غير معروف',
      avatarUrl: json['avatarUrl'],
      handle: json['handle'] ?? '@${json['name']?.replaceAll(' ', '_').toLowerCase()}',
    );
  }

  // للتوافق مع الواجهة القديمة
  String get username => name;
  String get avatar => avatarUrl ?? 'https://via.placeholder.com/50';
}

// نموذج التعليق
class XPostComment {
  final String id;
  final String username;
  final String content;
  final DateTime date;

  XPostComment({
    required this.id,
    required this.username,
    required this.content,
    required this.date,
  });

  factory XPostComment.fromJson(Map<String, dynamic> json) {
    return XPostComment(
      id: json['_id'] ?? DateTime.now().millisecondsSinceEpoch.toString(),
      username: json['username'] ?? 'مستخدم غير معروف',
      content: json['content'] ?? '',
      date: json['date'] != null ? DateTime.parse(json['date']) : DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'username': username,
      'content': content,
      'date': date.toIso8601String(),
    };
  }

  // للتوافق مع الواجهة القديمة
  String get time {
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inHours < 1) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inDays < 1) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} يوم';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }
}

// نموذج المنشور المتكامل
class XPost {
  final String id;
  final String userId;
  final String content;
  final String? mediaUrl;
  final String? mediaType;
  final int likes;
  final int loves;
  final int haha;
  final List<XPostComment> comments;
  final DateTime createdAt;
  final XPostUser? author;
  final bool promoted;
  final String privacy;
  final List<String> hashtags;
  final double? latitude;
  final double? longitude;
  
  // حالات محلية للواجهة
  bool isLiked;
  bool isLoved;
  bool isHaha;
  bool isSaved;
  bool isPinned;

  XPost({
    required this.id,
    required this.userId,
    required this.content,
    this.mediaUrl,
    this.mediaType,
    required this.likes,
    required this.loves,
    required this.haha,
    required this.comments,
    required this.createdAt,
    this.author,
    required this.promoted,
    required this.privacy,
    required this.hashtags,
    this.latitude,
    this.longitude,
    this.isLiked = false,
    this.isLoved = false,
    this.isHaha = false,
    this.isSaved = false,
    this.isPinned = false,
  });

  factory XPost.fromJson(Map<String, dynamic> json) {
    return XPost(
      id: json['_id'] ?? json['id'] ?? '',
      userId: json['userId'] ?? '',
      content: json['content'] ?? '',
      mediaUrl: json['mediaUrl'],
      mediaType: json['mediaType'],
      likes: json['likes'] ?? 0,
      loves: json['loves'] ?? 0,
      haha: json['haha'] ?? 0,
      comments: (json['comments'] as List<dynamic>?)
          ?.map((comment) => XPostComment.fromJson(comment))
          .toList() ?? [],
      createdAt: json['createdAt'] != null 
          ? DateTime.parse(json['createdAt']) 
          : DateTime.now(),
      author: json['author'] != null 
          ? XPostUser.fromJson(json['author']) 
          : null,
      promoted: json['promoted'] ?? false,
      privacy: json['privacy'] ?? 'public',
      hashtags: (json['hashtags'] as List<dynamic>?)
          ?.map((tag) => tag.toString())
          .toList() ?? [],
      latitude: json['latitude']?.toDouble(),
      longitude: json['longitude']?.toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'content': content,
      'mediaUrl': mediaUrl,
      'mediaType': mediaType,
      'privacy': privacy,
      'latitude': latitude,
      'longitude': longitude,
    };
  }

  // الحصول على عدد التفاعلات الإجمالي
  int get totalInteractions => likes + loves + haha + comments.length;

  // للتوافق مع الواجهة القديمة
  String get username => author?.username ?? 'مستخدم غير معروف';
  String get handle => author?.handle ?? '@unknown';
  String get avatar => author?.avatar ?? 'https://via.placeholder.com/50';
  String? get media => mediaUrl;
  int get retweets => 0; // لا يوجد في الباك إند حالياً
  
  // الحصول على الوقت المنسق
  String get time {
    final now = DateTime.now();
    final difference = now.difference(createdAt);
    
    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inHours < 1) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inDays < 1) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} يوم';
    } else {
      return '${createdAt.day}/${createdAt.month}/${createdAt.year}';
    }
  }

  // تحويل التعليقات للتوافق مع الواجهة القديمة
  List<Map<String, String>> get replies {
    return comments.map((comment) => {
      'username': comment.username,
      'content': comment.content,
      'time': comment.time,
    }).toList();
  }

  // نسخ المنشور مع تحديث بعض القيم
  XPost copyWith({
    int? likes,
    int? loves,
    int? haha,
    List<XPostComment>? comments,
    bool? isLiked,
    bool? isLoved,
    bool? isHaha,
    bool? isSaved,
    bool? isPinned,
  }) {
    return XPost(
      id: id,
      userId: userId,
      content: content,
      mediaUrl: mediaUrl,
      mediaType: mediaType,
      likes: likes ?? this.likes,
      loves: loves ?? this.loves,
      haha: haha ?? this.haha,
      comments: comments ?? this.comments,
      createdAt: createdAt,
      author: author,
      promoted: promoted,
      privacy: privacy,
      hashtags: hashtags,
      latitude: latitude,
      longitude: longitude,
      isLiked: isLiked ?? this.isLiked,
      isLoved: isLoved ?? this.isLoved,
      isHaha: isHaha ?? this.isHaha,
      isSaved: isSaved ?? this.isSaved,
      isPinned: isPinned ?? this.isPinned,
    );
  }
}

// نموذج الاستجابة من API
class PostsResponse {
  final bool success;
  final int count;
  final List<XPost> posts;
  final Map<String, dynamic>? pagination;

  PostsResponse({
    required this.success,
    required this.count,
    required this.posts,
    this.pagination,
  });

  factory PostsResponse.fromJson(Map<String, dynamic> json) {
    return PostsResponse(
      success: json['success'] ?? false,
      count: json['count'] ?? 0,
      posts: (json['posts'] as List<dynamic>?)
          ?.map((post) => XPost.fromJson(post))
          .toList() ?? [],
      pagination: json['pagination'],
    );
  }
}
