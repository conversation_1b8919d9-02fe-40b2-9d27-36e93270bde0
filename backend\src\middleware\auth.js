const jwt = require('jsonwebtoken');
const { promisify } = require('util');
const User = require('../models/user.model.new');

/**
 * وحدة المصادقة للتحقق من وجود المستخدم وصلاحية التوكن
 * تدعم كلا من نظام المشاركة والاستعارة والنظام الأساسي
 */

// التحقق من وجود المستخدم وصلاحية التوكن
exports.protect = async (req, res, next) => {
  let token;

  // التحقق من وجود التوكن في الهيدر
  if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
    token = req.headers.authorization.split(' ')[1];
  }

  // التحقق من وجود التوكن
  if (!token) {
    return res.status(401).json({
      success: false,
      message: 'غير مصرح لك بالوصول إلى هذا المسار'
    });
  }

  try {
    // التحقق من صحة التوكن
    const decoded = await promisify(jwt.verify)(token, process.env.JWT_SECRET || 'your-secret-key');

    // التحقق من وجود المستخدم
    const user = await User.findById(decoded.id);
    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'المستخدم الذي ينتمي إليه هذا التوكن لم يعد موجودًا'
      });
    }

    // إضافة المستخدم إلى الطلب
    req.user = user;
    next();
  } catch (err) {
    return res.status(401).json({
      success: false,
      message: 'غير مصرح لك بالوصول إلى هذا المسار'
    });
  }
};

// التحقق من صلاحيات المستخدم
exports.authorize = (...allowedRoles) => {
  return (req, res, next) => {
    // Access denied if user has no role
    if (!req.user || !req.user.role) {
      return res.status(403).json({
        success: false,
        message: 'غير مصرح لك بتنفيذ هذا الإجراء'
      });
    }

    // Create role hierarchy
    const roleHierarchy = {
      'admin': 4,
      'hokama': 3,
      'olama': 2,
      'mogtahdin': 1,
      'medical': 1,
      'member': 0,
      'guest': -1
    };

    // Get the minimum required role level from allowed roles
    const requiredRoleLevel = Math.min(...allowedRoles.map(role => roleHierarchy[role] || -1));
    const userRoleLevel = roleHierarchy[req.user.role] || -1;

    // Allow access if user's role level is >= required level
    if (userRoleLevel >= requiredRoleLevel) {
      return next();
    }

    return res.status(403).json({
      success: false,
      message: 'غير مصرح لك بتنفيذ هذا الإجراء'
    });
  };
};

// للاستخدام مع نظام المشاركة والاستعارة
module.exports = exports.protect;
