const mongoose = require('mongoose');

const RideRequestSchema = new mongoose.Schema({
  pickup: {
    lat: { type: Number, required: true },
    lng: { type: Number, required: true }
  },
  destination: { type: String, required: true },
  destinationLocation: {
    lat: { type: Number },
    lng: { type: Number }
  },
  timestamp: { type: Date, default: Date.now },
  status: { type: String, default: 'Pending' },
  driverId: { type: String },
});

module.exports = mongoose.model('RideRequest', RideRequestSchema);
