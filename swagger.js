const swaggerAutogen = require('swagger-autogen')();

const doc = {
  info: {
    title: 'Social App Backend API',
    description: 'API documentation for a multi-functional social application with features like posts, chats, groups, and more.',
    version: '1.0.0',
  },
  host: 'http://localhost:3000',
  schemes: ['http'],
  securityDefinitions: {
    bearerAuth: {
      type: 'http',
      scheme: 'bearer',
      bearerFormat: 'JWT',
    },
  },
  definitions: {
    User: {
      username: 'exampleUser',
      phoneNumber: '1234567890',
      job: 'developer',
      location: 'Cairo',
      avatarUrl: '/uploads/default-avatar.jpg',
      isOnline: false,
    },
    Post: {
      content: 'This is a sample post',
      media: 'http://s3.amazonaws.com/sample.jpg',
      mediaType: 'image',
      likes: 0,
      comments: [{ username: 'user1', text: 'Great post!' }],
      createdAt: '2025-03-29T12:00:00Z',
    },
    City: {
      name: 'Cairo',
      image: 'http://s3.amazonaws.com/cairo.jpg',
      villages: [{ name: 'Village1', image: 'http://s3.amazonaws.com/village1.jpg' }],
    },
    Error: {
      message: 'Error message',
    },
  },
};

// مسارات ملفات الـ routes في مجلد ways
const endpointsFiles = [
  './backend/routes/authRoutes.js',
  './backend/routes/cities.js',
  './backend/routes/clubRoutes.js',
  './backend/routes/groups.js',
  './backend/routes/postRoutes.js',
  './backend/routes/healthPosts.js',
  './backend/routes/healthTasks.js',
  './backend/routes/healthContent.js',
  './backend/routes/chatRooms.js',
  './backend/routes/chats.js',
  './backend/routes/content.js',
  './backend/routes/contributions.js',
  './backend/routes/natureRoutes.js',
  './backend/routes/notificationRoutes.js',
];

// ملف الإخراج
const outputFile = './swagger.json';

// توليد الوثائق
swaggerAutogen(outputFile, endpointsFiles, doc).then(() => {
  console.log('Swagger documentation generated successfully');
});