const express = require('express');
const router = express.Router();
const { check } = require('express-validator');
const { protect } = require('../middleware/authmiddleware');
const asyncHandler = require('../middleware/asyncmiddleware');
const {
  createNotification,
  getNotifications,
  getUnreadCount,
  markAsRead,
  markAllAsRead,
  deleteNotification,
  getNotificationsByType,
  getRecentNotifications
} = require('../controllers/notification.controller');

// Create a new notification
router.post(
  '/',
  [
    check('userId', 'User ID is required').not().isEmpty(),
    check('type', 'Type is required').not().isEmpty(),
    check('message', 'Message is required').not().isEmpty()
  ],
  protect,
  asyncHandler(createNotification)
);

// Get user notifications with pagination
router.get('/', protect, asyncHandler(getNotifications));

// Get unread notifications count
router.get('/unread-count', protect, asyncHandler(getUnreadCount));

// Get notifications by type
router.get('/type/:type', protect, asyncHandler(getNotificationsByType));

// Get recent notifications
router.get('/recent', protect, asyncHandler(getRecentNotifications));

// Mark a notification as read
router.patch('/:id/read', protect, asyncHandler(markAsRead));
router.put('/:id/read', protect, asyncHandler(markAsRead)); // For backward compatibility

// Mark all notifications as read
router.patch('/mark-all-read', protect, asyncHandler(markAllAsRead));

// Delete a notification
router.delete('/:id', protect, asyncHandler(deleteNotification));

module.exports = router;
