import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:animate_do/animate_do.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_pdfview/flutter_pdfview.dart';
import 'package:http/http.dart' as http;
import 'package:path_provider/path_provider.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:share_plus/share_plus.dart';
import 'package:timeago/timeago.dart' as timeago;
import 'package:youtube_player_flutter/youtube_player_flutter.dart';
import 'appstate.dart';
import 'models/figure_model.dart';

// Initialize timeago for Arabic
void setupTimeago() => timeago.setLocaleMessages('ar', timeago.ArMessages());


// Leader Service
class LeaderService {
  final String baseUrl;

  LeaderService() : baseUrl = AppState.getBackendUrl();

  Future<List<Map<String, dynamic>>> fetchNotifications(String token) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/notifications'),
        headers: {'x-auth-token': token},
      ).timeout(const Duration(seconds: 10));
      if (response.statusCode == 200) {
        return List<Map<String, dynamic>>.from(jsonDecode(response.body));
      }
      throw Exception('فشل في جلب الإشعارات');
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }

  Future<void> markNotificationRead(String token, String notificationId) async {
    try {
      final response = await http.put(
        Uri.parse('$baseUrl/notifications/$notificationId/read'),
        headers: {'x-auth-token': token},
      );
      if (response.statusCode != 200) {
        throw Exception('فشل في تحديث الإشعار');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }

  Future<List<Figure>> fetchFigures(String token, String category,
      {int page = 1, int limit = 20}) async {
    try {
      final response = await http.get(
        Uri.parse(
            '$baseUrl/figures?category=$category&page=$page&limit=$limit'),
        headers: {'x-auth-token': token},
      );
      if (response.statusCode == 200) {
        return (jsonDecode(response.body) as List)
            .map((json) => Figure.fromJson(json))
            .toList();
      }
      throw Exception('فشل في جلب الشخصيات');
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }

  Future<List<Map<String, dynamic>>> fetchUserContributions(
      String token) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/content/مساهمات المستخدمين'),
        headers: {'x-auth-token': token},
      );
      if (response.statusCode == 200) {
        return List<Map<String, dynamic>>.from(jsonDecode(response.body));
      }
      throw Exception('فشل في جلب المساهمات');
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }

  Future<void> addUserContribution(
      String token, String title, File file, String type) async {
    try {
      final request =
          http.MultipartRequest('POST', Uri.parse('$baseUrl/content'));
      request.headers['x-auth-token'] = token;
      request.fields['category'] = 'مساهمات المستخدمين';
      request.fields['title'] = title;
      request.fields['type'] = type;
      if (await file.length() > 5 * 1024 * 1024) {
        throw Exception('حجم الملف كبير جدًا (يجب أن يكون أقل من 5 ميجابايت)');
      }
      request.files.add(await http.MultipartFile.fromPath('file', file.path));
      final response = await request.send();
      if (response.statusCode != 201) {
        throw Exception('فشل في إضافة المساهمة');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }

  Future<void> removeUserContribution(String token, String id) async {
    try {
      final response = await http.delete(
        Uri.parse('$baseUrl/content/$id'),
        headers: {'x-auth-token': token},
      );
      if (response.statusCode != 200) {
        throw Exception('فشل في حذف المساهمة');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }

  Future<void> toggleFavorite(
      String token, String title, bool isFavorite) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/favorites'),
        headers: {'Content-Type': 'application/json', 'x-auth-token': token},
        body: jsonEncode({'title': title, 'isFavorite': isFavorite}),
      );
      if (response.statusCode != 200) {
        throw Exception('فشل في تحديث المفضلة');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }

  Future<bool> isFavorite(String token, String title) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/favorites/$title'),
        headers: {'x-auth-token': token},
      );
      if (response.statusCode == 200) {
        return jsonDecode(response.body)['isFavorite'] ?? false;
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  Future<String?> cachePdf(String url, String title) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final filePath = '${directory.path}/${title}_${url.hashCode}.pdf';
      final file = File(filePath);
      if (await file.exists()) return filePath;
      final response = await http.get(Uri.parse(url));
      if (response.statusCode == 200) {
        await file.writeAsBytes(response.bodyBytes);
        return filePath;
      }
      return null;
    } catch (e) {
      return null;
    }
  }
}

class Rayan14 extends StatefulWidget {
  const Rayan14({super.key});

  @override
  _Rayan14State createState() => _Rayan14State();
}

class _Rayan14State extends State<Rayan14> with TickerProviderStateMixin {
  final LeaderService _leaderService = LeaderService();
  final TextEditingController _searchController = TextEditingController();
  List<Map<String, dynamic>> _notifications = [];
  List<Figure> _rulers = [];
  List<Figure> _leaders = [];
  List<Figure> _fighters = [];
  List<Figure> _sages = [];
  List<Figure> _philosophers = [];
  List<Figure> _scientists = [];
  List<Figure> _religiousFigures = [];
  List<Figure> _documentaries = [];
  List<Figure> _booksAndLetters = [];
  List<Map<String, dynamic>> _userContributions = [];
  bool _isLoading = true;
  Timer? _debounce;
  late AnimationController _searchAnimationController;
  late Animation<double> _searchAnimation;

  @override
  void initState() {
    super.initState();
    setupTimeago();
    _fetchData();
    _searchController.addListener(_onSearch);

    _searchAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    _searchAnimation = Tween<double>(begin: 0.9, end: 1.0).animate(
      CurvedAnimation(
        parent: _searchAnimationController,
        curve: Curves.easeInOut,
      ),
    );

    final appState = Provider.of<AppState>(context, listen: false);
    if (appState.socket != null) {
      appState.socket!.on('new_contribution', (data) => _fetchData());
      appState.socket!.on('new_notification', (data) {
        setState(() {
          _notifications.add({
            'message': data['message'] ?? 'إشعار جديد',
            'date': DateTime.now().toString(),
            'read': false,
            '_id': data['notificationId'] ?? DateTime.now().toString(),
          });
        });
      });
    }
  }

  Future<void> _fetchData() async {
    setState(() => _isLoading = true);
    final appState = Provider.of<AppState>(context, listen: false);
    try {
      await Future.wait([
        _leaderService
            .fetchFigures(appState.token ?? '', 'rulers')
            .then((value) => _rulers = value),
        _leaderService
            .fetchFigures(appState.token ?? '', 'leaders')
            .then((value) => _leaders = value),
        _leaderService
            .fetchFigures(appState.token ?? '', 'fighters')
            .then((value) => _fighters = value),
        _leaderService
            .fetchFigures(appState.token ?? '', 'sages')
            .then((value) => _sages = value),
        _leaderService
            .fetchFigures(appState.token ?? '', 'philosophers')
            .then((value) => _philosophers = value),
        _leaderService
            .fetchFigures(appState.token ?? '', 'scientists')
            .then((value) => _scientists = value),
        _leaderService
            .fetchFigures(appState.token ?? '', 'religiousFigures')
            .then((value) => _religiousFigures = value),
        _leaderService
            .fetchFigures(appState.token ?? '', 'documentaries')
            .then((value) => _documentaries = value),
        _leaderService
            .fetchFigures(appState.token ?? '', 'booksAndLetters')
            .then((value) => _booksAndLetters = value),
        _leaderService
            .fetchUserContributions(appState.token ?? '')
            .then((value) => _userContributions = value),
        _leaderService
            .fetchNotifications(appState.token ?? '')
            .then((value) => _notifications = value),
      ]);
      setState(() => _isLoading = false);
    } catch (e) {
      _showSnackBar('خطأ في جلب البيانات: $e', Colors.red);
      setState(() => _isLoading = false);
    }
  }

  void _onSearch() {
    if (_debounce?.isActive ?? false) _debounce!.cancel();
    _debounce = Timer(const Duration(milliseconds: 300), () {
      setState(() {
        _searchAnimationController
            .forward()
            .then((_) => _searchAnimationController.reverse());
      });
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _debounce?.cancel();
    _searchAnimationController.dispose();
    final appState = Provider.of<AppState>(context, listen: false);
    appState.socket?.off('new_contribution');
    appState.socket?.off('new_notification');
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final filteredContributions = _userContributions
        .where((item) => item["title"]
            .toString()
            .toLowerCase()
            .contains(_searchController.text.toLowerCase()))
        .toList();

    return Scaffold(
      appBar: AppBar(
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: isDarkMode
                  ? [Colors.blue[900]!, Colors.blue[700]!]
                  : [Colors.blue, Colors.blueAccent],
            ),
          ),
        ),
        title: ScaleTransition(
          scale: _searchAnimation,
          child: TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'بحث عن الرؤساء والقادة...',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(20),
                borderSide: BorderSide.none,
              ),
              filled: true,
              fillColor:
                  isDarkMode ? Colors.grey[800] : Colors.white.withOpacity(0.9),
              hintStyle:
                  TextStyle(color: isDarkMode ? Colors.grey[400] : Colors.grey),
              prefixIcon: const Icon(Icons.search, color: Colors.grey),
              suffixIcon: _searchController.text.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear, color: Colors.grey),
                      onPressed: () {
                        _searchController.clear();
                        setState(() {});
                      },
                    )
                  : null,
            ),
            style: TextStyle(color: isDarkMode ? Colors.white : Colors.black87),
            textDirection: TextDirection.rtl,
          ),
        ),
        actions: [
          Stack(
            children: [
              IconButton(
                icon: const Icon(Icons.notifications),
                onPressed: () => _showNotifications(context),
              ),
              if (_notifications.isNotEmpty)
                Positioned(
                  right: 8,
                  top: 8,
                  child: ElasticIn(
                    child: Container(
                      padding: const EdgeInsets.all(2),
                      decoration: const BoxDecoration(
                          color: Colors.red, shape: BoxShape.circle),
                      constraints:
                          const BoxConstraints(minWidth: 16, minHeight: 16),
                      child: Text(
                        '${_notifications.length}',
                        style:
                            const TextStyle(color: Colors.white, fontSize: 10),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                ),
            ],
          ),
          ElasticIn(
            child: IconButton(
              icon: const Icon(Icons.add_circle, size: 28),
              onPressed: () => _showAddContentDialog(context),
            ),
          ),
        ],
      ),
      body: _isLoading
          ? _buildSkeletonLoader()
          : RefreshIndicator(
              onRefresh: _fetchData,
              color: Colors.blue,
              child: CustomScrollView(
                slivers: [
                  SliverList(
                    delegate: SliverChildListDelegate([
                      _buildCategorySection(context, "الحكام", _rulers),
                      _buildCategorySection(context, "القادة", _leaders),
                      _buildCategorySection(context, "المناضلون", _fighters),
                      _buildCategorySection(context, "الحكماء", _sages),
                      _buildCategorySection(context, "الفلاسفة", _philosophers),
                      _buildCategorySection(context, "العلماء", _scientists),
                      _buildCategorySection(
                          context, "رجال الدين", _religiousFigures),
                      _buildCategorySection(
                          context, "الأفلام الوثائقية", _documentaries),
                      _buildCategorySection(context,
                          "كتب ورسائل الرؤساء والحكام", _booksAndLetters),
                      _buildCategorySection(
                        context,
                        "مساهمات المستخدمين",
                        filteredContributions
                            .map((item) => ({
                                  "title": item["title"] as String,
                                  "url": item["url"] as String,
                                  "type": item["type"] as String,
                                  "id": item["_id"] as String,
                                }))
                            .toList(),
                      ),
                    ]),
                  ),
                ],
              ),
            ),
      floatingActionButton: ElasticIn(
        child: FloatingActionButton(
          onPressed: () => _showAddContentDialog(context),
          backgroundColor: Colors.blue,
          child: const Icon(Icons.add),
        ),
      ),
    );
  }

  Widget _buildSkeletonLoader() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return CustomScrollView(
      slivers: [
        SliverList(
          delegate: SliverChildBuilderDelegate(
            (context, index) => Padding(
              padding: const EdgeInsets.all(8.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    width: 150,
                    height: 20,
                    color: isDarkMode ? Colors.grey[800] : Colors.grey[300],
                  ),
                  const SizedBox(height: 8),
                  SizedBox(
                    height: 200,
                    child: ListView.builder(
                      scrollDirection: Axis.horizontal,
                      itemCount: 5,
                      itemBuilder: (context, _) => Container(
                        width: 150,
                        margin: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color:
                              isDarkMode ? Colors.grey[800] : Colors.grey[300],
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            childCount: 9,
          ),
        ),
      ],
    );
  }

  Widget _buildCategorySection(
      BuildContext context, String categoryName, List<dynamic> items) {
    if (items.isEmpty) return const SizedBox.shrink();
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return FadeInUp(
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 8.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Text(
                categoryName,
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: isDarkMode ? Colors.white : Colors.black87,
                ),
              ),
            ),
            SizedBox(
              height: 200,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: items.length,
                itemBuilder: (context, index) {
                  final item = items[index] is Figure
                      ? items[index] as Figure
                      : items[index] as Map<String, dynamic>;
                  final title =
                      item is Figure ? item.title : (item as Map)['title'];
                  final url = item is Figure ? item.url : (item as Map)['url'];
                  final type =
                      item is Figure ? item.type : (item as Map)['type'];
                  final id = item is Map ? item['id'] : null;

                  if (_searchController.text.isNotEmpty &&
                      !title
                          .toLowerCase()
                          .contains(_searchController.text.toLowerCase())) {
                    return const SizedBox.shrink();
                  }

                  return FadeInRight(
                    child: GestureDetector(
                      onTap: () => Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => LeaderDetailsPage(
                            leaderTitle: title,
                            contentUrl: url.isNotEmpty
                                ? '$_leaderService.baseUrl$url'
                                : 'https://via.placeholder.com/150',
                            contentType: type,
                          ),
                        ),
                      ),
                      child: Stack(
                        children: [
                          Container(
                            width: 150,
                            margin: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(12),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.blue.withOpacity(0.2),
                                  blurRadius: 8,
                                  offset: const Offset(0, 4),
                                ),
                              ],
                            ),
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(12),
                              child: Stack(
                                fit: StackFit.expand,
                                children: [
                                  type == 'فيديو'
                                      ? CachedNetworkImage(
                                          imageUrl: YoutubePlayer
                                                      .convertUrlToId(url) !=
                                                  null
                                              ? 'https://img.youtube.com/vi/${YoutubePlayer.convertUrlToId(url)}/0.jpg'
                                              : 'https://via.placeholder.com/150',
                                          fit: BoxFit.cover,
                                          placeholder: (context, url) =>
                                              Container(
                                            color: isDarkMode
                                                ? Colors.grey[800]
                                                : Colors.grey[300],
                                          ),
                                          errorWidget: (context, url, error) =>
                                              const Icon(Icons.broken_image,
                                                  color: Colors.grey),
                                        )
                                      : type == 'صورة'
                                          ? CachedNetworkImage(
                                              imageUrl:
                                                  '$_leaderService.baseUrl$url',
                                              fit: BoxFit.cover,
                                              placeholder: (context, url) =>
                                                  Container(
                                                color: isDarkMode
                                                    ? Colors.grey[800]
                                                    : Colors.grey[300],
                                              ),
                                              errorWidget: (context, url,
                                                      error) =>
                                                  const Icon(Icons.broken_image,
                                                      color: Colors.grey),
                                            )
                                          : FutureBuilder<String?>(
                                              future: _leaderService.cachePdf(
                                                  '$_leaderService.baseUrl$url',
                                                  title),
                                              builder: (context, snapshot) {
                                                if (snapshot.hasData) {
                                                  return PDFView(
                                                    filePath: snapshot.data!,
                                                    fitPolicy: FitPolicy.BOTH,
                                                    enableSwipe: false,
                                                    autoSpacing: false,
                                                    pageFling: false,
                                                  );
                                                }
                                                return Container(
                                                  color: isDarkMode
                                                      ? Colors.grey[800]
                                                      : Colors.grey[300],
                                                  child: const Center(
                                                      child:
                                                          CircularProgressIndicator(
                                                              color:
                                                                  Colors.blue)),
                                                );
                                              },
                                            ),
                                  Container(
                                    decoration: BoxDecoration(
                                      gradient: LinearGradient(
                                        colors: [
                                          Colors.black.withOpacity(0.6),
                                          Colors.transparent
                                        ],
                                        begin: Alignment.bottomCenter,
                                        end: Alignment.topCenter,
                                      ),
                                    ),
                                  ),
                                  if (type == 'فيديو')
                                    Center(
                                      child: Icon(
                                        Icons.play_circle_filled,
                                        color: Colors.white.withOpacity(0.7),
                                        size: 40,
                                      ),
                                    ),
                                  Align(
                                    alignment: Alignment.bottomCenter,
                                    child: Padding(
                                      padding: const EdgeInsets.all(8.0),
                                      child: Text(
                                        title,
                                        style: const TextStyle(
                                          color: Colors.white,
                                          fontSize: 16,
                                          fontWeight: FontWeight.bold,
                                        ),
                                        textAlign: TextAlign.center,
                                        maxLines: 2,
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          if (categoryName == "مساهمات المستخدمين" &&
                              id != null)
                            Positioned(
                              top: 8,
                              right: 8,
                              child: ElasticIn(
                                child: IconButton(
                                  icon: const Icon(Icons.delete,
                                      color: Colors.red),
                                  onPressed: () async {
                                    final confirmed = await showDialog<bool>(
                                      context: context,
                                      builder: (context) => AlertDialog(
                                        shape: RoundedRectangleBorder(
                                            borderRadius:
                                                BorderRadius.circular(20)),
                                        backgroundColor: isDarkMode
                                            ? Colors.grey[900]
                                            : Colors.white,
                                        title: const Text('تأكيد الحذف'),
                                        content: const Text(
                                            'هل أنت متأكد من حذف هذه المساهمة؟'),
                                        actions: [
                                          TextButton(
                                            onPressed: () =>
                                                Navigator.pop(context, false),
                                            child: Text(
                                              'إلغاء',
                                              style: TextStyle(
                                                  color: isDarkMode
                                                      ? Colors.white70
                                                      : Colors.blue),
                                            ),
                                          ),
                                          ElevatedButton(
                                            onPressed: () =>
                                                Navigator.pop(context, true),
                                            style: ElevatedButton.styleFrom(
                                              backgroundColor: Colors.red,
                                              shape: RoundedRectangleBorder(
                                                  borderRadius:
                                                      BorderRadius.circular(
                                                          10)),
                                            ),
                                            child: const Text(
                                              'حذف',
                                              style: TextStyle(
                                                  color: Colors.white),
                                            ),
                                          ),
                                        ],
                                      ),
                                    );
                                    if (confirmed ?? false) {
                                      final appState = Provider.of<AppState>(
                                          context,
                                          listen: false);
                                      try {
                                        await _leaderService
                                            .removeUserContribution(
                                                appState.token ?? '', id);
                                        await _fetchData();
                                        _showSnackBar('تم الحذف بنجاح');
                                      } catch (e) {
                                        _showSnackBar(
                                            'خطأ في الحذف: $e', Colors.red);
                                      }
                                    }
                                  },
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showNotifications(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.5,
        maxChildSize: 0.9,
        builder: (context, scrollController) => Container(
          decoration: BoxDecoration(
            color: isDarkMode ? Colors.grey[900] : Colors.white,
            borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: Column(
            children: [
              Container(
                margin: const EdgeInsets.only(top: 8, bottom: 16),
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey[400],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              const Text(
                'الإشعارات',
                style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              ),
              Expanded(
                child: _notifications.isEmpty
                    ? Center(
                        child: Text(
                          'لا توجد إشعارات',
                          style: TextStyle(
                              color: isDarkMode
                                  ? Colors.grey[400]
                                  : Colors.grey[600]),
                        ),
                      )
                    : ListView.builder(
                        controller: scrollController,
                        itemCount: _notifications.length,
                        itemBuilder: (context, index) {
                          final notification = _notifications[index];
                          return Dismissible(
                            key: Key(notification['_id']),
                            onDismissed: (direction) async {
                              final appState =
                                  Provider.of<AppState>(context, listen: false);
                              try {
                                await _leaderService.markNotificationRead(
                                    appState.token ?? '', notification['_id']);
                                setState(() {
                                  _notifications.removeAt(index);
                                });
                              } catch (e) {
                                _showSnackBar(
                                    'خطأ في تحديث الإشعار', Colors.red);
                              }
                            },
                            background: Container(
                              color: Colors.red,
                              alignment: Alignment.centerRight,
                              padding: const EdgeInsets.only(right: 16),
                              child:
                                  const Icon(Icons.delete, color: Colors.white),
                            ),
                            child: FadeIn(
                              child: ListTile(
                                leading: ElasticIn(
                                  child: const Icon(
                                    Icons.notifications_active,
                                    color: Colors.blue,
                                    size: 30,
                                  ),
                                ),
                                title: Text(
                                  notification['message'] ?? 'إشعار',
                                  style: TextStyle(
                                    fontWeight: !(notification['read'] ?? false)
                                        ? FontWeight.bold
                                        : FontWeight.normal,
                                    color: isDarkMode
                                        ? Colors.white
                                        : Colors.black87,
                                  ),
                                ),
                                subtitle: Text(
                                  timeago.format(
                                      DateTime.parse(notification['date'] ??
                                          DateTime.now().toString()),
                                      locale: 'ar'),
                                  style: TextStyle(
                                      color: isDarkMode
                                          ? Colors.grey[400]
                                          : Colors.grey[600]),
                                ),
                                trailing: notification['read']
                                    ? null
                                    : ElasticIn(
                                        child: const Icon(Icons.circle,
                                            color: Colors.blue, size: 10),
                                      ),
                                onTap: () async {
                                  if (!notification['read']) {
                                    final appState = Provider.of<AppState>(
                                        context,
                                        listen: false);
                                    try {
                                      await _leaderService.markNotificationRead(
                                          appState.token ?? '',
                                          notification['_id']);
                                      setState(() {
                                        notification['read'] = true;
                                      });
                                    } catch (e) {
                                      _showSnackBar(
                                          'خطأ في تحديث الإشعار', Colors.red);
                                    }
                                  }
                                },
                              ),
                            ),
                          );
                        },
                      ),
              ),
              FadeInUp(
                child: ElevatedButton(
                  onPressed: () {
                    setState(() => _notifications.clear());
                    Navigator.pop(context);
                    _showSnackBar('تم مسح جميع الإشعارات');
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10)),
                    minimumSize: const Size(double.infinity, 50),
                  ),
                  child: const Text(
                    'مسح الكل',
                    style: TextStyle(color: Colors.white),
                  ),
                ),
              ),
              const SizedBox(height: 16),
            ],
          ),
        ),
      ),
    );
  }

  void _showAddContentDialog(BuildContext context) {
    String title = '';
    File? file;
    String type = 'فيديو';
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
          backgroundColor: isDarkMode ? Colors.grey[900] : Colors.white,
          title: Container(
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: isDarkMode
                    ? [Colors.blue[900]!, Colors.blue[700]!]
                    : [Colors.blue, Colors.blueAccent],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius:
                  const BorderRadius.vertical(top: Radius.circular(20)),
            ),
            child: const Text(
              "إضافة محتوى جديد",
              style:
                  TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
            ),
          ),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  decoration: InputDecoration(
                    labelText: "العنوان",
                    border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12)),
                    filled: true,
                    fillColor: isDarkMode ? Colors.grey[800] : Colors.grey[100],
                  ),
                  style: TextStyle(
                      color: isDarkMode ? Colors.white : Colors.black87),
                  onChanged: (value) => title = value,
                ),
                const SizedBox(height: 10),
                DropdownButtonFormField<String>(
                  value: type,
                  decoration: InputDecoration(
                    border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12)),
                    filled: true,
                    fillColor: isDarkMode ? Colors.grey[800] : Colors.grey[100],
                  ),
                  items: ['فيديو', 'صورة', 'PDF']
                      .map((value) => DropdownMenuItem(
                            value: value,
                            child: Text(
                              value,
                              style: TextStyle(
                                  color: isDarkMode
                                      ? Colors.white
                                      : Colors.black87),
                            ),
                          ))
                      .toList(),
                  onChanged: (value) => setDialogState(() => type = value!),
                ),
                const SizedBox(height: 10),
                ElevatedButton(
                  onPressed: () async {
                    final result = await FilePicker.platform.pickFiles(
                      type: FileType.custom,
                      allowedExtensions: type == 'صورة'
                          ? ['jpg', 'png', 'jpeg']
                          : type == 'فيديو'
                              ? ['mp4', 'mov']
                              : ['pdf'],
                    );
                    if (result != null) {
                      file = File(result.files.single.path!);
                      setDialogState(() {});
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10)),
                    minimumSize: const Size(double.infinity, 50),
                  ),
                  child: Text(
                    file == null ? "اختر ملف" : file!.path.split('/').last,
                    style: const TextStyle(color: Colors.white),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(
                "إلغاء",
                style:
                    TextStyle(color: isDarkMode ? Colors.white70 : Colors.blue),
              ),
            ),
            ElevatedButton(
              onPressed: () async {
                if (title.isNotEmpty &&
                    file != null &&
                    await _validateFile(file!, type)) {
                  final appState =
                      Provider.of<AppState>(context, listen: false);
                  try {
                    await _leaderService.addUserContribution(
                        appState.token ?? '', title, file!, type);
                    Navigator.pop(context);
                    await _fetchData();
                    _showSnackBar("تمت الإضافة بنجاح");
                  } catch (e) {
                    _showSnackBar("خطأ في الإضافة: $e", Colors.red);
                  }
                } else {
                  _showSnackBar(
                      "يرجى ملء الحقول أو اختيار ملف صالح", Colors.red);
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10)),
              ),
              child: const Text(
                "إضافة",
                style: TextStyle(color: Colors.white),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<bool> _validateFile(File file, String type) async {
    final size = await file.length();
    if (size > 5 * 1024 * 1024) return false;
    if (type == 'صورة' &&
        !['jpg', 'png', 'jpeg']
            .contains(file.path.split('.').last.toLowerCase())) {
      return false;
    }
    if (type == 'فيديو' &&
        !['mp4', 'mov'].contains(file.path.split('.').last.toLowerCase())) {
      return false;
    }
    if (type == 'PDF' && file.path.split('.').last.toLowerCase() != 'pdf') {
      return false;
    }
    return true;
  }

  void _showSnackBar(String message, [Color? backgroundColor]) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: backgroundColor ?? Colors.blue,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        elevation: 6,
      ),
    );
  }
}

class LeaderDetailsPage extends StatefulWidget {
  final String leaderTitle;
  final String contentUrl;
  final String contentType;

  const LeaderDetailsPage(
      {super.key,
      required this.leaderTitle,
      required this.contentUrl,
      required this.contentType});

  @override
  _LeaderDetailsPageState createState() => _LeaderDetailsPageState();
}

class _LeaderDetailsPageState extends State<LeaderDetailsPage>
    with SingleTickerProviderStateMixin {
  final LeaderService _leaderService = LeaderService();
  String? _pdfPath;
  bool _isLoading = true;
  bool _isFavorite = false;
  late TabController _tabController;
  late AnimationController _favoriteAnimationController;
  late Animation<double> _favoriteAnimation;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _favoriteAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    _favoriteAnimation = Tween<double>(begin: 1.0, end: 1.3).animate(
      CurvedAnimation(
        parent: _favoriteAnimationController,
        curve: Curves.elasticIn,
      ),
    );
    if (widget.contentType == "PDF") {
      _cachePdf();
    } else {
      setState(() => _isLoading = false);
    }
    _loadFavoriteStatus();
  }

  Future<void> _cachePdf() async {
    try {
      _pdfPath =
          await _leaderService.cachePdf(widget.contentUrl, widget.leaderTitle);
      setState(() => _isLoading = false);
    } catch (e) {
      setState(() => _isLoading = false);
      _showSnackBar("خطأ في تحميل PDF: $e", Colors.red);
    }
  }

  Future<void> _loadFavoriteStatus() async {
    final prefs = await SharedPreferences.getInstance();
    final appState = Provider.of<AppState>(context, listen: false);
    try {
      _isFavorite = await _leaderService.isFavorite(
          appState.token ?? '', widget.leaderTitle);
      await prefs.setBool(widget.leaderTitle, _isFavorite);
      setState(() {});
    } catch (e) {
      _isFavorite = prefs.getBool(widget.leaderTitle) ?? false;
      setState(() {});
    }
  }

  Future<void> _toggleFavorite() async {
    final appState = Provider.of<AppState>(context, listen: false);
    final prefs = await SharedPreferences.getInstance();
    setState(() => _isFavorite = !_isFavorite);
    _favoriteAnimationController
        .forward()
        .then((_) => _favoriteAnimationController.reverse());
    try {
      await _leaderService.toggleFavorite(
          appState.token ?? '', widget.leaderTitle, _isFavorite);
      await prefs.setBool(widget.leaderTitle, _isFavorite);
      _showSnackBar(
          _isFavorite ? "تمت الإضافة إلى المفضلة" : "تمت الإزالة من المفضلة");
    } catch (e) {
      setState(() => _isFavorite = !_isFavorite);
      _showSnackBar("خطأ في تحديث المفضلة: $e", Colors.red);
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    Widget contentWidget = _buildContentWidget();

    return Scaffold(
      appBar: AppBar(
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: isDarkMode
                  ? [Colors.blue[900]!, Colors.blue[700]!]
                  : [Colors.blue, Colors.blueAccent],
            ),
          ),
        ),
        title: Text(widget.leaderTitle),
        actions: [
          ElasticIn(
            child: IconButton(
              icon: const Icon(Icons.share),
              onPressed: () =>
                  Share.share(widget.contentUrl, subject: widget.leaderTitle),
            ),
          ),
          ScaleTransition(
            scale: _favoriteAnimation,
            child: IconButton(
              icon: Icon(_isFavorite ? Icons.bookmark : Icons.bookmark_border),
              onPressed: _toggleFavorite,
            ),
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(text: "المحتوى"),
            Tab(text: "التفاصيل"),
          ],
        ),
      ),
      body: _isLoading
          ? _buildSkeletonLoader()
          : TabBarView(
              controller: _tabController,
              children: [
                _buildContentTab(contentWidget),
                _buildDetailsTab(),
              ],
            ),
    );
  }

  Widget _buildSkeletonLoader() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return CustomScrollView(
      slivers: [
        SliverToBoxAdapter(
          child: Container(
            height: 250,
            color: isDarkMode ? Colors.grey[800] : Colors.grey[300],
          ),
        ),
        SliverToBoxAdapter(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  width: 150,
                  height: 20,
                  color: isDarkMode ? Colors.grey[800] : Colors.grey[300],
                ),
                const SizedBox(height: 8),
                Container(
                  width: double.infinity,
                  height: 100,
                  color: isDarkMode ? Colors.grey[800] : Colors.grey[300],
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildContentWidget() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    if (widget.contentType == "PDF") {
      return _pdfPath != null
          ? SizedBox(
              height: 500,
              child: PDFView(
                filePath: _pdfPath!,
                onError: (error) =>
                    _showSnackBar("خطأ في تحميل PDF: $error", Colors.red),
                enableSwipe: true,
                swipeHorizontal: true,
                autoSpacing: false,
                pageFling: false,
              ),
            )
          : Center(
              child: Text(
                "فشل تحميل PDF",
                style: TextStyle(
                    color: isDarkMode ? Colors.grey[400] : Colors.grey[600]),
              ),
            );
    } else if (widget.contentType == "صورة") {
      return GestureDetector(
        onTap: () => _showFullImage(context),
        child: CachedNetworkImage(
          imageUrl: widget.contentUrl,
          fit: BoxFit.cover,
          height: 250,
          width: double.infinity,
          placeholder: (context, url) => Container(
            height: 250,
            color: isDarkMode ? Colors.grey[800] : Colors.grey[300],
          ),
          errorWidget: (context, url, error) =>
              const Icon(Icons.broken_image, color: Colors.grey),
        ),
      );
    } else {
      final String? videoId = YoutubePlayer.convertUrlToId(widget.contentUrl);
      return videoId != null
          ? YoutubePlayer(
              controller: YoutubePlayerController(
                initialVideoId: videoId,
                flags: const YoutubePlayerFlags(autoPlay: false, mute: false),
              ),
              showVideoProgressIndicator: true,
              progressIndicatorColor: Colors.blueAccent,
              thumbnail: CachedNetworkImage(
                imageUrl: 'https://img.youtube.com/vi/$videoId/0.jpg',
                fit: BoxFit.cover,
                placeholder: (context, url) => Container(
                  height: 250,
                  color: isDarkMode ? Colors.grey[800] : Colors.grey[300],
                ),
                errorWidget: (context, url, error) =>
                    const Icon(Icons.broken_image, color: Colors.grey),
              ),
            )
          : Center(
              child: Text(
                "رابط فيديو غير صالح",
                style: TextStyle(
                    color: isDarkMode ? Colors.grey[400] : Colors.grey[600]),
              ),
            );
    }
  }

  Widget _buildContentTab(Widget contentWidget) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return RefreshIndicator(
      onRefresh: () async {
        if (widget.contentType == "PDF") await _cachePdf();
      },
      color: Colors.blue,
      child: CustomScrollView(
        slivers: [
          SliverToBoxAdapter(
            child: FadeIn(
              child: contentWidget,
            ),
          ),
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: FadeInUp(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "عن ${widget.leaderTitle}",
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: isDarkMode ? Colors.white : Colors.black87,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      "معلومات حول ${widget.leaderTitle}. يمكنك هنا إضافة تفاصيل مثل الإنجازات والتأثير التاريخي.",
                      style: TextStyle(
                        fontSize: 16,
                        color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailsTab() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return CustomScrollView(
      slivers: [
        SliverToBoxAdapter(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: FadeInUp(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "عن ${widget.leaderTitle}",
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: isDarkMode ? Colors.white : Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    "معلومات حول ${widget.leaderTitle}. يمكنك هنا إضافة تفاصيل مثل الإنجازات والتأثير التاريخي.",
                    style: TextStyle(
                      fontSize: 16,
                      color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    "معلومات إضافية",
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: isDarkMode ? Colors.white : Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    "يمكنك إضافة المزيد من المعلومات مثل الخلفية التاريخية أو الخطب الشهيرة.",
                    style: TextStyle(
                      fontSize: 16,
                      color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  void _showFullImage(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        child: Container(
          padding: const EdgeInsets.all(16),
          constraints: const BoxConstraints(maxHeight: 400, maxWidth: 300),
          child: Stack(
            children: [
              CachedNetworkImage(
                imageUrl: widget.contentUrl,
                fit: BoxFit.contain,
                placeholder: (context, url) => Container(
                  height: 400,
                  color: isDarkMode ? Colors.grey[800] : Colors.grey[300],
                ),
                errorWidget: (context, url, error) =>
                    const Icon(Icons.broken_image, color: Colors.grey),
              ),
              Positioned(
                top: 0,
                right: 0,
                child: ElasticIn(
                  child: IconButton(
                    icon: const Icon(Icons.close, color: Colors.white),
                    onPressed: () => Navigator.pop(context),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showSnackBar(String message, [Color? backgroundColor]) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: backgroundColor ?? Colors.blue,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        elevation: 6,
      ),
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    _favoriteAnimationController.dispose();
    super.dispose();
  }
}
