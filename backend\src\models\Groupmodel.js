const mongoose = require('mongoose');

const GroupSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'الرجاء إدخال اسم المجموعة'],
    trim: true,
    maxlength: [50, 'اسم المجموعة لا يمكن أن يتجاوز 50 حرفًا']
  },
  description: {
    type: String,
    required: [true, 'الرجاء إدخال وصف المجموعة'],
    maxlength: [500, 'الوصف لا يمكن أن يتجاوز 500 حرف']
  },
  creatorId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  type: {
    type: String,
    enum: [
      'cooking', 'shopping', 'sports', 'mentalSupport', 'study',
      'entertainment', 'technology', 'travel', 'work', 'health',
      'homework', 'photography', 'art', 'music', 'gaming', 'other'
    ],
    default: 'other'
  },
  members: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }],
  groupImage: {
    type: String,
    default: '/uploads/default-group.png'
  },
  videoUrl: {
    type: String
  },
  points: {
    type: Number,
    default: 0
  },
  media: [{
    path: String,
    type: String
  }],
  createdAt: {
    type: Date,
    default: Date.now
  }
});

// الحصول على معلومات المنشئ
GroupSchema.virtual('creator', {
  ref: 'User',
  localField: 'creatorId',
  foreignField: '_id',
  justOne: true
});

// حساب عدد الأعضاء
GroupSchema.virtual('membersCount').get(function() {
  return this.members.length;
});

module.exports = mongoose.model('Group', GroupSchema);
