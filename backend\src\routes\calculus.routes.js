const express = require('express');
const math = require('mathjs');
const router = express.Router();

// حساب النهاية (limit)
router.post('/limit', (req, res) => {
    const { expression, variable, value } = req.body;
    try {
        // mathjs لا تدعم limits مباشرة، نستخدم تقريب عددي
        const delta = 1e-7;
        const scope1 = { [variable]: value - delta };
        const scope2 = { [variable]: value + delta };
        const expr = math.parse(expression);
        const left = expr.evaluate(scope1);
        const right = expr.evaluate(scope2);
        if (Math.abs(left - right) < 1e-5) {
            res.json({ result: (left + right) / 2 });
        } else {
            res.json({ result: null, error: 'Limit does not exist or is not continuous.' });
        }
    } catch (e) {
        res.status(400).json({ error: e.message });
    }
});

// حساب التكامل (definite integral)
router.post('/integral', (req, res) => {
    const { expression, variable, lower, upper } = req.body;
    try {
        // حساب التكامل العددي باستخدام mathjs (طريقة شبه المنحرف)
        const f = (x) => math.evaluate(expression, { [variable]: x });
        const n = 1000; // عدد التقسيمات
        const dx = (upper - lower) / n;
        let area = 0;
        for (let i = 0; i < n; i++) {
            const x0 = lower + i * dx;
            const x1 = lower + (i + 1) * dx;
            area += 0.5 * (f(x0) + f(x1)) * dx;
        }
        res.json({ result: area });
    } catch (e) {
        res.status(400).json({ error: e.message });
    }
});

module.exports = router;
