const express = require('express');
const router = express.Router();
const { check } = require('express-validator');
const userController = require('../controllers/user.controller');
const { protect, authorize } = require('../middleware/authmiddleware');
const uploadController = require('../controllers/upload.controller');

// Authentication routes
router.post('/register', userController.register);
router.post('/login', userController.login);

// Profile routes
router.get('/:id', protect, userController.getProfile);
router.put('/:id', protect, userController.updateProfile);
router.post('/:id/rate', protect, userController.rateUser);

// Items routes
router.get('/:id/shared-items', protect, userController.getSharedItems);
router.get('/:id/favorite-items', protect, userController.getFavoriteItems);
router.post('/:id/favorites', protect, userController.addToFavorites);
router.delete('/:id/favorites', protect, userController.removeFromFavorites);

// Device token route
router.post('/device-token', protect, userController.updateDeviceToken);

// Points management routes
router.post(
  '/points', 
  [
    protect,
    check('points', 'Points must be a positive number').isInt({ min: 1 }),
    check('reason', 'Reason is required').not().isEmpty()
  ], 
  userController.addPoints
);

router.get(
  '/points/history',
  protect,
  userController.getPointsHistory
);

router.get(
  '/points/summary',
  protect,
  userController.getPointsSummary
);

// User management
router.get('/', protect, userController.getUsers);
router.put('/stars', protect, userController.updateUserStars);

// Follow/Unfollow routes
router.post('/:id/follow', protect, userController.followUser);
router.post('/:id/unfollow', protect, userController.unfollowUser);

// Get followers/following/friends
router.get('/:id/followers', protect, userController.getFollowers);
router.get('/:id/following', protect, userController.getFollowing);
router.get('/:id/friends', protect, userController.getFriends);

// Search users
router.get('/search', protect, userController.searchUsers);
router.get('/advanced-search', protect, userController.advancedSearchUsers);

// Notifications
router.get('/notifications', protect, userController.getNotifications);
router.patch('/notifications/:notifId/read', protect, userController.markNotificationRead);
router.patch('/notifications/read-all', protect, userController.markAllNotificationsRead);

// Notification settings
router.put('/:id/notifications-settings', protect, userController.updateNotificationSettings);

// Admin notification
router.post('/admin-notification', protect, userController.sendAdminNotification);

// Ban/Unban user (admin only)
router.put('/:id/ban', protect, authorize('admin'), userController.banUser);
router.put('/:id/unban', protect, authorize('admin'), userController.unbanUser);

// Avatar
router.put('/avatar', protect, userController.updateAvatar);

module.exports = router;
