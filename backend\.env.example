# إعدادات التطبيق
NODE_ENV=development
PORT=3000
HOST=0.0.0.0

# إعدادات قاعدة البيانات
MONGODB_URI=mongodb://localhost:27017/fulk
# MONGODB_URI=mongodb+srv://username:<EMAIL>/fulk?retryWrites=true&w=majority

# إعدادات JWT
JWT_SECRET=your_jwt_secret_key_here
JWT_EXPIRE=30d
JWT_COOKIE_EXPIRE=30

# إعدادات CORS
CLIENT_URL=http://localhost:3000
FRONTEND_URL=http://localhost
ALLOWED_ORIGINS=http://localhost:3000,http://localhost

# إعدادات Firebase (اختياري)
FIREBASE_API_KEY=your_firebase_api_key
FIREBASE_AUTH_DOMAIN=your_firebase_auth_domain
FIREBASE_PROJECT_ID=your_firebase_project_id
FIREBASE_STORAGE_BUCKET=your_firebase_storage_bucket
FIREBASE_MESSAGING_SENDER_ID=your_firebase_messaging_sender_id
FIREBASE_APP_ID=your_firebase_app_id

# إعدادات التخزين (اختياري)
AWS_ACCESS_KEY_ID=your_aws_access_key_id
AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key
AWS_REGION=your_aws_region
AWS_BUCKET_NAME=your_aws_bucket_name

# إعدادات البريد الإلكتروني (اختياري)
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_EMAIL=<EMAIL>
SMTP_PASSWORD=your_email_password
FROM_EMAIL=<EMAIL>
FROM_NAME=Your App Name