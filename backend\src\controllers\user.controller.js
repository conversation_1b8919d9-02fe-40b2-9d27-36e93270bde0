const User = require('../models/User.model');
const Task = require('../models/Task.model');
const jwt = require('jsonwebtoken');
const ShareBorrowItem = require('../models/ShareBorrowItem.model');
const Notification = require('../models/notification.model');
const PointsTransaction = require('../models/pointstransaction.model');
const mongoose = require('mongoose');
const { updateUserLevel } = require('./points.controller');
const ErrorResponse = require('../utils/errorResponse');
const asyncHandler = require('../middleware/asyncmiddleware');

// Constants for points system
const WELCOME_POINTS = 100; // Points awarded for new user registration
const REFERRAL_BONUS = 200; // Points awarded for referring a friend
const PROFILE_COMPLETION_BONUS = 50; // Points for completing profile

// Register a new user
exports.register = async (req, res) => {
  const session = await mongoose.startSession();
  session.startTransaction();
  
  try {
    const { username, email, password, name, referralCode } = req.body;
    
    // Check if user already exists
    const existingUser = await User.findOne({ $or: [{ email }, { username }] });
    if (existingUser) {
      await session.abortTransaction();
      session.endSession();
      return res.status(400).json({
        success: false,
        error: 'User already exists with this email or username'
      });
    }

    // Create new user
    const user = new User({
      username,
      email,
      password,
      name,
      points: WELCOME_POINTS,
      level: 1
    });

    // Handle referral code if provided
    if (referralCode) {
      const referrer = await User.findOne({ referralCode });
      if (referrer) {
        user.referredBy = referrer._id;
        
        // Add referral bonus to referrer
        referrer.points = (referrer.points || 0) + REFERRAL_BONUS;
        await referrer.save({ session });
        
        // Record points transaction for referrer
        const referrerTransaction = new PointsTransaction({
          user: referrer._id,
          points: REFERRAL_BONUS,
          type: 'referral_bonus',
          description: `Referral bonus for referring ${username}`
        });
        await referrerTransaction.save({ session });
        
        // Update referrer's level if needed
        await updateUserLevel(referrer._id, session);
      }
    }

    await user.save({ session });
    
    // Record welcome points transaction
    const welcomeTransaction = new PointsTransaction({
      user: user._id,
      points: WELCOME_POINTS,
      type: 'welcome_bonus',
      description: 'Welcome bonus for new registration'
    });
    await welcomeTransaction.save({ session });
    
    // Create JWT token
    const token = jwt.sign(
      { id: user._id, username: user.username, email: user.email },
      process.env.JWT_SECRET,
      { expiresIn: process.env.JWT_EXPIRE }
    );
    
    await session.commitTransaction();
    session.endSession();
    
    res.status(201).json({
      success: true,
      token,
      user: {
        id: user._id,
        username: user.username,
        email: user.email,
        name: user.name,
        points: user.points,
        level: user.level
      }
    });
    
  } catch (error) {
    await session.abortTransaction();
    session.endSession();
    console.error('Registration error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error during registration'
    });
  }
};

// Login user
exports.login = async (req, res) => {
  try {
    const { email, password } = req.body;
    
    // Validate email & password
    if (!email || !password) {
      return res.status(400).json({
        success: false,
        error: 'Please provide an email and password'
      });
    }
    
    // Check for user
    const user = await User.findOne({ email }).select('+password');
    if (!user) {
      return res.status(401).json({
        success: false,
        error: 'Invalid credentials'
      });
    }
    
    // Check if password matches
    const isMatch = await user.matchPassword(password);
    if (!isMatch) {
      return res.status(401).json({
        success: false,
        error: 'Invalid credentials'
      });
    }
    
    // Create token
    const token = jwt.sign(
      { id: user._id, username: user.username, email: user.email },
      process.env.JWT_SECRET,
      { expiresIn: process.env.JWT_EXPIRE }
    );
    
    res.status(200).json({
      success: true,
      token,
      user: {
        id: user._id,
        username: user.username,
        email: user.email,
        name: user.name,
        points: user.points,
        level: user.level
      }
    });
    
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error during login'
    });
  }
};

// Get user profile
exports.getProfile = async (req, res) => {
  try {
    const user = await User.findById(req.user.id).select('-password');
    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }
    
    res.status(200).json({
      success: true,
      data: user
    });
    
  } catch (error) {
    console.error('Get profile error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching profile'
    });
  }
};

// Update user profile
exports.updateProfile = async (req, res) => {
  try {
    const { name, bio, phone, address } = req.body;
    const updateData = {};
    
    if (name) updateData.name = name;
    if (bio !== undefined) updateData.bio = bio;
    if (phone !== undefined) updateData.phone = phone;
    if (address !== undefined) updateData.address = address;
    
    const user = await User.findByIdAndUpdate(
      req.user.id,
      updateData,
      { new: true, runValidators: true }
    ).select('-password');
    
    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }
    
    res.status(200).json({
      success: true,
      data: user
    });
    
  } catch (error) {
    console.error('Update profile error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while updating profile'
    });
  }
};

// Get all users with filtering
exports.getUsers = asyncHandler(async (req, res, next) => {
  const filter = {};
  if (req.query.isBanned !== undefined) {
    filter.isBanned = req.query.isBanned === 'true';
  }
  if (req.query.role) {
    filter.role = req.query.role;
  }
  const users = await User.find(filter).select('-password');

  res.status(200).json({
    success: true,
    count: users.length,
    data: users
  });
});

// Get user by ID
exports.getUserById = asyncHandler(async (req, res, next) => {
  const user = await User.findById(req.params.id).select('-password');
  
  if (!user) {
    return next(new ErrorResponse(`User not found with id of ${req.params.id}`, 404));
  }
  
  res.status(200).json({
    success: true,
    data: user
  });
});

// Update user by ID
exports.updateUser = asyncHandler(async (req, res, next) => {
  const user = await User.findByIdAndUpdate(req.params.id, req.body, {
    new: true,
    runValidators: true
  }).select('-password');

  if (!user) {
    return next(new ErrorResponse(`User not found with id of ${req.params.id}`, 404));
  }

  res.status(200).json({
    success: true,
    data: user
  });
});

// Delete user by ID
exports.deleteUser = asyncHandler(async (req, res, next) => {
  const user = await User.findById(req.params.id);

  if (!user) {
    return next(new ErrorResponse(`User not found with id of ${req.params.id}`, 404));
  }

  // Delete user's tasks
  await Task.deleteMany({ user: req.params.id });
  
  // Delete user
  await user.remove();

  res.status(200).json({
    success: true,
    data: {}
  });
});

// Get user's friends
exports.getFriends = asyncHandler(async (req, res, next) => {
  const user = await User.findById(req.params.id).populate('followers following', 'name avatarUrl');
  if (!user) {
    return next(new ErrorResponse('User not found', 404));
  }
  
  res.status(200).json({
    success: true,
    count: user.followers.length + user.following.length,
    data: {
      followers: user.followers,
      following: user.following
    }
  });
});

// Follow/Unfollow user
exports.toggleFollow = asyncHandler(async (req, res, next) => {
  if (req.user.id === req.params.id) {
    return next(new ErrorResponse('You cannot follow yourself', 400));
  }
  
  const userToFollow = await User.findById(req.params.id);
  const currentUser = await User.findById(req.user.id);
  
  if (!userToFollow || !currentUser) {
    return next(new ErrorResponse('User not found', 404));
  }
  
  const isFollowing = currentUser.following.includes(userToFollow._id);
  
  if (isFollowing) {
    // Unfollow
    currentUser.following.pull(userToFollow._id);
    userToFollow.followers.pull(currentUser._id);
  } else {
    // Follow
    currentUser.following.push(userToFollow._id);
    userToFollow.followers.push(currentUser._id);
    
    // Create notification for the user being followed
    const notification = new Notification({
      user: userToFollow._id,
      type: 'follow',
      message: `${currentUser.name} started following you`,
      link: `/profile/${currentUser._id}`,
      fromUser: currentUser._id
    });
    await notification.save();
  }
  
  await Promise.all([currentUser.save(), userToFollow.save()]);
  
  res.status(200).json({
    success: true,
    data: { isFollowing: !isFollowing }
  });
});

// Upload user avatar
const upload = multer({
  storage: multer.diskStorage({
    destination: function(req, file, cb) {
      const uploadPath = './public/uploads/avatars';
      if (!fs.existsSync(uploadPath)) {
        fs.mkdirSync(uploadPath, { recursive: true });
      }
      cb(null, uploadPath);
    },
    filename: function(req, file, cb) {
      cb(null, `avatar_${req.user.id}${path.extname(file.originalname)}`);
    }
  }),
  fileFilter: function(req, file, cb) {
    if (!file.mimetype.startsWith('image')) {
      return cb(new Error('Only image files are allowed'), false);
    }
    cb(null, true);
  },
  limits: {
    fileSize: 1024 * 1024 * 5 // 5MB
  }
}).single('avatar');

exports.uploadAvatar = asyncHandler(async (req, res, next) => {
  upload(req, res, async (err) => {
    if (err) {
      return next(new ErrorResponse(`Error uploading file: ${err.message}`, 400));
    }
    
    if (!req.file) {
      return next(new ErrorResponse('Please upload a file', 400));
    }
    
    const user = await User.findById(req.user.id);
    if (!user) {
      return next(new ErrorResponse('User not found', 404));
    }
    
    // Delete old avatar if exists
    if (user.avatar && fs.existsSync(`./public${user.avatar}`)) {
      fs.unlinkSync(`./public${user.avatar}`);
    }
    
    user.avatar = `/uploads/avatars/${req.file.filename}`;
    await user.save();
    
    res.status(200).json({
      success: true,
      data: user.avatar
    });
  });
});

// Get user's notifications
exports.getUserNotifications = asyncHandler(async (req, res, next) => {
  const user = await User.findById(req.user.id).populate({
    path: 'notifications',
    populate: {
      path: 'fromUser',
      select: 'name avatarUrl'
    },
    options: { sort: { createdAt: -1 }, limit: 20 }
  });
  
  if (!user) {
    return next(new ErrorResponse('User not found', 404));
  }
  
  // Categorize notifications
  const categorized = {
    likes: [],
    comments: [],
    follows: [],
    mentions: [],
    other: []
  };
  
  // Helper function to calculate relative time
  const relativeTime = (date) => {
    const now = new Date();
    const diffInSeconds = Math.floor((now - date) / 1000);
    
    if (diffInSeconds < 60) return 'Just now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d ago`;
    return date.toLocaleDateString();
  };
  
  for (const notif of user.notifications) {
    const n = { ...notif._doc, relativeTime: relativeTime(notif.date || notif.createdAt || notif._id.getTimestamp()) };
    if (categorized[n.type]) categorized[n.type].push(n);
    else categorized['other'] = (categorized['other'] || []).concat(n);
  }
  
  res.status(200).json({
    success: true,
    data: categorized
  });
});

// Mark notification as read
exports.markNotificationAsRead = asyncHandler(async (req, res, next) => {
  const notification = await Notification.findByIdAndUpdate(
    req.params.id,
    { isRead: true },
    { new: true }
  );
  
  if (!notification) {
    return next(new ErrorResponse('Notification not found', 404));
  }
  
  res.status(200).json({
    success: true,
    data: notification
  });
});

// Mark all notifications as read
exports.markAllNotificationsAsRead = asyncHandler(async (req, res, next) => {
  await Notification.updateMany(
    { user: req.user.id, isRead: false },
    { $set: { isRead: true } }
  );
  
  res.status(200).json({
    success: true,
    data: {}
  });
});

// Helper function to add notification to user
const addNotification = async (userId, notif) => {
  const notification = new Notification({
    user: userId,
    fromUser: notif.fromUser || null,
    title: notif.title || '',
    message: notif.message,
    type: notif.type || 'system',
    link: notif.link || '',
    targetId: notif.targetId || null,
    targetType: notif.targetType || 'other',
    isRead: false
  });
  
  await notification.save();
  
  // Populate fromUser for the response if it exists
  if (notification.fromUser) {
    await notification.populate('fromUser', 'name avatarUrl');
  }
  
  return notification;
};

// Send admin notification
exports.sendAdminNotification = asyncHandler(async (req, res, next) => {
  const { userId, title, message, type, link } = req.body;
  
  if (userId) {
    // Send to specific user
    const user = await User.findById(userId);
    if (!user) {
      return next(new ErrorResponse('User not found', 404));
    }
    
    const notification = await addNotification(userId, {
      title,
      message,
      type: type || 'admin',
      link,
      isRead: false
    });
    
    return res.status(200).json({
      success: true,
      data: notification
    });
  } else {
    // Send to all users
    const users = await User.find({}).select('_id');
    const notifications = [];
    
    for (const user of users) {
      const notification = await addNotification(user._id, {
        title,
        message,
        type: type || 'admin',
        link,
        isRead: false
      });
      notifications.push(notification);
    }
    
    res.status(200).json({
      success: true,
      count: notifications.length,
      data: notifications
    });
  }
});

// Get user's activity feed
exports.getActivityFeed = asyncHandler(async (req, res, next) => {
  const page = parseInt(req.query.page, 10) || 1;
  const limit = parseInt(req.query.limit, 10) || 10;
  const skip = (page - 1) * limit;
  
  const user = await User.findById(req.user.id)
    .select('following')
    .populate({
      path: 'following',
      select: 'name avatarUrl'
    });
  
  if (!user) {
    return next(new ErrorResponse('User not found', 404));
  }
  
  // Get activities from followed users
  const activities = await Activity.find({
    $or: [
      { user: { $in: user.following.map(u => u._id) } },
      { user: req.user.id }
    ]
  })
  .sort('-createdAt')
  .skip(skip)
  .limit(limit)
  .populate('user', 'name avatarUrl')
  .populate('targetUser', 'name avatarUrl')
  .populate('targetPost', 'content')
  .populate('targetComment', 'content');
  
  res.status(200).json({
    success: true,
    count: activities.length,
    data: activities
  });
});
