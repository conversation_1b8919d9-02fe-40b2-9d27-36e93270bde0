// نموذج الإعلان المبوب (Classified Ad)
class ClassifiedAd {
  bool isFavorite;
  final String id;
  final String title;
  final String description;
  final String location;
  final String price;
  final String contact;
  final String category;
  final bool isNegotiable;
  final String fileUrl;
  final DateTime date;
  final bool isFeatured;
  final String creatorId;
  final String status;

  ClassifiedAd({
    required this.id,
    this.isFavorite = false,
    required this.title,
    required this.description,
    required this.location,
    required this.price,
    required this.contact,
    required this.category,
    required this.isNegotiable,
    required this.fileUrl,
    required this.date,
    required this.isFeatured,
    required this.creatorId,
    required this.status,
  });

  factory ClassifiedAd.fromJson(Map<String, dynamic> json) {
    return ClassifiedAd(
      id: json['_id'] ?? '',
      title: json['title'] ?? 'بدون عنوان',
      description: json['description'] ?? 'بدون وصف',
      location: json['location'] ?? 'غير محدد',
      price: json['price'] ?? 'غير محدد',
      contact: json['contact'] ?? 'غير متوفر',
      category: json['category'] ?? 'غير مصنف',
      isNegotiable: json['isNegotiable'] ?? false,
      fileUrl: json['fileUrl'] ?? '',
      date: DateTime.parse(json['date'] ?? DateTime.now().toString()),
      isFeatured: json['isFeatured'] ?? false,
      creatorId: json['creatorId'] ?? '',
      status: json['status'] ?? 'active',
      isFavorite: json['isFavorite'] ?? false,
    );
  }
}
