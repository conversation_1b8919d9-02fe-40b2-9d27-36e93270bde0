import 'dart:convert';
import 'package:http/http.dart' as http;

class CalculusService {
  static const String baseUrl = 'http://localhost:3000/api/calculus';

  static Future<double?> calculateLimit({
    required String expression,
    required String variable,
    required double value,
  }) async {
    final url = Uri.parse('$baseUrl/limit');
    final response = await http.post(
      url,
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({
        'expression': expression,
        'variable': variable,
        'value': value,
      }),
    );
    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      return data['result']?.toDouble();
    } else {
      return null;
    }
  }

  static Future<double?> calculateIntegral({
    required String expression,
    required String variable,
    required double lower,
    required double upper,
  }) async {
    final url = Uri.parse('$baseUrl/integral');
    final response = await http.post(
      url,
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({
        'expression': expression,
        'variable': variable,
        'lower': lower,
        'upper': upper,
      }),
    );
    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      return data['result']?.toDouble();
    } else {
      return null;
    }
  }
}
