// نموذج المحتوى الخاص بالهوايات
class HobbyContent {
  final String id;
  final String title;
  final String type;
  final String? description;
  final String? filePath;
  final DateTime createdAt;
  final String creatorId;
  bool isFavorite;
  int rating;

  HobbyContent({
    required this.id,
    required this.title,
    required this.type,
    this.description,
    this.filePath,
    required this.createdAt,
    required this.creatorId,
    this.isFavorite = false,
    this.rating = 0,
  });

  factory HobbyContent.fromJson(Map<String, dynamic> json) {
    return HobbyContent(
      id: json['_id'] ?? '',
      title: json['title'] ?? 'بدون عنوان',
      type: json['type'] ?? 'غير محدد',
      description: json['description'],
      filePath: json['fileUrl'],
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toString()),
      creatorId: json['creatorId'] ?? '',
    );
  }
}

// نموذج العضو
class Member {
  final String id;
  final String name;

  Member({required this.id, required this.name});

  factory Member.fromJson(Map<String, dynamic> json) {
    return Member(id: json['id'] ?? '', name: json['name'] ?? 'غير معروف');
  }
}
