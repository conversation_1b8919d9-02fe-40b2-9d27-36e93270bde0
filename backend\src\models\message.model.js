const mongoose = require('mongoose');

const MessageSchema = new mongoose.Schema(
  {
    // المرسل والمستقبل
    senderId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: [true, 'يجب تحديد المرسل']
    },
    receiverId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: function() {
        return !this.groupId; // مطلوب إذا لم يكن هناك معرف مجموعة
      }
    },
    groupId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Group',
      required: function() {
        return !this.receiverId; // مطلوب إذا لم يكن هناك مستقبل
      }
    },
    
    // محتوى الرسالة
    content: {
      type: String,
      required: [true, 'الرجاء إدخال محتوى الرسالة'],
      trim: true,
      maxlength: [2000, 'المحتوى لا يمكن أن يتجاوز 2000 حرف']
    },
    
    // مرفقات الرسالة
    attachments: [{
      url: {
        type: String,
        required: [true, 'يجب تحديد رابط المرفق']
      },
      type: {
        type: String,
        enum: ['image', 'video', 'audio', 'document', 'location', 'contact', 'other'],
        required: true
      },
      name: String,
      size: Number,
      mimeType: String,
      thumbnail: String,
      duration: Number // للملفات الصوتية والمرئية
    }],
    
    // نوع الرسالة
    type: {
      type: String,
      enum: ['text', 'image', 'audio', 'video', 'file', 'location', 'contact', 'system'],
      default: 'text'
    },
    
    // حالة الرسالة
    status: {
      type: String,
      enum: ['sending', 'sent', 'delivered', 'read', 'failed'],
      default: 'sending'
    },
    
    // من قرأ الرسالة
    readBy: [{
      user: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
      },
      readAt: {
        type: Date,
        default: Date.now
      }
    }],
    
    // الرد على رسالة سابقة
    replyTo: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Message'
    },
    
    // تفاعلات المستخدمين مع الرسالة (إعجابات، ردود فعل)
    reactions: [{
      user: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
      },
      reaction: {
        type: String,
        required: true
      },
      createdAt: {
        type: Date,
        default: Date.now
      }
    }],
    
    // تنسيق النص
    format: {
      isBold: { type: Boolean, default: false },
      isItalic: { type: Boolean, default: false },
      isUnderlined: { type: Boolean, default: false },
      color: { type: String, default: '#000000' },
      fontSize: { type: Number, default: 14 }
    },
    
    // بيانات إضافية
    metadata: {
      isEdited: { type: Boolean, default: false },
      editedAt: Date,
      isForwarded: { type: Boolean, default: false },
      forwardedFrom: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
      },
      isDeleted: { type: Boolean, default: false },
      deletedAt: Date,
      deletedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
      }
    }
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  }
);

// فهرسة الحقول المستخدمة في البحث
MessageSchema.index({ content: 'text' });
MessageSchema.index({ senderId: 1, receiverId: 1, createdAt: -1 });
MessageSchema.index({ groupId: 1, createdAt: -1 });

// الإشارات المرجعية
MessageSchema.virtual('sender', {
  ref: 'User',
  localField: 'senderId',
  foreignField: '_id',
  justOne: true
});

MessageSchema.virtual('receiver', {
  ref: 'User',
  localField: 'receiverId',
  foreignField: '_id',
  justOne: true
});

MessageSchema.virtual('group', {
  ref: 'Group',
  localField: 'groupId',
  foreignField: '_id',
  justOne: true
});

// تحديث حالة الرسالة
MessageSchema.methods.updateStatus = async function(newStatus, userId = null) {
  if (this.status === newStatus) return this;
  
  this.status = newStatus;
  
  if (newStatus === 'read' && userId) {
    // تجنب تكرار إضافة نفس المستخدم إلى قائمة القراء
    const alreadyRead = this.readBy.some(reader => reader.user.toString() === userId.toString());
    
    if (!alreadyRead) {
      this.readBy.push({ user: userId });
    }
  }
  
  await this.save();
  return this;
};

// إضافة رد فعل
MessageSchema.methods.addReaction = async function(userId, reaction) {
  // إزالة أي رد فعل سابق من نفس المستخدم
  this.reactions = this.reactions.filter(r => r.user.toString() !== userId.toString());
  
  // إضافة رد الفعل الجديد
  this.reactions.push({ user: userId, reaction });
  
  await this.save();
  return this;
};

// إزالة رد فعل
MessageSchema.methods.removeReaction = async function(userId) {
  this.reactions = this.reactions.filter(r => r.user.toString() !== userId.toString());
  
  await this.save();
  return this;
};

// حذف الرسالة (حذف ناعم)
MessageSchema.methods.softDelete = async function(userId) {
  this.metadata.isDeleted = true;
  this.metadata.deletedAt = new Date();
  this.metadata.deletedBy = userId;
  
  await this.save();
  return this;
};

// استعادة الرسالة المحذوفة
MessageSchema.methods.restore = async function() {
  this.metadata.isDeleted = false;
  this.metadata.deletedAt = undefined;
  this.metadata.deletedBy = undefined;
  
  await this.save();
  return this;
};

// الحصول على آخر الرسائل للمحادثات
MessageSchema.statics.getConversations = async function(userId, limit = 20) {
  return this.aggregate([
    {
      $match: {
        $or: [
          { senderId: mongoose.Types.ObjectId(userId) },
          { receiverId: mongoose.Types.ObjectId(userId) },
          { 'readBy.user': mongoose.Types.ObjectId(userId) }
        ],
        'metadata.isDeleted': false
      }
    },
    {
      $sort: { createdAt: -1 }
    },
    {
      $group: {
        _id: {
          $cond: [
            { $eq: ['$senderId', mongoose.Types.ObjectId(userId)] },
            '$receiverId',
            '$senderId'
          ]
        },
        lastMessage: { $first: '$$ROOT' },
        unreadCount: {
          $sum: {
            $cond: [
              {
                $and: [
                  { $ne: ['$senderId', mongoose.Types.ObjectId(userId)] },
                  { $not: { $in: [mongoose.Types.ObjectId(userId), '$readBy.user'] } }
                ]
              },
              1,
              0
            ]
          }
        }
      }
    },
    {
      $lookup: {
        from: 'users',
        localField: '_id',
        foreignField: '_id',
        as: 'user'
      }
    },
    { $unwind: '$user' },
    {
      $project: {
        'user.password': 0,
        'user.tokens': 0,
        'lastMessage.readBy': 0
      }
    },
    { $sort: { 'lastMessage.createdAt': -1 } },
    { $limit: limit }
  ]);
};

// الحصول على محادثة بين مستخدمين
MessageSchema.statics.getConversation = async function(user1Id, user2Id, limit = 50, before = null) {
  const match = {
    $or: [
      { senderId: user1Id, receiverId: user2Id },
      { senderId: user2Id, receiverId: user1Id }
    ],
    'metadata.isDeleted': false
  };
  
  if (before) {
    match.createdAt = { $lt: new Date(before) };
  }
  
  return this.find(match)
    .sort({ createdAt: -1 })
    .limit(limit)
    .populate('sender', 'name avatar email')
    .populate('receiver', 'name avatar email')
    .populate('replyTo', 'content senderId')
    .populate('replyTo.senderId', 'name')
    .lean();
};

// تصدير النموذج
module.exports = mongoose.model('Message', MessageSchema);
