// نماذج البيانات لصفحة Reels المتكاملة مع الباك إند

// نموذج المستخدم
class ReelUser {
  final String id;
  final String username;
  final String? avatarUrl;
  final String? bio;
  final int followersCount;
  final int followingCount;
  final bool isVerified;
  final bool isFollowing;

  ReelUser({
    required this.id,
    required this.username,
    this.avatarUrl,
    this.bio,
    required this.followersCount,
    required this.followingCount,
    required this.isVerified,
    required this.isFollowing,
  });

  factory ReelUser.fromJson(Map<String, dynamic> json) {
    return ReelUser(
      id: json['_id'] ?? json['id'] ?? '',
      username: json['username'] ?? 'مستخدم غير معروف',
      avatarUrl: json['avatarUrl'],
      bio: json['bio'],
      followersCount: json['followersCount'] ?? 0,
      followingCount: json['followingCount'] ?? 0,
      isVerified: json['isVerified'] ?? false,
      isFollowing: json['isFollowing'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'username': username,
      'avatarUrl': avatarUrl,
      'bio': bio,
      'followersCount': followersCount,
      'followingCount': followingCount,
      'isVerified': isVerified,
    };
  }
}

// نموذج التعليق
class ReelComment {
  final String id;
  final String userId;
  final String username;
  final String? avatarUrl;
  final String content;
  final DateTime createdAt;
  final int likes;
  final bool isLiked;
  final List<ReelComment> replies;

  ReelComment({
    required this.id,
    required this.userId,
    required this.username,
    this.avatarUrl,
    required this.content,
    required this.createdAt,
    required this.likes,
    required this.isLiked,
    required this.replies,
  });

  factory ReelComment.fromJson(Map<String, dynamic> json) {
    return ReelComment(
      id: json['_id'] ?? json['id'] ?? '',
      userId: json['userId'] ?? '',
      username: json['username'] ?? 'مستخدم غير معروف',
      avatarUrl: json['avatarUrl'],
      content: json['content'] ?? '',
      createdAt: json['createdAt'] != null 
          ? DateTime.parse(json['createdAt']) 
          : DateTime.now(),
      likes: json['likes'] ?? 0,
      isLiked: json['isLiked'] ?? false,
      replies: (json['replies'] as List<dynamic>?)
          ?.map((reply) => ReelComment.fromJson(reply))
          .toList() ?? [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'content': content,
    };
  }
}

// نموذج السؤال التعليمي
class ReelQuiz {
  final String id;
  final String question;
  final List<String> options;
  final String correctAnswer;
  final String explanation;
  final int points;
  final String difficulty;

  ReelQuiz({
    required this.id,
    required this.question,
    required this.options,
    required this.correctAnswer,
    required this.explanation,
    required this.points,
    required this.difficulty,
  });

  factory ReelQuiz.fromJson(Map<String, dynamic> json) {
    return ReelQuiz(
      id: json['_id'] ?? json['id'] ?? '',
      question: json['question'] ?? '',
      options: (json['options'] as List<dynamic>?)
          ?.map((option) => option.toString())
          .toList() ?? [],
      correctAnswer: json['correctAnswer'] ?? '',
      explanation: json['explanation'] ?? '',
      points: json['points'] ?? 10,
      difficulty: json['difficulty'] ?? 'easy',
    );
  }
}

// نموذج الفيديو التعليمي المحدث
class ReelVideo {
  final String id;
  final String userId;
  final ReelUser? author;
  final String title;
  final String description;
  final String? educationalContent;
  final String category;
  final List<String> tags;
  final Map<String, String> videoUrls; // جودات مختلفة
  final String? thumbnailUrl;
  final String? fallbackAsset;
  final Duration duration;
  final DateTime createdAt;
  final DateTime? updatedAt;
  
  // إحصائيات التفاعل
  final int likes;
  final int dislikes;
  final int views;
  final int shares;
  final int commentsCount;
  final double rating;
  
  // حالات المستخدم
  final bool isLiked;
  final bool isDisliked;
  final bool isSaved;
  final bool isFollowing;
  final bool hasWatched;
  
  // المحتوى التعليمي
  final ReelQuiz? quiz;
  final List<String> learningObjectives;
  final String difficulty;
  final int estimatedWatchTime;
  
  // إعدادات الخصوصية
  final String privacy; // public, private, unlisted
  final bool allowComments;
  final bool allowDownload;
  final bool isPromoted;

  ReelVideo({
    required this.id,
    required this.userId,
    this.author,
    required this.title,
    required this.description,
    this.educationalContent,
    required this.category,
    required this.tags,
    required this.videoUrls,
    this.thumbnailUrl,
    this.fallbackAsset,
    required this.duration,
    required this.createdAt,
    this.updatedAt,
    required this.likes,
    required this.dislikes,
    required this.views,
    required this.shares,
    required this.commentsCount,
    required this.rating,
    required this.isLiked,
    required this.isDisliked,
    required this.isSaved,
    required this.isFollowing,
    required this.hasWatched,
    this.quiz,
    required this.learningObjectives,
    required this.difficulty,
    required this.estimatedWatchTime,
    required this.privacy,
    required this.allowComments,
    required this.allowDownload,
    required this.isPromoted,
  });

  factory ReelVideo.fromJson(Map<String, dynamic> json) {
    return ReelVideo(
      id: json['_id'] ?? json['id'] ?? '',
      userId: json['userId'] ?? '',
      author: json['author'] != null ? ReelUser.fromJson(json['author']) : null,
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      educationalContent: json['educationalContent'],
      category: json['category'] ?? 'general',
      tags: (json['tags'] as List<dynamic>?)
          ?.map((tag) => tag.toString())
          .toList() ?? [],
      videoUrls: Map<String, String>.from(json['videoUrls'] ?? {}),
      thumbnailUrl: json['thumbnailUrl'],
      fallbackAsset: json['fallbackAsset'],
      duration: Duration(seconds: json['duration'] ?? 0),
      createdAt: json['createdAt'] != null 
          ? DateTime.parse(json['createdAt']) 
          : DateTime.now(),
      updatedAt: json['updatedAt'] != null 
          ? DateTime.parse(json['updatedAt']) 
          : null,
      likes: json['likes'] ?? 0,
      dislikes: json['dislikes'] ?? 0,
      views: json['views'] ?? 0,
      shares: json['shares'] ?? 0,
      commentsCount: json['commentsCount'] ?? 0,
      rating: (json['rating'] ?? 0.0).toDouble(),
      isLiked: json['isLiked'] ?? false,
      isDisliked: json['isDisliked'] ?? false,
      isSaved: json['isSaved'] ?? false,
      isFollowing: json['isFollowing'] ?? false,
      hasWatched: json['hasWatched'] ?? false,
      quiz: json['quiz'] != null ? ReelQuiz.fromJson(json['quiz']) : null,
      learningObjectives: (json['learningObjectives'] as List<dynamic>?)
          ?.map((obj) => obj.toString())
          .toList() ?? [],
      difficulty: json['difficulty'] ?? 'beginner',
      estimatedWatchTime: json['estimatedWatchTime'] ?? 0,
      privacy: json['privacy'] ?? 'public',
      allowComments: json['allowComments'] ?? true,
      allowDownload: json['allowDownload'] ?? false,
      isPromoted: json['isPromoted'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'description': description,
      'educationalContent': educationalContent,
      'category': category,
      'tags': tags,
      'learningObjectives': learningObjectives,
      'difficulty': difficulty,
      'privacy': privacy,
      'allowComments': allowComments,
      'allowDownload': allowDownload,
    };
  }

  // نسخ الفيديو مع تحديث بعض القيم
  ReelVideo copyWith({
    int? likes,
    int? dislikes,
    int? views,
    int? shares,
    int? commentsCount,
    double? rating,
    bool? isLiked,
    bool? isDisliked,
    bool? isSaved,
    bool? isFollowing,
    bool? hasWatched,
  }) {
    return ReelVideo(
      id: id,
      userId: userId,
      author: author,
      title: title,
      description: description,
      educationalContent: educationalContent,
      category: category,
      tags: tags,
      videoUrls: videoUrls,
      thumbnailUrl: thumbnailUrl,
      fallbackAsset: fallbackAsset,
      duration: duration,
      createdAt: createdAt,
      updatedAt: updatedAt,
      likes: likes ?? this.likes,
      dislikes: dislikes ?? this.dislikes,
      views: views ?? this.views,
      shares: shares ?? this.shares,
      commentsCount: commentsCount ?? this.commentsCount,
      rating: rating ?? this.rating,
      isLiked: isLiked ?? this.isLiked,
      isDisliked: isDisliked ?? this.isDisliked,
      isSaved: isSaved ?? this.isSaved,
      isFollowing: isFollowing ?? this.isFollowing,
      hasWatched: hasWatched ?? this.hasWatched,
      quiz: quiz,
      learningObjectives: learningObjectives,
      difficulty: difficulty,
      estimatedWatchTime: estimatedWatchTime,
      privacy: privacy,
      allowComments: allowComments,
      allowDownload: allowDownload,
      isPromoted: isPromoted,
    );
  }

  // الحصول على الوقت المنسق
  String get formattedDuration {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  // الحصول على تاريخ النشر المنسق
  String get formattedCreatedAt {
    final now = DateTime.now();
    final difference = now.difference(createdAt);
    
    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inHours < 1) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inDays < 1) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} يوم';
    } else {
      return '${createdAt.day}/${createdAt.month}/${createdAt.year}';
    }
  }
}

// نموذج استجابة API
class ReelsResponse {
  final bool success;
  final int count;
  final List<ReelVideo> reels;
  final Map<String, dynamic>? pagination;

  ReelsResponse({
    required this.success,
    required this.count,
    required this.reels,
    this.pagination,
  });

  factory ReelsResponse.fromJson(Map<String, dynamic> json) {
    return ReelsResponse(
      success: json['success'] ?? false,
      count: json['count'] ?? 0,
      reels: (json['reels'] as List<dynamic>?)
          ?.map((reel) => ReelVideo.fromJson(reel))
          .toList() ?? [],
      pagination: json['pagination'],
    );
  }
}
