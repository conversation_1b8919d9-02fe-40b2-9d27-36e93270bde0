import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';
import 'package:share_plus/share_plus.dart';
import '../models/profession_model.dart';
import '../services/profession_service.dart';

class ProfessionDetailsPage extends StatelessWidget {
  final String professionTitle;
  final Profession profession;

  const ProfessionDetailsPage({
    super.key,
    required this.professionTitle,
    required this.profession,
  });

  String _buildImageUrl(String baseUrl, String imagePath) {
    if (profession.isLocal) {
      return imagePath;
    }
    return '$baseUrl/$imagePath';
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final professionService = ProfessionService();
    final imageUrl = profession.image.isNotEmpty
        ? _buildImageUrl(professionService.baseUrl, profession.image)
        : 'https://via.placeholder.com/300';

    return Scaffold(
      appBar: AppBar(
        title: Text(
          professionTitle,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: isDarkMode ? Colors.blue[900] : Colors.blue,
        actions: [
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: () {
              Share.share(
                'اكتشف مهنة ${profession.title}\n${profession.description}',
              );
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            profession.isLocal
                ? Image.asset(
              imageUrl,
              height: 300,
              width: double.infinity,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return const Icon(
                  Icons.broken_image,
                  size: 100,
                  color: Colors.blue,
                );
              },
            )
                : CachedNetworkImage(
              imageUrl: imageUrl,
              height: 300,
              width: double.infinity,
              fit: BoxFit.cover,
              placeholder: (context, url) => Lottie.asset(
                'assets/animations/loading.json',
                height: 300,
              ),
              errorWidget: (context, url, error) {
                return const Icon(
                  Icons.broken_image,
                  size: 100,
                  color: Colors.blue,
                );
              },
            ),
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    profession.title,
                    style: TextStyle(
                      fontSize: 28,
                      fontWeight: FontWeight.bold,
                      color: isDarkMode ? Colors.blue[300] : Colors.blue,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'الوصف:',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: isDarkMode ? Colors.white : Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    profession.description,
                    style: TextStyle(
                      fontSize: 16,
                      color: isDarkMode ? Colors.grey[300] : Colors.black54,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'الراتب المتوسط: ${profession.averageSalary} ريال',
                    style: TextStyle(
                      fontSize: 18,
                      color: isDarkMode ? Colors.white : Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'مستوى الطلب: ${profession.demand}',
                    style: TextStyle(
                      fontSize: 18,
                      color: isDarkMode ? Colors.white : Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'المهارات المطلوبة:',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: isDarkMode ? Colors.white : Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Wrap(
                    spacing: 8,
                    children: profession.requiredSkills
                        .map((skill) => Chip(
                      label: Text(skill),
                      backgroundColor: isDarkMode ? Colors.blue[700] : Colors.blue[100],
                    ))
                        .toList(),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}