import 'package:latlong2/latlong.dart';

class RideRequest {
  final String id;
  final LatLng pickup;
  final String destination;
  final LatLng? destinationLocation;
  final DateTime timestamp;
  String status;
  String? driverId;
  final String requestType;
  final String? passengerId;
  final bool isUrgent; // حقل جديد للطلبات العاجلة
  final double? rating; // حقل جديد لتقييم الرحلة

  RideRequest({
    required this.id,
    required this.pickup,
    required this.destination,
    this.destinationLocation,
    required this.timestamp,
    this.status = 'Pending',
    this.driverId,
    required this.requestType,
    this.passengerId,
    this.isUrgent = false,
    this.rating,
  });

  RideRequest copyWith({
    String? id,
    LatLng? pickup,
    String? destination,
    LatLng? destinationLocation,
    DateTime? timestamp,
    String? status,
    String? driverId,
    String? requestType,
    String? passengerId,
    bool? isUrgent,
    double? rating,
  }) {
    return RideRequest(
      id: id ?? this.id,
      pickup: pickup ?? this.pickup,
      destination: destination ?? this.destination,
      destinationLocation: destinationLocation ?? this.destinationLocation,
      timestamp: timestamp ?? this.timestamp,
      status: status ?? this.status,
      driverId: driverId ?? this.driverId,
      requestType: requestType ?? this.requestType,
      passengerId: passengerId ?? this.passengerId,
      isUrgent: isUrgent ?? this.isUrgent,
      rating: rating ?? this.rating,
    );
  }
}
