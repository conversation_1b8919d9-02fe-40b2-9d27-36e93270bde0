const ErrorResponse = require('../utils/errorResponse');
const asyncHandler = require('./async');
const { Role } = require('./roleMiddleware');

/**
 * Middleware للتحقق من أن المستخدم لديه دور hokama
 * @param {Object} req - كائن الطلب
 * @param {Object} res - كائن الاستجابة
 * @param {Function} next - الوظيفة التالية في السلسلة
 */
const hokamaAccess = asyncHandler(async (req, res, next) => {
  // التحقق من وجود مستخدم في الطلب
  if (!req.user) {
    return next(new ErrorResponse('يجب تسجيل الدخول أولاً', 401));
  }

  // التحقق من أن المستخدم لديه دور hokama
  if (req.user.role !== Role.HOKAMA) {
    return next(
      new ErrorResponse('غير مصرح لك بالوصول إلى هذا المورد', 403)
    );
  }

  next();
});

module.exports = hokamaAccess;
