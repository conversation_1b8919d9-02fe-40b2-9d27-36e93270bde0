import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'id22.dart';
import 'appstate.dart';

class ShareBorrowScreen extends StatelessWidget {
  const ShareBorrowScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => ThemeProvider()),
        ChangeNotifierProvider(create: (_) => AppState()),
        Provider<ShareItemService>(
          create: (context) => ShareItemService(
            baseUrl: 'https://your-api-url.com/api',
            authToken: context.read<AppState>().authToken ?? '',
          ),
        ),
      ],
      child: const ShareApp(),
    );
  }
}
