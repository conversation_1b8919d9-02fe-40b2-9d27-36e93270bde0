const mongoose = require('mongoose');

const professionSchema = new mongoose.Schema({
  title: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    required: true
  },
  image: {
    type: String,
    required: true
  },
  isLocal: {
    type: Boolean,
    default: false
  },
  averageSalary: {
    type: Number,
    required: true
  },
  demand: {
    type: String,
    enum: ['high', 'medium', 'low'],
    default: 'medium'
  },
  difficulty: {
    type: String,
    enum: ['beginner', 'intermediate', 'advanced'],
    default: 'beginner'
  },
  courseDuration: {
    type: Number,
    min: 1,
    default: 1
  },
  requiredSkills: [{
    type: String,
    trim: true
  }],
  category: {
    type: String,
    enum: ['technical', 'creative', 'service', 'health', 'education', 'social'],
    required: true
  },
  resources: [{
    title: String,
    description: String,
    url: String,
    type: {
      type: String,
      enum: ['book', 'article', 'video', 'course', 'other']
    }
  }],
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Add text index for search
professionSchema.index({
  title: 'text',
  description: 'text',
  'requiredSkills': 'text'
});

// Update the updatedAt field before saving
professionSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

// Static method for searching professions
professionSchema.statics.search = async function(query) {
  return this.find(
    { $text: { $search: query } },
    { score: { $meta: 'textScore' } }
  ).sort({ score: { $meta: 'textScore' } });
};

// Static method for filtering professions
professionSchema.statics.filter = async function(filters) {
  const { category, minSalary, maxSalary, difficulty, demand } = filters;
  const query = {};

  if (category) query.category = category;
  if (minSalary) query.averageSalary = { $gte: minSalary };
  if (maxSalary) {
    query.averageSalary = query.averageSalary || {};
    query.averageSalary.$lte = maxSalary;
  }
  if (difficulty) query.difficulty = difficulty;
  if (demand) query.demand = demand;

  return this.find(query);
};

module.exports = mongoose.model('Profession', professionSchema);
