import 'package:flutter/material.dart';

class ManagePostsPage extends StatelessWidget {
  const ManagePostsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة المنشورات', style: TextStyle(fontFamily: '<PERSON><PERSON><PERSON>')),
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          ListTile(
            leading: const Icon(Icons.article),
            title: const Text('منشور 1', style: TextStyle(fontFamily: 'Tajawal')),
            subtitle: const Text('محتوى المنشور الأول...'),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                IconButton(icon: const Icon(Icons.edit), onPressed: () {}),
                IconButton(icon: const Icon(Icons.delete), onPressed: () {}),
              ],
            ),
          ),
          ListTile(
            leading: const Icon(Icons.article),
            title: const Text('منشور 2', style: TextStyle(fontFamily: '<PERSON><PERSON><PERSON>')),
            subtitle: const Text('محتوى المنشور الثاني...'),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                IconButton(icon: const Icon(Icons.edit), onPressed: () {}),
                IconButton(icon: const Icon(Icons.delete), onPressed: () {}),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
