const mongoose = require('mongoose');

const challengeSchema = new mongoose.Schema({
  title: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    required: true
  },
  reward: {
    type: Number,
    required: true,
    min: 0
  },
  target: {
    type: Number,
    required: true,
    min: 1
  },
  startDate: {
    type: Date,
    default: Date.now
  },
  endDate: {
    type: Date,
    required: true,
    validate: {
      validator: function(value) {
        return value > this.startDate;
      },
      message: 'End date must be after start date'
    }
  },
  reward: {
    type: Number,
    required: true,
    min: 0,
    default: 10
  },
  target: {
    type: Number,
    required: true,
    min: 1,
    default: 1
  },
  isActive: {
    type: Boolean,
    default: true
  },
  participants: [{
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    progress: {
      type: Number,
      default: 0,
      min: 0
    },
    completed: {
      type: Boolean,
      default: false
    },
    completedAt: {
      type: Date
    },
    joinedAt: {
      type: Date,
      default: Date.now
    }
  }],
  // Additional fields for tracking
  totalParticipants: {
    type: Number,
    default: 0
  },
  completions: {
    type: Number,
    default: 0
  },
  // Metadata for analytics
  metadata: {
    type: Map,
    of: mongoose.Schema.Types.Mixed
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for better query performance
challengeSchema.index({ isActive: 1, endDate: 1 });
challengeSchema.index({ 'participants.userId': 1 });
challengeSchema.index({ startDate: -1 });

module.exports = mongoose.model('Challenge', challengeSchema);
