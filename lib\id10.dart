import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:http/http.dart' as http;
import 'package:provider/provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:socket_io_client/socket_io_client.dart' as io;
import 'package:timeago/timeago.dart' as timeago;
import 'appstate.dart';
import 'models/ad_model.dart';

// تهيئة Timeago للغة العربية
void setupTimeago() => timeago.setLocaleMessages('ar', timeago.ArMessages());

// خدمة إدارة الإعلانات
class AdService {
  final String baseUrl;
  final String authToken;
  io.Socket? socket;

  AdService({required this.baseUrl, required this.authToken});

  Future<Map<String, dynamic>> fetchAds({
    String search = '',
    String filter = 'all',
    String sort = 'newest',
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final response = await http.get(
        Uri.parse(
            '$baseUrl/ads?filter=$filter&sort=$sort&search=${Uri.encodeQueryComponent(search)}&page=$page&limit=$limit'),
        headers: {'x-auth-token': authToken},
      ).timeout(const Duration(seconds: 10));
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return {
          'ads': (data['ads'] as List).map((json) => Ad.fromJson(json)).toList(),
          'total': data['total'] as int,
          'page': data['page'] as int,
        };
      } else {
        throw Exception('فشل في جلب الإعلانات: ${response.statusCode} ${response.body}');
      }
    } catch (e) {
      print('Error fetching ads: $e');
      throw Exception('خطأ في جلب الإعلانات: $e');
    }
  }

  Future<List<Map<String, dynamic>>> fetchNotifications() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/notifications'),
        headers: {'x-auth-token': authToken},
      ).timeout(const Duration(seconds: 10));
      if (response.statusCode == 200) {
        return List<Map<String, dynamic>>.from(jsonDecode(response.body));
      } else {
        throw Exception('فشل في جلب الإشعارات: ${response.statusCode} ${response.body}');
      }
    } catch (e) {
      print('Error fetching notifications: $e');
      throw Exception('خطأ في جلب الإشعارات: $e');
    }
  }

  Future<void> updateNotification(String notificationId) async {
    try {
      final response = await http.put(
        Uri.parse('$baseUrl/notifications/$notificationId'),
        headers: {'x-auth-token': authToken},
      ).timeout(const Duration(seconds: 10));
      if (response.statusCode != 200) {
        throw Exception('فشل في تحديث الإشعار: ${response.statusCode} ${response.body}');
      }
    } catch (e) {
      print('Error updating notification: $e');
      throw Exception('خطأ في تحديث الإشعار: $e');
    }
  }

  Future<void> createAd(String title, String description, String category,
      String location, double? price, File? file, String creatorId) async {
    try {
      var request = http.MultipartRequest('POST', Uri.parse('$baseUrl/ads'));
      request.headers['x-auth-token'] = authToken;
      request.fields['title'] = title;
      request.fields['description'] = description;
      request.fields['category'] = category;
      request.fields['location'] = location;
      request.fields['creatorId'] = creatorId;
      if (price != null) request.fields['price'] = price.toString();
      if (file != null) {
        if (await file.length() > 10 * 1024 * 1024) {
          throw Exception('حجم الملف كبير جدًا (يجب أن يكون أقل من 10 ميجابايت)');
        }
        request.files.add(await http.MultipartFile.fromPath('file', file.path));
      }
      final response = await request.send().timeout(const Duration(seconds: 15));
      if (response.statusCode != 201) {
        final responseBody = await response.stream.bytesToString();
        throw Exception('فشل في إنشاء الإعلان: ${response.statusCode} $responseBody');
      }
    } catch (e) {
      print('Error creating ad: $e');
      throw Exception('خطأ في إنشاء الإعلان: $e');
    }
  }

  Future<void> likeAd(String adId, int newLikes, bool isLiked) async {
    try {
      final response = await http.put(
        Uri.parse('$baseUrl/ads/$adId/like'),
        headers: {
          'x-auth-token': authToken,
          'Content-Type': 'application/json',
        },
        body: jsonEncode({'likes': newLikes, 'isLiked': isLiked}),
      ).timeout(const Duration(seconds: 10));
      if (response.statusCode != 200) {
        throw Exception('فشل في تحديث الإعجاب: ${response.statusCode} ${response.body}');
      }
    } catch (e) {
      print('Error liking ad: $e');
      throw Exception('خطأ في تحديث الإعجاب: $e');
    }
  }

  void setupSocketListeners(io.Socket socket, Function(dynamic) onNewAd) {
    this.socket = socket;
    socket.onConnect((_) => print('Socket connected'));
    socket.onDisconnect((_) => print('Socket disconnected'));
    socket.on('new_ad', (data) {
      print('New ad received: $data');
      onNewAd(data);
    });
    socket.onConnectError((error) => print('Socket connect error: $error'));
    socket.onError((error) => print('Socket error: $error'));
  }
}

class Rayan10 extends StatelessWidget {
  const Rayan10({super.key});

  @override
  Widget build(BuildContext context) {
    return const AdsHomeScreen();
  }
}

class AdsHomeScreen extends StatefulWidget {
  const AdsHomeScreen({super.key});

  @override
  _AdsHomeScreenState createState() => _AdsHomeScreenState();
}

class _AdsHomeScreenState extends State<AdsHomeScreen> with TickerProviderStateMixin {
  // Flag to prevent nested mouse event handling
  bool _isHandlingMouseEvent = false;
  final PageController _adController = PageController();
  final ScrollController _scrollController = ScrollController();
  final TextEditingController _searchController = TextEditingController();
  late final AdService _adService;
  late io.Socket _socket;

  int _currentAd = 0;
  int _selectedCategory = 0;
  String _selectedSort = 'newest';
  String _selectedFilter = 'all';
  Timer? _timer;
  bool _isLoadingMore = false;
  bool _isLoadingInitial = true;
  int _page = 1;
  static const int _limit = 20;
  int _totalAds = 0;
  late AnimationController _fabAnimationController;
  late Animation<double> _fabAnimation;

  final List<Ad> _ads = [];
  List<Map<String, dynamic>> _notifications = [];
  final List<String> adImages = [
    'assets/images/ad1.jpg',
    'assets/images/ad2.jpg',
    'assets/images/ad3.jpg',
  ];

  final List<Map<String, dynamic>> categories = const [
    {'icon': Icons.carpenter, 'title': 'نجار', 'filter': 'carpenter'},
    {'icon': Icons.plumbing, 'title': 'سباك', 'filter': 'plumber'},
    {'icon': Icons.electrical_services, 'title': 'كهربائي', 'filter': 'electrician'},
    {'icon': Icons.power, 'title': 'كهربائي سيارات', 'filter': 'car_electrician'},
    {'icon': Icons.local_shipping, 'title': 'سائق نقل', 'filter': 'transport_driver'},
    {'icon': Icons.car_repair, 'title': 'ميكانيكي سيارات', 'filter': 'car_mechanic'},
    {'icon': Icons.local_shipping, 'title': 'عفشجي', 'filter': 'mover'},
    {'icon': Icons.hardware, 'title': 'سمكري', 'filter': 'carpenter_metal'},
    {'icon': Icons.tire_repair, 'title': 'اصلاح اطارات', 'filter': 'tire_repair'},
    {'icon': Icons.ac_unit, 'title': 'تكييف سيارات', 'filter': 'car_ac'},
    {'icon': Icons.emergency, 'title': 'انقاذ و طوارئ', 'filter': 'rescue'},
    {'icon': Icons.build_circle, 'title': 'قطع غيار', 'filter': 'spare_parts'},
    {'icon': Icons.construction, 'title': 'رخام وجرانيت', 'filter': 'marble_granite'},
    {'icon': Icons.wallpaper, 'title': 'جبسمبورد', 'filter': 'gypsum_board'},
    {'icon': Icons.format_paint, 'title': 'جبس وكارنيش', 'filter': 'gypsum_cornice'},
    {'icon': Icons.cut, 'title': 'مصفف شعر', 'filter': 'hairdresser'},
    {'icon': Icons.gavel, 'title': 'هدم وشيال', 'filter': 'demolition'},
    {'icon': Icons.local_laundry_service, 'title': 'مكواه وغسيل', 'filter': 'laundry'},
    {'icon': Icons.palette, 'title': 'استرجي و ديكورات', 'filter': 'decorator'},
    {'icon': Icons.satellite, 'title': 'دش و تليفزيون', 'filter': 'satellite_tv'},
    {'icon': Icons.blinds, 'title': 'ستائر و تنجيد', 'filter': 'curtains_upholstery'},
    {'icon': Icons.window, 'title': 'الوميتال', 'filter': 'aluminum'},
    {'icon': Icons.handyman, 'title': 'صيانه اجهزه كهربائيه', 'filter': 'appliance_repair'},
    {'icon': Icons.directions_car, 'title': 'سائق ملاكي', 'filter': 'private_driver'},
    {'icon': Icons.hardware, 'title': 'حداد', 'filter': 'blacksmith'},
    {'icon': Icons.layers, 'title': 'باركيه', 'filter': 'parquet'},
    {'icon': Icons.format_shapes, 'title': 'سراميك', 'filter': 'ceramic'},
    {'icon': Icons.window, 'title': 'زجاج', 'filter': 'glass'},
    {'icon': Icons.brush, 'title': 'نقاش', 'filter': 'painter'},
    {'icon': Icons.foundation, 'title': 'اعمال بناء', 'filter': 'construction'},
  ];

  // Mock search suggestions
  final List<String> _searchSuggestions = [
    'نجار أثاث',
    'سباك 24 ساعة',
    'كهربائي منازل',
    'ميكانيكي سيارات',
    'نقل عفش',
    'تصليح تكييف',
  ];

  @override
  void initState() {
    super.initState();
    setupTimeago();
    print('AdsHomeScreen initState started');

    // Initialize AdService
    final appState = Provider.of<AppState>(context, listen: false);
    _adService = AdService(
      baseUrl: AppState.getBackendUrl(),
      authToken: appState.token ?? defaultAuthToken,
    );

    // Initialize socket
    _socket = io.io(AppState.getBackendUrl(), <String, dynamic>{
      'transports': ['websocket'],
      'autoConnect': false,
    });
    _socket.connect();
    _adService.setupSocketListeners(_socket, (data) {
      print('Received new ad via socket: $data');
      _fetchAds(1);
    });

    // Initialize FAB animation
    _fabAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    _fabAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fabAnimationController, curve: Curves.easeOut),
    );
    _fabAnimationController.forward();

    // Start auto-scroll and listeners
    _startAutoScroll();
    _scrollController.addListener(_scrollListener);
    _searchController.addListener(() {
      _debounce(() => _fetchAds(1));
    });

    // Fetch initial data
    _loadInitialData();
  }

  Future<void> _loadInitialData() async {
    print('Loading initial data...');
    try {
      await Future.wait([
        _fetchAds(1),
        _fetchNotifications(),
      ]);
      if (mounted) {
        setState(() {
          _isLoadingInitial = false;
          print('Initial data loaded, _isLoadingInitial set to false');
        });
      }
    } catch (e) {
      print('Error loading initial data: $e');
      if (mounted) {
        setState(() {
          _isLoadingInitial = false;
          print('Initial data failed, _isLoadingInitial set to false');
        });
        _showSnackBar('خطأ في تحميل البيانات الأولية: $e', Colors.red);
      }
    }
  }

  void _startAutoScroll() {
    _timer = Timer.periodic(const Duration(seconds: 5), (timer) {
      if (!mounted) {
        timer.cancel();
        return;
      }
      if (!_adController.hasClients) {
        return;
      }
      if (mounted) {
        setState(() {
          _currentAd = (_currentAd < adImages.length - 1) ? _currentAd + 1 : 0;
          if (_adController.hasClients) {
            _adController.animateToPage(
              _currentAd,
              duration: const Duration(milliseconds: 500),
              curve: Curves.easeInOut,
            );
          }
        });
      }
    });
  }

  void _scrollListener() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 50 &&
        !_isLoadingMore &&
        _ads.length < _totalAds) {
      _loadMoreAds();
    }
  }

  Future<void> _loadMoreAds() async {
    if (_isLoadingMore) return;
    setState(() {
      _isLoadingMore = true;
      print('Loading more ads, page: ${_page + 1}');
    });
    await _fetchAds(_page + 1);
    if (mounted) {
      setState(() {
        _isLoadingMore = false;
        print('More ads loaded');
      });
    }
  }

  Future<void> _fetchAds(int page) async {
    try {
      print('Fetching ads, page: $page, filter: $_selectedFilter, sort: $_selectedSort, search: ${_searchController.text}');
      final data = await _adService.fetchAds(
        search: _searchController.text,
        filter: _selectedFilter,
        sort: _selectedSort,
        page: page,
        limit: _limit,
      );
      if (mounted) {
        setState(() {
          if (page == 1) _ads.clear();
          _ads.addAll(data['ads'] as List<Ad>);
          _totalAds = data['total'] as int;
          _page = data['page'] as int;
          print('Fetched ${_ads.length} ads, total: $_totalAds');
        });
      }
    } catch (e) {
      print('Error in _fetchAds: $e');
      if (mounted) {
        _showSnackBar('خطأ في جلب الإعلانات: $e', Colors.red);
      }
    }
  }

  Future<void> _fetchNotifications() async {
    try {
      print('Fetching notifications');
      _notifications = await _adService.fetchNotifications();
      if (mounted) {
        setState(() {
          print('Fetched ${_notifications.length} notifications');
        });
      }
    } catch (e) {
      print('Error in _fetchNotifications: $e');
      if (mounted) {
        _showSnackBar('خطأ في جلب الإشعارات: $e', Colors.red);
      }
    }
  }

  Future<void> _refreshData() async {
    print('Refreshing data');
    setState(() {
      _isLoadingInitial = true;
    });
    await Future.wait([_fetchAds(1), _fetchNotifications()]);
    if (mounted) {
      setState(() {
        _isLoadingInitial = false;
        print('Data refreshed');
      });
    }
  }

  Timer? _debounceTimer;
  void _debounce(VoidCallback callback) {
    const duration = Duration(milliseconds: 300);
    _debounceTimer?.cancel();
    _debounceTimer = Timer(duration, callback);
  }

  @override
  void dispose() {
    print('Disposing AdsHomeScreen');
    _debounceTimer?.cancel();
    _timer?.cancel();
    _adController.dispose();
    _scrollController.dispose();
    _searchController.dispose();
    _fabAnimationController.dispose();
    _socket.disconnect();
    _socket.dispose();
    super.dispose();
  }

  void _showSnackBar(String message, [Color? backgroundColor, SnackBarAction? action]) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: backgroundColor ?? Colors.teal,
          behavior: SnackBarBehavior.floating,
          action: action,
          elevation: 6,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    print('Building AdsHomeScreen, _isLoadingInitial: $_isLoadingInitial');
    return Scaffold(
      body: _isLoadingInitial
          ? _buildSkeletonLoader()
          : RefreshIndicator(
        onRefresh: _refreshData,
        color: Colors.teal,
        child: CustomScrollView(
          controller: _scrollController,
          slivers: [
            _buildSliverAppBar(isDarkMode),
            SliverToBoxAdapter(child: _buildFilterChips()),
            SliverToBoxAdapter(child: _buildAdsCarousel()),
            SliverPersistentHeader(
              pinned: true,
              delegate: _StickyHeaderDelegate(child: _buildCategories()),
            ),
            SliverToBoxAdapter(child: _buildAdsList()),
            if (_isLoadingMore)
              const SliverToBoxAdapter(
                child: Padding(
                  padding: EdgeInsets.all(16),
                  child: CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.teal),
                  ),
                ),
              ),
          ],
        ),
      ),
      floatingActionButton: ScaleTransition(
        scale: _fabAnimation,
        child: FloatingActionButton(
          onPressed: () => _showCreateAdDialog(context),
          backgroundColor: Colors.teal,
          tooltip: 'إنشاء إعلان',
          elevation: 8,
          child: const Icon(Icons.post_add, size: 30, color: Colors.white),
        ),
      ),
    );
  }

  Widget _buildSkeletonLoader() {
    print('Building skeleton loader');
    return SingleChildScrollView(
      child: Column(
        children: [
          Container(
            height: 200,
            margin: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(12),
            ),
          ),
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: 0.75,
              crossAxisSpacing: 8,
              mainAxisSpacing: 8,
            ),
            itemCount: 6,
            itemBuilder: (context, index) => Container(
              margin: const EdgeInsets.all(4),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.2),
                    spreadRadius: 1,
                    blurRadius: 4,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  SliverAppBar _buildSliverAppBar(bool isDarkMode) {
    return SliverAppBar(
      expandedHeight: 120,
      flexibleSpace: FlexibleSpaceBar(
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: isDarkMode
                  ? [Colors.teal[900]!, Colors.teal[700]!]
                  : [Colors.teal, Colors.tealAccent],
            ),
          ),
          child: SafeArea(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Column(
                children: [
                  TextField(
                    controller: _searchController,
                    decoration: InputDecoration(
                      hintText: 'ابحث عن حرفي...',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(30),
                        borderSide: BorderSide.none,
                      ),
                      hintStyle: TextStyle(color: isDarkMode ? Colors.white70 : Colors.white),
                      prefixIcon: Icon(Icons.search, color: isDarkMode ? Colors.white : Colors.white),
                      suffixIcon: _searchController.text.isNotEmpty
                          ? IconButton(
                        icon: Icon(Icons.clear, color: isDarkMode ? Colors.white : Colors.white),
                        onPressed: () {
                          _searchController.clear();
                          _fetchAds(1);
                        },
                      )
                          : null,
                      filled: true,
                      fillColor: isDarkMode ? Colors.white12 : Colors.white24,
                    ),
                    style: TextStyle(color: isDarkMode ? Colors.white : Colors.white),
                    onChanged: (value) {
                      if (value.isNotEmpty) {
                        _showSearchSuggestions(context, value);
                      }
                    },
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
      actions: [
        Stack(
          children: [
            IconButton(
              icon: const Icon(Icons.notifications, color: Colors.white),
              onPressed: () => _showNotifications(context),
            ),
            if (_notifications.isNotEmpty)
              Positioned(
                right: 8,
                top: 8,
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 300),
                  padding: const EdgeInsets.all(4),
                  decoration: const BoxDecoration(
                    color: Colors.red,
                    shape: BoxShape.circle,
                  ),
                  constraints: const BoxConstraints(minWidth: 16, minHeight: 16),
                  child: Text(
                    '${_notifications.length}',
                    style: const TextStyle(color: Colors.white, fontSize: 10),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
          ],
        ),
        PopupMenuButton<String>(
          color: isDarkMode ? Colors.grey[800] : Colors.white,
          onSelected: (value) {
            setState(() {
              _selectedSort = value;
              _fetchAds(1);
            });
          },
          itemBuilder: (context) => const [
            PopupMenuItem(value: 'newest', child: Text('الأحدث')),
            PopupMenuItem(value: 'price_asc', child: Text('السعر من الأقل')),
            PopupMenuItem(value: 'price_desc', child: Text('السعر من الأعلى')),
            PopupMenuItem(value: 'likes', child: Text('الأكثر إعجابًا')),
          ],
        ),
      ],
      pinned: true,
    );
  }

  void _showSearchSuggestions(BuildContext context, String query) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        height: 200,
        padding: const EdgeInsets.all(16),
        child: ListView(
          children: _searchSuggestions
              .where((suggestion) => suggestion.toLowerCase().contains(query.toLowerCase()))
              .map((suggestion) => ListTile(
            title: Text(suggestion),
            onTap: () {
              _searchController.text = suggestion;
              _fetchAds(1);
              Navigator.pop(context);
            },
          ))
              .toList(),
        ),
      ),
    );
  }

  Widget _buildFilterChips() {
    return Container(
      height: 50,
      padding: const EdgeInsets.symmetric(horizontal: 8),
      child: ListView(
        scrollDirection: Axis.horizontal,
        children: [
          _buildFilterChip('الكل', 'all'),
          ...categories
              .map((category) => _buildFilterChip(category['title'] as String, category['filter'] as String))
              ,
        ],
      ),
    );
  }

  Widget _buildFilterChip(String label, String filter) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 4),
      child: GestureDetector(
        onTap: () {
          if (_isHandlingMouseEvent) return;
          _isHandlingMouseEvent = true;
          setState(() {
            _selectedFilter = filter;
            _fetchAds(1);
          });
          _isHandlingMouseEvent = false;
        },
        child: FilterChip(
          label: Text(label),
          selected: _selectedFilter == filter,
          onSelected: (v) {
            setState(() {
              _selectedFilter = filter;
              _fetchAds(1);
            });
          },
          selectedColor: isDarkMode ? Colors.teal[700] : Colors.teal[100],
          checkmarkColor: isDarkMode ? Colors.teal[200] : Colors.teal[900],
          backgroundColor: isDarkMode ? Colors.grey[800] : Colors.grey[200],
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
          elevation: _selectedFilter == filter ? 4 : 0,
        ),
      ),
    );
  }

  Widget _buildAdsCarousel() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return Column(
      children: [
        SizedBox(
          height: 200,
          child: PageView.builder(
            controller: _adController,
            itemCount: adImages.length,
            onPageChanged: (index) => setState(() => _currentAd = index),
            itemBuilder: (context, index) => GestureDetector(
              onTap: () => _onAdTap(index),
              child: Transform(
                transform: Matrix4.identity()..setEntry(3, 2, 0.001)..rotateX(0.1 * (_currentAd - index)),
                alignment: Alignment.center,
                child: Container(
                  margin: const EdgeInsets.symmetric(horizontal: 8),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.2),
                        spreadRadius: 2,
                        blurRadius: 8,
                      ),
                    ],
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(12),
                    child: Image.asset(
                      adImages[index],
                      fit: BoxFit.cover,
                      width: double.infinity,
                      height: 200,
                      errorBuilder: (context, error, stackTrace) => Container(
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [Colors.teal[200]!, Colors.teal[400]!],
                          ),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Center(
                          child: Icon(
                            Icons.image_not_supported_outlined,
                            color: Colors.white,
                            size: 50,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
        const SizedBox(height: 8),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: List.generate(
            adImages.length,
                (index) => AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              margin: const EdgeInsets.symmetric(horizontal: 4),
              width: _currentAd == index ? 20 : 8,
              height: 8,
              decoration: BoxDecoration(
                color: _currentAd == index ? Colors.teal : (isDarkMode ? Colors.grey[600] : Colors.grey),
                borderRadius: BorderRadius.circular(4),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildCategories() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return Container(
      height: 120,
      padding: const EdgeInsets.symmetric(horizontal: 8),
      color: isDarkMode ? Colors.grey[900] : Colors.white,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: categories.length,
        itemBuilder: (context, index) => _buildCategoryItem(index),
      ),
    );
  }

  Widget _buildCategoryItem(int index) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final count = _ads.where((ad) => ad.category == categories[index]['filter']).length;
    return GestureDetector(
      onTap: () {
        if (_isHandlingMouseEvent) return;
        _isHandlingMouseEvent = true;
        setState(() {
          _selectedCategory = index;
          _selectedFilter = categories[index]['filter'] as String;
          _fetchAds(1);
        });
        _isHandlingMouseEvent = false;
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        width: 100,
        padding: const EdgeInsets.all(8),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: _selectedCategory == index
                    ? (isDarkMode ? Colors.teal[800] : Colors.teal[50])
                    : (isDarkMode ? Colors.grey[800] : Colors.grey[200]),
                borderRadius: BorderRadius.circular(15),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.2),
                    spreadRadius: 1,
                    blurRadius: 4,
                  ),
                ],
              ),
              child: Icon(
                categories[index]['icon'] as IconData,
                color: _selectedCategory == index
                    ? Colors.teal
                    : (isDarkMode ? Colors.grey[400] : Colors.grey[700]),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '${categories[index]['title']} ($count)',
              style: TextStyle(
                color: _selectedCategory == index
                    ? Colors.teal
                    : (isDarkMode ? Colors.white70 : Colors.black),
                fontSize: 12,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAdsList() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    List<Ad> filteredAds = _ads.where((ad) {
      final matchesFilter = _selectedFilter == 'all' || ad.category == _selectedFilter;
      final matchesSearch = ad.title.toLowerCase().contains(_searchController.text.toLowerCase()) ||
          ad.description.toLowerCase().contains(_searchController.text.toLowerCase());
      return matchesFilter && matchesSearch;
    }).toList();

    switch (_selectedSort) {
      case 'newest':
        filteredAds.sort((a, b) => b.createdAt.compareTo(a.createdAt));
        break;
      case 'price_asc':
        filteredAds.sort((a, b) {
          final priceA = double.tryParse(a.price ?? '') ?? double.infinity;
          final priceB = double.tryParse(b.price ?? '') ?? double.infinity;
          return priceA.compareTo(priceB);
        });
        break;
      case 'price_desc':
        filteredAds.sort((a, b) {
          final priceA = double.tryParse(a.price ?? '') ?? -double.infinity;
          final priceB = double.tryParse(b.price ?? '') ?? -double.infinity;
          return priceB.compareTo(priceA);
        });
        break;
      case 'likes':
        filteredAds.sort((a, b) => b.likes.compareTo(a.likes));
        break;
    }

    print('Building ads list, filteredAds count: ${filteredAds.length}');
    return Padding(
      padding: const EdgeInsets.all(8),
      child: filteredAds.isEmpty
          ? Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.info_outline, size: 50, color: Colors.grey),
            const SizedBox(height: 8),
            Text(
              'لا توجد إعلانات متاحة',
              style: TextStyle(
                fontSize: 18,
                color: isDarkMode ? Colors.grey[400] : Colors.grey,
              ),
            ),
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: _refreshData,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.teal,
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
              ),
              child: const Text('إعادة المحاولة', style: TextStyle(color: Colors.white)),
            ),
          ],
        ),
      )
          : MasonryGridView.count(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        crossAxisCount: 2,
        mainAxisSpacing: 8,
        crossAxisSpacing: 8,
        itemCount: filteredAds.length,
        itemBuilder: (context, index) => _buildAdItem(filteredAds[index]),
      ),
    );
  }

  Widget _buildAdItem(Ad ad) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return GestureDetector(
      onTap: () => _showAdDetails(context, ad),
      onDoubleTap: () => _likeAd(ad),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        transform: Matrix4.identity()..scale(ad.isLiked ? 1.05 : 1.0),
        child: Card(
          elevation: 5,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          color: isDarkMode ? Colors.grey[850] : Colors.white,
          child: Stack(
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    child: ClipRRect(
                      borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
                      child: CachedNetworkImage(
                        imageUrl: ad.fileUrl != null
                            ? '${_adService.baseUrl}${ad.fileUrl}'
                            : 'https://via.placeholder.com/400x300',
                        fit: BoxFit.cover,
                        width: double.infinity,
                        placeholder: (context, url) => Container(
                          color: isDarkMode ? Colors.grey[800] : Colors.grey[300],
                          child: const Center(
                            child: CircularProgressIndicator(
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.teal),
                            ),
                          ),
                        ),
                        errorWidget: (context, url, error) => Container(
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                              colors: [Colors.teal[200]!, Colors.teal[400]!],
                            ),
                            borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
                          ),
                          child: const Center(
                            child: Icon(
                              Icons.image_not_supported_outlined,
                              color: Colors.white,
                              size: 50,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.all(8),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          ad.title,
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: isDarkMode ? Colors.white : Colors.black,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 4),
                        Text(
                          ad.price != null ? 'ج.م ${ad.price}' : 'غير محدد',
                          style: TextStyle(
                            color: isDarkMode ? Colors.teal[300] : Colors.teal,
                            fontSize: 16,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            const Icon(Icons.location_on, size: 16, color: Colors.grey),
                            Expanded(
                              child: Text(
                                ad.location,
                                style: TextStyle(color: isDarkMode ? Colors.grey[400] : Colors.grey),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            IconButton(
                              icon: AnimatedSwitcher(
                                duration: const Duration(milliseconds: 300),
                                child: Icon(
                                  ad.isLiked ? Icons.favorite : Icons.favorite_border,
                                  key: ValueKey(ad.isLiked),
                                  size: 20,
                                  color: ad.isLiked ? Colors.red : Colors.grey,
                                ),
                              ),
                              onPressed: () => _likeAd(ad),
                            ),
                            Text(
                              '${ad.likes}',
                              style: TextStyle(color: isDarkMode ? Colors.white70 : Colors.black87),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              if (ad.isFeatured)
                Positioned(
                  top: 8,
                  left: 8,
                  child: DottedBorder(
                    color: Colors.amber,
                    strokeWidth: 2,
                    dashPattern: const [4, 4],
                    borderType: BorderType.RRect,
                    radius: const Radius.circular(8),
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      color: Colors.amber.withOpacity(0.2),
                      child: const Text(
                        'مميز',
                        style: TextStyle(color: Colors.amber, fontSize: 12),
                      ),
                    ),
                  ),
                ),
              Positioned(
                top: 8,
                right: 8,
                child: IconButton(
                  icon: const Icon(Icons.share, size: 20, color: Colors.teal),
                  onPressed: () => _shareAd(ad),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _likeAd(Ad ad) async {
    final originalLikes = ad.likes;
    final originalIsLiked = ad.isLiked;
    try {
      if (mounted) {
        setState(() {
          ad.isLiked = !ad.isLiked;
          ad.likes += ad.isLiked ? 1 : -1;
        });
      }
      await _adService.likeAd(ad.id, ad.likes, ad.isLiked);
      print('Ad liked: ${ad.id}, likes: ${ad.likes}, isLiked: ${ad.isLiked}');
    } catch (e) {
      if (mounted) {
        setState(() {
          ad.isLiked = originalIsLiked;
          ad.likes = originalLikes;
        });
        _showSnackBar('خطأ في تحديث الإعجاب: $e', Colors.red);
      }
    }
  }

  void _showCreateAdDialog(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final titleController = TextEditingController();
    final descriptionController = TextEditingController();
    final locationController = TextEditingController();
    double? priceValue;
    String? selectedFileName;
    File? selectedFile;
    String selectedCategory = categories[0]['filter'] as String;
    final appState = Provider.of<AppState>(context, listen: false);
    final creatorId = appState.userId ?? 'user1';

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
          backgroundColor: isDarkMode ? Colors.grey[900] : Colors.white,
          title: Container(
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: isDarkMode
                    ? [Colors.teal[900]!, Colors.teal[700]!]
                    : [Colors.teal, Colors.tealAccent],
              ),
              borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
            ),
            child: const Text(
              'إنشاء إعلان جديد',
              style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
          ),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildDialogField('عنوان الإعلان:', titleController, 'أدخل عنوان الإعلان...'),
                _buildDialogField('الوصف:', descriptionController, 'أدخل وصف الإعلان...', maxLines: 3),
                _buildCategoryDropdown(
                  setDialogState,
                  selectedCategory,
                      (value) => setDialogState(() => selectedCategory = value),
                ),
                _buildDialogField('الموقع:', locationController, 'أدخل الموقع...'),
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'السعر (اختياري):',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: isDarkMode ? Colors.teal[300] : Colors.teal,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Slider(
                        value: priceValue ?? 0,
                        min: 0,
                        max: 10000,
                        divisions: 100,
                        label: priceValue != null ? 'ج.م $priceValue' : 'غير محدد',
                        onChanged: (value) => setDialogState(() => priceValue = value),
                        activeColor: Colors.teal,
                        inactiveColor: isDarkMode ? Colors.grey[700] : Colors.grey[300],
                      ),
                      if (priceValue != null && priceValue! > 0)
                        Text(
                          'ج.م $priceValue',
                          style: TextStyle(color: isDarkMode ? Colors.white70 : Colors.black87),
                        ),
                    ],
                  ),
                ),
                _buildFilePicker(
                  setDialogState,
                  selectedFileName,
                      (name, file) => setDialogState(() {
                    selectedFileName = name;
                    selectedFile = file;
                  }),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(
                'إلغاء',
                style: TextStyle(color: isDarkMode ? Colors.white70 : Colors.teal),
              ),
            ),
            ElevatedButton(
              onPressed: () => _submitAd(
                context,
                titleController,
                descriptionController,
                locationController,
                priceValue,
                selectedFile,
                selectedCategory,
                creatorId,
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.teal,
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
              ),
              child: const Text('إنشاء', style: TextStyle(color: Colors.white)),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _submitAd(
      BuildContext context,
      TextEditingController title,
      TextEditingController desc,
      TextEditingController loc,
      double? price,
      File? file,
      String category,
      String creatorId,
      ) async {
    if (title.text.isEmpty || desc.text.isEmpty || loc.text.isEmpty) {
      _showSnackBar('يرجى ملء جميع الحقول الإلزامية', Colors.orange);
      return;
    }

    if (price != null && price <= 0) {
      price = null;
    }

    try {
      await _adService.createAd(
        title.text,
        desc.text,
        category,
        loc.text,
        price,
        file,
        creatorId,
      );
      final newAd = Ad(
        id: DateTime.now().toString(),
        title: title.text,
        description: desc.text,
        category: category,
        location: loc.text,
        price: price?.toString(),
        fileUrl: file?.path,
        createdAt: DateTime.now(),
      );
      if (mounted) {
        setState(() {
          _ads.insert(0, newAd);
        });
      }
      Navigator.pop(context);
      _showSnackBar(
        'تم إنشاء الإعلان: ${title.text}',
        Colors.green,
        SnackBarAction(
          label: 'عرض',
          textColor: Colors.white,
          onPressed: () => _showAdDetails(context, newAd),
        ),
      );
    } catch (e) {
      _showSnackBar('خطأ في إنشاء الإعلان: $e', Colors.red);
    }
  }

  Widget _buildDialogField(
      String label,
      TextEditingController controller,
      String hint, {
        int maxLines = 1,
      }) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: isDarkMode ? Colors.teal[300] : Colors.teal,
            ),
          ),
          const SizedBox(height: 8),
          TextField(
            controller: controller,
            maxLines: maxLines,
            decoration: InputDecoration(
              hintText: hint,
              filled: true,
              fillColor: isDarkMode ? Colors.grey[800] : Colors.grey[100],
              border: OutlineInputBorder(borderRadius: BorderRadius.circular(10)),
              contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            ),
            style: TextStyle(color: isDarkMode ? Colors.white : Colors.black),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryDropdown(
      void Function(void Function()) setDialogState,
      String selectedCategory,
      void Function(String) onChanged,
      ) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'الفئة:',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: isDarkMode ? Colors.teal[300] : Colors.teal,
            ),
          ),
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12),
            decoration: BoxDecoration(
              color: isDarkMode ? Colors.grey[800] : Colors.grey[100],
              borderRadius: BorderRadius.circular(10),
              border: Border.all(color: Colors.teal),
            ),
            child: DropdownButton<String>(
              value: selectedCategory,
              items: categories
                  .map((category) => DropdownMenuItem<String>(
                value: category['filter'] as String,
                child: Text(
                  category['title'] as String,
                  style: TextStyle(color: isDarkMode ? Colors.white : Colors.black),
                ),
              ))
                  .toList(),
              onChanged: (value) => onChanged(value!),
              underline: const SizedBox(),
              isExpanded: true,
              icon: const Icon(Icons.arrow_drop_down, color: Colors.teal),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilePicker(
      void Function(void Function()) setDialogState,
      String? selectedFileName,
      void Function(String, File) onFilePicked,
      ) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'اختر ملف (صورة/فيديو/PDF):',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: isDarkMode ? Colors.teal[300] : Colors.teal,
            ),
          ),
          const SizedBox(height: 8),
          ElevatedButton.icon(
            onPressed: () async {
              final result = await FilePicker.platform.pickFiles(
                type: FileType.custom,
                allowedExtensions: ['jpg', 'jpeg', 'png', 'mp4', 'mov', 'pdf'],
              );
              if (result != null && result.files.isNotEmpty) {
                final file = File(result.files.single.path!);
                if (await file.length() > 10 * 1024 * 1024) {
                  _showSnackBar(
                    'حجم الملف كبير جدًا (يجب أن يكون أقل من 10 ميجابايت)',
                    Colors.orange,
                  );
                  return;
                }
                setDialogState(() => onFilePicked(result.files.single.name, file));
              }
            },
            icon: const Icon(Icons.upload_file, color: Colors.white),
            label: Text(
              selectedFileName ?? 'اختيار ملف',
              style: const TextStyle(color: Colors.white),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.teal,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
              elevation: 4,
            ),
          ),
        ],
      ),
    );
  }

  void _showAdDetails(BuildContext context, Ad ad) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: isDarkMode ? Colors.grey[900] : Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.8,
        minChildSize: 0.5,
        maxChildSize: 0.9,
        expand: false,
        builder: (context, scrollController) => Container(
          padding: const EdgeInsets.all(16),
          child: SingleChildScrollView(
            controller: scrollController,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  ad.title,
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: isDarkMode ? Colors.teal[300] : Colors.teal,
                  ),
                ),
                const SizedBox(height: 16),
                if (ad.fileUrl != null)
                  ClipRRect(
                    borderRadius: BorderRadius.circular(12),
                    child: CachedNetworkImage(
                      imageUrl: '${_adService.baseUrl}${ad.fileUrl}',
                      height: 200,
                      width: double.infinity,
                      fit: BoxFit.cover,
                      placeholder: (context, url) => Container(
                        color: isDarkMode ? Colors.grey[800] : Colors.grey[300],
                        child: const Center(
                          child: CircularProgressIndicator(
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.teal),
                          ),
                        ),
                      ),
                      errorWidget: (context, url, error) => const Center(
                        child: Text(
                          'خطأ في تحميل الملف',
                          style: TextStyle(color: Colors.red),
                        ),
                      ),
                    ),
                  ),
                const SizedBox(height: 16),
                Text(
                  ad.description,
                  style: TextStyle(
                    fontSize: 16,
                    color: isDarkMode ? Colors.white70 : Colors.black87,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'تم النشر: ${timeago.format(ad.createdAt, locale: 'ar')}',
                  style: TextStyle(color: isDarkMode ? Colors.grey[400] : Colors.grey),
                ),
                const SizedBox(height: 8),
                Text(
                  'الفئة: ${categories.firstWhere((c) => c['filter'] == ad.category)['title'] as String}',
                  style: TextStyle(color: isDarkMode ? Colors.teal[300] : Colors.teal),
                ),
                const SizedBox(height: 8),
                Text(
                  'الموقع: ${ad.location}',
                  style: TextStyle(color: isDarkMode ? Colors.teal[300] : Colors.teal),
                ),
                const SizedBox(height: 8),
                Text(
                  'السعر: ${ad.price != null ? 'ج.م ${ad.price}' : 'غير محدد'}',
                  style: TextStyle(color: isDarkMode ? Colors.teal[300] : Colors.teal),
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    const Icon(Icons.favorite, color: Colors.red),
                    const SizedBox(width: 4),
                    Text(
                      '${ad.likes} إعجابات',
                      style: TextStyle(color: isDarkMode ? Colors.teal[300] : Colors.teal),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    ElevatedButton(
                      onPressed: () => Navigator.pop(context),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: isDarkMode ? Colors.grey[700] : Colors.grey,
                        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
                      ),
                      child: const Text('إغلاق', style: TextStyle(color: Colors.white)),
                    ),
                    ElevatedButton(
                      onPressed: () => _contactAdOwner(context, ad),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.teal,
                        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
                      ),
                      child: const Text('تواصل الآن', style: TextStyle(color: Colors.white)),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _contactAdOwner(BuildContext context, Ad ad) async {
    try {
      final response = await http.get(
        Uri.parse('${_adService.baseUrl}/ads/${ad.id}/contact'),
        headers: {'x-auth-token': _adService.authToken},
      ).timeout(const Duration(seconds: 10));
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        _showSnackBar('تم التواصل مع صاحب الإعلان: ${data['contact']}', Colors.green);
      } else {
        throw Exception('فشل في جلب معلومات الاتصال: ${response.statusCode} ${response.body}');
      }
    } catch (e) {
      _showSnackBar('خطأ في التواصل مع صاحب الإعلان: $e', Colors.red);
    }
    Navigator.pop(context);
  }

  void _showNotifications(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    showModalBottomSheet(
      context: context,
      backgroundColor: isDarkMode ? Colors.grey[900] : Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.5,
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Text(
              'الإشعارات',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: isDarkMode ? Colors.teal[300] : Colors.teal,
              ),
            ),
            const SizedBox(height: 10),
            Expanded(
              child: _notifications.isEmpty
                  ? Center(
                child: Text(
                  'لا توجد إشعارات جديدة حالياً',
                  style: TextStyle(color: isDarkMode ? Colors.grey[400] : Colors.grey),
                ),
              )
                  : ListView.builder(
                itemCount: _notifications.length,
                itemBuilder: (context, index) => Dismissible(
                  key: Key(_notifications[index]['_id']),
                  background: Container(
                    color: Colors.red,
                    alignment: Alignment.centerRight,
                    padding: const EdgeInsets.only(right: 16),
                    child: const Icon(Icons.delete, color: Colors.white),
                  ),
                  onDismissed: (direction) async {
                    final notificationId = _notifications[index]['_id'];
                    try {
                      await _adService.updateNotification(notificationId);
                      if (mounted) {
                        setState(() {
                          _notifications.removeAt(index);
                        });
                      }
                    } catch (e) {
                      _showSnackBar('خطأ في حذف الإشعار: $e', Colors.red);
                    }
                  },
                  child: ListTile(
                    leading: const Icon(Icons.notifications_active, color: Colors.teal),
                    title: Text(
                      _notifications[index]['message'] as String? ?? 'غير معروف',
                      style: TextStyle(color: isDarkMode ? Colors.white : Colors.black87),
                    ),
                    subtitle: Text(
                      timeago.format(
                        DateTime.parse(_notifications[index]['date'] as String),
                        locale: 'ar',
                      ),
                      style: TextStyle(color: isDarkMode ? Colors.grey[400] : Colors.grey),
                    ),
                    trailing: _notifications[index]['read']
                        ? null
                        : const Icon(Icons.circle, color: Colors.teal, size: 10),
                    onTap: () async {
                      if (!_notifications[index]['read']) {
                        try {
                          await _adService.updateNotification(_notifications[index]['_id']);
                          if (mounted) {
                            setState(() => _notifications[index]['read'] = true);
                          }
                        } catch (e) {
                          _showSnackBar('خطأ في تحديث الإشعار: $e', Colors.red);
                        }
                      }
                    },
                  ),
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                if (mounted) {
                  setState(() => _notifications.clear());
                }
                Navigator.pop(context);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
              ),
              child: const Text('مسح الكل', style: TextStyle(color: Colors.white)),
            ),
          ],
        ),
      ),
    );
  }

  void _onAdTap(int index) {
    _showSnackBar('تم النقر على الإعلان ${index + 1}');
  }

  void _shareAd(Ad ad) {
    final shareText =
        'إعلان مميز: ${ad.title} - ${ad.price != null ? 'ج.م ${ad.price}' : 'غير محدد'} في ${ad.location}${ad.fileUrl != null ? '\n${_adService.baseUrl}${ad.fileUrl}' : ''}';
    Share.share(shareText);
    _showSnackBar('تمت المشاركة بنجاح', Colors.green);
  }
}

// Sticky header delegate for categories
class _StickyHeaderDelegate extends SliverPersistentHeaderDelegate {
  final Widget child;

  _StickyHeaderDelegate({required this.child});

  @override
  Widget build(BuildContext context, double shrinkOffset, bool overlapsContent) {
    return Container(
      color: Theme.of(context).brightness == Brightness.dark ? Colors.grey[900] : Colors.white,
      child: child,
    );
  }

  @override
  double get maxExtent => 120;

  @override
  double get minExtent => 120;

  @override
  bool shouldRebuild(covariant SliverPersistentHeaderDelegate oldDelegate) => true;
}
