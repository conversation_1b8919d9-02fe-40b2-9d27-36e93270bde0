import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class SocialPost {
  final String id;
  final String userId;
  final String userName;
  final String? userAvatar;
  final String content;
  final DateTime timestamp;
  final List<String> likes;
  final List<Comment> comments;
  final List<String> mediaUrls;
  final String? location;

  SocialPost({
    required this.id,
    required this.userId,
    required this.userName,
    this.userAvatar,
    required this.content,
    DateTime? timestamp,
    List<String>? likes,
    List<Comment>? comments,
    List<String>? mediaUrls,
    this.location,
  })  : timestamp = timestamp ?? DateTime.now(),
        likes = likes ?? [],
        comments = comments ?? [],
        mediaUrls = mediaUrls ?? [];

  Map<String, dynamic> toJson() => {
        'id': id,
        'userId': userId,
        'userName': userName,
        'userAvatar': userAvatar,
        'content': content,
        'timestamp': timestamp.toIso8601String(),
        'likes': likes,
        'comments': comments.map((e) => e.toJson()).toList(),
        'mediaUrls': mediaUrls,
        'location': location,
      };

  factory SocialPost.fromJson(Map<String, dynamic> json) => SocialPost(
        id: json['id'],
        userId: json['userId'],
        userName: json['userName'],
        userAvatar: json['userAvatar'],
        content: json['content'],
        timestamp: DateTime.parse(json['timestamp']),
        likes: List<String>.from(json['likes'] ?? []),
        comments: (json['comments'] as List<dynamic>?)
                ?.map((e) => Comment.fromJson(e))
                .toList() ??
            [],
        mediaUrls: List<String>.from(json['mediaUrls'] ?? []),
        location: json['location'],
      );

  SocialPost copyWith({
    String? id,
    String? userId,
    String? userName,
    String? userAvatar,
    String? content,
    DateTime? timestamp,
    List<String>? likes,
    List<Comment>? comments,
    List<String>? mediaUrls,
    String? location,
  }) {
    return SocialPost(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      userName: userName ?? this.userName,
      userAvatar: userAvatar ?? this.userAvatar,
      content: content ?? this.content,
      timestamp: timestamp ?? this.timestamp,
      likes: likes ?? this.likes,
      comments: comments ?? this.comments,
      mediaUrls: mediaUrls ?? this.mediaUrls,
      location: location ?? this.location,
    );
  }

  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inDays > 365) {
      return '${(difference.inDays / 365).floor()}y';
    } else if (difference.inDays > 30) {
      return '${(difference.inDays / 30).floor()}mo';
    } else if (difference.inDays > 0) {
      return '${difference.inDays}d';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m';
    } else {
      return 'now';
    }
  }

  bool get hasMedia => mediaUrls.isNotEmpty;
  bool get hasLocation => location != null && location!.isNotEmpty;
  int get likeCount => likes.length;
  int get commentCount => comments.length;
  bool isLikedBy(String userId) => likes.contains(userId);
}

class Comment {
  final String id;
  final String userId;
  final String userName;
  final String? userAvatar;
  final String content;
  final DateTime timestamp;
  final List<String> likes;

  Comment({
    required this.id,
    required this.userId,
    required this.userName,
    this.userAvatar,
    required this.content,
    DateTime? timestamp,
    List<String>? likes,
  })  : timestamp = timestamp ?? DateTime.now(),
        likes = likes ?? [];

  Map<String, dynamic> toJson() => {
        'id': id,
        'userId': userId,
        'userName': userName,
        'userAvatar': userAvatar,
        'content': content,
        'timestamp': timestamp.toIso8601String(),
        'likes': likes,
      };

  factory Comment.fromJson(Map<String, dynamic> json) => Comment(
        id: json['id'],
        userId: json['userId'],
        userName: json['userName'],
        userAvatar: json['userAvatar'],
        content: json['content'],
        timestamp: DateTime.parse(json['timestamp']),
        likes: List<String>.from(json['likes'] ?? []),
      );

  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inDays > 0) {
      return '${difference.inDays}d';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m';
    } else {
      return 'now';
    }
  }

  bool isLikedBy(String userId) => likes.contains(userId);
}
