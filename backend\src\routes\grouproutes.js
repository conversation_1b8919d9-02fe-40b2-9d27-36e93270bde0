const express = require('express');
const router = express.Router();
const {
  getGroups,
  createGroup,
  getGroup,
  updateGroup,
  deleteGroup,
  joinGroup,
  leaveGroup,
  addTask,
  getGroupTasks
} = require('../controllers/groupcontroller');
const { protect } = require('../middleware/authmiddleware');

// مسارات المجموعات
router.route('/')
  .get(protect, getGroups)
  .post(protect, createGroup);

router.route('/:id')
  .get(protect, getGroup)
  .put(protect, updateGroup)
  .delete(protect, deleteGroup);

router.put('/:id/join', protect, joinGroup);
router.put('/:id/leave', protect, leaveGroup);

router.route('/:id/tasks')
  .get(protect, getGroupTasks)
  .post(protect, addTask);

module.exports = router;
