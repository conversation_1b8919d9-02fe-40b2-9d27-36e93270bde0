import 'package:flutter/material.dart';
import 'package:flutter/services.dart'; // For HapticFeedback
import 'dart:math'; // For pi constant

import 'package:provider/provider.dart';
import 'package:audioplayers/audioplayers.dart';
import 'package:confetti/confetti.dart';
import 'package:animate_do/animate_do.dart';
import 'package:lottie/lottie.dart';

import 'appstate.dart';
import 'models/video_model.dart'; // Assuming Video model is used here as per memory

// Placeholder for AvatarScreen if it's in a separate file
// import 'avatar_screen.dart'; 

// Placeholder for other screen imports if they are in separate files
// import 'tips_screen_id24.dart';
// import 'nutrition_screen_id24.dart';
// import 'progress_screen_id24.dart';
// import 'community_screen_id24.dart';

// الصفحة الرئيسية مع شريط التنقل السفلي
class ChildCareApp extends StatefulWidget {
  const ChildCareApp({super.key});

  @override
  _ChildCareAppState createState() => _ChildCareAppState();
}

class _ChildCareAppState extends State<ChildCareApp> {
  void _showAddMainContentDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) {
        final TextEditingController titleController = TextEditingController();
        final TextEditingController descriptionController = TextEditingController();
        final TextEditingController urlController = TextEditingController();
        final TextEditingController categoryController = TextEditingController();
        String selectedType = 'نصيحة';
        return StatefulBuilder(
          builder: (context, setState) => AlertDialog(
            title: const Text('إضافة محتوى جديد'),
            content: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  DropdownButtonFormField<String>(
                    value: selectedType,
                    items: const [
                      DropdownMenuItem(value: 'نصيحة', child: Text('نصيحة')),
                      DropdownMenuItem(value: 'فيديو', child: Text('فيديو')),
                      DropdownMenuItem(value: 'نص', child: Text('نص')),
                    ],
                    onChanged: (value) {
                      if (value != null) setState(() => selectedType = value);
                    },
                    decoration: const InputDecoration(labelText: 'نوع المحتوى'),
                  ),
                  TextField(
                    controller: titleController,
                    decoration: const InputDecoration(labelText: 'العنوان'),
                  ),
                  if (selectedType == 'فيديو')
                    TextField(
                      controller: urlController,
                      decoration: const InputDecoration(labelText: 'رابط الفيديو'),
                    ),
                  TextField(
                    controller: descriptionController,
                    decoration: const InputDecoration(labelText: 'الوصف'),
                  ),
                  TextField(
                    controller: categoryController,
                    decoration: const InputDecoration(labelText: 'التصنيف (اختياري)'),
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () async {
                  final appState = Provider.of<AppState>(context, listen: false);
                  if (selectedType == 'فيديو') {
                    await appState.addVideoContent(
                      title: titleController.text,
                      description: descriptionController.text,
                      videoUrl: urlController.text,
                      category: categoryController.text,
                    );
                  } else {
                    // منطق حفظ نصيحة أو نص عام (يمكنك تعديله لاحقاً)
                  }
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('تمت إضافة المحتوى بنجاح!')),
                  );
                },
                child: const Text('إضافة'),
              ),
            ],
          ),
        );
      },
    );
  }

  int _currentIndex = 0;
  final List<Widget> _screens = [
    const HomeScreenId24(),
    const VideosScreenId24(),
    const ProgressScreenId24(),
    const CommunityScreenId24(),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _screens[_currentIndex],
      floatingActionButton: Consumer<AppState>(
        builder: (context, appState, _) {
          final roleName = appState.userRole.toLowerCase();
          final isHokama = roleName == 'hokama' || roleName == 'حكيم';
          if (!isHokama) return const SizedBox.shrink();
          return FloatingActionButton(
            backgroundColor: Colors.teal,
            tooltip: 'إضافة محتوى جديد',
            child: const Icon(Icons.add, size: 30, color: Colors.white),
            onPressed: () => _showAddMainContentDialog(context),
          );
        },
      ),
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: _currentIndex,
        onTap: (index) {
          setState(() {
            _currentIndex = index;
            HapticFeedback.lightImpact();
          });
        },
        selectedItemColor: Colors.teal,
        unselectedItemColor: Colors.grey,
        backgroundColor: Colors.white,
        elevation: 8,
        items: [
          BottomNavigationBarItem(
              icon: Bounce(child: const Icon(Icons.home)), label: 'الرئيسية'),
          BottomNavigationBarItem(
              icon: Bounce(child: const Icon(Icons.video_library)), label: 'فيديوهات'),
          BottomNavigationBarItem(
              icon: Bounce(child: const Icon(Icons.track_changes)), label: 'التقدم'),
          BottomNavigationBarItem(
              icon: Bounce(child: const Icon(Icons.group)), label: 'المجتمع'),
        ],
      ),
    );
  }
}

// الصفحة الرئيسية (محدثة مع تأثيرات تفاعلية)
class HomeScreenId24 extends StatefulWidget {
  const HomeScreenId24({super.key});

  @override
  _HomeScreenStateId24 createState() => _HomeScreenStateId24();
}

class _HomeScreenStateId24 extends State<HomeScreenId24> {
  final ConfettiController _confettiController =
  ConfettiController(duration: const Duration(seconds: 2));
  final AudioPlayer _audioPlayer = AudioPlayer();

  @override
  void dispose() {
    _confettiController.dispose();
    _audioPlayer.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: ZoomIn(child: const Text('رعاية الأطفال')),
        backgroundColor: Colors.teal,
        actions: [
          IconButton(
            icon: Bounce(child: const Icon(Icons.person)),
            onPressed: () {
              _audioPlayer.play(AssetSource('sounds/sparkle.mp3'));
              _confettiController.play();
              Navigator.push(
                  context, MaterialPageRoute(builder: (_) => const AvatarScreen()));
            },
          ),
        ],
      ),
      body: Stack(
        children: [
      Container(
      decoration: BoxDecoration(
      gradient: LinearGradient(
        colors: [Colors.teal.shade100, Colors.amber.shade100],
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
      ),
    ),
    child: ListView(
    padding: const EdgeInsets.all(16),
    children: [
    FadeInDown(
    child: Lottie.asset('assets/animations/welcome.json',
    height: 150),
    ),
    FadeInDown(
    child: Text(
    'مرحبًا بك في مغامرتك السعيدة!',
    style: TextStyle(
    fontSize: 24,
    fontWeight: FontWeight.bold,
    color: Colors.teal[800]),
    textAlign: TextAlign.center,
    ),
    ),
    const SizedBox(height: 20),
    _buildAgeGroupSelector(context),
    const SizedBox(height: 20),
    _buildMenuButton(
    context, 'الأنشطة التفاعلية', Icons.games, () {
    Navigator.push(context,
    MaterialPageRoute(builder: (_) => const ActivitiesScreenId24()));
    _audioPlayer.play(AssetSource('sounds/click.mp3'));
    _confettiController.play();
    }),
    _buildMenuButton(context, 'نصائح يومية', Icons.lightbulb, () {
  Navigator.push(context, MaterialPageRoute(builder: (_) => const TipsScreenId24()));
  _audioPlayer.play(AssetSource('sounds/click.mp3'));
  _confettiController.play();
}),
  _buildMenuButton(context, 'التغذية الصحية', Icons.food_bank, () {
  Navigator.push(context,
  MaterialPageRoute(builder: (_) => const NutritionScreenId24()));
  _audioPlayer.play(AssetSource('sounds/click.mp3'));
  _confettiController.play();
  }),
  _buildMenuButton(context, 'الجدول اليومي', Icons.schedule, () {
  Navigator.push(context,
  MaterialPageRoute(builder: (_) => const ScheduleScreenId24()));
  _audioPlayer.play(AssetSource('sounds/click.mp3'));
  _confettiController.play();
  }),
  _buildMenuButton(context, 'المكافآت', Icons.star, () {
  Navigator.push(context,
  MaterialPageRoute(builder: (_) => const RewardsScreenId24()));
  _audioPlayer.play(AssetSource('sounds/click.mp3'));
  _confettiController.play();
  }),
  _buildMenuButton(context, 'متجر النجوم', Icons.store, () {
  Navigator.push(context,
  MaterialPageRoute(builder: (_) => const StarShopScreen()));
  _audioPlayer.play(AssetSource('sounds/click.mp3'));
  _confettiController.play();
  }),
  ],
  ),
  ),
  Align(
  alignment: Alignment.topCenter,
  child: ConfettiWidget(
  confettiController: _confettiController,
  blastDirection: -pi / 2,
  emissionFrequency: 0.05,
  numberOfParticles: 30,
  maxBlastForce: 120,
  minBlastForce: 80,
  colors: const [
  Colors.red,
  Colors.blue,
  Colors.yellow,
  Colors.green,
  Colors.purple
  ],
  ),
  ),
  ],
  ),
  );
}

Widget _buildAgeGroupSelector(BuildContext context) {
  return Consumer<AppState>(
    builder: (context, appState, _) => DropdownButtonFormField<String>(
      value: appState.childAgeGroup,
      decoration: InputDecoration(
        labelText: 'اختر الفئة العمرية',
        border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12)),
        filled: true,
        fillColor: Colors.white,
        prefixIcon: const Icon(Icons.child_care, color: Colors.teal),
      ),
      items: ['0-2', '3-5', '6-12']
          .map((age) => DropdownMenuItem(value: age, child: Text(age)))
          .toList(),
      onChanged: (value) {
  if (value != null) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      appState.setChildAgeGroup(value);
    });
    _audioPlayer.play(AssetSource('sounds/sparkle.mp3'));
  }
},
    ),
  );
}

Widget _buildMenuButton(
    BuildContext context, String title, IconData icon, VoidCallback onTap) {
  return ZoomIn(
    child: Card(
      elevation: 6,
      shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () {
  WidgetsBinding.instance.addPostFrameCallback((_) {
    onTap();
  });
},
        borderRadius: BorderRadius.circular(12),
        child: ListTile(
          leading: Bounce(child: Icon(icon, color: Colors.teal, size: 30)),
          title: Text(title,
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.w600)),
          trailing: Bounce(child: const Icon(Icons.arrow_forward_ios)),
        ),
      ),
    ),
  );
}
}

// صفحة الفيديوهات للأطفال (محدثة مع تأثيرات إضافية)
class VideosScreenId24 extends StatefulWidget {
  const VideosScreenId24({super.key});

  @override
  _VideosScreenStateId24 createState() => _VideosScreenStateId24();
}

class _VideosScreenStateId24 extends State<VideosScreenId24> {
  final ConfettiController _confettiController =
      ConfettiController(duration: const Duration(seconds: 3));
  final AudioPlayer _audioPlayer = AudioPlayer();
  bool _isParentalControlEnabled = false;
  String _selectedCategory = 'الكل';
  // Removed local _videos list, will use AppState

  @override
  void initState() {
    super.initState();
    // Fetch videos when the screen initializes
    // Ensure AppState is available and token is passed correctly
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final appState = Provider.of<AppState>(context, listen: false);
      // Assuming appState.token holds the auth token
      // You might need to ensure the token is loaded before calling this
      if (appState.token != null) {
        appState.fetchChildCareVideos(appState.token);
      } else {
        // Handle missing token, e.g., show error or redirect to login
        print("Error: Auth token is null in VideosScreenId24");
        // Optionally, set an error message in AppState or a local state variable
      }
    });
  }

  @override
  void dispose() {
    _confettiController.dispose();
    _audioPlayer.dispose();
    super.dispose();
  }

  void _playVideo(Video video) async { // Changed to Video object
    await _audioPlayer.play(AssetSource('sounds/click.mp3'));
    _confettiController.play();
    Provider.of<AppState>(context, listen: false).addStars(5);
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('تشغيل: ${video.title} | +5 نجوم')), // Use video.title
    );
    // Add actual video playback logic here, e.g., navigate to a video player screen
    // with video.videoUrl (prepending backendBaseUrl if it's relative)
  }

  void _likeVideo(Video video) async { // Changed to Video object
    await _audioPlayer.play(AssetSource('sounds/like.mp3'));
    _confettiController.play();
    Provider.of<AppState>(context, listen: false).addStars(2);
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('أعجبني الفيديو! ${video.title} | +2 نجوم')), // Use video.title
    );
    // Add backend call for liking video here if needed
    // Example: Provider.of<AppState>(context, listen: false).likeBackendVideo(video.id);
  }

  @override
  Widget build(BuildContext context) {
    final appState = Provider.of<AppState>(context);
    final String baseUrl = AppState.getBackendUrl(); // Get base URL

    if (appState.isLoadingChildCareVideos) {
      return Scaffold(
        appBar: AppBar(
          title: ZoomIn(child: const Text('فيديوهات الأطفال')),
          backgroundColor: Colors.teal,
        ),
        body: const Center(child: CircularProgressIndicator()),
      );
    }

    if (appState.childCareVideosError.isNotEmpty) {
      return Scaffold(
        appBar: AppBar(
          title: ZoomIn(child: const Text('فيديوهات الأطفال')),
          backgroundColor: Colors.teal,
        ),
        body: Center(child: Text('خطأ: ${appState.childCareVideosError}')),
      );
    }

    // Use appState.childCareVideos instead of local _videos
    final List<Video> videos = appState.childCareVideos;

    final filteredVideos = _isParentalControlEnabled
        ? videos
            .where((video) => video.ageGroup == appState.childAgeGroup) // Assuming Video model has ageGroup
            .toList()
        : videos;

    // Get unique categories from fetched videos for the dropdown
    final categories = ['الكل', ...videos.map((v) => v.category).toSet()];
    // Ensure _selectedCategory is valid if categories list changes
    if (!categories.contains(_selectedCategory)) {
        _selectedCategory = 'الكل';
    }

    return Scaffold(
      appBar: AppBar(
        title: ZoomIn(child: const Text('فيديوهات الأطفال')),
        backgroundColor: Colors.teal,
        actions: [
          IconButton(
            icon: Bounce(
                child: Icon(_isParentalControlEnabled
                    ? Icons.lock
                    : Icons.lock_open)),
            onPressed: () {
              setState(() {
                _isParentalControlEnabled = !_isParentalControlEnabled;
                _audioPlayer.play(AssetSource('sounds/sparkle.mp3'));
              });
            },
          ),
        ],
      ),
      body: Stack(
        children: [
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.teal.shade100, Colors.amber.shade100],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ),
            ),
            child: Column(
              children: [
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: DropdownButtonFormField<String>(
                    value: _selectedCategory,
                    decoration: InputDecoration(
                      labelText: 'اختر الفئة',
                      border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12)),
                      filled: true,
                      fillColor: Colors.white,
                      prefixIcon: const Icon(Icons.category, color: Colors.teal),
                    ),
                    items: categories
                        .map((category) => DropdownMenuItem(
                      value: category,
                      child: Text(category ?? ''),
                    ))
                        .toList(),
                    onChanged: (value) {
                      if (value != null) {
                        setState(() {
                          _selectedCategory = value;
                          _audioPlayer
                              .play(AssetSource('sounds/sparkle.mp3'));
                        });
                      }
                    },
                  ),
                ),
                Expanded(
                  child: ListView.builder(
                    padding: const EdgeInsets.all(16),
                    itemCount: filteredVideos.length,
                    itemBuilder: (context, index) {
                      final video = filteredVideos[index];
                      if (_selectedCategory != 'الكل' &&
                          video.category != _selectedCategory) { // Use video.category
                        return const SizedBox.shrink();
                      }
                      return ZoomIn(
                        child: Card(
                          elevation: 6,
                          shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12)),
                          child: InkWell(
                            onTap: () => _playVideo(video),
                            borderRadius: BorderRadius.circular(12),
                            child: Row(
                              children: [
                                ClipRRect(
                                  borderRadius: const BorderRadius.horizontal(
                                      left: Radius.circular(12)),
                                  // Use Image.network for thumbnails from backend
                                  // Prepend base URL if thumbnail path is relative
                                  child: video.thumbnail.isNotEmpty && video.thumbnail.startsWith('/')
                                      ? Image.network(
                                          '$baseUrl${video.thumbnail}', // Prepend base URL
                                          width: 120,
                                          height: 80,
                                          fit: BoxFit.cover,
                                          errorBuilder: (context, error, stackTrace) => 
                                            const Icon(Icons.videocam_off, size: 80, color: Colors.grey),
                                          loadingBuilder: (context, child, loadingProgress) {
                                            if (loadingProgress == null) return child;
                                            return SizedBox(
                                              width: 120,
                                              height: 80,
                                              child: Center(
                                                child: CircularProgressIndicator(
                                                  value: loadingProgress.expectedTotalBytes != null
                                                      ? loadingProgress.cumulativeBytesLoaded /
                                                          loadingProgress.expectedTotalBytes!
                                                      : null,
                                                ),
                                              ),
                                            );
                                          },
                                        )
                                      // Fallback for potentially full URLs or invalid paths
                                      : (video.thumbnail.isNotEmpty 
                                          ? Image.network(video.thumbnail, width: 120, height: 80, fit: BoxFit.cover, errorBuilder: (context, error, stackTrace) => const Icon(Icons.videocam_off, size: 80, color: Colors.grey))
                                          : const Icon(Icons.videocam_off, size: 80, color: Colors.grey)),
                                ),
                                Expanded(
                                  child: Padding(
                                    padding: const EdgeInsets.all(12),
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          video.title, // Use video.title
                                          style: const TextStyle(
                                              fontSize: 16,
                                              fontWeight: FontWeight.bold),
                                        ),
                                        const SizedBox(height: 4),
                                        Text(
                                          video.description, // Use video.description
                                          style: const TextStyle(fontSize: 12),
                                          maxLines: 2,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                        const SizedBox(height: 8),
                                        Row(
                                          children: [
                                            const Icon(Icons.category, size: 14, color: Colors.grey),
                                            const SizedBox(width: 4),
                                            Text(video.category ?? '', style: const TextStyle(fontSize: 10)), // Use video.category
                                            const SizedBox(width: 8),
                                            const Icon(Icons.access_time, size: 14, color: Colors.grey),
                                            const SizedBox(width: 4),
                                            Text('${(video.duration ?? 0) ~/ 60}:${((video.duration ?? 0) % 60).toString().padLeft(2, '0')}', style: const TextStyle(fontSize: 10)), // Use video.duration
                                            const SizedBox(width: 8),
                                            // If ageGroup is needed and available in Video model
                                            // Icon(Icons.child_care, size: 14, color: Colors.grey),
                                            // SizedBox(width: 4),
                                            // Text(video.ageGroup ?? 'N/A', style: TextStyle(fontSize: 10)),
                                          ],
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                                IconButton(
                                  icon: const Icon(Icons.favorite_border, color: Colors.pinkAccent),
                                  onPressed: () => _likeVideo(video),
                                ),
                              ],
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
          Align(
            alignment: Alignment.topCenter,
            child: ConfettiWidget(
              confettiController: _confettiController,
              blastDirection: -pi / 2,
              emissionFrequency: 0.05,
              numberOfParticles: 30,
              maxBlastForce: 120,
              minBlastForce: 80,
              colors: const [
                Colors.red,
                Colors.blue,
                Colors.yellow,
                Colors.green,
                Colors.purple
              ],
            ),
          ),
        ],
      ),
    );
  }
}

// صفحة الأنشطة التفاعلية (محدثة مع ألعاب إضافية)
class ActivitiesScreenId24 extends StatefulWidget {
  const ActivitiesScreenId24({super.key});

  @override
  _ActivitiesScreenStateId24 createState() => _ActivitiesScreenStateId24();
}

class _ActivitiesScreenStateId24 extends State<ActivitiesScreenId24>
    with SingleTickerProviderStateMixin {
  int _score = 0;
  int _puzzleIndex = 0;
  bool _isChallengeMode = false;
  int _timeLeft = 30;
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  final AudioPlayer _audioPlayer = AudioPlayer();
  final ConfettiController _confettiController =
  ConfettiController(duration: const Duration(seconds: 3));
  late List<Map<String, dynamic>> _puzzles;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(begin: 1.0, end: 1.2).animate(
      CurvedAnimation(parent: _controller, curve: Curves.elasticOut),
    );
    _initializePuzzles();
    _startTimer();
  }

  void _initializePuzzles() {
    _puzzles = [
      {
        'type': 'text',
        'question': 'ما هو لون السماء؟',
        'answer': 'أزرق',
      },
      {
        'type': 'text',
        'question': 'كم عدد أرجل العنكبوت؟',
        'answer': '8',
      },
      {
        'type': 'image',
        'question': 'اختر الصورة التي تمثل القطة',
        'answer': 'cat',
        'options': [
          {'id': 'cat', 'asset': 'assets/cat.png'},
          {'id': 'dog', 'asset': 'assets/dog.png'},
          {'id': 'bird', 'asset': 'assets/bird.png'},
        ],
      },
      {
        'type': 'matching',
        'question': 'قم بمطابقة الألوان مع الأشياء',
        'answer': {
          'أحمر': 'apple',
          'أصفر': 'banana',
          'أخضر': 'lime'
        },
        'options': [
          {'color': 'أحمر', 'item': 'apple'},
          {'color': 'أصفر', 'item': 'banana'},
          {'color': 'أخضر', 'item': 'lime'},
        ],
      },
    ];
    _puzzles.shuffle();
  }

  void _startTimer() {
    if (_isChallengeMode) {
      Future.doWhile(() async {
        await Future.delayed(const Duration(seconds: 1));
        if (mounted && _isChallengeMode) {
          setState(() {
            _timeLeft--;
            if (_timeLeft <= 0) {
              _isChallengeMode = false;
              _audioPlayer.play(AssetSource('sounds/wrong.mp3'));
              ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('انتهى الوقت!')));
            }
          });
          return _isChallengeMode;
        }
        return false;
      });
    }
  }

  void _checkAnswer(dynamic answer) async {
    final currentPuzzle = _puzzles[_puzzleIndex];
    bool isCorrect = false;
    if (currentPuzzle['type'] == 'matching') {
      isCorrect = answer.toString() == currentPuzzle['answer'].toString();
    } else {
      isCorrect = currentPuzzle['answer'] == answer;
    }
    if (isCorrect) {
      setState(() {
        _score += _isChallengeMode ? 20 : 10;
        _puzzleIndex = (_puzzleIndex + 1) % _puzzles.length;
        _controller.forward().then((_) => _controller.reverse());
      });
      await _audioPlayer.play(AssetSource('sounds/correct.mp3'));
      _confettiController.play();
      Provider.of<AppState>(context, listen: false)
          .addStars(_isChallengeMode ? 10 : 5);
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          content: Text(
              'إجابة صحيحة! +${_isChallengeMode ? 20 : 10} نقاط | +${_isChallengeMode ? 10 : 5} نجوم')));
    } else {
      await _audioPlayer.play(AssetSource('sounds/wrong.mp3'));
      ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('إجابة خاطئة، حاول مجددًا!')));
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    _confettiController.dispose();
    _audioPlayer.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final currentPuzzle = _puzzles[_puzzleIndex];
    return Scaffold(
      appBar: AppBar(
        title: ZoomIn(child: const Text('الأنشطة التفاعلية')),
        backgroundColor: Colors.teal,
        actions: [
          IconButton(
            icon: Bounce(
                child: Icon(
                    _isChallengeMode ? Icons.timer_off : Icons.timer)),
            onPressed: () {
              setState(() {
                _isChallengeMode = !_isChallengeMode;
                _timeLeft = 30;
                if (_isChallengeMode) _startTimer();
                _audioPlayer.play(AssetSource('sounds/sparkle.mp3'));
              });
            },
          ),
        ],
      ),
      body: Stack(
        children: [
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.teal.shade100, Colors.amber.shade100],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  ZoomIn(
                    child: const Text(
                      'لعبة الألغاز الممتعة',
                      style:
                      TextStyle(fontSize: 22, fontWeight: FontWeight.bold),
                    ),
                  ),
                  const SizedBox(height: 10),
                  if (_isChallengeMode)
                    Bounce(
                      child: Text(
                        'الوقت المتبقي: $_timeLeft ثانية',
                        style: const TextStyle(fontSize: 16, color: Colors.red),
                      ),
                    ),
                  const SizedBox(height: 10),
                  LinearProgressIndicator(
                    value: _score / 100,
                    color: Colors.teal,
                    backgroundColor: Colors.grey.shade300,
                    minHeight: 10,
                  ),
                  const SizedBox(height: 20),
                  ScaleTransition(
                    scale: _scaleAnimation,
                    child: Card(
                      elevation: 6,
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12)),
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Text(
                          currentPuzzle['question'],
                          style: const TextStyle(fontSize: 18),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 20),
                  if (currentPuzzle['type'] == 'text')
                    TextField(
                      textDirection: TextDirection.rtl,
                      decoration: InputDecoration(
                        labelText: 'أدخل الإجابة',
                        border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12)),
                        filled: true,
                        fillColor: Colors.white,
                        prefixIcon: const Icon(Icons.edit, color: Colors.teal),
                      ),
                      onSubmitted: _checkAnswer,
                    )
                  else if (currentPuzzle['type'] == 'image')
                    Wrap(
                      spacing: 10,
                      runSpacing: 10,
                      children: (currentPuzzle['options'] as List)
                          .map((option) => GestureDetector(
                        onTap: () => _checkAnswer(option['id']),
                        child: ZoomIn(
                          child: Container(
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.teal),
                              borderRadius: BorderRadius.circular(12),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.grey.withOpacity(0.3),
                                  blurRadius: 5,
                                  offset: const Offset(0, 3),
                                ),
                              ],
                            ),
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(12),
                              child: Image.asset(
                                option['asset'],
                                width: 100,
                                height: 100,
                                fit: BoxFit.cover,
                                errorBuilder:
                                    (context, error, stackTrace) =>
                                    const Icon(Icons.error),
                              ),
                            ),
                          ),
                        ),
                      ))
                          .toList(),
                    )
                  else if (currentPuzzle['type'] == 'matching')
                      Column(
                        children: (currentPuzzle['options'] as List)
                            .map((pair) => ZoomIn(
                          child: Card(
                            margin: const EdgeInsets.symmetric(vertical: 8),
                            shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12)),
                            child: ListTile(
                              title: Text(pair['color']),
                              trailing: Text(pair['item']),
                              onTap: () => _checkAnswer({
                                pair['color']: pair['item']
                              }),
                            ),
                          ),
                        ))
                            .toList(),
                      ),
                  const SizedBox(height: 20),
                  ZoomIn(
                    child: Text(
                      'النقاط: $_score',
                      style:
                      const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                    ),
                  ),
                ],
              ),
            ),
          ),
          Align(
            alignment: Alignment.topCenter,
            child: ConfettiWidget(
              confettiController: _confettiController,
              blastDirection: -pi / 2,
              emissionFrequency: 0.05,
              numberOfParticles: 30,
              maxBlastForce: 120,
              minBlastForce: 80,
              colors: const [
                Colors.red,
                Colors.blue,
                Colors.yellow,
                Colors.green,
                Colors.purple
              ],
            ),
          ),
        ],
      ),
    );
  }
}

// صفحة الجدول اليومي
class ScheduleScreenId24 extends StatefulWidget {
  const ScheduleScreenId24({super.key});

  @override
  _ScheduleScreenStateId24 createState() => _ScheduleScreenStateId24();
}

class _ScheduleScreenStateId24 extends State<ScheduleScreenId24> {
  final TextEditingController _taskController = TextEditingController();
  final TextEditingController _timeController = TextEditingController();
  final AudioPlayer _audioPlayer = AudioPlayer();
  final ConfettiController _confettiController =
  ConfettiController(duration: const Duration(seconds: 3));

  @override
  void dispose() {
    _taskController.dispose();
    _timeController.dispose();
    _audioPlayer.dispose();
    _confettiController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: ZoomIn(child: const Text('الجدول اليومي')),
        backgroundColor: Colors.teal,
      ),
      body: Consumer<AppState>(
        builder: (context, appState, _) => Stack(
          children: [
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [Colors.teal.shade100, Colors.amber.shade100],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                ),
              ),
              child: Column(
                children: [
                  Padding(
                    padding: const EdgeInsets.all(16),
                    child: Row(
                      children: [
                        Expanded(
                          child: TextField(
                            controller: _taskController,
                            decoration: InputDecoration(
                              labelText: 'أدخل المهمة',
                              border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12)),
                              filled: true,
                              fillColor: Colors.white,
                              prefixIcon:
                              const Icon(Icons.task, color: Colors.teal),
                            ),
                          ),
                        ),
                        const SizedBox(width: 10),
                        Expanded(
                          child: TextField(
                            controller: _timeController,
                            decoration: InputDecoration(
                              labelText: 'الوقت (مثال: 10:00)',
                              border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12)),
                              filled: true,
                              fillColor: Colors.white,
                              prefixIcon:
                              const Icon(Icons.access_time, color: Colors.teal),
                            ),
                          ),
                        ),
                        const SizedBox(width: 10),
                        ZoomIn(
                          child: ElevatedButton(
                            onPressed: () {
  if (_taskController.text.isNotEmpty && _timeController.text.isNotEmpty) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      appState.addSchedule(_taskController.text, _timeController.text);
      appState.addStars(3);
    });
    _taskController.clear();
    _timeController.clear();
    _audioPlayer.play(AssetSource('sounds/correct.mp3'));
    _confettiController.play();
    ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('تمت إضافة المهمة! | +3 نجوم')));
  }
},
                            style: ElevatedButton.styleFrom(
                              shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12)),
                              backgroundColor: Colors.teal,
                            ),
                            child: const Text('إضافة'),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Expanded(
                    child: ListView.builder(
                      padding: const EdgeInsets.all(16),
                      itemCount: appState.schedule.length,
                      itemBuilder: (context, index) => ZoomIn(
                        child: Card(
                          margin: const EdgeInsets.symmetric(vertical: 8),
                          shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12)),
                          child: ListTile(
                            title: Text(appState.schedule[index]['task']!),
                            subtitle: Text(
                                'الوقت: ${appState.schedule[index]['time']}'),
                            leading: Bounce(
                                child: const Icon(Icons.schedule,
                                    color: Colors.teal)),
                            trailing: Bounce(
                              child: IconButton(
                                icon: const Icon(Icons.delete, color: Colors.red),
                                onPressed: () {
                                  appState.removeSchedule(index);
                                  _audioPlayer
                                      .play(AssetSource('sounds/click.mp3'));
                                  ScaffoldMessenger.of(context).showSnackBar(
                                      const SnackBar(
                                          content:
                                          Text('تم حذف المهمة!')));
                                },
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            Align(
              alignment: Alignment.topCenter,
              child: ConfettiWidget(
                confettiController: _confettiController,
                blastDirection: -pi / 2,
                emissionFrequency: 0.05,
                numberOfParticles: 30,
                maxBlastForce: 120,
                minBlastForce: 80,
                colors: const [
                  Colors.red,
                  Colors.blue,
                  Colors.yellow,
                  Colors.green,
                  Colors.purple
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// صفحة المكافآت (محدثة مع تأثيرات بصرية)
class RewardsScreenId24 extends StatefulWidget {
  const RewardsScreenId24({super.key});

  @override
  _RewardsScreenStateId24 createState() => _RewardsScreenStateId24();
}

class _RewardsScreenStateId24 extends State<RewardsScreenId24> {
  final AudioPlayer _audioPlayer = AudioPlayer();
  final ConfettiController _confettiController =
  ConfettiController(duration: const Duration(seconds: 3));

  @override
  void dispose() {
    _audioPlayer.dispose();
    _confettiController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: ZoomIn(child: const Text('المكافآت')),
        backgroundColor: Colors.teal,
      ),
      body: Consumer<AppState>(
        builder: (context, appState, _) => Stack(
          children: [
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [Colors.teal.shade100, Colors.amber.shade100],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                ),
              ),
              child: Column(
                children: [
                  const SizedBox(height: 20),
                  ZoomIn(
                    child: Lottie.asset('assets/animations/trophy.json',
                        height: 150),
                  ),
                  ZoomIn(
                    child: Text(
                      'مكافآتك الرائعة!',
                      style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Colors.teal[800]),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  const SizedBox(height: 20),
                  Expanded(
                    child: GridView.count(
                      crossAxisCount: 2,
                      padding: const EdgeInsets.all(16),
                      mainAxisSpacing: 10,
                      crossAxisSpacing: 10,
                      children: List.generate(
                        appState.rewards.length,
                            (index) => ZoomIn(
                          child: Card(
                            elevation: 6,
                            shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12)),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Bounce(
                                  child: const Icon(Icons.star,
                                      size: 50, color: Colors.amber),
                                ),
                                const SizedBox(height: 10),
                                Text(
                                  'مكافأة ${index + 1}',
                                  style: const TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w600),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                  ZoomIn(
                    child: ElevatedButton(
                      onPressed: () {
  WidgetsBinding.instance.addPostFrameCallback((_) {
    appState.addReward();
    appState.addStars(10);
  });
  _audioPlayer.play(AssetSource('sounds/correct.mp3'));
  _confettiController.play();
  ScaffoldMessenger.of(context).showSnackBar(
    const SnackBar(content: Text('تمت إضافة مكافأة جديدة! | +10 نجوم')),
  );
},
                      style: ElevatedButton.styleFrom(
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12)),
                        backgroundColor: Colors.teal,
                        padding:
                        const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                      ),
                      child: const Text('إضافة مكافأة'),
                    ),
                  ),
                  const SizedBox(height: 20),
                ],
              ),
            ),
            Align(
              alignment: Alignment.topCenter,
              child: ConfettiWidget(
                confettiController: _confettiController,
                blastDirection: -pi / 2,
                emissionFrequency: 0.05,
                numberOfParticles: 30,
                maxBlastForce: 120,
                minBlastForce: 80,
                colors: const [
                  Colors.red,
                  Colors.blue,
                  Colors.yellow,
                  Colors.green,
                  Colors.purple
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// صفحة اختيار الأفاتار
class AvatarScreen extends StatefulWidget {
  const AvatarScreen({super.key});

  @override
  _AvatarScreenState createState() => _AvatarScreenState();
}

class _AvatarScreenState extends State<AvatarScreen> {
  final AudioPlayer _audioPlayer = AudioPlayer();
  final ConfettiController _confettiController =
  ConfettiController(duration: const Duration(seconds: 3));

  final List<Map<String, String>> avatars = [
    {'id': 'bear', 'asset': 'assets/avatars/bear.png', 'name': 'الدب'},
    {'id': 'cat', 'asset': 'assets/avatars/cat.png', 'name': 'القط'},
    {'id': 'rocket', 'asset': 'assets/avatars/rocket.png', 'name': 'الصاروخ'},
    {'id': 'star', 'asset': 'assets/avatars/star.png', 'name': 'النجمة'},
  ];

  @override
  void dispose() {
    _audioPlayer.dispose();
    _confettiController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: ZoomIn(child: const Text('اختر أفاتارك')),
        backgroundColor: Colors.teal,
      ),
      body: Consumer<AppState>(
        builder: (context, appState, _) => Stack(
          children: [
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [Colors.teal.shade100, Colors.amber.shade100],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                ),
              ),
              child: Column(
                children: [
                  const SizedBox(height: 20),
                  ZoomIn(
                    child: Text(
                      'اختر شخصيتك المفضلة!',
                      style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Colors.teal[800]),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  const SizedBox(height: 20),
                  Expanded(
                    child: GridView.count(
                      crossAxisCount: 2,
                      padding: const EdgeInsets.all(16),
                      mainAxisSpacing: 10,
                      crossAxisSpacing: 10,
                      children: avatars
                          .map((avatar) => ZoomIn(
                        child: Card(
                          elevation: 6,
                          shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12)),
                          child: InkWell(
                            onTap: () {
  WidgetsBinding.instance.addPostFrameCallback((_) {
    appState.setAvatar(avatar['id']!);
    appState.addStars(5);
  });
  _audioPlayer.play(AssetSource('sounds/sparkle.mp3'));
  _confettiController.play();
  ScaffoldMessenger.of(context).showSnackBar(
    SnackBar(content: Text('تم اختيار ${avatar['name']}! | +5 نجوم')),
  );
},
                            borderRadius: BorderRadius.circular(12),
                            child: Column(
                              mainAxisAlignment:
                              MainAxisAlignment.center,
                              children: [
                                Image.asset(
                                  avatar['asset']!,
                                  height: 100,
                                  width: 100,
                                  fit: BoxFit.contain,
                                  errorBuilder:
                                      (context, error, stackTrace) =>
                                      const Icon(Icons.error),
                                ),
                                const SizedBox(height: 10),
                                Text(
                                  avatar['name']!,
                                  style: const TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w600),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ))
                          .toList(),
                    ),
                  ),
                ],
              ),
            ),
            Align(
              alignment: Alignment.topCenter,
              child: ConfettiWidget(
                confettiController: _confettiController,
                blastDirection: -pi / 2,
                emissionFrequency: 0.05,
                numberOfParticles: 30,
                maxBlastForce: 120,
                minBlastForce: 80,
                colors: const [
                  Colors.red,
                  Colors.blue,
                  Colors.yellow,
                  Colors.green,
                  Colors.purple
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// صفحة متجر النجوم
class StarShopScreen extends StatefulWidget {
  const StarShopScreen({super.key});

  @override
  _StarShopScreenState createState() => _StarShopScreenState();
}

class _StarShopScreenState extends State<StarShopScreen> {
  final AudioPlayer _audioPlayer = AudioPlayer();
  final ConfettiController _confettiController =
  ConfettiController(duration: const Duration(seconds: 3));

  final List<Map<String, dynamic>> shopItems = [
    {
      'id': 'sticker1',
      'name': 'ملصق النجمة',
      'asset': 'assets/stickers/star.png',
      'cost': 20
    },
    {
      'id': 'sticker2',
      'name': 'ملصق القلب',
      'asset': 'assets/stickers/heart.png',
      'cost': 20
    },
    {
      'id': 'theme1',
      'name': 'ثيم البحر',
      'asset': 'assets/themes/sea.png',
      'cost': 50
    },
    {
      'id': 'theme2',
      'name': 'ثيم الفضاء',
      'asset': 'assets/themes/space.png',
      'cost': 50
    },
  ];

  @override
  void dispose() {
    _audioPlayer.dispose();
    _confettiController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: ZoomIn(child: const Text('متجر النجوم')),
        backgroundColor: Colors.teal,
      ),
      body: Consumer<AppState>(
        builder: (context, appState, _) => Stack(
          children: [
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [Colors.teal.shade100, Colors.amber.shade100],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                ),
              ),
              child: Column(
                children: [
                  const SizedBox(height: 20),
                  ZoomIn(
                    child: Text(
                      'النجوم: ${appState.stars}',
                      style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Colors.teal[800]),
                    ),
                  ),
                  const SizedBox(height: 20),
                  Expanded(
                    child: GridView.count(
                      crossAxisCount: 2,
                      padding: const EdgeInsets.all(16),
                      mainAxisSpacing: 10,
                      crossAxisSpacing: 10,
                      children: shopItems
                          .map((item) => ZoomIn(
                        child: Card(
                          elevation: 6,
                          shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12)),
                          child: InkWell(
                            onTap: () {
  if (appState.stars >= item['cost']) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      appState.spendStars(item['cost']);
      appState.addPurchasedItem(item['id']);
    });
    _audioPlayer.play(AssetSource('sounds/correct.mp3'));
    _confettiController.play();
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('تم شراء ${item['name']}!')),
    );
  } else {
    _audioPlayer.play(AssetSource('sounds/wrong.mp3'));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('نجوم غير كافية!')),
    );
  }
},
                            borderRadius: BorderRadius.circular(12),
                            child: Column(
                              mainAxisAlignment:
                              MainAxisAlignment.center,
                              children: [
                                Image.asset(
                                  item['asset'],
                                  height: 80,
                                  width: 80,
                                  fit: BoxFit.contain,
                                  errorBuilder:
                                      (context, error, stackTrace) =>
                                      const Icon(Icons.error),
                                ),
                                const SizedBox(height: 10),
                                Text(
                                  item['name'],
                                  style: const TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w600),
                                ),
                                const SizedBox(height: 5),
                                Text(
                                  '${item['cost']} نجمة',
                                  style: const TextStyle(
                                      fontSize: 14,
                                      color: Colors.teal),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ))
                          .toList(),
                    ),
                  ),
                ],
              ),
            ),
            Align(
              alignment: Alignment.topCenter,
              child: ConfettiWidget(
                confettiController: _confettiController,
                blastDirection: -pi / 2,
                emissionFrequency: 0.05,
                numberOfParticles: 30,
                maxBlastForce: 120,
                minBlastForce: 80,
                colors: const [
                  Colors.red,
                  Colors.blue,
                  Colors.yellow,
                  Colors.green,
                  Colors.purple
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// صفحة النصائح اليومية
class TipsScreenId24 extends StatelessWidget {
  final List<String> tips;

  const TipsScreenId24({super.key, this.tips = const [
    'اقرأ قصة لطفلك قبل النوم لتعزيز خياله.',
    'خصص 10 دقائق يوميًا للعب مع طفلك.',
    'شجع طفلك على التعبير عن مشاعره.',
    'جرب نشاطًا إبداعيًا مثل الرسم أو الصلصال.',
  ]});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: ZoomIn(child: const Text('نصائح يومية')),
        backgroundColor: Colors.teal,
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [Colors.teal.shade100, Colors.amber.shade100],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
        ),
        child: ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: tips.length,
          itemBuilder: (context, index) => ZoomIn(
            child: Card(
              margin: const EdgeInsets.symmetric(vertical: 8),
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12)),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    Bounce(
                        child: const Icon(Icons.lightbulb,
                            color: Colors.teal, size: 30)),
                    const SizedBox(width: 10),
                    Expanded(
                      child: Text(
                        tips[index],
                        style: const TextStyle(fontSize: 16),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}

// صفحة التغذية الصحية
class NutritionScreenId24 extends StatelessWidget {
  static const List<Map<String, String>> _defaultRecipes = [
    {
      'title': 'سلطة الفواكه',
      'description': 'مزيج من التفاح، الموز، والفراولة مع عصير الليمون.'
    },
    {
      'title': 'ساندويتش الخضار',
      'description': 'خبز الحبوب الكاملة مع الخس، الطماطم، والجبن.'
    },
    {
      'title': 'عصير الأفوكادو',
      'description': 'أفوكادو، حليب، وعسل لمشروب مغذي.'
    },
  ];

  final List<Map<String, String>> recipes;

  const NutritionScreenId24({super.key, this.recipes = _defaultRecipes});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: ZoomIn(child: const Text('التغذية الصحية')),
        backgroundColor: Colors.teal,
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [Colors.teal.shade100, Colors.amber.shade100],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
        ),
        child: ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: recipes.length,
          itemBuilder: (context, index) => ZoomIn(
            child: Card(
              margin: const EdgeInsets.symmetric(vertical: 8),
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12)),
              child: ListTile(
                title: Text(recipes[index]['title']!),
                subtitle: Text(recipes[index]['description']!),
                leading: Bounce(
                    child: const Icon(Icons.food_bank, color: Colors.teal)),
              ),
            ),
          ),
        ),
      ),
    );
  }
}

// صفحة تتبع التقدم
class ProgressScreenId24 extends StatefulWidget {
  const ProgressScreenId24({super.key});

  @override
  _ProgressScreenStateId24 createState() => _ProgressScreenStateId24();
}

class _ProgressScreenStateId24 extends State<ProgressScreenId24> {
  final TextEditingController _milestoneController = TextEditingController();
  final AudioPlayer _audioPlayer = AudioPlayer();
  final ConfettiController _confettiController =
  ConfettiController(duration: const Duration(seconds: 3));

  @override
  void initState() {
    super.initState();
    Provider.of<AppState>(context, listen: false).loadProgress();
  }

  @override
  void dispose() {
    _milestoneController.dispose();
    _audioPlayer.dispose();
    _confettiController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: ZoomIn(child: const Text('تتبع التقدم')),
        backgroundColor: Colors.teal,
      ),
      body: Consumer<AppState>(
        builder: (context, appState, _) => Stack(
          children: [
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [Colors.teal.shade100, Colors.amber.shade100],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                ),
              ),
              child: Column(
                children: [
                  Padding(
                    padding: const EdgeInsets.all(16),
                    child: Row(
                      children: [
                        Expanded(
                          child: TextField(
                            controller: _milestoneController,
                            decoration: InputDecoration(
                              labelText: 'إضافة إنجاز جديد',
                              border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12)),
                              filled: true,
                              fillColor: Colors.white,
                              prefixIcon: const Icon(Icons.star,
                                  color: Colors.teal),
                            ),
                          ),
                        ),
                        const SizedBox(width: 10),
                        ZoomIn(
                          child: ElevatedButton(
                            onPressed: () {
  if (_milestoneController.text.isNotEmpty) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      appState.addProgress(
        _milestoneController.text,
        DateTime.now().toString().substring(0, 10),
      );
      appState.addStars(5);
    });
    _milestoneController.clear();
    _audioPlayer.play(AssetSource('sounds/correct.mp3'));
    _confettiController.play();
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تمت إضافة إنجاز! | +5 نجوم')),
    );
  }
},
                            style: ElevatedButton.styleFrom(
                              shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12)),
                              backgroundColor: Colors.teal,
                            ),
                            child: const Text('إضافة'),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Expanded(
                    child: ListView.builder(
                      padding: const EdgeInsets.all(16),
                      itemCount: appState.progress.length,
                      itemBuilder: (context, index) => ZoomIn(
                        child: Card(
                          margin: const EdgeInsets.symmetric(vertical: 8),
                          shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12)),
                          child: ListTile(
                            title:
                            Text(appState.progress[index]['milestone']!),
                            subtitle: Text(
                                'التاريخ: ${appState.progress[index]['date']}'),
                            leading: Bounce(
                                child: const Icon(Icons.check_circle,
                                    color: Colors.teal)),
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            Align(
              alignment: Alignment.topCenter,
              child: ConfettiWidget(
                confettiController: _confettiController,
                blastDirection: -pi / 2,
                emissionFrequency: 0.05,
                numberOfParticles: 30,
                maxBlastForce: 120,
                minBlastForce: 80,
                colors: const [
                  Colors.red,
                  Colors.blue,
                  Colors.yellow,
                  Colors.green,
                  Colors.purple
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// صفحة المجتمع
class CommunityScreenId24 extends StatefulWidget {
  const CommunityScreenId24({super.key});

  @override
  _CommunityScreenStateId24 createState() => _CommunityScreenStateId24();
}

class _CommunityScreenStateId24 extends State<CommunityScreenId24> {
  final TextEditingController _messageController = TextEditingController();
  final AudioPlayer _audioPlayer = AudioPlayer();
  final ConfettiController _confettiController =
  ConfettiController(duration: const Duration(seconds: 3));

  @override
  void dispose() {
    _messageController.dispose();
    _audioPlayer.dispose();
    _confettiController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: ZoomIn(child: const Text('المجتمع')),
        backgroundColor: Colors.teal,
      ),
      body: Consumer<AppState>(
        builder: (context, appState, _) => Stack(
          children: [
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [Colors.teal.shade100, Colors.amber.shade100],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                ),
              ),
              child: Column(
                children: [
                  Expanded(
                    child: ListView.builder(
                      padding: const EdgeInsets.all(16),
                      itemCount: appState.communityMessages.length,
                      itemBuilder: (context, index) => ZoomIn(
                        child: Card(
                          margin: const EdgeInsets.symmetric(vertical: 8),
                          shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12)),
                          child: ListTile(
                            title: Text(appState
                                .communityMessages[index]['message']!),
                            subtitle: Text(
                                'بواسطة: ${appState.communityMessages[index]['user']}'),
                            leading: Bounce(
                                child: const Icon(Icons.person,
                                    color: Colors.teal)),
                          ),
                        ),
                      ),
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.all(16),
                    child: Row(
                      children: [
                        Expanded(
                          child: TextField(
                            controller: _messageController,
                            decoration: InputDecoration(
                              labelText: 'أدخل رسالتك',
                              border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12)),
                              filled: true,
                              fillColor: Colors.white,
                              prefixIcon: const Icon(Icons.message,
                                  color: Colors.teal),
                            ),
                          ),
                        ),
                        const SizedBox(width: 10),
                        ZoomIn(
                          child: ElevatedButton(
                            onPressed: () {
  if (_messageController.text.isNotEmpty) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      appState.addCommunityMessage(_messageController.text, 'مستخدم');
      appState.addStars(3);
    });
    _messageController.clear();
    _audioPlayer.play(AssetSource('sounds/correct.mp3'));
    _confettiController.play();
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم إرسال الرسالة! | +3 نجوم')),
    );
  }
},
                            style: ElevatedButton.styleFrom(
                              shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12)),
                              backgroundColor: Colors.teal,
                            ),
                            child: const Text('إرسال'),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            Align(
              alignment: Alignment.topCenter,
              child: ConfettiWidget(
                confettiController: _confettiController,
                blastDirection: -pi / 2,
                emissionFrequency: 0.05,
                numberOfParticles: 30,
                maxBlastForce: 120,
                minBlastForce: 80,
                colors: const [
                  Colors.red,
                  Colors.blue,
                  Colors.yellow,
                  Colors.green,
                  Colors.purple
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}