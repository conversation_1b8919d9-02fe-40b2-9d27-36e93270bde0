
/// أنواع الإنجازات التي تمنح نقاطًا
enum RewardAction {
  donation,
  share,
  borrow,
  completeCourse,
  publishContent,
  help,
  custom
}

class RewardLevel {
  final String name;
  final int minPoints;
  final String badgeAsset;
  RewardLevel(this.name, this.minPoints, this.badgeAsset);
}

class RewardsManager {
  static final List<RewardLevel> levels = [
    RewardLevel('عادي', 0, 'assets/badges/normal.png'),
    RewardLevel('مجتهد', 1000, 'assets/badges/active.png'),
    RewardLevel('متفوق', 3000, 'assets/badges/expert.png'),
    RewardLevel('قدوة', 6000, 'assets/badges/leader.png'),
  ];

  static final Map<RewardAction, int> pointsTable = {
    RewardAction.donation: 50,
    RewardAction.share: 20,
    RewardAction.borrow: 15,
    RewardAction.completeCourse: 100,
    RewardAction.publishContent: 30,
    RewardAction.help: 40,
    RewardAction.custom: 10,
  };

  /// حساب النقاط حسب نوع الإنجاز
  static int getPointsForAction(RewardAction action) => pointsTable[action] ?? 0;

  /// حساب المستوى الحالي بناءً على النقاط
  static RewardLevel getLevel(int points) {
    return levels.lastWhere((lvl) => points >= lvl.minPoints, orElse: () => levels.first);
  }
}
