import React, { useState } from 'react';
import axios from 'axios';

function Register() {
  const [formData, setFormData] = useState({
    username: '',
    password: '',
    phoneNumber: '',
    job: '',
    userType: '',
    profileImagePath: '',
    location: '',
  });

  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      const response = await axios.post('http://192.168.1.3:3000/api/auth/register', formData);
      console.log('تسجيل ناجح:', response.data);
      // احفظ الـ token في localStorage أو state إذا لزم الأمر
      localStorage.setItem('token', response.data.token);
    } catch (error) {
      console.error('خطأ في التسجيل:', error.response.data);
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      <input type="text" name="username" placeholder="اسم المستخدم" onChange={handleChange} />
      <input type="password" name="password" placeholder="كلمة المرور" onChange={handleChange} />
      <input type="text" name="phoneNumber" placeholder="رقم الهاتف" onChange={handleChange} />
      <input type="text" name="job" placeholder="الوظيفة" onChange={handleChange} />
      <input type="text" name="userType" placeholder="نوع المستخدم" onChange={handleChange} />
      <input type="text" name="profileImagePath" placeholder="رابط الصورة" onChange={handleChange} />
      <input type="text" name="location" placeholder="الموقع" onChange={handleChange} />
      <button type="submit">تسجيل</button>
    </form>
  );
}

export default Register;