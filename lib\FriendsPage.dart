import 'dart:async';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:animate_do/animate_do.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:socket_io_client/socket_io_client.dart' as io;
import 'appstate.dart';
import 'models/messenger_models.dart';
import 'models/user_model.dart';
// import 'models/group_model.dart'; // Removed unused import
// import 'models/message_model.dart'; // Removed unused import
import 'chat_page.dart';

// Debouncer to limit frequent setState calls during search
class Debouncer {
  final int milliseconds;
  Timer? _timer;

  Debouncer({required this.milliseconds});

  void run(VoidCallback action) {
    _timer?.cancel();
    _timer = Timer(Duration(milliseconds: milliseconds), action);
  }

  void dispose() {
    _timer?.cancel();
  }
}

// Service layer for friend-related API calls
class FriendService {
  static Future<List<User>> fetchFriends(String token, String backendUrl) async {
    if (token.isEmpty) {
      throw Exception('No valid token found');
    }
    final response = await http.get(
      Uri.parse('$backendUrl/api/friends'),
      headers: {'Authorization': 'Bearer $token'},
    );
    if (response.statusCode == 200) {
      final List<dynamic> friendData = json.decode(response.body);
      return friendData.map((data) => User.fromJson({
        'id': data['_id'],
        'name': data['username'],
        'job': data['job'] ?? 'No job specified',
        'latitude': data['latitude'] ?? 0.0,
        'longitude': data['longitude'] ?? 0.0,
        'avatarUrl': data['avatarUrl'] != null
            ? '$backendUrl${data['avatarUrl']}'
            : 'https://via.placeholder.com/150',
        'isOnline': data['isOnline'] ?? false,
        'isFavorite': data['isFavorite'] ?? false,
        'shareLocation': data['shareLocation'] ?? false,
      })).toList();
    } else {
      throw Exception('Failed to fetch friends: ${response.body}');
    }
  }
}

class FriendsPage extends StatefulWidget {
  final User friend;
  const FriendsPage({super.key, required this.friend});

  @override
  State<FriendsPage> createState() => _FriendsPageState();
}

class _FriendsPageState extends State<FriendsPage> {
  List<User> friends = [];
  bool isLoading = true;
  bool hasError = false;
  String errorMessage = '';
  final TextEditingController _searchController = TextEditingController();
  List<User> filteredFriends = [];
  late io.Socket socket;
  final Debouncer _debouncer = Debouncer(milliseconds: 300);

  @override
  void initState() {
    super.initState();
    // Initialize after widget is built to safely access Provider
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final appState = Provider.of<AppState>(context, listen: false);
      socket = appState.socket ?? _initSocket();
      _fetchFriendsWithCache();
      _initializeSocketListeners();
    });
    _searchController.addListener(_filterFriends);
  }

  // Initialize WebSocket with proper error handling
  io.Socket _initSocket() {
    final appState = Provider.of<AppState>(context, listen: false);
    if (appState.token == null || appState.token!.isEmpty) {
      throw Exception('No valid token found');
    }
    io.Socket newSocket = io.io(
      AppState.getBackendUrl(),
      io.OptionBuilder()
          .setTransports(['websocket'])
          .setExtraHeaders({'Authorization': 'Bearer ${appState.token!}'})
          .build(),
    );
    newSocket.onConnect((_) { /* print('Connected to WebSocket'); */ });
    newSocket.onDisconnect((_) { /* print('Disconnected from WebSocket'); */ });
    newSocket.onConnectError((error) {
      // print('WebSocket connection error: $error');
      Provider.of<AppState>(context, listen: false).showSnackBar(
        'فشل الاتصال بالخادم',
        Colors.red,
      );
    });
    return newSocket;
  }

  // Set up WebSocket listeners for real-time user status updates
  void _initializeSocketListeners() {
    socket.on('user_status', (data) {
      setState(() {
        final userId = data['userId'];
        final isOnline = data['isOnline'];
        final friend = friends.firstWhere(
              (f) => f.id == userId,
          orElse: () => User(id: '', name: '', job: '', avatarUrl: '', isOnline: false, latitude: 0.0, longitude: 0.0),
        );
        if (friend.id.isNotEmpty) {
          friend.isOnline = isOnline;
        }
      });
    });
  }

  // Fetch friends with caching support
  Future<void> _fetchFriendsWithCache() async {
    setState(() {
      isLoading = true;
      hasError = false;
    });
    try {
      final appState = Provider.of<AppState>(context, listen: false);
      // Check cache first
      if (appState.cachedFriends != null) {
        setState(() {
          friends = (appState.cachedFriends as List<User>?) ?? [];
          filteredFriends = friends;
          isLoading = false;
        });
        return;
      }
      // Fetch from API
      final fetchedFriends = await FriendService.fetchFriends(appState.token!, AppState.getBackendUrl());
      setState(() {
        friends = fetchedFriends;
        appState.cachedFriends = fetchedFriends as List<UserModel>?;
        isLoading = false;
      });
      // Update cache
      appState.cachedFriends = friends as List<UserModel>?;
    } catch (e) {
      setState(() {
        isLoading = false;
        hasError = true;
        errorMessage = e.toString();
      });
      // Show error dialog
      WidgetsBinding.instance.addPostFrameCallback((_) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('خطأ', style: TextStyle(fontFamily: 'Tajawal')),
            content: Text(errorMessage, style: const TextStyle(fontFamily: 'Tajawal')),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إغلاق', style: TextStyle(fontFamily: 'Tajawal')),
              ),
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                  _fetchFriendsWithCache();
                },
                child: const Text('إعادة المحاولة', style: TextStyle(fontFamily: 'Tajawal')),
              ),
            ],
          ),
        );
      });
    }
  }

  // Debounced search to improve performance
  void _filterFriends() {
    _debouncer.run(() {
      setState(() {
        filteredFriends = friends
            .where((friend) => friend.name.toLowerCase().contains(_searchController.text.toLowerCase()))
            .toList();
      });
    });
  }

  // Toggle favorite status
  void _toggleFavorite(User friend) {
    final appState = Provider.of<AppState>(context, listen: false);
    setState(() {
      friend.isFavorite = !friend.isFavorite;
    });
    appState.showSnackBar(
      friend.isFavorite ? '${friend.name} أضيف إلى المفضلة' : '${friend.name} أزيل من المفضلة',
      Colors.green,
    );
  }

  // Navigate to chat page
  void _openChat(BuildContext context, String userId, String userName) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) {
          final appState = Provider.of<AppState>(context, listen: false);
          return ChatPage(
            recipientId: widget.friend.id,
            userName: widget.friend.name,
            currentUserId: appState.userId ?? 'unknown',
            socket: appState.socket!,
            messages: const [], // Initialize with empty list
          );
        },
      ),
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    _debouncer.dispose();
    socket.off('user_status');
    socket.disconnect();
    socket.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final primaryColor = Theme.of(context).primaryColor;
    const avatarRadius = 0.06; // Relative to screen width
    const cardRadius = 0.04; // Relative to screen width

    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: primaryColor,
        elevation: 4,
        title: const Text(
          'الأصدقاء (متابعين متبادلين)',
          style: TextStyle(fontFamily: 'Tajawal', fontWeight: FontWeight.bold),
        ),
      ),
      body: Container(
        decoration: BoxDecoration(
          color: Theme.of(context).scaffoldBackgroundColor,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.all(12.0),
              child: TextField(
                controller: _searchController,
                style: TextStyle(
                  color: Theme.of(context).textTheme.bodyLarge?.color,
                  fontFamily: 'Tajawal',
                ),
                decoration: InputDecoration(
                  hintText: 'ابحث عن صديق...',
                  hintStyle: TextStyle(
                    color: Theme.of(context).textTheme.bodyLarge?.color?.withAlpha(128),
                    fontFamily: 'Tajawal',
                  ),
                  filled: true,
                  fillColor: Theme.of(context).scaffoldBackgroundColor,
                  prefixIcon: Icon(Icons.search, color: Theme.of(context).textTheme.bodyLarge?.color),
                  suffixIcon: _searchController.text.isNotEmpty
                      ? IconButton(
                    icon: const Icon(Icons.clear),
                    onPressed: () {
                      _searchController.clear();
                      _filterFriends();
                    },
                  )
                      : null,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(30),
                    borderSide: BorderSide(
                      color: Theme.of(context).brightness == Brightness.dark ? Colors.white30 : Colors.grey,
                    ),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(30),
                    borderSide: BorderSide(color: primaryColor),
                  ),
                ),
              ),
            ),
            Expanded(
              child: isLoading
                  ? const Center(child: CircularProgressIndicator(color: Colors.teal))
                  : hasError
                  ? const SizedBox.shrink() // Error handled by dialog
                  : RefreshIndicator(
                onRefresh: _fetchFriendsWithCache,
                color: primaryColor,
                child: filteredFriends.isEmpty
                    ? Center(
                  child: Text(
                    'لا يوجد أصدقاء مطابقين',
                    style: TextStyle(
                      color: Theme.of(context).textTheme.bodyLarge?.color,
                      fontFamily: 'Tajawal',
                      fontSize: 18,
                    ),
                  ),
                )
                    : ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: filteredFriends.length,
                  itemBuilder: (context, idx) {
                    final friend = filteredFriends[idx];
                    return FadeInUp(
                      duration: const Duration(milliseconds: 300),
                      child: GestureDetector(
                        onTap: () => _openChat(context, friend.id, friend.name),
                        onLongPress: () => _toggleFavorite(friend),
                        child: Card(
                          elevation: 5,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(screenWidth * cardRadius),
                          ),
                          color: Theme.of(context).cardColor,
                          child: ListTile(
                            contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                            leading: Stack(
                              children: [
                                CachedNetworkImage(
                                  imageUrl: friend.avatarUrl,
                                  imageBuilder: (context, imageProvider) => CircleAvatar(
                                    backgroundImage: imageProvider,
                                    radius: screenWidth * avatarRadius,
                                  ),
                                  placeholder: (context, url) => CircleAvatar(
                                    backgroundColor: Colors.grey[300],
                                    radius: screenWidth * avatarRadius,
                                    child: const CircularProgressIndicator(),
                                  ),
                                  errorWidget: (context, url, error) => CircleAvatar(
                                    backgroundImage: const AssetImage('assets/default_avatar.png'),
                                    radius: screenWidth * avatarRadius,
                                  ),
                                ),
                                if (friend.isOnline)
                                  Positioned(
                                    right: 0,
                                    bottom: 0,
                                    child: Container(
                                      width: 12,
                                      height: 12,
                                      decoration: BoxDecoration(
                                        color: Colors.green,
                                        shape: BoxShape.circle,
                                        border: Border.all(color: Colors.white, width: 2),
                                      ),
                                    ),
                                  ),
                              ],
                            ),
                            title: Text(
                              friend.name,
                              style: TextStyle(
                                fontFamily: 'Tajawal',
                                color: Theme.of(context).brightness == Brightness.dark ? Colors.white : Colors.black87,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            subtitle: Text(
                              friend.job,
                              style: TextStyle(
                                fontFamily: 'Tajawal',
                                color: Theme.of(context).brightness == Brightness.dark ? Colors.white70 : Colors.black54,
                                fontSize: 12,
                              ),
                            ),
                            trailing: Icon(
                              friend.isFavorite ? Icons.star : Icons.star_border,
                              color: friend.isFavorite ? Colors.yellow : Colors.grey,
                            ),
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: ZoomIn(
        duration: const Duration(milliseconds: 500),
        child: FloatingActionButton(
          onPressed: _fetchFriendsWithCache,
          backgroundColor: Colors.teal,
          child: const Icon(Icons.refresh, color: Colors.white),
        ),
      ),
    );
  }
}
