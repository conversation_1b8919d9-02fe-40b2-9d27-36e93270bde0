const Notification = require('../models/notification.model');
const User = require('../models/User.model');
const ErrorResponse = require('../utils/errorResponse');
const asyncHandler = require('../middleware/asyncmiddleware');

// @desc    Create a new notification
// @route   POST /api/notifications
// @access  Private
// @body    {string} userId - ID of the user to notify
// @body    {string} type - Type of notification (like, comment, follow, etc.)
// @body    {string} message - The notification message
// @body    {string} link - Optional link for the notification
// @body    {string} fromUser - ID of the user who triggered the notification
// @body    {string} targetId - ID of the target entity (post, comment, etc.)
// @body    {string} targetType - Type of the target entity (post, comment, user, etc.)
exports.createNotification = asyncHandler(async (req, res, next) => {
  const { userId, type, message, link, fromUser, targetId, targetType } = req.body;
  
  const notification = await Notification.create({
    user: userId,
    type,
    message,
    link,
    fromUser,
    targetId,
    targetType
  });
  
  // Populate the fromUser field with user details
  await notification.populate('fromUser', 'name avatarUrl');
  
  res.status(201).json({
    success: true,
    data: notification
  });
});

// @desc    Get user notifications with pagination
// @route   GET /api/notifications
// @access  Private
// @query   {number} [page=1] - Page number for pagination
// @query   {number} [limit=20] - Number of notifications per page
exports.getNotifications = asyncHandler(async (req, res, next) => {
  const page = parseInt(req.query.page, 10) || 1;
  const limit = parseInt(req.query.limit, 10) || 20;
  const skip = (page - 1) * limit;

  const [notifications, total] = await Promise.all([
    Notification.find({ user: req.user.id })
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .populate('fromUser', 'name avatarUrl')
      .lean(),
    Notification.countDocuments({ user: req.user.id })
  ]);

  // Add relative time to each notification
  const notificationsWithRelativeTime = notifications.map(notif => ({
    ...notif,
    relativeTime: getRelativeTime(notif.createdAt)
  }));

  res.status(200).json({
    success: true,
    count: notificationsWithRelativeTime.length,
    total,
    pagination: {
      currentPage: page,
      totalPages: Math.ceil(total / limit),
      hasMore: page * limit < total
    },
    data: notificationsWithRelativeTime
  });
});

// @desc    Get unread notifications count
// @route   GET /api/notifications/unread-count
// @access  Private
exports.getUnreadCount = asyncHandler(async (req, res, next) => {
  const count = await Notification.countDocuments({
    user: req.user.id,
    isRead: false
  });
  
  res.status(200).json({
    success: true,
    count
  });
});

// @desc    Mark a notification as read
// @route   PUT /api/notifications/:id/read
// @access  Private
exports.markAsRead = asyncHandler(async (req, res, next) => {
  const notification = await Notification.findOneAndUpdate(
    { _id: req.params.id, user: req.user.id },
    { isRead: true },
    { new: true }
  );
  
  if (!notification) {
    return next(new ErrorResponse('Notification not found', 404));
  }
  
  res.status(200).json({
    success: true,
    data: notification
  });
});

// @desc    Mark all notifications as read
// @route   PUT /api/notifications/read-all
// @access  Private
exports.markAllAsRead = asyncHandler(async (req, res, next) => {
  await Notification.updateMany(
    { user: req.user.id, isRead: false },
    { $set: { isRead: true } }
  );
  
  res.status(200).json({
    success: true,
    message: 'All notifications marked as read'
  });
});

// @desc    Delete a notification
// @route   DELETE /api/notifications/:id
// @access  Private
exports.deleteNotification = asyncHandler(async (req, res, next) => {
  const notification = await Notification.findOneAndDelete({
    _id: req.params.id,
    user: req.user.id
  });
  
  if (!notification) {
    return next(new ErrorResponse('Notification not found', 404));
  }
  
  res.status(200).json({
    success: true,
    data: {}
  });
});

// @desc    Get notifications by type
// @route   GET /api/notifications/type/:type
// @access  Private
// @param   {string} type - Type of notifications to filter by
exports.getNotificationsByType = asyncHandler(async (req, res, next) => {
  const page = parseInt(req.query.page, 10) || 1;
  const limit = parseInt(req.query.limit, 10) || 20;
  const skip = (page - 1) * limit;
  
  const [notifications, total] = await Promise.all([
    Notification.find({
      user: req.user.id,
      type: req.params.type
    })
    .sort({ createdAt: -1 })
    .skip(skip)
    .limit(limit)
    .populate('fromUser', 'name avatarUrl')
    .lean(),
    Notification.countDocuments({
      user: req.user.id,
      type: req.params.type
    })
  ]);
  
  // Add relative time to each notification
  const notificationsWithRelativeTime = notifications.map(notif => ({
    ...notif,
    relativeTime: getRelativeTime(notif.createdAt)
  }));
  
  res.status(200).json({
    success: true,
    count: notificationsWithRelativeTime.length,
    total,
    pagination: {
      currentPage: page,
      totalPages: Math.ceil(total / limit),
      hasMore: page * limit < total
    },
    data: notificationsWithRelativeTime
  });
});

// @desc    Get recent notifications (last 5 unread or all unread if less than 5)
// @route   GET /api/notifications/recent
// @access  Private
exports.getRecentNotifications = asyncHandler(async (req, res, next) => {
  const recentNotifications = await Notification.find({
    user: req.user.id,
    isRead: false
  })
  .sort({ createdAt: -1 })
  .limit(5)
  .populate('fromUser', 'name avatarUrl')
  .lean();
  
  // If no unread notifications, get the 5 most recent notifications
  if (recentNotifications.length === 0) {
    const notifications = await Notification.find({ user: req.user.id })
      .sort({ createdAt: -1 })
      .limit(5)
      .populate('fromUser', 'name avatarUrl')
      .lean();
    
    // Add relative time to each notification
    const notificationsWithRelativeTime = notifications.map(notif => ({
      ...notif,
      relativeTime: getRelativeTime(notif.createdAt)
    }));
    
    return res.status(200).json({
      success: true,
      data: notificationsWithRelativeTime,
      allRead: true
    });
  }
  
  // Add relative time to each notification
  const notificationsWithRelativeTime = recentNotifications.map(notif => ({
    ...notif,
    relativeTime: getRelativeTime(notif.createdAt)
  }));
  
  res.status(200).json({
    success: true,
    data: notificationsWithRelativeTime,
    allRead: false
  });
});

// Helper function to calculate relative time
function getRelativeTime(date) {
  if (!date) return '';
  
  const now = new Date();
  const diffInSeconds = Math.floor((now - new Date(date)) / 1000);
  
  if (diffInSeconds < 60) return 'Just now';
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
  if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d ago`;
  
  return new Date(date).toLocaleDateString();
}
