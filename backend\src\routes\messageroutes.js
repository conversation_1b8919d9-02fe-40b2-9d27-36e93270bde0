const express = require('express');
const router = express.Router();
const {
  getMessages,
  sendMessage,
  getConversations,
  markAsRead
} = require('../controllers/messagecontroller');
const { protect } = require('../middleware/authmiddleware');

// مسارات الرسائل
router.route('/')
  .post(protect, sendMessage);

router.get('/conversations', protect, getConversations);
router.get('/:userId', protect, getMessages);
router.put('/read/:userId', protect, markAsRead);

module.exports = router;
