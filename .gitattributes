# Auto detect text files and perform LF normalization
* text=auto eol=lf

# Source code files (LF)
*.js text eol=lf diff=javascript
*.jsx text eol=lf diff=jsx
*.ts text eol=lf diff=typescript
*.tsx text eol=lf diff=tsx
*.json text eol=lf diff=json
*.html text eol=lf diff=html
*.css text eol=lf diff=css
*.scss text eol=lf diff=scss
*.sass text eol=lf diff=sass
*.less text eol=lf diff=less
*.md text eol=lf diff=markdown
*.txt text eol=lf
*.yml text eol=lf diff=yaml
*.yaml text eol=lf diff=yaml
*.graphql text eol=lf
graphql.config.* text eol=lf
*.gql text eol=lf
.editorconfig text eol=lf
.gitattributes text eol=lf
.gitignore text eol=lf
*.lock text eol=lf
*.log text eol=lf
*.sql text eol=lf
*.sh text eol=lf
*.bat text eol=crlf
*.cmd text eol=crlf
*.ps1 text eol=lf
*.psm1 text eol=lf
*.psd1 text eol=lf
*.ps1xml text eol=lf
*.psc1 text eol=lf
*.pssc text eol=lf
*.cdxml text eol=lf
*.xml text eol=lf diff=xml
*.svg text eol=lf

# Binary files that should not be modified
*.png binary
*.jpg binary
*.jpeg binary
*.gif binary
*.ico binary
*.pdf binary
*.zip binary
*.gz binary
*.tar binary
*.exe binary
*.dll binary
*.so binary
*.dylib binary
*.a binary
*.o binary
*.class binary
*.jar binary
*.war binary
*.ear binary
*.sar binary
*.dmg binary
*.pkg binary
*.app binary
*.msi binary
*.msm binary
*.msp binary
*.woff binary
*.woff2 binary
*.eot binary
*.ttf binary
*.otf binary
*.mp3 binary
*.wav binary
*.mp4 binary
*.avi binary
*.mkv binary
*.mov binary
*.flv binary
*.wmv binary
*.m4v binary
*.mpg binary
*.mpeg binary
*.3gp binary
*.3g2 binary
*.3gpp binary
*.3gpp2 binary
*.m4a binary
*.m4p binary
*.m4b binary
*.m4r binary
*.m4v binary
*.aac binary
*.ogg binary
*.oga binary
*.wma binary
*.wmv binary
*.flac binary
*.alac binary
*.aiff binary
*.ape binary
*.wv binary
*.wvx binary
*.wvc binary
*.wvpkg binary
*.wvx binary
*.wvc binary
*.wvpkg binary

# Platform-specific files
.DS_Store binary
.DS_Store? binary
._* binary
.Spotlight-V100 binary
.Trashes binary
ehthumbs.db binary
Thumbs.db binary

# Node.js specific
package-lock.json text eol=lf
package.json text eol=lf
yarn.lock text eol=lf

# Docker specific
Dockerfile* text eol=lf
docker-compose*.yml text eol=lf
.dockerignore text eol=lf

# Editor directories and files
.idea/ export-ignore
.vscode/ export-ignore
*.suo export-ignore
*.ntvs* export-ignore
*.njsproj export-ignore
*.sln export-ignore
*.sw? export-ignore

# Dependency directories
node_modules/ export-ignore
jspm_packages/ export-ignore

# Build outputs
/dist export-ignore
/build export-ignore
/coverage export-ignore

# Logs
logs export-ignore
*.log export-ignore
npm-debug.log* export-ignore
yarn-debug.log* export-ignore
yarn-error.log* export-ignore

# Environment files
.env export-ignore
.env.* export-ignore
!.env.example

# Local development files
.cache/ export-ignore
.temp/ export-ignore
.tmp/ export-ignore
.temp*/ export-ignore
.tmp*/ export-ignore

# Testing
/coverage export-ignore
/cypress/videos/ export-ignore
/cypress/screenshots/ export-ignore

# Misc
.DS_Store export-ignore
Thumbs.db export-ignore

# Custom merge drivers for specific files
package.json merge=ours
package-lock.json merge=ours
yarn.lock merge=ours
*.lock merge=ours

# Define merge strategy for specific files
*.css merge=union
*.html merge=union
*.js merge=union
*.json merge=union
*.md merge=union
*.ts merge=union
*.xml merge=union
*.yml merge=union
*.yaml merge=union

# Define diff for specific files
*.css diff=css
*.html diff=html
*.js diff=javascript
*.json diff=json
*.md diff=markdown
*.ts diff=typescript
*.xml diff=xml
*.yml diff=yaml
*.yaml diff=yaml