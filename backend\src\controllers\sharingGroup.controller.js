const SharingGroup = require('../models/SharingGroup.model');
const ShareBorrowItem = require('../models/ShareBorrowItem.model');

// Get all sharing groups
exports.getSharingGroups = async (req, res) => {
  try {
    const groups = await SharingGroup.find();
    res.json(groups);
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};

// Get sharing group by ID
exports.getSharingGroupById = async (req, res) => {
  try {
    const group = await SharingGroup.findById(req.params.id)
      .populate('items');
    
    if (!group) return res.status(404).json({ error: 'Sharing group not found' });
    res.json(group);
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};

// Create new sharing group
exports.createSharingGroup = async (req, res) => {
  try {
    const { name, description, location, creatorId } = req.body;
    
    const newGroup = new SharingGroup({
      name,
      description,
      location,
      creatorId,
      members: [creatorId] // C<PERSON> is automatically a member
    });
    
    await newGroup.save();
    res.status(201).json(newGroup);
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
};

// Update sharing group
exports.updateSharingGroup = async (req, res) => {
  try {
    const updated = await SharingGroup.findByIdAndUpdate(
      req.params.id,
      req.body,
      { new: true }
    );
    
    if (!updated) return res.status(404).json({ error: 'Sharing group not found' });
    res.json(updated);
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
};

// Delete sharing group
exports.deleteSharingGroup = async (req, res) => {
  try {
    const deleted = await SharingGroup.findByIdAndDelete(req.params.id);
    if (!deleted) return res.status(404).json({ error: 'Sharing group not found' });
    res.json({ message: 'Sharing group deleted successfully' });
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
};

// Add member to sharing group
exports.addMember = async (req, res) => {
  try {
    const { userId } = req.body;
    const group = await SharingGroup.findById(req.params.id);
    
    if (!group) return res.status(404).json({ error: 'Sharing group not found' });
    
    // Check if user is already a member
    if (group.members.includes(userId)) {
      return res.status(400).json({ error: 'User is already a member of this group' });
    }
    
    group.members.push(userId);
    await group.save();
    
    res.json(group);
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
};

// Remove member from sharing group
exports.removeMember = async (req, res) => {
  try {
    const { userId } = req.body;
    const group = await SharingGroup.findById(req.params.id);
    
    if (!group) return res.status(404).json({ error: 'Sharing group not found' });
    
    // Check if user is a member
    if (!group.members.includes(userId)) {
      return res.status(400).json({ error: 'User is not a member of this group' });
    }
    
    // Cannot remove creator
    if (userId === group.creatorId) {
      return res.status(400).json({ error: 'Cannot remove the creator from the group' });
    }
    
    group.members = group.members.filter(id => id !== userId);
    await group.save();
    
    res.json(group);
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
};

// Add item to sharing group
exports.addItem = async (req, res) => {
  try {
    const { itemId } = req.body;
    const group = await SharingGroup.findById(req.params.id);
    
    if (!group) return res.status(404).json({ error: 'Sharing group not found' });
    
    const item = await ShareBorrowItem.findById(itemId);
    if (!item) return res.status(404).json({ error: 'Item not found' });
    
    // Check if item is already in the group
    if (group.items.includes(itemId)) {
      return res.status(400).json({ error: 'Item is already in this group' });
    }
    
    group.items.push(itemId);
    await group.save();
    
    res.json(group);
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
};

// Remove item from sharing group
exports.removeItem = async (req, res) => {
  try {
    const { itemId } = req.body;
    const group = await SharingGroup.findById(req.params.id);
    
    if (!group) return res.status(404).json({ error: 'Sharing group not found' });
    
    // Check if item is in the group
    if (!group.items.includes(itemId)) {
      return res.status(400).json({ error: 'Item is not in this group' });
    }
    
    group.items = group.items.filter(id => id.toString() !== itemId);
    await group.save();
    
    res.json(group);
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
};
