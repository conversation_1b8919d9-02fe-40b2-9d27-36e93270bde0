import 'package:json_annotation/json_annotation.dart';

part 'challenge_model.g.dart';

@JsonSerializable()
class Challenge {
  final String id;
  final String title;
  final String description;
  final String? imageUrl;
  final DateTime startDate;
  final DateTime endDate;
  final int pointsReward;
  final bool isCompleted;
  final int progress;
  final int target;
  final String metric; // e.g., 'posts', 'comments', 'likes', 'days'

  Challenge({
    required this.id,
    required this.title,
    required this.description,
    this.imageUrl,
    required this.startDate,
    required this.endDate,
    required this.pointsReward,
    this.isCompleted = false,
    this.progress = 0,
    required this.target,
    required this.metric,
  });

  factory Challenge.fromJson(Map<String, dynamic> json) => _$ChallengeFromJson(json);
  Map<String, dynamic> toJson() => _$ChallengeToJson(this);

  Challenge copyWith({
    String? id,
    String? title,
    String? description,
    String? imageUrl,
    DateTime? startDate,
    DateTime? endDate,
    int? pointsReward,
    bool? isCompleted,
    int? progress,
    int? target,
    String? metric,
  }) {
    return Challenge(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      imageUrl: imageUrl ?? this.imageUrl,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      pointsReward: pointsReward ?? this.pointsReward,
      isCompleted: isCompleted ?? this.isCompleted,
      progress: progress ?? this.progress,
      target: target ?? this.target,
      metric: metric ?? this.metric,
    );
  }

  // Helper to check if challenge is active
  bool get isActive {
    final now = DateTime.now();
    return now.isAfter(startDate) && now.isBefore(endDate);
  }

  // Helper to get progress percentage
  double get progressPercent {
    return (progress / target).clamp(0.0, 1.0);
  }

  // Helper to check if challenge is completed
  bool get isFullyCompleted => progress >= target;
}
