const mongoose = require('mongoose');
const User = require('../models/User.model');
const PointsTransaction = require('../models/pointstransaction.model');
const Notification = require('../models/notification.model');

/**
 * @desc    Add points to user
 * @route   POST /api/users/points
 * @access  Private
 */
exports.addPoints = async (req, res) => {
  const session = await mongoose.startSession();
  session.startTransaction();
  
  try {
    const { points, reason, referenceId, referenceType } = req.body;
    
    if (!points || points <= 0) {
      await session.abortTransaction();
      return res.status(400).json({ 
        success: false,
        error: 'Points must be a positive number' 
      });
    }
    
    // Update user's points
    const user = await User.findByIdAndUpdate(
      req.user.id,
      { $inc: { points: points } },
      { new: true, session }
    );
    
    if (!user) {
      await session.abortTransaction();
      return res.status(404).json({ 
        success: false,
        error: 'User not found' 
      });
    }
    
    // Log the points transaction
    const pointsTransaction = new PointsTransaction({
      userId: req.user.id,
      points: points,
      type: 'earned',
      reason: reason || 'Points awarded',
      referenceId: referenceId,
      referenceType: referenceType || 'other'
    });
    
    await pointsTransaction.save({ session });
    
    // Create notification for significant point changes
    if (points >= 10) {
      const notification = new Notification({
        userId: req.user.id,
        message: `You've earned ${points} points! ${reason || ''}`,
        type: 'points_earned',
        referenceId: referenceId || null
      });
      await notification.save({ session });
    }
    
    // Check for level up
    await updateUserLevel(user, points, session);
    
    await session.commitTransaction();
    
    res.json({
      success: true,
      points: user.points,
      pointsAdded: points
    });
  } catch (error) {
    await session.abortTransaction();
    console.error('Error adding points:', error);
    res.status(500).json({ 
      success: false,
      error: 'Server error while adding points' 
    });
  } finally {
    session.endSession();
  }
};

/**
 * @desc    Get user's points history
 * @route   GET /api/users/points/history
 * @access  Private
 */
exports.getPointsHistory = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const skip = (page - 1) * limit;
    
    const [transactions, total] = await Promise.all([
      PointsTransaction.find({ userId: req.user.id })
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .lean(),
      PointsTransaction.countDocuments({ userId: req.user.id })
    ]);
    
    res.json({
      success: true,
      transactions,
      pagination: {
        total,
        page,
        pages: Math.ceil(total / limit),
        limit
      }
    });
  } catch (error) {
    console.error('Error fetching points history:', error);
    res.status(500).json({ 
      success: false,
      error: 'Server error while fetching points history' 
    });
  }
};

/**
 * @desc    Get user's current points and level
 * @route   GET /api/users/points/summary
 * @access  Private
 */
exports.getPointsSummary = async (req, res) => {
  try {
    const user = await User.findById(req.user.id).select('points level experience');
    
    if (!user) {
      return res.status(404).json({ 
        success: false,
        error: 'User not found' 
      });
    }
    
    // Calculate next level progress (simplified example)
    const pointsToNextLevel = Math.pow(user.level * 10, 2);
    const progress = Math.min(100, Math.floor((user.experience / pointsToNextLevel) * 100));
    
    res.json({
      success: true,
      points: user.points,
      level: user.level,
      experience: user.experience,
      pointsToNextLevel,
      progress,
      nextLevel: user.level + 1
    });
  } catch (error) {
    console.error('Error fetching points summary:', error);
    res.status(500).json({ 
      success: false,
      error: 'Server error while fetching points summary' 
    });
  }
};

/**
 * @desc    Redeem points for rewards
 * @route   POST /api/users/points/redeem
 * @access  Private
 */
exports.redeemPoints = async (req, res) => {
  const session = await mongoose.startSession();
  session.startTransaction();
  
  try {
    const { rewardId, points } = req.body;
    
    if (!rewardId || !points || points <= 0) {
      await session.abortTransaction();
      return res.status(400).json({ 
        success: false,
        error: 'Invalid reward or points' 
      });
    }
    
    // In a real app, you would validate the reward and its cost here
    // For now, we'll just deduct the points
    
    const user = await User.findById(req.user.id).session(session);
    
    if (!user) {
      await session.abortTransaction();
      return res.status(404).json({ 
        success: false,
        error: 'User not found' 
      });
    }
    
    if (user.points < points) {
      await session.abortTransaction();
      return res.status(400).json({ 
        success: false,
        error: 'Not enough points' 
      });
    }
    
    // Deduct points
    user.points -= points;
    await user.save({ session });
    
    // Log the points transaction
    const pointsTransaction = new PointsTransaction({
      userId: req.user.id,
      points: -points,
      type: 'spent',
      reason: `Redeemed reward ${rewardId}`,
      referenceId: rewardId,
      referenceType: 'reward_redemption'
    });
    await pointsTransaction.save({ session });
    
    // Create notification
    const notification = new Notification({
      userId: req.user.id,
      message: `You've redeemed ${points} points for a reward!`,
      type: 'points_spent',
      referenceId: rewardId
    });
    await notification.save({ session });
    
    await session.commitTransaction();
    
    res.json({
      success: true,
      points: user.points,
      pointsSpent: points,
      rewardId
    });
  } catch (error) {
    await session.abortTransaction();
    console.error('Error redeeming points:', error);
    res.status(500).json({ 
      success: false,
      error: 'Server error while redeeming points' 
    });
  } finally {
    session.endSession();
  }
};

/**
 * @desc    Calculate and update user's level based on experience
 * @param   {Object} user - The user document
 * @param   {Number} experienceToAdd - Experience points to add
 * @param   {Object} session - MongoDB session
 * @private
 */
const updateUserLevel = async (user, experienceToAdd, session) => {
  if (!experienceToAdd || experienceToAdd <= 0) return;
  
  // Add experience
  user.experience += experienceToAdd;
  
  // Check for level up (simplified formula)
  const experienceForNextLevel = Math.pow(user.level * 10, 2);
  
  if (user.experience >= experienceForNextLevel) {
    // Level up!
    user.level += 1;
    user.experience -= experienceForNextLevel;
    
    // Create level up notification
    const notification = new Notification({
      userId: user._id,
      message: `Congratulations! You've reached level ${user.level}!`,
      type: 'level_up',
      referenceId: user._id
    });
    await notification.save({ session });
  }
  
  await user.save({ session });
};

// Export for testing
module.exports.updateUserLevel = updateUserLevel;
