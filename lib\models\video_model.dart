import 'package:uuid/uuid.dart';
import 'comment_model.dart';

const Uuid _uuid = Uuid();

/// فئة تمثل فيديو في التطبيق
class Video {
  /// معرف الفيديو الفريد
  final String id;
  
  /// عنوان الفيديو
  final String title;
  
  /// وصف الفيديو
  final String description;
  
  /// رابط الفيديو
  final String videoUrl;
  
  /// رابط الصورة المصغرة
  final String thumbnailUrl;
  
  /// حالة البث المباشر
  final bool isLive;
  
  /// التفاعلات (مثل الإعجاب، الحب)
  final Map<String, int> reactions;
  
  /// تفاعل المستخدم الحالي
  final String? userReaction;
  
  /// قائمة التعليقات
  final List<Comment> comments;
  
  /// معرف المستخدم صاحب الفيديو
  final String? userId;
  
  /// اسم صاحب الفيديو
  final String? authorName;
  
  /// صورة الملف الشخصي لصاحب الفيديو
  final String? authorAvatar;
  
  /// تاريخ النشر
  final DateTime createdAt;
  
  /// تاريخ التحديث الأخير
  final DateTime? updatedAt;
  
  /// عدد المشاهدات
  final int viewCount;
  
  /// مدة الفيديو (بالثواني)
  final int? duration;
  
  /// فئة الفيديو
  final String category;
  
  /// الفئة العمرية المستهدفة للفيديو
  final String? ageGroup;
  
  /// حالة الفيديو (معلن، خاص، إلخ)
  final String status;
  
  /// علامات للفيديو
  final List<String> tags;
  
  /// هل الفيديو مميز؟
  final bool isFeatured;
  
  /// متوسط تقييم الفيديو (من 1 إلى 5)
  final double? rating;
  
  /// عدد التقييمات
  final int ratingCount;

  Video({
    String? id,
    required this.title,
    this.description = '',
    required this.videoUrl,
    required this.thumbnailUrl,
    this.isLive = false,
    Map<String, int>? reactions,
    this.userReaction,
    List<Comment>? comments,
    this.userId,
    this.authorName,
    this.authorAvatar,
    DateTime? createdAt,
    this.updatedAt,
    int? viewCount,
    this.duration,
    required this.category,
    this.ageGroup,
    this.status = 'public',
    List<String>? tags,
    this.isFeatured = false,
    this.rating,
    int? ratingCount,
  }) : 
    id = id ?? _uuid.v4(),
    reactions = reactions ?? {'like': 0, 'love': 0, 'haha': 0},
    comments = comments ?? [],
    createdAt = createdAt ?? DateTime.now(),
    viewCount = viewCount ?? 0,
    tags = tags ?? [],
    ratingCount = ratingCount ?? 0;

  /// إنشاء نموذج من JSON
  factory Video.fromJson(Map<String, dynamic> json) {
    return Video(
      id: json['id'] ?? json['_id'],
      title: json['title']?.toString() ?? 'فيديو بدون عنوان',
      description: json['description']?.toString() ?? '',
      videoUrl: json['videoUrl']?.toString() ?? json['url']?.toString() ?? '',
      thumbnailUrl: json['thumbnailUrl']?.toString() ?? json['thumbnail']?.toString() ?? '',
      isLive: json['isLive'] == true,
      reactions: {
        'like': (json['likes'] as num?)?.toInt() ?? (json['reactions']?['like'] as num?)?.toInt() ?? 0,
        'love': (json['loves'] as num?)?.toInt() ?? (json['reactions']?['love'] as num?)?.toInt() ?? 0,
        'haha': (json['hahas'] as num?)?.toInt() ?? (json['reactions']?['haha'] as num?)?.toInt() ?? 0,
      },
      userReaction: json['userReaction']?.toString(),
      comments: json['comments'] is List
          ? (json['comments'] as List).map((e) => Comment.fromJson(e)).toList()
          : [],
      userId: json['userId'],
      authorName: json['authorName'] ?? json['author']?['name'],
      authorAvatar: json['authorAvatar'] ?? json['author']?['avatar'],
      createdAt: json['createdAt'] != null 
          ? DateTime.tryParse(json['createdAt'].toString()) ?? DateTime.now()
          : null,
      updatedAt: json['updatedAt'] != null 
          ? DateTime.tryParse(json['updatedAt'].toString())
          : null,
      viewCount: json['viewCount'] is int 
          ? json['viewCount'] 
          : (json['viewCount'] is num ? (json['viewCount'] as num).toInt() : 0),
      duration: json['duration'] is int 
          ? json['duration'] 
          : (json['duration'] is num ? (json['duration'] as num).toInt() : null),
      category: json['category']?.toString() ?? 'عام',
      ageGroup: json['ageGroup'],
      status: json['status']?.toString() ?? 'public',
      tags: json['tags'] is List ? List<String>.from(json['tags']) : [],
      isFeatured: json['isFeatured'] == true,
      rating: json['rating'] is double 
          ? json['rating'] 
          : (json['rating'] is num ? (json['rating'] as num).toDouble() : null),
      ratingCount: json['ratingCount'] is int 
          ? json['ratingCount'] 
          : (json['ratingCount'] is num ? (json['ratingCount'] as num).toInt() : 0),
    );
  }

  /// تحويل النموذج إلى خريطة
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'videoUrl': videoUrl,
      'thumbnailUrl': thumbnailUrl,
      'isLive': isLive,
      'reactions': Map<String, int>.from(reactions),
      'userReaction': userReaction,
      'comments': comments.map((c) => c.toJson()).toList(),
      'userId': userId,
      'authorName': authorName,
      'authorAvatar': authorAvatar,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'viewCount': viewCount,
      'duration': duration,
      'category': category,
      'ageGroup': ageGroup,
      'status': status,
      'tags': List<String>.from(tags),
      'isFeatured': isFeatured,
      'rating': rating,
      'ratingCount': ratingCount,
    };
  }

  /// إنشاء نسخة من النموذج مع تحديث بعض الخصائص
  Video copyWith({
    String? id,
    String? title,
    String? description,
    String? videoUrl,
    String? thumbnailUrl,
    bool? isLive,
    Map<String, int>? reactions,
    String? userReaction,
    List<Comment>? comments,
    String? userId,
    String? authorName,
    String? authorAvatar,
    DateTime? createdAt,
    DateTime? updatedAt,
    int? viewCount,
    int? duration,
    String? category,
    String? ageGroup,
    String? status,
    List<String>? tags,
    bool? isFeatured,
    double? rating,
    int? ratingCount,
  }) {
    return Video(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      videoUrl: videoUrl ?? this.videoUrl,
      thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
      isLive: isLive ?? this.isLive,
      reactions: reactions ?? Map<String, int>.from(this.reactions),
      userReaction: userReaction ?? this.userReaction,
      comments: comments ?? List<Comment>.from(this.comments),
      userId: userId ?? this.userId,
      authorName: authorName ?? this.authorName,
      authorAvatar: authorAvatar ?? this.authorAvatar,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      viewCount: viewCount ?? this.viewCount,
      duration: duration ?? this.duration,
      category: category ?? this.category,
      ageGroup: ageGroup ?? this.ageGroup,
      status: status ?? this.status,
      tags: tags ?? List<String>.from(this.tags),
      isFeatured: isFeatured ?? this.isFeatured,
      rating: rating ?? this.rating,
      ratingCount: ratingCount ?? this.ratingCount,
    );
  }

  @override
  String toString() {
    return 'Video(id: $id, title: $title, author: $authorName, views: $viewCount)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Video &&
        other.id == id &&
        other.title == title &&
        other.description == description &&
        other.videoUrl == videoUrl &&
        other.thumbnailUrl == thumbnailUrl &&
        other.isLive == isLive &&
        other.userId == userId &&
        other.viewCount == viewCount &&
        other.category == category &&
        other.status == status &&
        other.isFeatured == isFeatured;
  }

  @override
  int get hashCode => Object.hash(
        id,
        title,
        description,
        videoUrl,
        thumbnailUrl,
        isLive,
        userId,
        viewCount,
        category,
        status,
        isFeatured,
      );

  // Increment view count
  Video incrementViewCount() {
    return copyWith(viewCount: viewCount + 1);
  }

  // Add or update a reaction
  Video addReaction(String reactionType) {
    final newReactions = Map<String, int>.from(reactions);
    newReactions[reactionType] = (newReactions[reactionType] ?? 0) + 1;
    return copyWith(
      reactions: newReactions,
      userReaction: reactionType,
    );
  }

  // Remove a reaction
  Video removeReaction(String reactionType) {
    final newReactions = Map<String, int>.from(reactions);
    final currentCount = newReactions[reactionType] ?? 0;
    if (currentCount > 0) {
      newReactions[reactionType] = currentCount - 1;
    }
    return copyWith(
      reactions: newReactions,
      userReaction: userReaction == reactionType ? null : userReaction,
    );
  }

  // Add a comment
  Video addComment(Comment comment) {
    final newComments = List<Comment>.from(comments)..add(comment);
    return copyWith(comments: newComments);
  }

  // Remove a comment
  Video removeComment(String commentId) {
    final newComments = comments.where((c) => c.id != commentId).toList();
    return copyWith(comments: newComments);
  }

  // Update rating
  Video updateRating(double newRating) {
    final newRatingCount = ratingCount + 1;
    final newAverage = ((rating ?? 0) * ratingCount + newRating) / newRatingCount;
    
    return copyWith(
      rating: newAverage,
      ratingCount: newRatingCount,
    );
  }

  /// Convert the video to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'videoUrl': videoUrl,
      'thumbnailUrl': thumbnailUrl,
      'isLive': isLive,
      'reactions': Map<String, int>.from(reactions),
      'userReaction': userReaction,
      'comments': comments.map((comment) => comment.toJson()).toList(),
      'userId': userId,
      'authorName': authorName,
      'authorAvatar': authorAvatar,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'viewCount': viewCount,
      'duration': duration,
      'category': category,
      'ageGroup': ageGroup,
      'status': status,
      'tags': List<String>.from(tags),
      'isFeatured': isFeatured,
      'rating': rating,
      'ratingCount': ratingCount,
    };
  }

  // Add a getter for backward compatibility with 'thumbnail' field
  String get thumbnail => thumbnailUrl;
  
  // Add a getter for backward compatibility with 'publishDate' field
  DateTime? get publishDate => createdAt;
}
