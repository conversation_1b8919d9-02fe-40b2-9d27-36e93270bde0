import 'dart:async';
import 'dart:convert';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart' as geo;
import 'package:http/http.dart' as http;
import 'package:animate_do/animate_do.dart';
import 'appstate.dart';

// ===============================
// نماذج البيانات - Data Models
// ===============================

/// نموذج الموقع الجغرافي
class LocationData {
  final double latitude;
  final double longitude;
  final String address;
  final String? city;
  final String? country;
  final String? postalCode;

  LocationData({
    required this.latitude,
    required this.longitude,
    required this.address,
    this.city,
    this.country,
    this.postalCode,
  });

  factory LocationData.fromJson(Map<String, dynamic> json) {
    return LocationData(
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
      address: json['address'] ?? '',
      city: json['city'],
      country: json['country'],
      postalCode: json['postalCode'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'latitude': latitude,
      'longitude': longitude,
      'address': address,
      'city': city,
      'country': country,
      'postalCode': postalCode,
    };
  }

  LatLng get latLng => LatLng(latitude, longitude);

  /// حساب المسافة بين موقعين (بالكيلومتر)
  double distanceTo(LocationData other) {
    return Geolocator.distanceBetween(
          latitude,
          longitude,
          other.latitude,
          other.longitude,
        ) /
        1000; // Convert to kilometers
  }
}

/// نموذج إعلان التبرع مع الموقع
class DonationAdWithLocation {
  final String id;
  final String title;
  final String description;
  final String category;
  final double targetAmount;
  final double raisedAmount;
  final String imageUrl;
  final LocationData? location;
  final DateTime createdAt;
  final bool isUrgent;
  final bool isFeatured;
  final double averageRating;
  final int ratingCount;

  DonationAdWithLocation({
    required this.id,
    required this.title,
    required this.description,
    required this.category,
    required this.targetAmount,
    required this.raisedAmount,
    required this.imageUrl,
    this.location,
    required this.createdAt,
    this.isUrgent = false,
    this.isFeatured = false,
    this.averageRating = 0.0,
    this.ratingCount = 0,
  });

  factory DonationAdWithLocation.fromJson(Map<String, dynamic> json) {
    return DonationAdWithLocation(
      id: json['_id'] ?? '',
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      category: json['category'] ?? '',
      targetAmount: (json['targetAmount'] as num?)?.toDouble() ?? 0.0,
      raisedAmount: (json['raisedAmount'] as num?)?.toDouble() ?? 0.0,
      imageUrl: json['imageUrl'] ?? '',
      location: json['location'] != null
          ? LocationData.fromJson(json['location'])
          : null,
      createdAt: DateTime.tryParse(json['createdAt'] ?? '') ?? DateTime.now(),
      isUrgent: json['isUrgent'] ?? false,
      isFeatured: json['isFeatured'] ?? false,
      averageRating: (json['averageRating'] as num?)?.toDouble() ?? 0.0,
      ratingCount: json['ratingCount'] ?? 0,
    );
  }

  double get progressPercentage =>
      targetAmount > 0 ? (raisedAmount / targetAmount).clamp(0.0, 1.0) : 0.0;

  bool get isNearlyComplete => progressPercentage >= 0.8;
}

/// نموذج منطقة البحث
class SearchArea {
  final LatLng center;
  final double radiusKm;
  final String name;

  SearchArea({
    required this.center,
    required this.radiusKm,
    required this.name,
  });

  /// التحقق من وجود نقطة داخل المنطقة
  bool containsPoint(LatLng point) {
    final distance = Geolocator.distanceBetween(
          center.latitude,
          center.longitude,
          point.latitude,
          point.longitude,
        ) /
        1000; // Convert to kilometers

    return distance <= radiusKm;
  }
}

// ===============================
// خدمة الخرائط - Maps Service
// ===============================

class MapsService {
  final String baseUrl;
  final String authToken;
  final String googleMapsApiKey;

  MapsService({
    required this.baseUrl,
    required this.authToken,
    required this.googleMapsApiKey,
  });

  /// إنشاء خدمة خرائط باستخدام إعدادات التطبيق
  factory MapsService.fromAppState() {
    return MapsService(
      baseUrl: AppState.getBackendUrl(),
      authToken: AppState.signerKey,
      googleMapsApiKey: 'YOUR_GOOGLE_MAPS_API_KEY', // يجب تحديث هذا
    );
  }

  /// جلب إعلانات التبرع مع المواقع
  Future<List<DonationAdWithLocation>> fetchAdsWithLocations({
    LatLng? center,
    double? radiusKm,
    String? category,
    bool? urgentOnly,
  }) async {
    try {
      final queryParams = <String, String>{};

      if (center != null) {
        queryParams['lat'] = center.latitude.toString();
        queryParams['lng'] = center.longitude.toString();
      }

      if (radiusKm != null) {
        queryParams['radius'] = radiusKm.toString();
      }

      if (category != null && category.isNotEmpty) {
        queryParams['category'] = category;
      }

      if (urgentOnly == true) {
        queryParams['urgent'] = 'true';
      }

      final uri = Uri.parse('$baseUrl/donation_ads/locations')
          .replace(queryParameters: queryParams);

      final response = await http.get(
        uri,
        headers: {
          'Authorization': 'Bearer $authToken',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return (data['ads'] as List)
            .map((item) => DonationAdWithLocation.fromJson(item))
            .toList();
      } else {
        throw Exception('فشل في جلب الإعلانات: ${response.body}');
      }
    } catch (e) {
      throw Exception('خطأ في الشبكة: $e');
    }
  }

  /// البحث عن عنوان باستخدام النص
  Future<List<LocationData>> searchLocations(String query) async {
    try {
      final uri = Uri.parse('https://maps.googleapis.com/maps/api/geocode/json')
          .replace(queryParameters: {
        'address': query,
        'key': googleMapsApiKey,
        'language': 'ar',
      });

      final response = await http.get(uri);

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);

        if (data['status'] == 'OK') {
          return (data['results'] as List).map((result) {
            final location = result['geometry']['location'];
            final addressComponents = result['address_components'] as List;

            String? city, country, postalCode;

            for (final component in addressComponents) {
              final types = component['types'] as List;
              if (types.contains('locality')) {
                city = component['long_name'];
              } else if (types.contains('country')) {
                country = component['long_name'];
              } else if (types.contains('postal_code')) {
                postalCode = component['long_name'];
              }
            }

            return LocationData(
              latitude: location['lat'].toDouble(),
              longitude: location['lng'].toDouble(),
              address: result['formatted_address'],
              city: city,
              country: country,
              postalCode: postalCode,
            );
          }).toList();
        } else {
          throw Exception('فشل في البحث: ${data['status']}');
        }
      } else {
        throw Exception('فشل في الاتصال بخدمة الخرائط');
      }
    } catch (e) {
      throw Exception('خطأ في البحث عن الموقع: $e');
    }
  }

  /// الحصول على الموقع الحالي
  Future<LocationData> getCurrentLocation() async {
    try {
      // Check permissions
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          throw Exception('تم رفض إذن الموقع');
        }
      }

      if (permission == LocationPermission.deniedForever) {
        throw Exception('تم رفض إذن الموقع نهائياً');
      }

      // Get current position
      final position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );

      // Get address from coordinates
      final placemarks = await geo.placemarkFromCoordinates(
        position.latitude,
        position.longitude,
      );

      final placemark = placemarks.isNotEmpty ? placemarks.first : null;

      return LocationData(
        latitude: position.latitude,
        longitude: position.longitude,
        address: placemark != null
            ? '${placemark.street}, ${placemark.locality}, ${placemark.country}'
            : 'موقع غير محدد',
        city: placemark?.locality,
        country: placemark?.country,
        postalCode: placemark?.postalCode,
      );
    } catch (e) {
      throw Exception('فشل في الحصول على الموقع الحالي: $e');
    }
  }
}

// ===============================
// مكونات واجهة المستخدم - UI Components
// ===============================

/// مكون الخريطة التفاعلية
class InteractiveMap extends StatefulWidget {
  final List<DonationAdWithLocation> ads;
  final LatLng? initialCenter;
  final double initialZoom;
  final Function(DonationAdWithLocation)? onAdSelected;
  final Function(LatLng)? onLocationSelected;
  final bool showCurrentLocation;

  const InteractiveMap({
    Key? key,
    required this.ads,
    this.initialCenter,
    this.initialZoom = 10.0,
    this.onAdSelected,
    this.onLocationSelected,
    this.showCurrentLocation = true,
  }) : super(key: key);

  @override
  State<InteractiveMap> createState() => _InteractiveMapState();
}

class _InteractiveMapState extends State<InteractiveMap> {
  GoogleMapController? _mapController;
  Set<Marker> _markers = {};
  Set<Circle> _circles = {};
  LatLng? _currentLocation;
  DonationAdWithLocation? _selectedAd;

  @override
  void initState() {
    super.initState();
    _createMarkers();
    if (widget.showCurrentLocation) {
      _getCurrentLocation();
    }
  }

  @override
  void didUpdateWidget(InteractiveMap oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.ads != widget.ads) {
      _createMarkers();
    }
  }

  Future<void> _getCurrentLocation() async {
    try {
      final mapsService = MapsService.fromAppState();

      final location = await mapsService.getCurrentLocation();
      setState(() {
        _currentLocation = location.latLng;
      });

      _addCurrentLocationMarker();
    } catch (e) {
      // Handle error silently
    }
  }

  void _createMarkers() {
    final markers = <Marker>{};

    for (final ad in widget.ads) {
      if (ad.location != null) {
        markers.add(
          Marker(
            markerId: MarkerId(ad.id),
            position: ad.location!.latLng,
            icon: _getMarkerIcon(ad),
            infoWindow: InfoWindow(
              title: ad.title,
              snippet:
                  '${ad.raisedAmount.toStringAsFixed(0)} / ${ad.targetAmount.toStringAsFixed(0)}',
              onTap: () => _onMarkerTapped(ad),
            ),
            onTap: () => _onMarkerTapped(ad),
          ),
        );
      }
    }

    setState(() => _markers = markers);
  }

  BitmapDescriptor _getMarkerIcon(DonationAdWithLocation ad) {
    if (ad.isUrgent) {
      return BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueRed);
    } else if (ad.isFeatured) {
      return BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueOrange);
    } else if (ad.isNearlyComplete) {
      return BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueGreen);
    } else {
      return BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueBlue);
    }
  }

  void _addCurrentLocationMarker() {
    if (_currentLocation != null) {
      setState(() {
        _markers.add(
          Marker(
            markerId: const MarkerId('current_location'),
            position: _currentLocation!,
            icon: BitmapDescriptor.defaultMarkerWithHue(
                BitmapDescriptor.hueViolet),
            infoWindow: const InfoWindow(
              title: 'موقعي الحالي',
            ),
          ),
        );

        _circles.add(
          Circle(
            circleId: const CircleId('current_location_radius'),
            center: _currentLocation!,
            radius: 1000, // 1km radius
            fillColor: Colors.blue.withOpacity(0.1),
            strokeColor: Colors.blue,
            strokeWidth: 2,
          ),
        );
      });
    }
  }

  void _onMarkerTapped(DonationAdWithLocation ad) {
    setState(() => _selectedAd = ad);
    widget.onAdSelected?.call(ad);

    // Animate camera to marker
    if (_mapController != null && ad.location != null) {
      _mapController!.animateCamera(
        CameraUpdate.newLatLngZoom(ad.location!.latLng, 15.0),
      );
    }
  }

  void _onMapTapped(LatLng position) {
    setState(() => _selectedAd = null);
    widget.onLocationSelected?.call(position);
  }

  @override
  Widget build(BuildContext context) {
    final center = widget.initialCenter ??
        (widget.ads.isNotEmpty && widget.ads.first.location != null
            ? widget.ads.first.location!.latLng
            : const LatLng(24.7136, 46.6753)); // Riyadh default

    return Stack(
      children: [
        GoogleMap(
          onMapCreated: (controller) => _mapController = controller,
          initialCameraPosition: CameraPosition(
            target: center,
            zoom: widget.initialZoom,
          ),
          markers: _markers,
          circles: _circles,
          onTap: _onMapTapped,
          myLocationEnabled: widget.showCurrentLocation,
          myLocationButtonEnabled: widget.showCurrentLocation,
          mapType: MapType.normal,
          zoomControlsEnabled: true,
          compassEnabled: true,
          trafficEnabled: false,
        ),

        // Map legend
        Positioned(
          top: 16,
          right: 16,
          child: _buildMapLegend(),
        ),

        // Selected ad info
        if (_selectedAd != null)
          Positioned(
            bottom: 16,
            left: 16,
            right: 16,
            child: _buildSelectedAdCard(),
          ),
      ],
    );
  }

  Widget _buildMapLegend() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'دليل الخريطة',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 8),
            _buildLegendItem(Colors.red, 'عاجل'),
            _buildLegendItem(Colors.orange, 'مميز'),
            _buildLegendItem(Colors.green, 'مكتمل تقريباً'),
            _buildLegendItem(Colors.blue, 'عادي'),
            if (widget.showCurrentLocation)
              _buildLegendItem(Colors.purple, 'موقعي'),
          ],
        ),
      ),
    );
  }

  Widget _buildLegendItem(Color color, String label) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 12,
            height: 12,
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 8),
          Text(
            label,
            style: Theme.of(context).textTheme.bodySmall,
          ),
        ],
      ),
    );
  }

  Widget _buildSelectedAdCard() {
    return FadeInUp(
      duration: const Duration(milliseconds: 300),
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      _selectedAd!.title,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  IconButton(
                    onPressed: () => setState(() => _selectedAd = null),
                    icon: const Icon(Icons.close),
                    iconSize: 20,
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                _selectedAd!.description,
                style: Theme.of(context).textTheme.bodyMedium,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'المبلغ المطلوب: ${_selectedAd!.targetAmount.toStringAsFixed(0)} ريال',
                          style: Theme.of(context).textTheme.bodySmall,
                        ),
                        const SizedBox(height: 4),
                        LinearProgressIndicator(
                          value: _selectedAd!.progressPercentage,
                          backgroundColor: Colors.grey[300],
                          valueColor: AlwaysStoppedAnimation<Color>(
                            _selectedAd!.isNearlyComplete
                                ? Colors.green
                                : Colors.blue,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '${(_selectedAd!.progressPercentage * 100).toStringAsFixed(1)}% مكتمل',
                          style: Theme.of(context).textTheme.bodySmall,
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 16),
                  ElevatedButton(
                    onPressed: () {
                      // Navigate to ad details
                    },
                    child: const Text('عرض التفاصيل'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// شاشة الخريطة الكاملة
class MapsScreen extends StatefulWidget {
  const MapsScreen({Key? key}) : super(key: key);

  @override
  State<MapsScreen> createState() => _MapsScreenState();
}

class _MapsScreenState extends State<MapsScreen> {
  late final MapsService _mapsService;

  List<DonationAdWithLocation> _ads = [];
  bool _isLoading = true;
  String? _selectedCategory;
  bool _urgentOnly = false;
  double _searchRadius = 10.0; // km
  LatLng? _searchCenter;
  final TextEditingController _searchController = TextEditingController();

  final List<String> _categories = [
    'الكل',
    'كفالة أبنائنا',
    'علاجية',
    'إنسانية',
    'تعليمية',
    'بناء مساجد',
    'أخرى',
  ];

  @override
  void initState() {
    super.initState();
    _mapsService = MapsService.fromAppState();
    _loadAds();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadAds() async {
    setState(() => _isLoading = true);

    try {
      final ads = await _mapsService.fetchAdsWithLocations(
        center: _searchCenter,
        radiusKm: _searchRadius,
        category: _selectedCategory == 'الكل' ? null : _selectedCategory,
        urgentOnly: _urgentOnly,
      );

      setState(() {
        _ads = ads;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('خطأ في جلب البيانات: $e')),
      );
    }
  }

  Future<void> _searchLocation() async {
    final query = _searchController.text.trim();
    if (query.isEmpty) return;

    try {
      final locations = await _mapsService.searchLocations(query);
      if (locations.isNotEmpty) {
        setState(() {
          _searchCenter = locations.first.latLng;
        });
        _loadAds();
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('خطأ في البحث: $e')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('خريطة الحالات'),
        actions: [
          IconButton(
            onPressed: _showFilterDialog,
            icon: const Icon(Icons.filter_list),
          ),
        ],
      ),
      body: Column(
        children: [
          // Search bar
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _searchController,
                    decoration: const InputDecoration(
                      hintText: 'ابحث عن موقع...',
                      prefixIcon: Icon(Icons.search),
                      border: OutlineInputBorder(),
                    ),
                    onSubmitted: (_) => _searchLocation(),
                  ),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: _searchLocation,
                  child: const Text('بحث'),
                ),
              ],
            ),
          ),

          // Category filter
          SizedBox(
            height: 50,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: _categories.length,
              itemBuilder: (context, index) {
                final category = _categories[index];
                final isSelected = _selectedCategory == category ||
                    (_selectedCategory == null && category == 'الكل');

                return Padding(
                  padding: const EdgeInsets.only(right: 8),
                  child: FilterChip(
                    label: Text(category),
                    selected: isSelected,
                    onSelected: (selected) {
                      setState(() {
                        _selectedCategory =
                            category == 'الكل' ? null : category;
                      });
                      _loadAds();
                    },
                  ),
                );
              },
            ),
          ),

          // Map
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : InteractiveMap(
                    ads: _ads,
                    initialCenter: _searchCenter,
                    onAdSelected: (ad) {
                      // Handle ad selection
                    },
                    onLocationSelected: (location) {
                      setState(() => _searchCenter = location);
                    },
                  ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _getCurrentLocationAndSearch,
        child: const Icon(Icons.my_location),
      ),
    );
  }

  Future<void> _getCurrentLocationAndSearch() async {
    try {
      final location = await _mapsService.getCurrentLocation();
      setState(() {
        _searchCenter = location.latLng;
      });
      _loadAds();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('خطأ في الحصول على الموقع: $e')),
      );
    }
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('خيارات التصفية'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SwitchListTile(
              title: const Text('الحالات العاجلة فقط'),
              value: _urgentOnly,
              onChanged: (value) {
                setState(() => _urgentOnly = value);
              },
            ),
            const SizedBox(height: 16),
            Text('نطاق البحث: ${_searchRadius.toStringAsFixed(0)} كم'),
            Slider(
              value: _searchRadius,
              min: 1.0,
              max: 100.0,
              divisions: 99,
              onChanged: (value) {
                setState(() => _searchRadius = value);
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _loadAds();
            },
            child: const Text('تطبيق'),
          ),
        ],
      ),
    );
  }
}
