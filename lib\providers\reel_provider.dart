import 'package:flutter/material.dart';
import 'package:socket_io_client/socket_io_client.dart' as io;
import '../models/reel_models.dart';
import '../services/reel_service.dart';
import '../appstate.dart';

class ReelProvider with ChangeNotifier {
  List<ReelVideo> _reels = [];
  List<ReelComment> _comments = [];
  bool _isLoading = false;
  bool _hasError = false;
  String _errorMessage = '';
  int _currentPage = 1;
  bool _hasMoreReels = true;
  io.Socket? _socket;
  String _currentCategory = 'all';
  String _currentDifficulty = 'all';
  String _sortBy = 'latest';

  // Getters
  List<ReelVideo> get reels => _reels;
  List<ReelComment> get comments => _comments;
  bool get isLoading => _isLoading;
  bool get hasError => _hasError;
  String get errorMessage => _errorMessage;
  bool get hasMoreReels => _hasMoreReels;
  String get currentCategory => _currentCategory;
  String get currentDifficulty => _currentDifficulty;
  bool _isConnected = true;
  bool get isConnected => _isConnected;

  // تهيئة الاتصال
  void initialize() {
    _connectSocket();
    // تأخير تحميل البيانات لتجنب setState during build
    Future.microtask(() => loadReels());
  }

  // تحميل الفيديوهات من الباك إند
  Future<void> loadReels({bool refresh = false}) async {
    if (_isLoading) return;

    try {
      _setLoading(true);
      _setError(false, '');

      if (refresh) {
        _currentPage = 1;
        _hasMoreReels = true;
      }

      final newReels = await ReelService.getReels(
        page: _currentPage,
        limit: 10,
        category: _currentCategory,
        difficulty: _currentDifficulty,
        sortBy: _sortBy,
      );

      if (refresh) {
        _reels = newReels;
      } else {
        _reels.addAll(newReels);
      }

      _hasMoreReels = newReels.length >= 10;
      _currentPage++;
    } catch (e) {
      _setError(true, e.toString());
    } finally {
      _setLoading(false);
    }
  }

  // تغيير الفئة والتحميل
  Future<void> changeCategory(String category) async {
    if (_currentCategory == category) return;

    _currentCategory = category;
    await loadReels(refresh: true);
  }

  // تغيير مستوى الصعوبة
  Future<void> changeDifficulty(String difficulty) async {
    if (_currentDifficulty == difficulty) return;

    _currentDifficulty = difficulty;
    await loadReels(refresh: true);
  }

  // تغيير طريقة الترتيب
  Future<void> changeSortBy(String sortBy) async {
    if (_sortBy == sortBy) return;

    _sortBy = sortBy;
    await loadReels(refresh: true);
  }

  // التفاعل مع فيديو (إعجاب)
  Future<void> toggleLike(String reelId) async {
    final index = _reels.indexWhere((reel) => reel.id == reelId);
    if (index == -1) return;

    try {
      // تحديث محلي فوري
      final reel = _reels[index];
      final newReel = reel.copyWith(
        isLiked: !reel.isLiked,
        likes: reel.isLiked ? reel.likes - 1 : reel.likes + 1,
      );
      _reels[index] = newReel;
      notifyListeners();

      // إرسال إلى الباك إند
      await ReelService.reactToReel(reelId, 'like');
    } catch (e) {
      // إعادة التحديث في حالة الخطأ
      final reel = _reels[index];
      final revertReel = reel.copyWith(
        isLiked: !reel.isLiked,
        likes: reel.isLiked ? reel.likes - 1 : reel.likes + 1,
      );
      _reels[index] = revertReel;
      notifyListeners();
      _setError(true, 'فشل في الإعجاب: $e');
    }
  }

  // عدم الإعجاب
  Future<void> toggleDislike(String reelId) async {
    final index = _reels.indexWhere((reel) => reel.id == reelId);
    if (index == -1) return;

    try {
      final reel = _reels[index];
      final newReel = reel.copyWith(
        isDisliked: !reel.isDisliked,
        dislikes: reel.isDisliked ? reel.dislikes - 1 : reel.dislikes + 1,
      );
      _reels[index] = newReel;
      notifyListeners();

      await ReelService.reactToReel(reelId, 'dislike');
    } catch (e) {
      final reel = _reels[index];
      final revertReel = reel.copyWith(
        isDisliked: !reel.isDisliked,
        dislikes: reel.isDisliked ? reel.dislikes - 1 : reel.dislikes + 1,
      );
      _reels[index] = revertReel;
      notifyListeners();
      _setError(true, 'فشل في عدم الإعجاب: $e');
    }
  }

  // حفظ فيديو
  Future<void> toggleSave(String reelId) async {
    final index = _reels.indexWhere((reel) => reel.id == reelId);
    if (index == -1) return;

    try {
      final reel = _reels[index];
      final newReel = reel.copyWith(isSaved: !reel.isSaved);
      _reels[index] = newReel;
      notifyListeners();

      await ReelService.toggleSave(reelId);
    } catch (e) {
      final reel = _reels[index];
      final revertReel = reel.copyWith(isSaved: !reel.isSaved);
      _reels[index] = revertReel;
      notifyListeners();
      _setError(true, 'فشل في حفظ الفيديو: $e');
    }
  }

  // متابعة مستخدم
  Future<void> toggleFollow(String userId) async {
    try {
      await ReelService.toggleFollow(userId);

      // تحديث حالة المتابعة في جميع الفيديوهات للمستخدم
      for (int i = 0; i < _reels.length; i++) {
        if (_reels[i].userId == userId) {
          _reels[i] = _reels[i].copyWith(isFollowing: !_reels[i].isFollowing);
        }
      }
      notifyListeners();
    } catch (e) {
      _setError(true, 'فشل في المتابعة: $e');
    }
  }

  // إضافة تعليق
  Future<void> addComment(String reelId, String content) async {
    try {
      final newComment = await ReelService.addComment(reelId, content);

      // إضافة التعليق الجديد للقائمة
      _comments.add(newComment);

      final index = _reels.indexWhere((reel) => reel.id == reelId);
      if (index != -1) {
        final reel = _reels[index];
        _reels[index] = reel.copyWith(
          commentsCount: reel.commentsCount + 1,
        );
        notifyListeners();
      }
    } catch (e) {
      _setError(true, 'فشل في إضافة التعليق: $e');
    }
  }

  // تحميل التعليقات
  Future<List<ReelComment>> loadComments(String reelId) async {
    try {
      return await ReelService.getComments(reelId);
    } catch (e) {
      _setError(true, 'فشل في تحميل التعليقات: $e');
      return [];
    }
  }

  // تسجيل مشاهدة
  Future<void> recordView(String reelId, {int watchTime = 0}) async {
    try {
      await ReelService.recordView(reelId, watchTime: watchTime);

      final index = _reels.indexWhere((reel) => reel.id == reelId);
      if (index != -1) {
        final reel = _reels[index];
        _reels[index] = reel.copyWith(
          views: reel.views + 1,
          hasWatched: true,
        );
        notifyListeners();
      }
    } catch (e) {
      // تسجيل المشاهدة ليس بالغ الأهمية، لا نعرض خطأ للمستخدم
      print('فشل في تسجيل المشاهدة: $e');
    }
  }

  // إرسال إجابة الكويز
  Future<Map<String, dynamic>?> submitQuizAnswer(
      String reelId, String answer) async {
    try {
      return await ReelService.submitQuizAnswer(reelId, answer);
    } catch (e) {
      _setError(true, 'فشل في إرسال الإجابة: $e');
      return null;
    }
  }

  // البحث في الفيديوهات
  Future<List<ReelVideo>> searchReels(String query) async {
    try {
      return await ReelService.searchReels(
        query,
        category: _currentCategory,
        difficulty: _currentDifficulty,
      );
    } catch (e) {
      _setError(true, 'فشل في البحث: $e');
      return [];
    }
  }

  // تحميل المزيد من الفيديوهات
  Future<void> loadMoreReels() async {
    if (!_hasMoreReels || _isLoading) return;
    await loadReels();
  }

  // إعداد Socket.IO
  void _connectSocket() {
    try {
      _socket = io.io(AppState.getBackendUrl(), <String, dynamic>{
        'transports': ['websocket'],
        'autoConnect': false,
      });

      _socket?.connect();

      _socket?.on('connect', (_) {
        print('متصل بـ Socket.IO');
        _isConnected = true;
        notifyListeners();
      });

      _socket?.on('disconnect', (_) {
        print('انقطع الاتصال مع Socket.IO');
        _isConnected = false;
        notifyListeners();
      });

      _socket?.on('new_reel', (data) {
        try {
          final newReel = ReelVideo.fromJson(data);
          _reels.insert(0, newReel);
          notifyListeners();
        } catch (e) {
          print('خطأ في استقبال فيديو جديد: $e');
        }
      });

      _socket?.on('reel_updated', (data) {
        try {
          final updatedReel = ReelVideo.fromJson(data);
          final index = _reels.indexWhere((reel) => reel.id == updatedReel.id);
          if (index != -1) {
            _reels[index] = updatedReel;
            notifyListeners();
          }
        } catch (e) {
          print('خطأ في تحديث الفيديو: $e');
        }
      });

      _socket?.on('new_comment', (data) {
        try {
          final reelId = data['reelId'];
          final index = _reels.indexWhere((reel) => reel.id == reelId);
          if (index != -1) {
            final reel = _reels[index];
            _reels[index] = reel.copyWith(
              commentsCount: reel.commentsCount + 1,
            );
            notifyListeners();
          }
        } catch (e) {
          print('خطأ في استقبال تعليق جديد: $e');
        }
      });
    } catch (e) {
      print('خطأ في الاتصال بـ Socket.IO: $e');
      _isConnected = false;
    }
  }

  // دوال مساعدة
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(bool hasError, String message) {
    _hasError = hasError;
    _errorMessage = message;
    notifyListeners();
  }

  @override
  void dispose() {
    _socket?.disconnect();
    _socket?.dispose();
    super.dispose();
  }
}
