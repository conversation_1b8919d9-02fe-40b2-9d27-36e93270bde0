import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'package:provider/provider.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:fluttericon/font_awesome5_icons.dart';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'appstate.dart';
import 'ride_request.dart';
enum UserType { passenger, driver }

class RideServicePage extends StatefulWidget {
  const RideServicePage({super.key});

  @override
  _RideServicePageState createState() => _RideServicePageState();
}

class _RideServicePageState extends State<RideServicePage> with SingleTickerProviderStateMixin {
  UserType? _selectedUserType;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  bool _isDarkMode = false;

  @override
  void initState() {
    super.initState();
    _loadTheme();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 600),
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.forward();
  }

  Future<void> _loadTheme() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _isDarkMode = prefs.getBool('isDarkMode') ?? false;
    });
  }

  Future<void> _toggleTheme() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _isDarkMode = !_isDarkMode;
      prefs.setBool('isDarkMode', _isDarkMode);
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Theme(
      data: _isDarkMode ? ThemeData.dark().copyWith(
        primaryColor: Colors.blue.shade900,
        scaffoldBackgroundColor: Colors.grey.shade900,
        cardColor: Colors.grey.shade800,
      ) : ThemeData.light().copyWith(
        primaryColor: Colors.blue.shade700,
        scaffoldBackgroundColor: Colors.white,
        cardColor: Colors.white,
      ),
      child: Scaffold(
        appBar: AppBar(
          title: const Text(
            'خدمة التوصيل المجاني والمخفض',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
          backgroundColor: _isDarkMode ? Colors.blue.shade900 : Colors.blue.shade700,
          elevation: 0,
          actions: [
            IconButton(
              icon: _isDarkMode ? const Icon(FontAwesome5.sun) : const Icon(FontAwesome5.moon),
              onPressed: _toggleTheme,
            ),
          ],
        ),
        body: FadeTransition(
          opacity: _fadeAnimation,
          child: _selectedUserType == null
              ? _buildSelectionScreen(context)
              : _selectedUserType == UserType.passenger
              ? const FreeRidePage()
              : const DriverPage(),
        ),
      ),
    );
  }

  Widget _buildSelectionScreen(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: _isDarkMode
              ? [Colors.blue.shade900, Colors.blue.shade800]
              : [Colors.blue.shade700, Colors.blue.shade100],
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            'اختر نوع المستخدم',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: _isDarkMode ? Colors.white70 : Colors.white,
            ),
            textAlign: TextAlign.center,
            semanticsLabel: 'اختر نوع المستخدم',
          ),
          const SizedBox(height: 40),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildUserTypeButton(
                context,
                icon: FontAwesome5.user_alt,
                label: 'مستخدم',
                userType: UserType.passenger,
              ),
              const SizedBox(width: 20),
              _buildUserTypeButton(
                context,
                icon: FontAwesome5.car,
                label: 'سائق',
                userType: UserType.driver,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildUserTypeButton(
      BuildContext context, {
        required IconData icon,
        required String label,
        required UserType userType,
      }) {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        setState(() {
          _selectedUserType = userType;
        });
      },
      child: AnimatedScale(
        scale: _selectedUserType == userType ? 1.1 : 1.0,
        duration: const Duration(milliseconds: 200),
        child: Container(
          width: 140,
          height: 140,
          decoration: BoxDecoration(
            color: _isDarkMode ? Colors.grey.shade800 : Colors.white,
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: _isDarkMode ? Colors.black54 : Colors.black.withOpacity(0.2),
                blurRadius: 10,
                offset: const Offset(0, 5),
              ),
            ],
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 50,
                color: _isDarkMode ? Colors.blue.shade300 : Colors.blue.shade700,
                semanticLabel: label,
              ),
              const SizedBox(height: 10),
              Text(
                label,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: _isDarkMode ? Colors.blue.shade300 : Colors.blue.shade700,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class FreeRidePage extends StatefulWidget {
  const FreeRidePage({super.key});

  @override
  _FreeRidePageState createState() => _FreeRidePageState();
}

class _FreeRidePageState extends State<FreeRidePage> with SingleTickerProviderStateMixin {
  final MapController _mapController = MapController();
  final TextEditingController _destinationController = TextEditingController();
  final TextEditingController _userIdController = TextEditingController();
  LatLng? _pickupLocation;
  LatLng? _destinationLocation;
  bool _isLoading = false;
  String _selectedRequestType = 'normal';
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  List<String> _destinationSuggestions = [];
  String _historyFilter = 'all';

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 600),
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.forward();
    _checkNetworkStatus();
  }

  Future<void> _checkNetworkStatus() async {
    // Placeholder for network status check (use package like connectivity_plus in production)
    await Future.delayed(const Duration(seconds: 1));
    if (false) { // Simulate offline mode
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('تم الكشف عن وضع عدم الاتصال، بعض الميزات قد تكون محدودة')),
      );
    }
  }

  @override
  void dispose() {
    _mapController.dispose();
    _destinationController.dispose();
    _userIdController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _fetchDestinationSuggestions(String query) async {
    if (query.isEmpty) {
      setState(() => _destinationSuggestions = []);
      return;
    }
    // Mock geocoding service (replace with Google Places API in production)
    final suggestions = [
      'دمشق - شارع الحمراء',
      'دمشق - المزة',
      'دمشق - باب توما',
      'دمشق - المالكي',
      'دمشق - البرامكة',
    ].where((s) => s.toLowerCase().contains(query.toLowerCase())).toList();
    setState(() {
      _destinationSuggestions = suggestions;
    });
  }

  void _requestRide(BuildContext context) async {
    final appState = Provider.of<AppState>(context, listen: false);

    if (_pickupLocation == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('الرجاء تحديد موقع الانطلاق'),
          backgroundColor: Colors.red.shade600,
        ),
      );
      return;
    }

    if (_destinationController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('الرجاء إدخال وجهة الرحلة'),
          backgroundColor: Colors.red.shade600,
        ),
      );
      return;
    }

    if (!(appState.isEligible ?? false)) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('الرجاء التحقق من أهليتك للرحلة'),
          backgroundColor: Colors.red.shade600,
        ),
      );
      return;
    }

    setState(() => _isLoading = true);

    try {
      await Future.delayed(const Duration(seconds: 1));
      appState.addRideRequest(
        pickup: _pickupLocation!,
        destination: _destinationController.text,
        requestType: _selectedRequestType,
      );
      HapticFeedback.vibrate();
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('تم إرسال طلب الرحلة بنجاح'),
          backgroundColor: Colors.green.shade600,
        ),
      );
      // Simulate push notification
      print('Push Notification: Ride request sent for ${_destinationController.text}');
      _destinationController.clear();
      setState(() => _destinationSuggestions = []);
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('حدث خطأ: ${e.toString()}'),
          backgroundColor: Colors.red.shade600,
        ),
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _cancelRide(BuildContext context, String requestId) {
    final appState = Provider.of<AppState>(context, listen: false);
    appState.cancelRideRequest(requestId);
    HapticFeedback.vibrate();
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم إلغاء الطلب بنجاح')),
    );
  }

  void _verifyEligibility(BuildContext context) async {
    if (_userIdController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('الرجاء إدخال معرف المستخدم')),
      );
      return;
    }

    setState(() => _isLoading = true);

    try {
      final appState = Provider.of<AppState>(context, listen: false);
      await Future.delayed(const Duration(seconds: 1));
      appState.verifyEligibility(_userIdController.text);
      HapticFeedback.vibrate();
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(appState.isEligible
              ? 'تم التحقق من الأهلية بنجاح'
              : 'عذراً، أنت غير مؤهل لطلب الرحلة'),
          backgroundColor: appState.isEligible ? Colors.green.shade600 : Colors.red.shade600,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('حدث خطأ: ${e.toString()}'),
          backgroundColor: Colors.red.shade600,
        ),
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _showRideDetails(BuildContext context, RideRequest request) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'تفاصيل الطلب',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
              semanticsLabel: 'تفاصيل الطلب',
            ),
            const SizedBox(height: 16),
            Text('الوجهة: ${request.destination}'),
            Text('الحالة: ${request.status}'),
            Text('نوع الطلب: ${request.requestType}'),
            Text('وقت الطلب: ${request.timestamp.toString().substring(0, 16)}'),
            Text('موقع الانطلاق: (${request.pickup.latitude.toStringAsFixed(4)}, ${request.pickup.longitude.toStringAsFixed(4)})'),
            if (request.status == 'Pending' || request.status == 'IECAccepted') ...[
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  _cancelRide(context, request.id);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red.shade600,
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                  minimumSize: const Size(double.infinity, 50),
                ),
                child: const Text('إلغاء الطلب', style: TextStyle(fontSize: 16)),
              ),
            ],
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final appState = Provider.of<AppState>(context);
    _pickupLocation = appState.currentLocation ?? const LatLng(33.5138, 36.2765);
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: Stack(
          children: [
            Column(
              children: [
                Expanded(
                  flex: 3,
                  child: AnimatedSwitcher(
                    duration: const Duration(milliseconds: 300),
                    child: FlutterMap(
                      key: ValueKey(_pickupLocation),
                      mapController: _mapController,
                      options: MapOptions(
                        center: _pickupLocation!,
                        zoom: 13.0,
                        onMapReady: () {
                          _mapController.move(_pickupLocation!, 13.0);
                        },
                      ),
                      children: [
                        TileLayer(
                          urlTemplate: isDarkMode
                              ? "https://cartodb-basemaps-{s}.global.ssl.fastly.net/dark_all/{z}/{x}/{y}.png"
                              : "https://tile.openstreetmap.org/{z}/{x}/{y}.png",
                          userAgentPackageName: 'com.example.app',
                        ),
                        MarkerLayer(
                          markers: [
                            Marker(
                              width: 60.0,
                              height: 60.0,
                              point: _pickupLocation!,
                              child: const Icon(Icons.location_pin, color: Colors.blue, size: 40),
                            ),
                            if (_destinationLocation != null)
                              Marker(
                                width: 60.0,
                                height: 60.0,
                                point: _destinationLocation!,
                                child: const Icon(Icons.location_on, color: Colors.red, size: 40),
                              ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Container(
                    decoration: BoxDecoration(
                      color: Theme.of(context).scaffoldBackgroundColor,
                      borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 10,
                          offset: const Offset(0, -5),
                        ),
                      ],
                    ),
                    child: SingleChildScrollView(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          if (!appState.isEligible) ...[
                            TextField(
                              controller: _userIdController,
                              decoration: InputDecoration(
                                labelText: 'رقم الهوية',
                                hintText: 'أدخل رقم الهوية (10 أرقام)',
                                border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                                prefixIcon: const Icon(FontAwesome5.id_card),
                                filled: true,
                                fillColor: Theme.of(context).cardColor,
                              ),
                              keyboardType: TextInputType.number,
                            ),
                            const SizedBox(height: 16.0),
                            ElevatedButton(
                              onPressed: () => _verifyEligibility(context),
                              style: ElevatedButton.styleFrom(
                                minimumSize: const Size(double.infinity, 50),
                                backgroundColor: isDarkMode ? Colors.blue.shade800 : Colors.blue.shade700,
                                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                                elevation: 5,
                              ),
                              child: const Text('التحقق من الأهلية', style: TextStyle(fontSize: 16)),
                            ),
                            const SizedBox(height: 16.0),
                          ],
                          TextField(
                            readOnly: true,
                            decoration: InputDecoration(
                              labelText: 'موقع الانطلاق',
                              hintText: _pickupLocation != null
                                  ? 'الموقع الحالي (${_pickupLocation!.latitude.toStringAsFixed(4)}, ${_pickupLocation!.longitude.toStringAsFixed(4)})'
                                  : 'جارٍ تحديد الموقع...',
                              border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                              prefixIcon: const Icon(FontAwesome5.map_marker_alt),
                              filled: true,
                              fillColor: Theme.of(context).cardColor,
                            ),
                          ),
                          const SizedBox(height: 16.0),
                          TextField(
                            controller: _destinationController,
                            decoration: InputDecoration(
                              hintText: 'أدخل الوجهة',
                              border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                              filled: true,
                              fillColor: Theme.of(context).cardColor,
                              prefixIcon: const Icon(FontAwesome5.map_marker_alt),
                            ),
                            onChanged: _fetchDestinationSuggestions,
                          ),
                          if (_destinationSuggestions.isNotEmpty)
                            Container(
                              height: 150,
                              margin: const EdgeInsets.only(top: 8.0),
                              decoration: BoxDecoration(
                                color: Theme.of(context).cardColor,
                                borderRadius: BorderRadius.circular(12),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.1),
                                    blurRadius: 5,
                                    offset: const Offset(0, 3),
                                  ),
                                ],
                              ),
                              child: ListView.builder(
                                itemCount: _destinationSuggestions.length,
                                itemBuilder: (context, index) {
                                  return ListTile(
                                    title: Text(_destinationSuggestions[index]),
                                    onTap: () {
                                      setState(() {
                                        _destinationController.text = _destinationSuggestions[index];
                                        _destinationLocation = LatLng(33.5138 + index * 0.01, 36.2765 + index * 0.01);
                                        _destinationSuggestions = [];
                                      });
                                      _mapController.move(_destinationLocation!, 13.0);
                                    },
                                  );
                                },
                              ),
                            ),
                          const SizedBox(height: 16.0),
                          const Text(
                            'نوع الطلب',
                            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                            semanticsLabel: 'نوع الطلب',
                          ),
                          const SizedBox(height: 8.0),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                            children: [
                              _buildRequestTypeButton(
                                icon: FontAwesome5.car,
                                label: 'مجاني',
                                type: 'free',
                              ),
                              _buildRequestTypeButton(
                                icon: FontAwesome5.route,
                                label: 'مخفض',
                                type: 'discounted',
                              ),
                              _buildRequestTypeButton(
                                icon: FontAwesome5.car_crash,
                                label: 'مساعدة',
                                type: 'roadside_assistance',
                              ),
                            ],
                          ),
                          const SizedBox(height: 16.0),
                          ElevatedButton(
                            onPressed: appState.isEligible ? () => _requestRide(context) : null,
                            style: ElevatedButton.styleFrom(
                              minimumSize: const Size(double.infinity, 50),
                              backgroundColor: Colors.green.shade600,
                              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                              elevation: 5,
                            ),
                            child: const Text('طلب التوصيل', style: TextStyle(fontSize: 16)),
                          ),
                          const SizedBox(height: 16.0),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              const Text(
                                'سجل الطلبات',
                                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                                semanticsLabel: 'سجل الطلبات',
                              ),
                              DropdownButton<String>(
                                value: _historyFilter,
                                items: const [
                                  DropdownMenuItem(value: 'all', child: Text('الكل')),
                                  DropdownMenuItem(value: 'pending', child: Text('قيد الانتظار')),
                                  DropdownMenuItem(value: 'accepted', child: Text('مقبول')),
                                  DropdownMenuItem(value: 'in_progress', child: Text('جارية')),
                                  DropdownMenuItem(value: 'completed', child: Text('مكتمل')),
                                  DropdownMenuItem(value: 'cancelled', child: Text('ملغى')),
                                ],
                                onChanged: (value) {
                                  setState(() {
                                    _historyFilter = value!;
                                  });
                                },
                              ),
                            ],
                          ),
                          const SizedBox(height: 8.0),
                          appState.rideRequests.isEmpty
                              ? const Text('لا توجد طلبات')
                              : ListView.builder(
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                            itemCount: appState.rideRequests.length,
                            itemBuilder: (context, index) {
                              final request = appState.rideRequests[index];
                              if (_historyFilter != 'all' && request.status.toLowerCase() != _historyFilter) {
                                return const SizedBox.shrink();
                              }
                              return GestureDetector(
                                onTap: () => _showRideDetails(context, request),
                                child: Card(
                                  margin: const EdgeInsets.symmetric(vertical: 4.0),
                                  elevation: 5,
                                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                                  child: ListTile(
                                    leading: Icon(
                                      request.status == 'Pending'
                                          ? FontAwesome5.hourglass_half
                                          : request.status == 'Accepted'
                                          ? FontAwesome5.check_circle
                                          : request.status == 'In Progress'
                                          ? FontAwesome5.car_side
                                          : FontAwesome5.times_circle,
                                      color: Colors.blue.shade700,
                                    ),
                                    title: Text(request.destination),
                                    subtitle: Text(
                                      '${request.timestamp.toString().substring(0, 16)} - ${request.status} - نوع: ${request.requestType}',
                                    ),
                                  ),
                                ),
                              );
                            },
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
            if (_isLoading)
              Center(
                child: SpinKitFadingCube(
                  color: isDarkMode ? Colors.blue.shade300 : Colors.blue.shade700,
                  size: 50.0,
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildRequestTypeButton({
    required IconData icon,
    required String label,
    required String type,
  }) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        setState(() {
          _selectedRequestType = type;
        });
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.all(12.0),
        decoration: BoxDecoration(
          color: _selectedRequestType == type
              ? (isDarkMode ? Colors.blue.shade800 : Colors.blue.shade100)
              : (isDarkMode ? Colors.grey.shade800 : Colors.white),
          border: Border.all(
            color: _selectedRequestType == type ? Colors.blue.shade700 : Colors.grey,
          ),
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: isDarkMode ? Colors.black54 : Colors.black.withOpacity(0.1),
              blurRadius: 5,
              offset: const Offset(0, 3),
            ),
          ],
        ),
        child: Column(
          children: [
            Icon(
              icon,
              size: 30,
              color: isDarkMode ? Colors.blue.shade300 : Colors.blue.shade700,
              semanticLabel: label,
            ),
            const SizedBox(height: 4.0),
            Text(
              label,
              style: TextStyle(
                fontSize: 14,
                color: isDarkMode ? Colors.blue.shade300 : Colors.blue.shade700,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class DriverPage extends StatefulWidget {
  const DriverPage({super.key});

  @override
  _DriverPageState createState() => _DriverPageState();
}

class _DriverPageState extends State<DriverPage> with SingleTickerProviderStateMixin {
  final MapController _mapController = MapController();
  final TextEditingController _driverNameController = TextEditingController();
  final TextEditingController _vehicleInfoController = TextEditingController();
  bool _isLoading = false;
  bool _showProfile = false;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  LatLng _currentLocation = const LatLng(33.5138, 36.2765);
  late AppState _appState;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 600),
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.forward();
    _startLocationUpdates();
  }

  void _startLocationUpdates() {
    Future.doWhile(() async {
      await Future.delayed(const Duration(seconds: 5));
      if (mounted) {
        setState(() {
          _currentLocation = LatLng(
            _currentLocation.latitude + 0.0001,
            _currentLocation.longitude + 0.0001,
          );
          _mapController.move(_currentLocation, 13.0);
        });
      }
      return mounted;
    });
  }

  @override
  void dispose() {
    _mapController.dispose();
    _driverNameController.dispose();
    _vehicleInfoController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  void _acceptRequest(BuildContext context, RideRequest request) async {
    if (_appState.currentDriver == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('الرجاء تسجيل الدخول كسائق أولاً')),
      );
      setState(() => _showProfile = true);
      return;
    }

    setState(() => _isLoading = true);
    try {
      await Future.delayed(const Duration(seconds: 1));
      _appState.acceptRideRequest(request.id, _appState.currentDriver!.id);
      _appState.addDriverPoints(_appState.currentDriver!.id, request.requestType == 'roadside_assistance' ? 20 : 10);
      HapticFeedback.vibrate();
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('تم قبول الطلب بنجاح'),
          backgroundColor: Colors.green.shade600,
        ),
      );
      // Simulate push notification
      print('Push Notification: Ride request accepted for ${request.destination}');
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('حدث خطأ: ${e.toString()}'),
          backgroundColor: Colors.red.shade600,
        ),
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _updateRideStatus(BuildContext context, String requestId, String status) async {
    setState(() => _isLoading = true);
    try {
      await Future.delayed(const Duration(seconds: 1));
      _appState.updateRideStatus(requestId, status);
      if (status == 'Completed') {
        final driverId = _appState.currentDriver!.id;
        final completedRides = (_appState.driverStats[driverId]?['completedRides'] ?? 0) + 1;
        final rating = (_appState.driverStats[driverId]?['rating'] ?? 0.0) + 4.5;
        _appState.updateDriverStats(driverId, completedRides, rating / completedRides);
        if (_appState.checkDailyGoal(driverId, completedRides)) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('تهانينا! لقد حققت الهدف اليومي وحصلت على 50 نقطة إضافية')),
          );
        }
      }
      HapticFeedback.vibrate();
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('تم تحديث حالة الطلب إلى $status'),
          backgroundColor: Colors.green.shade600,
        ),
      );
      // Simulate push notification
      print('Push Notification: Ride status updated to $status');
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('حدث خطأ: ${e.toString()}'),
          backgroundColor: Colors.red.shade600,
        ),
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _loginAsDriver(BuildContext context) {
    if (_driverNameController.text.isNotEmpty && _vehicleInfoController.text.isNotEmpty) {
      _appState.loginAsDriver(_driverNameController.text, _vehicleInfoController.text);
      setState(() => _showProfile = false);
      HapticFeedback.vibrate();
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('تم تسجيل الدخول كسائق')),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى إدخال جميع البيانات')),
      );
    }
  }

  void _showRideDetails(BuildContext context, RideRequest request) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'تفاصيل الطلب',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
              semanticsLabel: 'تفاصيل الطلب',
            ),
            const SizedBox(height: 16),
            Text('الوجهة: ${request.destination}'),
            Text('الحالة: ${request.status}'),
            Text('نوع الطلب: ${request.requestType}'),
            Text('وقت الطلب: ${request.timestamp.toString().substring(0, 16)}'),
            Text('موقع الانطلاق: (${request.pickup.latitude.toStringAsFixed(4)}, ${request.pickup.longitude.toStringAsFixed(4)})'),
            if (request.status == 'Pending' || request.status == 'Accepted') ...[
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  _updateRideStatus(context, request.id, 'In Progress');
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green.shade600,
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                  minimumSize: const Size(double.infinity, 50),
                ),
                child: const Text('بدء الرحلة', style: TextStyle(fontSize: 16)),
              ),
            ],
            if (request.status == 'In Progress') ...[
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  _updateRideStatus(context, request.id, 'Completed');
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green.shade600,
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                  minimumSize: const Size(double.infinity, 50),
                ),
                child: const Text('إنهاء الرحلة', style: TextStyle(fontSize: 16)),
              ),
            ],
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                showDialog(
                  context: context,
                  builder: (context) => AlertDialog(
                    title: const Text('تقييم الرحلة'),
                    content: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Text('قم بتقييم تجربتك:'),
                        const SizedBox(height: 8),
                        DropdownButton<double>(
                          value: 5.0,
                          items: [1.0, 2.0, 3.0, 4.0, 5.0]
                              .map((rating) => DropdownMenuItem(
                            value: rating,
                            child: Text('$rating نجوم'),
                          ))
                              .toList(),
                          onChanged: (value) {
                            // Simulate rating submission
                            print('Ride rated: $value stars');
                            Navigator.pop(context);
                          },
                        ),
                      ],
                    ),
                  ),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue.shade700,
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                minimumSize: const Size(double.infinity, 50),
              ),
              child: const Text('تقييم الرحلة', style: TextStyle(fontSize: 16)),
            ),
          ],
        ),
      ),
    );
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _appState = Provider.of<AppState>(context);
    if (_appState.currentDriver != null) {
      _showProfile = false;
    }
    setState(() {});
  }

  Widget _buildDriverProfile(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'إعدادات السائق',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          semanticsLabel: 'إعدادات السائق',
        ),
        const SizedBox(height: 16.0),
        TextField(
          controller: _driverNameController,
          decoration: InputDecoration(
            labelText: 'اسم السائق',
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
            prefixIcon: const Icon(FontAwesome5.user),
            filled: true,
            fillColor: Theme.of(context).cardColor,
          ),
        ),
        const SizedBox(height: 16.0),
        TextField(
          controller: _vehicleInfoController,
          decoration: InputDecoration(
            labelText: 'معلومات المركبة',
            hintText: 'مثال: تويوتا كامري 2020',
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
            prefixIcon: const Icon(FontAwesome5.car),
            filled: true,
            fillColor: Theme.of(context).cardColor,
          ),
        ),
        const SizedBox(height: 16.0),
        Row(
          children: [
            const Text('متاح: ', style: TextStyle(fontSize: 16)),
            Switch(
              value: _appState.currentDriver?.isAvailable ?? true,
              onChanged: (value) {
                _appState.updateDriverAvailability(value);
                HapticFeedback.lightImpact();
              },
              activeColor: Colors.green.shade600,
            ),
          ],
        ),
        const SizedBox(height: 16.0),
        ElevatedButton(
          onPressed: () => _loginAsDriver(context),
          style: ElevatedButton.styleFrom(
            minimumSize: const Size(double.infinity, 50),
            backgroundColor: Colors.green.shade600,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            elevation: 5,
          ),
          child: const Text('حفظ وتسجيل الدخول', style: TextStyle(fontSize: 16)),
        ),
      ],
    );
  }

  Widget _buildDriverRequests(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    final availableRequests = _appState.rideRequests
        .where((r) => r.status == 'Pending' &&
        (_appState.currentDriver?.isAvailable ?? false))
        .toList();

    final activeRequests = _appState.rideRequests
        .where((r) => r.driverId == _appState.currentDriver?.id &&
        r.status != 'Completed' &&
        r.status != 'Cancelled')
        .toList();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Card(
          margin: const EdgeInsets.symmetric(vertical: 8.0),
          elevation: 5,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'إحصائيات الأداء',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  semanticsLabel: 'إحصائيات الأداء',
                ),
                const SizedBox(height: 8),
                Text('النقاط: ${_appState.driverPoints[_appState.currentDriver?.id] ?? 0}'),
                Text('الرحلات المكتملة: ${_appState.driverStats[_appState.currentDriver?.id]?['completedRides'] ?? 0}'),
                Text('التقييم: ${_appState.driverStats[_appState.currentDriver?.id]?['rating']?.toStringAsFixed(1) ?? 'غير متوفر'}'),
                Text('الإيرادات: ${_appState.driverStats[_appState.currentDriver?.id]?['earnings']?.toStringAsFixed(2) ?? '0.00'}\$'),
              ],
            ),
          ),
        ),
        Card(
          margin: const EdgeInsets.symmetric(vertical: 8.0),
          elevation: 5,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          child: ExpansionTile(
            title: const Text('لوحة المتصدرين', style: TextStyle(fontWeight: FontWeight.bold)),
            children: _appState.leaderboard.asMap().entries.map((entry) {
              final index = entry.key;
              final leader = entry.value;
              return ListTile(
                leading: Text('#${index + 1}', style: const TextStyle(fontWeight: FontWeight.bold)),
                title: Text(leader['name']),
                subtitle: Text('النقاط: ${leader['points']} - التقييم: ${leader['rating'].toStringAsFixed(1)}'),
              );
            }).toList(),
          ),
        ),
        const SizedBox(height: 16.0),
        const Text(
          'الطلبات الواردة والنشطة',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          semanticsLabel: 'الطلبات الواردة والنشطة',
        ),
        const SizedBox(height: 8.0),
        if (_appState.currentDriver == null)
          Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Text('الرجاء تسجيل الدخول كسائق أولاً'),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () => setState(() => _showProfile = true),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue.shade700,
                    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                    elevation: 5,
                  ),
                  child: const Text('تسجيل الدخول كسائق'),
                ),
              ],
            ),
          )
        else if (availableRequests.isEmpty && activeRequests.isEmpty)
          const Text('لا توجد طلبات حاليًا')
        else ...[
            if (activeRequests.isNotEmpty) ...[
              const Text(
                'الطلبات النشطة',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
              ),
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: activeRequests.length,
                itemBuilder: (context, index) {
                  final request = activeRequests[index];
                  return GestureDetector(
                    onTap: () => _showRideDetails(context, request),
                    child: Card(
                      margin: const EdgeInsets.symmetric(vertical: 4.0),
                      elevation: 5,
                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                      child: ListTile(
                        leading: Icon(
                          request.requestType == 'roadside_assistance'
                              ? FontAwesome5.car_crash
                              : FontAwesome5.car_side,
                          color: isDarkMode ? Colors.blue.shade300 : Colors.blue.shade700,
                        ),
                        title: Text('الوجهة: ${request.destination}'),
                        subtitle: Text('الحالة: ${request.status} - نوع: ${request.requestType}'),
                        trailing: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            if (request.status == 'Accepted')
                              ElevatedButton(
                                onPressed: () => _updateRideStatus(context, request.id, 'In Progress'),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.green.shade600,
                                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                                ),
                                child: const Text('بدء الرحلة'),
                              ),
                            if (request.status == 'In Progress')
                              ElevatedButton(
                                onPressed: () => _updateRideStatus(context, request.id, 'Completed'),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.green.shade600,
                                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                                ),
                                child: const Text('إنهاء الرحلة'),
                              ),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              ),
              const SizedBox(height: 16.0),
            ],
            if (availableRequests.isNotEmpty) ...[
              const Text(
                'طلبات جديدة',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                semanticsLabel: 'طلبات جديدة',
              ),
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: availableRequests.length,
                itemBuilder: (context, index) {
                  final request = availableRequests[index];
                  return GestureDetector(
                    onTap: () => _showRideDetails(context, request),
                    child: Card(
                      margin: const EdgeInsets.symmetric(vertical: 4.0),
                      elevation: 5,
                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                      color: request.requestType == 'roadside_assistance'
                          ? (isDarkMode ? Colors.red.shade900 : Colors.red.shade100)
                          : Theme.of(context).cardColor,
                      child: ListTile(
                        leading: Icon(
                          request.requestType == 'roadside_assistance'
                              ? FontAwesome5.car_crash
                              : FontAwesome5.map_marker_alt,
                          color: isDarkMode ? Colors.blue.shade300 : Colors.blue.shade700,
                        ),
                        title: Text('الوجهة: ${request.destination}'),
                        subtitle: Text(
                          request.pickup != null
                              ? 'الموقع: (${request.pickup.latitude.toStringAsFixed(4)}, ${request.pickup.longitude.toStringAsFixed(4)}) - نوع: ${request.requestType}'
                              : 'الموقع: غير محدد - نوع: ${request.requestType}',
                        ),
                        trailing: ElevatedButton(
                          onPressed: () => _acceptRequest(context, request),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: request.requestType == 'roadside_assistance'
                                ? Colors.red.shade600
                                : Colors.green.shade600,
                            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                          ),
                          child: Text(request.requestType == 'roadside_assistance' ? 'قبول عاجل' : 'قبول'),
                        ),
                      ),
                    ),
                  );
                },
              ),
            ],
          ],
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    if (_isLoading) {
      return Scaffold(
        body: Center(
          child: SpinKitFadingCube(
            color: isDarkMode ? Colors.blue.shade300 : Colors.blue.shade700,
            size: 50.0,
          ),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('واجهة السائق', style: TextStyle(fontWeight: FontWeight.bold)),
        backgroundColor: isDarkMode ? Colors.blue.shade900 : Colors.blue.shade700,
        elevation: 0,
        automaticallyImplyLeading: false,
        actions: [
          IconButton(
            icon: const Icon(FontAwesome5.user_cog),
            onPressed: () => setState(() => _showProfile = !_showProfile),
          ),
        ],
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: Stack(
          children: [
            Column(
              children: [
                Expanded(
                  flex: 3,
                  child: AnimatedSwitcher(
                    duration: const Duration(milliseconds: 300),
                    child: FlutterMap(
                      key: ValueKey('map_${_currentLocation.latitude}_${_currentLocation.longitude}'),
                      mapController: _mapController,
                      options: MapOptions(
                        center: _currentLocation,
                        zoom: 13.0,
                        onMapReady: () {
                          _mapController.move(_currentLocation, 13.0);
                        },
                      ),
                      children: [
                        TileLayer(
                          urlTemplate: isDarkMode
                              ? "https://cartodb-basemaps-{s}.global.ssl.fastly.net/dark_all/{z}/{x}/{y}.png"
                              : "https://tile.openstreetmap.org/{z}/{x}/{y}.png",
                          userAgentPackageName: 'com.example.app',
                        ),
                        MarkerLayer(
                          markers: [
                            Marker(
                              width: 60.0,
                              height: 60.0,
                              point: _currentLocation,
                              child: Icon(
                                FontAwesome5.car,
                                color: isDarkMode ? Colors.blue.shade300 : Colors.blue.shade700,
                                size: 40,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Container(
                    decoration: BoxDecoration(
                      color: Theme.of(context).scaffoldBackgroundColor,
                      borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 10,
                          offset: const Offset(0, -5),
                        ),
                      ],
                    ),
                    child: SingleChildScrollView(
                      padding: const EdgeInsets.all(16.0),
                      child: AnimatedSwitcher(
                        duration: const Duration(milliseconds: 300),
                        child: _showProfile
                            ? _buildDriverProfile(context)
                            : _buildDriverRequests(context),
                      ),
                    ),
                  ),
                ),
              ],
            ),
            if (_isLoading)
              Center(
                child: SpinKitFadingCube(
                  color: isDarkMode ? Colors.blue.shade300 : Colors.blue.shade700,
                  size: 50.0,
                ),
              ),
          ],
        ),
      ),
    );
  }
}
