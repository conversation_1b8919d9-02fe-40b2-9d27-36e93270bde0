import '../../models/notification_model.dart';
import '../../services/api_service.dart';

class NotificationRepository {
  final ApiService _apiService = ApiService();
  final String baseUrl = 'https://api.example.com'; // Replace with actual API base URL

  Future<List<NotificationModel>> getNotifications(String token) async {
    try {
      final response = await _apiService.get('$baseUrl/notifications', 
        headers: {'Authorization': 'Bearer $token'}, 
        parser: (data) {
          final List<dynamic> notificationsData = data['data'] ?? [];
          return notificationsData.map((json) => NotificationModel.fromJson(json)).toList();
        }
      );
      
      if (response.isSuccess) {
        return response.data ?? [];
      } else {
        throw Exception('Failed to fetch notifications: ${response.message}');
      }
    } catch (e) {
      print('Error fetching notifications: $e');
      return [];
    }
  }
}