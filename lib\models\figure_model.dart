class Figure {
  final String title;
  final String url;
  final String type;
  final String category;

  Figure({
    required this.title,
    required this.url,
    required this.type,
    required this.category,
  });

  factory Figure.fromJson(Map<String, dynamic> json) {
    return Figure(
      title: json['title'] ?? 'بدون عنوان',
      url: json['url'] ?? '',
      type: json['type'] ?? 'فيديو',
      category: json['category'] ?? '',
    );
  }
}
