import 'package:uuid/uuid.dart';
import 'comment_model.dart';

enum PostVisibility {
  public('عام'),
  friends('الأصدقاء فقط'),
  specificFriends('أصدقاء محددين'),
  onlyMe('أنا فقط'),
  custom('مخصص');

  final String label;
  const PostVisibility(this.label);

  static PostVisibility fromString(String value) {
    return PostVisibility.values.firstWhere(
      (e) => e.name == value,
      orElse: () => PostVisibility.public,
    );
  }
}

class Post {
  // Core fields
  final String id;
  final String userId;
  String content;
  final String? mediaUrl;
  final String? mediaType;
  final DateTime createdAt;
  final List<dynamic> replies;
  
  // Reactions
  final int likes;
  final int loves;
  final int haha;
  bool isLiked;
  bool isSaved;
  
  // User info
  final String? username;
  final String? handle;
  final String? avatar;
  
  // Additional fields
  final List<Comment> comments;
  final List<String> hashtags;
  final bool promoted;
  final PostVisibility visibility;
  final List<String>? visibleToUserIds;
  final List<String>? excludedUserIds;
  final List<String>? allowCommentsFrom;
  final List<String>? allowSharesFrom;
  final bool? allowReactions;
  final bool? allowDuets;
  final bool isPinned;

  // Computed properties
  String get time {
    final now = DateTime.now();
    final difference = now.difference(createdAt);
    
    if (difference.inDays > 0) {
      return 'منذ ${difference.inDays} يوم';
    } else if (difference.inHours > 0) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inMinutes > 0) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else {
      return 'الآن';
    }
  }

  Post({
    String? id,
    required this.userId,
    required this.content,
    this.mediaUrl,
    this.mediaType,
    DateTime? createdAt,
    this.likes = 0,
    this.loves = 0,
    this.haha = 0,
    this.comments = const [],
    this.hashtags = const [],
    this.isLiked = false,
    this.isSaved = false,
    this.username,
    this.handle,
    this.avatar,
    this.promoted = false,
    this.visibility = PostVisibility.public,
    this.visibleToUserIds,
    this.excludedUserIds,
    this.allowCommentsFrom,
    this.allowSharesFrom,
    this.allowReactions = true,
    this.allowDuets = true,
    this.replies = const [],
    this.isPinned = false,
  })  : id = id ?? const Uuid().v4(),
        createdAt = createdAt ?? DateTime.now();

  factory Post.fromJson(Map<String, dynamic> json) {
    final mediaUrl = json['mediaUrl'];
    final mediaType = json['mediaType'];
    final userId = json['userId'] is Map ? json['userId']['_id'] : json['userId'];
    final user = json['user'] is Map ? json['user'] : null;
    
    return Post(
      id: json['_id']?.toString() ?? json['id']?.toString() ?? const Uuid().v4(),
      userId: userId?.toString() ?? '',
      content: json['content']?.toString() ?? '',
      mediaUrl: mediaUrl?.toString(),
      mediaType: mediaType?.toString(),
      createdAt: json['createdAt'] is String 
          ? DateTime.tryParse(json['createdAt']) ?? DateTime.now()
          : (json['createdAt'] as Map?)?['\$date'] != null
              ? DateTime.fromMillisecondsSinceEpoch(
                  int.parse(json['createdAt']['\$date'].toString()))
              : DateTime.now(),
      likes: (json['likes'] as num?)?.toInt() ?? 0,
      loves: (json['loves'] as num?)?.toInt() ?? 0,
      haha: (json['haha'] as num?)?.toInt() ?? 0,
      isLiked: json['isLiked'] == true,
      isSaved: json['isSaved'] == true,
      username: user?['name']?.toString() ?? json['username']?.toString(),
      handle: user?['username']?.toString() ?? json['handle']?.toString(),
      avatar: user?['avatar']?.toString() ?? json['avatar']?.toString(),
      comments: (json['comments'] as List<dynamic>? ?? [])
          .map((c) => Comment.fromJson(c as Map<String, dynamic>))
          .toList(),
      hashtags: json['hashtags'] is List 
          ? List<String>.from(json['hashtags'].map((e) => e.toString()))
          : [],
      promoted: json['promoted'] == true,
      visibility: json['visibility'] != null
          ? PostVisibility.fromString(json['visibility'].toString())
          : PostVisibility.public,
      visibleToUserIds: json['visibleToUserIds'] is List
          ? List<String>.from(json['visibleToUserIds'].map((e) => e.toString()))
          : null,
      excludedUserIds: json['excludedUserIds'] is List
          ? List<String>.from(json['excludedUserIds'].map((e) => e.toString()))
          : null,
      allowCommentsFrom: json['allowCommentsFrom'] is List
          ? List<String>.from(json['allowCommentsFrom'].map((e) => e.toString()))
          : null,
      allowSharesFrom: json['allowSharesFrom'] is List
          ? List<String>.from(json['allowSharesFrom'].map((e) => e.toString()))
          : null,
      allowReactions: json['allowReactions'],
      allowDuets: json['allowDuets'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'content': content,
      'mediaUrl': mediaUrl,
      'mediaType': mediaType,
      'createdAt': createdAt.toIso8601String(),
      'likes': likes,
      'loves': loves,
      'haha': haha,
      'isLiked': isLiked,
      'isSaved': isSaved,
      'username': username,
      'handle': handle,
      'avatar': avatar,
      'comments': comments.map((c) => c.toJson()).toList(),
      'hashtags': hashtags,
      'promoted': promoted,
      'visibility': visibility.name,
      if (visibleToUserIds != null) 'visibleToUserIds': visibleToUserIds,
      if (excludedUserIds != null) 'excludedUserIds': excludedUserIds,
      if (allowCommentsFrom != null) 'allowCommentsFrom': allowCommentsFrom,
      if (allowSharesFrom != null) 'allowSharesFrom': allowSharesFrom,
      'allowReactions': allowReactions,
      'allowDuets': allowDuets,
    };
  }
  
  // For backward compatibility
  String get date => createdAt.toIso8601String();
  String? get profileImagePath => avatar;
  String? get imageUrl => mediaType == 'image' ? mediaUrl : null;
  String? get videoUrl => mediaType == 'video' ? mediaUrl : null;
  
  Map<String, int> get reactions => {
        'like': likes,
        'love': loves,
        'haha': haha,
      };
      
  String? get userReaction => isLiked ? 'like' : null;
  
  // Copy with method for immutability
  Post copyWith({
    String? id,
    String? userId,
    String? content,
    String? mediaUrl,
    String? mediaType,
    DateTime? createdAt,
    int? likes,
    int? loves,
    int? haha,
    bool? isLiked,
    bool? isSaved,
    String? username,
    String? handle,
    String? avatar,
    List<Comment>? comments,
    List<String>? hashtags,
    bool? promoted,
    PostVisibility? visibility,
    List<String>? visibleToUserIds,
    List<String>? excludedUserIds,
    List<String>? allowCommentsFrom,
    List<String>? allowSharesFrom,
    bool? allowReactions,
    bool? allowDuets,
  }) {
    return Post(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      content: content ?? this.content,
      mediaUrl: mediaUrl ?? this.mediaUrl,
      mediaType: mediaType ?? this.mediaType,
      createdAt: createdAt ?? this.createdAt,
      likes: likes ?? this.likes,
      loves: loves ?? this.loves,
      haha: haha ?? this.haha,
      isLiked: isLiked ?? this.isLiked,
      isSaved: isSaved ?? this.isSaved,
      username: username ?? this.username,
      handle: handle ?? this.handle,
      avatar: avatar ?? this.avatar,
      comments: comments ?? this.comments,
      hashtags: hashtags ?? this.hashtags,
      promoted: promoted ?? this.promoted,
      visibility: visibility ?? this.visibility,
      visibleToUserIds: visibleToUserIds ?? this.visibleToUserIds,
      excludedUserIds: excludedUserIds ?? this.excludedUserIds,
      allowCommentsFrom: allowCommentsFrom ?? this.allowCommentsFrom,
      allowSharesFrom: allowSharesFrom ?? this.allowSharesFrom,
      allowReactions: allowReactions ?? this.allowReactions,
      allowDuets: allowDuets ?? this.allowDuets,
    );
  }
}
