const Video = require('../models/video.model');
const User = require('../models/User.model');
const ErrorResponse = require('../utils/errorResponse');
const asyncHandler = require('../middleware/asyncmiddleware');
const path = require('path');
const fs = require('fs');
const multer = require('multer');
const { ACTIVITY_POINTS } = require('./activity.controller');

// Set up storage for video uploads
const storage = multer.diskStorage({
  destination: function(req, file, cb) {
    const uploadPath = './public/uploads/videos';
    if (!fs.existsSync(uploadPath)) {
      fs.mkdirSync(uploadPath, { recursive: true });
    }
    cb(null, uploadPath);
  },
  filename: function(req, file, cb) {
    cb(null, `video_${Date.now()}${path.extname(file.originalname)}`);
  }
});

// File filter for video uploads
const fileFilter = (req, file, cb) => {
  // Accept video files only
  if (!file.mimetype.startsWith('video')) {
    return cb(new Error('Only video files are allowed'), false);
  }
  cb(null, true);
};

// Initialize multer upload
const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 50000000 // 50MB
  }
}).single('video');

// @desc    Upload a video
// @route   POST /api/videos/upload
// @access  Private
exports.uploadVideo = asyncHandler(async (req, res, next) => {
  upload(req, res, async (err) => {
    if (err) {
      return next(new ErrorResponse(`Error uploading file: ${err.message}`, 400));
    }
    
    if (!req.file) {
      return next(new ErrorResponse('Please upload a video file', 400));
    }
    
    const { title, description, category, ageGroup, isPublic } = req.body;
    
    const video = await Video.create({
      title,
      description,
      url: `/uploads/videos/${req.file.filename}`,
      thumbnail: req.body.thumbnail || '/uploads/thumbnails/default.jpg',
      user: req.user.id,
      category,
      ageGroup,
      isPublic: isPublic === 'true' || isPublic === true,
      duration: req.body.duration || 0
    });
    
    // Populate user details
    await video.populate('user', 'name avatarUrl');
    
    res.status(201).json({
      success: true,
      data: video
    });
  });
});

// @desc    Record a video watch event
// @route   POST /api/videos/:videoId/watch
// @access  Private
exports.recordVideoWatch = asyncHandler(async (req, res, next) => {
  const { videoId } = req.params;
  const userId = req.user.id;

  // Find the video
  const video = await Video.findById(videoId);
  if (!video) {
    return next(new ErrorResponse('Video not found', 404));
  }

  // Check if user already watched this video recently (in the last 24 hours)
  const lastWatched = video.views.find(
    view => view.user.toString() === userId && 
    (new Date() - view.date) < 24 * 60 * 60 * 1000
  );

  if (!lastWatched) {
    // Add view if not watched recently
    video.views.push({ user: userId });
    video.viewCount += 1;
    
    // Add points to the video uploader
    const uploader = await User.findById(video.user);
    if (uploader) {
      uploader.points = (uploader.points || 0) + ACTIVITY_POINTS.VIDEO_VIEW;
      await uploader.save();
      
      // Record points transaction
      const transaction = new PointsTransaction({
        user: uploader._id,
        points: ACTIVITY_POINTS.VIDEO_VIEW,
        type: 'video_view',
        description: `Earned ${ACTIVITY_POINTS.VIDEO_VIEW} points for video view`,
        referenceId: video._id
      });
      await transaction.save();
    }
    
    await video.save();
  }

  res.status(200).json({
    success: true,
    data: video
  });
});

// @desc    Get all videos with optional filtering
// @route   GET /api/videos
// @access  Public
exports.getVideos = asyncHandler(async (req, res, next) => {
  // Copy req.query
  const reqQuery = { ...req.query };
  
  // Fields to exclude from filtering
  const removeFields = ['select', 'sort', 'page', 'limit'];
  
  // Loop over removeFields and delete them from reqQuery
  removeFields.forEach(param => delete reqQuery[param]);
  
  // Create query string
  let queryStr = JSON.stringify(reqQuery);
  
  // Create operators ($gt, $gte, etc)
  queryStr = queryStr.replace(/\b(gt|gte|lt|lte|in)\b/g, match => `$${match}`);
  
  // Finding resource
  let query = Video.find(JSON.parse(queryStr)).populate('user', 'name avatarUrl');
  
  // Select Fields
  if (req.query.select) {
    const fields = req.query.select.split(',').join(' ');
    query = query.select(fields);
  }
  
  // Sort
  if (req.query.sort) {
    const sortBy = req.query.sort.split(',').join(' ');
    query = query.sort(sortBy);
  } else {
    query = query.sort('-createdAt');
  }
  
  // Pagination
  const page = parseInt(req.query.page, 10) || 1;
  const limit = parseInt(req.query.limit, 10) || 10;
  const startIndex = (page - 1) * limit;
  const endIndex = page * limit;
  const total = await Video.countDocuments(JSON.parse(queryStr));
  
  query = query.skip(startIndex).limit(limit);
  
  // Executing query
  const videos = await query;
  
  // Pagination result
  const pagination = {};
  
  if (endIndex < total) {
    pagination.next = {
      page: page + 1,
      limit
    };
  }
  
  if (startIndex > 0) {
    pagination.prev = {
      page: page - 1,
      limit
    };
  }
  
  res.status(200).json({
    success: true,
    count: videos.length,
    pagination,
    data: videos
  });
});

// @desc    Get single video
// @route   GET /api/videos/:id
// @access  Public
exports.getVideo = asyncHandler(async (req, res, next) => {
  const video = await Video.findById(req.params.id).populate('user', 'name avatarUrl');
  
  if (!video) {
    return next(new ErrorResponse(`Video not found with id of ${req.params.id}`, 404));
  }
  
  res.status(200).json({
    success: true,
    data: video
  });
});

// @desc    Update video
// @route   PUT /api/videos/:id
// @access  Private
exports.updateVideo = asyncHandler(async (req, res, next) => {
  let video = await Video.findById(req.params.id);
  
  if (!video) {
    return next(new ErrorResponse(`Video not found with id of ${req.params.id}`, 404));
  }
  
  // Make sure user is video owner
  if (video.user.toString() !== req.user.id && req.user.role !== 'admin') {
    return next(new ErrorResponse(`User ${req.user.id} is not authorized to update this video`, 401));
  }
  
  video = await Video.findByIdAndUpdate(req.params.id, req.body, {
    new: true,
    runValidators: true
  });
  
  res.status(200).json({
    success: true,
    data: video
  });
});

// @desc    Delete video
// @route   DELETE /api/videos/:id
// @access  Private
exports.deleteVideo = asyncHandler(async (req, res, next) => {
  const video = await Video.findById(req.params.id);
  
  if (!video) {
    return next(new ErrorResponse(`Video not found with id of ${req.params.id}`, 404));
  }
  
  // Make sure user is video owner or admin
  if (video.user.toString() !== req.user.id && req.user.role !== 'admin') {
    return next(new ErrorResponse(`User ${req.user.id} is not authorized to delete this video`, 401));
  }
  
  // Remove video file
  const videoPath = `./public${video.url}`;
  if (fs.existsSync(videoPath)) {
    fs.unlinkSync(videoPath);
  }
  
  // Remove thumbnail if not default
  if (video.thumbnail && !video.thumbnail.includes('default.jpg')) {
    const thumbnailPath = `./public${video.thumbnail}`;
    if (fs.existsSync(thumbnailPath)) {
      fs.unlinkSync(thumbnailPath);
    }
  }
  
  await video.remove();
  
  res.status(200).json({
    success: true,
    data: {}
  });
});

// @desc    Add comment to video
// @route   POST /api/videos/:id/comments
// @access  Private
exports.addComment = asyncHandler(async (req, res, next) => {
  const video = await Video.findById(req.params.id);
  
  if (!video) {
    return next(new ErrorResponse(`Video not found with id of ${req.params.id}`, 404));
  }
  
  const newComment = {
    user: req.user.id,
    text: req.body.text,
    name: req.user.name,
    avatar: req.user.avatar
  };
  
  video.comments.unshift(newComment);
  
  await video.save();
  
  // Populate user details in the response
  await video.populate('user', 'name avatarUrl');
  
  res.status(200).json({
    success: true,
    data: video.comments[0]
  });
});

// @desc    Remove comment from video
// @route   DELETE /api/videos/:id/comments/:comment_id
// @access  Private
exports.removeComment = asyncHandler(async (req, res, next) => {
  const video = await Video.findById(req.params.id);
  
  if (!video) {
    return next(new ErrorResponse(`Video not found with id of ${req.params.id}`, 404));
  }
  
  // Find comment
  const comment = video.comments.find(
    comment => comment.id === req.params.comment_id
  );
  
  // Make sure comment exists
  if (!comment) {
    return next(new ErrorResponse('Comment does not exist', 404));
  }
  
  // Check user is comment owner or admin
  if (comment.user.toString() !== req.user.id && req.user.role !== 'admin') {
    return next(new ErrorResponse('User not authorized', 401));
  }
  
  // Get remove index
  const removeIndex = video.comments
    .map(comment => comment.id)
    .indexOf(req.params.comment_id);
    
  video.comments.splice(removeIndex, 1);
  
  await video.save();
  
  res.status(200).json({
    success: true,
    data: {}
  });
});

// @desc    Like a video
// @route   PUT /api/videos/like/:id
// @access  Private
exports.likeVideo = asyncHandler(async (req, res, next) => {
  const video = await Video.findById(req.params.id);
  
  if (!video) {
    return next(new ErrorResponse(`Video not found with id of ${req.params.id}`, 404));
  }
  
  // Check if the video has already been liked by this user
  if (
    video.likes.filter(like => like.user.toString() === req.user.id).length > 0
  ) {
    return next(new ErrorResponse('Video already liked', 400));
  }
  
  video.likes.unshift({ user: req.user.id });
  
  // Remove from dislikes if exists
  const removeIndex = video.dislikes
    .map(item => item.user.toString())
    .indexOf(req.user.id);
    
  if (removeIndex !== -1) {
    video.dislikes.splice(removeIndex, 1);
  }
  
  await video.save();
  
  // Add notification
  if (video.user.toString() !== req.user.id) {
    const notification = new Notification({
      user: video.user,
      fromUser: req.user.id,
      type: 'like',
      message: `${req.user.name} liked your video: ${video.title}`,
      link: `/videos/${video._id}`,
      targetId: video._id,
      targetType: 'video'
    });
    
    await notification.save();
  }
  
  res.status(200).json({
    success: true,
    data: video.likes
  });
});

// @desc    Unlike a video
// @route   PUT /api/videos/unlike/:id
// @access  Private
exports.unlikeVideo = asyncHandler(async (req, res, next) => {
  const video = await Video.findById(req.params.id);
  
  if (!video) {
    return next(new ErrorResponse(`Video not found with id of ${req.params.id}`, 404));
  }
  
  // Check if the video has not yet been liked by this user
  if (
    video.likes.filter(like => like.user.toString() === req.user.id).length === 0
  ) {
    return next(new ErrorResponse('Video has not yet been liked', 400));
  }
  
  // Get remove index
  const removeIndex = video.likes
    .map(like => like.user.toString())
    .indexOf(req.user.id);
    
  // Remove like
  video.likes.splice(removeIndex, 1);
  
  await video.save();
  
  res.status(200).json({
    success: true,
    data: video.likes
  });
});

// @desc    Dislike a video
// @route   PUT /api/videos/dislike/:id
// @access  Private
exports.dislikeVideo = asyncHandler(async (req, res, next) => {
  const video = await Video.findById(req.params.id);
  
  if (!video) {
    return next(new ErrorResponse(`Video not found with id of ${req.params.id}`, 404));
  }
  
  // Check if the video has already been disliked by this user
  if (
    video.dislikes.filter(dislike => dislike.user.toString() === req.user.id).length > 0
  ) {
    return next(new ErrorResponse('Video already disliked', 400));
  }
  
  video.dislikes.unshift({ user: req.user.id });
  
  // Remove from likes if exists
  const removeIndex = video.likes
    .map(like => like.user.toString())
    .indexOf(req.user.id);
    
  if (removeIndex !== -1) {
    video.likes.splice(removeIndex, 1);
  }
  
  await video.save();
  
  res.status(200).json({
    success: true,
    data: video.dislikes
  });
});

// @desc    Get videos by user
// @route   GET /api/videos/user/:userId
// @access  Public
exports.getVideosByUser = asyncHandler(async (req, res, next) => {
  const videos = await Video.find({ user: req.params.userId })
    .sort('-createdAt')
    .populate('user', 'name avatarUrl');
    
  res.status(200).json({
    success: true,
    count: videos.length,
    data: videos
  });
});

// @desc    Get trending videos
// @route   GET /api/videos/trending
// @access  Public
exports.getTrendingVideos = asyncHandler(async (req, res, next) => {
  const videos = await Video.find()
    .sort({ viewCount: -1, 'likes.length': -1 })
    .limit(10)
    .populate('user', 'name avatarUrl');
    
  res.status(200).json({
    success: true,
    count: videos.length,
    data: videos
  });
});

// @desc    Search videos
// @route   GET /api/videos/search
// @access  Public
exports.searchVideos = asyncHandler(async (req, res, next) => {
  if (!req.query.q) {
    return next(new ErrorResponse('Please provide a search query', 400));
  }
  
  const searchQuery = req.query.q;
  const regex = new RegExp(searchQuery, 'i');
  
  const videos = await Video.find({
    $or: [
      { title: { $regex: regex } },
      { description: { $regex: regex } },
      { tags: { $in: [regex] } }
    ]
  })
  .populate('user', 'name avatarUrl');
  
  res.status(200).json({
    success: true,
    count: videos.length,
    data: videos
  });
});
