import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:share_plus/share_plus.dart';
import 'package:animated_text_kit/animated_text_kit.dart';
import 'package:confetti/confetti.dart';
import 'package:intl/intl.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:geolocator/geolocator.dart';
import 'package:provider/provider.dart';
import 'package:shimmer/shimmer.dart';
import 'dart:io';

import 'models/fish_breeding_models.dart';
import 'appstate.dart';

class MarketItem {
  final String id;
  final String user;
  final String type;
  final List<String> imageUrls;
  final double quantity;
  final double price;
  final String address;
  final DateTime date;
  final String validity;
  final String contactNumber;
  final String details;
  final String? species;
  final String? waterQuality;
  final String? applicantStatus;
  final String? registrationType;
  final String? description;
  final String? waterSource;
  final String? pondType;
  final String? filtrationSystem;
  final String? structures;
  final String? structuresDetails;

  MarketItem({
    required this.id,
    required this.user,
    required this.type,
    required this.imageUrls,
    required this.quantity,
    required this.price,
    required this.address,
    required this.date,
    required this.validity,
    required this.contactNumber,
    required this.details,
    this.species,
    this.waterQuality,
    this.applicantStatus,
    this.registrationType,
    this.description,
    this.waterSource,
    this.pondType,
    this.filtrationSystem,
    this.structures,
    this.structuresDetails,
  });
}

class FishBreedingPage extends StatefulWidget {
  const FishBreedingPage({super.key});

  @override
  State<FishBreedingPage> createState() => _FishBreedingPageState();
}

class _FishBreedingPageState extends State<FishBreedingPage> with SingleTickerProviderStateMixin {
  bool _canAddItems(AppState appState) {
    final roleName = appState.currentUser?.role.name ?? 'guest';
    final userType = appState.userType ?? 'guest';
    return roleName == 'admin' || roleName == 'hokama' || userType == 'admin' || userType == 'hokama';
  }

  final List<FishBreedingGroup> fish = [
    FishBreedingGroup(
      id: '1',
      name: 'سمك الزينة',
      imageUrl: 'https://cdn-icons-png.flaticon.com/512/1998/1998627.png',
      videos: [
        Video(title: 'تربية سمك الزينة', url: 'https://www.youtube.com/watch?v=fish1'),
        Video(title: 'أمراض سمك الزينة', url: 'https://www.youtube.com/watch?v=fish2'),
      ],
      articles: [
        Article(title: 'دليل العناية بسمك الزينة', content: 'كل ما تحتاجه للعناية بسمك الزينة...'),
      ],
      faqs: [
        FAQ(question: 'ماذا يأكل سمك الزينة؟', answer: 'يأكل طعام الأسماك الجاف، الديدان، والطحالب...'),
      ],
      habitat: 'أحواض مائية صغيرة مع نباتات مائية وزخارف',
      diet: 'طعام جاف، دود حي، طحالب',
      lifespan: '2-5 سنوات',
      careTips: 'تغيير المياه أسبوعيًا، الحفاظ على درجة حرارة 24-28 مئوية، تجنب التغذية الزائدة',
    ),
    FishBreedingGroup(
      id: '2',
      name: 'سمك السلمون',
      imageUrl: 'https://cdn-icons-png.flaticon.com/512/1998/1998632.png',
      videos: [
        Video(title: 'تربية سمك السلمون', url: 'https://www.youtube.com/watch?v=salmon1'),
      ],
      articles: [
        Article(title: 'صحة سمك السلمون', content: 'نصائح للحفاظ على صحة سمك السلمون...'),
      ],
      faqs: [
        FAQ(question: 'كيف أعرف إذا كان سمك السلمون مريضًا؟', answer: 'راقب حركته وشهيته ولون زعانفه...'),
      ],
      habitat: 'مياه باردة ونظيفة مع تيارات قوية',
      diet: 'طعام غني بالبروتين، حبيبات متخصصة',
      lifespan: '3-7 سنوات',
      careTips: 'الحفاظ على درجة حرارة 10-15 مئوية، توفير أكسجين كافٍ، فحص جودة المياه بانتظام',
    ),
    FishBreedingGroup(
      id: '3',
      name: 'سمك البلطي',
      imageUrl: 'https://cdn-icons-png.flaticon.com/512/1998/1998627.png',
      videos: [
        Video(title: 'تربية سمك البلطي', url: 'https://www.youtube.com/watch?v=tilapia1'),
      ],
      articles: [
        Article(title: 'دليل تربية سمك البلطي', content: 'معلومات عن تربية سمك البلطي...'),
      ],
      faqs: [
        FAQ(question: 'ما هي درجة الحرارة المثالية لسمك البلطي؟', answer: '22-30 مئوية مع pH 6.5-8.5...'),
      ],
      habitat: 'مياه عذبة دافئة مع أحواض كبيرة أو برك',
      diet: 'طعام نباتي وحيواني، حبيبات تجارية',
      lifespan: '5-10 سنوات',
      careTips: 'توفير مساحة كافية، مراقبة الأكسجين، تغذية متوازنة',
    ),
  ];

  final List<Post> posts = [
    Post(
      user: 'علي',
      content: 'سمك الزينة الخاص بي ينمو بسرعة!',
      imageUrl: 'https://cdn-icons-png.flaticon.com/512/1998/1998627.png',
      likes: 12,
      comments: 5,
      shares: 2,
    ),
    Post(
      user: 'سارة',
      content: 'نصائح للعناية بحوض الأسماك.',
      likes: 8,
      comments: 3,
      shares: 1,
    ),
  ];

  final List<MarketItem> fishItems = [
    MarketItem(
      id: '1',
      user: 'محمد',
      type: 'سمك الزينة',
      imageUrls: ['https://cdn-icons-png.flaticon.com/512/1998/1998627.png'],
      quantity: 10.0,
      price: 50.0,
      address: 'الرياض',
      date: DateTime.now().subtract(const Duration(days: 1)),
      validity: 'شهر',
      contactNumber: '1234567890',
      details: 'سمك ذهبي صغير',
      species: 'ذهبي',
      waterQuality: 'pH 7.0-7.5',
    ),
  ];

  final List<MarketItem> equipmentItems = [
    MarketItem(
      id: '2',
      user: 'خالد',
      type: 'فلاتر مياه',
      imageUrls: ['https://cdn-icons-png.flaticon.com/512/3082/3082872.png'],
      quantity: 5.0,
      price: 500.0,
      address: 'جدة',
      date: DateTime.now().subtract(const Duration(days: 2)),
      validity: 'شهر',
      contactNumber: '0987654321',
      details: 'فلاتر عالية الجودة لحوض الأسماك',
    ),
  ];

  final List<MarketItem> pondItems = [
    MarketItem(
      id: '3',
      user: 'يوسف',
      type: 'للبيع',
      imageUrls: ['https://cdn-icons-png.flaticon.com/512/3097/3097973.png'],
      quantity: 500.0,
      price: 50000.0,
      address: 'الدمام',
      date: DateTime.now().subtract(const Duration(days: 3)),
      validity: 'شهر',
      contactNumber: '1122334455',
      details: 'بركة مناسبة لتربية الأسماك',
      applicantStatus: 'مالك',
      registrationType: 'شهر عقاري',
      description: 'بركة ممتازة لتربية السلمون',
      waterSource: 'آبار',
      pondType: 'خرسانية',
      filtrationSystem: 'نعم',
      structures: 'نعم',
      structuresDetails: 'نظام تهوية',
    ),
  ];

  final List<String> fishTypes = [
    'سمك الزينة',
    'سمك السلمون',
    'سمك البلطي',
    'سمك القراميط',
    'سمك القد',
  ];

  final List<String> pondTypes = [
    'خرسانية',
    'بلاستيكية',
    'طينية',
    'طبيعية',
  ];

  final TextEditingController _postController = TextEditingController();
  final TextEditingController _searchController = TextEditingController();
  final ConfettiController _confettiController = ConfettiController(duration: const Duration(seconds: 3));
  late AnimationController _fabAnimationController;
  late Animation<double> _fabScaleAnimation;
  ImageProvider? _selectedMedia;

  int userPoints = 100;

  @override
  void initState() {
    super.initState();
    _fabAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 600),
    )..repeat(reverse: true);
    _fabScaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(parent: _fabAnimationController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _confettiController.dispose();
    _fabAnimationController.dispose();
    _postController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Scaffold(
          appBar: AppBar(
            title: AnimatedTextKit(
              animatedTexts: [
                TypewriterAnimatedText(
                  'تربية الأسماك',
                  speed: const Duration(milliseconds: 100),
                  textStyle: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 20,
                    color: Colors.white,
                  ),
                ),
              ],
              repeatForever: true,
            ),
            backgroundColor: Colors.blue.shade800,
            elevation: 0,
            actions: [
              IconButton(
                icon: const Icon(Icons.search, color: Colors.white),
                onPressed: () => _showSearchDialog(context),
              ),
              Consumer<AppState>(
                builder: (context, appState, child) {
                  if (_canAddItems(appState)) {
                    return IconButton(
                      icon: const Icon(Icons.add_circle_outline, color: Colors.white),
                      onPressed: () => _showQuickActions(context),
                    );
                  } else {
                    return const SizedBox.shrink();
                  }
                },
              ),
            ],
          ),
          body: RefreshIndicator(
            onRefresh: () async {
              await Future.delayed(const Duration(seconds: 1));
              setState(() {});
              _confettiController.play();
              Fluttertoast.showToast(
                msg: 'تم تحديث الصفحة',
                backgroundColor: Colors.blue.shade800,
                textColor: Colors.white,
              );
            },
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: AnimationLimiter(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: AnimationConfiguration.toStaggeredList(
                    duration: const Duration(milliseconds: 600),
                    childAnimationBuilder: (widget) => SlideAnimation(
                      verticalOffset: 50.0,
                      child: FadeInAnimation(child: widget),
                    ),
                    children: [
                      _buildSectionTitle('أنواع الأسماك'),
                      const SizedBox(height: 16),
                      _buildFishCards(),
                      const SizedBox(height: 24),
                      _buildSectionTitle('الأسواق'),
                      const SizedBox(height: 12),
                      _buildMarketplaces(),
                      const SizedBox(height: 24),
                      _buildSectionTitle('منشورات المجتمع'),
                      const SizedBox(height: 12),
                      _buildPostInput(),
                      const SizedBox(height: 12),
                      _buildPostsList(),
                    ],
                  ),
                ),
              ),
            ),
          ),
          floatingActionButton: ScaleTransition(
            scale: _fabScaleAnimation,
            child: FloatingActionButton(
              onPressed: () => _showQuickActions(context),
              tooltip: 'إجراءات سريعة',
              backgroundColor: Colors.blue.shade800,
              elevation: 8,
              child: const Icon(Icons.add, color: Colors.white, size: 28),
            ),
          ),
        ),
        Align(
          alignment: Alignment.topCenter,
          child: ConfettiWidget(
            confettiController: _confettiController,
            blastDirectionality: BlastDirectionality.explosive,
            particleDrag: 0.05,
            emissionFrequency: 0.03,
            numberOfParticles: 60,
            gravity: 0.05,
            colors: const [Colors.blue, Colors.cyan, Colors.white, Colors.yellow],
            strokeWidth: 1,
            strokeColor: Colors.blue.shade200,
          ),
        ),
      ],
    );
  }

  Widget _buildSectionTitle(String title) {
    return AnimatedTextKit(
      animatedTexts: [
        FadeAnimatedText(
          title,
          textStyle: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: Colors.blue.shade800,
            shadows: [
              Shadow(
                blurRadius: 5,
                color: Colors.blue.shade200,
                offset: const Offset(2, 2),
              ),
            ],
          ),
        ),
      ],
      totalRepeatCount: 1,
    );
  }

  Widget _buildFishCards() {
    return SizedBox(
      height: 280,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: fish.length,
        itemBuilder: (context, index) {
          final fishGroup = fish[index];
          return AnimationConfiguration.staggeredList(
            position: index,
            duration: const Duration(milliseconds: 700),
            child: SlideAnimation(
              horizontalOffset: 50.0,
              child: FadeInAnimation(
                child: GestureDetector(
                  onTap: () => _showFishDetails(context, fishGroup),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8.0),
                    child: Card(
                      elevation: 10,
                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
                      child: Container(
                        width: 200,
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [Colors.blue.shade300, Colors.blue.shade600],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ),
                          borderRadius: BorderRadius.circular(20),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.blue.shade200.withOpacity(0.4),
                              blurRadius: 12,
                              offset: const Offset(0, 6),
                            ),
                          ],
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            ClipRRect(
                              borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
                              child: CachedNetworkImage(
                                imageUrl: fishGroup.imageUrl,
                                height: 120,
                                width: double.infinity,
                                fit: BoxFit.cover,
                                placeholder: (context, url) => _buildShimmerPlaceholder(),
                                errorWidget: (context, url, error) => const Icon(Icons.error, size: 50, color: Colors.white),
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.all(12.0),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  AnimatedTextKit(
                                    animatedTexts: [
                                      TypewriterAnimatedText(
                                        fishGroup.name,
                                        textStyle: const TextStyle(
                                          fontSize: 18,
                                          fontWeight: FontWeight.bold,
                                          color: Colors.white,
                                        ),
                                        speed: const Duration(milliseconds: 100),
                                      ),
                                    ],
                                    totalRepeatCount: 1,
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                    'العمر: ${fishGroup.lifespan}',
                                    style: const TextStyle(fontSize: 14, color: Colors.white70),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                  Text(
                                    'النظام الغذائي: ${fishGroup.diet}',
                                    style: const TextStyle(fontSize: 14, color: Colors.white70),
                                    maxLines: 2,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildMarketplaces() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildFishMarketplace(),
        const SizedBox(height: 24),
        _buildEquipmentMarketplace(),
        const SizedBox(height: 24),
        _buildPondsMarketplace(),
      ],
    );
  }

  Widget _buildFishMarketplace() {
    return Stack(
      children: [
        Card(
          elevation: 8,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.blue.shade100, Colors.blue.shade50],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(color: Colors.blue.shade200, width: 1),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.pets, color: Colors.blue.shade800, size: 32),
                      const SizedBox(width: 8),
                      Text(
                        'سوق الأسماك',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          color: Colors.blue.shade800,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  SizedBox(
                    height: 320,
                    child: ListView.builder(
                      scrollDirection: Axis.horizontal,
                      itemCount: fishItems.length,
                      itemBuilder: (context, index) {
                        final item = fishItems[index];
                        return AnimationConfiguration.staggeredList(
                          position: index,
                          duration: const Duration(milliseconds: 700),
                          child: SlideAnimation(
                            horizontalOffset: 50.0,
                            child: FadeInAnimation(
                              child: _buildMarketCard(item),
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        Positioned(
          bottom: 16,
          right: 16,
          child: ScaleTransition(
            scale: _fabScaleAnimation,
            child: FloatingActionButton(
              onPressed: () => _showAddFishDialog(context),
              backgroundColor: Colors.blue.shade700,
              tooltip: 'إضافة سمك',
              elevation: 8,
              child: const Icon(Icons.add, color: Colors.white),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildEquipmentMarketplace() {
    return Stack(
      children: [
        Card(
          elevation: 8,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.blue.shade100, Colors.blue.shade50],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(color: Colors.blue.shade200, width: 1),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.build, color: Colors.blue.shade800, size: 32),
                      const SizedBox(width: 8),
                      Text(
                        'سوق المعدات',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          color: Colors.blue.shade800,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: [
                      Chip(
                        label: const Text('فلاتر مياه'),
                        backgroundColor: Colors.blue.shade100,
                        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                      ),
                      Chip(
                        label: const Text('مضخات هواء'),
                        backgroundColor: Colors.blue.shade100,
                        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                      ),
                      Chip(
                        label: const Text('أحواض'),
                        backgroundColor: Colors.blue.shade100,
                        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                      ),
                      Chip(
                        label: const Text('مدافئ'),
                        backgroundColor: Colors.blue.shade100,
                        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                      ),
                      Chip(
                        label: const Text('إضاءة'),
                        backgroundColor: Colors.blue.shade100,
                        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  SizedBox(
                    height: 320,
                    child: ListView.builder(
                      scrollDirection: Axis.horizontal,
                      itemCount: equipmentItems.length,
                      itemBuilder: (context, index) {
                        final item = equipmentItems[index];
                        return AnimationConfiguration.staggeredList(
                          position: index,
                          duration: const Duration(milliseconds: 700),
                          child: SlideAnimation(
                            horizontalOffset: 50.0,
                            child: FadeInAnimation(
                              child: _buildMarketCard(item),
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        Positioned(
          bottom: 16,
          right: 16,
          child: ScaleTransition(
            scale: _fabScaleAnimation,
            child: FloatingActionButton(
              onPressed: () => _showAddEquipmentDialog(context),
              backgroundColor: Colors.blue.shade800,
              tooltip: 'إضافة معدات',
              elevation: 8,
              child: const Icon(Icons.add, color: Colors.white),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPondsMarketplace() {
    return Stack(
      children: [
        Card(
          elevation: 8,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.blue.shade100, Colors.blue.shade50],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(color: Colors.blue.shade200, width: 1),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.pool, color: Colors.blue.shade800, size: 32),
                      const SizedBox(width: 8),
                      Text(
                        'سوق البرك والأحواض',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          color: Colors.blue.shade800,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  SizedBox(
                    height: 320,
                    child: ListView.builder(
                      scrollDirection: Axis.horizontal,
                      itemCount: pondItems.length,
                      itemBuilder: (context, index) {
                        final item = pondItems[index];
                        return AnimationConfiguration.staggeredList(
                          position: index,
                          duration: const Duration(milliseconds: 700),
                          child: SlideAnimation(
                            horizontalOffset: 50.0,
                            child: FadeInAnimation(
                              child: _buildMarketCard(item),
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        Positioned(
          bottom: 16,
          right: 16,
          child: ScaleTransition(
            scale: _fabScaleAnimation,
            child: FloatingActionButton(
              onPressed: () => _showAddPondDialog(context),
              backgroundColor: Colors.blue.shade800,
              tooltip: 'إضافة بركة',
              elevation: 8,
              child: const Icon(Icons.add, color: Colors.white),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildMarketCard(MarketItem item) {
    return Padding(
      padding: const EdgeInsets.only(right: 12.0),
      child: MouseRegion(
        onEnter: (_) => setState(() {}),
        onExit: (_) => setState(() {}),
        child: Card(
          elevation: 8,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
          child: Container(
            width: 220,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: Colors.blue.shade200.withOpacity(0.3),
                  blurRadius: 10,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ClipRRect(
                  borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
                  child: item.imageUrls.isNotEmpty
                      ? CachedNetworkImage(
                    imageUrl: item.imageUrls.first,
                    width: 220,
                    height: 140,
                    fit: BoxFit.cover,
                    placeholder: (context, url) => _buildShimmerPlaceholder(),
                    errorWidget: (context, url, error) => const Icon(Icons.error, size: 50),
                  )
                      : Container(
                    width: 220,
                    height: 140,
                    color: Colors.grey.shade200,
                    child: Icon(Icons.image, color: Colors.grey.shade600, size: 50),
                  ),
                ),
                Flexible(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(12.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          item.type,
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                            color: Colors.blue.shade800,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'الكمية: ${item.quantity} ${item.species != null ? 'أسماك' : item.applicantStatus != null ? 'متر مربع' : 'وحدة'}',
                          style: TextStyle(fontSize: 14, color: Colors.grey.shade800),
                        ),
                        Text(
                          'السعر: ${item.price} ريال',
                          style: TextStyle(fontSize: 14, color: Colors.grey.shade800),
                        ),
                        Text(
                          'العنوان: ${item.address}',
                          style: TextStyle(fontSize: 14, color: Colors.grey.shade800),
                        ),
                        Text(
                          'رقم التواصل: ${item.contactNumber}',
                          style: TextStyle(fontSize: 14, color: Colors.grey.shade800),
                        ),
                        Text(
                          'التفاصيل: ${item.details}',
                          style: TextStyle(fontSize: 14, color: Colors.grey.shade800),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        Text(
                          'التاريخ: ${DateFormat('dd/MM/yyyy').format(item.date)}',
                          style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
                        ),
                        if (item.species != null) ...[
                          Text(
                            'النوع: ${item.species}',
                            style: TextStyle(fontSize: 14, color: Colors.grey.shade800),
                          ),
                          Text(
                            'جودة المياه: ${item.waterQuality}',
                            style: TextStyle(fontSize: 14, color: Colors.grey.shade800),
                          ),
                        ],
                        if (item.applicantStatus != null) ...[
                          Text(
                            'الحالة: ${item.applicantStatus}',
                            style: TextStyle(fontSize: 14, color: Colors.grey.shade800),
                          ),
                          Text(
                            'التسجيل: ${item.registrationType}',
                            style: TextStyle(fontSize: 14, color: Colors.grey.shade800),
                          ),
                          Text(
                            'مصدر المياه: ${item.waterSource}',
                            style: TextStyle(fontSize: 14, color: Colors.grey.shade800),
                          ),
                          Text(
                            'نوع البركة: ${item.pondType}',
                            style: TextStyle(fontSize: 14, color: Colors.grey.shade800),
                          ),
                          Text(
                            'نظام الترشيح: ${item.filtrationSystem}',
                            style: TextStyle(fontSize: 14, color: Colors.grey.shade800),
                          ),
                          Text(
                            'المنشآت: ${item.structures}',
                            style: TextStyle(fontSize: 14, color: Colors.grey.shade800),
                          ),
                        ],
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildShimmerPlaceholder() {
    return Shimmer.fromColors(
      baseColor: Colors.grey.shade300,
      highlightColor: Colors.grey.shade100,
      child: Container(
        width: double.infinity,
        height: 140,
        color: Colors.white,
      ),
    );
  }

  Widget _buildPostInput() {
    return Card(
      elevation: 6,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // User info and text input
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CircleAvatar(
                  backgroundColor: Colors.blue.shade200,
                  child: const Icon(Icons.person, color: Colors.white, size: 20),
                  radius: 20,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: TextField(
                    controller: _postController,
                    decoration: InputDecoration(
                      hintText: 'ما الذي تفكر به؟',
                      hintStyle: TextStyle(color: Colors.grey.shade600, fontSize: 15),
                      border: InputBorder.none,
                      contentPadding: const EdgeInsets.symmetric(vertical: 8),
                    ),
                    maxLines: 5,
                    minLines: 1,
                    style: const TextStyle(fontSize: 15),
                  ),
                ),
              ],
            ),
            
            // Media preview (if any)
            if (_selectedMedia != null)
              Container(
                margin: const EdgeInsets.only(top: 12),
                height: 200,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  image: DecorationImage(
                    image: _selectedMedia!,
                    fit: BoxFit.cover,
                  ),
                ),
                child: Stack(
                  children: [
                    Positioned(
                      top: 8,
                      right: 8,
                      child: GestureDetector(
                        onTap: () {
                          setState(() {
                            _selectedMedia = null;
                          });
                        },
                        child: Container(
                          padding: const EdgeInsets.all(4),
                          decoration: const BoxDecoration(
                            color: Colors.black54,
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(Icons.close, color: Colors.white, size: 18),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            
            // Action buttons
            Padding(
              padding: const EdgeInsets.only(top: 12.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  // Image button
                  TextButton.icon(
                    onPressed: () async {
                      // Add image selection logic here
                      // For now, we'll just show a toast
                      Fluttertoast.showToast(
                        msg: 'إضافة صورة',
                        backgroundColor: Colors.blue.shade800,
                        textColor: Colors.white,
                      );
                    },
                    icon: Icon(Icons.image, color: Colors.green.shade700, size: 22),
                    label: Text(
                      'صورة',
                      style: TextStyle(
                        color: Colors.grey.shade800,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    style: TextButton.styleFrom(
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(20),
                      ),
                      backgroundColor: Colors.green.shade50,
                    ),
                  ),
                  
                  // Video button
                  TextButton.icon(
                    onPressed: () {
                      Fluttertoast.showToast(
                        msg: 'إضافة فيديو',
                        backgroundColor: Colors.blue.shade800,
                        textColor: Colors.white,
                      );
                    },
                    icon: Icon(Icons.videocam, color: Colors.red.shade600, size: 22),
                    label: Text(
                      'فيديو',
                      style: TextStyle(
                        color: Colors.grey.shade800,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    style: TextButton.styleFrom(
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(20),
                      ),
                      backgroundColor: Colors.red.shade50,
                    ),
                  ),
                  
                  // Story button
                  TextButton.icon(
                    onPressed: () {
                      Fluttertoast.showToast(
                        msg: 'إضافة قصة',
                        backgroundColor: Colors.blue.shade800,
                        textColor: Colors.white,
                      );
                    },
                    icon: Icon(Icons.auto_awesome, color: Colors.purple.shade600, size: 22),
                    label: Text(
                      'قصة',
                      style: TextStyle(
                        color: Colors.grey.shade800,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    style: TextButton.styleFrom(
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(20),
                      ),
                      backgroundColor: Colors.purple.shade50,
                    ),
                  ),
                ],
              ),
            ),
            
            // Post button
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: () {
                if (_postController.text.trim().isNotEmpty || _selectedMedia != null) {
                  setState(() {
                    // Add the new post to the beginning of the list
                    posts.insert(
                      0,
                      Post(
                        user: 'Current User',
                        content: _postController.text.trim(),
                        imageUrl: _selectedMedia != null ? 'https://example.com/placeholder.jpg' : null,
                        likes: 0,
                        comments: 0,
                        shares: 0,
                      ),
                    );
                    
                    // Clear the input and selected media
                    _postController.clear();
                    _selectedMedia = null;
                    
                    // Show success message and confetti
                    _confettiController.play();
                    Fluttertoast.showToast(
                      msg: 'تم نشر المنشور بنجاح',
                      backgroundColor: Colors.green.shade700,
                      textColor: Colors.white,
                    );
                  });
                } else {
                  Fluttertoast.showToast(
                    msg: 'الرجاء إدخال نص أو إضافة وسائط',
                    backgroundColor: Colors.orange.shade700,
                    textColor: Colors.white,
                  );
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue.shade800,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                elevation: 2,
              ),
              child: const Text(
                'نشر',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPostsList() {
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: posts.length,
      itemBuilder: (context, index) {
        final post = posts[index];
        return AnimationConfiguration.staggeredList(
          position: index,
          duration: const Duration(milliseconds: 700),
          child: SlideAnimation(
            verticalOffset: 50.0,
            child: FadeInAnimation(
              child: Card(
                margin: const EdgeInsets.symmetric(vertical: 8),
                elevation: 6,
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(color: Colors.blue.shade100, width: 1),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            CircleAvatar(
                              backgroundColor: Colors.blue.shade800,
                              child: Text(
                                post.user[0],
                                style: const TextStyle(color: Colors.white),
                              ),
                            ),
                            const SizedBox(width: 8),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  post.user,
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 16,
                                    color: Colors.blue.shade800,
                                  ),
                                ),
                                Text(
                                  DateFormat('dd/MM/yyyy HH:mm').format(DateTime.now()),
                                  style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
                                ),
                              ],
                            ),
                          ],
                        ),
                        const SizedBox(height: 12),
                        Text(
                          post.content,
                          style: const TextStyle(fontSize: 14),
                        ),
                        if (post.imageUrl != null) ...[
                          const SizedBox(height: 12),
                          ClipRRect(
                            borderRadius: BorderRadius.circular(12),
                            child: CachedNetworkImage(
                              imageUrl: post.imageUrl!,
                              height: 200,
                              width: double.infinity,
                              fit: BoxFit.cover,
                              placeholder: (context, url) => _buildShimmerPlaceholder(),
                              errorWidget: (context, url, error) => const Icon(Icons.error),
                            ),
                          ),
                        ],
                        const SizedBox(height: 12),
                        Divider(color: Colors.blue.shade100),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Row(
                              children: [
                                IconButton(
                                  icon: Icon(Icons.thumb_up_alt, color: Colors.blue.shade800),
                                  onPressed: () {
                                    setState(() {
                                      post.likes += 1;
                                      _confettiController.play();
                                      Fluttertoast.showToast(
                                        msg: 'تم الإعجاب بالمنشور',
                                        backgroundColor: Colors.blue.shade800,
                                        textColor: Colors.white,
                                      );
                                    });
                                  },
                                ),
                                Text('${post.likes} إعجاب'),
                              ],
                            ),
                            Row(
                              children: [
                                IconButton(
                                  icon: Icon(Icons.comment, color: Colors.blue.shade800),
                                  onPressed: () {
                                    _showCommentDialog(context, post);
                                    _confettiController.play();
                                  },
                                ),
                                Text('${post.comments} تعليق'),
                              ],
                            ),
                            Row(
                              children: [
                                IconButton(
                                  icon: Icon(Icons.share, color: Colors.blue.shade800),
                                  onPressed: () {
                                    Share.share('تحقق من هذا المنشور: ${post.content}');
                                    setState(() {
                                      post.shares += 1;
                                      _confettiController.play();
                                      Fluttertoast.showToast(
                                        msg: 'تمت المشاركة',
                                        backgroundColor: Colors.blue.shade800,
                                        textColor: Colors.white,
                                      );
                                    });
                                  },
                                ),
                                Text('${post.shares} مشاركة'),
                              ],
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  void _showFishDetails(BuildContext context, FishBreedingGroup fishGroup) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.9,
        minChildSize: 0.5,
        maxChildSize: 0.95,
        builder: (context, scrollController) => Container(
          decoration: BoxDecoration(
            color: Theme.of(context).scaffoldBackgroundColor,
            borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
            boxShadow: [
              BoxShadow(
                color: Colors.blue.shade200.withOpacity(0.3),
                blurRadius: 12,
                offset: const Offset(0, -5),
              ),
            ],
          ),
          child: SingleChildScrollView(
            controller: scrollController,
            child: Column(
              children: [
                Container(
                  margin: const EdgeInsets.symmetric(vertical: 12),
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: Colors.grey.shade400,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        fishGroup.name,
                        style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          color: Colors.blue.shade800,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 12),
                      ClipRRect(
                        borderRadius: BorderRadius.circular(12),
                        child: CachedNetworkImage(
                          imageUrl: fishGroup.imageUrl,
                          height: 200,
                          width: double.infinity,
                          fit: BoxFit.cover,
                          placeholder: (context, url) => _buildShimmerPlaceholder(),
                          errorWidget: (context, url, error) => const Icon(Icons.error, size: 50),
                        ),
                      ),
                      const SizedBox(height: 16),
                      DefaultTabController(
                        length: 5,
                        child: Column(
                          children: [
                            TabBar(
                              tabs: const [
                                Tab(text: 'نظرة عامة'),
                                Tab(text: 'الفيديوهات'),
                                Tab(text: 'المقالات'),
                                Tab(text: 'الأسئلة'),
                                Tab(text: 'المتطلبات'),
                              ],
                              labelColor: Colors.blue.shade800,
                              unselectedLabelColor: Colors.grey.shade600,
                              indicatorColor: Colors.blue.shade800,
                              labelStyle: const TextStyle(fontWeight: FontWeight.bold),
                              unselectedLabelStyle: const TextStyle(fontWeight: FontWeight.normal),
                            ),
                            SizedBox(
                              height: 400,
                              child: TabBarView(
                                children: [
                                  _buildOverviewTab(fishGroup),
                                  _buildVideosTab(fishGroup.videos),
                                  _buildArticlesTab(fishGroup.articles),
                                  _buildFAQsTab(fishGroup.faqs),
                                  _buildRequirementsTab(),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildOverviewTab(FishBreedingGroup fishGroup) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'معلومات عامة',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold, color: Colors.blue.shade800),
          ),
          const SizedBox(height: 8),
          Text('الموطن: ${fishGroup.habitat}', style: const TextStyle(fontSize: 14)),
          Text('النظام الغذائي: ${fishGroup.diet}', style: const TextStyle(fontSize: 14)),
          Text('العمر الافتراضي: ${fishGroup.lifespan}', style: const TextStyle(fontSize: 14)),
          Text('نصائح العناية: ${fishGroup.careTips}', style: const TextStyle(fontSize: 14)),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () {
              Share.share('تعرف على ${fishGroup.name}: ${fishGroup.habitat}');
              _confettiController.play();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue.shade800,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
            ),
            child: const Text(
              'مشاركة المعلومات',
              style: TextStyle(color: Colors.white, fontSize: 14),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildVideosTab(List<Video> videos) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: videos.length,
      itemBuilder: (context, index) {
        final video = videos[index];
        return AnimationConfiguration.staggeredList(
          position: index,
          duration: const Duration(milliseconds: 700),
          child: SlideAnimation(
            verticalOffset: 50.0,
            child: FadeInAnimation(
              child: ListTile(
                leading: Icon(Icons.videocam, color: Colors.blue.shade800),
                title: Text(
                  video.title,
                  style: TextStyle(color: Colors.blue.shade800),
                ),
                onTap: () {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('تشغيل ${video.title}')),
                  );
                  _confettiController.play();
                },
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildArticlesTab(List<Article> articles) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: articles.length,
      itemBuilder: (context, index) {
        final article = articles[index];
        return AnimationConfiguration.staggeredList(
          position: index,
          duration: const Duration(milliseconds: 700),
          child: SlideAnimation(
            verticalOffset: 50.0,
            child: FadeInAnimation(
              child: ExpansionTile(
                leading: Icon(Icons.article, color: Colors.blue.shade800),
                title: Text(
                  article.title,
                  style: TextStyle(color: Colors.blue.shade800),
                ),
                tilePadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                collapsedBackgroundColor: Colors.blue.shade50,
                backgroundColor: Colors.blue.shade100,
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                children: [
                  Padding(
                    padding: const EdgeInsets.all(16),
                    child: Text(
                      article.content,
                      style: const TextStyle(fontSize: 14),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildFAQsTab(List<FAQ> faqs) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: faqs.length,
      itemBuilder: (context, index) {
        final faq = faqs[index];
        return AnimationConfiguration.staggeredList(
          position: index,
          duration: const Duration(milliseconds: 700),
          child: SlideAnimation(
            verticalOffset: 50.0,
            child: FadeInAnimation(
              child: ExpansionTile(
                leading: Icon(Icons.question_answer, color: Colors.blue.shade800),
                title: Text(
                  faq.question,
                  style: TextStyle(color: Colors.blue.shade800),
                ),
                tilePadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                collapsedBackgroundColor: Colors.blue.shade50,
                backgroundColor: Colors.blue.shade100,
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                children: [
                  Padding(
                    padding: const EdgeInsets.all(16),
                    child: Text(
                      faq.answer,
                      style: const TextStyle(fontSize: 14),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildRequirementsTab() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: const [
          Text(
            'متطلبات تربية الأسماك:',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
          SizedBox(height: 8),
          Text('- حوض أو بركة مناسبة', style: TextStyle(fontSize: 14)),
          Text('- تغذية صحية', style: TextStyle(fontSize: 14)),
          Text('- مياه نظيفة بجودة مناسبة', style: TextStyle(fontSize: 14)),
          Text('- فحوصات دورية للمياه والأسماك', style: TextStyle(fontSize: 14)),
        ],
      ),
    );
  }

  void _showAddFishDialog(BuildContext context) {
    final appState = Provider.of<AppState>(context, listen: false);
    if (!_canAddItems(appState)) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('غير مصرح'),
          content: const Text('هذه الميزة متاحة فقط للحكماء'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('إغلاق'),
            ),
          ],
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          backgroundColor: Colors.blue.shade50,
        ),
      );
      return;
    }

    final quantityController = TextEditingController();
    final priceController = TextEditingController();
    final addressController = TextEditingController();
    final contactController = TextEditingController();
    final detailsController = TextEditingController();
    final speciesController = TextEditingController();
    String selectedFishType = fishTypes.first;
    String selectedWaterQuality = 'pH 7.0-7.5';
    String selectedValidity = 'أسبوع';
    List<String> imageUrls = [];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'إضافة سمك',
          style: TextStyle(color: Colors.blue.shade800, fontWeight: FontWeight.bold),
        ),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              DropdownButton<String>(
                value: selectedFishType,
                isExpanded: true,
                items: fishTypes.map((String fish) {
                  return DropdownMenuItem<String>(
                    value: fish,
                    child: Text(fish),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  setState(() {
                    selectedFishType = newValue!;
                  });
                },
                dropdownColor: Colors.blue.shade50,
                style: TextStyle(color: Colors.blue.shade800),
                borderRadius: BorderRadius.circular(12),
              ),
              const SizedBox(height: 12),
              TextField(
                controller: quantityController,
                decoration: InputDecoration(
                  hintText: 'الكمية (أسماك)',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide.none,
                  ),
                  filled: true,
                  fillColor: Colors.blue.shade50,
                ),
                keyboardType: TextInputType.number,
              ),
              const SizedBox(height: 12),
              TextField(
                controller: priceController,
                decoration: InputDecoration(
                  hintText: 'السعر (ريال)',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide.none,
                  ),
                  filled: true,
                  fillColor: Colors.blue.shade50,
                ),
                keyboardType: TextInputType.number,
              ),
              const SizedBox(height: 12),
              TextField(
                controller: addressController,
                decoration: InputDecoration(
                  hintText: 'العنوان',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide.none,
                  ),
                  filled: true,
                  fillColor: Colors.blue.shade50,
                  suffixIcon: IconButton(
                    icon: Icon(Icons.location_on, color: Colors.blue.shade800),
                    onPressed: () async {
                      try {
                        Position position = await Geolocator.getCurrentPosition();
                        addressController.text = 'موقع جغرافي: ${position.latitude}, ${position.longitude}';
                        Fluttertoast.showToast(
                          msg: 'تم تحديد الموقع',
                          backgroundColor: Colors.blue.shade800,
                          textColor: Colors.white,
                        );
                      } catch (e) {
                        Fluttertoast.showToast(
                          msg: 'خطأ في تحديد الموقع',
                          backgroundColor: Colors.red,
                          textColor: Colors.white,
                        );
                      }
                    },
                  ),
                ),
              ),
              const SizedBox(height: 12),
              DropdownButton<String>(
                value: selectedValidity,
                isExpanded: true,
                items: ['أسبوع', 'أسبوعان', 'شهر'].map((String validity) {
                  return DropdownMenuItem<String>(
                    value: validity,
                    child: Text(validity),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  setState(() {
                    selectedValidity = newValue!;
                  });
                },
                dropdownColor: Colors.blue.shade50,
                style: TextStyle(color: Colors.blue.shade800),
                borderRadius: BorderRadius.circular(12),
              ),
              const SizedBox(height: 12),
              TextField(
                controller: contactController,
                decoration: InputDecoration(
                  hintText: 'رقم التواصل',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide.none,
                  ),
                  filled: true,
                  fillColor: Colors.blue.shade50,
                ),
                keyboardType: TextInputType.phone,
              ),
              const SizedBox(height: 12),
              TextField(
                controller: detailsController,
                decoration: InputDecoration(
                  hintText: 'تفاصيل العرض',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide.none,
                  ),
                  filled: true,
                  fillColor: Colors.blue.shade50,
                ),
                maxLines: 3,
              ),
              const SizedBox(height: 12),
              TextField(
                controller: speciesController,
                decoration: InputDecoration(
                  hintText: 'نوع السمك (اختياري)',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide.none,
                  ),
                  filled: true,
                  fillColor: Colors.blue.shade50,
                ),
              ),
              const SizedBox(height: 12),
              DropdownButton<String>(
                value: selectedWaterQuality,
                isExpanded: true,
                items: ['pH 7.0-7.5', 'pH 6.5-7.0', 'pH 7.5-8.0'].map((String quality) {
                  return DropdownMenuItem<String>(
                    value: quality,
                    child: Text(quality),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  setState(() {
                    selectedWaterQuality = newValue!;
                  });
                },
                dropdownColor: Colors.blue.shade50,
                style: TextStyle(color: Colors.blue.shade800),
                borderRadius: BorderRadius.circular(12),
              ),
              const SizedBox(height: 12),
              ElevatedButton(
                onPressed: () {
                  imageUrls.add('https://cdn-icons-png.flaticon.com/512/1998/1998627.png');
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('تم تحميل الصورة')),
                  );
                  _confettiController.play();
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue.shade800,
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                  padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                ),
                child: const Text(
                  'تحميل صورة',
                  style: TextStyle(color: Colors.white, fontSize: 14),
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء', style: TextStyle(color: Colors.grey)),
          ),
          ElevatedButton(
            onPressed: () {
              if (quantityController.text.isEmpty ||
                  priceController.text.isEmpty ||
                  addressController.text.isEmpty ||
                  contactController.text.isEmpty ||
                  detailsController.text.isEmpty) {
                Fluttertoast.showToast(
                  msg: 'يرجى ملء جميع الحقول',
                  backgroundColor: Colors.red,
                  textColor: Colors.white,
                );
                return;
              }
              setState(() {
                fishItems.add(MarketItem(
                  id: DateTime.now().millisecondsSinceEpoch.toString(),
                  user: appState.currentUser?.name ?? 'مستخدم',
                  type: selectedFishType,
                  imageUrls: imageUrls,
                  quantity: double.parse(quantityController.text),
                  price: double.parse(priceController.text),
                  address: addressController.text,
                  date: DateTime.now(),
                  validity: selectedValidity,
                  contactNumber: contactController.text,
                  details: detailsController.text,
                  species: speciesController.text.isNotEmpty ? speciesController.text : null,
                  waterQuality: selectedWaterQuality,
                ));
                _confettiController.play();
                Fluttertoast.showToast(
                  msg: 'تم إضافة السمك بنجاح',
                  backgroundColor: Colors.blue.shade800,
                  textColor: Colors.white,
                );
              });
              Navigator.pop(context);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue.shade800,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            ),
            child: const Text(
              'إضافة',
              style: TextStyle(color: Colors.white),
            ),
          ),
        ],
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        backgroundColor: Colors.blue.shade50,
      ),
    );
  }

  void _showAddEquipmentDialog(BuildContext context) {
    final appState = Provider.of<AppState>(context, listen: false);
    if (!_canAddItems(appState)) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('غير مصرح'),
          content: const Text('هذه الميزة متاحة فقط للحكماء'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('إغلاق'),
            ),
          ],
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          backgroundColor: Colors.blue.shade50,
        ),
      );
      return;
    }

    final quantityController = TextEditingController();
    final priceController = TextEditingController();
    final addressController = TextEditingController();
    final contactController = TextEditingController();
    final detailsController = TextEditingController();
    String selectedEquipmentType = 'فلاتر مياه';
    String selectedValidity = 'أسبوع';
    List<String> imageUrls = [];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'إضافة معدات',
          style: TextStyle(color: Colors.blue.shade800, fontWeight: FontWeight.bold),
        ),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              DropdownButton<String>(
                value: selectedEquipmentType,
                isExpanded: true,
                items: ['فلاتر مياه', 'مضخات هواء', 'أحواض', 'مدافئ', 'إضاءة'].map((String type) {
                  return DropdownMenuItem<String>(
                    value: type,
                    child: Text(type),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  setState(() {
                    selectedEquipmentType = newValue!;
                  });
                },
                dropdownColor: Colors.blue.shade50,
                style: TextStyle(color: Colors.blue.shade800),
                borderRadius: BorderRadius.circular(12),
              ),
              const SizedBox(height: 12),
              TextField(
                controller: quantityController,
                decoration: InputDecoration(
                  hintText: 'الكمية (وحدة)',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide.none,
                  ),
                  filled: true,
                  fillColor: Colors.blue.shade50,
                ),
                keyboardType: TextInputType.number,
              ),
              const SizedBox(height: 12),
              TextField(
                controller: priceController,
                decoration: InputDecoration(
                  hintText: 'السعر (ريال)',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide.none,
                  ),
                  filled: true,
                  fillColor: Colors.blue.shade50,
                ),
                keyboardType: TextInputType.number,
              ),
              const SizedBox(height: 12),
              TextField(
                controller: addressController,
                decoration: InputDecoration(
                  hintText: 'العنوان',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide.none,
                  ),
                  filled: true,
                  fillColor: Colors.blue.shade50,
                  suffixIcon: IconButton(
                    icon: Icon(Icons.location_on, color: Colors.blue.shade800),
                    onPressed: () async {
                      try {
                        Position position = await Geolocator.getCurrentPosition();
                        addressController.text = 'موقع جغرافي: ${position.latitude}, ${position.longitude}';
                        Fluttertoast.showToast(
                          msg: 'تم تحديد الموقع',
                          backgroundColor: Colors.blue.shade800,
                          textColor: Colors.white,
                        );
                      } catch (e) {
                        Fluttertoast.showToast(
                          msg: 'خطأ في تحديد الموقع',
                          backgroundColor: Colors.red,
                          textColor: Colors.white,
                        );
                      }
                    },
                  ),
                ),
              ),
              const SizedBox(height: 12),
              DropdownButton<String>(
                value: selectedValidity,
                isExpanded: true,
                items: ['أسبوع', 'أسبوعان', 'شهر'].map((String validity) {
                  return DropdownMenuItem<String>(
                    value: validity,
                    child: Text(validity),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  setState(() {
                    selectedValidity = newValue!;
                  });
                },
                dropdownColor: Colors.blue.shade50,
                style: TextStyle(color: Colors.blue.shade800),
                borderRadius: BorderRadius.circular(12),
              ),
              const SizedBox(height: 12),
              TextField(
                controller: contactController,
                decoration: InputDecoration(
                  hintText: 'رقم التواصل',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide.none,
                  ),
                  filled: true,
                  fillColor: Colors.blue.shade50,
                ),
                keyboardType: TextInputType.phone,
              ),
              const SizedBox(height: 12),
              TextField(
                controller: detailsController,
                decoration: InputDecoration(
                  hintText: 'تفاصيل العرض',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide.none,
                  ),
                  filled: true,
                  fillColor: Colors.blue.shade50,
                ),
                maxLines: 3,
              ),
              const SizedBox(height: 12),
              ElevatedButton(
                onPressed: () {
                  imageUrls.add('https://cdn-icons-png.flaticon.com/512/3082/3082872.png');
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('تم تحميل الصورة')),
                  );
                  _confettiController.play();
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue.shade800,
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                  padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                ),
                child: const Text(
                  'تحميل صورة',
                  style: TextStyle(color: Colors.white, fontSize: 14),
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء', style: TextStyle(color: Colors.grey)),
          ),
          ElevatedButton(
            onPressed: () {
              if (quantityController.text.isEmpty ||
                  priceController.text.isEmpty ||
                  addressController.text.isEmpty ||
                  contactController.text.isEmpty ||
                  detailsController.text.isEmpty) {
                Fluttertoast.showToast(
                  msg: 'يرجى ملء جميع الحقول',
                  backgroundColor: Colors.red,
                  textColor: Colors.white,
                );
                return;
              }
              setState(() {
                equipmentItems.add(MarketItem(
                  id: DateTime.now().millisecondsSinceEpoch.toString(),
                  user: appState.currentUser?.name ?? 'مستخدم',
                  type: selectedEquipmentType,
                  imageUrls: imageUrls,
                  quantity: double.parse(quantityController.text),
                  price: double.parse(priceController.text),
                  address: addressController.text,
                  date: DateTime.now(),
                  validity: selectedValidity,
                  contactNumber: contactController.text,
                  details: detailsController.text,
                ));
                _confettiController.play();
                Fluttertoast.showToast(
                  msg: 'تم إضافة المعدات بنجاح',
                  backgroundColor: Colors.blue.shade800,
                  textColor: Colors.white,
                );
              });
              Navigator.pop(context);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue.shade800,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            ),
            child: const Text(
              'إضافة',
              style: TextStyle(color: Colors.white),
            ),
          ),
        ],
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        backgroundColor: Colors.blue.shade50,
      ),
    );
  }

  void _showAddPondDialog(BuildContext context) {
    final appState = Provider.of<AppState>(context, listen: false);
    if (!_canAddItems(appState)) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('غير مصرح'),
          content: const Text('هذه الميزة متاحة فقط للحكماء'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('إغلاق'),
            ),
          ],
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          backgroundColor: Colors.blue.shade50,
        ),
      );
      return;
    }

    final quantityController = TextEditingController();
    final priceController = TextEditingController();
    final addressController = TextEditingController();
    final contactController = TextEditingController();
    final descriptionController = TextEditingController();
    String selectedPondType = pondTypes.first;
    String selectedWaterSource = 'آبار';
    String selectedFiltrationSystem = 'نعم';
    String selectedStructures = 'نعم';
    String selectedApplicantStatus = 'مالك';
    String selectedRegistrationType = 'شهر عقاري';
    String selectedValidity = 'شهر';
    List<String> imageUrls = [];

    showDialog(
        context: context,
        builder: (context) => AlertDialog(
            title: Text(
              'إضافة بركة',
              style: TextStyle(color: Colors.blue.shade800, fontWeight: FontWeight.bold),
            ),
            content: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  DropdownButton<String>(
                    value: selectedPondType,
                    isExpanded: true,
                    items: pondTypes.map((String type) {
                      return DropdownMenuItem<String>(
                        value: type,
                        child: Text(type),
                      );
                    }).toList(),
                    onChanged: (String? newValue) {
                      setState(() {
                        selectedPondType = newValue!;
                      });
                    },
                    dropdownColor: Colors.blue.shade50,
                    style: TextStyle(color: Colors.blue.shade800),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  const SizedBox(height: 12),
                  TextField(
                    controller: quantityController,
                    decoration: InputDecoration(
                      hintText: 'المساحة (متر مربع)',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: BorderSide.none,
                      ),
                      filled: true,
                      fillColor: Colors.blue.shade50,
                    ),
                    keyboardType: TextInputType.number,
                  ),
                  const SizedBox(height: 12),
                  TextField(
                    controller: priceController,
                    decoration: InputDecoration(
                      hintText: 'السعر (ريال)',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: BorderSide.none,
                      ),
                      filled: true,
                      fillColor: Colors.blue.shade50,
                    ),
                    keyboardType: TextInputType.number,
                  ),
                  const SizedBox(height: 12),
                  TextField(
                    controller: addressController,
                    decoration: InputDecoration(
                      hintText: 'العنوان',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: BorderSide.none,
                      ),
                      filled: true,
                      fillColor: Colors.blue.shade50,
                      suffixIcon: IconButton(
                        icon: Icon(Icons.location_on, color: Colors.blue.shade800),
                        onPressed: () async {
                          try {
                            Position position = await Geolocator.getCurrentPosition();
                            addressController.text = 'موقع جغرافي: ${position.latitude}, ${position.longitude}';
                            Fluttertoast.showToast(
                              msg: 'تم تحديد الموقع',
                              backgroundColor: Colors.blue.shade800,
                              textColor: Colors.white,
                            );
                          } catch (e) {
                            Fluttertoast.showToast(
                              msg: 'خطأ في تحديد الموقع',
                              backgroundColor: Colors.red,
                              textColor: Colors.white,
                            );
                          }
                        },
                      ),
                    ),
                  ),
                  const SizedBox(height: 12),
                  DropdownButton<String>(
                    value: selectedValidity,
                    isExpanded: true,
                    items: ['أسبوع', 'أسبوعان', 'شهر'].map((String validity) {
                      return DropdownMenuItem<String>(
                        value: validity,
                        child: Text(validity),
                      );
                    }).toList(),
                    onChanged: (String? newValue) {
                      setState(() {
                        selectedValidity = newValue!;
                      });
                    },
                    dropdownColor: Colors.blue.shade50,
                    style: TextStyle(color: Colors.blue.shade800),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  const SizedBox(height: 12),
                  TextField(
                    controller: contactController,
                    decoration: InputDecoration(
                      hintText: 'رقم التواصل',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: BorderSide.none,
                      ),
                      filled: true,
                      fillColor: Colors.blue.shade50,
                    ),
                    keyboardType: TextInputType.phone,
                  ),
                  const SizedBox(height: 12),
                  TextField(
                    controller: descriptionController,
                    decoration: InputDecoration(
                      hintText: 'وصف البركة',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: BorderSide.none,
                      ),
                      filled: true,
                      fillColor: Colors.blue.shade50,
                    ),
                    maxLines: 3,
                  ),
                  const SizedBox(height: 12),
                  DropdownButton<String>(
                    value: selectedWaterSource,
                    isExpanded: true,
                    items: ['آبار', 'مياه نهر', 'مياه معالجة'].map((String source) {
                      return DropdownMenuItem<String>(
                        value: source,
                        child: Text(source),
                      );
                    }).toList(),
                    onChanged: (String? newValue) {
                      setState(() {
                        selectedWaterSource = newValue!;
                      });
                    },
                    dropdownColor: Colors.blue.shade50,
                    style: TextStyle(color: Colors.blue.shade800),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  const SizedBox(height: 12),
                  DropdownButton<String>(
                    value: selectedFiltrationSystem,
                    isExpanded: true,
                    items: ['نعم', 'لا'].map((String system) {
                      return DropdownMenuItem<String>(
                        value: system,
                        child: Text(system),
                      );
                    }).toList(),
                    onChanged: (String? newValue) {
                      setState(() {
                        selectedFiltrationSystem = newValue!;
                      });
                    },
                    dropdownColor: Colors.blue.shade50,
                    style: TextStyle(color: Colors.blue.shade800),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  const SizedBox(height: 12),
                  DropdownButton<String>(
                    value: selectedStructures,
                    isExpanded: true,
                    items: ['نعم', 'لا'].map((String structure) {
                      return DropdownMenuItem<String>(
                        value: structure,
                        child: Text(structure),
                      );
                    }).toList(),
                    onChanged: (String? newValue) {
                      setState(() {
                        selectedStructures = newValue!;
                      });
                    },
                    dropdownColor: Colors.blue.shade50,
                    style: TextStyle(color: Colors.blue.shade800),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  const SizedBox(height: 12),
                  DropdownButton<String>(
                    value: selectedApplicantStatus,
                    isExpanded: true,
                    items: ['مالك', 'مستأجر'].map((String status) {
                      return DropdownMenuItem<String>(
                        value: status,
                        child: Text(status),
                      );
                    }).toList(),
                    onChanged: (String? newValue) {
                      setState(() {
                        selectedApplicantStatus = newValue!;
                      });
                    },
                    dropdownColor: Colors.blue.shade50,
                    style: TextStyle(color: Colors.blue.shade800),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  const SizedBox(height: 12),
                  DropdownButton<String>(
                    value: selectedRegistrationType,
                    isExpanded: true,
                    items: ['شهر عقاري', 'غير مسجل'].map((String type) {
                      return DropdownMenuItem<String>(
                        value: type,
                        child: Text(type),
                      );
                    }).toList(),
                    onChanged: (String? newValue) {
                      setState(() {
                        selectedRegistrationType = newValue!;
                      });
                    },
                    dropdownColor: Colors.blue.shade50,
                    style: TextStyle(color: Colors.blue.shade800),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  const SizedBox(height: 12),
                  ElevatedButton(
                    onPressed: () {
                      imageUrls.add('https://cdn-icons-png.flaticon.com/512/3097/3097973.png');
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('تم تحميل الصورة')),
                      );
                      _confettiController.play();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue.shade800,
                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                    ),
                    child: const Text(
                      'تحميل صورة',
                      style: TextStyle(color: Colors.white, fontSize: 14),
                    ),
                  ),
                ],
              ),
            ),
            actions: [
            TextButton(
            onPressed: () => Navigator.pop(context),
    child: const Text('إلغاء', style: TextStyle(color: Colors.grey)),
    ),
    ElevatedButton(
    onPressed: () {
    if (quantityController.text.isEmpty ||
    priceController.text.isEmpty ||
    addressController.text.isEmpty ||
    contactController.text.isEmpty ||
    descriptionController.text.isEmpty) {
    Fluttertoast.showToast(
    msg: 'يرجى ملء جميع الحقول',
    backgroundColor: Colors.red,
    textColor: Colors.white,
    );
    return;
    }
    setState(() {
    pondItems.add(MarketItem(
    id: DateTime.now().millisecondsSinceEpoch.toString(),
    user: appState.currentUser?.name ?? 'مستخدم',
    type: 'للبيع',
    imageUrls: imageUrls,
    quantity: double.parse(quantityController.text),
    price: double.parse(priceController.text),
    address: addressController.text,
    date: DateTime.now(),
    validity: selectedValidity,
    contactNumber: contactController.text,
    details: descriptionController.text,
    applicantStatus: selectedApplicantStatus,
    registrationType: selectedRegistrationType,
    description: descriptionController.text,
    waterSource: selectedWaterSource,
    pondType: selectedPondType,
    filtrationSystem: selectedFiltrationSystem,
    structures: selectedStructures,
    structuresDetails: selectedStructures,
    ));
    _confettiController.play();
    Fluttertoast.showToast(
      msg: 'تم إضافة البركة بنجاح',
      backgroundColor: Colors.blue.shade800,
      textColor: Colors.white,
    );
    });
    Navigator.pop(context);
    },
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.blue.shade800,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
      child: const Text(
        'إضافة',
        style: TextStyle(color: Colors.white),
      ),
    ),
            ],
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          backgroundColor: Colors.blue.shade50,
        ),
    );
  }

  void _showCommentDialog(BuildContext context, Post post) {
    final commentController = TextEditingController();
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'إضافة تعليق',
          style: TextStyle(color: Colors.blue.shade800, fontWeight: FontWeight.bold),
        ),
        content: TextField(
          controller: commentController,
          decoration: InputDecoration(
            hintText: 'اكتب تعليقك...',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide.none,
            ),
            filled: true,
            fillColor: Colors.blue.shade50,
          ),
          maxLines: 3,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء', style: TextStyle(color: Colors.grey)),
          ),
          ElevatedButton(
            onPressed: () {
              if (commentController.text.isNotEmpty) {
                setState(() {
                  post.comments += 1;
                  _confettiController.play();
                  Fluttertoast.showToast(
                    msg: 'تم إضافة التعليق',
                    backgroundColor: Colors.blue.shade800,
                    textColor: Colors.white,
                  );
                });
                Navigator.pop(context);
              } else {
                Fluttertoast.showToast(
                  msg: 'يرجى إدخال نص التعليق',
                  backgroundColor: Colors.red,
                  textColor: Colors.white,
                );
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue.shade800,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            ),
            child: const Text(
              'إضافة',
              style: TextStyle(color: Colors.white),
            ),
          ),
        ],
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        backgroundColor: Colors.blue.shade50,
      ),
    );
  }

  void _showSearchDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'البحث',
          style: TextStyle(color: Colors.blue.shade800, fontWeight: FontWeight.bold),
        ),
        content: TextField(
          controller: _searchController,
          decoration: InputDecoration(
            hintText: 'ابحث عن الأسماك أو المعدات...',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide.none,
            ),
            filled: true,
            fillColor: Colors.blue.shade50,
            suffixIcon: Icon(Icons.search, color: Colors.blue.shade800),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء', style: TextStyle(color: Colors.grey)),
          ),
          ElevatedButton(
            onPressed: () {
              if (_searchController.text.isNotEmpty) {
                Fluttertoast.showToast(
                  msg: 'جاري البحث عن ${_searchController.text}',
                  backgroundColor: Colors.blue.shade800,
                  textColor: Colors.white,
                );
                Navigator.pop(context);
              } else {
                Fluttertoast.showToast(
                  msg: 'يرجى إدخال نص البحث',
                  backgroundColor: Colors.red,
                  textColor: Colors.white,
                );
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue.shade800,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            ),
            child: const Text(
              'بحث',
              style: TextStyle(color: Colors.white),
            ),
          ),
        ],
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        backgroundColor: Colors.blue.shade50,
      ),
    );
  }

  void _showQuickActions(BuildContext context) {
    final appState = Provider.of<AppState>(context, listen: false);
    if (!_canAddItems(appState)) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('غير مصرح'),
          content: const Text('هذه الميزة متاحة فقط للحكماء'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('إغلاق'),
            ),
          ],
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          backgroundColor: Colors.blue.shade50,
        ),
      );
      return;
    }

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.blue.shade50,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: Icon(Icons.pets, color: Colors.blue.shade800),
              title: const Text('إضافة سمك'),
              onTap: () {
                Navigator.pop(context);
                _showAddFishDialog(context);
              },
            ),
            ListTile(
              leading: Icon(Icons.build, color: Colors.blue.shade800),
              title: const Text('إضافة معدات'),
              onTap: () {
                Navigator.pop(context);
                _showAddEquipmentDialog(context);
              },
            ),
            ListTile(
              leading: Icon(Icons.pool, color: Colors.blue.shade800),
              title: const Text('إضافة بركة'),
              onTap: () {
                Navigator.pop(context);
                _showAddPondDialog(context);
              },
            ),
          ],
        ),
      ),
    );
  }
}


