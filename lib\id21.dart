import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:share_plus/share_plus.dart';
import 'package:animated_text_kit/animated_text_kit.dart';
import 'package:confetti/confetti.dart';
import 'package:intl/intl.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:geolocator/geolocator.dart';
import 'package:provider/provider.dart';
import 'dart:io';
import 'appstate.dart';

class ApplianceGroup {
  final String id;
  final String name;
  final String imageUrl;
  final List<Video> videos;
  final List<Article> articles;
  final List<FAQ> faqs;
  final String maintenanceGuide;
  final List<String> galleryImages;

  ApplianceGroup({
    required this.id,
    required this.name,
    required this.imageUrl,
    required this.videos,
    required this.articles,
    required this.faqs,
    required this.maintenanceGuide,
    required this.galleryImages,
  });
}

class Video {
  final String title;
  final String url;

  Video({required this.title, required this.url});
}

class Article {
  final String title;
  final String content;

  Article({required this.title, required this.content});
}

class FAQ {
  final String question;
  final String answer;

  FAQ({required this.question, required this.answer});
}

class Post {
  final String id;
  final String user;
  final String content;
  final String? imageUrl;
  int likes;
  List<String> comments;

  Post({
    required this.id,
    required this.user,
    required this.content,
    this.imageUrl,
    this.likes = 0,
    this.comments = const [],
  });
}

class NotificationItem {
  final String title;
  final String description;
  final DateTime date;

  NotificationItem({
    required this.title,
    required this.description,
    required this.date,
  });
}

class MaintenanceService {
  final String id;
  final String user;
  final String type;
  final List<String> imageUrls;
  final double price;
  final String address;
  final DateTime date;
  final String validity;
  final String contactNumber;
  final String details;
  final String? serviceType;
  final String? applianceCondition;
  final String? serviceLocation;

  MaintenanceService({
    required this.id,
    required this.user,
    required this.type,
    required this.imageUrls,
    required this.price,
    required this.address,
    required this.date,
    required this.validity,
    required this.contactNumber,
    required this.details,
    this.serviceType,
    this.applianceCondition,
    this.serviceLocation,
  });
}

class DeviceMaintenancePage extends StatefulWidget {
  const DeviceMaintenancePage({super.key});

  @override
  State<DeviceMaintenancePage> createState() => _DeviceMaintenancePageState();
}

class _DeviceMaintenancePageState extends State<DeviceMaintenancePage> with SingleTickerProviderStateMixin {
  late ConfettiController _confettiController;
  final List<ApplianceGroup> appliances = [
    ApplianceGroup(
      id: '1',
      name: 'الثلاجات',
      imageUrl: 'https://cdn-icons-png.flaticon.com/512/1234/1234567.png',
      videos: [
        Video(title: 'صيانة الثلاجات', url: 'https://www.youtube.com/watch?v=fridge1'),
        Video(title: 'مشاكل الثلاجات الشائعة', url: 'https://www.youtube.com/watch?v=fridge2'),
      ],
      articles: [
        Article(title: 'دليل صيانة الثلاجات', content: 'كيفية الحفاظ على ثلاجتك...'),
      ],
      faqs: [
        FAQ(question: 'لماذا لا تبرد الثلاجة؟', answer: 'تحقق من منظم الحرارة أو تسرب الفريون...'),
      ],
      maintenanceGuide: 'معلومات الصيانة: تنظيف المكثف، فحص الأبواب...',
      galleryImages: ['https://cdn-icons-png.flaticon.com/512/1234/1234567.png'],
    ),
    ApplianceGroup(
      id: '2',
      name: 'الغسالات',
      imageUrl: 'https://cdn-icons-png.flaticon.com/512/2345/2345678.png',
      videos: [
        Video(title: 'إصلاح الغسالات', url: 'https://www.youtube.com/watch?v=washer1'),
      ],
      articles: [
        Article(title: 'صيانة الغسالات', content: 'نصائح للحفاظ على أداء الغسالة...'),
      ],
      faqs: [
        FAQ(question: 'لماذا تهتز الغسالة؟', answer: 'تحقق من توازن الحمولة أو القواعد...'),
      ],
      maintenanceGuide: 'معلومات الصيانة: تنظيف الفلتر، فحص الأنابيب...',
      galleryImages: ['https://cdn-icons-png.flaticon.com/512/2345/2345678.png'],
    ),
  ];

  final List<Post> posts = [
    Post(
      id: '1',
      user: 'أحمد',
      content: 'قمت بصيانة ثلاجتي بنجاح!',
      imageUrl: 'https://cdn-icons-png.flaticon.com/512/1234/1234567.png',
    ),
    Post(
      id: '2',
      user: 'فاطمة',
      content: 'نصائح لصيانة الغسالات.',
    ),
  ];

  final List<NotificationItem> notifications = [
    NotificationItem(
      title: 'تذكير بالصيانة',
      description: 'موعد صيانة الثلاجة غداً',
      date: DateTime.now().add(const Duration(days: 1)),
    ),
  ];

  final List<MaintenanceService> serviceItems = [
    MaintenanceService(
      id: '1',
      user: 'محمد',
      type: 'إصلاح ثلاجات',
      imageUrls: ['https://cdn-icons-png.flaticon.com/512/1234/1234567.png'],
      price: 200.0,
      address: 'الرياض',
      date: DateTime.now().subtract(const Duration(days: 1)),
      validity: 'أسبوع',
      contactNumber: '1234567890',
      details: 'خدمة إصلاح ثلاجات عالية الجودة',
      serviceType: 'إصلاح في الموقع',
      applianceCondition: 'مستعمل',
      serviceLocation: 'المنزل',
    ),
  ];

  final List<MaintenanceService> sparePartItems = [
    MaintenanceService(
      id: '2',
      user: 'خالد',
      type: 'قطع غيار',
      imageUrls: ['https://cdn-icons-png.flaticon.com/512/3456/3456789.png'],
      price: 150.0,
      address: 'جدة',
      date: DateTime.now().subtract(const Duration(days: 1)),
      validity: 'شهر',
      contactNumber: '0987654321',
      details: 'قطع غيار أصلية للغسالات',
    ),
  ];

  final List<String> applianceTypes = [
    'ثلاجات',
    'غسالات',
    'مكيفات هواء',
    'ميكروويف',
    'مكانس كهربائية',
    'تلفزيونات',
    'أفران',
    'غسالات صحون',
    'مراوح',
    'مدافئ',
  ];

  final List<String> serviceTypes = [
    'إصلاح في الموقع',
    'إصلاح في الورشة',
    'صيانة وقائية',
    'تركيب',
  ];

  final TextEditingController _postController = TextEditingController();
  final TextEditingController _searchController = TextEditingController();
  final TextEditingController _healthCheckController = TextEditingController();

  late AnimationController _fabAnimationController;
  late Animation<double> _fabScaleAnimation;
  ImageProvider? _selectedMedia;

  int userPoints = 100;

  @override
  void initState() {
    super.initState();
    _confettiController = ConfettiController(duration: const Duration(seconds: 1));
    _fabAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    _fabScaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fabAnimationController,
      curve: Curves.easeOut,
    ));
    _fabAnimationController.forward();
  }

  @override
  void dispose() {
    _confettiController.dispose();
    _fabAnimationController.dispose();
    _postController.dispose();
    _searchController.dispose();
    _healthCheckController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Scaffold(
          appBar: AppBar(
            title: AnimatedTextKit(
              animatedTexts: [
                TypewriterAnimatedText(
                  'موسوعة صيانة الأجهزة',
                  textStyle: const TextStyle(fontSize: 22, fontWeight: FontWeight.bold, color: Colors.white),
                  speed: const Duration(milliseconds: 100),
                ),
              ],
              totalRepeatCount: 1,
            ),
            backgroundColor: Colors.blue.shade700,
            actions: [
              IconButton(
                icon: const Icon(Icons.search, color: Colors.white),
                onPressed: () => _showSearchDialog(context),
                tooltip: 'بحث',
              ),
            ],
            flexibleSpace: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [Colors.blue.shade700, Colors.blue.shade300],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
              ),
            ),
            elevation: 4,
          ),
          body: RefreshIndicator(
            onRefresh: () async {
              await Future.delayed(const Duration(seconds: 1));
              setState(() {});
              _confettiController.play();
              Fluttertoast.showToast(msg: 'تم تحديث الصفحة', backgroundColor: Colors.blue.shade700);
            },
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: AnimationLimiter(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: AnimationConfiguration.toStaggeredList(
                    duration: const Duration(milliseconds: 500),
                    childAnimationBuilder: (widget) => SlideAnimation(
                      verticalOffset: 50.0,
                      child: FadeInAnimation(child: widget),
                    ),
                    children: [
                      _buildSectionTitle('أنواع الأجهزة'),
                      const SizedBox(height: 16),
                      _buildApplianceCards(),
                      const SizedBox(height: 24),
                      _buildSectionTitle('سوق خدمات الصيانة'),
                      const SizedBox(height: 12),
                      _buildMarketplaceSection(),
                      const SizedBox(height: 24),
                      _buildSectionTitle('منشورات المجتمع'),
                      const SizedBox(height: 12),
                      _buildPostInput(),
                      const SizedBox(height: 12),
                      _buildPostsList(),
                    ],
                  ),
                ),
              ),
            ),
          ),
          floatingActionButton: ScaleTransition(
            scale: _fabScaleAnimation,
            child: FloatingActionButton(
              onPressed: () => _showQuickActions(context),
              tooltip: 'إجراءات سريعة',
              backgroundColor: Colors.blue.shade700,
              elevation: 6,
              child: const Icon(Icons.add, color: Colors.white),
            ),
          ),
        ),
        Align(
          alignment: Alignment.topCenter,
          child: ConfettiWidget(
            confettiController: _confettiController,
            blastDirectionality: BlastDirectionality.explosive,
            particleDrag: 0.05,
            emissionFrequency: 0.05,
            numberOfParticles: 50,
            gravity: 0.05,
            colors: const [Colors.blue, Colors.green, Colors.yellow, Colors.red],
          ),
        ),
      ],
    );
  }

  Widget _buildSectionTitle(String title) {
    return AnimatedTextKit(
      animatedTexts: [
        FadeAnimatedText(
          title,
          textStyle: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: Colors.blue.shade700,
          ),
        ),
      ],
      totalRepeatCount: 1,
    );
  }

  Widget _buildApplianceCards() {
    return SizedBox(
      height: 220,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: appliances.length,
        itemBuilder: (context, index) {
          final appliance = appliances[index];
          return AnimationConfiguration.staggeredList(
            position: index,
            duration: const Duration(milliseconds: 600),
            child: SlideAnimation(
              horizontalOffset: 50.0,
              child: FadeInAnimation(
                child: GestureDetector(
                  onTap: () => _showApplianceDetails(context, appliance),
                  child: Padding(
                    padding: const EdgeInsets.only(right: 16.0, left: 8.0),
                    child: Card(
                      elevation: 8,
                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
                      child: Container(
                        width: 160,
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [Colors.blue.shade200, Colors.blue.shade400],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ),
                          borderRadius: BorderRadius.circular(16),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.blue.withOpacity(0.3),
                              blurRadius: 10,
                              offset: const Offset(0, 5),
                            ),
                          ],
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            ClipRRect(
                              borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
                              child: CachedNetworkImage(
                                imageUrl: appliance.imageUrl,
                                height: 100,
                                width: double.infinity,
                                fit: BoxFit.cover,
                                placeholder: (context, url) => _buildShimmerPlaceholder(),
                                errorWidget: (context, url, error) => const Icon(Icons.error),
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: AnimatedTextKit(
                                animatedTexts: [
                                  TypewriterAnimatedText(
                                    appliance.name,
                                    textStyle: const TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.white,
                                    ),
                                    speed: const Duration(milliseconds: 100),
                                  ),
                                ],
                                totalRepeatCount: 1,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildMarketplaceSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildMarketplaceCategory('خدمات الصيانة', serviceItems, _showAddServiceDialog, Icons.build),
        const SizedBox(height: 24),
        _buildMarketplaceCategory('قطع الغيار', sparePartItems, _showAddSparePartDialog, Icons.construction),
      ],
    );
  }

  Widget _buildMarketplaceCategory(String title, List<MaintenanceService> items, Function(BuildContext) addDialog, IconData icon) {
    return Stack(
      children: [
        Card(
          elevation: 6,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.blue.shade100, Colors.blue.shade50],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(icon, color: Colors.blue.shade700, size: 30),
                      const SizedBox(width: 8),
                      Text(
                        title,
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(color: Colors.blue.shade700),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  if (title == 'خدمات الصيانة') ...[
                    Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children: [
                        Chip(label: const Text('إصلاح ثلاجات'), backgroundColor: Colors.blue.shade100),
                        Chip(label: const Text('إصلاح غسالات'), backgroundColor: Colors.blue.shade100),
                        Chip(label: const Text('صيانة مكيفات'), backgroundColor: Colors.blue.shade100),
                        Chip(label: const Text('إصلاح أفران'), backgroundColor: Colors.blue.shade100),
                        Chip(label: const Text('تركيب أجهزة'), backgroundColor: Colors.blue.shade100),
                      ],
                    ),
                    const SizedBox(height: 12),
                  ],
                  SizedBox(
                    height: 300,
                    child: ListView.builder(
                      scrollDirection: Axis.horizontal,
                      itemCount: items.length,
                      itemBuilder: (context, index) {
                        final item = items[index];
                        return AnimationConfiguration.staggeredList(
                          position: index,
                          duration: const Duration(milliseconds: 600),
                          child: SlideAnimation(
                            horizontalOffset: 50.0,
                            child: FadeInAnimation(
                              child: _buildMarketCard(item),
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        Positioned(
          bottom: 16,
          right: 16,
          child: ScaleTransition(
            scale: _fabScaleAnimation,
            child: FloatingActionButton(
              onPressed: () => addDialog(context),
              backgroundColor: Colors.blue.shade700,
              tooltip: 'إضافة إلى $title',
              elevation: 6,
              child: const Icon(Icons.add, color: Colors.white),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildMarketCard(MaintenanceService item) {
    return Padding(
      padding: const EdgeInsets.only(right: 12.0),
      child: MouseRegion(
        onEnter: (_) => setState(() {}),
        onExit: (_) => setState(() {}),
        child: Card(
          elevation: 6,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          child: Container(
            width: 200,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.blue.withOpacity(0.2),
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ClipRRect(
                  borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
                  child: item.imageUrls.isNotEmpty
                      ? CachedNetworkImage(
                    imageUrl: item.imageUrls.first,
                    width: 200,
                    height: 120,
                    fit: BoxFit.cover,
                    placeholder: (context, url) => _buildShimmerPlaceholder(),
                    errorWidget: (context, url, error) => const Icon(Icons.error),
                  )
                      : Container(
                    width: 200,
                    height: 120,
                    color: Colors.grey.shade300,
                    child: Icon(Icons.image, color: Colors.grey.shade600),
                  ),
                ),
                Flexible(
                  child: SingleChildScrollView(
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(item.type, style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16)),
                          Text('السعر: ${item.price} ريال'),
                          Text('العنوان: ${item.address}'),
                          Text('رقم التواصل: ${item.contactNumber}'),
                          Text('التفاصيل: ${item.details}'),
                          Text(
                            'التاريخ: ${DateFormat('dd/MM/yyyy').format(item.date)}',
                            style: const TextStyle(fontSize: 12, color: Colors.grey),
                          ),
                          if (item.serviceType != null) ...[
                            Text('نوع الخدمة: ${item.serviceType}'),
                            Text('حالة الجهاز: ${item.applianceCondition}'),
                            Text('مكان الخدمة: ${item.serviceLocation}'),
                          ],
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildShimmerPlaceholder() {
    return Container(
      width: double.infinity,
      height: 120,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.grey.shade300, Colors.grey.shade100, Colors.grey.shade300],
          stops: const [0.1, 0.3, 0.4],
          begin: const Alignment(-1.0, -0.3),
          end: const Alignment(1.0, 0.3),
          tileMode: TileMode.repeated,
        ),
      ),
    );
  }

  void _showAddServiceDialog(BuildContext context) {
    final priceController = TextEditingController();
    final addressController = TextEditingController();
    final contactController = TextEditingController();
    final detailsController = TextEditingController();
    String selectedApplianceType = applianceTypes.first;
    String selectedServiceType = serviceTypes.first;
    String selectedApplianceCondition = 'مستعمل';
    String selectedServiceLocation = 'المنزل';
    String selectedValidity = 'أسبوع';
    List<String> imageUrls = [];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('إضافة خدمة صيانة', style: TextStyle(color: Colors.blue.shade700)),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              DropdownButton<String>(
                value: selectedApplianceType,
                isExpanded: true,
                items: applianceTypes.map((String appliance) {
                  return DropdownMenuItem<String>(
                    value: appliance,
                    child: Text(appliance),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  setState(() {
                    selectedApplianceType = newValue!;
                  });
                },
                dropdownColor: Colors.blue.shade50,
              ),
              const SizedBox(height: 8),
              DropdownButton<String>(
                value: selectedServiceType,
                isExpanded: true,
                items: serviceTypes.map((String service) {
                  return DropdownMenuItem<String>(
                    value: service,
                    child: Text(service),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  setState(() {
                    selectedServiceType = newValue!;
                  });
                },
                dropdownColor: Colors.blue.shade50,
              ),
              const SizedBox(height: 8),
              DropdownButton<String>(
                value: selectedApplianceCondition,
                isExpanded: true,
                items: ['جديد', 'مستعمل'].map((String condition) {
                  return DropdownMenuItem<String>(
                    value: condition,
                    child: Text(condition),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  setState(() {
                    selectedApplianceCondition = newValue!;
                  });
                },
                dropdownColor: Colors.blue.shade50,
              ),
              const SizedBox(height: 8),
              DropdownButton<String>(
                value: selectedServiceLocation,
                isExpanded: true,
                items: ['المنزل', 'الورشة'].map((String location) {
                  return DropdownMenuItem<String>(
                    value: location,
                    child: Text(location),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  setState(() {
                    selectedServiceLocation = newValue!;
                  });
                },
                dropdownColor: Colors.blue.shade50,
              ),
              const SizedBox(height: 8),
              TextField(
                controller: priceController,
                decoration: InputDecoration(
                  hintText: 'السعر (ريال)',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.blue.shade50,
                ),
                keyboardType: TextInputType.number,
              ),
              const SizedBox(height: 8),
              TextField(
                controller: addressController,
                decoration: InputDecoration(
                  hintText: 'العنوان',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.blue.shade50,
                  suffixIcon: IconButton(
                    icon: Icon(Icons.location_on, color: Colors.blue.shade700),
                    onPressed: () async {
                      Position position = await Geolocator.getCurrentPosition();
                      addressController.text = 'موقع جغرافي: ${position.latitude}, ${position.longitude}';
                    },
                  ),
                ),
              ),
              const SizedBox(height: 8),
              DropdownButton<String>(
                value: selectedValidity,
                isExpanded: true,
                items: ['أسبوع', 'أسبوعان', 'شهر'].map((String validity) {
                  return DropdownMenuItem<String>(
                    value: validity,
                    child: Text(validity),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  setState(() {
                    selectedValidity = newValue!;
                  });
                },
                dropdownColor: Colors.blue.shade50,
              ),
              const SizedBox(height: 8),
              TextField(
                controller: contactController,
                decoration: InputDecoration(
                  hintText: 'رقم التواصل',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.blue.shade50,
                ),
                keyboardType: TextInputType.phone,
              ),
              const SizedBox(height: 8),
              TextField(
                controller: detailsController,
                decoration: InputDecoration(
                  hintText: 'تفاصيل الخدمة',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.blue.shade50,
                ),
                maxLines: 3,
              ),
              const SizedBox(height: 8),
              ElevatedButton(
                onPressed: () {
                  if (imageUrls.length < 4) {
                    imageUrls.addAll(List.generate(4 - imageUrls.length, (index) => 'https://cdn-icons-png.flaticon.com/512/1234/1234567.png'));
                  }
                  Fluttertoast.showToast(msg: 'تم رفع ${imageUrls.length} صور');
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue.shade700,
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                ),
                child: Text('رفع الصور (${imageUrls.length}/4)'),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              if (priceController.text.isNotEmpty &&
                  addressController.text.isNotEmpty &&
                  contactController.text.isNotEmpty &&
                  detailsController.text.isNotEmpty &&
                  imageUrls.length >= 4) {
                setState(() {
                  serviceItems.add(MaintenanceService(
                    id: DateTime.now().millisecondsSinceEpoch.toString(),
                    user: 'Current User',
                    type: selectedApplianceType,
                    imageUrls: imageUrls,
                    price: double.parse(priceController.text),
                    address: addressController.text,
                    date: DateTime.now(),
                    validity: selectedValidity,
                    contactNumber: contactController.text,
                    details: detailsController.text,
                    serviceType: selectedServiceType,
                    applianceCondition: selectedApplianceCondition,
                    serviceLocation: selectedServiceLocation,
                  ));
                });
                _confettiController.play();
                Fluttertoast.showToast(msg: 'تم إضافة الخدمة بنجاح', backgroundColor: Colors.blue.shade700);
                Navigator.pop(context);
              } else {
                Fluttertoast.showToast(msg: 'يرجى ملء جميع الحقول ورفع 4 صور', backgroundColor: Colors.red);
              }
            },
            child: const Text('إضافة'),
          ),
        ],
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        backgroundColor: Colors.blue.shade50,
      ),
    );
  }

  void _showAddSparePartDialog(BuildContext context) {
    final priceController = TextEditingController();
    final addressController = TextEditingController();
    final contactController = TextEditingController();
    final detailsController = TextEditingController();
    String selectedApplianceType = applianceTypes.first;
    String selectedValidity = 'أسبوع';
    List<String> imageUrls = [];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('إضافة قطعة غيار', style: TextStyle(color: Colors.blue.shade700)),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              DropdownButton<String>(
                value: selectedApplianceType,
                isExpanded: true,
                items: applianceTypes.map((String appliance) {
                  return DropdownMenuItem<String>(
                    value: appliance,
                    child: Text(appliance),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  setState(() {
                    selectedApplianceType = newValue!;
                  });
                },
                dropdownColor: Colors.blue.shade50,
              ),
              const SizedBox(height: 8),
              TextField(
                controller: priceController,
                decoration: InputDecoration(
                  hintText: 'السعر (ريال)',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.blue.shade50,
                ),
                keyboardType: TextInputType.number,
              ),
              const SizedBox(height: 8),
              TextField(
                controller: addressController,
                decoration: InputDecoration(
                  hintText: 'العنوان',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.blue.shade50,
                  suffixIcon: IconButton(
                    icon: Icon(Icons.location_on, color: Colors.blue.shade700),
                    onPressed: () async {
                      Position position = await Geolocator.getCurrentPosition();
                      addressController.text = 'موقع جغرافي: ${position.latitude}, ${position.longitude}';
                    },
                  ),
                ),
              ),
              const SizedBox(height: 8),
              DropdownButton<String>(
                value: selectedValidity,
                isExpanded: true,
                items: ['أسبوع', 'أسبوعان', 'شهر'].map((String validity) {
                  return DropdownMenuItem<String>(
                    value: validity,
                    child: Text(validity),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  setState(() {
                    selectedValidity = newValue!;
                  });
                },
                dropdownColor: Colors.blue.shade50,
              ),
              const SizedBox(height: 8),
              TextField(
                controller: contactController,
                decoration: InputDecoration(
                  hintText: 'رقم التواصل',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.blue.shade50,
                ),
                keyboardType: TextInputType.phone,
              ),
              const SizedBox(height: 8),
              TextField(
                controller: detailsController,
                decoration: InputDecoration(
                  hintText: 'تفاصيل قطعة الغيار',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.blue.shade50,
                ),
                maxLines: 3,
              ),
              const SizedBox(height: 8),
              ElevatedButton(
                onPressed: () {
                  if (imageUrls.length < 4) {
                    imageUrls.addAll(List.generate(4 - imageUrls.length, (index) => 'https://cdn-icons-png.flaticon.com/512/3456/3456789.png'));
                  }
                  Fluttertoast.showToast(msg: 'تم رفع ${imageUrls.length} صور');
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue.shade700,
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                ),
                child: Text('رفع الصور (${imageUrls.length}/4)'),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              if (priceController.text.isNotEmpty &&
                  addressController.text.isNotEmpty &&
                  contactController.text.isNotEmpty &&
                  detailsController.text.isNotEmpty &&
                  imageUrls.length >= 4) {
                setState(() {
                  sparePartItems.add(MaintenanceService(
                    id: DateTime.now().millisecondsSinceEpoch.toString(),
                    user: 'Current User',
                    type: selectedApplianceType,
                    imageUrls: imageUrls,
                    price: double.parse(priceController.text),
                    address: addressController.text,
                    date: DateTime.now(),
                    validity: selectedValidity,
                    contactNumber: contactController.text,
                    details: detailsController.text,
                  ));
                });
                _confettiController.play();
                Fluttertoast.showToast(msg: 'تم إضافة قطعة الغيار بنجاح', backgroundColor: Colors.blue.shade700);
                Navigator.pop(context);
              } else {
                Fluttertoast.showToast(msg: 'يرجى ملء جميع الحقول ورفع 4 صور', backgroundColor: Colors.red);
              }
            },
            child: const Text('إضافة'),
          ),
        ],
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        backgroundColor: Colors.blue.shade50,
      ),
    );
  }

  void _showAddNewApplianceTypeDialog(BuildContext context) {
    final nameController = TextEditingController();
    final imageUrlController = TextEditingController();
    final videoTitleController = TextEditingController();
    final videoUrlController = TextEditingController();
    final articleTitleController = TextEditingController();
    final articleContentController = TextEditingController();
    final faqQuestionController = TextEditingController();
    final faqAnswerController = TextEditingController();
    final maintenanceGuideController = TextEditingController();
    final galleryImageController = TextEditingController();
    List<String> galleryImages = [];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('إضافة نوع جهاز جديد', style: TextStyle(color: Colors.blue.shade700)),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: nameController,
                decoration: InputDecoration(
                  hintText: 'اسم الجهاز',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.blue.shade50,
                ),
              ),
              const SizedBox(height: 8),
              TextField(
                controller: imageUrlController,
                decoration: InputDecoration(
                  hintText: 'رابط صورة الجهاز',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.blue.shade50,
                ),
              ),
              const SizedBox(height: 8),
              TextField(
                controller: videoTitleController,
                decoration: InputDecoration(
                  hintText: 'عنوان الفيديو',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.blue.shade50,
                ),
              ),
              const SizedBox(height: 8),
              TextField(
                controller: videoUrlController,
                decoration: InputDecoration(
                  hintText: 'رابط الفيديو',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.blue.shade50,
                ),
              ),
              const SizedBox(height: 8),
              TextField(
                controller: articleTitleController,
                decoration: InputDecoration(
                  hintText: 'عنوان المقالة',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.blue.shade50,
                ),
              ),
              const SizedBox(height: 8),
              TextField(
                controller: articleContentController,
                decoration: InputDecoration(
                  hintText: 'محتوى المقالة',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.blue.shade50,
                ),
                maxLines: 3,
              ),
              const SizedBox(height: 8),
              TextField(
                controller: faqQuestionController,
                decoration: InputDecoration(
                  hintText: 'السؤال',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.blue.shade50,
                ),
              ),
              const SizedBox(height: 8),
              TextField(
                controller: faqAnswerController,
                decoration: InputDecoration(
                  hintText: 'الإجابة',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.blue.shade50,
                ),
                maxLines: 3,
              ),
              const SizedBox(height: 8),
              TextField(
                controller: maintenanceGuideController,
                decoration: InputDecoration(
                  hintText: 'دليل الصيانة',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.blue.shade50,
                ),
                maxLines: 3,
              ),
              const SizedBox(height: 8),
              TextField(
                controller: galleryImageController,
                decoration: InputDecoration(
                  hintText: 'رابط صورة المعرض',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.blue.shade50,
                ),
              ),
              const SizedBox(height: 8),
              ElevatedButton(
                onPressed: () {
                  if (galleryImageController.text.isNotEmpty) {
                    galleryImages.add(galleryImageController.text);
                    galleryImageController.clear();
                    Fluttertoast.showToast(msg: 'تم إضافة صورة إلى المعرض (${galleryImages.length})');
                  }
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue.shade700,
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                ),
                child: Text('إضافة صورة إلى المع butch ({galleryImages.length})'),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              if (nameController.text.isNotEmpty &&
                  imageUrlController.text.isNotEmpty &&
                  videoTitleController.text.isNotEmpty &&
                  videoUrlController.text.isNotEmpty &&
                  articleTitleController.text.isNotEmpty &&
                  articleContentController.text.isNotEmpty &&
                  faqQuestionController.text.isNotEmpty &&
                  faqAnswerController.text.isNotEmpty &&
                  maintenanceGuideController.text.isNotEmpty &&
                  galleryImages.isNotEmpty) {
                setState(() {
                  appliances.add(ApplianceGroup(
                    id: DateTime.now().millisecondsSinceEpoch.toString(),
                    name: nameController.text,
                    imageUrl: imageUrlController.text,
                    videos: [Video(title: videoTitleController.text, url: videoUrlController.text)],
                    articles: [Article(title: articleTitleController.text, content: articleContentController.text)],
                    faqs: [FAQ(question: faqQuestionController.text, answer: faqAnswerController.text)],
                    maintenanceGuide: maintenanceGuideController.text,
                    galleryImages: galleryImages,
                  ));
                });
                _confettiController.play();
                Fluttertoast.showToast(msg: 'تم إضافة نوع الجهاز بنجاح', backgroundColor: Colors.blue.shade700);
                Navigator.pop(context);
              } else {
                Fluttertoast.showToast(msg: 'يرجى ملء جميع الحقول وإضافة صورة واحدة على الأقل', backgroundColor: Colors.red);
              }
            },
            child: const Text('إضافة'),
          ),
        ],
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        backgroundColor: Colors.blue.shade50,
      ),
    );
  }

  Widget _buildPostInput() {
    return Card(
      elevation: 6,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CircleAvatar(
                  backgroundColor: Colors.blue.shade200,
                  child: const Icon(Icons.person, color: Colors.white, size: 20),
                  radius: 20,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: TextField(
                    controller: _postController,
                    decoration: InputDecoration(
                      hintText: 'ما الذي تريد مشاركته عن الصيانة؟',
                      hintStyle: TextStyle(color: Colors.grey.shade600, fontSize: 15),
                      border: InputBorder.none,
                      contentPadding: const EdgeInsets.symmetric(vertical: 8),
                    ),
                    maxLines: 5,
                    minLines: 1,
                    style: const TextStyle(fontSize: 15),
                  ),
                ),
              ],
            ),
            if (_selectedMedia != null)
              Container(
                margin: const EdgeInsets.only(top: 12),
                height: 200,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  image: DecorationImage(
                    image: _selectedMedia!,
                    fit: BoxFit.cover,
                  ),
                ),
                child: Stack(
                  children: [
                    Positioned(
                      top: 8,
                      right: 8,
                      child: GestureDetector(
                        onTap: () {
                          setState(() {
                            _selectedMedia = null;
                          });
                        },
                        child: Container(
                          padding: const EdgeInsets.all(4),
                          decoration: const BoxDecoration(
                            color: Colors.black54,
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(Icons.close, color: Colors.white, size: 18),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            Padding(
              padding: const EdgeInsets.only(top: 12.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  TextButton.icon(
                    onPressed: () async {
                      Fluttertoast.showToast(
                        msg: 'إضافة صورة',
                        backgroundColor: Colors.blue.shade800,
                        textColor: Colors.white,
                      );
                    },
                    icon: Icon(Icons.image, color: Colors.blue.shade700, size: 22),
                    label: Text(
                      'صورة',
                      style: TextStyle(
                        color: Colors.grey.shade800,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    style: TextButton.styleFrom(
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(20),
                      ),
                      backgroundColor: Colors.blue.shade50,
                    ),
                  ),
                  TextButton.icon(
                    onPressed: () {
                      Fluttertoast.showToast(
                        msg: 'إضافة فيديو',
                        backgroundColor: Colors.blue.shade800,
                        textColor: Colors.white,
                      );
                    },
                    icon: Icon(Icons.videocam, color: Colors.blue.shade700, size: 22),
                    label: Text(
                      'فيديو',
                      style: TextStyle(
                        color: Colors.grey.shade800,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    style: TextButton.styleFrom(
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(20),
                      ),
                      backgroundColor: Colors.blue.shade50,
                    ),
                  ),
                  TextButton.icon(
                    onPressed: () {
                      Fluttertoast.showToast(
                        msg: 'إضافة ملف',
                        backgroundColor: Colors.blue.shade800,
                        textColor: Colors.white,
                      );
                    },
                    icon: Icon(Icons.insert_drive_file, color: Colors.blue.shade700, size: 22),
                    label: Text(
                      'ملف',
                      style: TextStyle(
                        color: Colors.grey.shade800,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    style: TextButton.styleFrom(
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(20),
                      ),
                      backgroundColor: Colors.blue.shade50,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 12),
            ElevatedButton(
              onPressed: () {
                if (_postController.text.isNotEmpty || _selectedMedia != null) {
                  setState(() {
                    posts.insert(
                      0,
                      Post(
                        id: DateTime.now().millisecondsSinceEpoch.toString(),
                        user: 'المستخدم الحالي',
                        content: _postController.text,
                        imageUrl: _selectedMedia != null ? 'https://example.com/placeholder.jpg' : null,
                      ),
                    );
                    userPoints += 10;
                    _confettiController.play();
                    Fluttertoast.showToast(
                      msg: 'تم نشر المنشور',
                      backgroundColor: Colors.blue.shade700,
                    );
                    _postController.clear();
                    _selectedMedia = null;
                  });
                } else {
                  Fluttertoast.showToast(
                    msg: 'الرجاء إدخال نص أو إضافة وسائط',
                    backgroundColor: Colors.red,
                  );
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue.shade700,
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                elevation: 2,
              ),
              child: const Text(
                'نشر',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPostsList() {
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: posts.length,
      itemBuilder: (context, index) {
        final post = posts[index];
        final commentController = TextEditingController();
        return AnimationConfiguration.staggeredList(
          position: index,
          duration: const Duration(milliseconds: 600),
          child: SlideAnimation(
            verticalOffset: 50.0,
            child: FadeInAnimation(
              child: Card(
                elevation: 4,
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          CircleAvatar(
                            backgroundColor: Colors.blue.shade700,
                            child: Text(post.user[0], style: const TextStyle(color: Colors.white)),
                          ),
                          const SizedBox(width: 8),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(post.user, style: const TextStyle(fontWeight: FontWeight.bold)),
                              Text(
                                DateFormat('dd/MM/yyyy HH:mm').format(DateTime.now()),
                                style: const TextStyle(fontSize: 12, color: Colors.grey),
                              ),
                            ],
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(post.content),
                      if (post.imageUrl != null) ...[
                        const SizedBox(height: 8),
                        CachedNetworkImage(
                          imageUrl: post.imageUrl!,
                          width: double.infinity,
                          height: 200,
                          fit: BoxFit.cover,
                          placeholder: (context, url) => _buildShimmerPlaceholder(),
                          errorWidget: (context, url, error) => const Icon(Icons.error),
                        ),
                      ],
                      const Divider(),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Row(
                            children: [
                              IconButton(
                                icon: Icon(
                                  Icons.thumb_up,
                                  color: post.likes > 0 ? Colors.blue : Colors.grey,
                                ),
                                onPressed: () {
                                  setState(() {
                                    post.likes++;
                                  });
                                  _confettiController.play();
                                  Fluttertoast.showToast(msg: 'تم الإعجاب بالمنشور');
                                },
                              ),
                              Text('${post.likes} إعجاب'),
                            ],
                          ),
                          Row(
                            children: [
                              IconButton(
                                icon: const Icon(Icons.comment, color: Colors.grey),
                                onPressed: () {
                                  showDialog(
                                    context: context,
                                    builder: (context) => AlertDialog(
                                      title: const Text('إضافة تعليق'),
                                      content: TextField(
                                        controller: commentController,
                                        decoration: InputDecoration(
                                          hintText: 'اكتب تعليقك...',
                                          border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                                          filled: true,
                                          fillColor: Colors.blue.shade50,
                                        ),
                                      ),
                                      actions: [
                                        TextButton(
                                          onPressed: () => Navigator.pop(context),
                                          child: const Text('إلغاء'),
                                        ),
                                        TextButton(
                                          onPressed: () {
                                            if (commentController.text.isNotEmpty) {
                                              setState(() {
                                                post.comments.add(commentController.text);
                                              });
                                              commentController.clear();
                                              Navigator.pop(context);
                                              Fluttertoast.showToast(msg: 'تم إضافة التعليق');
                                            }
                                          },
                                          child: const Text('إضافة'),
                                        ),
                                      ],
                                    ),
                                  );
                                },
                              ),
                              Text('${post.comments.length} تعليق'),
                            ],
                          ),
                          IconButton(
                            icon: Icon(Icons.share, color: Colors.blue.shade700),
                            onPressed: () {
                              Share.share('${post.user}: ${post.content} ${post.imageUrl ?? ''}');
                              _confettiController.play();
                            },
                          ),
                        ],
                      ),
                      if (post.comments.isNotEmpty) ...[
                        const Divider(),
                        Text('التعليقات', style: Theme.of(context).textTheme.titleMedium),
                        ListView.builder(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          itemCount: post.comments.length,
                          itemBuilder: (context, commentIndex) {
                            return ListTile(
                              leading: const CircleAvatar(
                                backgroundColor: Colors.grey,
                                child: Icon(Icons.person, color: Colors.white),
                              ),
                              title: Text('مستخدم ${commentIndex + 1}'),
                              subtitle: Text(post.comments[commentIndex]),
                            );
                          },
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  void _showApplianceDetails(BuildContext context, ApplianceGroup appliance) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.9,
        minChildSize: 0.5,
        maxChildSize: 0.95,
        builder: (context, scrollController) => Container(
          decoration: BoxDecoration(
            color: Theme.of(context).scaffoldBackgroundColor,
            borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
            boxShadow: [
              BoxShadow(
                color: Colors.blue.withOpacity(0.3),
                blurRadius: 10,
                offset: const Offset(0, -5),
              ),
            ],
          ),
          child: DefaultTabController(
            length: 6,
            child: Column(
              children: [
                Container(
                  margin: const EdgeInsets.symmetric(vertical: 8),
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: Colors.grey,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                TabBar(
                  isScrollable: true,
                  tabs: const [
                    Tab(text: 'نظرة عامة'),
                    Tab(text: 'الفيديوهات'),
                    Tab(text: 'المقالات'),
                    Tab(text: 'الأسئلة الشائعة'),
                    Tab(text: 'دليل الصيانة'),
                    Tab(text: 'معرض الصور'),
                  ],
                  labelColor: Colors.blue.shade700,
                  unselectedLabelColor: Colors.grey,
                  indicatorColor: Colors.blue.shade700,
                ),
                Expanded(
                  child: TabBarView(
                    children: [
                      _buildOverviewTab(appliance),
                      _buildVideosTab(appliance.videos),
                      _buildArticlesTab(appliance.articles),
                      _buildFAQsTab(appliance.faqs),
                      _buildMaintenanceGuideTab(appliance.maintenanceGuide),
                      _buildGalleryTab(appliance.galleryImages),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildOverviewTab(ApplianceGroup appliance) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(12),
            child: CachedNetworkImage(
              imageUrl: appliance.imageUrl,
              height: 200,
              width: double.infinity,
              fit: BoxFit.cover,
              placeholder: (context, url) => _buildShimmerPlaceholder(),
              errorWidget: (context, url, error) => const Icon(Icons.error),
            ),
          ),
          const SizedBox(height: 16),
          Text(
            appliance.name,
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: Colors.blue.shade700,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'معلومات شاملة عن ${appliance.name}',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: 8),
          Text('عدد الفيديوهات: ${appliance.videos.length}'),
          Text('عدد المقالات: ${appliance.articles.length}'),
          Text('عدد الأسئلة الشائعة: ${appliance.faqs.length}'),
          Text('دليل الصيانة: متوفر'),
          Text('صور المعرض: ${appliance.galleryImages.length}'),
        ],
      ),
    );
  }

  Widget _buildVideosTab(List<Video> videos) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: videos.length,
      itemBuilder: (context, index) {
        final video = videos[index];
        return AnimationConfiguration.staggeredList(
          position: index,
          duration: const Duration(milliseconds: 600),
          child: SlideAnimation(
            verticalOffset: 50.0,
            child: FadeInAnimation(
              child: ListTile(
                leading: Icon(Icons.videocam, color: Colors.blue.shade700),
                title: Text(video.title),
                onTap: () {
                  ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('Playing ${video.title}')));
                  _confettiController.play();
                },
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildArticlesTab(List<Article> articles) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: articles.length,
      itemBuilder: (context, index) {
        final article = articles[index];
        return AnimationConfiguration.staggeredList(
          position: index,
          duration: const Duration(milliseconds: 600),
          child: SlideAnimation(
            verticalOffset: 50.0,
            child: FadeInAnimation(
              child: ExpansionTile(
                leading: Icon(Icons.article, color: Colors.blue.shade700),
                title: Text(article.title),
                tilePadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                collapsedBackgroundColor: Colors.blue.shade50,
                backgroundColor: Colors.blue.shade100,
                children: [
                  Padding(
                    padding: const EdgeInsets.all(16),
                    child: Text(article.content),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildFAQsTab(List<FAQ> faqs) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: faqs.length,
      itemBuilder: (context, index) {
        final faq = faqs[index];
        return AnimationConfiguration.staggeredList(
          position: index,
          duration: const Duration(milliseconds: 600),
          child: SlideAnimation(
            verticalOffset: 50.0,
            child: FadeInAnimation(
              child: ExpansionTile(
                leading: Icon(Icons.question_answer, color: Colors.blue.shade700),
                title: Text(faq.question),
                tilePadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                collapsedBackgroundColor: Colors.blue.shade50,
                backgroundColor: Colors.blue.shade100,
                children: [
                  Padding(
                    padding: const EdgeInsets.all(16),
                    child: Text(faq.answer),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildMaintenanceGuideTab(String maintenanceGuide) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'دليل الصيانة',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 12),
          Text(maintenanceGuide),
        ],
      ),
    );
  }

  Widget _buildGalleryTab(List<String> galleryImages) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: galleryImages.length,
      itemBuilder: (context, index) {
        final imageUrl = galleryImages[index];
        return AnimationConfiguration.staggeredList(
          position: index,
          duration: const Duration(milliseconds: 600),
          child: SlideAnimation(
            verticalOffset: 50.0,
            child: FadeInAnimation(
              child: Padding(
                padding: const EdgeInsets.only(bottom: 12.0),
                child: GestureDetector(
                  onTap: () {
                    Share.share('تحقق من هذه الصورة: $imageUrl');
                    _confettiController.play();
                  },
                  child: CachedNetworkImage(
                    imageUrl: imageUrl,
                    width: double.infinity,
                    height: 150,
                    fit: BoxFit.cover,
                    placeholder: (context, url) => _buildShimmerPlaceholder(),
                    errorWidget: (context, url, error) => const Icon(Icons.error),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  void _showQuickActions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      backgroundColor: Colors.blue.shade50,
      builder: (context) => Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: Icon(Icons.add_circle, color: Colors.blue.shade700),
              title: const Text('إضافة نوع جهاز جديد'),
              onTap: () {
                Navigator.pop(context);
                _showAddNewApplianceTypeDialog(context);
              },
            ),
            ListTile(
              leading: Icon(Icons.post_add, color: Colors.blue.shade700),
              title: const Text('إضافة منشور'),
              onTap: () {
                Navigator.pop(context);
                _showAddPostDialog(context);
              },
            ),
            ListTile(
              leading: Icon(Icons.add_shopping_cart, color: Colors.blue.shade700),
              title: const Text('إضافة إلى السوق'),
              onTap: () {
                Navigator.pop(context);
                showModalBottomSheet(
                  context: context,
                  shape: const RoundedRectangleBorder(
                    borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
                  ),
                  backgroundColor: Colors.blue.shade50,
                  builder: (context) => Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        ListTile(
                          leading: Icon(Icons.build, color: Colors.blue.shade700),
                          title: const Text('إضافة خدمة صيانة'),
                          onTap: () {
                            Navigator.pop(context);
                            _showAddServiceDialog(context);
                          },
                        ),
                        ListTile(
                          leading: Icon(Icons.construction, color: Colors.blue.shade700),
                          title: const Text('إضافة قطعة غيار'),
                          onTap: () {
                            Navigator.pop(context);
                            _showAddSparePartDialog(context);
                          },
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showAddPostDialog(BuildContext context) {
    final postImageController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('إضافة منشور جديد', style: TextStyle(color: Colors.blue.shade700)),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: _postController,
                decoration: InputDecoration(
                  hintText: 'اكتب منشورك...',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.blue.shade50,
                ),
                maxLines: 3,
              ),
              const SizedBox(height: 8),
              TextField(
                controller: postImageController,
                decoration: InputDecoration(
                  hintText: 'رابط الصورة (اختياري)',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.blue.shade50,
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              if (_postController.text.isNotEmpty) {
                setState(() {
                  posts.add(Post(
                    id: DateTime.now().millisecondsSinceEpoch.toString(),
                    user: 'Current User',
                    content: _postController.text,
                    imageUrl: postImageController.text.isNotEmpty ? postImageController.text : null,
                  ));
                  _postController.clear();
                  postImageController.clear();
                });
                _confettiController.play();
                Fluttertoast.showToast(msg: 'تم إضافة المنشور بنجاح', backgroundColor: Colors.blue.shade700);
                Navigator.pop(context);
              } else {
                Fluttertoast.showToast(msg: 'يرجى كتابة محتوى المنشور', backgroundColor: Colors.red);
              }
            },
            child: const Text('نشر'),
          ),
        ],
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        backgroundColor: Colors.blue.shade50,
      ),
    );
  }

  void _showSearchDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('بحث', style: TextStyle(color: Colors.blue.shade700)),
        content: TextField(
          controller: _searchController,
          decoration: InputDecoration(
            hintText: 'ابحث عن جهاز أو موضوع...',
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
            filled: true,
            fillColor: Colors.blue.shade50,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              if (_searchController.text.isNotEmpty) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('نتائج البحث عن: ${_searchController.text}')),
                );
                _searchController.clear();
                Navigator.pop(context);
              }
            },
            child: const Text('بحث'),
          ),
        ],
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        backgroundColor: Colors.blue.shade50,
      ),
    );
  }
}
