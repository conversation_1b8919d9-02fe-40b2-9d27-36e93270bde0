import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:flutter/foundation.dart';

/// تكوين متغيرات البيئة للتطبيق
/// يستخدم هذا الصف للوصول إلى متغيرات البيئة بطريقة آمنة ومنظمة
class EnvConfig {
  // نمط Singleton للتأكد من وجود نسخة واحدة فقط
  static final EnvConfig _instance = EnvConfig._internal();
  factory EnvConfig() => _instance;
  EnvConfig._internal();

  // حالة التهيئة
  bool _isInitialized = false;

  /// تهيئة متغيرات البيئة
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      // تحميل ملف البيئة المناسب بناءً على وضع التشغيل
      const envFile = kReleaseMode ? 'assets/.env.production' : 'assets/.env';
      
      debugPrint('🔄 جاري تحميل ملف البيئة: $envFile');
      await dotenv.load(fileName: envFile);
      
      _isInitialized = true;
      debugPrint('✅ تم تحميل متغيرات البيئة بنجاح');
      debugPrint('🔑 قاعدة البيانات: ${dotenv.env['SUPABASE_URL']}');
    } catch (e) {
      debugPrint('❌ فشل تحميل متغيرات البيئة: $e');
      // قم بتهيئة متغيرات افتراضية في حالة الفشل
      _initializeDefaultValues();
      _isInitialized = true;
      debugPrint('⚠️ تم استخدام القيم الافتراضية');
    }
  }
  
  void _initializeDefaultValues() {
    // قم بتعيين قيم افتراضية للمتغيرات المهمة
    dotenv.env['ENVIRONMENT'] = 'development';
    dotenv.env['API_URL'] = 'http://localhost:3000/api';
    dotenv.env['SUPABASE_URL'] = 'your_supabase_url_here';
    dotenv.env['SUPABASE_ANON_KEY'] = 'your_supabase_anon_key_here';
    dotenv.env['FIREBASE_API_KEY'] = 'your_firebase_api_key_here';
  }

  /// الحصول على قيمة متغير بيئة
  /// إذا لم يكن المتغير موجوداً، يتم إرجاع القيمة الافتراضية
  String get(String key, {String defaultValue = ''}) {
    _checkInitialization();
    return dotenv.env[key] ?? defaultValue;
  }

  /// الحصول على قيمة متغير بيئة كرقم صحيح
  int getInt(String key, {int defaultValue = 0}) {
    _checkInitialization();
    final value = dotenv.env[key];
    if (value == null) return defaultValue;
    return int.tryParse(value) ?? defaultValue;
  }

  /// الحصول على قيمة متغير بيئة كرقم عشري
  double getDouble(String key, {double defaultValue = 0.0}) {
    _checkInitialization();
    final value = dotenv.env[key];
    if (value == null) return defaultValue;
    return double.tryParse(value) ?? defaultValue;
  }

  /// الحصول على قيمة متغير بيئة كقيمة منطقية
  bool getBool(String key, {bool defaultValue = false}) {
    _checkInitialization();
    final value = dotenv.env[key];
    if (value == null) return defaultValue;
    return value.toLowerCase() == 'true';
  }

  /// التحقق من تهيئة الصف
  void _checkInitialization() {
    if (!_isInitialized) {
      debugPrint('⚠️ تحذير: لم يتم تهيئة EnvConfig بعد. استخدم EnvConfig().initialize() أولاً.');
    }
  }

  // الوصول المباشر إلى متغيرات البيئة الشائعة

  /// عنوان URL الخاص بـ API
  String get apiUrl => get('API_URL', defaultValue: 'http://localhost:3000/api');

  /// مفتاح Supabase URL
  String get supabaseUrl => get('SUPABASE_URL');

  /// مفتاح Supabase المجهول
  String get supabaseAnonKey => get('SUPABASE_ANON_KEY');

  /// مفتاح Firebase
  String get firebaseApiKey => get('FIREBASE_API_KEY');

  /// بيئة التطبيق (development, production)
  String get environment => get('ENVIRONMENT', defaultValue: 'development');

  /// التحقق مما إذا كانت البيئة هي بيئة التطوير
  bool get isDevelopment => environment.toLowerCase() == 'development';

  /// التحقق مما إذا كانت البيئة هي بيئة الإنتاج
  bool get isProduction => environment.toLowerCase() == 'production';
}
