name: untitled10
description: A new Flutter project.
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.16.0"

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  page_transition: ^2.1.0

  # Localization and Offline support
  easy_localization: ^3.0.2
  flutter_offline: ^2.0.0
  retry: ^3.1.2
  intl: ^0.20.2
  shared_preferences: ^2.2.2
  flutter_secure_storage: ^9.0.0
  path_provider: ^2.1.4
  encrypt: ^5.0.3
  sqflite: any
  path: any
  equatable: any
  http_parser: any

  # UI and Icons
  cupertino_icons: ^1.0.8
  dotted_border: ^2.0.0+3
  cached_network_image: ^3.3.1
  emoji_picker_flutter: ^1.5.0
  flutter_spinkit: ^5.2.0
  font_awesome_flutter: ^10.6.0
  google_fonts: ^5.0.0
  fluttericon: ^2.0.0
  marquee: ^2.2.3
  badges: ^3.1.2
  table_calendar: ^3.0.9
  flutter_staggered_grid_view: ^0.7.0
  lottie: ^3.1.2
  animated_text_kit: ^4.2.2
  flutter_staggered_animations: ^1.1.1
  flutter_chat_ui: ^1.6.6
  shimmer: ^3.0.0
  percent_indicator: ^4.2.3
  pull_to_refresh: ^2.0.0
  animate_do: ^3.0.2
  animated_background: ^2.0.0
  flutter_rating_bar: ^4.0.1
  confetti: ^0.7.0
  fl_chart: ^0.64.0
  photo_view: ^0.14.0
  carousel_slider: ^5.0.0

  # Maps and Location
  latlong2: ^0.9.0
  flutter_map: ^6.2.1
  flutter_map_cancellable_tile_provider: ^2.0.0
  geolocator: ^8.0.0
  geocoding: ^2.1.1
  google_maps_flutter: ^2.9.0
  google_places_flutter: ^2.0.1
  local_hero_transform: ^1.0.0
  permission_handler: ^11.3.1

  # Media and Files
  webview_flutter: ^4.8.0
  webview_flutter_android: ^4.2.0
  webview_flutter_wkwebview: ^3.10.2
  chewie: ^1.8.1
  youtube_player_flutter: ^9.1.0
  flutter_pdfview: ^1.3.2
  pdfx: ^2.4.0
  image_picker: ^0.8.4
  file_picker: ^8.3.7
  video_player: ^2.9.1
  image_cropper: ^9.0.0
  flutter_sound: ^9.11.2
  audioplayers: ^5.2.1
  flutter_cache_manager: ^3.4.1

  # Networking and WebSockets
  socket_io_client: ^2.0.3
  web_socket_channel: ^3.0.1
  dio: ^5.6.0
  retrofit: ^4.4.0
  json_annotation: ^4.9.0
  http: ^1.2.1
  connectivity_plus: ^2.3.9
  flutter_dotenv: ^5.0.2
  uuid: ^4.5.0

  # Firebase
  firebase_analytics: ^11.4.5
  firebase_core: ^3.13.0
  firebase_auth: ^5.5.2
  firebase_storage: ^12.4.5
  cloud_firestore: ^5.4.4
  firebase_messaging: ^15.1.3

  # Data Persistence
  hive: ^2.2.3
  hive_flutter: ^1.1.0

  # Search and Fuzzy Matching
  fuzzy: ^0.5.1

  # Social and Sharing
  google_sign_in: ^6.2.1
  share_plus: ^10.1.4
  url_launcher: ^6.3.0


  # Audio and Chat
  speech_to_text: ^6.1.1
  agora_chat_sdk: ^1.3.1+1
  agora_rtc_engine: ^6.2.0
  record: ^4.4.0

  # State Management
  provider: ^6.1.1
  flutter_bloc: ^8.1.6
  get_it: ^8.0.0

  # Utilities
  jwt_decoder: ^2.0.1
  js: ^0.6.7
  mongo_dart: ^0.10.3
  pull_to_refresh_flutter3: ^2.0.0
  fluttertoast: ^8.2.2
  hijri: ^3.0.0
  flutter_local_notifications: ^17.2.2
  timezone: ^0.9.4
  android_alarm_manager_plus: ^4.0.0
  wakelock_plus: ^1.3.2
  haptic_feedback: ^0.5.1+1
  vibration: ^2.0.0
  flutter_vibrate: ^1.3.0
  timeago: ^3.7.0
  logger: ^2.4.0
  lint: ^2.0.0


  # Game and Animation
  flame: ^1.8.0
  particle_field: ^1.0.0
  flutter_animate: ^4.0.0

  # Database
  supabase_flutter: ^2.9.0
  sqflite_common_ffi: ^2.3.0
  sqflite_common_ffi_web: ^0.4.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^4.0.0
  build_runner: ^2.4.13
  json_serializable: ^6.8.0
  retrofit_generator: ^9.1.3

flutter:
  uses-material-design: true
  generate: true  # Enable generation of localizations code

  # Asset files and directories
  assets:
    - assets/.env
    - .env.development
    - assets/images/
    - assets/placeholder.jpg
    - assets/assets/images/logo.png
