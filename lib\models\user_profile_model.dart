// نموذج الملف الشخصي
class UserProfile {
  bool isFavorite;
  final String id;
  final String name;
  final String age;
  final String nationality;
  final String maritalStatus;
  final String bio;
  final String location;
  final String category;
  final String fileUrl;
  final DateTime date;
  final bool isFeatured;
  final String creatorId;

  UserProfile({
    required this.id,
    this.isFavorite = false,
    required this.name,
    required this.age,
    required this.nationality,
    required this.maritalStatus,
    required this.bio,
    required this.location,
    required this.category,
    required this.fileUrl,
    required this.date,
    required this.isFeatured,
    required this.creatorId,
  });

  factory UserProfile.fromJson(Map<String, dynamic> json) {
    return UserProfile(
      id: json['_id'] ?? '',
      name: json['name'] ?? 'بدون اسم',
      age: json['age'] ?? 'غير محدد',
      nationality: json['nationality'] ?? 'غير محدد',
      maritalStatus: json['maritalStatus'] ?? 'غير محدد',
      bio: json['bio'] ?? 'بدون نبذة',
      location: json['location'] ?? 'غير محدد',
      category: json['category'] ?? 'غير مصنف',
      fileUrl: json['fileUrl'] ?? '',
      date: DateTime.parse(json['date'] ?? DateTime.now().toString()),
      isFeatured: json['isFeatured'] ?? false,
      creatorId: json['creatorId'] ?? '',
      isFavorite: json['isFavorite'] ?? false,
    );
  }
}
