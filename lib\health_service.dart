import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:socket_io_client/socket_io_client.dart' as io;
import 'appstate.dart';
import 'models/disease_model.dart';

class HealthService {
  final String baseUrl;

  HealthService() : baseUrl = AppState.getBackendUrl();

  Future<List<Map<String, dynamic>>> fetchNotifications(String token) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/notifications'),
        headers: {'x-auth-token': token},
      );
      if (response.statusCode == 200) {
        return List<Map<String, dynamic>>.from(jsonDecode(response.body));
      }
      throw Exception('فشل في جلب الإشعارات: ${response.body}');
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }

  Future<void> markNotificationRead(String token, String notificationId) async {
    try {
      final response = await http.put(
        Uri.parse('$baseUrl/notifications/$notificationId'),
        headers: {'x-auth-token': token},
      );
      if (response.statusCode != 200) {
        throw Exception('فشل في تحديث الإشعار: ${response.body}');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }

  Future<List<Disease>> fetchDiseases(String token, String category) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/diseases?category=$category'),
        headers: {'x-auth-token': token},
      );
      if (response.statusCode == 200) {
        return (jsonDecode(response.body) as List)
            .map((json) => Disease.fromJson(json))
            .toList();
      }
      throw Exception('فشل في جلب الأمراض: ${response.body}');
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }

  Future<List<Map<String, dynamic>>> fetchPosts(
      String token, String disease) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/health-posts?disease=$disease'),
        headers: {'x-auth-token': token},
      );
      if (response.statusCode == 200) {
        return List<Map<String, dynamic>>.from(jsonDecode(response.body));
      }
      throw Exception('فشل في جلب المنشورات: ${response.body}');
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }

  Future<void> addPost(String token, String disease, String content, File? file,
      String? fileType) async {
    try {
      final request =
      http.MultipartRequest('POST', Uri.parse('$baseUrl/health-posts'));
      request.headers['x-auth-token'] = token;
      request.fields['memberName'] = 'مستخدم';
      request.fields['postContent'] = content;
      request.fields['disease'] = disease;
      if (file != null) {
        if (await file.length() > 5 * 1024 * 1024) {
          throw Exception(
              'حجم الملف كبير جدًا (يجب أن يكون أقل من 5 ميجابايت)');
        }
        request.fields['fileType'] = fileType!;
        request.files.add(await http.MultipartFile.fromPath('file', file.path));
      }
      final response = await request.send();
      if (response.statusCode != 201) {
        throw Exception(
            'فشل في نشر المنشور: ${await response.stream.bytesToString()}');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }

  Future<List<Map<String, dynamic>>> fetchTasks(
      String token, String disease) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/health-tasks?disease=$disease'),
        headers: {'x-auth-token': token},
      );
      if (response.statusCode == 200) {
        return List<Map<String, dynamic>>.from(jsonDecode(response.body));
      }
      throw Exception('فشل في جلب المهام: ${response.body}');
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }

  Future<void> completeTask(String token, String taskId) async {
    try {
      final response = await http.put(
        Uri.parse('$baseUrl/health-tasks/$taskId/complete'),
        headers: {'x-auth-token': token},
      );
      if (response.statusCode != 200) {
        throw Exception('فشل في إكمال المهمة: ${response.body}');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }

  Future<List<Map<String, dynamic>>> fetchContents(
      String token, String disease) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/health-content?disease=$disease'),
        headers: {'x-auth-token': token},
      );
      if (response.statusCode == 200) {
        return List<Map<String, dynamic>>.from(jsonDecode(response.body));
      }
      throw Exception('فشل في جلب المحتوى: ${response.body}');
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }

  Future<void> addContent(String token, String disease, String title,
      String contentType, String? description, File file) async {
    try {
      final request =
      http.MultipartRequest('POST', Uri.parse('$baseUrl/health-content'));
      request.headers['x-auth-token'] = token;
      request.fields['title'] = title;
      request.fields['contentType'] = contentType;
      request.fields['disease'] = disease;
      if (description != null) request.fields['description'] = description;
      if (await file.length() > 5 * 1024 * 1024) {
        throw Exception('حجم الملف كبير جدًا (يجب أن يكون أقل من 5 ميجابايت)');
      }
      request.files.add(await http.MultipartFile.fromPath('file', file.path));
      final response = await request.send();
      if (response.statusCode != 201) {
        throw Exception(
            'فشل في إضافة المحتوى: ${await response.stream.bytesToString()}');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }

  Future<List<Map<String, dynamic>>> fetchChatRooms(
      String token, String disease) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/chat-rooms?disease=$disease'),
        headers: {'x-auth-token': token},
      );
      if (response.statusCode == 200) {
        return List<Map<String, dynamic>>.from(jsonDecode(response.body));
      }
      throw Exception('فشل في جلب غرف الدردشة: ${response.body}');
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }

  Future<void> createChatRoom(
      String token, String name, List<String> members, String disease) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/chat-rooms'),
        headers: {'Content-Type': 'application/json', 'x-auth-token': token},
        body: jsonEncode({'name': name, 'members': members, 'disease': disease}),
      );
      if (response.statusCode != 201) {
        throw Exception('فشل في إنشاء غرفة الدردشة: ${response.body}');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }

  Future<List<Map<String, dynamic>>> fetchMessages(
      String token, String roomId) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/chat-rooms/$roomId/messages'),
        headers: {'x-auth-token': token},
      );
      if (response.statusCode == 200) {
        return List<Map<String, dynamic>>.from(jsonDecode(response.body));
      }
      throw Exception('فشل في جلب الرسائل: ${response.body}');
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }

  Future<void> sendMessage(
      String token, String roomId, String content, String sender) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/chat-rooms/$roomId/message'),
        headers: {'Content-Type': 'application/json', 'x-auth-token': token},
        body: jsonEncode({'sender': sender, 'content': content}),
      );
      if (response.statusCode != 201) {
        throw Exception('فشل في إرسال الرسالة: ${response.body}');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }

  Future<void> saveProgress(String token, String disease, Map<String, dynamic> progress) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/progress'),
        headers: {'Content-Type': 'application/json', 'x-auth-token': token},
        body: jsonEncode({'disease': disease, 'progress': progress}),
      );
      if (response.statusCode != 201) {
        throw Exception('فشل في حفظ التقدم: ${response.body}');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }

  Future<List<Map<String, dynamic>>> fetchProgress(String token, String disease) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/progress?disease=$disease'),
        headers: {'x-auth-token': token},
      );
      if (response.statusCode == 200) {
        return List<Map<String, dynamic>>.from(jsonDecode(response.body));
      }
      throw Exception('فشل في جلب التقدم: ${response.body}');
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }

  Future<List<Map<String, dynamic>>> fetchQuizzes(String token, String disease) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/quizzes?disease=$disease'),
        headers: {'x-auth-token': token},
      );
      if (response.statusCode == 200) {
        return List<Map<String, dynamic>>.from(jsonDecode(response.body));
      }
      throw Exception('فشل في جلب الاختبارات: ${response.body}');
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }

  void setupSocketListeners(
      io.Socket socket,
      Function(dynamic) onNewMessage,
      Function(dynamic) onNewPost,
      Function(dynamic) onNewContent,
      Function(dynamic) onNewChatRoom,
      Function(dynamic) onNewNotification) {
    socket.on('new_message', onNewMessage);
    socket.on('new_post', onNewPost);
    socket.on('new_content', onNewContent);
    socket.on('new_chat_room', onNewChatRoom);
    socket.on('new_notification', onNewNotification);
  }
}