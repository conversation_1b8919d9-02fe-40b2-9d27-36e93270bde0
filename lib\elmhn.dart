import 'package:flutter/material.dart';
import 'package:untitled10/main.dart' show MyApp;
// أضف أي استيرادات إضافية هنا عند الحاجة
import 'package:provider/provider.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:animate_do/animate_do.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'dart:async';
import 'dart:io';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:file_picker/file_picker.dart';
import 'appstate.dart';

class CourseDetailsPage1 extends StatefulWidget {
  final String courseTitle;

  const CourseDetailsPage1({super.key, required this.courseTitle});

  @override
  _CourseDetailsPage1State createState() => _CourseDetailsPage1State();
}

class _CourseDetailsPage1State extends State<CourseDetailsPage1>
    with SingleTickerProviderStateMixin {
  List<Map<String, dynamic>> _memberPosts = [];
  List<Map<String, dynamic>> _dailyTasks = [];
  List<Map<String, dynamic>> _contents = [];
  List<Map<String, dynamic>> _relatedCourses = [];
  final TextEditingController _newPostController = TextEditingController();
  final TextEditingController _commentController = TextEditingController();
  String? _attachedFileName;
  String? _attachedFileType;
  File? _attachedFile;
  bool _isLoadingPosts = true;
  bool _isLoadingTasks = true;
  bool _isLoadingContents = true;
  bool _isLoadingRelatedCourses = true;
  bool _isLiveChatEnabled = false;
  double _courseProgress = 0.0;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _fetchPosts();
    _fetchDailyTasks();
    _fetchContents();
    _fetchRelatedCourses();
    _fetchCourseProgress();
    _animationController.forward();
  }

  @override
  void dispose() {
    _newPostController.dispose();
    _commentController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _fetchPosts() async {
    if (!await Connectivity()
        .checkConnectivity()
        .then((r) => r != ConnectivityResult.none)) {
      setState(() => _isLoadingPosts = false);
      _showSnackBar('لا يوجد اتصال بالإنترنت');
      return;
    }

    final appState = Provider.of<AppState>(context, listen: false);
    try {
      final response = await http.get(
        Uri.parse('${AppState.getBackendUrl()}/posts'),
        headers: {'x-auth-token': appState.token ?? defaultAuthToken},
      ).timeout(const Duration(seconds: 10));
      if (response.statusCode == 200) {
        setState(() {
          _memberPosts =
              List<Map<String, dynamic>>.from(jsonDecode(response.body))
                  .map((post) => {
                        "id": post['_id'],
                        "likes": post['likes'] ?? 0,
                        "views": post['views'] ?? 0,
                        "comments": List<String>.from(post['comments'] ?? []),
                        "isLiked": post['isLiked'] ?? false,
                        "points": post['points'] ?? 0,
                        "memberName": post['memberName'] ?? appState.username,
                        "learningActionsToday":
                            post['learningActionsToday'] ?? 0,
                        "selectedForChat": false,
                        "postContent": post['content'] ?? "",
                        "attachedFile": post['imagePath'],
                        "fileType": post['imagePath'] != null
                            ? (post['imagePath'].endsWith('.mp4')
                                ? "video"
                                : "image")
                            : null,
                        "isPinned": post['isPinned'] ?? false,
                      })
                  .toList();
          _isLoadingPosts = false;
        });
      } else {
        throw Exception('فشل في جلب المنشورات');
      }
    } catch (e) {
      setState(() => _isLoadingPosts = false);
      _showSnackBar('خطأ: $e');
    }
  }

  Future<void> _fetchDailyTasks() async {
    if (!await Connectivity()
        .checkConnectivity()
        .then((r) => r != ConnectivityResult.none)) {
      setState(() => _isLoadingTasks = false);
      _showSnackBar('لا يوجد اتصال بالإنترنت');
      return;
    }

    final appState = Provider.of<AppState>(context, listen: false);
    try {
      final response = await http.get(
        Uri.parse('${AppState.getBackendUrl()}/tasks'),
        headers: {'x-auth-token': appState.token ?? defaultAuthToken},
      ).timeout(const Duration(seconds: 10));
      if (response.statusCode == 200) {
        setState(() {
          _dailyTasks =
              List<Map<String, dynamic>>.from(jsonDecode(response.body));
          _isLoadingTasks = false;
        });
      } else {
        throw Exception('فشل في جلب المهام');
      }
    } catch (e) {
      setState(() => _isLoadingTasks = false);
      _showSnackBar('خطأ: $e');
    }
  }

  Future<void> _fetchContents() async {
    if (!await Connectivity()
        .checkConnectivity()
        .then((r) => r != ConnectivityResult.none)) {
      setState(() => _isLoadingContents = false);
      _showSnackBar('لا يوجد اتصال بالإنترنت');
      return;
    }

    final appState = Provider.of<AppState>(context, listen: false);
    try {
      final response = await http.get(
        Uri.parse('${AppState.getBackendUrl()}/contents'),
        headers: {'x-auth-token': appState.token ?? defaultAuthToken},
      ).timeout(const Duration(seconds: 10));
      if (response.statusCode == 200) {
        setState(() {
          _contents =
              List<Map<String, dynamic>>.from(jsonDecode(response.body));
          _isLoadingContents = false;
        });
      } else {
        throw Exception('فشل في جلب المحتويات');
      }
    } catch (e) {
      setState(() => _isLoadingContents = false);
      _showSnackBar('خطأ: $e');
    }
  }

  Future<void> _fetchRelatedCourses() async {
    if (!await Connectivity()
        .checkConnectivity()
        .then((r) => r != ConnectivityResult.none)) {
      setState(() => _isLoadingRelatedCourses = false);
      _showSnackBar('لا يوجد اتصال بالإنترنت');
      return;
    }

    final appState = Provider.of<AppState>(context, listen: false);
    try {
      final response = await http.get(
        Uri.parse(
            '${AppState.getBackendUrl()}/courses/related?title=${widget.courseTitle}'),
        headers: {'x-auth-token': appState.token ?? defaultAuthToken},
      ).timeout(const Duration(seconds: 10));
      if (response.statusCode == 200) {
        setState(() {
          _relatedCourses =
              List<Map<String, dynamic>>.from(jsonDecode(response.body));
          _isLoadingRelatedCourses = false;
        });
      } else {
        throw Exception('فشل في جلب الدورات ذات الصلة');
      }
    } catch (e) {
      setState(() => _isLoadingRelatedCourses = false);
      _showSnackBar('خطأ: $e');
    }
  }

  Future<void> _fetchCourseProgress() async {
    final appState = Provider.of<AppState>(context, listen: false);
    try {
      final response = await http.get(
        Uri.parse(
            '${AppState.getBackendUrl()}/courses/progress?title=${widget.courseTitle}'),
        headers: {'x-auth-token': appState.token ?? defaultAuthToken},
      ).timeout(const Duration(seconds: 10));
      if (response.statusCode == 200) {
        setState(() {
          _courseProgress = jsonDecode(response.body)['progress'] ?? 0.0;
        });
      }
    } catch (e) {
      _showSnackBar('خطأ في جلب تقدم الدورة: $e');
    }
  }

  Future<void> _completeTask(int taskIndex) async {
    if (!_dailyTasks[taskIndex]['completed']) {
      final appState = Provider.of<AppState>(context, listen: false);
      try {
        final response = await http.post(
          Uri.parse('${AppState.getBackendUrl()}/tasks/complete'),
          headers: {
            "Content-Type": "application/json",
            'x-auth-token': appState.token ?? defaultAuthToken
          },
          body: jsonEncode({"taskId": _dailyTasks[taskIndex]['_id']}),
        );
        if (response.statusCode == 200) {
          setState(() {
            _dailyTasks[taskIndex]['completed'] = true;
            if (_memberPosts.isNotEmpty) {
              _memberPosts[0]['points'] += _dailyTasks[taskIndex]['points'];
            }
            _courseProgress += 0.1; // Increment progress (example logic)
          });
          _showSnackBar("تم إكمال المهمة: ${_dailyTasks[taskIndex]['task']}");
        } else {
          throw Exception('فشل في إكمال المهمة');
        }
      } catch (e) {
        _showSnackBar('خطأ: $e');
      }
    }
  }

  Future<void> _attachImage() async {
    FilePickerResult? result = await FilePicker.platform
        .pickFiles(type: FileType.image, allowMultiple: false);
    if (result != null) {
      setState(() {
        _attachedFileName = result.files.single.name;
        _attachedFileType = "image";
        _attachedFile = File(result.files.single.path!);
      });
    }
  }

  Future<void> _attachVideo() async {
    FilePickerResult? result = await FilePicker.platform
        .pickFiles(type: FileType.video, allowMultiple: false);
    if (result != null) {
      setState(() {
        _attachedFileName = result.files.single.name;
        _attachedFileType = "video";
        _attachedFile = File(result.files.single.path!);
      });
    }
  }

  Future<void> _postNewContent() async {
    if (_newPostController.text.isNotEmpty || _attachedFile != null) {
      final appState = Provider.of<AppState>(context, listen: false);
      var request = http.MultipartRequest(
          'POST', Uri.parse('${AppState.getBackendUrl()}/posts'));
      request.headers['x-auth-token'] = appState.token ?? defaultAuthToken;
      request.fields['content'] = _newPostController.text.isNotEmpty
          ? _newPostController.text
          : "منشور بدون نص";
      request.fields['memberName'] = appState.username;
      if (_attachedFile != null) {
        request.files.add(
            await http.MultipartFile.fromPath('image', _attachedFile!.path));
      }

      try {
        final response =
            await request.send().timeout(const Duration(seconds: 15));
        if (response.statusCode == 201) {
          setState(() {
            _newPostController.clear();
            _attachedFileName = null;
            _attachedFileType = null;
            _attachedFile = null;
          });
          await _fetchPosts();
          _showSnackBar("تم نشر المنشور بنجاح!");
        } else {
          final error = await response.stream.bytesToString();
          throw Exception('فشل في نشر المنشور: $error');
        }
      } catch (e) {
        _showSnackBar('خطأ: $e');
      }
    }
  }

  Future<void> _pinPost(String postId) async {
    final appState = Provider.of<AppState>(context, listen: false);
    try {
      final response = await http.post(
        Uri.parse('${AppState.getBackendUrl()}/posts/pin'),
        headers: {
          "Content-Type": "application/json",
          'x-auth-token': appState.token ?? defaultAuthToken
        },
        body: jsonEncode({"postId": postId}),
      );
      if (response.statusCode == 200) {
        await _fetchPosts();
        _showSnackBar("تم تثبيت المنشور!");
      } else {
        throw Exception('فشل في تثبيت المنشور');
      }
    } catch (e) {
      _showSnackBar('خطأ: $e');
    }
  }

  void _showCreateChatRoomDialog() {
    String selectedChatType = "كتابية";
    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          backgroundColor: Colors.teal[50],
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
          title: Container(
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.teal[700]!, Colors.teal[300]!],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius:
                  const BorderRadius.vertical(top: Radius.circular(20)),
            ),
            child: const Text("إنشاء غرفة دردشة",
                style:
                    TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                textAlign: TextAlign.center),
          ),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text("اختر الأعضاء:",
                    style: TextStyle(
                        fontWeight: FontWeight.bold, color: Colors.teal[800])),
                ..._memberPosts.map((member) => CheckboxListTile(
                      title: Text(member['memberName']),
                      value: member['selectedForChat'],
                      onChanged: (value) => setDialogState(
                          () => member['selectedForChat'] = value!),
                    )),
                DropdownButton<String>(
                  value: selectedChatType,
                  items: ["كتابية", "صوتية", "فيديو"]
                      .map((type) =>
                          DropdownMenuItem(value: type, child: Text(type)))
                      .toList(),
                  onChanged: (value) =>
                      setDialogState(() => selectedChatType = value!),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
                onPressed: () => Navigator.pop(context),
                child:
                    const Text("إلغاء", style: TextStyle(color: Colors.grey))),
            ElevatedButton(
              onPressed: () {
                final selectedMembers = _memberPosts
                    .where((m) => m['selectedForChat'])
                    .map((m) => m['memberName'])
                    .toList();
                if (selectedMembers.isNotEmpty) {
                  Navigator.pop(context);
                  _showSnackBar(
                      "تم إنشاء غرفة $selectedChatType مع: ${selectedMembers.join(', ')}");
                } else {
                  _showSnackBar("يرجى اختيار عضو واحد على الأقل");
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.teal[700],
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12)),
              ),
              child: const Text("إنشاء", style: TextStyle(color: Colors.white)),
            ),
          ],
        ),
      ),
    );
  }

  void _showAddContentDialog() {
    String selectedContentType = "فيديو";
    final titleController = TextEditingController();
    final descriptionController = TextEditingController();
    File? selectedFile;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
          title: const Text("إضافة محتوى جديد",
              style: TextStyle(color: Colors.teal)),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: titleController,
                  decoration: InputDecoration(
                    hintText: "عنوان المحتوى",
                    filled: true,
                    fillColor: Colors.grey[100],
                    border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: BorderSide.none),
                  ),
                  textDirection: TextDirection.rtl,
                ),
                DropdownButton<String>(
                  value: selectedContentType,
                  items: ["فيديو", "كتاب", "مقالة"]
                      .map((type) =>
                          DropdownMenuItem(value: type, child: Text(type)))
                      .toList(),
                  onChanged: (value) =>
                      setDialogState(() => selectedContentType = value!),
                ),
                ElevatedButton(
                  onPressed: () async {
                    FilePickerResult? result =
                        await FilePicker.platform.pickFiles(
                      type: FileType.custom,
                      allowedExtensions: selectedContentType == "فيديو"
                          ? ['mp4', 'mov']
                          : selectedContentType == "كتاب"
                              ? ['pdf', 'epub']
                              : ['txt', 'doc'],
                    );
                    if (result != null) {
                      setDialogState(
                          () => selectedFile = File(result.files.single.path!));
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.teal[700],
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12)),
                  ),
                  child: Text(
                      selectedFile == null ? "اختيار ملف" : "تم اختيار الملف",
                      style: const TextStyle(color: Colors.white)),
                ),
                TextField(
                  controller: descriptionController,
                  maxLines: 3,
                  decoration: InputDecoration(
                    hintText: "الوصف (اختياري)",
                    filled: true,
                    fillColor: Colors.grey[100],
                    border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: BorderSide.none),
                  ),
                  textDirection: TextDirection.rtl,
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
                onPressed: () => Navigator.pop(context),
                child:
                    const Text("إلغاء", style: TextStyle(color: Colors.grey))),
            ElevatedButton(
              onPressed: () async {
                if (titleController.text.isNotEmpty && selectedFile != null) {
                  final appState =
                      Provider.of<AppState>(context, listen: false);
                  var request = http.MultipartRequest('POST',
                      Uri.parse('${AppState.getBackendUrl()}/contents'));
                  request.headers['x-auth-token'] =
                      appState.token ?? defaultAuthToken;
                  request.fields['title'] = titleController.text;
                  request.fields['type'] = selectedContentType;
                  request.fields['description'] = descriptionController.text;
                  request.files.add(await http.MultipartFile.fromPath(
                      'file', selectedFile!.path));
                  final response = await request.send();
                  if (response.statusCode == 201) {
                    Navigator.pop(context);
                    await _fetchContents();
                  } else {
                    _showSnackBar(
                        "فشل في إضافة المحتوى: ${response.statusCode}");
                  }
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.teal[700],
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12)),
              ),
              child: const Text("إضافة", style: TextStyle(color: Colors.white)),
            ),
          ],
        ),
      ),
    );
  }

  void _shareCourseLink() {
    final courseUrl =
        "${AppState.getBackendUrl()}/courses/${widget.courseTitle}";
    _showSnackBar("تم نسخ رابط الدورة: $courseUrl");
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          const Positioned.fill(
            child: Opacity(
              opacity: 0.3,
              child: FadeInImage(
                placeholder: AssetImage('assets/placeholder.jpg'),
                image: NetworkImage(
                    'https://images.unsplash.com/photo-1516321310768-61f3f3c93b80'),
                fit: BoxFit.cover,
              ),
            ),
          ),
          CustomScrollView(
            slivers: [
              SliverAppBar(
                expandedHeight: 200,
                floating: false,
                pinned: true,
                flexibleSpace: FlexibleSpaceBar(
                  title: Text(widget.courseTitle,
                      style: const TextStyle(
                          color: Colors.white, fontWeight: FontWeight.bold)),
                  background: Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [Colors.teal[700]!, Colors.teal[300]!],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                    ),
                    child: Center(
                        child: FaIcon(FontAwesomeIcons.graduationCap,
                            size: 80, color: Colors.white.withOpacity(0.5))),
                  ),
                ),
                actions: [
                  IconButton(
                    icon: const FaIcon(FontAwesomeIcons.share,
                        color: Colors.white),
                    onPressed: _shareCourseLink,
                  ),
                  IconButton(
                    icon: const FaIcon(FontAwesomeIcons.bookmark,
                        color: Colors.white),
                    onPressed: () => _showSnackBar("تم حفظ الدورة في المفضلة"),
                  ),
                ],
              ),
              SliverToBoxAdapter(
                child: _isLoadingPosts ||
                        _isLoadingTasks ||
                        _isLoadingContents ||
                        _isLoadingRelatedCourses
                    ? const Center(
                        child: CircularProgressIndicator(
                            valueColor: AlwaysStoppedAnimation(Colors.teal)))
                    : FadeTransition(
                        opacity: _fadeAnimation,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            ZoomIn(
                              child: Padding(
                                padding: const EdgeInsets.all(16.0),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text("تقدم الدورة",
                                        style: TextStyle(
                                            fontSize: 20,
                                            fontWeight: FontWeight.bold,
                                            color: Colors.teal[800])),
                                    const SizedBox(height: 8),
                                    LinearProgressIndicator(
                                      value: _courseProgress,
                                      backgroundColor: Colors.grey[300],
                                      valueColor: AlwaysStoppedAnimation(
                                          Colors.teal[700]),
                                      minHeight: 10,
                                    ),
                                    const SizedBox(height: 8),
                                    Text(
                                        "${(_courseProgress * 100).toInt()}% مكتمل",
                                        style:
                                            TextStyle(color: Colors.teal[700])),
                                  ],
                                ),
                              ),
                            ),
                            FadeInUp(
                              child: Padding(
                                padding: const EdgeInsets.all(16.0),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text("اكتب منشورًا جديدًا",
                                        style: TextStyle(
                                            fontSize: 18,
                                            fontWeight: FontWeight.bold,
                                            color: Colors.teal[800])),
                                    TextField(
                                      controller: _newPostController,
                                      maxLines: 3,
                                      decoration: InputDecoration(
                                        hintText: "ما الذي يدور في ذهنك؟",
                                        filled: true,
                                        fillColor:
                                            Colors.white.withOpacity(0.9),
                                        border: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(12),
                                            borderSide: BorderSide.none),
                                        contentPadding:
                                            const EdgeInsets.symmetric(
                                                horizontal: 16, vertical: 12),
                                      ),
                                      textDirection: TextDirection.rtl,
                                    ),
                                    Row(
                                      children: [
                                        IconButton(
                                            icon: const FaIcon(
                                                FontAwesomeIcons.image,
                                                color: Colors.teal),
                                            onPressed: _attachImage),
                                        IconButton(
                                            icon: const FaIcon(
                                                FontAwesomeIcons.video,
                                                color: Colors.teal),
                                            onPressed: _attachVideo),
                                        if (_attachedFileName != null)
                                          Flexible(
                                              child: Text(_attachedFileName!,
                                                  style: const TextStyle(
                                                      color: Colors.teal))),
                                      ],
                                    ),
                                    ElevatedButton(
                                      onPressed: _postNewContent,
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor: Colors.teal[700],
                                        shape: RoundedRectangleBorder(
                                            borderRadius:
                                                BorderRadius.circular(12)),
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: 24, vertical: 12),
                                      ),
                                      child: const Text("نشر",
                                          style: TextStyle(
                                              color: Colors.white,
                                              fontSize: 16)),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            SlideInRight(
                              child: Padding(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 16.0),
                                child: SwitchListTile(
                                  title: Text("تفعيل الدردشة المباشرة",
                                      style:
                                          TextStyle(color: Colors.teal[800])),
                                  value: _isLiveChatEnabled,
                                  onChanged: (value) {
                                    setState(() => _isLiveChatEnabled = value);
                                    if (value) {
                                      Provider.of<AppState>(context,
                                              listen: false)
                                          .socket
                                          ?.emit('joinCourseChat',
                                              widget.courseTitle);
                                      _showSnackBar(
                                          "تم تفعيل الدردشة المباشرة");
                                    } else {
                                      Provider.of<AppState>(context,
                                              listen: false)
                                          .socket
                                          ?.emit('leaveCourseChat',
                                              widget.courseTitle);
                                      _showSnackBar(
                                          "تم إيقاف الدردشة المباشرة");
                                    }
                                  },
                                  activeColor: Colors.teal[700],
                                ),
                              ),
                            ),
                            _buildSection(
                                "فيديوهات تعليمية",
                                _contents
                                    .where((c) => c['type'] == 'فيديو')
                                    .toList(),
                                _buildVideoCard),
                            _buildSection(
                                "كتب دراسية",
                                _contents
                                    .where((c) => c['type'] == 'كتاب')
                                    .toList(),
                                _buildBookCard),
                            _buildSection(
                                "مقالات تعليمية",
                                _contents
                                    .where((c) => c['type'] == 'مقالة')
                                    .toList(),
                                _buildArticleCard),
                            _buildSection(
                                "المهام اليومية", _dailyTasks, _buildTaskCard),
                            _buildSection("منشورات الأعضاء", _memberPosts,
                                _buildMemberPostCard),
                            _buildSection("دورات ذات صلة", _relatedCourses,
                                _buildRelatedCourseCard),
                          ],
                        ),
                      ),
              ),
            ],
          ),
        ],
      ),
      floatingActionButton: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          FloatingActionButton(
            onPressed: _showCreateChatRoomDialog,
            backgroundColor: Colors.teal[700],
            heroTag: 'chat',
            child: const FaIcon(FontAwesomeIcons.comments, color: Colors.white),
          ),
          const SizedBox(height: 16),
          FloatingActionButton(
            onPressed: _showAddContentDialog,
            backgroundColor: Colors.teal[700],
            heroTag: 'addContent',
            child: const FaIcon(FontAwesomeIcons.plus, color: Colors.white),
          ),
        ],
      ),
    );
  }

  Widget _buildSection(String title, List<Map<String, dynamic>> items,
      Widget Function(Map<String, dynamic>) builder) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Text(title,
              style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.teal[800])),
        ),
        SizedBox(
          height: 220,
          child: items.isEmpty
              ? const Center(
                  child: Text("لا توجد عناصر متاحة",
                      style: TextStyle(color: Colors.grey)))
              : ListView.builder(
                  scrollDirection: Axis.horizontal,
                  itemCount: items.length,
                  itemBuilder: (context, index) =>
                      ElasticIn(child: builder(items[index])),
                ),
        ),
      ],
    );
  }

  Widget _buildVideoCard(Map<String, dynamic> content) {
    return GestureDetector(
      onTap: () {},
      child: Container(
        width: 220,
        margin: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            colors: [Colors.teal[700]!, Colors.teal[300]!],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          boxShadow: [
            BoxShadow(
                color: Colors.teal.withOpacity(0.5),
                spreadRadius: 2,
                blurRadius: 8)
          ],
        ),
        child: Column(
          children: [
            ClipRRect(
              borderRadius:
                  const BorderRadius.vertical(top: Radius.circular(16)),
              child: Image.network(
                '${AppState.getBackendUrl()}${content['file']}',
                height: 120,
                width: 220,
                fit: BoxFit.cover,
                errorBuilder: (_, __, ___) =>
                    const Icon(Icons.error, color: Colors.red),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Text(
                content['title'],
                style: const TextStyle(
                    color: Colors.white, fontWeight: FontWeight.bold),
                textAlign: TextAlign.center,
              ),
            ),
            ElevatedButton(
              onPressed: () {},
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.white,
                foregroundColor: Colors.teal,
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8)),
              ),
              child: const Text("تشغيل"),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBookCard(Map<String, dynamic> content) {
    return GestureDetector(
      onTap: () {},
      child: Container(
        width: 140,
        margin: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            colors: [Colors.teal[700]!, Colors.teal[300]!],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          boxShadow: [
            BoxShadow(
                color: Colors.teal.withOpacity(0.5),
                spreadRadius: 2,
                blurRadius: 8)
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const FaIcon(FontAwesomeIcons.book, size: 80, color: Colors.white),
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Text(
                content['title'],
                style: const TextStyle(
                    color: Colors.white, fontWeight: FontWeight.bold),
                textAlign: TextAlign.center,
              ),
            ),
            IconButton(
              icon:
                  const FaIcon(FontAwesomeIcons.download, color: Colors.white),
              onPressed: () {},
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildArticleCard(Map<String, dynamic> content) {
    return GestureDetector(
      onTap: () {},
      child: Container(
        width: 220,
        margin: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            colors: [Colors.teal[700]!, Colors.teal[300]!],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          boxShadow: [
            BoxShadow(
                color: Colors.teal.withOpacity(0.5),
                spreadRadius: 2,
                blurRadius: 8)
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const FaIcon(FontAwesomeIcons.newspaper,
                size: 80, color: Colors.white),
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Text(
                content['title'],
                style: const TextStyle(
                    color: Colors.white, fontWeight: FontWeight.bold),
                textAlign: TextAlign.center,
              ),
            ),
            TextButton(
              onPressed: () {},
              style: TextButton.styleFrom(foregroundColor: Colors.white),
              child: const Text("قراءة"),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTaskCard(Map<String, dynamic> task) {
    return ListTile(
      leading: FaIcon(
        task['completed']
            ? FontAwesomeIcons.circleCheck
            : FontAwesomeIcons.circle,
        color: task['completed'] ? Colors.green : Colors.grey,
      ),
      title: Text(task['task']),
      subtitle: LinearProgressIndicator(
        value: task['completed'] ? 1.0 : 0.0,
        backgroundColor: Colors.grey[300],
        valueColor: AlwaysStoppedAnimation(Colors.teal[700]),
        minHeight: 4,
      ),
      trailing: Text("+${task['points']} نقاط",
          style: TextStyle(color: Colors.teal[700])),
      onTap: () => _completeTask(_dailyTasks.indexOf(task)),
    );
  }

  Widget _buildMemberPostCard(Map<String, dynamic> post) {
    return Card(
      margin: const EdgeInsets.all(8),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      elevation: 8,
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ListTile(
              leading: CircleAvatar(child: Text(post['memberName'][0])),
              title: Row(
                children: [
                  Text(post['memberName'],
                      style: const TextStyle(fontWeight: FontWeight.bold)),
                  if (post['isPinned'])
                    const Padding(
                      padding: EdgeInsets.only(right: 8.0),
                      child: FaIcon(FontAwesomeIcons.thumbtack,
                          color: Colors.teal, size: 16),
                    ),
                ],
              ),
              subtitle: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(post['postContent']),
                  const SizedBox(height: 8),
                  if (post['attachedFile'] != null)
                    ClipRRect(
                      borderRadius: BorderRadius.circular(12),
                      child: Image.network(
                        '${AppState.getBackendUrl()}${post['attachedFile']}',
                        height: 100,
                        fit: BoxFit.cover,
                        errorBuilder: (_, __, ___) =>
                            const Icon(Icons.error, color: Colors.red),
                      ),
                    ),
                ],
              ),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text("${post['views']} مشاهدة",
                    style: const TextStyle(color: Colors.grey)),
                Row(
                  children: [
                    IconButton(
                      icon: FaIcon(
                        post['isLiked']
                            ? FontAwesomeIcons.thumbsUp
                            : FontAwesomeIcons.thumbsUp,
                        color: post['isLiked'] ? Colors.teal : Colors.grey,
                      ),
                      onPressed: () async {
                        final appState =
                            Provider.of<AppState>(context, listen: false);
                        final response = await http.post(
                          Uri.parse('${AppState.getBackendUrl()}/posts/like'),
                          headers: {
                            'x-auth-token': appState.token ?? defaultAuthToken,
                            "Content-Type": "application/json"
                          },
                          body: jsonEncode({"postId": post['id']}),
                        );
                        if (response.statusCode == 200) {
                          setState(() {
                            post['isLiked'] = !post['isLiked'];
                            post['likes'] += post['isLiked'] ? 1 : -1;
                          });
                        }
                      },
                    ),
                    Text("${post['likes']}",
                        style: TextStyle(color: Colors.teal[700])),
                  ],
                ),
                IconButton(
                  icon:
                      const FaIcon(FontAwesomeIcons.share, color: Colors.teal),
                  onPressed: () {},
                ),
                IconButton(
                  icon: const FaIcon(
                    FontAwesomeIcons.thumbtack,
                    color: Colors.teal,
                  ),
                  onPressed: () => _pinPost(post['id']),
                ),
              ],
            ),
            Column(
              children: [
                Text("التعليقات (${post['comments'].length})",
                    style: TextStyle(color: Colors.teal[700])),
                ...post['comments']
                    .map<Widget>((comment) => Text(comment,
                        style: const TextStyle(color: Colors.grey)))
                    .toList(),
                Row(
                  children: [
                    Expanded(
                      child: TextField(
                        controller: _commentController,
                        decoration: InputDecoration(
                          hintText: "أضف تعليقك...",
                          filled: true,
                          fillColor: Colors.grey[100],
                          border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide.none),
                        ),
                        textDirection: TextDirection.rtl,
                      ),
                    ),
                    IconButton(
                      icon: const FaIcon(FontAwesomeIcons.paperPlane,
                          color: Colors.teal),
                      onPressed: () async {
                        if (_commentController.text.isNotEmpty) {
                          final appState =
                              Provider.of<AppState>(context, listen: false);
                          final response = await http.post(
                            Uri.parse(
                                '${AppState.getBackendUrl()}/posts/comment'),
                            headers: {
                              'x-auth-token': appState.token ?? defaultAuthToken,
                              "Content-Type": "application/json"
                            },
                            body: jsonEncode({
                              "postId": post['id'],
                              "comment": _commentController.text
                            }),
                          );
                          if (response.statusCode == 200) {
                            setState(() =>
                                post['comments'].add(_commentController.text));
                            _commentController.clear();
                          }
                        }
                      },
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRelatedCourseCard(Map<String, dynamic> course) {
    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
              builder: (context) =>
                  CourseDetailsPage1(courseTitle: course['title'])),
        );
      },
      child: Container(
        width: 180,
        margin: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            colors: [Colors.teal[700]!, Colors.teal[300]!],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          boxShadow: [
            BoxShadow(
                color: Colors.teal.withOpacity(0.5),
                spreadRadius: 2,
                blurRadius: 8)
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const FaIcon(FontAwesomeIcons.bookOpen,
                size: 60, color: Colors.white),
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Text(
                course['title'],
                style: const TextStyle(
                    color: Colors.white, fontWeight: FontWeight.bold),
                textAlign: TextAlign.center,
              ),
            ),
            TextButton(
              onPressed: () {},
              style: TextButton.styleFrom(foregroundColor: Colors.white),
              child: const Text("استكشاف"),
            ),
          ],
        ),
      ),
    );
  }

  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.teal[700],
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      ),
    );
  }
}
