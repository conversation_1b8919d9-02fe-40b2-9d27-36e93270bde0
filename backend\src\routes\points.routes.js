const express = require('express');
const router = express.Router();
const { check } = require('express-validator');
const pointsController = require('../controllers/points.controller');
const { protect } = require('../middleware/authmiddleware');

/**
 * @route   POST /api/points
 * @desc    Add points to user
 * @access  Private
 */
router.post(
  '/',
  protect,
  [
    check('points', 'Points must be a positive number').isInt({ min: 1 }),
    check('reason', 'Reason is required').not().isEmpty()
  ],
  pointsController.addPoints
);

/**
 * @route   GET /api/points/history
 * @desc    Get user's points history
 * @access  Private
 */
router.get(
  '/history',
  protect,
  pointsController.getPointsHistory
);

/**
 * @route   GET /api/points/summary
 * @desc    Get user's current points and level
 * @access  Private
 */
router.get(
  '/summary',
  protect,
  pointsController.getPointsSummary
);

/**
 * @route   POST /api/points/redeem
 * @desc    Redeem points for rewards
 * @access  Private
 */
router.post(
  '/redeem',
  protect,
  [
    check('rewardId', 'Reward ID is required').not().isEmpty(),
    check('points', 'Points must be a positive number').isInt({ min: 1 })
  ],
  pointsController.redeemPoints
);

module.exports = router;
