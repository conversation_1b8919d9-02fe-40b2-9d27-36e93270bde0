import 'package:uuid/uuid.dart';

class Story {
  final String id;
  final String user;
  final String image;
  final String date;
  final String? profileImagePath;
  final bool viewed;

  Story({
    required this.id,
    required this.user,
    required this.image,
    required this.date,
    this.profileImagePath,
    this.viewed = false,
  });

  factory Story.fromJson(Map<String, dynamic> json) {
    return Story(
      id: json['_id'] ?? const Uuid().v4(),
      user: json['user'] ?? 'غير معروف',
      image: json['image'] ?? '',
      date: json['createdAt'] ?? DateTime.now().toString(),
      profileImagePath: json['profileImagePath'],
      viewed: json['viewed'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user': user,
      'image': image,
      'date': date,
      'profileImagePath': profileImagePath,
      'viewed': viewed,
    };
  }

  Story copyWith({
    String? id,
    String? user,
    String? image,
    String? date,
    String? profileImagePath,
    bool? viewed,
  }) {
    return Story(
      id: id ?? this.id,
      user: user ?? this.user,
      image: image ?? this.image,
      date: date ?? this.date,
      profileImagePath: profileImagePath ?? this.profileImagePath,
      viewed: viewed ?? this.viewed,
    );
  }
}
