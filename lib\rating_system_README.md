# نظام التقييم - Rating System

## نظرة عامة

نظام التقييم هو نظام متكامل لتقييم إعلانات التبرع في التطبيق. يتيح للمستخدمين تقييم الإعلانات وإضافة تعليقات عليها، مما يساعد في بناء الثقة وتحسين جودة الإعلانات.

## المكونات الرئيسية

### 1. نماذج البيانات (Data Models)

#### `Rating`
نموذج يمثل تقييم واحد:
- `id`: معرف التقييم
- `adId`: معرف الإعلان
- `donorId`: معرف المتبرع
- `donorName`: اسم المتبرع
- `donorAvatarUrl`: رابط صورة المتبرع
- `value`: قيمة التقييم (1-5 نجوم)
- `comment`: تعليق التقييم (اختياري)
- `createdAt`: تاريخ الإنشاء
- `updatedAt`: تاريخ التحديث
- `isVerified`: هل التقييم موثق
- `helpfulCount`: عدد الأشخاص الذين وجدوا التقييم مفيداً

#### `RatingStats`
نموذج يمثل إحصائيات التقييم:
- `averageRating`: متوسط التقييم
- `totalRatings`: إجمالي عدد التقييمات
- `ratingDistribution`: توزيع التقييمات (1-5 نجوم)
- `totalComments`: إجمالي عدد التعليقات

### 2. خدمة التقييم (Rating Service)

#### `RatingService`
خدمة للتفاعل مع API التقييمات:

**الطرق المتاحة:**
- `fetchRatings()`: جلب تقييمات إعلان معين
- `fetchRatingStats()`: جلب إحصائيات التقييم
- `addRating()`: إضافة تقييم جديد
- `updateRating()`: تحديث تقييم موجود
- `deleteRating()`: حذف تقييم
- `markRatingHelpful()`: تسجيل التقييم كمفيد

### 3. مكونات واجهة المستخدم (UI Components)

#### `StarRating`
مكون لعرض النجوم التفاعلية:
- يدعم التقييمات النصفية
- قابل للتخصيص (الحجم، اللون)
- يدعم التفاعل (إضافة تقييم)

#### `RatingDisplay`
مكون لعرض التقييم مع النص:
- يعرض النجوم والمتوسط
- يعرض عدد التقييمات

#### `RatingSummary`
مكون مختصر لعرض التقييم في البطاقات:
- يعرض التقييم المختصر
- قابل للنقر للانتقال لصفحة التقييمات
- يدعم التحميل التلقائي للبيانات

#### `AddRatingWidget`
مكون لإضافة تقييم جديد:
- اختيار التقييم بالنجوم
- إضافة تعليق اختياري
- التحقق من صحة البيانات

#### `RatingsList`
مكون لعرض قائمة التقييمات:
- عرض إحصائيات التقييم
- قائمة التقييمات مع التعليقات
- خيارات الترتيب والتصفية
- إمكانية تسجيل التقييم كمفيد

#### `RatingsScreen`
شاشة كاملة لعرض التقييمات:
- تجمع جميع مكونات التقييم
- إمكانية إضافة تقييم جديد
- عرض قائمة التقييمات

## كيفية الاستخدام

### 1. إضافة التقييم في بطاقة الإعلان

```dart
// في بطاقة الإعلان
RatingSummary(
  adId: ad.id,
  initialRating: ad.metadata['averageRating']?.toDouble(),
  initialCount: ad.metadata['ratingCount']?.toInt(),
)
```

### 2. إضافة زر التقييمات في تفاصيل الإعلان

```dart
// في تفاصيل الإعلان
OutlinedButton.icon(
  onPressed: () => _showRatingsScreen(ad),
  icon: const Icon(Icons.star),
  label: const Text('عرض التقييمات'),
)
```

### 3. عرض شاشة التقييمات الكاملة

```dart
Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => RatingsScreen(
      adId: ad.id,
      adTitle: ad.title,
      currentUserId: currentUserId,
    ),
  ),
);
```

### 4. إضافة تقييم جديد

```dart
AddRatingWidget(
  adId: ad.id,
  donorId: currentUserId,
  onRatingAdded: (rating) {
    // تحديث الواجهة بعد إضافة التقييم
    setState(() {
      // تحديث البيانات
    });
  },
)
```

## التكامل مع API

### إعداد الخدمة

```dart
final ratingService = RatingService(
  baseUrl: '${AppState.getBackendUrl()}/api',
  authToken: AppState.signerKey,
);
```

### نقاط النهاية المطلوبة

1. `GET /api/ratings/{adId}` - جلب تقييمات إعلان
2. `GET /api/ratings/{adId}/stats` - جلب إحصائيات التقييم
3. `POST /api/ratings` - إضافة تقييم جديد
4. `PUT /api/ratings/{ratingId}` - تحديث تقييم
5. `DELETE /api/ratings/{ratingId}` - حذف تقييم
6. `POST /api/ratings/{ratingId}/helpful` - تسجيل التقييم كمفيد

## الميزات المتقدمة

### 1. التقييمات الموثقة
- تمييز التقييمات من متبرعين موثقين
- عرض شارة التحقق

### 2. نظام المفيد
- إمكانية تسجيل التقييم كمفيد
- ترتيب التقييمات حسب الفائدة

### 3. التصفية والترتيب
- ترتيب حسب الأحدث/الأقدم
- ترتيب حسب التقييم الأعلى/الأقل
- ترتيب حسب الأكثر فائدة

### 4. التحديثات المباشرة
- تحديث التقييمات في الوقت الفعلي
- إشعارات للتقييمات الجديدة

## التخصيص

### الألوان
```dart
StarRating(
  color: Colors.amber, // لون النجوم المملوءة
  unratedColor: Colors.grey, // لون النجوم الفارغة
)
```

### الأحجام
```dart
StarRating(
  size: 24.0, // حجم النجوم
)
```

### النصوص
جميع النصوص قابلة للتخصيص من خلال ملفات الترجمة.

## الأمان والخصوصية

1. **التحقق من الهوية**: جميع طلبات API تتطلب رمز مصادقة
2. **حماية البيانات**: لا يتم عرض معلومات شخصية حساسة
3. **منع التقييمات المكررة**: كل مستخدم يمكنه تقييم إعلان واحد مرة واحدة
4. **التحقق من المحتوى**: فحص التعليقات للمحتوى غير المناسب

## استكشاف الأخطاء

### مشاكل شائعة

1. **فشل في تحميل التقييمات**
   - تحقق من الاتصال بالإنترنت
   - تحقق من صحة رمز المصادقة
   - تحقق من صحة معرف الإعلان

2. **فشل في إضافة التقييم**
   - تحقق من أن المستخدم لم يقم بالتقييم من قبل
   - تحقق من صحة قيمة التقييم (1-5)
   - تحقق من طول التعليق

3. **مشاكل في العرض**
   - تحقق من إعدادات التطبيق
   - تحقق من ملفات الترجمة
   - تحقق من إعدادات الخط

## التطوير المستقبلي

1. **تقييمات متعددة المستويات**: تقييم جوانب مختلفة من الإعلان
2. **صور في التقييمات**: إمكانية إضافة صور للتعليقات
3. **ردود على التقييمات**: إمكانية الرد على التقييمات
4. **تقييمات مجهولة**: خيار التقييم بدون كشف الهوية
5. **تحليلات متقدمة**: إحصائيات مفصلة عن التقييمات 