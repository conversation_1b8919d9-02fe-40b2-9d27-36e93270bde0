const cors = require('cors');
const rateLimit = require('express-rate-limit');

const corsMiddleware = () => {
  // قوائم المواقع المسموح بها
  const allowedOrigins = process.env.ALLOWED_ORIGINS?.split(',').map(origin => origin.trim()) || [];
  const allowedMethods = ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'];
  const allowedHeaders = [
    'Content-Type',
    'Authorization',
    'X-Requested-With',
    'Accept',
    'Origin',
    'Access-Control-Allow-Origin'
  ];

  // قيود على عدد الطلبات
  const limiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 دقائق
    max: 100, // 100 طلب في 15 دقيقة
    standardHeaders: true,
    legacyHeaders: false
  });

  return cors({
    origin: function (origin, callback) {
      if (!origin || allowedOrigins.includes('*') || allowedOrigins.includes(origin)) {
        callback(null, true);
      } else {
        callback(null, false);
      }
    },
    credentials: true,
    methods: allowedMethods,
    allowedHeaders: allowedHeaders,
    exposedHeaders: ['Content-Length', 'X-Foo', 'X-Bar'],
    maxAge: 3600,
    preflightContinue: false,
    optionsSuccessStatus: 204,
    optionsDelegate: (req, callback) => {
      const headers = {
        'Access-Control-Allow-Origin': req.headers.origin,
        'Access-Control-Allow-Methods': allowedMethods.join(','),
        'Access-Control-Allow-Headers': allowedHeaders.join(','),
        'Access-Control-Max-Age': 3600
      };
      callback(null, headers);
    }
  });
};

module.exports = {
  corsMiddleware,
  limiter
};
};

module.exports = corsMiddleware;
