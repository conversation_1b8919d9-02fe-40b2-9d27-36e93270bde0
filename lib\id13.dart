import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:animate_do/animate_do.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_pdfview/flutter_pdfview.dart';
import 'package:http/http.dart' as http;
import 'package:path_provider/path_provider.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:share_plus/share_plus.dart';
import 'package:socket_io_client/socket_io_client.dart' as io;
import 'package:timeago/timeago.dart' as timeago;
import 'package:youtube_player_flutter/youtube_player_flutter.dart';
import 'appstate.dart';
// import 'models/user_model.dart'; // No utilizado
import 'models/species_model.dart';
// Initialize timeago for Arabic
void setupTimeago() => timeago.setLocaleMessages('ar', timeago.ArMessages());


// Nature Service

void _showAddContentDialog(BuildContext context) {
  final TextEditingController _contentController = TextEditingController();
  showDialog(
    context: context,
    builder: (context) => AlertDialog(
      title: const Text('إضافة محتوى جديد'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          TextField(
            controller: _contentController,
            decoration: const InputDecoration(
              labelText: 'المحتوى',
              border: OutlineInputBorder(),
            ),
            maxLines: 4,
            textDirection: TextDirection.rtl,
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('إغلاق'),
        ),
        ElevatedButton(
          onPressed: () {
            final content = _contentController.text.trim();
            if (content.isNotEmpty) {
              // هنا منطق إضافة المحتوى الفعلي (مثلاً: استدعاء API أو إضافة للقائمة)
              print('تمت إضافة المحتوى: ' + content);
              Navigator.of(context).pop();
            }
          },
          child: const Text('إرسال'),
        ),
      ],
    ),
  );
}

class NatureService {
  final String baseUrl;

  NatureService() : baseUrl = AppState.getBackendUrl();

  Future<List<Map<String, dynamic>>> fetchNotifications(String token) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/notifications'),
        headers: {'x-auth-token': token},
      ).timeout(const Duration(seconds: 10));
      if (response.statusCode == 200) {
        return List<Map<String, dynamic>>.from(jsonDecode(response.body));
      }
      throw Exception('فشل في جلب الإشعارات: ${response.body}');
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }

  Future<void> markNotificationRead(String token, String notificationId) async {
    try {
      final response = await http.put(
        Uri.parse('$baseUrl/notifications/$notificationId/read'),
        headers: {'x-auth-token': token},
      );
      if (response.statusCode != 200) {
        throw Exception('فشل في تحديث الإشعار');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }

  Future<List<Species>> fetchSpecies(String token, String category,
      {int page = 1, int limit = 20}) async {
    try {
      final response = await http.get(
        Uri.parse(
            '$baseUrl/species?category=$category&page=$page&limit=$limit'),
        headers: {'x-auth-token': token},
      );
      if (response.statusCode == 200) {
        return (jsonDecode(response.body) as List)
            .map((json) => Species.fromJson(json))
            .toList();
      }
      throw Exception('فشل في جلب الأنواع');
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }

  Future<List<Map<String, dynamic>>> fetchContributions(
      String token, String species) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/contributions/$species'),
        headers: {'x-auth-token': token},
      );
      if (response.statusCode == 200) {
        return List<Map<String, dynamic>>.from(jsonDecode(response.body));
      }
      throw Exception('فشل في جلب المساهمات');
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }

  Future<List<Map<String, dynamic>>> fetchPosts(String token, String species,
      {int page = 1, int limit = 10}) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/nature-posts/$species?page=$page&limit=$limit'),
        headers: {'x-auth-token': token},
      );
      if (response.statusCode == 200) {
        return List<Map<String, dynamic>>.from(jsonDecode(response.body));
      }
      throw Exception('فشل في جلب المنشورات');
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }

  Future<void> addContribution(String token, String species, String title,
      File file, String type) async {
    try {
      final request =
      http.MultipartRequest('POST', Uri.parse('$baseUrl/contributions'));
      request.headers['x-auth-token'] = token;
      request.fields['speciesTitle'] = species;
      request.fields['title'] = title;
      request.fields['type'] = type;
      if (await file.length() > 5 * 1024 * 1024) {
        throw Exception('حجم الملف كبير جدًا (يجب أن يكون أقل من 5 ميجابايت)');
      }
      request.files.add(await http.MultipartFile.fromPath('file', file.path));
      final response = await request.send();
      if (response.statusCode != 201) {
        throw Exception('فشل في إضافة المساهمة');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }

  Future<void> addPost(String token, String species, String content, File? file,
      String? fileType, String username) async {
    try {
      final request =
      http.MultipartRequest('POST', Uri.parse('$baseUrl/nature-posts'));
      request.headers['x-auth-token'] = token;
      request.fields['speciesTitle'] = species;
      request.fields['memberName'] = username;
      request.fields['content'] =
      content.isNotEmpty ? content : 'منشور بدون نص';
      if (file != null && fileType != null) {
        if (await file.length() > 5 * 1024 * 1024) {
          throw Exception(
              'حجم الملف كبير جدًا (يجب أن يكون أقل من 5 ميجابايت)');
        }
        request.fields['fileType'] = fileType;
        request.files.add(await http.MultipartFile.fromPath('file', file.path));
      }
      final response = await request.send();
      if (response.statusCode != 201) {
        throw Exception('فشل في النشر');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }

  Future<void> likePost(String token, String postId) async {
    try {
      final response = await http.put(
        Uri.parse('$baseUrl/nature-posts/$postId/like'),
        headers: {'x-auth-token': token},
      );
      if (response.statusCode != 200) {
        throw Exception('فشل في الإعجاب');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }

  Future<void> commentPost(String token, String postId, String comment) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/nature-posts/$postId/comment'),
        headers: {'Content-Type': 'application/json', 'x-auth-token': token},
        body: jsonEncode({'comment': comment}),
      );
      if (response.statusCode != 201) {
        throw Exception('فشل في التعليق');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }

  Future<void> toggleFavorite(
      String token, String speciesTitle, bool isFavorite) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/favorites'),
        headers: {'Content-Type': 'application/json', 'x-auth-token': token},
        body: jsonEncode(
            {'speciesTitle': speciesTitle, 'isFavorite': isFavorite}),
      );
      if (response.statusCode != 200) {
        throw Exception('فشل في تحديث المفضلة');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }

  Future<bool> isFavorite(String token, String speciesTitle) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/favorites/$speciesTitle'),
        headers: {'x-auth-token': token},
      );
      if (response.statusCode == 200) {
        return jsonDecode(response.body)['isFavorite'] ?? false;
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  Future<String?> cachePdf(String url, String speciesTitle) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final filePath = '${directory.path}/${speciesTitle}_${url.hashCode}.pdf';
      final file = File(filePath);
      if (await file.exists()) return filePath;
      final response = await http.get(Uri.parse(url));
      if (response.statusCode == 200) {
        await file.writeAsBytes(response.bodyBytes);
        return filePath;
      }
      return null;
    } catch (e) {
      return null;
    }
  }
}

class Rayan13 extends StatefulWidget {
  const Rayan13({super.key});

  @override
  _Rayan13State createState() => _Rayan13State();
}

class _Rayan13State extends State<Rayan13> with TickerProviderStateMixin {
  // Flag to prevent nested mouse event handling
  bool _isHandlingMouseEvent = false;
  final NatureService _natureService = NatureService();
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  List<Map<String, dynamic>> _notifications = [];
  List<Species> _animals = [];
  List<Species> _plants = [];
  List<Species> _insects = [];
  List<Species> _fish = [];
  bool _isLoading = true;
  Timer? _debounce;
  late AnimationController _searchAnimationController;
  late Animation<double> _searchAnimation;
  
  // Propiedades computadas para filtrar especies según el texto de búsqueda
  List<Species> get filteredAnimals {
    if (_searchController.text.isEmpty) return _animals;
    return _animals.where((species) =>
        species.title.toLowerCase().contains(_searchController.text.toLowerCase()))
        .toList();
  }
  
  List<Species> get filteredPlants {
    if (_searchController.text.isEmpty) return _plants;
    return _plants.where((species) =>
        species.title.toLowerCase().contains(_searchController.text.toLowerCase()))
        .toList();
  }
  
  List<Species> get filteredInsects {
    if (_searchController.text.isEmpty) return _insects;
    return _insects.where((species) =>
        species.title.toLowerCase().contains(_searchController.text.toLowerCase()))
        .toList();
  }
  
  List<Species> get filteredFish {
    if (_searchController.text.isEmpty) return _fish;
    return _fish.where((species) =>
        species.title.toLowerCase().contains(_searchController.text.toLowerCase()))
        .toList();
  }

  @override
  void initState() {
    super.initState();
    setupTimeago();
    _fetchData();
    _searchController.addListener(_onSearch);

    _searchAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    _searchAnimation = Tween<double>(begin: 0.9, end: 1.0).animate(
      CurvedAnimation(
        parent: _searchAnimationController,
        curve: Curves.easeInOut,
      ),
    );
  }

  Future<void> _fetchData() async {
    setState(() => _isLoading = true);
    final appState = Provider.of<AppState>(context, listen: false);
    try {
      await Future.wait([
        _natureService
            .fetchSpecies(appState.token ?? '', 'animals')
            .then((value) => _animals = value),
        _natureService
            .fetchSpecies(appState.token ?? '', 'plants')
            .then((value) => _plants = value),
        _natureService
            .fetchSpecies(appState.token ?? '', 'insects')
            .then((value) => _insects = value),
        _natureService
            .fetchSpecies(appState.token ?? '', 'fish')
            .then((value) => _fish = value),
        _natureService
            .fetchNotifications(appState.token ?? '')
            .then((value) => _notifications = value),
      ]);
      setState(() => _isLoading = false);
    } catch (e) {
      _showSnackBar('خطأ في جلب البيانات: $e', Colors.red);
      setState(() => _isLoading = false);
    }
  }

  void _onSearch() {
    if (_debounce?.isActive ?? false) _debounce!.cancel();
    _debounce = Timer(const Duration(milliseconds: 300), () {
      // Prevent nested mouse event handling
      if (_isHandlingMouseEvent) return;
      _isHandlingMouseEvent = true;
      
      // Use a microtask to avoid triggering during a frame
      Future.microtask(() {
        setState(() {
          _searchAnimationController.forward().then((_) =>
              _searchAnimationController.reverse());
        });
        _isHandlingMouseEvent = false;
      });
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _debounce?.cancel();
    _searchAnimationController.dispose();
    super.dispose();
  }

  void _showSnackBar(String message, [Color? backgroundColor]) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: backgroundColor ?? Colors.green,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        elevation: 6,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return Scaffold(
      appBar: AppBar(
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: isDarkMode
                  ? [Colors.green[900]!, Colors.green[700]!]
                  : [Colors.green, Colors.greenAccent],
            ),
          ),
        ),
        title: ScaleTransition(
          scale: _searchAnimation,
          child: TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'بحث في عالم الطبيعة...',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(20),
                borderSide: BorderSide.none,
              ),
              filled: true,
              fillColor: isDarkMode ? Colors.grey[800] : Colors.white.withOpacity(0.9),
              hintStyle: TextStyle(color: isDarkMode ? Colors.grey[400] : Colors.grey),
              prefixIcon: const Icon(Icons.search, color: Colors.grey),
            ),
            style: TextStyle(color: isDarkMode ? Colors.white : Colors.black87),
            textDirection: TextDirection.rtl,
          ),
        ),
        actions: [
          IconButton(
            icon: Stack(
              children: [
                const Icon(Icons.notifications),
                if (_notifications.isNotEmpty)
                  Positioned(
                    right: 0,
                    child: Container(
                      padding: const EdgeInsets.all(2),
                      decoration: const BoxDecoration(
                          color: Colors.red, shape: BoxShape.circle),
                      constraints:
                      const BoxConstraints(minWidth: 12, minHeight: 12),
                      child: Text(
                        '${_notifications.length}',
                        style:
                        const TextStyle(color: Colors.white, fontSize: 8),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
              ],
            ),
            onPressed: () => _showNotifications(context),
          ),
        ],
      ),
      body: _isLoading
          ? _buildSkeletonLoader()
          : RefreshIndicator(
        onRefresh: _fetchData,
        color: Colors.green,
        child: CustomScrollView(
          slivers: [
            SliverList(
              delegate: SliverChildListDelegate([
                _buildCategorySection(
                    context, "عالم الحيوانات", filteredAnimals),
                _buildCategorySection(
                    context, "عالم النباتات", filteredPlants),
                _buildCategorySection(
                    context, "عالم الحشرات", filteredInsects),
                _buildCategorySection(
                    context, "عالم الأسماك", filteredFish),
              ]),
            ),
          ],
        ),
      ),
      floatingActionButton: Consumer<AppState>(
        builder: (context, appState, _) {
          final isHokama = appState.userRole == 'hokama' ||
              (appState.currentUser?.role.name?.toLowerCase() == 'hokama');
          return isHokama
              ? FloatingActionButton(
                  onPressed: () => _showAddContentDialog(context),
                  backgroundColor: Colors.green,
                  child: const Icon(Icons.add),
                )
              : const SizedBox.shrink();
        },
      ),
    );
  }

  Widget _buildSkeletonLoader() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return CustomScrollView(
      slivers: [
        SliverList(
          delegate: SliverChildBuilderDelegate(
                (context, index) => Padding(
              padding: const EdgeInsets.all(8.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    width: 150,
                    height: 20,
                    color: isDarkMode ? Colors.grey[800] : Colors.grey[300],
                  ),
                  const SizedBox(height: 8),
                  SizedBox(
                    height: 200,
                    child: ListView.builder(
                      scrollDirection: Axis.horizontal,
                      itemCount: 5,
                      itemBuilder: (context, _) => Container(
                        width: 150,
                        margin: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: isDarkMode ? Colors.grey[800] : Colors.grey[300],
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            childCount: 4,
          ),
        ),
      ],
    );
  }

  Widget _buildCategorySection(
      BuildContext context, String categoryName, List<Species> items) {
    if (items.isEmpty) return const SizedBox.shrink();
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: FadeInDown(
              child: Text(
                categoryName,
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: isDarkMode ? Colors.white : Colors.black87,
                ),
              ),
            ),
          ),
          SizedBox(
            height: 200,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: items.length,
              itemBuilder: (context, index) => FadeInRight(
                child: GestureDetector(
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => SpeciesDetailsPage(
                          speciesTitle: items[index].title,
                          speciesImage: items[index].image,
                        ),
                      ),
                    );
                  },
                  child: Container(
                    width: 150,
                    margin: const EdgeInsets.all(8),
                    child: Column(
                      children: [
                        Expanded(
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(12),
                            child: CachedNetworkImage(
                              imageUrl: items[index].image,
                              fit: BoxFit.cover,
                              placeholder: (context, url) => Container(
                                color: isDarkMode
                                    ? Colors.grey[800]
                                    : Colors.grey[300],
                                child: const Center(
                                    child: CircularProgressIndicator(
                                        color: Colors.green)),
                              ),
                              errorWidget: (context, url, error) => const Icon(
                                  Icons.broken_image,
                                  size: 50,
                                  color: Colors.grey),
                            ),
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          items[index].title,
                          textAlign: TextAlign.center,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                          style: TextStyle(
                            color: isDarkMode ? Colors.white : Colors.black87,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showNotifications(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.5,
        maxChildSize: 0.9,
        builder: (context, scrollController) => Container(
          decoration: BoxDecoration(
            color: Theme.of(context).scaffoldBackgroundColor,
            borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: Column(
            children: [
              Container(
                margin: const EdgeInsets.only(top: 8, bottom: 16),
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey[400],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              const Text(
                'الإشعارات',
                style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              ),
              Expanded(
                child: _notifications.isEmpty
                    ? const Center(child: Text('لا توجد إشعارات'))
                    : ListView.builder(
                  controller: scrollController,
                  itemCount: _notifications.length,
                  itemBuilder: (context, index) {
                    final notification = _notifications[index];
                    return Dismissible(
                      key: Key(notification['_id']),
                      onDismissed: (direction) async {
                        final appState = Provider.of<AppState>(context,
                            listen: false);
                        try {
                          await _natureService.markNotificationRead(
                              appState.token ?? '', notification['_id']);
                          // Use a microtask to avoid triggering during a frame
                          Future.microtask(() {
                            setState(() {
                              _notifications.removeAt(index);
                            });
                          });
                        } catch (e) {
                          _showSnackBar(
                              'خطأ في تحديث الإشعار', Colors.red);
                        }
                      },
                      background: Container(
                        color: Colors.red,
                        alignment: Alignment.centerRight,
                        padding: const EdgeInsets.only(right: 16),
                        child: const Icon(Icons.delete, color: Colors.white),
                      ),
                      child: FadeIn(
                        child: ListTile(
                          leading: ElasticIn(
                            child: const Icon(
                              Icons.notifications_active,
                              color: Colors.green,
                              size: 30,
                            ),
                          ),
                          title: Text(
                            notification['message'] ?? 'إشعار',
                            style: TextStyle(
                              fontWeight: !(notification['read'] ?? false)
                                  ? FontWeight.bold
                                  : FontWeight.normal,
                            ),
                          ),
                          subtitle: Text(
                            timeago.format(
                                DateTime.parse(notification['date'] ??
                                    DateTime.now().toString()),
                                locale: 'ar'),
                            style: TextStyle(color: Colors.grey[600]),
                          ),
                          onTap: () async {
                            if (!(notification['read'] ?? false)) {
                              final appState = Provider.of<AppState>(
                                  context,
                                  listen: false);
                              try {
                                await _natureService.markNotificationRead(
                                    appState.token ?? '',
                                    notification['_id']);
                                // Use a microtask to avoid triggering during a frame
                                Future.microtask(() {
                                  setState(() {
                                    _notifications[index]['read'] = true;
                                  });
                                });
                              } catch (e) {
                                _showSnackBar(
                                    'خطأ في تحديث الإشعار', Colors.red);
                              }
                            }
                          },
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class SpeciesDetailsPage extends StatefulWidget {
  final String speciesTitle;
  final String speciesImage;

  const SpeciesDetailsPage(
      {super.key, required this.speciesTitle, required this.speciesImage});

  @override
  _SpeciesDetailsPageState createState() => _SpeciesDetailsPageState();
}

class _SpeciesDetailsPageState extends State<SpeciesDetailsPage>
    with SingleTickerProviderStateMixin {
  // Flag to prevent nested mouse event handling
  bool _isHandlingMouseEvent = false;
  final NatureService _natureService = NatureService();
  List<dynamic> _userContributions = [];
  List<dynamic> _posts = [];
  final TextEditingController _postController = TextEditingController();
  File? _attachedFile;
  String? _attachedFileType;
  bool _isLoading = true;
  late TabController _tabController;
  bool _isFavorite = false;
  late AnimationController _favoriteAnimationController;
  late Animation<double> _favoriteAnimation;
  bool _isGridView = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _favoriteAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    _favoriteAnimation = Tween<double>(begin: 1.0, end: 1.3).animate(
      CurvedAnimation(
        parent: _favoriteAnimationController,
        curve: Curves.elasticIn,
      ),
    );
    _fetchData();
    _loadFavoriteStatus();
  }

  Future<void> _fetchData() async {
    setState(() => _isLoading = true);
    final appState = Provider.of<AppState>(context, listen: false);
    try {
      _userContributions = await _natureService.fetchContributions(
          appState.token ?? '', widget.speciesTitle);
      _posts = await _natureService.fetchPosts(
          appState.token ?? '', widget.speciesTitle);
      setState(() => _isLoading = false);
    } catch (e) {
      _showSnackBar('خطأ في جلب البيانات: $e', Colors.red);
      setState(() => _isLoading = false);
    }
  }

  Future<void> _loadFavoriteStatus() async {
    final prefs = await SharedPreferences.getInstance();
    final appState = Provider.of<AppState>(context, listen: false);
    setState(() => _isFavorite = prefs.getBool(widget.speciesTitle) ?? false);
    try {
      _isFavorite = await _natureService.isFavorite(
          appState.token ?? '', widget.speciesTitle);
      await prefs.setBool(widget.speciesTitle, _isFavorite);
      // Use a microtask to avoid triggering during a frame
      Future.microtask(() {
        setState(() {});
      });
    } catch (e) {
      _showSnackBar('خطأ في تحميل المفضلة', Colors.red);
    }
  }

  Future<void> _toggleFavorite() async {
    // Prevent nested mouse event handling
    if (_isHandlingMouseEvent) return;
    _isHandlingMouseEvent = true;
    
    // Use a microtask to avoid triggering during a frame
    Future.microtask(() {
      setState(() {
        _isFavorite = !_isFavorite;
      });
      _isHandlingMouseEvent = false;
    });
    final appState = Provider.of<AppState>(context, listen: false);
    _favoriteAnimationController.forward().then((_) =>
        _favoriteAnimationController.reverse());
    final prefs = await SharedPreferences.getInstance();
    try {
      await _natureService.toggleFavorite(
          appState.token ?? '', widget.speciesTitle, _isFavorite);
      await prefs.setBool(widget.speciesTitle, _isFavorite);
      _showSnackBar(
          _isFavorite ? 'تمت الإضافة إلى المفضلة' : 'تمت الإزالة من المفضلة');
    } catch (e) {
      setState(() => _isFavorite = !_isFavorite);
      _showSnackBar('خطأ في تحديث المفضلة', Colors.red);
    }
  }

  Future<void> _addPost() async {
    if (_postController.text.isEmpty && _attachedFile == null) {
      _showSnackBar('يرجى إدخال نص أو إرفاق ملف', Colors.red);
      return;
    }
    final appState = Provider.of<AppState>(context, listen: false);
    try {
      await _natureService.addPost(
        appState.token ?? '',
        widget.speciesTitle,
        _postController.text,
        _attachedFile,
        _attachedFileType,
        appState.username ?? 'مستخدم',
      );
      _postController.clear();
      // Use a microtask to avoid triggering during a frame
      Future.microtask(() {
        setState(() {
          _attachedFile = _attachedFile = null;
          _attachedFileType = null;
        });
      });
      await _fetchData();
      _showSnackBar('تم النشر بنجاح');
    } catch (e) {
      _showSnackBar('خطأ في النشر: $e', Colors.red);
    }
  }

  Future<bool> _validateFile(File file, String type) async {
    final size = await file.length();
    if (size > 5 * 1024 * 1024) return false;
    if (type == 'صورة' &&
        !['jpg', 'png', 'jpeg']
            .contains(file.path.split('.').last.toLowerCase())) {
      return false;
    }
    if (type == 'فيديو' &&
        !['mp4', 'mov'].contains(file.path.split('.').last.toLowerCase())) {
      return false;
    }
    if (type == 'PDF' && file.path.split('.').last.toLowerCase() != 'pdf') {
      return false;
    }
    return true;
  }

  void _showSnackBar(String message, [Color? backgroundColor]) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: backgroundColor ?? Colors.green,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        elevation: 6,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return Scaffold(
      appBar: AppBar(
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: isDarkMode
                  ? [Colors.green[900]!, Colors.green[700]!]
                  : [Colors.green, Colors.greenAccent],
            ),
          ),
        ),
        title: Text(widget.speciesTitle),
        actions: [
          ScaleTransition(
            scale: _favoriteAnimation,
            child: IconButton(
              icon: Icon(_isFavorite ? Icons.bookmark : Icons.bookmark_border),
              onPressed: _toggleFavorite,
            ),
          ),
          IconButton(
            icon: Icon(_isGridView ? Icons.list : Icons.grid_view),
            onPressed: () => setState(() => _isGridView = !_isGridView),
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(text: "المحتوى"),
            Tab(text: "المنشورات"),
          ],
        ),
      ),
      body: _isLoading
          ? _buildSkeletonLoader()
          : TabBarView(
        controller: _tabController,
        children: [
          _buildContributionsTab(),
          _buildPostsTab(),
        ],
      ),
      floatingActionButton: Consumer<AppState>(
        builder: (context, appState, _) {
          final isHokama = appState.userRole == 'hokama' ||
              (appState.currentUser?.role.name?.toLowerCase() == 'hokama');
          return isHokama
              ? FloatingActionButton(
                  onPressed: () => _showAddContentDialog(context),
                  backgroundColor: Colors.green,
                  child: const Icon(Icons.add),
                )
              : const SizedBox.shrink();
        },
      ),
    );
  }

  Widget _buildSkeletonLoader() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return CustomScrollView(
      slivers: [
        SliverToBoxAdapter(
          child: Container(
            height: 250,
            color: isDarkMode ? Colors.grey[800] : Colors.grey[300],
          ),
        ),
        SliverToBoxAdapter(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  width: 150,
                  height: 20,
                  color: isDarkMode ? Colors.grey[800] : Colors.grey[300],
                ),
                const SizedBox(height: 8),
                Container(
                  width: double.infinity,
                  height: 60,
                  color: isDarkMode ? Colors.grey[800] : Colors.grey[300],
                ),
                const SizedBox(height: 16),
                Container(
                  width: 150,
                  height: 20,
                  color: isDarkMode ? Colors.grey[800] : Colors.grey[300],
                ),
                const SizedBox(height: 8),
                SizedBox(
                  height: 200,
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount: 5,
                    itemBuilder: (context, _) => Container(
                      width: 150,
                      margin: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: isDarkMode ? Colors.grey[800] : Colors.grey[300],
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildContributionsTab() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return CustomScrollView(
      slivers: [
        SliverToBoxAdapter(
          child: FadeIn(
            child: SizedBox(
              height: 250,
              width: double.infinity,
              child: CachedNetworkImage(
                imageUrl: widget.speciesImage,
                fit: BoxFit.cover,
                placeholder: (context, url) => Container(
                  color: isDarkMode ? Colors.grey[800] : Colors.grey[300],
                  child: const Center(
                      child: CircularProgressIndicator(color: Colors.green)),
                ),
                errorWidget: (context, url, error) =>
                const Icon(Icons.broken_image, size: 50, color: Colors.grey),
              ),
            ),
          ),
        ),
        SliverToBoxAdapter(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: FadeInUp(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "عن ${widget.speciesTitle}",
                    style: const TextStyle(
                        fontSize: 20, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    "معلومات حول ${widget.speciesTitle}. يمكنك هنا إضافة تفاصيل مثل الموئل الطبيعي والسلوك.",
                    style: TextStyle(
                      color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
                    ),
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    "المحتوى المضاف",
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  SizedBox(
                    height: 200,
                    child: _userContributions.isEmpty
                        ? Center(
                      child: Text(
                        "لا يوجد محتوى مضاف بعد",
                        style: TextStyle(
                            color: isDarkMode
                                ? Colors.grey[400]
                                : Colors.grey[600]),
                      ),
                    )
                        : ListView.builder(
                      scrollDirection: Axis.horizontal,
                      itemCount: _userContributions.length,
                      itemBuilder: (context, index) => FadeInRight(
                        child: GestureDetector(
                          onTap: () => _showFullContent(
                              context, _userContributions[index]),
                          child: Container(
                            width: 150,
                            margin: const EdgeInsets.all(8),
                            child: Column(
                              children: [
                                Expanded(
                                  child: ClipRRect(
                                    borderRadius: BorderRadius.circular(12),
                                    child: _buildContentPreview(
                                        _userContributions[index]),
                                  ),
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  _userContributions[index]["title"],
                                  textAlign: TextAlign.center,
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                  style: TextStyle(
                                    color: isDarkMode
                                        ? Colors.white
                                        : Colors.black87,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPostsTab() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return RefreshIndicator(
      onRefresh: _fetchData,
      color: Colors.green,
      child: CustomScrollView(
        slivers: [
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: FadeInUp(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    TextField(
                      controller: _postController,
                      maxLines: 3,
                      decoration: InputDecoration(
                        hintText: "اكتب منشورك هنا...",
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        filled: true,
                        fillColor:
                        isDarkMode ? Colors.grey[800] : Colors.grey[100],
                        suffixIcon: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            IconButton(
                              icon: const Icon(Icons.attach_file),
                              onPressed: () => _attachFileDialog(context),
                            ),
                            IconButton(
                              icon: const Icon(Icons.send),
                              onPressed: _addPost,
                            ),
                          ],
                        ),
                      ),
                      style: TextStyle(
                        color: isDarkMode ? Colors.white : Colors.black87,
                      ),
                    ),
                    if (_attachedFile != null)
                      Padding(
                        padding: const EdgeInsets.only(top: 8.0),
                        child: FadeIn(
                          child: Row(
                            children: [
                              ElasticIn(
                                child: Icon(
                                  _attachedFileType == "صورة"
                                      ? Icons.image
                                      : _attachedFileType == "فيديو"
                                      ? Icons.video_library
                                      : Icons.picture_as_pdf,
                                  color: Colors.green,
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  _attachedFile!.path.split('/').last,
                                  overflow: TextOverflow.ellipsis,
                                  style: TextStyle(
                                    color: isDarkMode
                                        ? Colors.white70
                                        : Colors.black87,
                                  ),
                                ),
                              ),
                              IconButton(
                                icon: const Icon(Icons.close, color: Colors.red),
                                onPressed: () => // Use a microtask to avoid triggering during a frame
                                    Future.microtask(() {
                                  setState(() {
                                    _attachedFile = null;
                                    _attachedFileType = null;
                                  });
                                }),
                              ),
                            ],
                          ),
                        ),
                      ),
                    const SizedBox(height: 16),
                    if (_posts.isEmpty)
                      Center(
                        child: Text(
                          "لا توجد منشورات بعد",
                          style: TextStyle(
                              color: isDarkMode
                                  ? Colors.grey[400]
                                  : Colors.grey[600]),
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ),
          _isGridView
              ? SliverGrid(
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: 0.75,
              crossAxisSpacing: 8,
              mainAxisSpacing: 8,
            ),
            delegate: SliverChildBuilderDelegate(
                  (context, index) => FadeInUp(
                child: _buildPostCard(context, index, isGrid: true),
              ),
              childCount: _posts.length,
            ),
          )
              : SliverList(
            delegate: SliverChildBuilderDelegate(
                  (context, index) => FadeInUp(
                child: _buildPostCard(context, index),
              ),
              childCount: _posts.length,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContentPreview(Map<String, dynamic> item) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    if (item["type"] == "فيديو") {
      final String? videoId = YoutubePlayer.convertUrlToId(item["url"]);
      return videoId != null
          ? Stack(
        children: [
          YoutubePlayer(
            controller: YoutubePlayerController(
              initialVideoId: videoId,
              flags: const YoutubePlayerFlags(
                autoPlay: false,
                mute: true,
                disableDragSeek: true,
              ),
            ),
            thumbnail: CachedNetworkImage(
              imageUrl: 'https://img.youtube.com/vi/$videoId/0.jpg',
              fit: BoxFit.cover,
              placeholder: (context, url) => Container(
                color: isDarkMode ? Colors.grey[800] : Colors.grey[300],
              ),
              errorWidget: (context, url, error) =>
              const Icon(Icons.broken_image, color: Colors.grey),
            ),
          ),
          Center(
            child: Icon(
              Icons.play_circle_filled,
              color: Colors.white.withOpacity(0.7),
              size: 40,
            ),
          ),
        ],
      )
          : Container(
        color: isDarkMode ? Colors.grey[800] : Colors.grey[300],
        child: const Center(child: Text("فيديو غير صالح")),
      );
    } else if (item["type"] == "صورة") {
      return CachedNetworkImage(
        imageUrl: item["url"],
        fit: BoxFit.cover,
        placeholder: (context, url) => Container(
          color: isDarkMode ? Colors.grey[800] : Colors.grey[300],
        ),
        errorWidget: (context, url, error) =>
        const Icon(Icons.broken_image, color: Colors.grey),
      );
    } else {
      return FutureBuilder<String?>(
        future: _natureService.cachePdf(item["url"], widget.speciesTitle),
        builder: (context, snapshot) => snapshot.hasData
            ? PDFView(
          filePath: snapshot.data!,
          enableSwipe: false,
          swipeHorizontal: true,
          autoSpacing: false,
          pageFling: false,
        )
            : Container(
          color: isDarkMode ? Colors.grey[800] : Colors.grey[300],
          child: const Center(
              child: CircularProgressIndicator(color: Colors.green)),
        ),
      );
    }
  }

  void _showFullContent(BuildContext context, Map<String, dynamic> item) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        child: Container(
          padding: const EdgeInsets.all(16),
          constraints: const BoxConstraints(maxHeight: 500, maxWidth: 400),
          child: Column(
            children: [
              Text(
                item["title"],
                style:
                const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Expanded(
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: _buildContentPreview(item),
                ),
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => Navigator.pop(context),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10)),
                ),
                child: const Text(
                  "إغلاق",
                  style: TextStyle(color: Colors.white),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPostCard(BuildContext context, int index, {bool isGrid = false}) {
    final post = _posts[index];
    final TextEditingController commentController = TextEditingController();
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    Widget? attachedContent;
    bool isLiked = post['liked'] ?? false;

    if (post['fileUrl'] != null) {
      if (post['fileType'] == "فيديو") {
        final String? videoId = YoutubePlayer.convertUrlToId(post['fileUrl']);
        attachedContent = videoId != null
            ? YoutubePlayer(
          controller: YoutubePlayerController(
            initialVideoId: videoId,
            flags: const YoutubePlayerFlags(autoPlay: false),
          ),
        )
            : Container(
          height: isGrid ? 100 : 200,
          color: isDarkMode ? Colors.grey[800] : Colors.grey[300],
          child: const Center(child: Text("فيديو غير صالح")),
        );
      } else if (post['fileType'] == "صورة") {
        attachedContent = CachedNetworkImage(
          imageUrl: post['fileUrl'],
          fit: BoxFit.cover,
          height: isGrid ? 100 : 200,
          placeholder: (context, url) => Container(
            height: isGrid ? 100 : 200,
            color: isDarkMode ? Colors.grey[800] : Colors.grey[300],
          ),
          errorWidget: (context, url, error) => const Icon(
            Icons.broken_image,
            color: Colors.grey,
          ),
        );
      } else if (post['fileType'] == "PDF") {
        attachedContent = FutureBuilder<String?>(
          future: _natureService.cachePdf(post['fileUrl'], widget.speciesTitle),
          builder: (context, snapshot) => snapshot.hasData
              ? SizedBox(
            height: isGrid ? 100 : 200,
            child: PDFView(
              filePath: snapshot.data!,
              enableSwipe: false,
              swipeHorizontal: true,
              autoSpacing: false,
              pageFling: false,
            ),
          )
              : Container(
            height: isGrid ? 100 : 200,
            color: isDarkMode ? Colors.grey[800] : Colors.grey[300],
            child: const Center(
                child: CircularProgressIndicator(color: Colors.green)),
          ),
        );
      }
    }

    return GestureDetector(
      onDoubleTap: () async {
        if (!isLiked) {
          final appState = Provider.of<AppState>(context, listen: false);
          await _natureService.likePost(appState.token ?? '', post['_id']);
          // Use a microtask to avoid triggering during a frame
          Future.microtask(() {
            setState(() {
              post['likes'] = (post['likes'] ?? 0) + 1;
              post['liked'] = true;
            });
          });
        }
      },
      child: Card(
        margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
        elevation: 4,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        color: isDarkMode ? Colors.grey[850] : Colors.white,
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: isGrid
              ? Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (attachedContent != null) ...[
                ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: attachedContent,
                ),
                const SizedBox(height: 8),
              ],
              Text(
                post['content'],
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
                style: TextStyle(
                  color: isDarkMode ? Colors.white70 : Colors.black87,
                ),
              ),
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      ElasticIn(
                        child: Icon(
                          isLiked
                              ? Icons.favorite
                              : Icons.favorite_border,
                          color: isLiked ? Colors.red : Colors.grey,
                          size: 20,
                        ),
                      ),
                      const SizedBox(width: 4),
                      Text(
                        "${post['likes'] ?? 0}",
                        style: TextStyle(
                            color: isDarkMode
                                ? Colors.white70
                                : Colors.black87),
                      ),
                    ],
                  ),
                  Row(
                    children: [
                      const Icon(Icons.comment,
                          size: 20, color: Colors.grey),
                      const SizedBox(width: 4),
                      Text(
                        "${post['comments'].length}",
                        style: TextStyle(
                            color: isDarkMode
                                ? Colors.white70
                                : Colors.black87),
                      ),
                    ],
                  ),
                ],
              ),
            ],
          )
              : Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  ElasticIn(
                    child: CircleAvatar(
                      backgroundColor: Colors.green,
                      child: Text(post['memberName'][0]),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        post['memberName'],
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                      Text(
                        timeago.format(DateTime.parse(post['timestamp'])),
                        style: TextStyle(
                            color: isDarkMode
                                ? Colors.grey[400]
                                : Colors.grey[600]),
                      ),
                    ],
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                post['content'],
                style: TextStyle(
                  color: isDarkMode ? Colors.white70 : Colors.black87,
                ),
              ),
              if (attachedContent != null) ...[
                const SizedBox(height: 8),
                ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: attachedContent,
                ),
              ],
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      ElasticIn(
                        child: Icon(
                          isLiked
                              ? Icons.favorite
                              : Icons.favorite_border,
                          color: isLiked ? Colors.red : Colors.grey,
                        ),
                      ),
                      const SizedBox(width: 4),
                      Text(
                        "${post['likes'] ?? 0} إعجاب",
                        style: TextStyle(
                            color: isDarkMode
                                ? Colors.white70
                                : Colors.black87),
                      ),
                    ],
                  ),
                  Row(
                    children: [
                      const Icon(Icons.comment, color: Colors.grey),
                      const SizedBox(width: 4),
                      Text(
                        "${post['comments'].length} تعليق",
                        style: TextStyle(
                            color: isDarkMode
                                ? Colors.white70
                                : Colors.black87),
                      ),
                    ],
                  ),
                ],
              ),
              const Divider(),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  TextButton.icon(
                    onPressed: () async {
                      final appState =
                      Provider.of<AppState>(context, listen: false);
                      await _natureService.likePost(
                          appState.token ?? '', post['_id']);
                      // Use a microtask to avoid triggering during a frame
                      Future.microtask(() {
                        setState(() {
                          post['likes'] = (post['likes'] ?? 0) + 1;
                          post['liked'] = true;
                        });
                      });
                    },
                    icon: Icon(
                      isLiked ? Icons.favorite : Icons.favorite_border,
                      color: isLiked ? Colors.red : Colors.grey,
                    ),
                    label: Text(
                      "إعجاب",
                      style: TextStyle(
                          color: isDarkMode
                              ? Colors.white70
                              : Colors.black87),
                    ),
                  ),
                  TextButton.icon(
                    onPressed: () {
                      showModalBottomSheet(
                        context: context,
                        isScrollControlled: true,
                        builder: (context) => _buildCommentSheet(
                            context, post['_id'], commentController),
                      );
                    },
                    icon: const Icon(Icons.comment, color: Colors.grey),
                    label: Text(
                      "تعليق",
                      style: TextStyle(
                          color: isDarkMode
                              ? Colors.white70
                              : Colors.black87),
                    ),
                  ),
                  TextButton.icon(
                    onPressed: () => _sharePost(post),
                    icon: const Icon(Icons.share, color: Colors.grey),
                    label: Text(
                      "مشاركة",
                      style: TextStyle(
                          color: isDarkMode
                              ? Colors.white70
                              : Colors.black87),
                    ),
                  ),
                ],
              ),
              if (post['comments'].isNotEmpty) ...[
                const SizedBox(height: 8),
                ExpansionTile(
                  title: Text(
                    "عرض التعليقات (${post['comments'].length})",
                    style: TextStyle(
                        color:
                        isDarkMode ? Colors.white70 : Colors.black87),
                  ),
                  children: post['comments']
                      .map<Widget>((comment) => FadeIn(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                          vertical: 4.0, horizontal: 16.0),
                      child: Row(
                        crossAxisAlignment:
                        CrossAxisAlignment.start,
                        children: [
                          const CircleAvatar(
                            radius: 12,
                            backgroundColor: Colors.green,
                            child: Text("U",
                                style: TextStyle(
                                    color: Colors.white)),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              comment,
                              style: TextStyle(
                                  color: isDarkMode
                                      ? Colors.white70
                                      : Colors.black87),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ))
                      .toList(),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCommentSheet(
      BuildContext context, String postId, TextEditingController controller) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return Padding(
      padding: EdgeInsets.only(
        bottom: MediaQuery.of(context).viewInsets.bottom,
        left: 16,
        right: 16,
        top: 16,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          TextField(
            controller: controller,
            decoration: InputDecoration(
              hintText: "اكتب تعليقًا...",
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              filled: true,
              fillColor: isDarkMode ? Colors.grey[800] : Colors.grey[100],
            ),
            style: TextStyle(
              color: isDarkMode ? Colors.white : Colors.black87,
            ),
            maxLines: 3,
            textInputAction: TextInputAction.send,
            onSubmitted: (_) async {
              if (controller.text.isNotEmpty) {
                final appState = Provider.of<AppState>(context, listen: false);
                await _natureService.commentPost(
                    appState.token ?? '', postId, controller.text);
                controller.clear();
                Navigator.pop(context);
                await _fetchData();
              }
            },
          ),
          const SizedBox(height: 8),
          ElevatedButton(
            onPressed: () async {
              if (controller.text.isNotEmpty) {
                final appState = Provider.of<AppState>(context, listen: false);
                await _natureService.commentPost(
                    appState.token ?? '', postId, controller.text);
                controller.clear();
                Navigator.pop(context);
                await _fetchData();
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10)),
              minimumSize: const Size(double.infinity, 50),
            ),
            child: const Text(
              "إرسال",
              style: TextStyle(color: Colors.white, fontSize: 16),
            ),
          ),
          const SizedBox(height: 16),
        ],
      ),
    );
  }

  void _showAddContentDialog(BuildContext context) {
    String title = '';
    File? file;
    String type = 'فيديو';
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
          backgroundColor: isDarkMode ? Colors.grey[900] : Colors.white,
          title: const Text(
            "إضافة محتوى جديد",
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  decoration: InputDecoration(
                    labelText: "العنوان",
                    border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12)),
                    filled: true,
                    fillColor: isDarkMode ? Colors.grey[800] : Colors.grey[100],
                  ),
                  style: TextStyle(
                    color: isDarkMode ? Colors.white : Colors.black87,
                  ),
                  onChanged: (value) => title = value,
                ),
                const SizedBox(height: 10),
                DropdownButtonFormField<String>(
                  value: type,
                  decoration: InputDecoration(
                    border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12)),
                    filled: true,
                    fillColor: isDarkMode ? Colors.grey[800] : Colors.grey[100],
                  ),
                  items: ['فيديو', 'صورة', 'PDF']
                      .map((value) => DropdownMenuItem(
                    value: value,
                    child: Text(
                      value,
                      style: TextStyle(
                          color: isDarkMode
                              ? Colors.white
                              : Colors.black87),
                    ),
                  ))
                      .toList(),
                  onChanged: (value) => setDialogState(() => type = value!),
                ),
                const SizedBox(height: 10),
                ElevatedButton(
                  onPressed: () async {
                    final result = await FilePicker.platform.pickFiles(
                      type: FileType.custom,
                      allowedExtensions: type == 'صورة'
                          ? ['jpg', 'png', 'jpeg']
                          : type == 'فيديو'
                          ? ['mp4', 'mov']
                          : ['pdf'],
                    );
                    if (result != null) {
                      file = File(result.files.single.path!);
                      setDialogState(() {});
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10)),
                    minimumSize: const Size(double.infinity, 50),
                  ),
                  child: Text(
                    file == null
                        ? "اختر ملف"
                        : file!.path.split('/').last,
                    style: const TextStyle(color: Colors.white),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(
                "إلغاء",
                style: TextStyle(
                  color: isDarkMode ? Colors.white70 : Colors.green,
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () async {
                if (title.isNotEmpty &&
                    file != null &&
                    await _validateFile(file!, type)) {
                  final appState =
                  Provider.of<AppState>(context, listen: false);
                  try {
                    await _natureService.addContribution(
                        appState.token ?? '',
                        widget.speciesTitle,
                        title,
                        file!,
                        type);
                    Navigator.pop(context);
                    await _fetchData();
                    _showSnackBar("تمت الإضافة بنجاح");
                  } catch (e) {
                    _showSnackBar("خطأ في الإضافة: $e", Colors.red);
                  }
                } else {
                  _showSnackBar(
                      "يرجى ملء الحقول أو اختيار ملف صالح", Colors.red);
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10)),
              ),
              child: const Text(
                "إضافة",
                style: TextStyle(color: Colors.white),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showChatOptions(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        backgroundColor: isDarkMode ? Colors.grey[900] : Colors.white,
        title: const Text(
          "إنشاء غرفة دردشة",
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.chat, color: Colors.green),
              title: Text(
                "دردشة كتابية",
                style: TextStyle(
                    color: isDarkMode ? Colors.white : Colors.black87),
              ),
              onTap: () {
                Navigator.pop(context);
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) =>
                        ChatRoomPage(speciesTitle: widget.speciesTitle),
                  ),
                );
              },
            ),
            ListTile(
              leading: const Icon(Icons.mic, color: Colors.green),
              title: Text(
                "دردشة صوتية",
                style: TextStyle(
                    color: isDarkMode ? Colors.white : Colors.black87),
              ),
              onTap: () {
                Navigator.pop(context);
                _showSnackBar("الدردشة الصوتية تحت التطوير", Colors.orange);
              },
            ),
            ListTile(
              leading: const Icon(Icons.videocam, color: Colors.green),
              title: Text(
                "دردشة فيديو",
                style: TextStyle(
                    color: isDarkMode ? Colors.white : Colors.black87),
              ),
              onTap: () {
                Navigator.pop(context);
                _showSnackBar("دردشة الفيديو تحت التطوير", Colors.orange);
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              "إلغاء",
              style: TextStyle(
                  color: isDarkMode ? Colors.white70 : Colors.green),
            ),
          ),
        ],
      ),
    );
  }

  void _attachFileDialog(BuildContext context) {
    String type = 'صورة';
    File? file;
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
          backgroundColor: isDarkMode ? Colors.grey[900] : Colors.white,
          title: const Text(
            "إرفاق ملف",
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              DropdownButtonFormField<String>(
                value: type,
                decoration: InputDecoration(
                  border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: isDarkMode ? Colors.grey[800] : Colors.grey[100],
                ),
                items: ['صورة', 'فيديو', 'PDF']
                    .map((value) => DropdownMenuItem(
                  value: value,
                  child: Text(
                    value,
                    style: TextStyle(
                        color: isDarkMode
                            ? Colors.white
                            : Colors.black87),
                  ),
                ))
                    .toList(),
                onChanged: (value) => setDialogState(() => type = value!),
              ),
              const SizedBox(height: 10),
              ElevatedButton(
                onPressed: () async {
                  final result = await FilePicker.platform.pickFiles(
                    type: FileType.custom,
                    allowedExtensions: type == 'صورة'
                        ? ['jpg', 'png', 'jpeg']
                        : type == 'فيديو'
                        ? ['mp4', 'mov']
                        : ['pdf'],
                  );
                  if (result != null) {
                    file = File(result.files.single.path!);
                    setDialogState(() {});
                  }
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10)),
                  minimumSize: const Size(double.infinity, 50),
                ),
                child: Text(
                  file == null ? "اختر ملف" : file!.path.split('/').last,
                  style: const TextStyle(color: Colors.white),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(
                "إلغاء",
                style: TextStyle(
                    color: isDarkMode ? Colors.white70 : Colors.green),
              ),
            ),
            ElevatedButton(
              onPressed: () async {
                if (file != null && await _validateFile(file!, type)) {
                  // Use a microtask to avoid triggering during a frame
                  Future.microtask(() {
                    setState(() {
                      _attachedFile = file;
                      _attachedFileType = type;
                    });
                  });
                  Navigator.pop(context);
                  _showSnackBar("تم إرفاق الملف");
                } else {
                  _showSnackBar("يرجى اختيار ملف صالح", Colors.red);
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10)),
              ),
              child: const Text(
                "إرفاق",
                style: TextStyle(color: Colors.white),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _sharePost(Map<String, dynamic> post) {
    Share.share(
      'منشور من ${post['memberName']} عن ${widget.speciesTitle}: ${post['content']}${post['fileUrl'] != null ? '\n${post['fileUrl']}' : ''}',
      subject: 'منشور عن ${widget.speciesTitle}',
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    _postController.dispose();
    _favoriteAnimationController.dispose();
    _isHandlingMouseEvent = false;
    super.dispose();
  }
}

class ChatRoomPage extends StatefulWidget {
  final String speciesTitle;

  const ChatRoomPage({super.key, required this.speciesTitle});

  @override
  _ChatRoomPageState createState() => _ChatRoomPageState();
}

class _ChatRoomPageState extends State<ChatRoomPage>
    with TickerProviderStateMixin {
  // Flag to prevent nested mouse event handling
  final bool _isHandlingMouseEvent = false;
  final TextEditingController _messageController = TextEditingController();
  final List<Map<String, dynamic>> _messages = [];
  io.Socket? _socket;
  bool _isTyping = false;
  late AnimationController _typingAnimationController;
  late Animation<double> _typingAnimation;

  @override
  void initState() {
    super.initState();
    _connectSocket();

    _typingAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1000),
    )..repeat(reverse: true);
    _typingAnimation = Tween<double>(begin: 0.8, end: 1.2).animate(
      CurvedAnimation(
        parent: _typingAnimationController,
        curve: Curves.easeInOut,
      ),
    );

    Timer.periodic(const Duration(seconds: 5), (timer) {
      if (mounted) {
        setState(() => _isTyping = !_isTyping);
      }
    });
  }

  void _connectSocket() {
    final appState = Provider.of<AppState>(context, listen: false);
    _socket = appState.socket;
    _socket?.emit('joinRoom', {'room': widget.speciesTitle});
    _socket?.on('message', (data) {
      // Use a microtask to avoid triggering during a frame
      Future.microtask(() {
        setState(() {
          _messages.add({
            'sender': data['sender'],
            'content': data['content'],
            'timestamp': data['timestamp'],
          });
        });
        WidgetsBinding.instance.addPostFrameCallback((_) {
          Scrollable.ensureVisible(
            context,
            alignment: 1.0,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
          );
        });
      });
    });
  }

  void _sendMessage() {
    if (_messageController.text.isNotEmpty) {
      final appState = Provider.of<AppState>(context, listen: false);
      _socket?.emit('sendMessage', {
        'room': widget.speciesTitle,
        'sender': appState.username ?? 'مستخدم',
        'content': _messageController.text,
        'timestamp': DateTime.now().toIso8601String(),
      });
      _messageController.clear();
    }
  }

  @override
  void dispose() {
    _messageController.dispose();
    _typingAnimationController.dispose();
    _socket?.emit('leaveRoom', {'room': widget.speciesTitle});
    _socket?.off('message');
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return Scaffold(
      appBar: AppBar(
        title: Text('دردشة ${widget.speciesTitle}'),
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: isDarkMode
                  ? [Colors.green[900]!, Colors.green[700]!]
                  : [Colors.green, Colors.greenAccent],
            ),
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.info_outline),
            onPressed: () => _showRoomInfo(),
          ),
        ],
      ),
      body: Column(
        children: [
          Expanded(
            child: ListView.builder(
              itemCount: _messages.length + (_isTyping ? 1 : 0),
              itemBuilder: (context, index) {
                if (_isTyping && index == _messages.length) {
                  return FadeInLeft(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                          vertical: 8, horizontal: 16),
                      child: Row(
                        children: [
                          const CircleAvatar(
                            radius: 12,
                            backgroundColor: Colors.green,
                            child: Text(
                              "U",
                              style: TextStyle(color: Colors.white),
                            ),
                          ),
                          const SizedBox(width: 8),
                          ScaleTransition(
                            scale: _typingAnimation,
                            child: Text(
                              "يكتب...",
                              style: TextStyle(
                                  color: isDarkMode
                                      ? Colors.grey[400]
                                      : Colors.grey[600]),
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                }
                final message = _messages[index];
                final isMe = message['sender'] ==
                    (Provider.of<AppState>(context, listen: false).username ??
                        'مستخدم');
                return FadeIn(
                  child: Align(
                    alignment:
                    isMe ? Alignment.centerRight : Alignment.centerLeft,
                    child: Container(
                      margin: const EdgeInsets.symmetric(
                          vertical: 4, horizontal: 16),
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: isMe
                            ? Colors.green[100]
                            : (isDarkMode
                            ? Colors.grey[800]
                            : Colors.grey[200]),
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.1),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            message['sender'] ?? 'مجهول',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color:
                              isDarkMode ? Colors.white : Colors.black87,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            message['content'] ?? '',
                            style: TextStyle(
                              color:
                              isDarkMode ? Colors.white70 : Colors.black87,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            timeago.format(
                              DateTime.parse(message['timestamp'] ??
                                  DateTime.now().toString()),
                              locale: 'ar',
                            ),
                            style: TextStyle(
                              fontSize: 10,
                              color: isDarkMode ? Colors.grey[400] : Colors.grey,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _messageController,
                    decoration: InputDecoration(
                      hintText: "اكتب رسالتك...",
                      border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(20)),
                      filled: true,
                      fillColor:
                      isDarkMode ? Colors.grey[800] : Colors.grey[100],
                    ),
                    style: TextStyle(
                      color: isDarkMode ? Colors.white : Colors.black87,
                    ),
                    textInputAction: TextInputAction.send,
                    onSubmitted: (_) => _sendMessage(),
                  ),
                ),
                const SizedBox(width: 8),
                FloatingActionButton(
                  onPressed: _sendMessage,
                  backgroundColor: Colors.green,
                  mini: true,
                  child: const Icon(Icons.send, color: Colors.white),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _showRoomInfo() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        backgroundColor: isDarkMode ? Colors.grey[900] : Colors.white,
        title: Text(
          widget.speciesTitle,
          style: TextStyle(
              color: isDarkMode ? Colors.white : Colors.black87,
              fontWeight: FontWeight.bold),
        ),
        content: Text(
          'معلومات الغرفة: هذه ميزة قيد التطوير',
          style: TextStyle(
              color: isDarkMode ? Colors.white70 : Colors.black87),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'إغلاق',
              style: TextStyle(
                  color: isDarkMode ? Colors.white70 : Colors.green),
            ),
          ),
        ],
      ),
    );
  }
}