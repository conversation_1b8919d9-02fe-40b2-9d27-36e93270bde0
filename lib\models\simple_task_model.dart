import 'package:uuid/uuid.dart';

class SimpleTask {
  String id;
  String title;
  bool isDone;
  DateTime? dueDate;
  bool isCollaborative; // أضيفت لعلاج خطأ isCollaborative
  List<String> collaborators; // أضيفت لعلاج خطأ collaborators

  SimpleTask({
    required this.id,
    required this.title,
    this.isDone = false,
    this.dueDate,
    this.isCollaborative = false, // قيمة افتراضية
    this.collaborators = const [], // قيمة افتراضية
  });

  factory SimpleTask.fromJson(Map<String, dynamic> json) {
    return SimpleTask(
      id: json['id'] ?? const Uuid().v4(),
      title: json['title'] ?? '',
      isDone: json['isDone'] ?? false,
      dueDate: json['dueDate'] != null ? DateTime.parse(json['dueDate']) : null,
      isCollaborative: json['isCollaborative'] ?? false,
      collaborators: List<String>.from(json['collaborators'] ?? []),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'isDone': isDone,
      'dueDate': dueDate?.toIso8601String(),
      'isCollaborative': isCollaborative,
      'collaborators': collaborators,
    };
  }

  SimpleTask copyWith({
    String? id,
    String? title,
    bool? isDone,
    DateTime? dueDate,
    bool? isCollaborative,
    List<String>? collaborators,
  }) {
    return SimpleTask(
      id: id ?? this.id,
      title: title ?? this.title,
      isDone: isDone ?? this.isDone,
      dueDate: dueDate ?? this.dueDate,
      isCollaborative: isCollaborative ?? this.isCollaborative,
      collaborators: collaborators ?? this.collaborators,
    );
  }
}