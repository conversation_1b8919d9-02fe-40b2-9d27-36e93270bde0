import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:socket_io_client/socket_io_client.dart' as io;
import '../appstate.dart';
import '../models/reel_models.dart';

// خدمة API للفيديوهات التعليمية (Reels)
class ReelService {
  static String get baseUrl => AppState.getBackendUrl();
  
  // الحصول على الفيديوهات التعليمية
  static Future<List<ReelVideo>> getReels({
    int page = 1,
    int limit = 10,
    String category = 'all',
    String difficulty = 'all',
    String sortBy = 'latest',
    String? userId,
  }) async {
    try {
      final queryParams = <String, String>{
        'page': page.toString(),
        'limit': limit.toString(),
        'category': category,
        'difficulty': difficulty,
        'sortBy': sortBy,
      };
      
      if (userId != null) queryParams['userId'] = userId;
      
      final uri = Uri.parse('$baseUrl/api/reels').replace(queryParameters: queryParams);
      
      final response = await http.get(
        uri,
        headers: _getHeaders(),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final reels = (data['reels'] as List)
            .map((reel) => ReelVideo.fromJson(reel))
            .toList();
        return reels;
      } else {
        throw Exception('فشل في جلب الفيديوهات: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }

  // رفع فيديو تعليمي جديد
  static Future<ReelVideo> uploadReel({
    required String title,
    required String description,
    required String category,
    required File videoFile,
    File? thumbnailFile,
    String? educationalContent,
    List<String>? tags,
    List<String>? learningObjectives,
    String difficulty = 'beginner',
    String privacy = 'public',
    bool allowComments = true,
    bool allowDownload = false,
    Map<String, dynamic>? quiz,
  }) async {
    try {
      final request = http.MultipartRequest(
        'POST',
        Uri.parse('$baseUrl/api/reels'),
      );
      
      request.headers.addAll(_getHeaders());
      request.fields['title'] = title;
      request.fields['description'] = description;
      request.fields['category'] = category;
      request.fields['difficulty'] = difficulty;
      request.fields['privacy'] = privacy;
      request.fields['allowComments'] = allowComments.toString();
      request.fields['allowDownload'] = allowDownload.toString();
      
      if (educationalContent != null) {
        request.fields['educationalContent'] = educationalContent;
      }
      
      if (tags != null) {
        request.fields['tags'] = json.encode(tags);
      }
      
      if (learningObjectives != null) {
        request.fields['learningObjectives'] = json.encode(learningObjectives);
      }
      
      if (quiz != null) {
        request.fields['quiz'] = json.encode(quiz);
      }
      
      // إضافة ملف الفيديو
      request.files.add(await http.MultipartFile.fromPath(
        'video',
        videoFile.path,
      ));
      
      // إضافة الصورة المصغرة إن وجدت
      if (thumbnailFile != null) {
        request.files.add(await http.MultipartFile.fromPath(
          'thumbnail',
          thumbnailFile.path,
        ));
      }

      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);

      if (response.statusCode == 201) {
        final data = json.decode(response.body);
        return ReelVideo.fromJson(data['reel']);
      } else {
        throw Exception('فشل في رفع الفيديو: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('خطأ في رفع الفيديو: $e');
    }
  }

  // التفاعل مع فيديو (إعجاب، عدم إعجاب)
  static Future<void> reactToReel(String reelId, String reactionType) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/api/reels/$reelId/react'),
        headers: _getHeaders(),
        body: json.encode({'reactionType': reactionType}),
      );

      if (response.statusCode != 200) {
        throw Exception('فشل في التفاعل مع الفيديو: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('خطأ في التفاعل: $e');
    }
  }

  // إضافة تعليق
  static Future<ReelComment> addComment(String reelId, String content, {String? parentId}) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/api/reels/$reelId/comments'),
        headers: _getHeaders(),
        body: json.encode({
          'content': content,
          if (parentId != null) 'parentId': parentId,
        }),
      );

      if (response.statusCode == 201) {
        final data = json.decode(response.body);
        return ReelComment.fromJson(data['comment']);
      } else {
        throw Exception('فشل في إضافة التعليق: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('خطأ في إضافة التعليق: $e');
    }
  }

  // الحصول على تعليقات فيديو
  static Future<List<ReelComment>> getComments(String reelId, {int page = 1, int limit = 20}) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/api/reels/$reelId/comments?page=$page&limit=$limit'),
        headers: _getHeaders(),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return (data['comments'] as List)
            .map((comment) => ReelComment.fromJson(comment))
            .toList();
      } else {
        throw Exception('فشل في جلب التعليقات: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('خطأ في جلب التعليقات: $e');
    }
  }

  // متابعة/إلغاء متابعة مستخدم
  static Future<void> toggleFollow(String userId) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/api/users/$userId/follow'),
        headers: _getHeaders(),
      );

      if (response.statusCode != 200) {
        throw Exception('فشل في تحديث المتابعة: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('خطأ في المتابعة: $e');
    }
  }

  // حفظ/إلغاء حفظ فيديو
  static Future<void> toggleSave(String reelId) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/api/reels/$reelId/save'),
        headers: _getHeaders(),
      );

      if (response.statusCode != 200) {
        throw Exception('فشل في حفظ الفيديو: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('خطأ في حفظ الفيديو: $e');
    }
  }

  // تسجيل مشاهدة فيديو
  static Future<void> recordView(String reelId, {int watchTime = 0}) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/api/reels/$reelId/view'),
        headers: _getHeaders(),
        body: json.encode({'watchTime': watchTime}),
      );

      if (response.statusCode != 200) {
        throw Exception('فشل في تسجيل المشاهدة: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('خطأ في تسجيل المشاهدة: $e');
    }
  }

  // إرسال إجابة الكويز
  static Future<Map<String, dynamic>> submitQuizAnswer(String reelId, String answer) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/api/reels/$reelId/quiz'),
        headers: _getHeaders(),
        body: json.encode({'answer': answer}),
      );

      if (response.statusCode == 200) {
        return json.decode(response.body);
      } else {
        throw Exception('فشل في إرسال الإجابة: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('خطأ في إرسال الإجابة: $e');
    }
  }

  // البحث في الفيديوهات
  static Future<List<ReelVideo>> searchReels(String query, {
    String category = 'all',
    String difficulty = 'all',
    int page = 1,
    int limit = 10,
  }) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/api/reels/search?query=${Uri.encodeComponent(query)}&category=$category&difficulty=$difficulty&page=$page&limit=$limit'),
        headers: _getHeaders(),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return (data['reels'] as List)
            .map((reel) => ReelVideo.fromJson(reel))
            .toList();
      } else {
        throw Exception('فشل في البحث: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('خطأ في البحث: $e');
    }
  }

  // الحصول على الفيديوهات المحفوظة
  static Future<List<ReelVideo>> getSavedReels({int page = 1, int limit = 10}) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/api/reels/saved?page=$page&limit=$limit'),
        headers: _getHeaders(),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return (data['reels'] as List)
            .map((reel) => ReelVideo.fromJson(reel))
            .toList();
      } else {
        throw Exception('فشل في جلب الفيديوهات المحفوظة: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('خطأ في جلب الفيديوهات المحفوظة: $e');
    }
  }

  // الحصول على فيديوهات مستخدم معين
  static Future<List<ReelVideo>> getUserReels(String userId, {int page = 1, int limit = 10}) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/api/users/$userId/reels?page=$page&limit=$limit'),
        headers: _getHeaders(),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return (data['reels'] as List)
            .map((reel) => ReelVideo.fromJson(reel))
            .toList();
      } else {
        throw Exception('فشل في جلب فيديوهات المستخدم: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('خطأ في جلب فيديوهات المستخدم: $e');
    }
  }

  // حذف فيديو
  static Future<void> deleteReel(String reelId) async {
    try {
      final response = await http.delete(
        Uri.parse('$baseUrl/api/reels/$reelId'),
        headers: _getHeaders(),
      );

      if (response.statusCode != 200) {
        throw Exception('فشل في حذف الفيديو: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('خطأ في حذف الفيديو: $e');
    }
  }

  // الحصول على إحصائيات فيديو
  static Future<Map<String, dynamic>> getReelAnalytics(String reelId) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/api/reels/$reelId/analytics'),
        headers: _getHeaders(),
      );

      if (response.statusCode == 200) {
        return json.decode(response.body);
      } else {
        throw Exception('فشل في جلب الإحصائيات: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('خطأ في جلب الإحصائيات: $e');
    }
  }

  // الحصول على headers المطلوبة
  static Map<String, String> _getHeaders() {
    String? token;
    try {
      token = null; // سيتم تحديثه من AppState
    } catch (e) {
      print('خطأ في الحصول على التوكن: $e');
    }
    
    return {
      'Content-Type': 'application/json',
      if (token != null) 'Authorization': 'Bearer $token',
    };
  }
}
