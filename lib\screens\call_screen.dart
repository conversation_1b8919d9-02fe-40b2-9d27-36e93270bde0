import 'package:flutter/material.dart';
import 'package:agora_rtc_engine/agora_rtc_engine.dart';
import '../services/call_service.dart';

class CallScreen extends StatefulWidget {
  final String channelName;
  final String userName;
  final bool isVideo;
  final List<String> participants;

  const CallScreen({
    super.key,
    required this.channelName,
    required this.userName,
    required this.isVideo,
    required this.participants,
  });

  @override
  State<CallScreen> createState() => _CallScreenState();
}

class _CallScreenState extends State<CallScreen> {
  final CallService _callService = CallService();
  final List<String> _users = [];
  bool _localUserJoined = false;
  bool _muted = false;
  bool _videoEnabled = true;
  bool _speakerOn = true;

  @override
  void initState() {
    super.initState();
    _initializeCall();
  }

  Future<void> _initializeCall() async {
    await _callService.initialize();
    await _callService.startCall(
      channelName: widget.channelName,
      isVideo: widget.isVideo,
      onUserJoined: (userId) {
        setState(() {
          _users.add(userId);
        });
      },
      onUserLeft: (userId) {
        setState(() {
          _users.remove(userId);
        });
      },
      onLocalUserJoined: () {
        setState(() {
          _localUserJoined = true;
        });
      },
      onError: (error) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(error, style: const TextStyle(fontFamily: 'Tajawal'))),
        );
        Navigator.pop(context);
      },
    );
  }

  // بناء واجهة المستخدم لمكالمة الفيديو
  Widget _buildVideoView() {
    return Stack(
      children: [
        // عرض الفيديو المحلي
        if (_localUserJoined)
          AgoraVideoView(
            controller: VideoViewController(
              rtcEngine: _callService.engine!,
              canvas: const VideoCanvas(uid: 0),
            ),
          ),
        
        // عرض الفيديو البعيد
        if (_users.isNotEmpty)
          Positioned(
            top: 10,
            right: 10,
            width: 120,
            height: 160,
            child: Container(
              decoration: BoxDecoration(
                border: Border.all(color: Colors.white, width: 2),
                borderRadius: BorderRadius.circular(10),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: AgoraVideoView(
                  controller: VideoViewController.remote(
                    rtcEngine: _callService.engine!,
                    canvas: VideoCanvas(uid: int.parse(_users.first)),
                    connection: const RtcConnection(channelId: ''),
                  ),
                ),
              ),
            ),
          ),
      ],
    );
  }

  // بناء واجهة المستخدم لمكالمة الصوت
  Widget _buildAudioView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.phone, size: 80, color: Colors.green),
          const SizedBox(height: 20),
          Text(
            'مكالمة صوتية مع ${widget.userName}',
            style: const TextStyle(
              fontSize: 24,
              fontFamily: 'Tajawal',
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 20),
          Text(
            _localUserJoined ? 'متصل' : 'جاري الاتصال...',
            style: TextStyle(
              fontSize: 18,
              fontFamily: 'Tajawal',
              color: _localUserJoined ? Colors.green : Colors.orange,
            ),
          ),
          const SizedBox(height: 40),
          if (_users.isNotEmpty)
            Text(
              'عدد المشاركين: ${_users.length + 1}',
              style: const TextStyle(
                fontSize: 16,
                fontFamily: 'Tajawal',
                color: Colors.white70,
              ),
            ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        title: Text(
          widget.isVideo ? 'مكالمة فيديو' : 'مكالمة صوتية',
          style: const TextStyle(fontFamily: 'Tajawal'),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: widget.isVideo ? _buildVideoView() : _buildAudioView(),
      bottomNavigationBar: Container(
        padding: const EdgeInsets.symmetric(vertical: 16),
        color: Colors.black,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            // زر كتم الصوت
            CircleAvatar(
              radius: 25,
              backgroundColor: _muted ? Colors.red : Colors.white24,
              child: IconButton(
                icon: Icon(_muted ? Icons.mic_off : Icons.mic, color: Colors.white),
                onPressed: () {
                  setState(() {
                    _muted = !_muted;
                    _callService.toggleMute(_muted);
                  });
                },
              ),
            ),
            
            // زر إنهاء المكالمة
            CircleAvatar(
              radius: 30,
              backgroundColor: Colors.red,
              child: IconButton(
                icon: const Icon(Icons.call_end, color: Colors.white, size: 30),
                onPressed: () {
                  _callService.endCall();
                  Navigator.pop(context);
                },
              ),
            ),
            
            // زر تبديل الكاميرا (للفيديو فقط) أو مكبر الصوت (للصوت فقط)
            CircleAvatar(
              radius: 25,
              backgroundColor: Colors.white24,
              child: IconButton(
                icon: Icon(
                  widget.isVideo
                      ? Icons.flip_camera_ios
                      : (_speakerOn ? Icons.volume_up : Icons.volume_off),
                  color: Colors.white,
                ),
                onPressed: () {
                  if (widget.isVideo) {
                    _callService.switchCamera();
                  } else {
                    setState(() {
                      _speakerOn = !_speakerOn;
                      // تنفيذ تبديل السماعة الخارجية
                      _callService.engine?.setEnableSpeakerphone(_speakerOn);
                    });
                  }
                },
              ),
            ),
            
            // زر تبديل الفيديو (للفيديو فقط)
            if (widget.isVideo)
              CircleAvatar(
                radius: 25,
                backgroundColor: _videoEnabled ? Colors.white24 : Colors.red,
                child: IconButton(
                  icon: Icon(
                    _videoEnabled ? Icons.videocam : Icons.videocam_off,
                    color: Colors.white,
                  ),
                  onPressed: () {
                    setState(() {
                      _videoEnabled = !_videoEnabled;
                      _callService.toggleVideo(_videoEnabled);
                    });
                  },
                ),
              ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _callService.dispose();
    super.dispose();
  }
}