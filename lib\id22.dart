import 'dart:async';
import 'dart:convert';
import 'dart:io';

// Flutter packages
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';

// State management
import 'package:provider/provider.dart';

// UI components
import 'package:cached_network_image/cached_network_image.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:shimmer/shimmer.dart';
import 'package:confetti/confetti.dart';
import 'package:animate_do/animate_do.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

// Network & storage
import 'package:http/http.dart' as http;
import 'package:dio/dio.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:retry/retry.dart';

// Location & maps
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:geolocator/geolocator.dart';

// File handling
import 'package:file_picker/file_picker.dart';

// Localization
import 'package:intl/intl.dart';
import 'package:easy_localization/easy_localization.dart';

// Utilities
import 'package:uuid/uuid.dart';
import 'package:socket_io_client/socket_io_client.dart' as io;
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter_offline/flutter_offline.dart';
import 'package:share_plus/share_plus.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:package_info_plus/package_info_plus.dart';

// App modules
import 'appstate.dart';
import 'share_borrow_features_screen.dart';
import 'maps_system.dart';
import 'rating_system.dart';

// ===============================
// Constants and Configuration
// ===============================

/// Animation constants for consistent UI behavior
const Duration _animationDuration = Duration(milliseconds: 300);
const Duration _fastAnimationDuration = Duration(milliseconds: 150);
const Duration _slowAnimationDuration = Duration(milliseconds: 500);
const double _scaleFactor = 0.95;
const double _borderRadius = 12.0;
const double _cardElevation = 4.0;

/// Error message constants for better user experience
const String networkError = 'خطأ في الشبكة، يرجى التحقق من الاتصال';
const String loadingError = 'خطأ في تحميل البيانات';
const String authError = 'فشل في المصادقة، يرجى تسجيل الدخول مرة أخرى';
const String serverError = 'خطأ في الخادم، يرجى المحاولة لاحقاً';
const String offlineMessage = 'أنت غير متصل بالإنترنت';

/// Retry configuration for network requests
final RetryOptions _retryOptions = RetryOptions(
  maxAttempts: 3,
  delayFactor: Duration(seconds: 2),
);

/// Theme colors for consistent design
class AppColors {
  static const Color primary = Color(0xFF00695C);
  static const Color primaryLight = Color(0xFF4DB6AC);
  static const Color primaryDark = Color(0xFF004D40);
  static const Color accent = Color(0xFF26A69A);
  static const Color success = Color(0xFF4CAF50);
  static const Color warning = Color(0xFFFF9800);
  static const Color error = Color(0xFFF44336);
  static const Color background = Color(0xFFF5F5F5);
  static const Color surface = Colors.white;
  static const Color onSurface = Color(0xFF212121);
  static const Color onSurfaceVariant = Color(0xFF757575);
}

// ===============================
// Enhanced Error Handling
// ===============================

/// Enhanced error handler with better user feedback
Future<T> handleError<T>(Future<T> Function() future, {String? customMessage}) async {
  try {
    return await future();
  } catch (e) {
    String errorMessage = customMessage ?? 'حدث خطأ غير متوقع';

    if (e is SocketException) {
      errorMessage = 'خطأ في الاتصال بالشبكة';
    } else if (e is TimeoutException) {
      errorMessage = 'انتهت مهلة الانتظار';
    } else if (e is DioError) {
      switch (e.response?.statusCode) {
        case 401:
          errorMessage = 'غير مصرح';
          break;
        case 404:
          errorMessage = 'البيانات المطلوبة غير موجودة';
          break;
        case 500:
          errorMessage = 'خطأ في الخادم';
          break;
        default:
          errorMessage = 'خطأ في الخادم: ${e.response?.statusCode}';
      }
    }

    throw errorMessage;
  }
}

// ===============================
// Enhanced Data Models
// ===============================

/// Enhanced ShareItem model for sharing and borrowing
class ShareItem {
  static const String defaultImageUrl = 'https://via.placeholder.com/400x300?text=No+Image';
  static const List<String> defaultCategories = [
    'أدوات',
    'كتب',
    'إلكترونيات',
    'ملابس',
    'أثاث',
    'ألعاب',
    'معدات رياضية',
    'أخرى'
  ];

  static const List<String> defaultTags = [
    'متوفر',
    'عاجل',
    'جديد',
    'مستعمل',
    'مجاني',
    'مؤقت',
    'دائم'
  ];

  final String id;
  final String title;
  final String description;
  final String location;
  final double? latitude;
  final double? longitude;
  final double? rating;
  final int? ratingCount;
  final String category;
  final List<String> imageUrls;
  final String? videoUrl;
  final bool isAvailable;
  final String creatorId;
  final String shareStatus;
  final int borrowDurationDays;
  final DateTime date;
  final DateTime requestDate;
  final DateTime? endDate;
  final bool isFeatured;
  bool isFavorite;
  final List<ImpactMedia> impactMedia;
  final int borrowerCount;
  final double urgencyLevel;
  final List<String> tags;
  final Map<String, dynamic> metadata;

  static String generateId() {
    return const Uuid().v4();
  }

  factory ShareItem.createNew({
    required String title,
    required String description,
    required String location,
    required String category,
    required String creatorId,
    List<String>? imageUrls,
    String? videoUrl,
    double? latitude,
    double? longitude,
    double? rating,
    int? ratingCount,
    DateTime? endDate,
    List<String>? tags,
    int borrowDurationDays = 7,
  }) {
    final now = DateTime.now();
    return ShareItem(
      id: generateId(),
      title: title,
      description: description,
      location: location,
      latitude: latitude,
      longitude: longitude,
      rating: rating,
      ratingCount: ratingCount ?? 0,
      category: category,
      imageUrls: imageUrls ?? [],
      videoUrl: videoUrl,
      isAvailable: true,
      creatorId: creatorId,
      shareStatus: 'available',
      borrowDurationDays: borrowDurationDays,
      date: now,
      requestDate: now,
      endDate: endDate,
      isFeatured: false,
      isFavorite: false,
      impactMedia: [],
      borrowerCount: 0,
      urgencyLevel: 0.0,
      tags: tags ?? [],
      metadata: {},
    );
  }

  ShareItem({
    required this.id,
    required this.title,
    required this.description,
    required this.location,
    this.latitude,
    this.longitude,
    this.rating,
    this.ratingCount = 0,
    required this.category,
    this.imageUrls = const [],
    this.videoUrl,
    required this.isAvailable,
    required this.creatorId,
    required this.shareStatus,
    required this.borrowDurationDays,
    required this.date,
    required this.requestDate,
    this.endDate,
    required this.isFeatured,
    this.isFavorite = false,
    this.impactMedia = const [],
    this.borrowerCount = 0,
    this.urgencyLevel = 0.0,
    this.tags = const [],
    this.metadata = const {},
  });

  bool get isUrgent => urgencyLevel > 0.7;

  ShareItem copyWith({
    String? id,
    String? title,
    String? description,
    String? location,
    double? latitude,
    double? longitude,
    double? rating,
    int? ratingCount,
    String? category,
    List<String>? imageUrls,
    String? videoUrl,
    bool? isAvailable,
    String? creatorId,
    String? shareStatus,
    int? borrowDurationDays,
    DateTime? date,
    DateTime? requestDate,
    DateTime? endDate,
    bool? isFeatured,
    bool? isFavorite,
    List<ImpactMedia>? impactMedia,
    int? borrowerCount,
    double? urgencyLevel,
    List<String>? tags,
    Map<String, dynamic>? metadata,
  }) {
    return ShareItem(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      location: location ?? this.location,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      rating: rating ?? this.rating,
      ratingCount: ratingCount ?? this.ratingCount,
      category: category ?? this.category,
      imageUrls: imageUrls ?? this.imageUrls,
      videoUrl: videoUrl ?? this.videoUrl,
      isAvailable: isAvailable ?? this.isAvailable,
      creatorId: creatorId ?? this.creatorId,
      shareStatus: shareStatus ?? this.shareStatus,
      borrowDurationDays: borrowDurationDays ?? this.borrowDurationDays,
      date: date ?? this.date,
      requestDate: requestDate ?? this.requestDate,
      endDate: endDate ?? this.endDate,
      isFeatured: isFeatured ?? this.isFeatured,
      isFavorite: isFavorite ?? this.isFavorite,
      impactMedia: impactMedia ?? this.impactMedia,
      borrowerCount: borrowerCount ?? this.borrowerCount,
      urgencyLevel: urgencyLevel ?? this.urgencyLevel,
      tags: tags ?? this.tags,
      metadata: metadata ?? this.metadata,
    );
  }

  bool get isValid {
    return title.isNotEmpty &&
        description.length >= 30 &&
        location.isNotEmpty &&
        (latitude != null && longitude != null) &&
        category.isNotEmpty &&
        (imageUrls.isNotEmpty || videoUrl != null);
  }

  int? get daysRemaining {
    if (endDate == null) return null;
    final now = DateTime.now();
    return endDate!.difference(now).inDays;
  }

  String get firstImageUrl => imageUrls.isNotEmpty
      ? imageUrls.first
      : videoUrl ?? defaultImageUrl;

  bool get isExpired => endDate != null && endDate!.isBefore(DateTime.now());

  String get statusText {
    if (!isAvailable) return 'غير متوفر';
    if (isExpired) return 'منتهي';
    return 'متوفر';
  }

  factory ShareItem.fromJson(Map<String, dynamic> json) {
    return ShareItem(
      id: json['_id'] ?? const Uuid().v4(),
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      location: json['location'] ?? '',
      latitude: (json['latitude'] as num?)?.toDouble(),
      longitude: (json['longitude'] as num?)?.toDouble(),
      rating: (json['rating'] as num?)?.toDouble(),
      ratingCount: json['ratingCount'] as int?,
      category: json['category'] ?? 'أخرى',
      imageUrls: List<String>.from(json['imageUrls'] ?? []),
      videoUrl: json['videoUrl'],
      isAvailable: json['isAvailable'] ?? true,
      creatorId: json['creatorId'] ?? '',
      shareStatus: json['shareStatus'] ?? 'available',
      borrowDurationDays: json['borrowDurationDays'] ?? 7,
      date: json['date'] != null
          ? DateTime.tryParse(json['date']) ?? DateTime.now()
          : DateTime.now(),
      requestDate: json['requestDate'] != null
          ? DateTime.tryParse(json['requestDate']) ?? DateTime.now()
          : DateTime.now(),
      endDate: json['endDate'] != null
          ? DateTime.tryParse(json['endDate'])
          : null,
      isFeatured: json['isFeatured'] ?? false,
      isFavorite: json['isFavorite'] ?? false,
      impactMedia: (json['impactMedia'] as List? ?? [])
          .map((item) => ImpactMedia.fromJson(item))
          .toList(),
      borrowerCount: json['borrowerCount'] ?? 0,
      urgencyLevel: (json['urgencyLevel'] as num?)?.toDouble() ?? 0.0,
      tags: List<String>.from(json['tags'] ?? []),
      metadata: Map<String, dynamic>.from(json['metadata'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'title': title,
      'description': description,
      'location': location,
      'latitude': latitude,
      'longitude': longitude,
      'rating': rating,
      'ratingCount': ratingCount,
      'category': category,
      'imageUrls': imageUrls,
      'videoUrl': videoUrl,
      'isAvailable': isAvailable,
      'creatorId': creatorId,
      'shareStatus': shareStatus,
      'borrowDurationDays': borrowDurationDays,
      'date': date.toIso8601String(),
      'requestDate': requestDate.toIso8601String(),
      'endDate': endDate?.toIso8601String(),
      'isFeatured': isFeatured,
      'isFavorite': isFavorite,
      'impactMedia': impactMedia.map((item) => item.toJson()).toList(),
      'borrowerCount': borrowerCount,
      'urgencyLevel': urgencyLevel,
      'tags': tags,
      'metadata': metadata,
    };
  }
}

/// Enhanced ImpactMedia model for sharing impact tracking
class ImpactMedia {
  final String id;
  final String url;
  final String type;
  final DateTime uploadedAt;
  final String description;
  final String? thumbnailUrl;
  final Map<String, dynamic> metadata;

  ImpactMedia({
    required this.id,
    required this.url,
    required this.type,
    required this.uploadedAt,
    required this.description,
    this.thumbnailUrl,
    this.metadata = const {},
  });

  factory ImpactMedia.fromJson(Map<String, dynamic> json) {
    return ImpactMedia(
      id: json['_id'] ?? '',
      url: json['url'] ?? '',
      type: json['type'] ?? 'image',
      uploadedAt: DateTime.tryParse(json['uploadedAt'] ?? '') ?? DateTime.now(),
      description: json['description'] ?? '',
      thumbnailUrl: json['thumbnailUrl'],
      metadata: Map<String, dynamic>.from(json['metadata'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'url': url,
      'type': type,
      'uploadedAt': uploadedAt.toIso8601String(),
      'description': description,
      'thumbnailUrl': thumbnailUrl,
      'metadata': metadata,
    };
  }
}

/// Enhanced User model for sharing/borrowing
class User {
  final String id;
  final String name;
  final String? avatarUrl;
  final int itemsShared;
  final int itemsBorrowed;
  final DateTime joinDate;
  final DateTime? lastActivityDate;
  bool showHistory;
  final List<String> badges;
  final Map<String, dynamic> preferences;

  User({
    required this.id,
    required this.name,
    this.avatarUrl,
    required this.itemsShared,
    required this.itemsBorrowed,
    required this.joinDate,
    this.lastActivityDate,
    required this.showHistory,
    this.badges = const [],
    this.preferences = const {},
  });

  String get userLevel {
    if (itemsShared >= 50) return 'مشارك ذهبي';
    if (itemsShared >= 20) return 'مشارك فضي';
    if (itemsShared >= 5) return 'مشارك برونزي';
    return 'مبتدئ';
  }

  Color get userLevelColor {
    switch (userLevel) {
      case 'مشارك ذهبي':
        return Colors.amber;
      case 'مشارك فضي':
        return Colors.grey[400]!;
      case 'مشارك برونزي':
        return Colors.brown;
      default:
        return Colors.blue;
    }
  }

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['_id'] ?? '',
      name: json['name'] ?? 'مستخدم مجهول',
      avatarUrl: json['avatarUrl'],
      itemsShared: json['itemsShared'] ?? 0,
      itemsBorrowed: json['itemsBorrowed'] ?? 0,
      joinDate: DateTime.tryParse(json['joinDate'] ?? '') ?? DateTime.now(),
      lastActivityDate: json['lastActivityDate'] != null
          ? DateTime.tryParse(json['lastActivityDate'])
          : null,
      showHistory: json['showHistory'] ?? false,
      badges: List<String>.from(json['badges'] ?? []),
      preferences: Map<String, dynamic>.from(json['preferences'] ?? {}),
    );
  }
}

/// Enhanced notification model
class AppNotification {
  final String id;
  final String title;
  final String message;
  final String type;
  final DateTime createdAt;
  final bool isRead;
  final Map<String, dynamic> data;
  final String? actionUrl;
  final String? imageUrl;

  AppNotification({
    required this.id,
    required this.title,
    required this.message,
    required this.type,
    required this.createdAt,
    this.isRead = false,
    this.data = const {},
    this.actionUrl,
    this.imageUrl,
  });

  IconData get icon {
    switch (type) {
      case 'share':
        return Icons.share;
      case 'borrow':
        return Icons.handshake;
      case 'urgent':
        return Icons.warning;
      case 'update':
        return Icons.info;
      default:
        return Icons.notifications;
    }
  }

  Color get color {
    switch (type) {
      case 'share':
        return AppColors.success;
      case 'borrow':
        return AppColors.primary;
      case 'urgent':
        return AppColors.warning;
      case 'update':
        return AppColors.accent;
      default:
        return AppColors.onSurfaceVariant;
    }
  }

  factory AppNotification.fromJson(Map<String, dynamic> json) {
    return AppNotification(
      id: json['_id'] ?? '',
      title: json['title'] ?? '',
      message: json['message'] ?? '',
      type: json['type'] ?? 'general',
      createdAt: DateTime.tryParse(json['createdAt'] ?? '') ?? DateTime.now(),
      isRead: json['isRead'] ?? false,
      data: Map<String, dynamic>.from(json['data'] ?? {}),
      actionUrl: json['actionUrl'],
      imageUrl: json['imageUrl'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'message': message,
      'type': type,
      'createdAt': createdAt.toIso8601String(),
      'isRead': isRead,
      'data': data,
      'actionUrl': actionUrl,
      'imageUrl': imageUrl,
    };
  }

  AppNotification copyWith({
    String? id,
    String? title,
    String? message,
    String? type,
    DateTime? createdAt,
    bool? isRead,
    Map<String, dynamic>? data,
    String? actionUrl,
    String? imageUrl,
  }) {
    return AppNotification(
      id: id ?? this.id,
      title: title ?? this.title,
      message: message ?? this.message,
      type: type ?? this.type,
      createdAt: createdAt ?? this.createdAt,
      isRead: isRead ?? this.isRead,
      data: data ?? this.data,
      actionUrl: actionUrl ?? this.actionUrl,
      imageUrl: imageUrl ?? this.imageUrl,
    );
  }
}

// ===============================
// Enhanced Service Layer
// ===============================

/// Borrowing model for tracking user borrowing activities
class Borrow {
  final String id;
  final String itemId;
  final String itemName;
  final DateTime borrowDate;
  final DateTime? returnDate;
  final String status; // 'مكتمل', 'معلق', 'ملغي'

  Borrow({
    required this.id,
    required this.itemId,
    required this.itemName,
    required this.borrowDate,
    this.returnDate,
    required this.status,
  });

  factory Borrow.fromJson(Map<String, dynamic> json) {
    return Borrow(
      id: json['id'] ?? '',
      itemId: json['itemId'] ?? '',
      itemName: json['itemName'] ?? 'عنصر مجهول',
      borrowDate: DateTime.parse(json['borrowDate'] ?? DateTime.now().toIso8601String()),
      returnDate: json['returnDate'] != null ? DateTime.parse(json['returnDate']) : null,
      status: json['status'] ?? 'معلق',
    );
  }
}

class ShareItemService {
  final String baseUrl;
  final String authToken;
  final Dio _dio;
  io.Socket? socket;

  final Map<String, List<ShareItem>> _itemsCache = {};
  final Map<String, DateTime> _cacheTimestamps = {};
  static const Duration _cacheExpiry = Duration(minutes: 5);

  ShareItemService({required this.baseUrl, required this.authToken})
      : _dio = Dio(BaseOptions(
    baseUrl: baseUrl,
    connectTimeout: const Duration(seconds: 10),
    receiveTimeout: const Duration(seconds: 10),
    headers: {'Authorization': 'Bearer $authToken'},
  )) {
    _setupInterceptors();
  }

  void _setupInterceptors() {
    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) {
        print('🚀 Request: ${options.method} ${options.path}');
        handler.next(options);
      },
      onResponse: (response, handler) {
        print('✅ Response: ${response.statusCode} ${response.requestOptions.path}');
        handler.next(response);
      },
      onError: (error, handler) {
        print('❌ Error: ${error.message}');
        handler.next(error);
      },
    ));
  }

  bool _isCacheValid(String key) {
    final timestamp = _cacheTimestamps[key];
    if (timestamp == null) return false;
    return DateTime.now().difference(timestamp) < _cacheExpiry;
  }

  Future<List<ShareItem>> fetchItems({
    String search = '',
    String filter = 'all',
    String sort = 'newest',
    int skip = 0,
    int limit = 10,
    bool forceRefresh = false,
  }) async {
    final cacheKey = 'items_${search}_${filter}_${sort}_${skip}_$limit';

    if (!forceRefresh &&
        _isCacheValid(cacheKey) &&
        _itemsCache.containsKey(cacheKey)) {
      return Future.value(_itemsCache[cacheKey]!);
    }

    return handleError(() => _dio.get('/share_items', queryParameters: {
      'search': search,
      'filter': filter,
      'sort': sort,
      'skip': skip,
      'limit': limit,
    }).then((response) {
      final items = (response.data as List)
          .map((item) => ShareItem.fromJson(item))
          .toList();

      _itemsCache[cacheKey] = items;
      _cacheTimestamps[cacheKey] = DateTime.now();

      return items;
    }),
        customMessage: 'فشل في جلب العناصر');
  }

  Future<List<Map<String, dynamic>>> fetchBorrowHistory(String itemId) {
    return handleError(
            () => _dio
            .get('/share_items/$itemId/borrows')
            .then((response) => List<Map<String, dynamic>>.from(response.data)),
        customMessage: 'فشل في جلب سجل الاستعارة');
  }

  Future<List<Map<String, dynamic>>> fetchSharedItems(String creatorId) {
    return handleError(
            () => _dio
            .get('/share_items/shared/$creatorId')
            .then((response) => List<Map<String, dynamic>>.from(response.data)),
        customMessage: 'فشل في جلب العناصر المشتركة');
  }

  Future<User?> fetchUser(String userId) {
    return handleError(
            () => _dio
            .get('/users/$userId')
            .then((response) => User.fromJson(response.data)),
        customMessage: 'فشل في جلب بيانات المستخدم');
  }

  Future<List<AppNotification>> fetchNotifications() async {
    return handleError(() async {
      final response = await _dio.get('/notifications');
      return (response.data as List)
          .map((item) => AppNotification.fromJson(item))
          .toList();
    }, customMessage: 'فشل في جلب الإشعارات');
  }

  Future<void> createItem({
    required String title,
    required String description,
    required String location,
    required String category,
    required File file,
    required String creatorId,
    List<String> tags = const [],
    double urgencyLevel = 0.0,
    int borrowDurationDays = 7,
  }) async {
    return handleError(() async {
      final formData = FormData.fromMap({
        'title': title,
        'description': description,
        'location': location,
        'category': category,
        'creatorId': creatorId,
        'requestDate': DateTime.now().toIso8601String(),
        'tags': tags.join(','),
        'urgencyLevel': urgencyLevel,
        'borrowDurationDays': borrowDurationDays,
        'file': await MultipartFile.fromFile(file.path),
      });

      await _dio.post('/share_items', data: formData);

      _itemsCache.clear();
      _cacheTimestamps.clear();
    }, customMessage: 'فشل في إنشاء العنصر');
  }

  Future<void> uploadImpactMedia({
    required String itemId,
    required File file,
    required String description,
    required String type,
  }) async {
    return handleError(
          () async {
        final formData = FormData.fromMap({
          'itemId': itemId,
          'description': description,
          'type': type,
          'file': await MultipartFile.fromFile(file.path),
        });

        await _dio.post('/share_items/impact-media', data: formData);
      },
      customMessage: 'فشل في رفع الوسائط',
    );
  }

  Future<void> deleteItem(String id) async {
    return handleError(
          () async {
        await _dio.delete('/share_items/$id');
        _itemsCache.clear();
        _cacheTimestamps.clear();
      },
      customMessage: 'فشل في حذف العنصر',
    );
  }

  Future<void> requestBorrow({
    required String itemId,
    required String borrowerId,
    String? message,
  }) async {
    return handleError(
          () async {
        await _dio.post('/share_items/borrows', data: {
          'itemId': itemId,
          'borrowerId': borrowerId,
          'message': message,
          'timestamp': DateTime.now().toIso8601String(),
        });

        _itemsCache.clear();
        _cacheTimestamps.clear();
      },
      customMessage: 'فشل في طلب الاستعارة',
    );
  }

  Future<void> updateItemStatus(String itemId, String status) async {
    return handleError(
          () async {
        await _dio.put('/share_items/$itemId/status', data: {'status': status});
        _itemsCache.clear();
        _cacheTimestamps.clear();
      },
      customMessage: 'فشل في تحديث حالة العنصر',
    );
  }

  Future<void> updateUserHistoryVisibility(String userId, bool showHistory) async {
    return handleError(
          () async {
        await _dio.put('/users/$userId/visibility', data: {'showHistory': showHistory});
      },
      customMessage: 'فشل في تحديث إعدادات الخصوصية',
    );
  }

  Future<void> markNotificationAsRead(String notificationId) async {
    return handleError(
          () async {
        await _dio.put('/notifications/$notificationId/read');
      },
      customMessage: 'فشل في تحديث حالة الإشعار',
    );
  }

  void setupSocketListeners(
      io.Socket socket, {
        Function(dynamic)? onNewItem,
        Function(dynamic)? onItemUpdate,
        Function(dynamic)? onNewBorrow,
        Function(dynamic)? onNewNotification,
      }) {
    this.socket = socket;

    socket.on('new_share_item', (data) {
      _itemsCache.clear();
      _cacheTimestamps.clear();
      onNewItem?.call(data);
    });

    socket.on('item_updated', (data) {
      _itemsCache.clear();
      _cacheTimestamps.clear();
      onItemUpdate?.call(data);
    });

    socket.on('new_borrow', (data) {
      _itemsCache.clear();
      _cacheTimestamps.clear();
      onNewBorrow?.call(data);
    });

    socket.on('new_notification', (data) {
      onNewNotification?.call(data);
    });

    socket.on('new_impact_media', (data) {});
  }

  void clearCache() {
    _itemsCache.clear();
    _cacheTimestamps.clear();
  }

  void dispose() {
    _dio.close();
    socket?.disconnect();
  }
}

// ===============================
// Enhanced Theme Provider
// ===============================

class ThemeProvider extends ChangeNotifier {
  bool _isDarkMode = false;
  late SharedPreferences _prefs;

  bool get isDarkMode => _isDarkMode;

  ThemeProvider() {
    _loadThemePreference();
  }

  Future<void> _loadThemePreference() async {
    _prefs = await SharedPreferences.getInstance();
    _isDarkMode = _prefs.getBool('isDarkMode') ?? false;
    notifyListeners();
  }

  Future<void>_toggleTheme() async {
    _isDarkMode = !_isDarkMode;
    await _prefs.setBool('isDarkMode', _isDarkMode);
    notifyListeners();
  }

  ThemeData get themeData {
    return _isDarkMode ? _darkTheme : _lightTheme;
  }

  static final ThemeData _lightTheme = ThemeData(
    useMaterial3: true,
    brightness: Brightness.light,
    colorScheme: ColorScheme.fromSeed(
      seedColor: AppColors.primary,
      brightness: Brightness.light,
    ),
    appBarTheme: AppBarTheme(
      backgroundColor: AppColors.primary,
      foregroundColor: Colors.white,
      elevation: 0,
      centerTitle: true,
    ),
    cardTheme: CardThemeData(
      elevation: _cardElevation,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(_borderRadius),
      ),
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      clipBehavior: Clip.antiAlias,
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(_borderRadius),
        ),
      ),
    ),
  );

  static final ThemeData _darkTheme = ThemeData(
    useMaterial3: true,
    brightness: Brightness.dark,
    colorScheme: ColorScheme.fromSeed(
      seedColor: AppColors.primary,
      brightness: Brightness.dark,
    ),
    appBarTheme: AppBarTheme(
      backgroundColor: AppColors.primaryDark,
      foregroundColor: Colors.white,
      elevation: 0,
      centerTitle: true,
    ),
    cardTheme: CardThemeData(
      elevation: _cardElevation,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(_borderRadius),
      ),
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      clipBehavior: Clip.antiAlias,
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.primaryLight,
        foregroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(_borderRadius),
        ),
      ),
    ),
  );
}

// ===============================
// Main Application Widget
// ===============================

class ShareApp extends StatelessWidget {
  const ShareApp({super.key});

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    return MaterialApp(
      title: 'منصة المشاركة والاستعارة',
      theme: themeProvider.themeData,
      home: const ShareItemsScreen(),
      debugShowCheckedModeBanner: false,
    );
  }
}

// ===============================
// Enhanced Main Screen
// ===============================

class ShareItemsScreen extends StatefulWidget {
  const ShareItemsScreen({super.key});

  @override
  State<ShareItemsScreen> createState() => _ShareItemsScreenState();
}

class _ShareItemsScreenState extends State<ShareItemsScreen>
    with TickerProviderStateMixin, AutomaticKeepAliveClientMixin {
  final PageController _adController = PageController();
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final RefreshController _refreshController =
  RefreshController(initialRefresh: false);

  ShareItemService get _itemService => Provider.of<ShareItemService>(context, listen: false);
  late final AnimationController _fabAnimationController;
  late final AnimationController _searchAnimationController;
  late final ConfettiController _confettiController;

  final Uuid _uuid = const Uuid();

  List<ShareItem> _shareItems = [];
  List<ShareItem> _filteredItems = [];
  List<AppNotification> _notifications = [];
  final Map<String, User> _users = {};
  final Map<String, List<Map<String, dynamic>>> _borrowHistory = {};
  final Map<String, List<Map<String, dynamic>>> _sharedItems = {};

  int _currentAd = 0;
  int _selectedCategoryIndex = 0;
  int _visibleItems = 10;
  String _selectedSort = 'newest';
  String _selectedFilter = 'all';
  bool _isLoading = true;
  bool _isLoadingMore = false;
  bool _showScrollToTop = false;
  bool _showFeaturedOnly = false;
  bool _isSearchExpanded = false;
  bool _isOffline = false;

  Timer? _timer;
  Timer? _searchDebounceTimer;

  final String _currentUserId = 'user1';
  final bool _isAdmin = false;

  final List<String> adImages = const [
    'https://images.unsplash.com/photo-1559027615-cd4628902d4a?w=800&h=400&fit=crop',
    'https://images.unsplash.com/photo-1488521787991-ed7bbaae773c?w=800&h=400&fit=crop',
    'https://images.unsplash.com/photo-1469571486292-0ba58a3f068b?w=800&h=400&fit=crop',
  ];

  final List<Map<String, dynamic>> categories = const [
    {
      'icon': Icons.all_inclusive,
      'title': 'الكل',
      'count': 0,
      'filter': 'all',
      'color': AppColors.primary
    },
    {
      'icon': Icons.build,
      'title': 'أدوات',
      'count': 45,
      'filter': 'tools',
      'color': Colors.blue
    },
    {
      'icon': Icons.book,
      'title': 'كتب',
      'count': 32,
      'filter': 'books',
      'color': Colors.red
    },
    {
      'icon': Icons.devices,
      'title': 'إلكترونيات',
      'count': 67,
      'filter': 'electronics',
      'color': Colors.orange
    },
    {
      'icon': Icons.checkroom,
      'title': 'ملابس',
      'count': 23,
      'filter': 'clothing',
      'color': Colors.green
    },
    {
      'icon': Icons.chair,
      'title': 'أثاث',
      'count': 23,
      'filter': 'furniture',
      'color': Colors.brown
    },
    {
      'icon': Icons.toys,
      'title': 'ألعاب',
      'count': 23,
      'filter': 'toys',
      'color': Colors.purple
    },
    {
      'icon': Icons.sports,
      'title': 'معدات رياضية',
      'count': 23,
      'filter': 'sports',
      'color': Colors.teal
    },
    {
      'icon': Icons.category,
      'title': 'أخرى',
      'count': 23,
      'filter': 'others',
      'color': Colors.grey
    },
  ];

  final List<Map<String, String>> sortOptions = const [
    {'value': 'newest', 'label': 'الأحدث'},
    {'value': 'oldest', 'label': 'الأقدم'},
    {'value': 'borrowers_high', 'label': 'عدد المستعيرين (الأعلى)'},
    {'value': 'borrowers_low', 'label': 'عدد المستعيرين (الأقل)'},
    {'value': 'urgent', 'label': 'عاجل'},
  ];

  @override
  bool get wantKeepAlive => true;

  // Animation durations
  static const _animationDuration = Duration(milliseconds: 300);
  static const _slowAnimationDuration = Duration(milliseconds: 500);

  @override
  void initState() {
    super.initState();
    _initializeServices();
    _initializeAnimations();
    _setupListeners();
    _loadInitialData();
  }

  @override
  void dispose() {
    _timer?.cancel();
    _searchDebounceTimer?.cancel();
    _scrollController.dispose();
    _searchController.dispose();
    _adController.dispose();
    _fabAnimationController.dispose();
    _searchAnimationController.dispose();
    _confettiController.dispose();
    _refreshController.dispose();
    super.dispose();
  }

  void _initializeServices() {
    // Service is now obtained through the getter
  }

  void _initializeAnimations() {
    _fabAnimationController = AnimationController(
      duration: _animationDuration,
      vsync: this,
    );

    _searchAnimationController = AnimationController(
      duration: _animationDuration,
      vsync: this,
    );

    _confettiController = ConfettiController(
      duration: const Duration(seconds: 3),
    );
  }

void _setupListeners() {
  _startAutoScroll();
  _scrollController.addListener(_scrollListener);
  _searchController.addListener(_onSearchChanged);

  Connectivity().onConnectivityChanged.listen((ConnectivityResult result) {
    setState(() {
      _isOffline = result == ConnectivityResult.none;
    });

    if (!_isOffline) {
      _refreshData();
    }
  });

  final appState = Provider.of<AppState>(context, listen: false);
  if (appState.socket != null) {
    _itemService.setupSocketListeners(
      appState.socket!,
      onNewItem: _handleNewItem,
      onItemUpdate: _handleItemUpdate,
      onNewBorrow: _handleNewBorrow,
      onNewNotification: _handleNewNotification,
    );
  }
}

Future<void> _loadInitialData() async {
  try {
    await _fetchItems(forceRefresh: true);
    await _fetchNotifications();
    
    if (mounted) {
      _confettiController.play();
    }
  } catch (e) {
    _showErrorSnackBar('Error loading initial data: $e');
  }
}

void _handleNewItem(dynamic data) {
  if (mounted) {
    setState(() {
      // Handle the new item data
      if (data is Map<String, dynamic>) {
        try {
          final newItem = ShareItem.fromJson(data);
          _shareItems.insert(0, newItem);
          _filterItems();
        } catch (e) {
          debugPrint('Error handling new item: $e');
        }
      }
    });
  }
}

void _handleItemUpdate(dynamic data) {
  _fetchItems();
}

void _handleNewBorrow(dynamic data) {
  _fetchItems();
  _showNotificationSnackBar(
    'طلب استعارة جديد لعنصر: ${data['itemName']}',
    AppColors.primary,
  );
}

void _handleNewNotification(dynamic data) {
  setState(() {
    _notifications.insert(0, AppNotification.fromJson(data));
  });
  _showNotificationSnackBar(
    data['message'],
    AppColors.accent,
  );
}

void _onSearchChanged() {
  _searchDebounceTimer?.cancel();
  _searchDebounceTimer = Timer(const Duration(milliseconds: 500), () {
    _filterItems();
  });
}

void _scrollListener() {
  if (_scrollController.position.pixels >=
      _scrollController.position.maxScrollExtent - 200 &&
      !_isLoadingMore) {
    _loadMoreItems();
  }

  final showScrollToTop = _scrollController.position.pixels > 200;
  if (showScrollToTop != _showScrollToTop) {
    setState(() => _showScrollToTop = showScrollToTop);

    if (showScrollToTop) {
      _fabAnimationController.forward();
    } else {
      _fabAnimationController.reverse();
    }
  }
}

Future<void> _fetchItems({bool forceRefresh = false}) async {
  if (!forceRefresh && _isOffline) return;

  setState(() => _isLoading = true);

  try {
    final items = await _itemService.fetchItems(
      search: _searchController.text,
      filter: _selectedFilter,
      sort: _selectedSort,
      forceRefresh: forceRefresh,
    );

    setState(() {
      _shareItems = items;
      _isLoading = false;
    });

    _filterItems();
  } catch (e) {
    _showErrorSnackBar('خطأ في جلب العناصر: $e');
    setState(() => _isLoading = false);
  }
}

Future<void> _loadMoreItems() async {
  if (_isOffline) return;

  setState(() => _isLoadingMore = true);

  try {
    final newItems = await _itemService.fetchItems(
      search: _searchController.text,
      filter: _selectedFilter,
      sort: _selectedSort,
      skip: _visibleItems,
    );

    setState(() {
      _shareItems.addAll(newItems);
      _visibleItems += 10;
      _isLoadingMore = false;
    });

    _filterItems();
  } catch (e) {
    _showErrorSnackBar('خطأ في تحميل المزيد: $e');
    setState(() => _isLoadingMore = false);
  }
}

Future<void> _fetchNotifications() async {
  if (_isOffline) return;

  try {
    final notifications = await _itemService.fetchNotifications();
    setState(() => _notifications = notifications);
  } catch (e) {
    _showErrorSnackBar('خطأ في جلب الإشعارات: $e');
  }
}

Future<void> _fetchBorrowHistory(String itemId) async {
  if (_isOffline) return;

  try {
    final history = await _itemService.fetchBorrowHistory(itemId);
    setState(() => _borrowHistory[itemId] = history);
  } catch (e) {
    _showErrorSnackBar('خطأ في جلب سجل الاستعارة: $e');
  }
}

Future<void> _fetchSharedItems(String creatorId) async {
  if (_isOffline) return;

  try {
    final items = await _itemService.fetchSharedItems(creatorId);
    setState(() => _sharedItems[creatorId] = items);
  } catch (e) {
    _showErrorSnackBar('خطأ في جلب العناصر المشتركة: $e');
  }
}

Future<void> _fetchUser(String userId) async {
  if (_isOffline || _users.containsKey(userId)) return;

  try {
    final user = await _itemService.fetchUser(userId);
    if (user != null) {
      setState(() => _users[userId] = user);
    }
  } catch (e) {}
}

void _filterItems() {
  List<ShareItem> filtered = List.from(_shareItems);

  if (_searchController.text.isNotEmpty) {
    final searchTerm = _searchController.text.toLowerCase();
    filtered = filtered
        .where((item) =>
    item.title.toLowerCase().contains(searchTerm) ||
        item.description.toLowerCase().contains(searchTerm) ||
        item.location.toLowerCase().contains(searchTerm) ||
        item.tags.any((tag) => tag.toLowerCase().contains(searchTerm)))
        .toList();
  }

  if (_selectedCategoryIndex > 0) {
    final categoryFilter = categories[_selectedCategoryIndex]['filter'];
    filtered = filtered.where((item) => item.category == categoryFilter).toList();
  }

  if (_showFeaturedOnly) {
    filtered = filtered.where((item) => item.isFeatured).toList();
  }

  setState(() => _filteredItems = filtered);
}

Future<void> _refreshData() async {
  HapticFeedback.lightImpact();
  setState(() => _visibleItems = 10);

  await Future.wait([
    _fetchItems(forceRefresh: true),
    _fetchNotifications(),
  ]);

  _refreshController.refreshCompleted();
}

void _startAutoScroll() {
  _timer = Timer.periodic(const Duration(seconds: 5), (timer) {
    if (!mounted || adImages.isEmpty) return;

    setState(() => _currentAd = (_currentAd + 1) % adImages.length);

    if (_adController.hasClients) {
      _adController.animateToPage(
        _currentAd,
        duration: _slowAnimationDuration,
        curve: Curves.easeInOut,
      );
    }
  });
}

void _showSuccessSnackBar(String message) {
  _showNotificationSnackBar(message, AppColors.success);
}

void _showErrorSnackBar(String message) {
  _showNotificationSnackBar(message, AppColors.error);
}

void _showNotificationSnackBar(String message, Color backgroundColor) {
  if (!mounted) return;

  ScaffoldMessenger.of(context).showSnackBar(
    SnackBar(
      content: Row(
        children: [
          Icon(
            backgroundColor == AppColors.error
                ? Icons.error
                : Icons.check_circle,
            color: Colors.white,
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              message,
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
      backgroundColor: backgroundColor,
      behavior: SnackBarBehavior.floating,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(_borderRadius),
      ),
      elevation: 6,
      margin: const EdgeInsets.all(16),
      duration: const Duration(seconds: 4),
      action: SnackBarAction(
        label: 'إغلاق',
        textColor: Colors.white,
        onPressed: () {
          ScaffoldMessenger.of(context).hideCurrentSnackBar();
        },
      ),
    ),
  );
}

@override
Widget build(BuildContext context) {
  super.build(context);
  final isDarkMode = Theme.of(context).brightness == Brightness.dark;

  return Scaffold(
    body: OfflineBuilder(
      connectivityBuilder: (context, connectivity, child) {
        final connected = connectivity != ConnectivityResult.none;

        if (!connected) {
          return _buildOfflineWidget();
        }

        return child!;
      },
      child: Stack(
        children: [
          _buildMainContent(isDarkMode),
          _buildFloatingElements(),
          _buildConfetti(),
        ],
      ),
    ),
  );
}

Widget _buildMainContent(bool isDarkMode) {
  return NestedScrollView(
    headerSliverBuilder: (context, innerBoxIsScrolled) => [
      _buildSliverAppBar(isDarkMode),
    ],
    body: SmartRefresher(
      controller: _refreshController,
      onRefresh: _refreshData,
      header: WaterDropMaterialHeader(
        backgroundColor: AppColors.primary,
        color: Colors.white,
      ),
      child: CustomScrollView(
        controller: _scrollController,
        slivers: [
          _buildBannerSection(),
          _buildCategoriesSection(),
          _buildFiltersSection(),
          _buildItemsSection(),
          _buildLoadingMoreSection(),
        ],
      ),
    ),
  );
}

Widget _buildSliverAppBar(bool isDarkMode) {
  return SliverAppBar(
    expandedHeight: 120,
    floating: true,
    pinned: true,
    snap: true,
    flexibleSpace: FlexibleSpaceBar(
      background: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: isDarkMode
                ? [AppColors.primaryDark, AppColors.primary]
                : [AppColors.primary, AppColors.primaryLight],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
      ),
      title: AnimatedSwitcher(
        duration: _animationDuration,
        child: _isSearchExpanded
            ? _buildSearchField()
            : const Text(
          'منصة المشاركة والاستعارة',
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    ),
    actions: [
      _buildSearchToggleButton(),
      _buildNotificationButton(),
      _buildThemeToggleButton(),
      _buildMenuButton(),
    ],
  );
}

Widget _buildSearchField() {
  return Container(
    height: 40,
    margin: const EdgeInsets.symmetric(horizontal: 16),
    decoration: BoxDecoration(
      color: Colors.white.withOpacity(0.2),
      borderRadius: BorderRadius.circular(_borderRadius),
    ),
    child: TextField(
      controller: _searchController,
      style: const TextStyle(color: Colors.white),
      decoration: InputDecoration(
        hintText: 'ابحث عن عنصر...',
        hintStyle: const TextStyle(color: Colors.white70),
        border: InputBorder.none,
        contentPadding:
        const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        prefixIcon: const Icon(Icons.search, color: Colors.white70),
        suffixIcon: _searchController.text.isNotEmpty
            ? IconButton(
          icon: const Icon(Icons.clear, color: Colors.white70),
          onPressed: () {
            _searchController.clear();
            HapticFeedback.selectionClick();
          },
        )
            : null,
      ),
      onSubmitted: (_) => _fetchItems(),
    ),
  );
}

Widget _buildSearchToggleButton() {
  return IconButton(
    icon: AnimatedRotation(
      turns: _isSearchExpanded ? 0.5 : 0,
      duration: _animationDuration,
      child: Icon(
        _isSearchExpanded ? Icons.close : Icons.search,
        color: Colors.white,
      ),
    ),
    onPressed: () {
      setState(() => _isSearchExpanded = !_isSearchExpanded);
      HapticFeedback.selectionClick();

      if (_isSearchExpanded) {
        _searchAnimationController.forward();
      } else {
        _searchAnimationController.reverse();
        _searchController.clear();
      }
    },
  );
}

Widget _buildNotificationButton() {
  final unreadCount = _notifications.where((n) => !n.isRead).length;

  return Stack(
    children: [
      IconButton(
        icon: const Icon(Icons.notifications, color: Colors.white),
        onPressed: () {
          HapticFeedback.selectionClick();
          _showNotificationsBottomSheet();
        },
      ),
      if (unreadCount > 0)
        Positioned(
          right: 8,
          top: 8,
          child: Container(
            padding: const EdgeInsets.all(2),
            decoration: BoxDecoration(
              color: AppColors.error,
              borderRadius: BorderRadius.circular(10),
            ),
            constraints: const BoxConstraints(
              minWidth: 16,
              minHeight: 16,
            ),
            child: Text(
              unreadCount > 99 ? '99+' : unreadCount.toString(),
              style: const TextStyle(
                color: Colors.white,
                fontSize: 10,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),
    ],
  );
}

Widget _buildThemeToggleButton() {
  return Consumer<ThemeProvider>(
    builder: (context, themeProvider, child) {
      return IconButton(
        icon: AnimatedSwitcher(
          duration: _animationDuration,
          child: Icon(
            themeProvider.isDarkMode ? Icons.light_mode : Icons.dark_mode,
            key: ValueKey(themeProvider.isDarkMode),
            color: Colors.white,
          ),
        ),
        onPressed: () {
          HapticFeedback.selectionClick();
          themeProvider._toggleTheme();
        },
      );
    },
  );
}

Widget _buildMenuButton() {
  return PopupMenuButton<String>(
    icon: const Icon(Icons.more_vert, color: Colors.white),
    onSelected: _handleMenuSelection,
    itemBuilder: (context) => [
      const PopupMenuItem(
        value: 'history',
        child: ListTile(
          leading: Icon(Icons.history),
          title: Text('سجل الاستعارة'),
          contentPadding: EdgeInsets.zero,
        ),
      ),
      const PopupMenuItem(
        value: 'favorites',
        child: ListTile(
          leading: Icon(Icons.favorite),
          title: Text('المفضلة'),
          contentPadding: EdgeInsets.zero,
        ),
      ),
      const PopupMenuItem(
        value: 'settings',
        child: ListTile(
          leading: Icon(Icons.settings),
          title: Text('الإعدادات'),
          contentPadding: EdgeInsets.zero,
        ),
      ),
      const PopupMenuItem(
        value: 'about',
        child: ListTile(
          leading: Icon(Icons.info),
          title: Text('حول التطبيق'),
          contentPadding: EdgeInsets.zero,
        ),
      ),
    ],
  );
}

void _handleMenuSelection(String value) {
  HapticFeedback.selectionClick();

  switch (value) {
    case 'history':
      _showBorrowHistory(context);
      break;
    case 'favorites':
      _showFavoriteItems();
      break;
    case 'settings':
      _showSettingsDialog();
      break;
    case 'about':
      _showAboutDialog();
      break;
  }
}

Widget _buildBannerSection() {
  return SliverToBoxAdapter(
    child: Container(
      height: 200,
      margin: const EdgeInsets.all(16),
      child: Stack(
        children: [
          PageView.builder(
            controller: _adController,
            onPageChanged: (index) => setState(() => _currentAd = index),
            itemCount: adImages.length,
            itemBuilder: (context, index) =>
                _buildBannerCard(adImages[index]),
          ),
          _buildBannerIndicators(),
        ],
      ),
    ),
  );
}

Widget _buildBannerCard(String imageUrl) {
  return FadeInUp(
    duration: _slowAnimationDuration,
    child: Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(_borderRadius),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(_borderRadius),
        child: Stack(
          fit: StackFit.expand,
          children: [
            CachedNetworkImage(
              imageUrl: imageUrl,
              fit: BoxFit.cover,
              placeholder: (context, url) => Shimmer.fromColors(
                baseColor: Colors.grey[300]!,
                highlightColor: Colors.grey[100]!,
                child: Container(color: Colors.white),
              ),
              errorWidget: (context, url, error) => Container(
                color: Colors.grey[300],
                child: const Icon(Icons.error, size: 50),
              ),
            ),
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.transparent,
                    Colors.black.withOpacity(0.7),
                  ],
                ),
              ),
            ),
            Positioned(
              bottom: 16,
              left: 16,
              right: 16,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'شارك واستعر مع المجتمع',
                    style:
                    Theme.of(context).textTheme.headlineSmall?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'انضم إلى منصتنا لمشاركة واستعارة الأغراض بسهولة وأمان',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.white70,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    ),
  );
}

Widget _buildBannerIndicators() {
  return Positioned(
    bottom: 16,
    right: 16,
    child: Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(
        adImages.length,
            (index) => AnimatedContainer(
          duration: _animationDuration,
          margin: const EdgeInsets.symmetric(horizontal: 2),
          width: _currentAd == index ? 20 : 8,
          height: 8,
          decoration: BoxDecoration(
            color: _currentAd == index
                ? Colors.white
                : Colors.white.withOpacity(0.5),
            borderRadius: BorderRadius.circular(4),
          ),
        ),
      ),
    ),
  );
}

Widget _buildCategoriesSection() {
  return SliverToBoxAdapter(
    child: Container(
      height: 120,
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: AnimationLimiter(
        child: ListView.builder(
          scrollDirection: Axis.horizontal,
          padding: const EdgeInsets.symmetric(horizontal: 16),
          itemCount: categories.length,
          itemBuilder: (context, index) {
            return AnimationConfiguration.staggeredList(
              position: index,
              duration: _animationDuration,
              child: SlideAnimation(
                horizontalOffset: 50.0,
                child: FadeInAnimation(
                  child: _buildCategoryCard(index),
                ),
              ),
            );
          },
        ),
      ),
    ),
  );
}

Widget _buildCategoryCard(int index) {
  final isSelected = _selectedCategoryIndex == index;
  final category = categories[index];

  return GestureDetector(
    onTap: () {
      setState(() => _selectedCategoryIndex = index);
      HapticFeedback.selectionClick();
      _filterItems();
    },
    child: AnimatedContainer(
      duration: _animationDuration,
      width: 100,
      margin: const EdgeInsets.only(right: 12),
      decoration: BoxDecoration(
        color: isSelected
            ? (category['color'] as Color).withOpacity(0.1)
            : Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(_borderRadius),
        border: Border.all(
          color:
          isSelected ? (category['color'] as Color) : Colors.transparent,
          width: 2,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          AnimatedScale(
            scale: isSelected ? 1.2 : 1.0,
            duration: _animationDuration,
            child: Icon(
              category['icon'] as IconData,
              size: 32,
              color: isSelected
                  ? (category['color'] as Color)
                  : Theme.of(context).iconTheme.color,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            category['title'] as String,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              fontWeight:
              isSelected ? FontWeight.bold : FontWeight.normal,
              color: isSelected ? (category['color'] as Color) : null,
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 4),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            decoration: BoxDecoration(
              color: (category['color'] as Color).withOpacity(0.1),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Text(
              '${category['count']}',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: category['color'] as Color,
                fontWeight: FontWeight.bold,
                fontSize: 10,
              ),
            ),
          ),
        ],
      ),
    ),
  );
}

Widget _buildFiltersSection() {
  return SliverToBoxAdapter(
    child: Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Expanded(
            child: _buildSortDropdown(),
          ),
          const SizedBox(width: 12),
          _buildFeaturedToggle(),
          const SizedBox(width: 12),
          _buildFilterButton(),
        ],
      ),
    ),
  );
}

Widget _buildSortDropdown() {
  return Container(
    padding: const EdgeInsets.symmetric(horizontal: 12),
    decoration: BoxDecoration(
      border: Border.all(color: Colors.grey[300]!),
      borderRadius: BorderRadius.circular(_borderRadius),
    ),
    child: DropdownButtonHideUnderline(
      child: DropdownButton<String>(
        value: _selectedSort,
        isExpanded: true,
        icon: const Icon(Icons.arrow_drop_down),
        items: sortOptions.map((option) {
          return DropdownMenuItem<String>(
            value: option['value'],
            child: Text(option['label']!),
          );
        }).toList(),
        onChanged: (value) {
          if (value != null) {
            setState(() => _selectedSort = value);
            HapticFeedback.selectionClick();
            _fetchItems();
          }
        },
      ),
    ),
  );
}

Widget _buildFeaturedToggle() {
  return FilterChip(
    label: const Text('مميز'),
    selected: _showFeaturedOnly,
    onSelected: (selected) {
      setState(() => _showFeaturedOnly = selected);
      HapticFeedback.selectionClick();
      _filterItems();
    },
    selectedColor: AppColors.primary.withOpacity(0.2),
    checkmarkColor: AppColors.primary,
  );
}

Widget _buildFilterButton() {
  return IconButton(
    icon: const Icon(Icons.filter_list),
    onPressed: () {
      HapticFeedback.selectionClick();
      _showAdvancedFiltersBottomSheet();
    },
  );
}

Widget _buildItemsSection() {
  if (_isLoading) {
    return _buildLoadingSection();
  }

  if (_filteredItems.isEmpty) {
    return _buildEmptySection();
  }

  return SliverPadding(
    padding: const EdgeInsets.symmetric(horizontal: 16),
    sliver: SliverList(
      delegate: SliverChildBuilderDelegate(
            (context, index) {
          if (index >= _filteredItems.length) return null;

          return AnimationConfiguration.staggeredList(
            position: index,
            duration: _animationDuration,
            child: SlideAnimation(
              verticalOffset: 50.0,
              child: FadeInAnimation(
                child: _buildItemCard(_filteredItems[index], index),
              ),
            ),
          );
        },
        childCount: _filteredItems.length,
      ),
    ),
  );
}

Widget _buildLoadingSection() {
  return SliverPadding(
    padding: const EdgeInsets.all(16),
    sliver: SliverList(
      delegate: SliverChildBuilderDelegate(
            (context, index) => _buildLoadingCard(),
        childCount: 5,
      ),
    ),
  );
}

Widget _buildLoadingCard() {
  return Container(
    margin: const EdgeInsets.only(bottom: 16),
    child: Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: Card(
        child: Container(
          height: 200,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(_borderRadius),
          ),
        ),
      ),
    ),
  );
}

Widget _buildEmptySection() {
  return SliverFillRemaining(
    child: Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 80,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد عناصر للمشاركة',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'جرب تغيير معايير البحث أو الفلترة',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[500],
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () {
              _searchController.clear();
              setState(() {
                _selectedCategoryIndex = 0;
                _selectedFilter = 'all';
                _showFeaturedOnly = false;
              });
              _fetchItems();
            },
            icon: const Icon(Icons.refresh),
            label: const Text('إعادة تحميل'),
          ),
        ],
      ),
    ),
  );
}

Widget _buildItemCard(ShareItem item, int index) {
  return Container(
    margin: const EdgeInsets.only(bottom: 16),
    child: Card(
      elevation: _cardElevation,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(_borderRadius),
      ),
      child: InkWell(
        borderRadius: BorderRadius.circular(_borderRadius),
        onTap: () => _showItemDetails(item),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildItemImage(item),
            _buildItemContent(item),
            _buildItemActions(item),
          ],
        ),
      ),
    ),
  );
}

Widget _buildItemImage(ShareItem item) {
  return Stack(
    children: [
      ClipRRect(
        borderRadius: const BorderRadius.vertical(
          top: Radius.circular(_borderRadius),
        ),
        child: AspectRatio(
          aspectRatio: 16 / 9,
          child: CachedNetworkImage(
            imageUrl: item.firstImageUrl,
            fit: BoxFit.cover,
            placeholder: (context, url) => Shimmer.fromColors(
              baseColor: Colors.grey[300]!,
              highlightColor: Colors.grey[100]!,
              child: Container(color: Colors.white),
            ),
            errorWidget: (context, url, error) => Container(
              color: Colors.grey[300],
              child: const Icon(Icons.image_not_supported, size: 50),
            ),
          ),
        ),
      ),
      _buildItemBadges(item),
      _buildFavoriteButton(item),
    ],
  );
}

Widget _buildItemBadges(ShareItem item) {
  return Positioned(
    top: 8,
    left: 8,
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (item.isFeatured)
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: AppColors.warning,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(Icons.star, color: Colors.white, size: 12),
                const SizedBox(width: 4),
                Text(
                  'مميز',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        if (item.isUrgent)
          Container(
            margin: const EdgeInsets.only(top: 4),
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: AppColors.error,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(Icons.warning, color: Colors.white, size: 12),
                const SizedBox(width: 4),
                Text(
                  'عاجل',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
      ],
    ),
  );
}

Widget _buildFavoriteButton(ShareItem item) {
  return Positioned(
    top: 8,
    right: 8,
    child: GestureDetector(
      onTap: () => _toggleFavorite(item),
      child: AnimatedScale(
        scale: item.isFavorite ? 1.2 : 1.0,
        duration: _animationDuration,
        child: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.9),
            shape: BoxShape.circle,
          ),
          child: Icon(
            item.isFavorite ? Icons.favorite : Icons.favorite_border,
            color: item.isFavorite ? AppColors.error : Colors.grey,
            size: 20,
          ),
        ),
      ),
    ),
  );
}

Widget _buildItemContent(ShareItem item) {
  return Padding(
    padding: const EdgeInsets.all(16),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildItemHeader(item),
        const SizedBox(height: 8),
        _buildItemDescription(item),
        const SizedBox(height: 12),
        _buildItemMetadata(item),
      ],
    ),
  );
}

Widget _buildItemHeader(ShareItem item) {
  return Row(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      Expanded(
        child: Text(
          item.title,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
      ),
      const SizedBox(width: 8),
      Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: _getCategoryColor(item.category).withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Text(
          _getCategoryTitle(item.category),
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: _getCategoryColor(item.category),
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    ],
  );
}

Widget _buildItemDescription(ShareItem item) {
  return Text(
    item.description,
    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
      color: Colors.grey[600],
    ),
    maxLines: 3,
    overflow: TextOverflow.ellipsis,
  );
}

Widget _buildItemMetadata(ShareItem item) {
  return Row(
    children: [
      Icon(Icons.location_on, size: 16, color: Colors.grey[600]),
      const SizedBox(width: 4),
      Expanded(
        child: Text(
          item.location,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Colors.grey[600],
          ),
          overflow: TextOverflow.ellipsis,
        ),
      ),
      const SizedBox(width: 16),
      Icon(Icons.people, size: 16, color: Colors.grey[600]),
      const SizedBox(width: 4),
      Text(
        '${item.borrowerCount} مستعير',
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
          color: Colors.grey[600],
        ),
      ),
      const SizedBox(width: 16),
      if (item.rating != null && (item.ratingCount ?? 0) > 0) ...[
        GestureDetector(
          onTap: () => _showRatingsScreen(item),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.star, size: 16, color: Colors.amber),
              const SizedBox(width: 2),
              Text(
                item.rating!.toStringAsFixed(1),
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.amber[700],
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(width: 2),
              Text(
                '(${item.ratingCount})',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
        const SizedBox(width: 16),
      ] else const SizedBox.shrink(),
      Icon(Icons.access_time, size: 16, color: Colors.grey[600]),
      const SizedBox(width: 4),
      Text(
        _formatDate(item.date),
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
          color: Colors.grey[600],
        ),
      ),
    ],
  );
}

Widget _buildItemActions(ShareItem item) {return Container(
  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
  decoration: BoxDecoration(
    border: Border(top: BorderSide(color: Colors.grey[200]!)),
  ),
  child: Row(
    mainAxisAlignment: MainAxisAlignment.spaceBetween,
    children: [
      _buildBorrowButton(item),
      _buildShareButton(item),
      _buildMoreActions(item),
    ],
  ),
);
}

Widget _buildBorrowButton(ShareItem item) {
  return TextButton.icon(
    icon: const Icon(Icons.handshake, size: 18),
    label: const Text('استعارة'),
    style: TextButton.styleFrom(
      foregroundColor: item.isAvailable ? AppColors.primary : Colors.grey,
    ),
    onPressed: item.isAvailable && !item.isExpired
        ? () => _handleBorrowRequest(item)
        : null,
  );
}

Widget _buildShareButton(ShareItem item) {
  return TextButton.icon(
    icon: const Icon(Icons.share, size: 18),
    label: const Text('مشاركة'),
    style: TextButton.styleFrom(foregroundColor: AppColors.accent),
    onPressed: () => _shareItem(item),
  );
}

Widget _buildMoreActions(ShareItem item) {
  return PopupMenuButton<String>(
    icon: const Icon(Icons.more_horiz),
    onSelected: (value) => _handleItemAction(value, item),
    itemBuilder: (context) => [
      if (item.creatorId == _currentUserId || _isAdmin) ...[
        const PopupMenuItem(
          value: 'edit',
          child: ListTile(
            leading: Icon(Icons.edit),
            title: Text('تعديل'),
            contentPadding: EdgeInsets.zero,
          ),
        ),
        const PopupMenuItem(
          value: 'delete',
          child: ListTile(
            leading: Icon(Icons.delete),
            title: Text('حذف'),
            contentPadding: EdgeInsets.zero,
          ),
        ),
      ],
      const PopupMenuItem(
        value: 'report',
        child: ListTile(
          leading: Icon(Icons.flag),
          title: Text('الإبلاغ'),
          contentPadding: EdgeInsets.zero,
        ),
      ),
    ],
  );
}

Widget _buildLoadingMoreSection() {
  return SliverToBoxAdapter(
    child: _isLoadingMore
        ? Container(
      padding: const EdgeInsets.all(16),
      child: const Center(
        child: CircularProgressIndicator(),
      ),
    )
        : const SizedBox.shrink(),
  );
}

Widget _buildFloatingElements() {
  return Positioned(
    bottom: 16,
    right: 16,
    child: Column(
      children: [
        if (_showScrollToTop)
          ScaleTransition(
            scale: CurvedAnimation(
              parent: _fabAnimationController,
              curve: Curves.easeInOut,
            ),
            child: FloatingActionButton(
              heroTag: 'scroll_to_top',
              mini: true,
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
              onPressed: () {
                _scrollController.animateTo(
                  0,
                  duration: _animationDuration,
                  curve: Curves.easeInOut,
                );
                HapticFeedback.mediumImpact();
              },
              child: const Icon(Icons.arrow_upward),
            ),
          ),
        const SizedBox(height: 16),
        FloatingActionButton(
          heroTag: 'add_item',
          backgroundColor: AppColors.accent,
          foregroundColor: Colors.white,
          onPressed: _showCreateItemDialog,
          child: const Icon(Icons.add),
        ),
      ],
    ),
  );
}

Widget _buildConfetti() {
  return Align(
    alignment: Alignment.topCenter,
    child: ConfettiWidget(
      confettiController: _confettiController,
      blastDirectionality: BlastDirectionality.explosive,
      emissionFrequency: 0.05,
      numberOfParticles: 20,
      maxBlastForce: 100,
      minBlastForce: 80,
      colors: const [
        AppColors.primary,
        AppColors.accent,
        AppColors.success,
      ],
    ),
  );
}

Widget _buildOfflineWidget() {
  return Center(
    child: Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          Icons.wifi_off,
          size: 80,
          color: Colors.grey[400],
        ),
        const SizedBox(height: 16),
        Text(
          'لا يوجد اتصال بالإنترنت',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            color: Colors.grey[600],
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'يرجى التحقق من الاتصال وإعادة المحاولة',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: Colors.grey[500],
          ),
        ),
        const SizedBox(height: 24),
        ElevatedButton.icon(
          onPressed: _refreshData,
          icon: const Icon(Icons.refresh),
          label: const Text('إعادة المحاولة'),
        ),
      ],
    ),
  );
}

// Helper Methods

void _handleBorrowRequest(ShareItem item) async {
  HapticFeedback.selectionClick();
  final messageController = TextEditingController();
  final result = await showDialog<bool>(
    context: context,
    builder: (context) => AlertDialog(
      title: const Text('طلب استعارة'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text('هل تريد استعارة "${item.title}"؟'),
          const SizedBox(height: 16),
          TextField(
            controller: messageController,
            decoration: const InputDecoration(
              labelText: 'رسالة اختيارية للمالك',
              border: OutlineInputBorder(),
            ),
            maxLines: 3,
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context, false),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: () => Navigator.pop(context, true),
          child: const Text('إرسال الطلب'),
        ),
      ],
    ),
  );

  if (result == true) {
    try {
      await _itemService.requestBorrow(
        itemId: item.id,
        borrowerId: _currentUserId,
        message: messageController.text,
      );
      _showSuccessSnackBar('تم إرسال طلب الاستعارة بنجاح');
      _fetchItems(forceRefresh: true);
    } catch (e) {
      _showErrorSnackBar('فشل في إرسال طلب الاستعارة: $e');
    }
  }
}

void _shareItem(ShareItem item) async {
  HapticFeedback.selectionClick();
  final url = 'https://shareapp.com/items/${item.id}';
  await Share.share(
    'تحقق من هذا العنصر على منصة المشاركة والاستعارة: ${item.title}\n$url',
    subject: item.title,
  );
}

void _handleItemAction(String action, ShareItem item) async {
  HapticFeedback.selectionClick();
  switch (action) {
    case 'edit':
      _showEditItemDialog(item);
      break;
    case 'delete':
      final confirm = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('تأكيد الحذف'),
          content: Text('هل أنت متأكد من حذف "${item.title}"؟'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context, false),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.pop(context, true),
              child: const Text('حذف'),
            ),
          ],
        ),
      );

      if (confirm == true) {
        try {
          await _itemService.deleteItem(item.id);
          _showSuccessSnackBar('تم حذف العنصر بنجاح');
          _fetchItems(forceRefresh: true);
        } catch (e) {
          _showErrorSnackBar('فشل في حذف العنصر: $e');
        }
      }
      break;
    case 'report':
      _showReportDialog(item);
      break;
  }
}

void _showItemDetails(ShareItem item) async {
  await _fetchUser(item.creatorId);
  await _fetchBorrowHistory(item.id);
  if (!mounted) return;

  Navigator.push(
    context,
    MaterialPageRoute(
      builder: (context) => ShareItemDetailsScreen(
        item: item,
        user: _users[item.creatorId],
        borrowHistory: _borrowHistory[item.id] ?? [],
        onBorrow: () => _handleBorrowRequest(item),
        onShare: () => _shareItem(item),
        onFavorite: () => _toggleFavorite(item),
        onReport: () => _showReportDialog(item),
        onEdit: item.creatorId == _currentUserId || _isAdmin
            ? () => _showEditItemDialog(item)
            : null,
        onDelete: item.creatorId == _currentUserId || _isAdmin
            ? () => _handleItemAction('delete', item)
            : null,
      ),
    ),
  );
}

void _toggleFavorite(ShareItem item) {
  setState(() {
    item.isFavorite = !item.isFavorite;
    _filteredItems = List.from(_filteredItems);
  });
  HapticFeedback.selectionClick();
  _showSuccessSnackBar(
      item.isFavorite ? 'تمت الإضافة إلى المفضلة' : 'تمت الإزالة من المفضلة');
}

void _showCreateItemDialog() {
  Navigator.push(
    context,
    MaterialPageRoute(
      builder: (context) => CreateShareItemScreen(
        onSubmit: (title, description, location, category, file, tags, urgencyLevel, borrowDurationDays) async {
          try {
            await _itemService.createItem(
              title: title,
              description: description,
              location: location,
              category: category,
              file: file,
              creatorId: _currentUserId,
              tags: tags,
              urgencyLevel: urgencyLevel,
              borrowDurationDays: borrowDurationDays,
            );
            _showSuccessSnackBar('تم إنشاء العنصر بنجاح');
            _fetchItems(forceRefresh: true);
          } catch (e) {
            _showErrorSnackBar('فشل في إنشاء العنصر: $e');
          }
        },
      ),
    ),
  );
}

void _showEditItemDialog(ShareItem item) {
  Navigator.push(
    context,
    MaterialPageRoute(
      builder: (context) => CreateShareItemScreen(
        item: item,
        onSubmit: (title, description, location, category, file, tags, urgencyLevel, borrowDurationDays) async {
          try {
            await _itemService.updateItemStatus(item.id, 'pending');
            // Note: Actual update logic would depend on backend API
            _showSuccessSnackBar('تم تحديث العنصر بنجاح');
            _fetchItems(forceRefresh: true);
          } catch (e) {
            _showErrorSnackBar('فشل في تحديث العنصر: $e');
          }
        },
      ),
    ),
  );
}

void _showReportDialog(ShareItem item) {
  final reportController = TextEditingController();
  showDialog(
    context: context,
    builder: (context) => AlertDialog(
      title: const Text('الإبلاغ عن العنصر'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text('لماذا تريد الإبلاغ عن "${item.title}"؟'),
          const SizedBox(height: 16),
          TextField(
            controller: reportController,
            decoration: const InputDecoration(
              labelText: 'سبب الإبلاغ',
              border: OutlineInputBorder(),
            ),
            maxLines: 3,
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: () {
            if (reportController.text.isNotEmpty) {
              _showSuccessSnackBar('تم إرسال بلاغك بنجاح');
              Navigator.pop(context);
            } else {
              _showErrorSnackBar('يرجى إدخال سبب الإبلاغ');
            }
          },
          child: const Text('إرسال'),
        ),
      ],
    ),
  );
}

void _showNotificationsBottomSheet() {
  showModalBottomSheet(
    context: context,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(top: Radius.circular(_borderRadius)),
    ),
    builder: (context) => NotificationSheet(
      notifications: _notifications,
      onMarkAsRead: (notification) async {
        try {
          await _itemService.markNotificationAsRead(notification.id);
          setState(() {
            final index = _notifications.indexWhere((n) => n.id == notification.id);
            if (index != -1) {
              _notifications[index] = notification.copyWith(isRead: true);
            }
          });
        } catch (e) {
          _showErrorSnackBar('فشل في تحديث الإشعار: $e');
        }
      },
      onAction: (notification) {
        if (notification.actionUrl != null) {
          launchUrl(Uri.parse(notification.actionUrl!));
        }
      },
    ),
  );
}

void _showAdvancedFiltersBottomSheet() {
  showModalBottomSheet(
    context: context,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(top: Radius.circular(_borderRadius)),
    ),
    builder: (context) => AdvancedFiltersSheet(
      selectedFilter: _selectedFilter,
      onApply: (filter) {
        setState(() => _selectedFilter = filter);
        _fetchItems();
      },
    ),
  );
}

void _showBorrowHistory(BuildContext context) {
  Navigator.push(
    context,
    MaterialPageRoute(
      builder: (context) => BorrowHistoryScreen(
        userId: _currentUserId,
        itemService: _itemService,
      ),
    ),
  );
}

void _showFavoriteItems() {
  setState(() {
    _filteredItems = _shareItems.where((item) => item.isFavorite).toList();
    _selectedCategoryIndex = 0;
    _selectedFilter = 'favorites';
    _showFeaturedOnly = false;
  });
}

void _showSettingsDialog() {
  showDialog(
    context: context,
    builder: (context) => SettingsDialog(
      onPrivacyChanged: (showHistory) async {
        try {
          await _itemService.updateUserHistoryVisibility(_currentUserId, showHistory);
          _showSuccessSnackBar('تم تحديث إعدادات الخصوصية');
        } catch (e) {
          _showErrorSnackBar('فشل في تحديث الإعدادات: $e');
        }
      },
    ),
  );
}

void _showAboutDialog() async {
  final packageInfo = await PackageInfo.fromPlatform();
  showAboutDialog(
    context: context,
    applicationName: 'منصة المشاركة والاستعارة',
    applicationVersion: packageInfo.version,
    applicationIcon: const Icon(Icons.share, size: 50, color: AppColors.primary),
    children: [
      const Text(
        'منصة لمشاركة واستعارة الأغراض بسهولة وأمان ضمن المجتمع',
        textAlign: TextAlign.center,
      ),
    ],
  );
}

void _showRatingsScreen(ShareItem item) {
  Navigator.push(
    context,
    MaterialPageRoute(
      builder: (context) => RatingsScreen(
        itemId: item.id,
        currentRating: item.rating,
        ratingCount: item.ratingCount ?? 0,
        onRate: (rating) async {
          try {
            // Placeholder for rating submission logic
            _showSuccessSnackBar('تم إرسال تقييمك بنجاح');
            _fetchItems(forceRefresh: true);
          } catch (e) {
            _showErrorSnackBar('فشل في إرسال التقييم: $e');
          }
        },
      ),
    ),
  );
}

String _formatDate(DateTime date) {
  final now = DateTime.now();
  final difference = now.difference(date);

  if (difference.inDays > 30) {
    return DateFormat.yMMMd('ar').format(date);
  } else if (difference.inDays > 0) {
    return 'منذ ${difference.inDays} يوم${difference.inDays > 1 ? '' : ''}';
  } else if (difference.inHours > 0) {
    return 'منذ ${difference.inHours} ساعة';
  } else {
    return 'منذ ${difference.inMinutes} دقيقة';
  }
}

Color _getCategoryColor(String category) {
  final categoryMap = categories.firstWhere(
        (cat) => cat['filter'] == category,
    orElse: () => categories[0],
  );
  return categoryMap['color'] as Color;
}

String _getCategoryTitle(String category) {
  final categoryMap = categories.firstWhere(
        (cat) => cat['filter'] == category,
    orElse: () => categories[0],
  );
  return categoryMap['title'] as String;
}
}

// Placeholder Widgets for Referenced Screens

class ShareItemDetailsScreen extends StatelessWidget {
  final ShareItem item;
  final User? user;
  final List<Map<String, dynamic>> borrowHistory;
  final VoidCallback onBorrow;
  final VoidCallback onShare;
  final VoidCallback onFavorite;
  final VoidCallback onReport;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;

  const ShareItemDetailsScreen({
    super.key,
    required this.item,
    this.user,
    required this.borrowHistory,
    required this.onBorrow,
    required this.onShare,
    required this.onFavorite,
    required this.onReport,
    this.onEdit,
    this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(item.title),
        actions: [
          IconButton(
            icon: Icon(item.isFavorite ? Icons.favorite : Icons.favorite_border),
            onPressed: onFavorite,
          ),
          PopupMenuButton<String>(
            onSelected: (value) {
              if (value == 'share') onShare();
              if (value == 'report') onReport();
              if (value == 'edit') onEdit?.call();
              if (value == 'delete') onDelete?.call();
            },
            itemBuilder: (context) => [
              const PopupMenuItem(value: 'share', child: Text('مشاركة')),
              const PopupMenuItem(value: 'report', child: Text('الإبلاغ')),
              if (onEdit != null)
                const PopupMenuItem(value: 'edit', child: Text('تعديل')),
              if (onDelete != null)
                const PopupMenuItem(value: 'delete', child: Text('حذف')),
            ],
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CachedNetworkImage(
              imageUrl: item.firstImageUrl,
              height: 200,
              width: double.infinity,
              fit: BoxFit.cover,
            ),
            const SizedBox(height: 16),
            Text(item.title, style: Theme.of(context).textTheme.headlineSmall),
            const SizedBox(height: 8),
            Text(item.description, style: Theme.of(context).textTheme.bodyMedium),
            const SizedBox(height: 16),
            Text('الفئة: ${_getCategoryTitle(item.category)}'),
            Text('الموقع: ${item.location}'),
            Text('مدة الاستعارة: ${item.borrowDurationDays} أيام'),
            Text('الحالة: ${item.statusText}'),
            if (item.rating != null)
              Text('التقييم: ${item.rating!.toStringAsFixed(1)} (${item.ratingCount} تقييم)'),
            const SizedBox(height: 16),
            if (user != null) ...[
              ListTile(
                leading: CircleAvatar(
                  backgroundImage: user!.avatarUrl != null
                      ? NetworkImage(user!.avatarUrl!)
                      : null,
                  child: user!.avatarUrl == null ? const Icon(Icons.person) : null,
                ),
                title: Text(user!.name),
                subtitle: Text('مستوى المشاركة: ${user!.userLevel}'),
              ),
              const SizedBox(height: 16),
            ],
            ElevatedButton(
              onPressed: item.isAvailable ? onBorrow : null,
              child: const Text('طلب استعارة'),
            ),
            if (borrowHistory.isNotEmpty) ...[
              const SizedBox(height: 16),
              Text('سجل الاستعارة:', style: Theme.of(context).textTheme.titleMedium),
              ...borrowHistory.map((borrow) => ListTile(
                title: Text(borrow['itemName'] ?? 'عنصر مجهول'),
                subtitle: Text(
                  'التاريخ: ${DateFormat.yMMMd('ar').format(DateTime.parse(borrow['borrowDate']))}',
                ),
                trailing: Text(borrow['status'] ?? 'معلق'),
              )),
            ],
          ],
        ),
      ),
    );
  }

  String _getCategoryTitle(String category) {
    // Placeholder mapping, should match categories defined earlier
    final categoryMap = {
      'tools': 'أدوات',
      'books': 'كتب',
      'electronics': 'إلكترونيات',
      'clothing': 'ملابس',
      'furniture': 'أثاث',
      'toys': 'ألعاب',
      'sports': 'معدات رياضية',
      'others': 'أخرى',
    };
    return categoryMap[category] ?? 'أخرى';
  }
}

class CreateShareItemScreen extends StatefulWidget {
  final ShareItem? item;
  final Function(String, String, String, String, File, List<String>, double, int) onSubmit;

  const CreateShareItemScreen({super.key, this.item, required this.onSubmit});

  @override
  State<CreateShareItemScreen> createState() => _CreateShareItemScreenState();
}

class _CreateShareItemScreenState extends State<CreateShareItemScreen> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _locationController = TextEditingController();
  String _selectedCategory = 'أدوات';
  File? _selectedFile;
  List<String> _tags = [];
  double _urgencyLevel = 0.0;
  int _borrowDurationDays = 7;

  @override
  void initState() {
    super.initState();
    if (widget.item != null) {
      _titleController.text = widget.item!.title;
      _descriptionController.text = widget.item!.description;
      _locationController.text = widget.item!.location;
      _selectedCategory = widget.item!.category;
      _tags = widget.item!.tags;
      _urgencyLevel = widget.item!.urgencyLevel;
      _borrowDurationDays = widget.item!.borrowDurationDays;
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _locationController.dispose();
    super.dispose();
  }

  Future<void> _pickFile() async {
    final result = await FilePicker.platform.pickFiles(type: FileType.image);
    if (result != null && result.files.single.path != null) {
      setState(() => _selectedFile = File(result.files.single.path!));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.item == null ? 'إنشاء عنصر جديد' : 'تعديل العنصر'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            children: [
              TextFormField(
                controller: _titleController,
                decoration: const InputDecoration(
                  labelText: 'العنوان',
                  border: OutlineInputBorder(),
                ),
                validator: (value) =>
                value!.isEmpty ? 'يرجى إدخال العنوان' : null,
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _descriptionController,
                decoration: const InputDecoration(
                  labelText: 'الوصف',
                  border: OutlineInputBorder(),
                ),
                maxLines: 4,
                validator: (value) =>
                value!.length < 30 ? 'الوصف يجب أن يكون 30 حرفًا على الأقل' : null,
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _locationController,
                decoration: const InputDecoration(
                  labelText: 'الموقع',
                  border: OutlineInputBorder(),
                ),
                validator: (value) =>
                value!.isEmpty ? 'يرجى إدخال الموقع' : null,
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                value: _selectedCategory,
                decoration: const InputDecoration(
                  labelText: 'الفئة',
                  border: OutlineInputBorder(),
                ),
                items: ShareItem.defaultCategories
                    .map((category) => DropdownMenuItem(
                  value: category,
                  child: Text(category),
                ))
                    .toList(),
                onChanged: (value) => setState(() => _selectedCategory = value!),
              ),
              const SizedBox(height: 16),
              TextFormField(
                decoration: const InputDecoration(
                  labelText: 'مدة الاستعارة (بالأيام)',
                  border: OutlineInputBorder(),
                ),
                initialValue: _borrowDurationDays.toString(),
                keyboardType: TextInputType.number,
                onChanged: (value) {
                  setState(() => _borrowDurationDays = int.tryParse(value) ?? 7);
                },
                validator: (value) =>
                value!.isEmpty || int.tryParse(value) == null
                    ? 'يرجى إدخال عدد صحيح' : null,
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: Text(
                      _selectedFile == null
                          ? 'لم يتم اختيار صورة'
                          : _selectedFile!.path.split('/').last,
                    ),
                  ),
                  ElevatedButton(
                    onPressed: _pickFile,
                    child: const Text('اختيار صورة'),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              TextField(
                decoration: const InputDecoration(
                  labelText: 'الوسوم (مفصولة بفواصل)',
                  border: OutlineInputBorder(),
                ),
                onChanged: (value) =>
                _tags = value.split(',').map((tag) => tag.trim()).toList(),
              ),
              const SizedBox(height: 16),
              Slider(
                value: _urgencyLevel,
                min: 0.0,
                max: 1.0,
                divisions: 10,
                label: 'مستوى الإلحاح: ${_urgencyLevel.toStringAsFixed(1)}',
                onChanged: (value) => setState(() => _urgencyLevel = value),
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: () {
                  if (_formKey.currentState!.validate() && _selectedFile != null) {
                    widget.onSubmit(
                      _titleController.text,
                      _descriptionController.text,
                      _locationController.text,
                      _selectedCategory,
                      _selectedFile!,
                      _tags,
                      _urgencyLevel,
                      _borrowDurationDays,
                    );
                    Navigator.pop(context);
                  } else {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('يرجى ملء جميع الحقول واختيار صورة')),
                    );
                  }
                },
                child: const Text('إرسال'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class NotificationSheet extends StatelessWidget {
  final List<AppNotification> notifications;
  final Function(AppNotification) onMarkAsRead;
  final Function(AppNotification) onAction;

  const NotificationSheet({
    super.key,
    required this.notifications,
    required this.onMarkAsRead,
    required this.onAction,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Text(
            'الإشعارات',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 16),
          Expanded(
            child: notifications.isEmpty
                ? const Center(child: Text('لا توجد إشعارات'))
                : ListView.builder(
              itemCount: notifications.length,
              itemBuilder: (context, index) {
                final notification = notifications[index];
                return ListTile(
                  leading: Icon(notification.icon, color: notification.color),
                  title: Text(notification.title),
                  subtitle: Text(notification.message),
                  trailing: !notification.isRead
                      ? IconButton(
                    icon: const Icon(Icons.mark_chat_read),
                    onPressed: () => onMarkAsRead(notification),
                  )
                      : null,
                  onTap: () => onAction(notification),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}

class AdvancedFiltersSheet extends StatefulWidget {
  final String selectedFilter;
  final Function(String) onApply;

  const AdvancedFiltersSheet({
    super.key,
    required this.selectedFilter,
    required this.onApply,
  });

  @override
  State<AdvancedFiltersSheet> createState() => _AdvancedFiltersSheetState();
}

class _AdvancedFiltersSheetState extends State<AdvancedFiltersSheet> {
  late String _selectedFilter;

  @override
  void initState() {
    super.initState();
    _selectedFilter = widget.selectedFilter;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            'الفلاتر المتقدمة',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 16),
          DropdownButtonFormField<String>(
            value: _selectedFilter,
            decoration: const InputDecoration(
              labelText: 'نوع الفلتر',
              border: OutlineInputBorder(),
            ),
            items: const [
              DropdownMenuItem(value: 'all', child: Text('الكل')),
              DropdownMenuItem(value: 'available', child: Text('متوفر')),
              DropdownMenuItem(value: 'urgent', child: Text('عاجل')),
              DropdownMenuItem(value: 'favorites', child: Text('المفضلة')),
            ],
            onChanged: (value) => setState(() => _selectedFilter = value!),
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () {
              widget.onApply(_selectedFilter);
              Navigator.pop(context);
            },
            child: const Text('تطبيق'),
          ),
        ],
      ),
    );
  }
}

class BorrowHistoryScreen extends StatelessWidget {
  final String userId;
  final ShareItemService itemService;

  const BorrowHistoryScreen({
    super.key,
    required this.userId,
    required this.itemService,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('سجل الاستعارة')),
      body: FutureBuilder<List<Borrow>>(
        future: itemService.fetchBorrowHistory(userId).then(
              (history) => history.map((h) => Borrow.fromJson(h)).toList(),
        ),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }
          if (snapshot.hasError) {
            return Center(child: Text('خطأ: ${snapshot.error}'));
          }
          final borrows = snapshot.data ?? [];
          return borrows.isEmpty
              ? const Center(child: Text('لا يوجد سجل استعارة'))
              : ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: borrows.length,
            itemBuilder: (context, index) {
              final borrow = borrows[index];
              return ListTile(
                title: Text(borrow.itemName),
                subtitle: Text(
                  'تاريخ الاستعارة: ${DateFormat.yMMMd('ar').format(borrow.borrowDate)}',
                ),
                trailing: Text(borrow.status),
              );
            },
          );
        },
      ),
    );
  }
}

class SettingsDialog extends StatefulWidget {
  final Function(bool) onPrivacyChanged;

  const SettingsDialog({super.key, required this.onPrivacyChanged});

  @override
  State<SettingsDialog> createState() => _SettingsDialogState();
}

class _SettingsDialogState extends State<SettingsDialog> {
  bool _showHistory = true;

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('الإعدادات'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SwitchListTile(
            title: const Text('إظهار سجل الاستعارة'),
            value: _showHistory,
            onChanged: (value) {
              setState(() => _showHistory = value);
              widget.onPrivacyChanged(value);
            },
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('إغلاق'),
        ),
      ],
    );
  }
}

class RatingsScreen extends StatelessWidget {
  final String itemId;
  final double? currentRating;
  final int ratingCount;
  final Function(double) onRate;

  const RatingsScreen({
    super.key,
    required this.itemId,
    this.currentRating,
    required this.ratingCount,
    required this.onRate,
  });

  @override
  Widget build(BuildContext context) {
    double newRating = currentRating ?? 0.0;
    return Scaffold(
      appBar: AppBar(title: const Text('تقييم العنصر')),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Text(
              'التقييم الحالي: ${currentRating?.toStringAsFixed(1) ?? 'لا يوجد'} ($ratingCount تقييم)',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            Slider(
              value: newRating,
              min: 0.0,
              max: 5.0,
              divisions: 10,
              label: newRating.toStringAsFixed(1),
              onChanged: (value) => newRating = value,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () {
                onRate(newRating);
                Navigator.pop(context);
              },
              child: const Text('إرسال التقييم'),
            ),
          ],
        ),
      ),
    );
  }
}

