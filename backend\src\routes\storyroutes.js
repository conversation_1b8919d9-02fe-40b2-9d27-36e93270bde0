const express = require('express');
const router = express.Router();
const {
  getStories,
  createStory,
  getStory,
  deleteStory,
  getUserStories
} = require('../controllers/storycontroller');
const { protect } = require('../middleware/authmiddleware');

// مسارات القصص
router.route('/')
  .get(protect, getStories)
  .post(protect, createStory);

router.route('/:id')
  .get(protect, getStory)
  .delete(protect, deleteStory);

router.get('/user/:userId', protect, getUserStories);

module.exports = router;
