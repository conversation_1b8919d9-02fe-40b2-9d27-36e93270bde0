import 'dart:async';
import 'dart:io';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/material.dart';
import 'package:untitled10/main.dart' show MyApp;
import 'package:google_fonts/google_fonts.dart';
import 'package:geolocator/geolocator.dart';
import 'package:http/http.dart' as http;
import 'package:provider/provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:uuid/uuid.dart';
import 'package:video_player/video_player.dart';
import 'package:youtube_player_flutter/youtube_player_flutter.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:animate_do/animate_do.dart';
import 'package:timezone/data/latest.dart' as tz;
import 'package:timezone/timezone.dart' as tz;
import 'package:socket_io_client/socket_io_client.dart' as io;
import 'package:shimmer/shimmer.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter_offline/flutter_offline.dart';
import 'package:percent_indicator/percent_indicator.dart';
import 'package:table_calendar/table_calendar.dart';
import 'appstate.dart';
import 'models/group_task_model.dart';
import 'models/notification_model.dart';
import 'models/simple_task_model.dart';
import 'models/task_group_model.dart';

// Constants for theme and colors
const Color primaryColor = Color(0xFF4A90E2);
const Color secondaryColor = Color(0xFF3498DB);
const Color darkPrimaryColor = Color(0xFF2C3E50);
const Color darkSecondaryColor = Color(0xFF34495E);

// Error messages
const String networkError = 'Network error occurred';
const String loadingError = 'Error loading data';
const String unknownError = 'An unexpected error occurred';

// Connectivity checker
Future<bool> checkConnectivity() async {
  var connectivityResult = await Connectivity().checkConnectivity();
  return connectivityResult != ConnectivityResult.none;
}

// Error handler
Future<T> handleError<T>(Future<T> future) async {
  try {
    return await future;
  } catch (e) {
    if (e is SocketException) {
      throw networkError;
    } else if (e is TimeoutException) {
      throw loadingError;
    } else {
      throw unknownError;
    }
  }
}

// Global ScaffoldMessengerKey for SnackBars
final GlobalKey<ScaffoldMessengerState> scaffoldMessengerKey =
    GlobalKey<ScaffoldMessengerState>();

// Notification Plugin Initialization
final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
    FlutterLocalNotificationsPlugin();

class Grop extends StatelessWidget {
  const Grop({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<AppState>(
      builder: (context, appState, child) =>
          appState.isLoggedIn ? const TasksHomePage() : const LoginPage(),
    );
  }
}

class TasksHomePage extends StatefulWidget {
  const TasksHomePage({super.key});

  @override
  _TasksHomePageState createState() => _TasksHomePageState();
}

class _TasksHomePageState extends State<TasksHomePage>
    with SingleTickerProviderStateMixin {
  Position? currentPosition;
  String? selectedTypeFilter;
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  late TabController _tabController;
  bool _isFabMenuOpen = false;

  final List<Map<String, String>> ads = [
    {
      'image': 'https://via.placeholder.com/300x100.png?text=إعلان+1',
      'title': 'إعلان 1: عرض خاص اليوم!'
    },
    {
      'image': 'https://via.placeholder.com/300x100.png?text=إعلان+2',
      'title': 'إعلان 2: خصم 20% على الاشتراك'
    },
    {
      'image': 'https://via.placeholder.com/300x100.png?text=إعلان+3',
      'title': 'إعلان 3: جرب خدمتنا مجانًا'
    },
  ];

  late PageController _pageController;
  int _currentPage = 0;

  @override
  void initState() {
    super.initState();
    _tabController =
        TabController(length: 4, vsync: this); // Added Leaderboard tab
    _pageController = PageController(viewportFraction: 0.9);
    _initializeData();
    _scrollController.addListener(() {
      if (_scrollController.position.pixels >=
          _scrollController.position.maxScrollExtent - 50) {
        Provider.of<AppState>(context, listen: false)
            .fetchGroups(loadMore: true);
      }
    });
    Timer.periodic(const Duration(seconds: 3), (Timer timer) {
      if (!mounted) {
        timer.cancel();
        return;
      }
      if (_currentPage < ads.length - 1) {
        _currentPage++;
      } else {
        _currentPage = 0;
      }
      _pageController.animateToPage(
        _currentPage,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _pageController.dispose();
    _scrollController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _initializeData() async {
    tz.initializeTimeZones();
    const androidInit = AndroidInitializationSettings('@mipmap/ic_launcher');
    const initSettings = InitializationSettings(android: androidInit);
    await flutterLocalNotificationsPlugin.initialize(initSettings);
    await _getCurrentLocation();
    await Provider.of<AppState>(context, listen: false).fetchGroups();
    _scheduleTaskReminders();
  }

  Future<void> _getCurrentLocation() async {
    try {
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        _showSnackBar('الرجاء تفعيل خدمة الموقع', Colors.red);
        return;
      }
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission != LocationPermission.whileInUse &&
            permission != LocationPermission.always) {
          _showSnackBar('تم رفض إذن الموقع', Colors.red);
          return;
        }
      }
      final position = await Geolocator.getCurrentPosition(
          desiredAccuracy: LocationAccuracy.high);
      if (mounted) {
        setState(() => currentPosition = position);
      }
    } catch (e) {
      _showSnackBar('فشل في تحديث الموقع: $e', Colors.red);
    }
  }

  void _showSnackBar(String message, [Color? backgroundColor]) {
    scaffoldMessengerKey.currentState?.showSnackBar(
      SnackBar(
        content: Text(message, style: const TextStyle(fontFamily: 'Tajawal')),
        backgroundColor: backgroundColor ?? Colors.teal[700],
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
    );
  }

  void _shareGroup(TaskGroup group) {
    Share.share(
      'انضم إلى مجموعتي "${group.name}" في Grop! نوع المجموعة: ${group.type.label}\nالوصف: ${group.description}',
      subject: 'دعوة للانضمام إلى مجموعة',
    );
  }

  void _shareTask(GroupTask task, String groupId) {
    Share.share(
      'لدي مهمة جديدة في Grop: ${task.title} (${task.category})\nالمكافأة: ${task.points}',
      subject: 'مشاركة مهمة',
    );
  }

  Future<void> _shareTaskCompletion(GroupTask task, String groupId) async {
    if (kIsWeb) {
      _showSnackBar('رفع الملفات غير مدعوم على الويب', Colors.red);
      return;
    }
    final appState = Provider.of<AppState>(context, listen: false);
    FilePickerResult? result =
        await FilePicker.platform.pickFiles(type: FileType.media);
    if (result != null && result.files.single.path != null) {
      final filePath = result.files.single.path!;
      await appState.completeTask(groupId, task, filePath);
      await Share.shareXFiles(
        [XFile(filePath)],
        text: 'أكملت مهمتي "${task.title}" في Grop! ${task.points}',
        subject: 'إتمام مهمة',
      );
      _showSnackBar('تم إكمال المهمة ومشاركتها بنجاح', Colors.green);
    }
  }

  Future<void> _collaborateOnTask(GroupTask task, String groupId) async {
    final appState = Provider.of<AppState>(context, listen: false);
    await appState.contributeToTask(groupId, task, appState.currentUser!.id);
    _showSnackBar('تمت المساهمة في المهمة بنجاح', Colors.green);
    await flutterLocalNotificationsPlugin.zonedSchedule(
      task.id.hashCode,
      'مساهمة في مهمة',
      'لقد ساهمت في "${task.title}" في مجموعة "${appState.groups.firstWhere((g) => g.id == groupId).name}"',
      tz.TZDateTime.now(tz.local).add(const Duration(seconds: 5)),
      const NotificationDetails(
        android: AndroidNotificationDetails(
          'collaboration_channel',
          'Collaboration Notifications',
          importance: Importance.high,
          priority: Priority.high,
        ),
      ),
      androidAllowWhileIdle: true,
      uiLocalNotificationDateInterpretation:
          UILocalNotificationDateInterpretation.absoluteTime,
    );
  }

  void _scheduleTaskReminders() {
    final appState = Provider.of<AppState>(context, listen: false);
    for (var group in appState.groups) {
      for (var tasks in group.tasksByUser.values) {
        for (var task in tasks) {
          if (task.dueDate != null && !task.isCompleted) {
            final dueTime = tz.TZDateTime.from(task.dueDate!, tz.local);
            if (dueTime.isAfter(tz.TZDateTime.now(tz.local))) {
              flutterLocalNotificationsPlugin.zonedSchedule(
                task.id.hashCode,
                'تذكير بمهمة',
                'مهمة "${task.title}" تستحق قريبًا!',
                dueTime.subtract(const Duration(hours: 1)),
                const NotificationDetails(
                  android: AndroidNotificationDetails(
                    'reminder_channel',
                    'Task Reminders',
                    importance: Importance.high,
                    priority: Priority.high,
                  ),
                ),
                androidAllowWhileIdle: true,
                uiLocalNotificationDateInterpretation:
                    UILocalNotificationDateInterpretation.absoluteTime,
              );
            }
          }
        }
      }
    }
  }

  List<TaskGroup> _filteredGroups(List<TaskGroup> groups) {
    var filtered = groups;
    if (selectedTypeFilter != null) {
      filtered =
          filtered.where((g) => g.type.label == selectedTypeFilter).toList();
    }
    if (_searchController.text.isNotEmpty) {
      filtered = filtered
          .where((g) => g.name
              .toLowerCase()
              .contains(_searchController.text.toLowerCase()))
          .toList();
    }
    return filtered..sort((a, b) => b.points.compareTo(a.points));
  }

  Widget _buildFabMenu() {
    return Stack(
      alignment: Alignment.bottomRight,
      children: [
        AnimatedOpacity(
          opacity: _isFabMenuOpen ? 1.0 : 0.0,
          duration: const Duration(milliseconds: 200),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              _buildFabMenuItem(
                icon: Icons.add,
                label: 'إضافة مجموعة',
                onPressed: () => _addNewGroup(context),
              ),
              const SizedBox(height: 10),
              _buildFabMenuItem(
                icon: Icons.task,
                label: 'إضافة مهمة',
                onPressed: () => _addTaskDialog(
                    context,
                    Provider.of<AppState>(context, listen: false)
                            .groups
                            .isNotEmpty
                        ? Provider.of<AppState>(context, listen: false)
                            .groups[0]
                        : TaskGroup(
                            id: 'default',
                            name: 'مجموعة افتراضية',
                            description: 'مجموعة للاختبار',
                            videoUrl: '',
                            members: [],
                            type: GroupType.other,
                          )),
              ),
              const SizedBox(height: 10),
              _buildFabMenuItem(
                icon: Icons.schedule,
                label: 'جدول اليوم',
                onPressed: () => _showDailySchedule(context),
              ),
              const SizedBox(height: 10),
            ],
          ),
        ),
        FloatingActionButton(
          onPressed: () {
            setState(() {
              _isFabMenuOpen = !_isFabMenuOpen;
            });
          },
          backgroundColor: Colors.teal[700],
          tooltip: 'الإجراءات',
          elevation: 6,
          child: Icon(_isFabMenuOpen ? Icons.close : Icons.add,
              color: Colors.white),
        ),
      ],
    );
  }

  Widget _buildFabMenuItem({
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
  }) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [Colors.teal[600]!, Colors.teal[400]!],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              label,
              style: const TextStyle(
                color: Colors.white,
                fontFamily: 'Tajawal',
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(width: 8),
            Icon(icon, color: Colors.white, size: 20),
          ],
        ),
      ),
    );
  }

  void _showDailySchedule(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => DailyScheduleDialog(),
    );
  }

  @override
  Widget build(BuildContext context) {
    final appState = Provider.of<AppState>(context);
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final theme = MyApp.themes[isDarkMode ? 'Dark' : 'Default']?['themeData'] as ThemeData? ?? Theme.of(context);
    final textTheme = GoogleFonts.tajawalTextTheme(theme.textTheme);

    return Theme(
      data: theme.copyWith(
        textTheme: textTheme,
        colorScheme: theme.colorScheme.copyWith(
          primary: theme.primaryColor,
          secondary: theme.colorScheme.secondary,
        ),
      ),
      child: ScaffoldMessenger(
        key: scaffoldMessengerKey,
        child: Scaffold(
          body: Column(
            children: [
              Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [theme.primaryColor, theme.primaryColorLight],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                ),
                child: SafeArea(
                  child: TabBar(
                    controller: _tabController,
                    tabs: const [
                      Tab(icon: Icon(Icons.group), text: 'المجموعات'),
                      Tab(icon: Icon(Icons.chat), text: 'الدردشة'),
                      Tab(icon: Icon(Icons.notifications), text: 'الإشعارات'),
                      Tab(icon: Icon(Icons.leaderboard), text: 'لوحة الصدارة'),
                    ],
                    labelColor: Colors.white,
                    unselectedLabelColor: Colors.white70,
                    indicatorColor: theme.colorScheme.secondary,
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    labelStyle: const TextStyle(
                      fontFamily: 'Tajawal',
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
              Expanded(
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    // Tab 1: Groups
                    Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [theme.cardColor, theme.scaffoldBackgroundColor],
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                        ),
                      ),
                      child: Stack(
                        children: [
                          Column(
                            children: [
                              Container(
                                padding: const EdgeInsets.only(top: 16.0),
                                height: 120.0,
                                child: PageView.builder(
                                  itemCount: ads.length,
                                  controller: _pageController,
                                  itemBuilder: (context, index) {
                                    final ad = ads[index];
                                    return ElasticIn(
                                      child: GestureDetector(
                                        onTap: () => _showSnackBar(
                                            ad['title']!, theme.primaryColor),
                                        child: Container(
                                          margin: const EdgeInsets.symmetric(
                                              horizontal: 5.0),
                                          decoration: BoxDecoration(
                                            borderRadius:
                                                BorderRadius.circular(12),
                                            boxShadow: [
                                              BoxShadow(
                                                color:
                                                    theme.shadowColor.withOpacity(0.1),
                                                blurRadius: 8,
                                                offset: const Offset(0, 4),
                                              ),
                                            ],
                                            image: DecorationImage(
                                              image: CachedNetworkImageProvider(ad['image']!),
                                              fit: BoxFit.cover,
                                            ),
                                          ),
                                          child: ClipRRect(
                                            borderRadius:
                                                BorderRadius.circular(12),
                                            child: Stack(
                                              children: [
                                                Container(
                                                  color: Colors.transparent,
                                                ),
                                                Positioned(
                                                  bottom: 8,
                                                  left: 8,
                                                  right: 8,
                                                child: Container(
                                                  padding:
                                                      const EdgeInsets.all(6),
                                                  decoration: BoxDecoration(
                                                    color: Colors.black
                                                        .withOpacity(0.7),
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            8),
                                                  ),
                                                  child: Text(
                                                    ad['title']!,
                                                    style: const TextStyle(
                                                      color: Colors.white,
                                                      fontSize: 14,
                                                      fontFamily: 'Tajawal',
                                                    ),
                                                    textAlign: TextAlign.center,
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ),
                                  );
                                },
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.all(16.0),
                              child: ZoomIn(
                                child: TextField(
                                  controller: _searchController,
                                  decoration: InputDecoration(
                                    hintText: 'ابحث عن مجموعة...',
                                    prefixIcon: const Icon(Icons.search,
                                        color: Colors.teal),
                                    filled: true,
                                    fillColor: Colors.white,
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(30),
                                      borderSide: BorderSide.none,
                                    ),
                                    contentPadding:
                                        const EdgeInsets.symmetric(vertical: 0),
                                    hintStyle:
                                        const TextStyle(fontFamily: 'Tajawal'),
                                  ),
                                  onChanged: (value) => setState(() {}),
                                ),
                              ),
                            ),
                            Expanded(
                              child: appState.groups.isEmpty &&
                                      !appState.isLoadingMore
                                  ? Center(
                                      child: Text(
                                        'لا توجد مجموعات بعد',
                                        style: TextStyle(
                                          fontSize: 18,
                                          fontFamily: 'Tajawal',
                                          color: Colors.grey[600],
                                        ),
                                      ),
                                    )
                                  : ListView.builder(
                                      controller: _scrollController,
                                      padding:
                                          const EdgeInsets.only(bottom: 16),
                                      cacheExtent: 1000,
                                      itemCount:
                                          _filteredGroups(appState.groups)
                                                  .length +
                                              (appState.isLoadingMore ? 1 : 0),
                                      itemBuilder: (context, index) {
                                        if (index ==
                                            _filteredGroups(appState.groups)
                                                .length) {
                                          return Shimmer.fromColors(
                                            baseColor: Colors.grey[300]!,
                                            highlightColor: Colors.grey[100]!,
                                            child: Container(
                                              margin:
                                                  const EdgeInsets.symmetric(
                                                      horizontal: 16,
                                                      vertical: 8),
                                              height: 100,
                                              decoration: BoxDecoration(
                                                color: Colors.white,
                                                borderRadius:
                                                    BorderRadius.circular(16),
                                              ),
                                            ),
                                          );
                                        }
                                        final group = _filteredGroups(
                                            appState.groups)[index];
                                        return FadeInUp(
                                          duration:
                                              const Duration(milliseconds: 300),
                                          child: _buildGroupCard(group,
                                              appState.currentUser?.id ?? ''),
                                        );
                                      },
                                    ),
                            ),
                          ],
                        ),
                        Positioned(
                          bottom: 16,
                          right: 16,
                          child: _buildFabMenu(),
                        ),
                      ],
                    ),
                  ),
                  // Tab 2: Chat
                  TaskChatPage(
                    chatType: 'كتابية',
                    group: appState.groups.isNotEmpty
                        ? appState.groups[0]
                        : TaskGroup(
                            id: 'default',
                            name: 'مجموعة افتراضية',
                            description: 'مجموعة للاختبار',
                            videoUrl: '',
                            members: [],
                            type: GroupType.other,
                          ),
                    baseUrl: appState.baseUrl,
                    authToken: appState.authToken ?? '',
                  ),
                  // Tab 3: Notifications
                  const NotificationsPage(),
                  // Tab 4: Leaderboard
                  const LeaderboardPage(),
                ],
              ),
            ),
          ],
        ),
      ),)
    );
  }

  Widget _buildGroupCard(TaskGroup group, String currentUserId) {
    final appState = Provider.of<AppState>(context);
    final isMember = group.members.any((member) => member.id == currentUserId);
    final userTasks = group.tasksByUser[currentUserId] ?? [];

    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            group.type.color.withOpacity(0.9),
            group.type.color.withOpacity(0.5)
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: group.type.color.withOpacity(0.2),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: ExpansionTile(
        leading: CircleAvatar(
          backgroundColor: Colors.white,
          child: Icon(group.type.icon, color: group.type.color),
        ),
        title: Row(
          children: [
            Expanded(
              child: Text(
                group.name,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                  fontSize: 18,
                  fontFamily: 'Tajawal',
                ),
              ),
            ),
            Chip(
              label: Text('${group.points} نقطة',
                  style: const TextStyle(
                      color: Colors.white, fontFamily: 'Tajawal')),
              backgroundColor: Colors.black26,
              padding: const EdgeInsets.symmetric(horizontal: 8),
            ),
          ],
        ),
        subtitle: Text(
          "${group.type.label} • ${group.members.length} أعضاء",
          style: const TextStyle(color: Colors.white70, fontFamily: 'Tajawal'),
        ),
        tilePadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "الوصف: ${group.description}",
                  style: const TextStyle(
                      color: Colors.white, fontFamily: 'Tajawal'),
                ),
                const SizedBox(height: 10),
                if (group.videoUrl.isNotEmpty &&
                    YoutubePlayer.convertUrlToId(group.videoUrl) != null)
                  ClipRRect(
                    borderRadius: BorderRadius.circular(12),
                    child: YoutubePlayer(
                      controller: YoutubePlayerController(
                        initialVideoId:
                            YoutubePlayer.convertUrlToId(group.videoUrl)!,
                        flags: const YoutubePlayerFlags(
                            autoPlay: false, mute: false),
                      ),
                      showVideoProgressIndicator: true,
                    ),
                  ),
                const SizedBox(height: 10),
                if (group.media.isNotEmpty)
                  SizedBox(
                    height: 120,
                    child: ListView.builder(
                      scrollDirection: Axis.horizontal,
                      itemCount: group.media.length,
                      itemBuilder: (context, index) {
                        final media = group.media[index];
                        final mediaUrl = media['path']!.startsWith('http')
                            ? media['path']!
                            : '${appState.baseUrl}${media['path']}';
                        return Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: ZoomIn(
                            child: media['type'] == 'image'
                                ? CachedNetworkImage(
                                    imageUrl: mediaUrl,
                                    width: 100,
                                    height: 100,
                                    fit: BoxFit.cover,
                                    placeholder: (context, url) =>
                                        Shimmer.fromColors(
                                      baseColor: Colors.grey[300]!,
                                      highlightColor: Colors.grey[100]!,
                                      child: Container(
                                        color: Colors.white,
                                      ),
                                    ),
                                    errorWidget: (context, url, error) =>
                                        const Icon(Icons.error,
                                            color: Colors.red),
                                  )
                                : GestureDetector(
                                    onTap: () =>
                                        _showVideoDialog(context, mediaUrl),
                                    child: Stack(
                                      children: [
                                        CachedNetworkImage(
                                          imageUrl: mediaUrl,
                                          width: 100,
                                          height: 100,
                                          fit: BoxFit.cover,
                                          placeholder: (context, url) =>
                                              Shimmer.fromColors(
                                            baseColor: Colors.grey[300]!,
                                            highlightColor: Colors.grey[100]!,
                                            child: Container(
                                              color: Colors.white,
                                            ),
                                          ),
                                          errorWidget: (context, url, error) =>
                                              const Icon(Icons.error),
                                        ),
                                        const Positioned.fill(
                                          child: Icon(Icons.play_circle_fill,
                                              color: Colors.white, size: 50),
                                        ),
                                      ],
                                    ),
                                  ),
                          ),
                        );
                      },
                    ),
                  ),
                const SizedBox(height: 10),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildChatButton(
                        "كتابية", Icons.chat, Colors.teal[800]!, group),
                    _buildChatButton(
                        "صوتية", Icons.mic, Colors.blue[800]!, group),
                    _buildChatButton(
                        "فيديو", Icons.videocam, Colors.red[800]!, group),
                  ],
                ),
                const SizedBox(height: 10),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildGradientButton(
                      text: isMember ? 'عضو بالفعل' : 'انضمام',
                      onPressed: isMember
                          ? null
                          : () async {
                              try {
                                await appState.joinGroup(group.id);
                                _showSnackBar(
                                    'تم الانضمام بنجاح', Colors.green);
                              } catch (e) {
                                _showSnackBar(
                                    'فشل في الانضمام: $e', Colors.red);
                              }
                            },
                      disabled: isMember,
                      gradient: LinearGradient(
                        colors: isMember
                            ? [Colors.grey[400]!, Colors.grey[400]!]
                            : [Colors.teal[700]!, Colors.teal[500]!],
                      ),
                    ),
                    _buildGradientButton(
                      text: 'مغادرة',
                      onPressed: isMember
                          ? () async {
                              try {
                                await appState.leaveGroup(group.id);
                                _showSnackBar(
                                    'تم المغادرة بنجاح', Colors.green);
                              } catch (e) {
                                _showSnackBar(
                                    'فشل في المغادرة: $e', Colors.red);
                              }
                            }
                          : null,
                      disabled: !isMember,
                      gradient: LinearGradient(
                        colors: !isMember
                            ? [Colors.grey[400]!, Colors.grey[400]!]
                            : [Colors.orange[700]!, Colors.orange[500]!],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 10),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    TextButton.icon(
                      onPressed: () => _editGroup(context, group),
                      icon: const Icon(Icons.edit, color: Colors.white),
                      label: const Text('تعديل',
                          style: TextStyle(
                              color: Colors.white, fontFamily: 'Tajawal')),
                    ),
                    TextButton.icon(
                      onPressed: () => _deleteGroup(context, group.id),
                      icon: const Icon(Icons.delete, color: Colors.red),
                      label: const Text('حذف',
                          style: TextStyle(
                              color: Colors.red, fontFamily: 'Tajawal')),
                    ),
                    TextButton.icon(
                      onPressed: () => _shareGroup(group),
                      icon: const Icon(Icons.share, color: Colors.white),
                      label: const Text('مشاركة',
                          style: TextStyle(
                              color: Colors.white, fontFamily: 'Tajawal')),
                    ),
                  ],
                ),
                const Divider(color: Colors.white54),
                const Text(
                  'مهامي',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    fontFamily: 'Tajawal',
                  ),
                ),
                const SizedBox(height: 10),
                _buildGradientButton(
                  text: 'إضافة مهمة',
                  onPressed: () => _addTaskDialog(context, group),
                  icon: Icons.add,
                  gradient: LinearGradient(
                    colors: [Colors.teal[600]!, Colors.teal[400]!],
                  ),
                ),
                const SizedBox(height: 10),
                if (userTasks.isEmpty)
                  const Text(
                    'لا توجد مهام بعد',
                    style:
                        TextStyle(color: Colors.white70, fontFamily: 'Tajawal'),
                  )
                else
                  ListView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: userTasks.length,
                    itemBuilder: (context, index) {
                      final task = userTasks[index];
                      return Card(
                        color:
                            task.isCompleted ? Colors.green[100] : Colors.white,
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12)),
                        elevation: 2,
                        child: ListTile(
                          leading: Checkbox(
                            value: task.isCompleted,
                            onChanged: task.isCompleted
                                ? null
                                : (value) =>
                                    _shareTaskCompletion(task, group.id),
                          ),
                          title: Text(
                            task.title,
                            style: TextStyle(
                              decoration: task.isCompleted
                                  ? TextDecoration.lineThrough
                                  : null,
                              fontFamily: 'Tajawal',
                            ),
                          ),
                          subtitle: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                '${task.category} • ${task.points} • ${task.startTime.format(context)}',
                                style: const TextStyle(fontFamily: 'Tajawal'),
                              ),
                              if (task.collaborators.isNotEmpty)
                                Text(
                                  'المساهمون: ${task.collaborators.length}',
                                  style: const TextStyle(
                                      fontFamily: 'Tajawal',
                                      color: Colors.blue),
                                ),
                            ],
                          ),
                          trailing: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              IconButton(
                                icon:
                                    const Icon(Icons.share, color: Colors.teal),
                                onPressed: () => _shareTask(task, group.id),
                              ),
                              if (!task.isCompleted)
                                IconButton(
                                  icon: const Icon(Icons.group_add,
                                      color: Colors.purple),
                                  onPressed: () =>
                                      _collaborateOnTask(task, group.id),
                                  tooltip: 'المساهمة في المهمة',
                                ),
                              if (task.isCompleted &&
                                  task.completionMediaPath != null)
                                IconButton(
                                  icon: const Icon(Icons.visibility,
                                      color: Colors.blue),
                                  onPressed: () => _showCompletionMedia(
                                      context, task.completionMediaPath!),
                                ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGradientButton({
    required String text,
    required VoidCallback? onPressed,
    bool disabled = false,
    required LinearGradient gradient,
    IconData? icon,
  }) {
    return InkWell(
      onTap: disabled ? null : onPressed,
      borderRadius: BorderRadius.circular(20),
      child: Container(
        decoration: BoxDecoration(
          gradient: gradient,
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (icon != null) ...[
              Icon(icon, color: Colors.white, size: 18),
              const SizedBox(width: 8),
            ],
            Text(
              text,
              style: const TextStyle(
                color: Colors.white,
                fontFamily: 'Tajawal',
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showVideoDialog(BuildContext context, String videoUrl) {
    showDialog(
        context: context,
        builder: (context) => VideoDialog(videoUrl: videoUrl));
  }

  void _showCompletionMedia(BuildContext context, String mediaPath) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: mediaPath.endsWith('.mp4')
            ? VideoDialog(videoUrl: mediaPath)
            : CachedNetworkImage(
                imageUrl: mediaPath,
                fit: BoxFit.cover,
                width: 300,
                height: 300,
                errorWidget: (context, url, error) =>
                    const Icon(Icons.error, color: Colors.red),
              ),
      ),
    );
  }

  Widget _buildChatButton(
      String label, IconData icon, Color color, TaskGroup group) {
    final appState = Provider.of<AppState>(context);
    return _buildGradientButton(
      text: label,
      onPressed: () => Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => TaskChatPage(
            chatType: label,
            group: group,
            baseUrl: appState.baseUrl,
            authToken: appState.authToken ?? '',
          ),
        ),
      ),
      gradient: LinearGradient(colors: [color, color.withOpacity(0.7)]),
      icon: icon,
    );
  }

  void _addNewGroup(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AddGroupDialog(
        onGroupAdded: (group) async {
          try {
            await Provider.of<AppState>(context, listen: false).addGroup(group);
            _showSnackBar('تم إضافة المجموعة بنجاح', Colors.green);
          } catch (e) {
            _showSnackBar('فشل في إضافة المجموعة: $e', Colors.red);
          }
        },
      ),
    );
  }

  void _editGroup(BuildContext context, TaskGroup group) {
    showDialog(
      context: context,
      builder: (context) => AddGroupDialog(
        initialGroup: group,
        onGroupAdded: (updatedGroup) async {
          try {
            await Provider.of<AppState>(context, listen: false)
                .addGroup(updatedGroup);
            _showSnackBar('تم تعديل المجموعة بنجاح', Colors.green);
          } catch (e) {
            _showSnackBar('فشل في تعديل المجموعة: $e', Colors.red);
          }
        },
      ),
    );
  }

  void _addTaskDialog(BuildContext context, TaskGroup group) {
    showDialog(
      context: context,
      builder: (context) => AddTaskDialog(
        group: group,
        onTaskAdded: (task) {
          Provider.of<AppState>(context, listen: false)
              .addTask(group.id, task.title);
          _showSnackBar('تم إضافة المهمة بنجاح', Colors.green);
        },
      ),
    );
  }

  Future<void> _deleteGroup(BuildContext context, String groupId) async {
    final appState = Provider.of<AppState>(context, listen: false);
    try {
      final response = await http.delete(
        Uri.parse('${appState.baseUrl}/api/groups/$groupId'),
        headers: {'x-auth-token': appState.authToken ?? ''},
      );
      if (response.statusCode == 200) {
        await appState.fetchGroups();
        _showSnackBar('تم حذف المجموعة بنجاح', Colors.green);
      } else {
        _showSnackBar('فشل في الحذف: ${response.body}', Colors.red);
      }
    } catch (e) {
      _showSnackBar('خطأ في الاتصال: $e', Colors.red);
    }
  }
}

class DailyScheduleDialog extends StatelessWidget {
  final List<Map<String, dynamic>> dailyTasks = [
    {
      'time': '06:00 ص',
      'task': 'الاستيقاظ وصلاة الفجر',
      'category': 'روتين يومي',
      'demographic': 'الجميع'
    },
    {
      'time': '07:00 ص',
      'task': 'الإفطار العائلي',
      'category': 'تغذية',
      'demographic': 'الجميع'
    },
    {
      'time': '08:00 ص',
      'task': 'الذهاب إلى المدرسة',
      'category': 'تعليم',
      'demographic': 'الأطفال'
    },
    {
      'time': '09:00 ص',
      'task': 'العمل أو الأعمال المنزلية',
      'category': 'عمل',
      'demographic': 'الرجال والنساء'
    },
    {
      'time': '12:00 م',
      'task': 'صلاة الظهر والغداء',
      'category': 'روتين يومي',
      'demographic': 'الجميع'
    },
    {
      'time': '03:00 م',
      'task': 'أنشطة ما بعد المدرسة',
      'category': 'ترفيه',
      'demographic': 'الأطفال'
    },
    {
      'time': '06:00 م',
      'task': 'صلاة المغرب ووقت العائلة',
      'category': 'روتين يومي',
      'demographic': 'الجميع'
    },
    {
      'time': '08:00 م',
      'task': 'الواجبات المنزلية أو الدراسة',
      'category': 'تعليم',
      'demographic': 'الأطفال'
    },
    {
      'time': '10:00 م',
      'task': 'النوم',
      'category': 'روتين يومي',
      'demographic': 'الجميع'
    },
  ];

  DailyScheduleDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      title: const Text(
        'جدول اليوم',
        style: TextStyle(
          color: Colors.teal,
          fontWeight: FontWeight.bold,
          fontFamily: 'Tajawal',
        ),
      ),
      content: SizedBox(
        width: double.maxFinite,
        child: ListView.builder(
          shrinkWrap: true,
          itemCount: dailyTasks.length,
          itemBuilder: (context, index) {
            final task = dailyTasks[index];
            return Card(
              margin: const EdgeInsets.symmetric(vertical: 4),
              child: ListTile(
                leading: CircleAvatar(
                  backgroundColor: Colors.teal[100],
                  child: Text(
                    task['time']!.substring(0, 5),
                    style: const TextStyle(fontSize: 12, fontFamily: 'Tajawal'),
                  ),
                ),
                title: Text(
                  task['task']!,
                  style: const TextStyle(fontFamily: 'Tajawal'),
                ),
                subtitle: Text(
                  '${task['category']} • ${task['demographic']}',
                  style: const TextStyle(fontFamily: 'Tajawal'),
                ),
                trailing: IconButton(
                  icon: const Icon(Icons.add_task, color: Colors.teal),
                  onPressed: () {
                    final appState =
                        Provider.of<AppState>(context, listen: false);
                    appState.addTask(
                      appState.groups.isNotEmpty
                          ? appState.groups[0].id
                          : 'default',
                      task['task']!,
                    );
                    Navigator.pop(context);
                    scaffoldMessengerKey.currentState?.showSnackBar(
                      const SnackBar(
                        content: Text('تم إضافة المهمة إلى مجموعتك',
                            style: TextStyle(fontFamily: 'Tajawal')),
                        backgroundColor: Colors.green,
                      ),
                    );
                  },
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}

class LeaderboardPage extends StatelessWidget {
  const LeaderboardPage({super.key});

  @override
  Widget build(BuildContext context) {
    final appState = Provider.of<AppState>(context);
    final leaderboardFuture = appState.getLeaderboard();

    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.grey.shade100, Colors.white],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        ),
      ),
      child: FutureBuilder<List<Map<String, dynamic>>>(
        future: leaderboardFuture,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          } else if (snapshot.hasError) {
            return Center(
              child: Text(
                'حدث خطأ أثناء تحميل لوحة الصدارة: ${snapshot.error}',
                style: const TextStyle(
                  fontSize: 18,
                  fontFamily: 'Tajawal',
                  color: Colors.red,
                ),
              ),
            );
          } else if (!snapshot.hasData || snapshot.data!.isEmpty) {
            return Center(
              child: Text(
                'لا توجد بيانات للوحة الصدارة بعد',
                style: TextStyle(
                  fontSize: 18,
                  fontFamily: 'Tajawal',
                  color: Colors.grey[600],
                ),
              ),
            );
          }
          final leaderboard = snapshot.data!;
          return ListView.builder(
            padding: const EdgeInsets.all(16.0),
            itemCount: leaderboard.length,
            itemBuilder: (context, index) {
              final user = leaderboard[index];
              return FadeInDown(
                duration: const Duration(milliseconds: 300),
                child: Card(
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 2,
                  margin: const EdgeInsets.symmetric(vertical: 8),
                  child: ListTile(
                    leading: CircleAvatar(
                      backgroundColor: Colors.teal[100],
                      child: Text(
                        (index + 1).toString(),
                        style: const TextStyle(
                          color: Colors.teal,
                          fontFamily: 'Tajawal',
                        ),
                      ),
                    ),
                    title: Text(
                      user['name']?.toString() ?? 'غير معروف',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontFamily: 'Tajawal',
                      ),
                    ),
                    subtitle: Text(
                      '${user['points']?.toString() ?? '0'} نقطة',
                      style: const TextStyle(fontFamily: 'Tajawal'),
                    ),
                    trailing: index < 3
                        ? Icon(
                            [
                              Icons.emoji_events,
                              Icons.star,
                              Icons.star_border
                            ][index],
                            color: Colors.amber,
                          )
                        : null,
                  ),
                ),
              );
            },
          );
        },
      ),
    );
  }
}

class NotificationsPage extends StatelessWidget {
  const NotificationsPage({super.key});

  @override
  Widget build(BuildContext context) {
    final appState = Provider.of<AppState>(context);
    // Mock notifications aligned with NotificationModel
    final notifications = [
      NotificationModel(
        id: '1',
        message: 'مهمة جديدة في مجموعة "الذراسة" تنتظرك!',
        date: DateTime.now().subtract(const Duration(hours: 1)).toString(),
        isRead: false,
        type: 'task_added',
      ),
      NotificationModel(
        id: '2',
        message: 'انضم أحمد إلى مجموعة "الرياضة"',
        date: DateTime.now().subtract(const Duration(hours: 3)).toString(),
        isRead: false,
        type: 'member_joined',
      ),
    ];

    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.grey.shade100, Colors.white],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        ),
      ),
      child: notifications.isEmpty
          ? Center(
              child: Text(
                'لا توجد إشعارات بعد',
                style: TextStyle(
                  fontSize: 18,
                  fontFamily: 'Tajawal',
                  color: Colors.grey[600],
                ),
              ),
            )
          : ListView.builder(
              padding: const EdgeInsets.all(16.0),
              itemCount: notifications.length,
              itemBuilder: (context, index) {
                final notification = notifications[index];
                return FadeInDown(
                  duration: const Duration(milliseconds: 300),
                  child: Card(
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12)),
                    elevation: 2,
                    margin: const EdgeInsets.symmetric(vertical: 8),
                    child: ListTile(
                      leading: CircleAvatar(
                        backgroundColor: Colors.teal[100],
                        child: Icon(
                          notification.type == 'task_added'
                              ? Icons.task
                              : Icons.group_add,
                          color: Colors.teal,
                        ),
                      ),
                      title: Text(
                        notification.message,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontFamily: 'Tajawal',
                        ),
                      ),
                      subtitle: Text(
                        'منذ ${DateTime.parse(notification.date).hour}:${DateTime.parse(notification.date).minute}',
                        style: const TextStyle(fontFamily: 'Tajawal'),
                      ),
                      trailing: Icon(
                        notification.isRead ? Icons.check_circle : Icons.circle,
                        color: notification.isRead ? Colors.green : Colors.grey,
                      ),
                    ),
                  ),
                );
              },
            ),
    );
  }
}

class VideoDialog extends StatefulWidget {
  final String videoUrl;

  const VideoDialog({super.key, required this.videoUrl});

  @override
  _VideoDialogState createState() => _VideoDialogState();
}

class _VideoDialogState extends State<VideoDialog> {
  VideoPlayerController? _controller;
  bool _isInitialized = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _initializeVideo();
  }

  Future<void> _initializeVideo() async {
    try {
      _controller =
          VideoPlayerController.networkUrl(Uri.parse(widget.videoUrl));
      await _controller!.initialize();
      if (mounted) {
        setState(() {
          _isInitialized = true;
          _controller!.play();
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'فشل تحميل الفيديو: $e';
        });
        _showSnackBar('فشل تحميل الفيديو: $e', Colors.red);
      }
    }
  }

  void _showSnackBar(String message, [Color? backgroundColor]) {
    scaffoldMessengerKey.currentState?.showSnackBar(
      SnackBar(
        content: Text(message, style: const TextStyle(fontFamily: 'Tajawal')),
        backgroundColor: backgroundColor ?? Colors.teal[700],
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
    );
  }

  @override
  void dispose() {
    _controller?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: SizedBox(
        width: 300,
        height: 200,
        child: _errorMessage != null
            ? Center(
                child: Text(
                  _errorMessage!,
                  style:
                      const TextStyle(color: Colors.red, fontFamily: 'Tajawal'),
                ),
              )
            : !_isInitialized
                ? Shimmer.fromColors(
                    baseColor: Colors.grey[300]!,
                    highlightColor: Colors.grey[100]!,
                    child: Container(
                      color: Colors.white,
                    ),
                  )
                : Stack(
                    children: [
                      VideoPlayer(_controller!),
                      Positioned(
                        bottom: 10,
                        right: 10,
                        child: FloatingActionButton(
                          mini: true,
                          backgroundColor: Colors.teal[700],
                          onPressed: () => setState(() {
                            _controller!.value.isPlaying
                                ? _controller!.pause()
                                : _controller!.play();
                          }),
                          child: Icon(
                            _controller!.value.isPlaying
                                ? Icons.pause
                                : Icons.play_arrow,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ],
                  ),
      ),
    );
  }
}

class AddGroupDialog extends StatefulWidget {
  final Function(TaskGroup) onGroupAdded;
  final TaskGroup? initialGroup;

  const AddGroupDialog(
      {super.key, required this.onGroupAdded, this.initialGroup});

  @override
  _AddGroupDialogState createState() => _AddGroupDialogState();
}

class _AddGroupDialogState extends State<AddGroupDialog> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _nameController;
  late TextEditingController _descController;
  late TextEditingController _videoController;
  GroupType _selectedType = GroupType.other;
  List<Map<String, dynamic>> _mediaFiles = [];

  @override
  void initState() {
    super.initState();
    _nameController =
        TextEditingController(text: widget.initialGroup?.name ?? '');
    _descController =
        TextEditingController(text: widget.initialGroup?.description ?? '');
    _videoController =
        TextEditingController(text: widget.initialGroup?.videoUrl ?? '');
    _selectedType = widget.initialGroup?.type ?? GroupType.other;
    _mediaFiles = widget.initialGroup?.media
            .map((m) => {'type': m['type'], 'path': m['path'], 'file': null})
            .toList() ??
        [];
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descController.dispose();
    _videoController.dispose();
    super.dispose();
  }

  Future<void> _addMedia() async {
    if (kIsWeb) {
      _showSnackBar('رفع الملفات غير مدعوم على الويب', Colors.red);
      return;
    }
    FilePickerResult? result = await FilePicker.platform
        .pickFiles(type: FileType.media, allowMultiple: true);
    if (result != null) {
      setState(() {
        _mediaFiles.addAll(result.files.map((file) => {
              'type': file.extension == 'mp4' ? 'video' : 'image',
              'file': File(file.path!),
              'path': file.path,
            }));
      });
    }
  }

  void _showSnackBar(String message, [Color? backgroundColor]) {
    scaffoldMessengerKey.currentState?.showSnackBar(
      SnackBar(
        content: Text(message, style: const TextStyle(fontFamily: 'Tajawal')),
        backgroundColor: backgroundColor ?? Colors.teal[700],
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
    );
  }

  void _saveGroup() {
    if (_formKey.currentState!.validate()) {
      final appState = Provider.of<AppState>(context, listen: false);
      final group = TaskGroup(
        id: widget.initialGroup?.id ?? const Uuid().v4(),
        name: _nameController.text,
        description: _descController.text,
        videoUrl: _videoController.text,
        members: widget.initialGroup?.members ??
            (appState.currentUser != null ? [appState.currentUser!] : []),
        type: _selectedType,
        points: widget.initialGroup?.points ?? 0,
        media: _mediaFiles
            .map((m) =>
                {'type': m['type'] as String, 'path': m['path'] as String})
            .toList(),
        tasksByUser: widget.initialGroup?.tasksByUser ?? {},
      );
      widget.onGroupAdded(group);
      Navigator.pop(context);
    }
  }

  Widget _buildGradientButton({
    required String text,
    required VoidCallback onPressed,
    required LinearGradient gradient,
    IconData? icon,
  }) {
    return InkWell(
      onTap: onPressed,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        decoration: BoxDecoration(
          gradient: gradient,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (icon != null) ...[
              Icon(icon, color: Colors.white, size: 18),
              const SizedBox(width: 8),
            ],
            Text(
              text,
              style: const TextStyle(
                color: Colors.white,
                fontFamily: 'Tajawal',
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      backgroundColor: Colors.white,
      title: Text(
        widget.initialGroup == null ? 'إضافة مجموعة جديدة' : 'تعديل المجموعة',
        style: const TextStyle(
          color: Colors.teal,
          fontWeight: FontWeight.bold,
          fontFamily: 'Tajawal',
        ),
      ),
      content: Form(
        key: _formKey,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ZoomIn(
                child: TextFormField(
                  controller: _nameController,
                  decoration: InputDecoration(
                    labelText: 'اسم المجموعة',
                    border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12)),
                    prefixIcon: const Icon(Icons.group, color: Colors.teal),
                    labelStyle: const TextStyle(fontFamily: 'Tajawal'),
                  ),
                  validator: (value) => value!.isEmpty ? 'الاسم مطلوب' : null,
                ),
              ),
              const SizedBox(height: 12),
              ZoomIn(
                child: TextFormField(
                  controller: _descController,
                  decoration: InputDecoration(
                    labelText: 'الوصف',
                    border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12)),
                    prefixIcon: const Icon(Icons.info, color: Colors.teal),
                    labelStyle: const TextStyle(fontFamily: 'Tajawal'),
                  ),
                  validator: (value) => value!.isEmpty ? 'الوصف مطلوب' : null,
                ),
              ),
              const SizedBox(height: 12),
              ZoomIn(
                child: TextFormField(
                  controller: _videoController,
                  decoration: InputDecoration(
                    labelText: 'رابط فيديو يوتيوب (اختياري)',
                    border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12)),
                    prefixIcon:
                        const Icon(Icons.video_collection, color: Colors.teal),
                    labelStyle: const TextStyle(fontFamily: 'Tajawal'),
                  ),
                ),
              ),
              const SizedBox(height: 12),
              ZoomIn(
                child: DropdownButtonFormField<GroupType>(
                  value: _selectedType,
                  decoration: InputDecoration(
                    labelText: 'نوع المجموعة',
                    border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12)),
                    labelStyle: const TextStyle(fontFamily: 'Tajawal'),
                  ),
                  items: GroupType.values
                      .map((type) => DropdownMenuItem(
                            value: type,
                            child: Row(
                              children: [
                                Icon(type.icon, color: type.color),
                                const SizedBox(width: 8),
                                Text(type.label,
                                    style:
                                        const TextStyle(fontFamily: 'Tajawal')),
                              ],
                            ),
                          ))
                      .toList(),
                  onChanged: (value) => setState(() => _selectedType = value!),
                  validator: (value) =>
                      value == null ? 'اختر نوع المجموعة' : null,
                ),
              ),
              const SizedBox(height: 12),
              ZoomIn(
                child: _buildGradientButton(
                  text: 'إضافة صورة/فيديو',
                  onPressed: _addMedia,
                  icon: Icons.upload,
                  gradient: LinearGradient(
                    colors: [Colors.teal[700]!, Colors.teal[500]!],
                  ),
                ),
              ),
              if (_mediaFiles.isNotEmpty)
                SizedBox(
                  height: 100,
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount: _mediaFiles.length,
                    itemBuilder: (context, index) => Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: _mediaFiles[index]['file'] != null
                          ? Image.file(
                              _mediaFiles[index]['file'],
                              width: 80,
                              height: 80,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) =>
                                  const Icon(Icons.error, color: Colors.red),
                            )
                          : CachedNetworkImage(
                              imageUrl: _mediaFiles[index]['path'],
                              width: 80,
                              height: 80,
                              fit: BoxFit.cover,
                              placeholder: (context, url) => Shimmer.fromColors(
                                baseColor: Colors.grey[300]!,
                                highlightColor: Colors.grey[100]!,
                                child: Container(
                                  color: Colors.white,
                                ),
                              ),
                              errorWidget: (context, url, error) =>
                                  const Icon(Icons.error, color: Colors.red),
                            ),
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text(
            'إلغاء',
            style: TextStyle(color: Colors.grey, fontFamily: 'Tajawal'),
          ),
        ),
        _buildGradientButton(
          text: widget.initialGroup == null ? 'إضافة' : 'تحديث',
          onPressed: _saveGroup,
          gradient: LinearGradient(
            colors: [Colors.teal[700]!, Colors.teal[500]!],
          ),
        ),
      ],
    );
  }
}

class AddTaskDialog extends StatefulWidget {
  final TaskGroup group;
  final Function(SimpleTask) onTaskAdded;

  const AddTaskDialog(
      {super.key, required this.group, required this.onTaskAdded});

  @override
  _AddTaskDialogState createState() => _AddTaskDialogState();
}

class _AddTaskDialogState extends State<AddTaskDialog> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _titleController;
  String _selectedCategory = 'يومي';
  late TextEditingController _pointsController;
  TimeOfDay _startTime = const TimeOfDay(hour: 8, minute: 0);
  int _duration = 30;
  DateTime? _dueDate;
  bool _isCollaborative = false;

  @override
  void initState() {
    super.initState();
    _titleController = TextEditingController();
    _pointsController = TextEditingController(text: '10 نقاط');
  }

  @override
  void dispose() {
    _titleController.dispose();
    _pointsController.dispose();
    super.dispose();
  }

  void _saveTask() {
    if (_formKey.currentState!.validate()) {
      final task = SimpleTask(
        id: const Uuid().v4(),
        title: _titleController.text,
        dueDate: _dueDate,
        isCollaborative: _isCollaborative,
      );
      widget.onTaskAdded(task);
      Navigator.pop(context);
    }
  }

  Future<void> _selectDueDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );
    if (picked != null && mounted) {
      setState(() => _dueDate = picked);
    }
  }

  Widget _buildGradientButton({
    required String text,
    required VoidCallback onPressed,
    required LinearGradient gradient,
    IconData? icon,
  }) {
    return InkWell(
      onTap: onPressed,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        decoration: BoxDecoration(
          gradient: gradient,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (icon != null) ...[
              Icon(icon, color: Colors.white, size: 18),
              const SizedBox(width: 8),
            ],
            Text(
              text,
              style: const TextStyle(
                color: Colors.white,
                fontFamily: 'Tajawal',
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      backgroundColor: Colors.white,
      title: const Text(
        'إضافة مهمة جديدة',
        style: TextStyle(
          color: Colors.teal,
          fontWeight: FontWeight.bold,
          fontFamily: 'Tajawal',
        ),
      ),
      content: Form(
        key: _formKey,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ZoomIn(
                child: TextFormField(
                  controller: _titleController,
                  decoration: InputDecoration(
                    labelText: 'عنوان المهمة',
                    border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12)),
                    prefixIcon: const Icon(Icons.list, color: Colors.teal),
                    labelStyle: const TextStyle(fontFamily: 'Tajawal'),
                  ),
                  validator: (value) => value!.isEmpty ? 'العنوان مطلوب' : null,
                ),
              ),
              const SizedBox(height: 12),
              ZoomIn(
                child: DropdownButtonFormField<String>(
                  value: _selectedCategory,
                  decoration: InputDecoration(
                    labelText: 'فئة المهمة',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    labelStyle: const TextStyle(fontFamily: 'Tajawal'),
                  ),
                  items: ['يومي', 'أسبوعي', 'شهري']
                      .map((category) => DropdownMenuItem<String>(
                            value: category,
                            child: Text(
                              category,
                              style: const TextStyle(fontFamily: 'Tajawal'),
                            ),
                          ))
                      .toList(),
                  onChanged: (value) {
                    if (value != null) {
                      setState(() => _selectedCategory = value);
                    }
                  },
                ),
              ),
              const SizedBox(height: 12),
              ZoomIn(
                child: TextFormField(
                  controller: _pointsController,
                  decoration: InputDecoration(
                    labelText: 'النقاط',
                    border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12)),
                    prefixIcon: const Icon(Icons.star, color: Colors.teal),
                    labelStyle: const TextStyle(fontFamily: 'Tajawal'),
                  ),
                  validator: (value) => value!.isEmpty ? 'النقاط مطلوبة' : null,
                ),
              ),
              const SizedBox(height: 12),
              ZoomIn(
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        'الوقت: ${_startTime.format(context)}',
                        style: const TextStyle(
                            color: Colors.teal, fontFamily: 'Tajawal'),
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.access_time, color: Colors.teal),
                      onPressed: () async {
                        final time = await showTimePicker(
                            context: context, initialTime: _startTime);
                        if (time != null) setState(() => _startTime = time);
                      },
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 12),
              ZoomIn(
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        _dueDate == null
                            ? 'تاريخ الاستحقاق: غير محدد'
                            : 'تاريخ الاستحقاق: ${_dueDate!.toString().substring(0, 10)}',
                        style: const TextStyle(
                            color: Colors.teal, fontFamily: 'Tajawal'),
                      ),
                    ),
                    IconButton(
                      icon:
                          const Icon(Icons.calendar_today, color: Colors.teal),
                      onPressed: () => _selectDueDate(context),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 12),
              ZoomIn(
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        'المدة: $_duration دقيقة',
                        style: const TextStyle(
                            color: Colors.teal, fontFamily: 'Tajawal'),
                      ),
                    ),
                    Slider(
                      value: _duration.toDouble(),
                      min: 5,
                      max: 120,
                      divisions: 23,
                      label: '$_duration',
                      activeColor: Colors.teal[700],
                      inactiveColor: Colors.teal[200],
                      onChanged: (value) =>
                          setState(() => _duration = value.toInt()),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 12),
              ZoomIn(
                child: CheckboxListTile(
                  title: const Text(
                    'مهمة تعاونية',
                    style: TextStyle(fontFamily: 'Tajawal'),
                  ),
                  value: _isCollaborative,
                  onChanged: (value) =>
                      setState(() => _isCollaborative = value!),
                  activeColor: Colors.teal,
                ),
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text(
            'إلغاء',
            style: TextStyle(color: Colors.grey, fontFamily: 'Tajawal'),
          ),
        ),
        _buildGradientButton(
          text: 'إضافة',
          onPressed: _saveTask,
          gradient: LinearGradient(
            colors: [Colors.teal[700]!, Colors.teal[500]!],
          ),
        ),
      ],
    );
  }
}

class TaskChatPage extends StatefulWidget {
  final String chatType;
  final TaskGroup group;
  final String baseUrl;
  final String authToken;

  const TaskChatPage({
    super.key,
    required this.chatType,
    required this.group,
    required this.baseUrl,
    required this.authToken,
  });

  @override
  _TaskChatPageState createState() => _TaskChatPageState();
}

class _TaskChatPageState extends State<TaskChatPage> {
  final TextEditingController _messageController = TextEditingController();
  final List<Map<String, String>> _messages = [];
  final ScrollController _scrollController = ScrollController();
  late io.Socket _socket;

  @override
  void initState() {
    super.initState();
    _initializeSocket();
  }

  void _initializeSocket() {
    _socket = io.io(widget.baseUrl, <String, dynamic>{
      'transports': ['websocket'],
      'autoConnect': false,
      'extraHeaders': {'x-auth-token': widget.authToken},
    });

    _socket.connect();
    _socket.onConnect((_) {
      _socket.emit('joinGroup', widget.group.id);
    });

    _socket.on('message', (data) {
      if (mounted) {
        setState(() {
          _messages.add({
            'username': data['username'] ?? 'مجهول',
            'content': data['content'] ?? '',
            'timestamp': DateTime.now().toString().substring(0, 16),
          });
        });
        _scrollToBottom();
      }
    });

    _socket.onConnectError((error) {
      if (mounted) {
        _showSnackBar('فشل الاتصال بالدردشة: $error', Colors.red);
      }
    });
  }

  void _showSnackBar(String message, [Color? backgroundColor]) {
    scaffoldMessengerKey.currentState?.showSnackBar(
      SnackBar(
        content: Text(message, style: const TextStyle(fontFamily: 'Tajawal')),
        backgroundColor: backgroundColor ?? Colors.teal[700],
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
    );
  }

  void _sendMessage() {
    if (_messageController.text.isNotEmpty) {
      final appState = Provider.of<AppState>(context, listen: false);
      final message = {
        'groupId': widget.group.id,
        'username': appState.currentUser?.name ?? 'مجهول',
        'content': _messageController.text,
      };
      _socket.emit('sendMessage', message);
      setState(() {
        _messages.add({
          'username': appState.currentUser?.name ?? 'مجهول',
          'content': _messageController.text,
          'timestamp': DateTime.now().toString().substring(0, 16),
        });
      });
      _messageController.clear();
      _scrollToBottom();
    }
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  @override
  void dispose() {
    _socket.disconnect();
    _socket.dispose();
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'دردشة ${widget.chatType} - ${widget.group.name}',
          style: const TextStyle(fontFamily: 'Tajawal'),
        ),
        backgroundColor: widget.group.type.color,
        elevation: 0,
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [Colors.grey[100]!, Colors.white],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
        ),
        child: Column(
          children: [
            Expanded(
              child: _messages.isEmpty
                  ? Center(
                      child: Text(
                        'لا توجد رسائل بعد',
                        style: TextStyle(
                          fontSize: 18,
                          fontFamily: 'Tajawal',
                          color: Colors.grey[600],
                        ),
                      ),
                    )
                  : ListView.builder(
                      controller: _scrollController,
                      padding: const EdgeInsets.all(8.0),
                      itemCount: _messages.length,
                      itemBuilder: (context, index) {
                        final message = _messages[index];
                        final isCurrentUser = message['username'] ==
                            Provider.of<AppState>(context).currentUser?.name;
                        return FadeIn(
                          duration: const Duration(milliseconds: 300),
                          child: Align(
                            alignment: isCurrentUser
                                ? Alignment.centerRight
                                : Alignment.centerLeft,
                            child: Container(
                              margin: const EdgeInsets.symmetric(
                                  vertical: 4, horizontal: 8),
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  colors: isCurrentUser
                                      ? [Colors.teal[200]!, Colors.teal[100]!]
                                      : [Colors.grey[200]!, Colors.grey[100]!],
                                ),
                                borderRadius: BorderRadius.circular(12),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.1),
                                    blurRadius: 4,
                                    offset: const Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: Column(
                                crossAxisAlignment: isCurrentUser
                                    ? CrossAxisAlignment.end
                                    : CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    message['username']!,
                                    style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                      color: isCurrentUser
                                          ? Colors.teal[800]
                                          : Colors.grey[800],
                                      fontFamily: 'Tajawal',
                                    ),
                                  ),
                                  const SizedBox(height: 4),
                                  Text(
                                    message['content']!,
                                    style:
                                        const TextStyle(fontFamily: 'Tajawal'),
                                  ),
                                  const SizedBox(height: 4),
                                  Text(
                                    message['timestamp']!,
                                    style: TextStyle(
                                      fontSize: 10,
                                      color: Colors.grey[600],
                                      fontFamily: 'Tajawal',
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        );
                      },
                    ),
            ),
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: _messageController,
                      decoration: InputDecoration(
                        hintText: 'اكتب رسالتك...',
                        border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(20)),
                        filled: true,
                        fillColor: Colors.white,
                        hintStyle: const TextStyle(fontFamily: 'Tajawal'),
                      ),
                      onSubmitted: (_) => _sendMessage(),
                    ),
                  ),
                  const SizedBox(width: 8),
                  _buildGradientButton(
                    text: 'إرسال',
                    onPressed: _sendMessage,
                    gradient: LinearGradient(
                      colors: [Colors.teal[700]!, Colors.teal[500]!],
                    ),
                    icon: Icons.send,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGradientButton({
    required String text,
    required VoidCallback onPressed,
    required LinearGradient gradient,
    IconData? icon,
  }) {
    return InkWell(
      onTap: onPressed,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        decoration: BoxDecoration(
          gradient: gradient,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (icon != null) ...[
              Icon(icon, color: Colors.white, size: 18),
              const SizedBox(width: 8),
            ],
            Text(
              text,
              style: const TextStyle(
                color: Colors.white,
                fontFamily: 'Tajawal',
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  _LoginPageState createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  bool _isLoading = false;

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  void _showSnackBar(String message, [Color? backgroundColor]) {
    scaffoldMessengerKey.currentState?.showSnackBar(
      SnackBar(
        content: Text(message, style: const TextStyle(fontFamily: 'Tajawal')),
        backgroundColor: backgroundColor ?? Colors.teal[700],
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
    );
  }

  Future<void> _login() async {
    if (_formKey.currentState!.validate()) {
      setState(() => _isLoading = true);
      try {
        final appState = Provider.of<AppState>(context, listen: false);
        await appState.login(
            _emailController.text, _passwordController.text, null);
        _showSnackBar('تم تسجيل الدخول بنجاح', Colors.green);
      } catch (e) {
        _showSnackBar('فشل في تسجيل الدخول: $e', Colors.red);
      } finally {
        if (mounted) {
          setState(() => _isLoading = false);
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [Colors.teal[700]!, Colors.teal[200]!],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
        ),
        child: SafeArea(
          child: Center(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Form(
                key: _formKey,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    ZoomIn(
                      child: const Text(
                        'Grop',
                        style: TextStyle(
                          fontSize: 48,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                          fontFamily: 'Tajawal',
                        ),
                      ),
                    ),
                    const SizedBox(height: 32),
                    ZoomIn(
                      child: TextFormField(
                        controller: _emailController,
                        decoration: InputDecoration(
                          labelText: 'البريد الإلكتروني',
                          prefixIcon:
                              const Icon(Icons.email, color: Colors.white),
                          filled: true,
                          fillColor: Colors.white.withOpacity(0.2),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide.none,
                          ),
                          labelStyle: const TextStyle(
                              color: Colors.white, fontFamily: 'Tajawal'),
                        ),
                        keyboardType: TextInputType.emailAddress,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'البريد الإلكتروني مطلوب';
                          }
                          if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$')
                              .hasMatch(value)) {
                            return 'البريد الإلكتروني غير صالح';
                          }
                          return null;
                        },
                      ),
                    ),
                    const SizedBox(height: 16),
                    ZoomIn(
                      child: TextFormField(
                        controller: _passwordController,
                        decoration: InputDecoration(
                          labelText: 'كلمة المرور',
                          prefixIcon:
                              const Icon(Icons.lock, color: Colors.white),
                          filled: true,
                          fillColor: Colors.white.withOpacity(0.2),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide.none,
                          ),
                          labelStyle: const TextStyle(
                              color: Colors.white, fontFamily: 'Tajawal'),
                        ),
                        obscureText: true,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'كلمة المرور مطلوبة';
                          }
                          if (value.length < 6) {
                            return 'كلمة المرور قصيرة جدًا';
                          }
                          return null;
                        },
                      ),
                    ),
                    const SizedBox(height: 24),
                    ZoomIn(
                      child: _isLoading
                          ? const CircularProgressIndicator(
                              valueColor:
                                  AlwaysStoppedAnimation<Color>(Colors.white),
                            )
                          : _buildGradientButton(
                              text: 'تسجيل الدخول',
                              onPressed: _login,
                              gradient: LinearGradient(
                                colors: [Colors.teal[600]!, Colors.teal[400]!],
                              ),
                              icon: Icons.login,
                            ),
                    ),
                    const SizedBox(height: 16),
                    ZoomIn(
                      child: TextButton(
                        onPressed: () {
                          // Navigate to registration page (to be implemented)
                          _showSnackBar(
                              'سيتم إضافة التسجيل قريبًا', Colors.teal[700]);
                        },
                        child: const Text(
                          'إنشاء حساب جديد',
                          style: TextStyle(
                            color: Colors.white,
                            fontFamily: 'Tajawal',
                            fontSize: 16,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildGradientButton({
    required String text,
    required VoidCallback onPressed,
    required LinearGradient gradient,
    IconData? icon,
  }) {
    return InkWell(
      onTap: onPressed,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        decoration: BoxDecoration(
          gradient: gradient,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (icon != null) ...[
              Icon(icon, color: Colors.white, size: 20),
              const SizedBox(width: 8),
            ],
            Text(
              text,
              style: const TextStyle(
                color: Colors.white,
                fontFamily: 'Tajawal',
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
