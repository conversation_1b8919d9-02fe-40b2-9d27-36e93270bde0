import 'dart:io';
import 'dart:convert';
import 'package:animate_do/animate_do.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:provider/provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:timeago/timeago.dart' as timeago;
import 'package:video_player/video_player.dart';
import 'package:http/http.dart' as http;
import 'package:timezone/timezone.dart' as tz;
import 'package:timezone/data/latest.dart' as tz_data;
import 'health_service.dart';
import 'appstate.dart';
import 'models/disease_model.dart';
import 'chat_room_page.dart';
import 'package:speech_to_text/speech_to_text.dart';

class CourseDetailsPage extends StatefulWidget {
  final String courseTitle;
  final Disease disease;

  const CourseDetailsPage({super.key, required this.courseTitle, required this.disease});

  @override
  _CourseDetailsPageState createState() => _CourseDetailsPageState();
}

class _CourseDetailsPageState extends State<CourseDetailsPage> with TickerProviderStateMixin {
  final HealthService _healthService = HealthService();
  final ScrollController _scrollController = ScrollController();
  final TextEditingController _newPostController = TextEditingController();
  final TextEditingController _progressController = TextEditingController();
  final TextEditingController _commentController = TextEditingController();
  final SpeechToText _speechToText = SpeechToText();
  List<dynamic> _memberPosts = [];
  List<dynamic> _dailyTasks = [];
  List<dynamic> _contents = [];
  List<dynamic> _chatRooms = [];
  List<dynamic> _progress = [];
  List<dynamic> _quizzes = [];
  File? _attachedFile;
  String? _attachedFileType;
  bool _isLoadingPosts = true;
  bool _isLoadingTasks = true;
  bool _isLoadingContents = true;
  bool _isLoadingChatRooms = true;
  bool _isLoadingProgress = true;
  bool _isLoadingQuizzes = true;
  bool _isGridView = false;
  bool _isSpeechEnabled = false;
  bool _isCommentsExpanded = false;
  late TabController _tabController;
  late AnimationController _fabAnimationController;
  late Animation<double> _fabAnimation;
  late FlutterLocalNotificationsPlugin _notificationsPlugin;

  @override
  void initState() {
    super.initState();
    // Initialize TabController with 6 tabs
    _tabController = TabController(length: 6, vsync: this);
    _fetchData();
    _initializeNotifications();
    _initializeSpeech();

    // Initialize FAB animation controller
    _fabAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 2),
    )..repeat(reverse: true);
    _fabAnimation = Tween<double>(begin: 1.0, end: 1.2).animate(
      CurvedAnimation(
        parent: _fabAnimationController,
        curve: Curves.easeInOut,
      ),
    );

    final appState = Provider.of<AppState>(context, listen: false);
    if (appState.socket != null) {
      _healthService.setupSocketListeners(
        appState.socket!,
            (data) => setState(() {}), // Placeholder for messages
            (data) => _fetchPosts(),
            (data) => _fetchContents(),
            (data) => _fetchChatRooms(),
            (data) {
          setState(() {
            // Add notification
            _showSnackBar(data['message'] ?? 'إشعار جديد', Colors.blue);
          });
        },
      );
    }
  }

  void _initializeNotifications() {
    _notificationsPlugin = FlutterLocalNotificationsPlugin();
    const initializationSettings = InitializationSettings(
      android: AndroidInitializationSettings('@mipmap/ic_launcher'),
      iOS: DarwinInitializationSettings(),
    );
    _notificationsPlugin.initialize(initializationSettings);
  }

  void _initializeSpeech() async {
    _isSpeechEnabled = await _speechToText.initialize();
    setState(() {});
  }

  Future<void> _scheduleNotification(String title, String body, DateTime scheduledTime) async {
    const androidDetails = AndroidNotificationDetails(
      'reminder_channel',
      'Reminders',
      importance: Importance.high,
      priority: Priority.high,
    );
    const platformDetails = NotificationDetails(android: androidDetails);

    tz_data.initializeTimeZones();
    final tz.TZDateTime tzScheduledTime = tz.TZDateTime.from(scheduledTime, tz.local);

    await _notificationsPlugin.zonedSchedule(
      0,
      title,
      body,
      tzScheduledTime,
      platformDetails,
      androidAllowWhileIdle: true,
      uiLocalNotificationDateInterpretation: UILocalNotificationDateInterpretation.absoluteTime,
    );
  }

  Future<void> _fetchData() async {
    await Future.wait([
      _fetchPosts(),
      _fetchTasks(),
      _fetchContents(),
      _fetchChatRooms(),
      _fetchProgress(),
      _fetchQuizzes(),
    ]);
  }

  Future<void> _fetchPosts() async {
    setState(() => _isLoadingPosts = true);
    final appState = Provider.of<AppState>(context, listen: false);
    try {
      _memberPosts = await _healthService.fetchPosts(appState.token ?? '', widget.courseTitle);
      setState(() => _isLoadingPosts = false);
    } catch (e) {
      _showSnackBar('خطأ في جلب المنشورات: $e', Colors.red);
      setState(() => _isLoadingPosts = false);
    }
  }

  Future<void> _fetchTasks() async {
    setState(() => _isLoadingTasks = true);
    final appState = Provider.of<AppState>(context, listen: false);
    try {
      _dailyTasks = await _healthService.fetchTasks(appState.token ?? '', widget.courseTitle);
      setState(() => _isLoadingTasks = false);
    } catch (e) {
      _showSnackBar('خطأ في جلب المهام: $e', Colors.red);
      setState(() => _isLoadingTasks = false);
    }
  }

  Future<void> _fetchContents() async {
    setState(() => _isLoadingContents = true);
    final appState = Provider.of<AppState>(context, listen: false);
    try {
      _contents = await _healthService.fetchContents(appState.token ?? '', widget.courseTitle);
      setState(() => _isLoadingContents = false);
    } catch (e) {
      _showSnackBar('خطأ في جلب المحتوى: $e', Colors.red);
      setState(() => _isLoadingContents = false);
    }
  }

  Future<void> _fetchChatRooms() async {
    setState(() => _isLoadingChatRooms = true);
    final appState = Provider.of<AppState>(context, listen: false);
    try {
      _chatRooms = await _healthService.fetchChatRooms(appState.token ?? '', widget.courseTitle);
      setState(() => _isLoadingChatRooms = false);
    } catch (e) {
      _showSnackBar('خطأ في جلب غرف الدردشة: $e', Colors.red);
      setState(() => _isLoadingChatRooms = false);
    }
  }

  Future<void> _fetchProgress() async {
    setState(() => _isLoadingProgress = true);
    final appState = Provider.of<AppState>(context, listen: false);
    try {
      _progress = await _healthService.fetchProgress(appState.token ?? '', widget.courseTitle);
      setState(() => _isLoadingProgress = false);
    } catch (e) {
      _showSnackBar('خطأ في جلب التقدم: $e', Colors.red);
      setState(() => _isLoadingProgress = false);
    }
  }

  Future<void> _fetchQuizzes() async {
    setState(() => _isLoadingQuizzes = true);
    final appState = Provider.of<AppState>(context, listen: false);
    try {
      _quizzes = await _healthService.fetchQuizzes(appState.token ?? '', widget.courseTitle);
      setState(() => _isLoadingQuizzes = false);
    } catch (e) {
      _showSnackBar('خطأ في جلب الاختبارات: $e', Colors.red);
      setState(() => _isLoadingQuizzes = false);
    }
  }

  Future<void> _postNewContent() async {
    if (_newPostController.text.isEmpty && _attachedFile == null) {
      _showSnackBar('يرجى إدخال محتوى أو إرفاق ملف', Colors.orange);
      return;
    }
    setState(() => _isLoadingPosts = true);
    final appState = Provider.of<AppState>(context, listen: false);
    try {
      await _healthService.addPost(
          appState.token ?? '', widget.courseTitle, _newPostController.text, _attachedFile, _attachedFileType);
      _newPostController.clear();
      setState(() {
        _attachedFile = null;
        _attachedFileType = null;
      });
      await _fetchPosts();
      _showSnackBar(
        "تم النشر بنجاح",
        Colors.green,
        SnackBarAction(
          label: 'عرض',
          textColor: Colors.white,
          onPressed: () {
            _scrollController.animateTo(
              0,
              duration: const Duration(milliseconds: 500),
              curve: Curves.easeInOut,
            );
          },
        ),
      );
    } catch (e) {
      _showSnackBar("خطأ في النشر: $e", Colors.red);
    }
    setState(() => _isLoadingPosts = false);
  }

  Future<void> _attachFile(String type) async {
    FilePickerResult? result = await FilePicker.platform.pickFiles(
      type: type == 'image' ? FileType.image : FileType.video,
      allowedExtensions: type == 'image' ? ['jpg', 'jpeg', 'png'] : ['mp4', 'mov'],
    );
    if (result != null) {
      final file = File(result.files.single.path!);
      if (await file.length() > 5 * 1024 * 1024) {
        _showSnackBar('حجم الملف كبير جدًا (يجب أن يكون أقل من 5 ميجابايت)', Colors.orange);
        return;
      }
      setState(() {
        _attachedFile = file;
        _attachedFileType = type;
      });
    }
  }

  Future<void> _startSpeechToText() async {
    if (_isSpeechEnabled) {
      await _speechToText.listen(
        onResult: (result) {
          setState(() {
            _newPostController.text = result.recognizedWords;
          });
        },
      );
    } else {
      _showSnackBar('التعرف على الصوت غير متوفر', Colors.red);
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    _newPostController.dispose();
    _progressController.dispose();
    _commentController.dispose();
    _scrollController.dispose();
    _fabAnimationController.dispose();
    _speechToText.stop();
    super.dispose();
  }

  void _showSnackBar(String message, [Color? backgroundColor, SnackBarAction? action]) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: backgroundColor ?? Colors.blue,
        behavior: SnackBarBehavior.floating,
        action: action,
        elevation: 6,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return Scaffold(
      body: _isLoadingPosts &&
          _isLoadingTasks &&
          _isLoadingContents &&
          _isLoadingChatRooms &&
          _isLoadingProgress &&
          _isLoadingQuizzes
          ? _buildSkeletonLoader()
          : CustomScrollView(
        controller: _scrollController,
        slivers: [
          _buildSliverAppBar(isDarkMode),
          SliverFillRemaining(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildPostsTab(),
                _buildTasksTab(),
                _buildContentsTab(),
                _buildChatRoomsTab(),
                _buildDiseaseDetailsTab(),
                _buildProgressTab(),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          ScaleTransition(
            scale: _fabAnimation,
            child: FloatingActionButton(
              onPressed: () => _showCreateChatRoomDialog(),
              backgroundColor: Colors.blue,
              heroTag: 'createChat',
              tooltip: 'إنشاء غرفة دردشة',
              elevation: 8,
              child: const Icon(Icons.chat, size: 30, color: Colors.white),
            ),
          ),
          const SizedBox(height: 16),
          ScaleTransition(
            scale: _fabAnimation,
            child: FloatingActionButton(
              onPressed: () => _showAddContentDialog(),
              backgroundColor: Colors.blue,
              heroTag: 'addContent',
              tooltip: 'إضافة محتوى',
              elevation: 8,
              child: const Icon(Icons.add, size: 30, color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSkeletonLoader() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return CustomScrollView(
      slivers: [
        SliverAppBar(
          expandedHeight: 150,
          flexibleSpace: FlexibleSpaceBar(
            background: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: isDarkMode
                      ? [Colors.blue[900]!, Colors.blue[700]!]
                      : [Colors.blue, Colors.blueAccent],
                ),
              ),
            ),
          ),
        ),
        SliverList(
          delegate: SliverChildBuilderDelegate(
                (context, index) => Pulse(
              child: Card(
                margin: const EdgeInsets.all(16),
                child: Padding(
                  padding: const EdgeInsets.all(12.0),
                  child: Column(
                    children: [
                      Container(height: 20, width: 100, color: Colors.grey[300]),
                      const SizedBox(height: 10),
                      Container(height: 100, width: double.infinity, color: Colors.grey[300]),
                    ],
                  ),
                ),
              ),
            ),
            childCount: 5,
          ),
        ),
      ],
    );
  }

  SliverAppBar _buildSliverAppBar(bool isDarkMode) {
    return SliverAppBar(
      expandedHeight: 150,
      flexibleSpace: FlexibleSpaceBar(
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: isDarkMode
                  ? [Colors.blue[900]!, Colors.blue[700]!]
                  : [Colors.blue, Colors.blueAccent],
            ),
          ),
          child: SafeArea(
            child: Center(
              child: Text(
                widget.courseTitle,
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ),
          ),
        ),
      ),
      actions: [
        IconButton(
          icon: Icon(_isGridView ? Icons.list : Icons.grid_view, color: Colors.white),
          onPressed: () => setState(() => _isGridView = !_isGridView),
        ),
        IconButton(
          icon: const Icon(Icons.favorite_border, color: Colors.white),
          onPressed: () => _showSnackBar(
            "تمت الإضافة إلى المفضلة",
            Colors.green,
            SnackBarAction(
              label: 'عرض',
              textColor: Colors.white,
              onPressed: () {
                _scrollController.animateTo(
                  0,
                  duration: const Duration(milliseconds: 500),
                  curve: Curves.easeInOut,
                );
              },
            ),
          ),
        ),
      ],
      bottom: TabBar(
        controller: _tabController,
        tabs: const [
          Tab(text: "المنشورات"),
          Tab(text: "المهام"),
          Tab(text: "المحتوى"),
          Tab(text: "الدردشة"),
          Tab(text: "تفاصيل المرض"),
          Tab(text: "التقدم"),
        ],
        labelColor: Colors.white,
        unselectedLabelColor: Colors.white70,
        indicatorColor: Colors.white,
      ),
      pinned: true,
    );
  }

  Widget _buildPostsTab() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return RefreshIndicator(
      onRefresh: _fetchPosts,
      color: Colors.blue,
      child: _isLoadingPosts
          ? const Center(child: CircularProgressIndicator())
          : CustomScrollView(
        slivers: [
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: FadeInDown(
                child: Column(
                  children: [
                    TextField(
                      controller: _newPostController,
                      maxLines: 3,
                      decoration: InputDecoration(
                        hintText: "اكتب منشورًا...",
                        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                        filled: true,
                        fillColor: isDarkMode ? Colors.blue[900] : Colors.blue[50],
                      ),
                      style: TextStyle(color: isDarkMode ? Colors.white : Colors.black87),
                    ),
                    Row(
                      children: [
                        IconButton(
                            icon: const Icon(Icons.photo, color: Colors.blue),
                            onPressed: () => _attachFile('image')),
                        IconButton(
                            icon: const Icon(Icons.videocam, color: Colors.blue),
                            onPressed: () => _attachFile('video')),
                        IconButton(
                            icon: const Icon(Icons.mic, color: Colors.blue),
                            onPressed: _startSpeechToText),
                        if (_attachedFile != null)
                          Flexible(
                            child: Text(
                              _attachedFile!.path.split('/').last,
                              overflow: TextOverflow.ellipsis,
                              style: TextStyle(
                                color: isDarkMode ? Colors.white70 : Colors.black87,
                              ),
                            ),
                          ),
                        const Spacer(),
                        ElevatedButton(
                          onPressed: _postNewContent,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.blue,
                            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
                          ),
                          child: const Text("نشر", style: TextStyle(color: Colors.white)),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
          SliverToBoxAdapter(
            child: _isGridView ? _buildGridPosts(isDarkMode) : _buildListPosts(isDarkMode),
          ),
        ],
      ),
    );
  }

  Widget _buildGridPosts(bool isDarkMode) {
    return Padding(
      padding: const EdgeInsets.all(8),
      child: MasonryGridView.count(
        crossAxisCount: 2,
        mainAxisSpacing: 8,
        crossAxisSpacing: 8,
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: _memberPosts.length,
        itemBuilder: (context, index) => FadeInUp(
          child: _buildMemberPostCard(_memberPosts[index], isDarkMode: isDarkMode, isGrid: true),
        ),
      ),
    );
  }

  Widget _buildListPosts(bool isDarkMode) {
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: _memberPosts.length,
      itemBuilder: (context, index) => FadeInUp(
        child: _buildMemberPostCard(_memberPosts[index], isDarkMode: isDarkMode, isGrid: false),
      ),
    );
  }

  Widget _buildTasksTab() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return RefreshIndicator(
      onRefresh: _fetchTasks,
      color: Colors.blue,
      child: _isLoadingTasks
          ? const Center(child: CircularProgressIndicator())
          : ListView.builder(
        itemCount: _dailyTasks.length,
        itemBuilder: (context, index) => FadeInUp(
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              color: _dailyTasks[index]['completed']
                  ? Colors.green[50]
                  : (isDarkMode ? Colors.grey[850] : Colors.white),
              boxShadow: [
                BoxShadow(
                  color: Colors.blue.withOpacity(0.2),
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: ListTile(
              leading: AnimatedSwitcher(
                duration: const Duration(milliseconds: 300),
                child: Icon(
                  _dailyTasks[index]['completed'] ? Icons.check_circle : Icons.circle_outlined,
                  key: ValueKey(_dailyTasks[index]['completed']),
                  color: Colors.blue,
                ),
              ),
              title: Text(
                _dailyTasks[index]['task'] ?? 'مهمة غير معروفة',
                style: TextStyle(color: isDarkMode ? Colors.white : Colors.black87),
              ),
              trailing: Text(
                "+${_dailyTasks[index]['points'] ?? 0} نقاط",
                style: const TextStyle(color: Colors.green),
              ),
              onTap: () async {
                if (!_dailyTasks[index]['completed']) {
                  final appState = Provider.of<AppState>(context, listen: false);
                  try {
                    await _healthService.completeTask(appState.token ?? '', _dailyTasks[index]['_id']);
                    await _fetchTasks();
                    _showSnackBar('تم إكمال المهمة', Colors.green);
                    _scheduleNotification(
                      'مهمة مكتملة',
                      'لقد أكملت المهمة: ${_dailyTasks[index]['task']}',
                      DateTime.now().add(const Duration(seconds: 5)),
                    );
                  } catch (e) {
                    _showSnackBar('خطأ في إكمال المهمة: $e', Colors.red);
                  }
                }
              },
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildContentsTab() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return RefreshIndicator(
      onRefresh: _fetchContents,
      color: Colors.blue,
      child: _isLoadingContents
          ? const Center(child: CircularProgressIndicator())
          : SizedBox(
        height: 200,
        child: ListView.builder(
          scrollDirection: Axis.horizontal,
          itemCount: _contents.length,
          itemBuilder: (context, index) => FadeInRight(
            child: GestureDetector(
              onTap: () => _showContent(_contents[index]),
              child: Transform(
                transform: Matrix4.identity()
                  ..setEntry(3, 2, 0.001)
                  ..rotateY(0.05),
                alignment: Alignment.center,
                child: Container(
                  width: 150,
                  margin: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    color: isDarkMode ? Colors.grey[850] : Colors.white,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.blue.withOpacity(0.2),
                        blurRadius: 8,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Column(
                    children: [
                      Icon(
                        _contents[index]['contentType'] == 'فيديو'
                            ? Icons.videocam
                            : _contents[index]['contentType'] == 'دليل'
                            ? Icons.picture_as_pdf
                            : Icons.book,
                        size: 40,
                        color: Colors.blue,
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 8.0),
                        child: Text(
                          _contents[index]['title'] ?? 'بدون عنوان',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: isDarkMode ? Colors.white : Colors.black87,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 8.0),
                        child: Text(
                          _contents[index]['description'] ?? '',
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                          style: TextStyle(
                            fontSize: 12,
                            color: isDarkMode ? Colors.grey[400] : Colors.grey,
                          ),
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.share, color: Colors.blue),
                        onPressed: () => Share.share(
                          'محتوى: ${_contents[index]['title']} - ${_contents[index]['description']} ${_healthService.baseUrl}${_contents[index]['file']}',
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildChatRoomsTab() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return RefreshIndicator(
      onRefresh: _fetchChatRooms,
      color: Colors.blue,
      child: _isLoadingChatRooms
          ? const Center(child: CircularProgressIndicator())
          : SizedBox(
        height: 120,
        child: ListView.builder(
          scrollDirection: Axis.horizontal,
          itemCount: _chatRooms.length,
          itemBuilder: (context, index) => FadeInRight(
            child: GestureDetector(
              onTap: () => Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => ChatRoomPage(
                    roomId: _chatRooms[index]['_id'],
                    roomName: _chatRooms[index]['name'],
                  ),
                ),
              ),
              child: Container(
                width: 150,
                margin: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: isDarkMode ? Colors.blue[900] : Colors.blue[50],
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.blue.withOpacity(0.2),
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Stack(
                  children: [
                    Center(
                      child: Text(
                        _chatRooms[index]['name'] ?? 'غرفة غير معروفة',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: isDarkMode ? Colors.white : Colors.black87,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    if (_chatRooms[index]['unread'] != null && _chatRooms[index]['unread'] > 0)
                      Positioned(
                        right: 8,
                        top: 8,
                        child: Container(
                          padding: const EdgeInsets.all(4),
                          decoration: const BoxDecoration(
                            color: Colors.red,
                            shape: BoxShape.circle,
                          ),
                          child: Text(
                            '${_chatRooms[index]['unread']}',
                            style: const TextStyle(color: Colors.white, fontSize: 10),
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDiseaseDetailsTab() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ZoomIn(
            child: Text(
              'تفاصيل المرض',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: isDarkMode ? Colors.blue[300] : Colors.blue,
              ),
            ),
          ),
          const SizedBox(height: 16),
          _buildExpandableSection('الأعراض', widget.disease.symptoms, isDarkMode),
          _buildExpandableSection('الأسباب', widget.disease.causes, isDarkMode),
          _buildExpandableSection('الفحوصات والتحاليل', widget.disease.tests, isDarkMode),
          _buildExpandableSection('العلاج', widget.disease.treatments, isDarkMode),
          _buildExpandableSection('الأطعمة المناسبة', widget.disease.suitableFoods, isDarkMode),
          _buildExpandableSection('الأطعمة غير العملية', widget.disease.unsuitableFoods, isDarkMode),
        ],
      ),
    );
  }

  Widget _buildExpandableSection(String title, List<String> items, bool isDarkMode) {
    return ExpansionTile(
      title: Text(
        title,
        style: TextStyle(fontWeight: FontWeight.bold, color: isDarkMode ? Colors.white : Colors.black87),
      ),
      children: items.isEmpty
          ? [
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: Text(
            'لا توجد بيانات متاحة',
            style: TextStyle(color: isDarkMode ? Colors.grey[400] : Colors.grey),
          ),
        ),
      ]
          : items
          .map((item) => ListTile(
        title: Text(
          item,
          style: TextStyle(color: isDarkMode ? Colors.white70 : Colors.black87),
        ),
      ))
          .toList(),
    );
  }

  Widget _buildProgressTab() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return RefreshIndicator(
      onRefresh: _fetchProgress,
      color: Colors.blue,
      child: _isLoadingProgress
          ? const Center(child: CircularProgressIndicator())
          : Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: FadeInDown(
              child: Column(
                children: [
                  TextField(
                    controller: _progressController,
                    decoration: InputDecoration(
                      hintText: "سجل تقدمك (مثال: مستوى السكر, الأعراض)",
                      border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                      filled: true,
                      fillColor: isDarkMode ? Colors.blue[900] : Colors.blue[50],
                    ),
                    style: TextStyle(color: isDarkMode ? Colors.white : Colors.black87),
                  ),
                  const SizedBox(height: 8),
                  ElevatedButton(
                    onPressed: () async {
                      if (_progressController.text.isNotEmpty) {
                        final appState = Provider.of<AppState>(context, listen: false);
                        try {
                          await _healthService.saveProgress(
                            appState.token ?? '',
                            widget.courseTitle,
                            {
                              'entry': _progressController.text,
                              'date': DateTime.now().toString(),
                            },
                          );
                          _progressController.clear();
                          await _fetchProgress();
                          _showSnackBar('تم تسجيل التقدم', Colors.green);
                        } catch (e) {
                          _showSnackBar('خطأ في تسجيل التقدم: $e', Colors.red);
                        }
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
                    ),
                    child: const Text("تسجيل", style: TextStyle(color: Colors.white)),
                  ),
                ],
              ),
            ),
          ),
          Expanded(
            child: ListView.builder(
              itemCount: _progress.length,
              itemBuilder: (context, index) => FadeInUp(
                child: ListTile(
                  title: Text(
                    _progress[index]['entry'] ?? 'غير معروف',
                    style: TextStyle(color: isDarkMode ? Colors.white : Colors.black87),
                  ),
                  subtitle: Text(
                    timeago.format(
                      DateTime.parse(_progress[index]['date'] ?? DateTime.now().toString()),
                      locale: 'ar',
                    ),
                    style: TextStyle(color: isDarkMode ? Colors.grey[400] : Colors.grey),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMemberPostCard(dynamic post, {required bool isDarkMode, required bool isGrid}) {
    return Card(
      margin: const EdgeInsets.all(8),
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  radius: 20,
                  backgroundColor: Colors.blue[100],
                  child: Text(
                    post['memberName']?.substring(0, 1) ?? '?',
                    style: const TextStyle(color: Colors.blue),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        post['memberName'] ?? 'مجهول',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: isDarkMode ? Colors.white : Colors.black87,
                        ),
                      ),
                      Text(
                        timeago.format(
                          DateTime.parse(post['createdAt'] ?? DateTime.now().toString()),
                          locale: 'ar',
                        ),
                        style: TextStyle(
                          fontSize: 12,
                          color: isDarkMode ? Colors.grey[400] : Colors.grey,
                        ),
                      ),
                    ],
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.more_vert, color: Colors.blue),
                  onPressed: () => _showPostOptions(context, post),
                ),
              ],
            ),
            const SizedBox(height: 12),
            if (post['attachedFile'] != null)
              ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: post['fileType'] == 'image'
                    ? CachedNetworkImage(
                  imageUrl: '${_healthService.baseUrl}${post['attachedFile']}',
                  height: isGrid ? 100 : 200,
                  width: double.infinity,
                  fit: BoxFit.cover,
                  placeholder: (context, url) => Container(
                    color: isDarkMode ? Colors.grey[800] : Colors.grey[300],
                    child: const Center(
                      child: CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
                      ),
                    ),
                  ),
                  errorWidget: (context, url, error) => const Icon(
                    Icons.error,
                    color: Colors.blue,
                    size: 50,
                  ),
                )
                    : VideoPlayerWidget(videoUrl: '${_healthService.baseUrl}${post['attachedFile']}'),
              ),
            const SizedBox(height: 12),
            Text(
              post['postContent'] ?? '',
              style: TextStyle(
                fontSize: 14,
                color: isDarkMode ? Colors.white70 : Colors.black87,
              ),
            ),
            const SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    const Icon(Icons.visibility, size: 18, color: Colors.grey),
                    const SizedBox(width: 4),
                    Text(
                      '${post['views'] ?? 0}',
                      style: TextStyle(color: isDarkMode ? Colors.grey[400] : Colors.grey),
                    ),
                  ],
                ),
                Row(
                  children: [
                    IconButton(
                      icon: AnimatedSwitcher(
                        duration: const Duration(milliseconds: 300),
                        child: Icon(
                          post['likes'] != null && post['likes'] > 0 ? Icons.thumb_up : Icons.thumb_up_outlined,
                          key: ValueKey(post['likes'] != null && post['likes'] > 0),
                          color: post['likes'] != null && post['likes'] > 0 ? Colors.blue : Colors.grey,
                        ),
                      ),
                      onPressed: () async {
                        final appState = Provider.of<AppState>(context, listen: false);
                        try {
                          await http.put(
                            Uri.parse('${_healthService.baseUrl}/health-posts/${post['_id']}/like'),
                            headers: {'x-auth-token': appState.token ?? ''},
                          );
                          await _fetchPosts();
                        } catch (e) {
                          _showSnackBar('خطأ في تحديث الإعجاب: $e', Colors.red);
                        }
                      },
                    ),
                    Text(
                      '${post['likes'] ?? 0}',
                      style: TextStyle(color: isDarkMode ? Colors.white70 : Colors.black87),
                    ),
                    IconButton(
                      icon: const Icon(Icons.share, color: Colors.blue),
                      onPressed: () => _sharePost(post),
                    ),
                  ],
                ),
              ],
            ),
            const Divider(),
            GestureDetector(
              onTap: () => setState(() => _isCommentsExpanded = !_isCommentsExpanded),
              child: Text(
                "التعليقات (${post['comments']?.length ?? 0})",
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: isDarkMode ? Colors.blue[300] : Colors.blue,
                ),
              ),
            ),
            const SizedBox(height: 8),
            if (_isCommentsExpanded || !isGrid)
              post['comments'] == null || post['comments'].isEmpty
                  ? Text(
                'لا توجد تعليقات بعد',
                style: TextStyle(color: isDarkMode ? Colors.grey[400] : Colors.grey),
              )
                  : AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                constraints: BoxConstraints(maxHeight: _isCommentsExpanded ? 200 : 50),
                child: ListView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: _isCommentsExpanded
                      ? post['comments'].length
                      : (isGrid ? 1 : post['comments'].length),
                  itemBuilder: (context, index) => Padding(
                    padding: const EdgeInsets.symmetric(vertical: 4),
                    child: Text(
                      post['comments'][index]['content'] ?? 'تعليق غير معروف',
                      style: TextStyle(
                        color: isDarkMode ? Colors.white70 : Colors.black87,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ),
              ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _commentController,
                    decoration: InputDecoration(
                      hintText: 'اكتب تعليقًا...',
                      border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
                      filled: true,
                      fillColor: isDarkMode ? Colors.blue[900] : Colors.blue[50],
                    ),
                    style: TextStyle(color: isDarkMode ? Colors.white : Colors.black87),
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.send, color: Colors.blue),
                  onPressed: () async {
                    if (_commentController.text.isNotEmpty) {
                      final appState = Provider.of<AppState>(context, listen: false);
                      try {
                        await http.post(
                          Uri.parse('${_healthService.baseUrl}/health-posts/${post['_id']}/comment'),
                          headers: {
                            'Content-Type': 'application/json',
                            'x-auth-token': appState.token ?? '',
                          },
                          body: jsonEncode({'content': _commentController.text}),
                        );
                        _commentController.clear();
                        await _fetchPosts();
                        _showSnackBar('تم إضافة التعليق', Colors.green);
                      } catch (e) {
                        _showSnackBar('خطأ في إضافة التعليق: $e', Colors.red);
                      }
                    }
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _showPostOptions(BuildContext context, dynamic post) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    showModalBottomSheet(
      context: context,
      backgroundColor: isDarkMode ? Colors.grey[900] : Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.share, color: Colors.blue),
              title: Text(
                'مشاركة المنشور',
                style: TextStyle(color: isDarkMode ? Colors.white : Colors.black87),
              ),
              onTap: () {
                Navigator.pop(context);
                _sharePost(post);
              },
            ),
            ListTile(
              leading: const Icon(Icons.delete, color: Colors.red),
              title: Text(
                'حذف المنشور',
                style: TextStyle(color: isDarkMode ? Colors.white : Colors.black87),
              ),
              onTap: () async {
                Navigator.pop(context);
                final appState = Provider.of<AppState>(context, listen: false);
                try {
                  await http.delete(
                    Uri.parse('${_healthService.baseUrl}/health-posts/${post['_id']}'),
                    headers: {'x-auth-token': appState.token ?? ''},
                  );
                  await _fetchPosts();
                  _showSnackBar('تم حذف المنشور', Colors.green);
                } catch (e) {
                  _showSnackBar('خطأ في حذف المنشور: $e', Colors.red);
                }
              },
            ),
          ],
        );
      },
    );
  }

  void _sharePost(dynamic post) {
    Share.share(
      'منشور: ${post['postContent']} ${post['attachedFile'] != null ? _healthService.baseUrl + post['attachedFile'] : ''}',
    );
  }

  void _showContent(dynamic content) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                content['title'] ?? 'بدون عنوان',
                style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),
              if (content['file'] != null)
                content['contentType'] == 'فيديو'
                    ? VideoPlayerWidget(videoUrl: '${_healthService.baseUrl}${content['file']}')
                    : content['contentType'] == 'صورة'
                    ? CachedNetworkImage(
                  imageUrl: '${_healthService.baseUrl}${content['file']}',
                  height: 200,
                  width: double.infinity,
                  fit: BoxFit.cover,
                )
                    : const Icon(Icons.picture_as_pdf, size: 100, color: Colors.blue),
              const SizedBox(height: 16),
              Text(
                content['description'] ?? 'بدون وصف',
                style: TextStyle(
                  color: Theme.of(context).brightness == Brightness.dark ? Colors.white70 : Colors.black87,
                ),
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => Navigator.pop(context),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
                ),
                child: const Text('إغلاق', style: TextStyle(color: Colors.white)),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showCreateChatRoomDialog() {
    final TextEditingController roomNameController = TextEditingController();
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: isDarkMode ? Colors.grey[900] : Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: const Text('إنشاء غرفة دردشة'),
        content: TextField(
          controller: roomNameController,
          decoration: InputDecoration(
            hintText: 'اسم الغرفة',
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
            filled: true,
            fillColor: isDarkMode ? Colors.blue[900] : Colors.blue[50],
          ),
          style: TextStyle(color: isDarkMode ? Colors.white : Colors.black87),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء', style: TextStyle(color: Colors.red)),
          ),
          ElevatedButton(
            onPressed: () async {
              if (roomNameController.text.isNotEmpty) {
                final appState = Provider.of<AppState>(context, listen: false);
                try {
                  await _healthService.createChatRoom(
                    appState.token ?? '',
                    roomNameController.text,
                    [appState.userId ?? ''],
                    widget.courseTitle,
                  );
                  Navigator.pop(context);
                  await _fetchChatRooms();
                  _showSnackBar('تم إنشاء غرفة الدردشة', Colors.green);
                } catch (e) {
                  _showSnackBar('خطأ في إنشاء غرفة الدردشة: $e', Colors.red);
                }
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
            ),
            child: const Text('إنشاء', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  void _showAddContentDialog() {
    final TextEditingController titleController = TextEditingController();
    final TextEditingController descriptionController = TextEditingController();
    String? contentType;
    File? contentFile;
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          backgroundColor: isDarkMode ? Colors.grey[900] : Colors.white,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          title: const Text('إضافة محتوى'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: titleController,
                  decoration: InputDecoration(
                    hintText: 'عنوان المحتوى',
                    border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
                    filled: true,
                    fillColor: isDarkMode ? Colors.blue[900] : Colors.blue[50],
                  ),
                  style: TextStyle(color: isDarkMode ? Colors.white : Colors.black87),
                ),
                const SizedBox(height: 8),
                DropdownButtonFormField<String>(
                  decoration: InputDecoration(
                    hintText: 'نوع المحتوى',
                    border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
                    filled: true,
                    fillColor: isDarkMode ? Colors.blue[900] : Colors.blue[50],
                  ),
                  items: ['صورة', 'فيديو', 'دليل']
                      .map((type) => DropdownMenuItem(value: type, child: Text(type)))
                      .toList(),
                  onChanged: (value) => setState(() => contentType = value),
                  style: TextStyle(color: isDarkMode ? Colors.white : Colors.black87),
                  dropdownColor: isDarkMode ? Colors.grey[900] : Colors.white,
                ),
                const SizedBox(height: 8),
                TextField(
                  controller: descriptionController,
                  decoration: InputDecoration(
                    hintText: 'وصف المحتوى (اختياري)',
                    border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
                    filled: true,
                    fillColor: isDarkMode ? Colors.blue[900] : Colors.blue[50],
                  ),
                  style: TextStyle(color: isDarkMode ? Colors.white : Colors.black87),
                ),
                const SizedBox(height: 8),
                ElevatedButton(
                  onPressed: () async {
                    FilePickerResult? result = await FilePicker.platform.pickFiles(
                      type: contentType == 'صورة'
                          ? FileType.image
                          : contentType == 'فيديو'
                          ? FileType.video
                          : FileType.any,
                    );
                    if (result != null) {
                      final file = File(result.files.single.path!);
                      if (await file.length() > 5 * 1024 * 1024) {
                        _showSnackBar(
                            'حجم الملف كبير جدًا (يجب أن يكون أقل من 5 ميجابايت)', Colors.orange);
                        return;
                      }
                      setState(() => contentFile = file);
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
                  ),
                  child: const Text('إرفاق ملف', style: TextStyle(color: Colors.white)),
                ),
                if (contentFile != null)
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8.0),
                    child: Text(
                      contentFile!.path.split('/').last,
                      style: TextStyle(color: isDarkMode ? Colors.white70 : Colors.black87),
                    ),
                  ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('إلغاء', style: TextStyle(color: Colors.red)),
            ),
            ElevatedButton(
              onPressed: () async {
                if (titleController.text.isNotEmpty && contentType != null && contentFile != null) {
                  final appState = Provider.of<AppState>(context, listen: false);
                  try {
                    await _healthService.addContent(
                      appState.token ?? '',
                      widget.courseTitle,
                      titleController.text,
                      contentType!,
                      descriptionController.text.isNotEmpty ? descriptionController.text : null,
                      contentFile!,
                    );
                    Navigator.pop(context);
                    await _fetchContents();
                    _showSnackBar('تم إضافة المحتوى', Colors.green);
                  } catch (e) {
                    _showSnackBar('خطأ في إضافة المحتوى: $e', Colors.red);
                  }
                } else {
                  _showSnackBar('يرجى ملء جميع الحقول المطلوبة', Colors.orange);
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
              ),
              child: const Text('إضافة', style: TextStyle(color: Colors.white)),
            ),
          ],
        ),
      ),
    );
  }
}

class VideoPlayerWidget extends StatefulWidget {
  final String videoUrl;

  const VideoPlayerWidget({super.key, required this.videoUrl});

  @override
  _VideoPlayerWidgetState createState() => _VideoPlayerWidgetState();
}

class _VideoPlayerWidgetState extends State<VideoPlayerWidget> {
  late VideoPlayerController _controller;
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    _controller = VideoPlayerController.network(widget.videoUrl)
      ..initialize().then((_) {
        setState(() => _isInitialized = true);
        _controller.setLooping(true);
      }).catchError((e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في تحميل الفيديو: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return _isInitialized
        ? AspectRatio(
      aspectRatio: _controller.value.aspectRatio,
      child: Stack(
        alignment: Alignment.bottomCenter,
        children: [
          VideoPlayer(_controller),
          IconButton(
            icon: Icon(
              _controller.value.isPlaying ? Icons.pause : Icons.play_arrow,
              color: Colors.white,
              size: 40,
            ),
            onPressed: () {
              setState(() {
                _controller.value.isPlaying ? _controller.pause() : _controller.play();
              });
            },
          ),
        ],
      ),
    )
        : Container(
      height: 200,
      color: Colors.grey[300],
      child: const Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
        ),
      ),
    );
  }
}