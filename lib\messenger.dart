import 'dart:io';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:animate_do/animate_do.dart';
import 'package:geolocator/geolocator.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'appstate.dart';
import 'package:socket_io_client/socket_io_client.dart' as io;
import 'package:image_picker/image_picker.dart';
import 'package:file_picker/file_picker.dart';
import 'services/audio_service.dart';
import 'package:emoji_picker_flutter/emoji_picker_flutter.dart';
import 'package:chewie/chewie.dart';
import 'package:audioplayers/audioplayers.dart';
import 'main.dart';
import 'models/messenger_models.dart';
import 'models/group_model.dart';
import 'models/message_model.dart';
import 'games_page.dart';
import 'FriendsPage.dart';

// Messenger main page
class Messenger extends StatelessWidget {
  const Messenger({super.key});

  @override
  Widget build(BuildContext context) {
    final appState = Provider.of<AppState>(context);
    return const Scaffold(
      body: SocialHomePage(),
    );
  }
}

// SocialHomePage
class SocialHomePage extends StatefulWidget {
  const SocialHomePage({super.key});

  @override
  _SocialHomePageState createState() => _SocialHomePageState();
}

class _SocialHomePageState extends State<SocialHomePage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  Position? currentPosition;
  bool isLoading = true;
  List<User> users = [];
  List<Group> groups = [];
  List<Message> unreadMessages = [];
  List<User> favoriteUsers = [];
  late io.Socket socket;
  final TextEditingController _searchController = TextEditingController();
  List<User> filteredUsers = [];
  List<Group> filteredGroups = [];
  List<BusinessContact> businessContacts = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
    final appState = Provider.of<AppState>(context, listen: false);
    socket = appState.socket ?? _initSocket();
    _initializeData();
    _searchController.addListener(_filterItems);
  }

  io.Socket _initSocket() {
    final appState = Provider.of<AppState>(context, listen: false);
    io.Socket newSocket = io.io(
      AppState.getBackendUrl(),
      io.OptionBuilder().setTransports(['websocket']).setExtraHeaders(
          {'Authorization': 'Bearer ${appState.token}'}).build(),
    );
    newSocket.onConnect((_) => print('Connected to WebSocket'));
    newSocket.onDisconnect((_) => print('Disconnected from WebSocket'));
    newSocket.onConnectError((error) {
      appState.showSnackBar('خطأ في اتصال WebSocket: $error', Colors.red);
    });
    newSocket.on('game_invite', (data) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
                'دعوة للعب ${data['gameTitle']} من ${data['senderName'] ?? 'مستخدم'}'),
            backgroundColor: Colors.green,
            action: SnackBarAction(
              label: 'قبول',
              textColor: Colors.white,
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => const GamesPage()),
                );
              },
            ),
            duration: const Duration(seconds: 10),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      }
    });
    appState.socket = newSocket;
    return newSocket;
  }

  // Helper method to get text style
  TextStyle _getTextStyle(BuildContext context,
      {bool isTitle = false, double? fontSize}) {
    final theme = Theme.of(context);
    if (isTitle) {
      return TextStyle(
        color: theme.textTheme.titleLarge?.color ?? Colors.black,
        fontFamily: 'Tajawal',
        fontWeight: FontWeight.bold,
        fontSize: fontSize,
      );
    } else {
      return TextStyle(
        color: theme.textTheme.bodyLarge?.color ?? Colors.black,
        fontFamily: 'Tajawal',
        fontSize: fontSize ?? 16,
      );
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    socket.off('user_status');
    socket.off('private_message');
    socket.off('group_message');
    super.dispose();
  }

  Future<void> _initializeData() async {
    setState(() => isLoading = true);
    await _getCurrentLocation();
    await _fetchUsers();
    await _fetchGroups();
    await _fetchBusinessContacts();
    setState(() {
      filteredUsers = users;
      filteredGroups = groups;
      isLoading = false;
    });
  }

  Future<void> _fetchUsers() async {
    final appState = Provider.of<AppState>(context, listen: false);
    try {
      final response = await http.get(
        Uri.parse('${AppState.getBackendUrl()}/api/users'),
        headers: {'Authorization': 'Bearer ${appState.token}'},
      );
      if (response.statusCode == 200) {
        final List<dynamic> userData = json.decode(response.body);
        setState(() {
          users = userData
              .map((data) => User.fromJson({
                    'id': data['_id'],
                    'name': data['username'],
                    'job': data['job'] ?? 'لا توجد وظيفة',
                    'latitude': data['latitude']?.toDouble() ?? 0.0,
                    'longitude': data['longitude']?.toDouble() ?? 0.0,
                    'avatarUrl': data['avatarUrl'] != null
                        ? '${AppState.getBackendUrl()}${data['avatarUrl']}'
                        : 'https://via.placeholder.com/150',
                    'isOnline': data['isOnline'] ?? false,
                    'isFavorite': data['isFavorite'] ?? false,
                    'shareLocation': data['shareLocation'] ?? false,
                  }))
              .toList();
          filteredUsers = users;
        });
      } else {
        throw Exception('Failed to fetch users: ${response.body}');
      }
    } catch (e) {
      appState.showSnackBar('خطأ في جلب المستخدمين: $e', Colors.red);
    }
  }

  Future<void> _fetchGroups() async {
    final appState = Provider.of<AppState>(context, listen: false);
    try {
      final response = await http.get(
        Uri.parse('${AppState.getBackendUrl()}/api/groups'),
        headers: {'Authorization': 'Bearer ${appState.token}'},
      );
      if (response.statusCode == 200) {
        final List<dynamic> groupData = json.decode(response.body);
        setState(() {
          groups = groupData.map((data) => Group.fromJson(data)).toList();
          filteredGroups = groups;
        });
      } else {
        throw Exception('Failed to fetch groups: ${response.body}');
      }
    } catch (e) {
      appState.showSnackBar('خطأ في جلب المجموعات: $e', Colors.red);
    }
  }

  Future<void> _fetchBusinessContacts() async {
    final appState = Provider.of<AppState>(context, listen: false);
    try {
      final response = await http.get(
        Uri.parse('${AppState.getBackendUrl()}/api/business-contacts'),
        headers: {'Authorization': 'Bearer ${appState.token}'},
      );
      if (response.statusCode == 200) {
        final List<dynamic> contactData = json.decode(response.body);
        setState(() {
          businessContacts = contactData
              .map((data) => BusinessContact.fromJson(data))
              .where((contact) {
            if (currentPosition == null) return false;
            final distance = Geolocator.distanceBetween(
              currentPosition!.latitude,
              currentPosition!.longitude,
              contact.latitude,
              contact.longitude,
            );
            return distance <= 5000; // Within 5km
          }).toList();
        });
      } else {
        throw Exception('Failed to fetch business contacts: ${response.body}');
      }
    } catch (e) {
      appState.showSnackBar('خطأ في جلب جهات الاتصال التجارية: $e', Colors.red);
    }
  }

  Future<void> _getCurrentLocation() async {
    try {
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        context
            .read<AppState>()
            .showSnackBar('يرجى تفعيل خدمات الموقع', Colors.red);
        return;
      }
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) return;
      }
      currentPosition = await Geolocator.getCurrentPosition(
          desiredAccuracy: LocationAccuracy.high);
      final appState = Provider.of<AppState>(context, listen: false);
      if (appState.shareLocation) {
        await _updateUserLocation(
            currentPosition!.latitude, currentPosition!.longitude);
      }
      setState(() {});
    } catch (e) {
      context.read<AppState>().showSnackBar('خطأ في الموقع: $e', Colors.red);
    }
  }

  Future<void> _updateUserLocation(double latitude, double longitude) async {
    final appState = Provider.of<AppState>(context, listen: false);
    try {
      final response = await http.patch(
        Uri.parse('${AppState.getBackendUrl()}/api/users/me'),
        headers: {
          'Authorization': 'Bearer ${appState.token}',
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'latitude': latitude,
          'longitude': longitude,
        }),
      );
      if (response.statusCode != 200) {
        throw Exception('Failed to update location: ${response.body}');
      }
    } catch (e) {
      appState.showSnackBar('خطأ في تحديث الموقع: $e', Colors.red);
    }
  }

  void _filterItems() {
    final query = _searchController.text.toLowerCase();
    setState(() {
      if (_tabController.index == 0) {
        filteredUsers = users
            .where((user) => user.name.toLowerCase().contains(query))
            .toList();
      } else if (_tabController.index == 2) {
        filteredGroups = groups
            .where((group) => group.name.toLowerCase().contains(query))
            .toList();
      }
    });
  }

  void _openChat(BuildContext context, String userId, String userName) {
    final appState = Provider.of<AppState>(context, listen: false);
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ChatPage(
          recipientId: userId,
          userName: userName,
          currentUserId: appState.username ?? 'unknown',
          socket: socket,
        ),
      ),
    );
  }

  void _openGroupChat(BuildContext context, String groupId, String groupName) {
    final appState = Provider.of<AppState>(context, listen: false);
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => GroupChatPage(
          groupId: groupId,
          groupName: groupName,
          currentUserId: appState.username ?? 'unknown',
          socket: socket,
        ),
      ),
    );
  }

  void _addNewGroup() {
    showDialog(
      context: context,
      builder: (context) => AddGroupDialog(
        onGroupAdded: (group) {
          setState(() {
            groups.add(group);
            filteredGroups = groups;
          });
        },
      ),
    );
  }

  void _openFriendsPage() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const FriendsPage()),
    );
  }

  void _openBusinessContactsPage() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => BusinessContactsPage(
          businessContacts: businessContacts,
          currentPosition: currentPosition,
        ),
      ),
    );
  }

  void _openMap(double latitude, double longitude, String userName) async {
    final url = 'https://www.google.com/maps?q=$latitude,$longitude';
    if (await canLaunch(url)) {
      await launch(url);
    } else {
      Provider.of<AppState>(context, listen: false)
          .showSnackBar('لا يمكن فتح الخريطة', Colors.red);
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(120),
        child: SafeArea(
          child: Column(
            children: [
              Container(
                padding: const EdgeInsets.fromLTRB(12.0, 8.0, 12.0, 8.0),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      theme.primaryColor,
                      theme.primaryColor.withOpacity(0.8)
                    ],
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.2),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    ElasticIn(
                      duration: const Duration(milliseconds: 500),
                      child: SizedBox(
                        width: MediaQuery.of(context).size.width * 0.65,
                        child: TextField(
                          controller: _searchController,
                          style: const TextStyle(
                            color: Colors.white,
                            fontFamily: 'Tajawal',
                            fontSize: 14,
                          ),
                          decoration: InputDecoration(
                            hintText: 'ابحث عن محادثة...',
                            hintStyle: TextStyle(
                              color: Colors.white.withOpacity(0.6),
                              fontFamily: 'Tajawal',
                              fontSize: 14,
                            ),
                            filled: true,
                            fillColor: Colors.grey[800],
                            prefixIcon: const Icon(Icons.search,
                                color: Colors.white, size: 18),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(25),
                              borderSide: BorderSide.none,
                            ),
                            contentPadding: const EdgeInsets.symmetric(
                                vertical: 10, horizontal: 20),
                          ),
                        ),
                      ),
                    ),
                    Row(
                      children: [
                        BounceInDown(
                          duration: const Duration(milliseconds: 600),
                          child: IconButton(
                            icon: const Icon(Icons.store,
                                color: Colors.white, size: 20),
                            onPressed: _openBusinessContactsPage,
                            tooltip: 'جهات الاتصال التجارية',
                          ),
                        ),
                        BounceInDown(
                          duration: const Duration(milliseconds: 600),
                          child: IconButton(
                            icon: const Icon(Icons.people,
                                color: Colors.white, size: 20),
                            onPressed: _openFriendsPage,
                            tooltip: 'الأصدقاء',
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              Container(
                color: Colors.transparent,
                child: TabBar(
                  controller: _tabController,
                  indicatorColor: theme.primaryColor,
                  labelColor: Colors.green,
                  unselectedLabelColor: Colors.amber.withOpacity(0.6),
                  labelStyle:
                      const TextStyle(fontFamily: 'Tajawal', fontSize: 14),
                  unselectedLabelStyle:
                      const TextStyle(fontFamily: 'Tajawal', fontSize: 14),
                  indicatorPadding: const EdgeInsets.symmetric(horizontal: 8.0),
                  tabs: const [
                    Tab(text: 'الكل'),
                    Tab(text: 'غير مقروء'),
                    Tab(text: 'المجموعات'),
                    Tab(text: 'المفضلة'),
                    Tab(text: 'الألعاب'),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
      body: Container(
        color: Theme.of(context).scaffoldBackgroundColor,
        child: isLoading
            ? Center(
                child: CircularProgressIndicator(color: theme.primaryColor))
            : SafeArea(
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    // Tab 0: All Chats
                    SingleChildScrollView(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const SizedBox(height: 16),
                          filteredUsers.isEmpty
                              ? Center(
                                  child: Text(
                                    'لا توجد محادثات',
                                    style: _getTextStyle(context, fontSize: 18),
                                  ),
                                )
                              : ListView.builder(
                                  shrinkWrap: true,
                                  physics: const NeverScrollableScrollPhysics(),
                                  itemCount: filteredUsers.length,
                                  itemBuilder: (context, index) {
                                    final user = filteredUsers[index];
                                    return FadeInUp(
                                      duration:
                                          const Duration(milliseconds: 300),
                                      child: Card(
                                        color: theme.cardColor,
                                        shape: RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(15),
                                        ),
                                        margin: const EdgeInsets.symmetric(
                                            horizontal: 12, vertical: 6),
                                        elevation: 5,
                                        child: ListTile(
                                          leading: Stack(
                                            children: [
                                              CircleAvatar(
                                                backgroundImage: NetworkImage(
                                                    user.avatarUrl),
                                                radius: 24,
                                              ),
                                              if (user.isOnline)
                                                Positioned(
                                                  right: 0,
                                                  bottom: 0,
                                                  child: Container(
                                                    width: 12,
                                                    height: 12,
                                                    decoration:
                                                        const BoxDecoration(
                                                      color: Colors.green,
                                                      shape: BoxShape.circle,
                                                      border: Border(
                                                        left: BorderSide(
                                                            color: Colors.white,
                                                            width: 2),
                                                        top: BorderSide(
                                                            color: Colors.white,
                                                            width: 2),
                                                        right: BorderSide(
                                                            color: Colors.white,
                                                            width: 2),
                                                        bottom: BorderSide(
                                                            color: Colors.white,
                                                            width: 2),
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                            ],
                                          ),
                                          title: Text(user.name,
                                              style: _getTextStyle(context,
                                                  isTitle: true)),
                                          subtitle: Row(
                                            children: [
                                              Expanded(
                                                child: Text(user.job,
                                                    style: TextStyle(
                                                        color: theme
                                                                .textTheme
                                                                .bodyMedium
                                                                ?.color
                                                                ?.withOpacity(
                                                                    0.8) ??
                                                            Colors.grey[600])),
                                              ),
                                              if (user.shareLocation &&
                                                  user.latitude != 0.0 &&
                                                  user.longitude != 0.0)
                                                GestureDetector(
                                                  onTap: () => _openMap(
                                                      user.latitude,
                                                      user.longitude,
                                                      user.name),
                                                  child: Text(
                                                    '(${user.latitude.toStringAsFixed(2)}, ${user.longitude.toStringAsFixed(2)})',
                                                    style: TextStyle(
                                                        color:
                                                            theme.primaryColor,
                                                        fontFamily: 'Tajawal'),
                                                  ),
                                                ),
                                            ],
                                          ),
                                          trailing: user.isOnline
                                              ? ZoomIn(
                                                  duration: const Duration(
                                                      milliseconds: 300),
                                                  child: const Icon(
                                                      Icons.circle,
                                                      color: Colors.green,
                                                      size: 12))
                                              : null,
                                          onTap: () => _openChat(
                                              context, user.id, user.name),
                                          contentPadding:
                                              const EdgeInsets.symmetric(
                                                  horizontal: 16, vertical: 8),
                                        ),
                                      ),
                                    );
                                  },
                                ),
                          const SizedBox(height: 80), // Space for FAB
                        ],
                      ),
                    ),
                    // Tab 1: Unread Messages
                    SingleChildScrollView(
                      child: Column(
                        children: [
                          const SizedBox(height: 16),
                          unreadMessages.isEmpty
                              ? Center(
                                  child: Text(
                                    'لا توجد رسائل غير مقروءة',
                                    style: _getTextStyle(context, fontSize: 18),
                                  ),
                                )
                              : ListView.builder(
                                  shrinkWrap: true,
                                  physics: const NeverScrollableScrollPhysics(),
                                  itemCount: unreadMessages.length,
                                  itemBuilder: (context, index) {
                                    final message = unreadMessages[index];
                                    return FadeInUp(
                                      duration:
                                          const Duration(milliseconds: 300),
                                      child: Card(
                                        color: theme.cardColor,
                                        shape: RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(15),
                                        ),
                                        margin: const EdgeInsets.symmetric(
                                            horizontal: 12, vertical: 6),
                                        elevation: 5,
                                        child: ListTile(
                                          title: Text(
                                              'رسالة من ${message.senderId}',
                                              style: _getTextStyle(context,
                                                  isTitle: true)),
                                          subtitle: Text(message.content,
                                              style: TextStyle(
                                                  color: theme.textTheme
                                                          .bodyMedium?.color
                                                          ?.withOpacity(0.8) ??
                                                      Colors.grey[600],
                                                  fontFamily: 'Tajawal')),
                                          onTap: () => _openChat(context,
                                              message.senderId, 'اسم المستخدم'),
                                          contentPadding:
                                              const EdgeInsets.symmetric(
                                                  horizontal: 16, vertical: 8),
                                        ),
                                      ),
                                    );
                                  },
                                ),
                          const SizedBox(height: 80),
                        ],
                      ),
                    ),
                    // Tab 2: Groups
                    SingleChildScrollView(
                      child: Column(
                        children: [
                          const SizedBox(height: 16),
                          filteredGroups.isEmpty
                              ? Center(
                                  child: Text(
                                    'لا توجد مجموعات',
                                    style: _getTextStyle(context, fontSize: 18),
                                  ),
                                )
                              : ListView.builder(
                                  shrinkWrap: true,
                                  physics: const NeverScrollableScrollPhysics(),
                                  itemCount: filteredGroups.length,
                                  itemBuilder: (context, index) {
                                    final group = filteredGroups[index];
                                    return FadeInUp(
                                      duration:
                                          const Duration(milliseconds: 300),
                                      child: Card(
                                        color: theme.cardColor,
                                        shape: RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(15),
                                        ),
                                        margin: const EdgeInsets.symmetric(
                                            horizontal: 12, vertical: 6),
                                        elevation: 5,
                                        child: ListTile(
                                          leading: CircleAvatar(
                                            backgroundImage:
                                                NetworkImage(group.iconUrl),
                                            radius: 24,
                                          ),
                                          title: Text(group.name,
                                              style: _getTextStyle(context,
                                                  isTitle: true)),
                                          subtitle: Text(
                                              '${group.membersCount} أعضاء',
                                              style: TextStyle(
                                                  color: theme.textTheme
                                                          .bodyMedium?.color
                                                          ?.withOpacity(0.8) ??
                                                      Colors.grey[600],
                                                  fontFamily: 'Tajawal')),
                                          onTap: () => _openGroupChat(
                                              context, group.id, group.name),
                                          contentPadding:
                                              const EdgeInsets.symmetric(
                                                  horizontal: 16, vertical: 8),
                                        ),
                                      ),
                                    );
                                  },
                                ),
                          const SizedBox(height: 80),
                        ],
                      ),
                    ),
                    // Tab 3: Favorites
                    SingleChildScrollView(
                      child: Column(
                        children: [
                          const SizedBox(height: 16),
                          favoriteUsers.isEmpty
                              ? Center(
                                  child: Text(
                                    'لا توجد مفضلات',
                                    style: _getTextStyle(context, fontSize: 18),
                                  ),
                                )
                              : ListView.builder(
                                  shrinkWrap: true,
                                  physics: const NeverScrollableScrollPhysics(),
                                  itemCount: favoriteUsers.length,
                                  itemBuilder: (context, index) {
                                    final user = favoriteUsers[index];
                                    return FadeInUp(
                                      duration:
                                          const Duration(milliseconds: 300),
                                      child: Card(
                                        color: theme.cardColor,
                                        shape: RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(15),
                                        ),
                                        margin: const EdgeInsets.symmetric(
                                            horizontal: 12, vertical: 6),
                                        elevation: 5,
                                        child: ListTile(
                                          leading: CircleAvatar(
                                            backgroundImage:
                                                NetworkImage(user.avatarUrl),
                                            radius: 24,
                                          ),
                                          title: Text(user.name,
                                              style: _getTextStyle(context,
                                                  isTitle: true)),
                                          subtitle: Row(
                                            children: [
                                              Expanded(
                                                child: Text(user.job,
                                                    style: TextStyle(
                                                        color: theme
                                                                .textTheme
                                                                .bodyMedium
                                                                ?.color
                                                                ?.withOpacity(
                                                                    0.8) ??
                                                            Colors.grey[600])),
                                              ),
                                              if (user.shareLocation &&
                                                  user.latitude != 0.0 &&
                                                  user.longitude != 0.0)
                                                GestureDetector(
                                                  onTap: () => _openMap(
                                                      user.latitude,
                                                      user.longitude,
                                                      user.name),
                                                  child: Text(
                                                    '(${user.latitude.toStringAsFixed(2)}, ${user.longitude.toStringAsFixed(2)})',
                                                    style: TextStyle(
                                                        color:
                                                            theme.primaryColor,
                                                        fontFamily: 'Tajawal'),
                                                  ),
                                                ),
                                            ],
                                          ),
                                          onTap: () => _openChat(
                                              context, user.id, user.name),
                                          contentPadding:
                                              const EdgeInsets.symmetric(
                                                  horizontal: 16, vertical: 8),
                                        ),
                                      ),
                                    );
                                  },
                                ),
                          const SizedBox(height: 80),
                        ],
                      ),
                    ),
                    // Tab 4: Games
                    const GamesPage(),
                  ],
                ),
              ),
      ),
      floatingActionButton: ZoomIn(
        duration: const Duration(milliseconds: 500),
        child: FloatingActionButton(
          onPressed: _addNewGroup,
          backgroundColor: theme.primaryColor,
          elevation: 6,
          heroTag: 'addGroup',
          child: const Icon(Icons.group_add, color: Colors.white),
        ),
      ),
    );
  }
}

// BusinessContact Model
class BusinessContact {
  final String id;
  final String businessName;
  final String phoneNumber;
  final double latitude;
  final double longitude;

  BusinessContact({
    required this.id,
    required this.businessName,
    required this.phoneNumber,
    required this.latitude,
    required this.longitude,
  });

  factory BusinessContact.fromJson(Map<String, dynamic> json) {
    return BusinessContact(
      id: json['_id'] ?? '',
      businessName: json['businessName'] ?? 'غير معروف',
      phoneNumber: json['phoneNumber'] ?? '',
      latitude: json['latitude']?.toDouble() ?? 0.0,
      longitude: json['longitude']?.toDouble() ?? 0.0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'businessName': businessName,
      'phoneNumber': phoneNumber,
      'latitude': latitude,
      'longitude': longitude,
    };
  }
}

// BusinessContactsPage
class BusinessContactsPage extends StatefulWidget {
  final List<BusinessContact> businessContacts;
  final Position? currentPosition;

  const BusinessContactsPage({
    super.key,
    required this.businessContacts,
    required this.currentPosition,
  });

  @override
  State<BusinessContactsPage> createState() => _BusinessContactsPageState();
}

class _BusinessContactsPageState extends State<BusinessContactsPage> {
  final TextEditingController _searchController = TextEditingController();
  List<BusinessContact> _filteredContacts = [];
  bool _isSearching = false;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _filteredContacts = List.from(widget.businessContacts);
  }

  void _filterContacts(String query) {
    setState(() {
      _searchQuery = query;
      if (query.isEmpty) {
        _filteredContacts = List.from(widget.businessContacts);
      } else {
        _filteredContacts = widget.businessContacts.where((contact) {
          final nameMatch =
              contact.businessName.toLowerCase().contains(query.toLowerCase());
          final phoneMatch =
              contact.phoneNumber.toLowerCase().contains(query.toLowerCase());
          return nameMatch || phoneMatch;
        }).toList();
      }
    });
  }

  void _toggleSearch() {
    setState(() {
      _isSearching = !_isSearching;
      if (!_isSearching) {
        _searchController.clear();
        _filterContacts('');
      }
    });
  }

  void _callBusiness(String phoneNumber) async {
    final url = 'tel:$phoneNumber';
    if (await canLaunch(url)) {
      await launch(url);
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
              content: Text('لا يمكن الاتصال بهذا الرقم',
                  style: TextStyle(fontFamily: 'Tajawal'))),
        );
      }
    }
  }

  void _openMap(double latitude, double longitude) async {
    final url = 'https://www.google.com/maps?q=$latitude,$longitude';
    if (await canLaunch(url)) {
      await launch(url);
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
              content: Text('لا يمكن فتح الخريطة',
                  style: TextStyle(fontFamily: 'Tajawal'))),
        );
      }
    }
  }

  // تصفية حسب المسافة
  void _filterByDistance() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تصفية حسب المسافة',
            style: TextStyle(fontFamily: 'Tajawal')),
        content: StatefulBuilder(
          builder: (context, setDialogState) {
            double maxDistance = 50.0;
            return Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Slider(
                  value: maxDistance,
                  min: 1,
                  max: 100,
                  divisions: 10,
                  label: '${maxDistance.round()} كم',
                  onChanged: (value) {
                    setDialogState(() {
                      maxDistance = value;
                    });
                  },
                ),
                Text('المسافة القصوى: ${maxDistance.round()} كم',
                    style: const TextStyle(fontFamily: 'Tajawal')),
              ],
            );
          },
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
            },
            child: const Text('إلغاء', style: TextStyle(fontFamily: 'Tajawal')),
          ),
          TextButton(
            onPressed: () {
              // تطبيق التصفية حسب المسافة
              Navigator.pop(context);
            },
            child: const Text('تطبيق', style: TextStyle(fontFamily: 'Tajawal')),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final appState = Provider.of<AppState>(context);

    return Scaffold(
      appBar: AppBar(
        title: _isSearching
            ? TextField(
                controller: _searchController,
                onChanged: _filterContacts,
                style:
                    const TextStyle(color: Colors.white, fontFamily: 'Tajawal'),
                decoration: const InputDecoration(
                  hintText: 'ابحث عن جهة اتصال...',
                  hintStyle:
                      TextStyle(color: Colors.white70, fontFamily: 'Tajawal'),
                  border: InputBorder.none,
                ),
                autofocus: true,
              )
            : const Text('جهات الاتصال التجارية',
                style: TextStyle(fontFamily: 'Tajawal', color: Colors.white)),
        backgroundColor: theme.primaryColor,
        elevation: 0,
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [theme.primaryColor, theme.primaryColor.withOpacity(0.8)],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
          ),
        ),
        actions: [
          // زر البحث
          IconButton(
            icon: Icon(_isSearching ? Icons.close : Icons.search,
                color: Colors.white),
            onPressed: _toggleSearch,
            tooltip: _isSearching ? 'إغلاق البحث' : 'بحث',
          ),
          // زر التصفية حسب المسافة
          IconButton(
            icon: const Icon(Icons.filter_list, color: Colors.white),
            onPressed: _filterByDistance,
            tooltip: 'تصفية حسب المسافة',
          ),
        ],
      ),
      body: Container(
        color: Theme.of(context).scaffoldBackgroundColor,
        child: Column(
          children: [
            // عرض نتائج البحث
            if (_searchQuery.isNotEmpty)
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Row(
                  children: [
                    const Icon(Icons.search, color: Colors.white70),
                    const SizedBox(width: 8),
                    Text(
                      'نتائج البحث لـ "$_searchQuery": ${_filteredContacts.length}',
                      style: const TextStyle(
                          color: Colors.white70, fontFamily: 'Tajawal'),
                    ),
                    const Spacer(),
                    if (_searchQuery.isNotEmpty)
                      TextButton(
                        onPressed: () {
                          setState(() {
                            _searchController.clear();
                            _filterContacts('');
                          });
                        },
                        child: const Text('مسح',
                            style: TextStyle(fontFamily: 'Tajawal')),
                      ),
                  ],
                ),
              ),

            // قائمة جهات الاتصال
            Expanded(
              child: _filteredContacts.isEmpty
                  ? Center(
                      child: Text(
                        _searchQuery.isEmpty
                            ? 'لا توجد جهات اتصال تجارية قريبة'
                            : 'لا توجد نتائج مطابقة للبحث',
                        style: const TextStyle(
                            color: Colors.white,
                            fontFamily: 'Tajawal',
                            fontSize: 18),
                      ),
                    )
                  : ListView.builder(
                      padding: const EdgeInsets.all(16),
                      itemCount: _filteredContacts.length,
                      itemBuilder: (context, index) {
                        final contact = _filteredContacts[index];
                        double distance = widget.currentPosition != null
                            ? Geolocator.distanceBetween(
                                  widget.currentPosition!.latitude,
                                  widget.currentPosition!.longitude,
                                  contact.latitude,
                                  contact.longitude,
                                ) /
                                1000 // Convert to kilometers
                            : 0.0;
                        return FadeInUp(
                          duration: const Duration(milliseconds: 300),
                          delay: Duration(milliseconds: index * 50),
                          child: Card(
                            color: Colors.grey[850],
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(15),
                            ),
                            elevation: 5,
                            margin: const EdgeInsets.symmetric(vertical: 6),
                            child: ListTile(
                              leading: CircleAvatar(
                                backgroundColor: theme.primaryColor,
                                radius: 24,
                                child: const Icon(Icons.store,
                                    color: Colors.white),
                              ),
                              title: Text(
                                contact.businessName,
                                style: const TextStyle(
                                    color: Colors.white,
                                    fontFamily: 'Tajawal',
                                    fontWeight: FontWeight.bold),
                              ),
                              subtitle: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    contact.phoneNumber,
                                    style: TextStyle(
                                        color: Colors.white.withOpacity(0.6),
                                        fontFamily: 'Tajawal'),
                                  ),
                                  if (widget.currentPosition != null)
                                    Text(
                                      'المسافة: ${distance.toStringAsFixed(2)} كم',
                                      style: TextStyle(
                                          color: Colors.white.withOpacity(0.6),
                                          fontFamily: 'Tajawal'),
                                    ),
                                ],
                              ),
                              trailing: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  IconButton(
                                    icon: const Icon(Icons.call,
                                        color: Colors.green),
                                    onPressed: () =>
                                        _callBusiness(contact.phoneNumber),
                                  ),
                                  IconButton(
                                    icon: const Icon(Icons.map,
                                        color: Colors.blueAccent),
                                    onPressed: () => _openMap(
                                        contact.latitude, contact.longitude),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        );
                      },
                    ),
            ),
          ],
        ),
      ),
      floatingActionButton: ZoomIn(
        duration: const Duration(milliseconds: 500),
        child: FloatingActionButton(
          onPressed: () {
            showDialog(
              context: context,
              builder: (context) => AddBusinessContactDialog(
                onContactAdded: (contact) {
                  setState(() {
                    _filteredContacts.add(contact);
                  });
                  Provider.of<AppState>(context, listen: false).showSnackBar(
                      'تم إضافة جهة الاتصال التجارية', Colors.green);
                },
              ),
            );
          },
          backgroundColor: theme.primaryColor,
          elevation: 6,
          heroTag: 'addBusiness',
          child: const Icon(Icons.add_business, color: Colors.white),
        ),
      ),
    );
  }
}

// AddBusinessContactDialog
class AddBusinessContactDialog extends StatefulWidget {
  final Function(BusinessContact) onContactAdded;

  const AddBusinessContactDialog({required this.onContactAdded, super.key});

  @override
  _AddBusinessContactDialogState createState() =>
      _AddBusinessContactDialogState();
}

class _AddBusinessContactDialogState extends State<AddBusinessContactDialog> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _phoneController = TextEditingController();
  Position? _currentPosition;
  io.Socket? socket;

  @override
  void initState() {
    super.initState();
    _getCurrentLocation();
    socket = _initSocket();
  }

  io.Socket _initSocket() {
    io.Socket newSocket = io.io(
      AppState.getBackendUrl(),
      io.OptionBuilder()
          .setTransports(['websocket'])
          .disableAutoConnect()
          .build(),
    );
    newSocket.connect();
    return newSocket;
  }

  Future<void> _getCurrentLocation() async {
    try {
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        Provider.of<AppState>(context, listen: false)
            .showSnackBar('يرجى تفعيل خدمات الموقع', Colors.red);
        return;
      }
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) return;
      }
      _currentPosition = await Geolocator.getCurrentPosition(
          desiredAccuracy: LocationAccuracy.high);
      setState(() {});
    } catch (e) {
      Provider.of<AppState>(context, listen: false)
          .showSnackBar('خطأ في الموقع: $e', Colors.red);
    }
  }

  Future<void> _saveContact() async {
    if (_formKey.currentState!.validate() && _currentPosition != null) {
      final contact = BusinessContact(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        businessName: _nameController.text,
        phoneNumber: _phoneController.text,
        latitude: _currentPosition!.latitude,
        longitude: _currentPosition!.longitude,
      );
      try {
        final appState = Provider.of<AppState>(context, listen: false);
        final response = await http.post(
          Uri.parse('${AppState.getBackendUrl()}/api/business-contacts'),
          headers: {
            'Authorization': 'Bearer ${appState.token}',
            'Content-Type': 'application/json',
          },
          body: jsonEncode(contact.toJson()),
        );
        if (response.statusCode == 201) {
          widget.onContactAdded(contact);
          Navigator.pop(context);
          appState.showSnackBar('تم إضافة جهة الاتصال بنجاح', Colors.green);
        } else {
          throw Exception('فشل في إضافة جهة الاتصال: ${response.body}');
        }
      } catch (e) {
        Provider.of<AppState>(context, listen: false)
            .showSnackBar('خطأ في إضافة جهة الاتصال: $e', Colors.red);
      }
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return AlertDialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      backgroundColor: Colors.grey[800],
      title: Text('إضافة جهة اتصال تجارية',
          style: TextStyle(color: theme.primaryColor, fontFamily: 'Tajawal')),
      content: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextFormField(
              controller: _nameController,
              style:
                  const TextStyle(color: Colors.white, fontFamily: 'Tajawal'),
              decoration: InputDecoration(
                labelText: 'اسم العمل',
                labelStyle: TextStyle(
                    color: Colors.white.withOpacity(0.6),
                    fontFamily: 'Tajawal'),
                filled: true,
                fillColor: Colors.grey[800],
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide.none,
                ),
              ),
              validator: (value) =>
                  value == null || value.isEmpty ? 'الاسم مطلوب' : null,
            ),
            const SizedBox(height: 12),
            TextFormField(
              controller: _phoneController,
              style:
                  const TextStyle(color: Colors.white, fontFamily: 'Tajawal'),
              decoration: InputDecoration(
                labelText: 'رقم الهاتف',
                labelStyle: TextStyle(
                    color: Colors.white.withOpacity(0.6),
                    fontFamily: 'Tajawal'),
                filled: true,
                fillColor: Colors.grey[800],
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide.none,
                ),
              ),
              keyboardType: TextInputType.phone,
              validator: (value) =>
                  value == null || value.isEmpty ? 'رقم الهاتف مطلوب' : null,
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('إلغاء',
              style: TextStyle(color: Colors.grey, fontFamily: 'Tajawal')),
        ),
        ElevatedButton(
          onPressed: _saveContact,
          style: ElevatedButton.styleFrom(
            backgroundColor: theme.primaryColor,
            shape:
                RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          ),
          child: const Text('إضافة',
              style: TextStyle(color: Colors.white, fontFamily: 'Tajawal')),
        ),
      ],
    );
  }
}

// FriendsPage, AddGroupDialog, ChatPage, and GroupChatPage remain unchanged
// Include them here for completeness
class FriendsPage extends StatefulWidget {
  const FriendsPage({super.key});

  @override
  State<FriendsPage> createState() => _FriendsPageState();
}

class _FriendsPageState extends State<FriendsPage> {
  List<User> friends = [];
  bool isLoading = true;
  bool hasError = false;
  String errorMessage = '';
  final TextEditingController _searchController = TextEditingController();
  List<User> filteredFriends = [];
  late io.Socket socket;

  @override
  void initState() {
    super.initState();
    final appState = Provider.of<AppState>(context, listen: false);
    socket = _initSocket();
    fetchFriends();
    _searchController.addListener(_filterFriends);
    _initializeSocketListeners();
  }

  io.Socket _initSocket() {
    final appState = Provider.of<AppState>(context, listen: false);
    io.Socket newSocket = io.io(
      AppState.getBackendUrl(),
      io.OptionBuilder().setTransports(['websocket']).setExtraHeaders(
          {'Authorization': 'Bearer ${appState.token}'}).build(),
    );
    newSocket.onConnect((_) => print('Connected to WebSocket'));
    newSocket.onDisconnect((_) => print('Disconnected from WebSocket'));
    appState.socket = newSocket;
    return newSocket;
  }

  void _initializeSocketListeners() {
    socket.on('user_status', (data) {
      setState(() {
        final userId = data['userId']?.toString();
        final isOnline = data['isOnline'] as bool? ?? false;
        final friend = friends.firstWhere(
          (f) => f.id == userId,
          orElse: () => User(
            id: '',
            name: '',
            job: '',
            avatarUrl: '',
            isOnline: false,
            latitude: 0.0,
            longitude: 0.0,
            isFavorite: false,
            shareLocation: false,
          ),
        );
        if (friend.id.isNotEmpty) {
          friend.isOnline = isOnline;
        }
      });
    });
  }

  Future<void> fetchFriends() async {
    setState(() {
      isLoading = true;
      hasError = false;
      errorMessage = '';
    });
    try {
      final appState = Provider.of<AppState>(context, listen: false);
      final response = await http.get(
        Uri.parse('${AppState.getBackendUrl()}/api/friends'),
        headers: {'Authorization': 'Bearer ${appState.token}'},
      );
      if (response.statusCode == 200) {
        final List<dynamic> friendData = json.decode(response.body);
        setState(() {
          friends = friendData
              .map((data) => User.fromJson({
                    'id': data['_id'],
                    'name': data['username'],
                    'job': data['job'] ?? 'لا توجد وظيفة محددة',
                    'latitude': data['latitude']?.toDouble() ?? 0.0,
                    'longitude': data['longitude']?.toDouble() ?? 0.0,
                    'avatarUrl': data['avatarUrl'] != null
                        ? '${AppState.getBackendUrl()}${data['avatarUrl']}'
                        : 'https://via.placeholder.com/150',
                    'isOnline': data['isOnline'] ?? false,
                    'isFavorite': data['isFavorite'] ?? false,
                    'shareLocation': data['shareLocation'] ?? false,
                  }))
              .toList();
          filteredFriends = friends;
          isLoading = false;
        });
      } else {
        throw Exception('فشل في جلب الأصدقاء: ${response.body}');
      }
    } catch (e) {
      setState(() {
        isLoading = false;
        hasError = true;
        errorMessage = e.toString();
      });
      Provider.of<AppState>(context, listen: false)
          .showSnackBar('خطأ في جلب الأصدقاء: $e', Colors.red);
    }
  }

  void _filterFriends() {
    final query = _searchController.text.toLowerCase();
    setState(() {
      filteredFriends = friends
          .where((friend) => friend.name.toLowerCase().contains(query))
          .toList();
    });
  }

  void _toggleFavorite(User friend) async {
    final appState = Provider.of<AppState>(context, listen: false);
    try {
      final response = await http.post(
        Uri.parse('${AppState.getBackendUrl()}/api/friends/favorite'),
        headers: {
          'Authorization': 'Bearer ${appState.token}',
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'userId': friend.id,
          'isFavorite': !friend.isFavorite,
        }),
      );
      if (response.statusCode == 200) {
        setState(() {
          friend.isFavorite = !friend.isFavorite;
        });
        appState.showSnackBar(
          friend.isFavorite
              ? 'تم إضافة ${friend.name} إلى المفضلة'
              : 'تم إزالة ${friend.name} من المفضلة',
          Colors.green,
        );
      } else {
        throw Exception('فشل في تحديث المفضلة: ${response.body}');
      }
    } catch (e) {
      appState.showSnackBar('خطأ في تحديث المفضلة: $e', Colors.red);
    }
  }

  void _openChat(BuildContext context, String userId, String userName) {
    final appState = Provider.of<AppState>(context, listen: false);
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ChatPage(
          recipientId: userId,
          userName: userName,
          currentUserId: appState.username ?? 'unknown',
          socket: socket,
        ),
      ),
    );
  }

  void _openMap(double latitude, double longitude, String userName) async {
    final url = 'https://www.google.com/maps?q=$latitude,$longitude';
    if (await canLaunch(url)) {
      await launch(url);
    } else {
      Provider.of<AppState>(context, listen: false)
          .showSnackBar('لا يمكن فتح الخريطة', Colors.red);
    }
  }

  Future<void> _updateUserLocation(double latitude, double longitude) async {
    final appState = Provider.of<AppState>(context, listen: false);
    try {
      final response = await http.patch(
        Uri.parse('${AppState.getBackendUrl()}/api/users/me'),
        headers: {
          'Authorization': 'Bearer ${appState.token}',
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'latitude': latitude,
          'longitude': longitude,
        }),
      );
      if (response.statusCode != 200) {
        throw Exception('Failed to update location: ${response.body}');
      }
    } catch (e) {
      appState.showSnackBar('خطأ في تحديث الموقع: $e', Colors.red);
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    socket.off('user_status');
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final appState = Provider.of<AppState>(context);
    final theme = Theme.of(context);
    return Theme(
      data: MyApp.themes[appState.selectedTheme]?['themeData'] ??
          MyApp.themes['Default']!['themeData'],
      child: Scaffold(
        extendBodyBehindAppBar: true,
        appBar: AppBar(
          backgroundColor: Colors.transparent,
          elevation: 0,
          toolbarHeight: 48,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back, color: Colors.white, size: 20),
            onPressed: () => Navigator.pop(context),
          ),
          actions: [
            IconButton(
              icon: Icon(
                appState.shareLocation ? Icons.location_on : Icons.location_off,
                color: Colors.white,
                size: 20,
              ),
              onPressed: () async {
                appState.toggleLocationSharing();
                if (appState.shareLocation) {
                  try {
                    bool serviceEnabled =
                        await Geolocator.isLocationServiceEnabled();
                    if (!serviceEnabled) {
                      appState.showSnackBar(
                          'يرجى تفعيل خدمات الموقع', Colors.red);
                      appState.toggleLocationSharing();
                      return;
                    }
                    LocationPermission permission =
                        await Geolocator.checkPermission();
                    if (permission == LocationPermission.denied) {
                      permission = await Geolocator.requestPermission();
                      if (permission == LocationPermission.denied) {
                        appState.toggleLocationSharing();
                        return;
                      }
                    }
                    final position = await Geolocator.getCurrentPosition(
                        desiredAccuracy: LocationAccuracy.high);
                    await _updateUserLocation(
                        position.latitude, position.longitude);
                    appState.showSnackBar(
                        'تم تفعيل مشاركة الموقع', Colors.green);
                  } catch (e) {
                    appState.showSnackBar('خطأ في الموقع: $e', Colors.red);
                    appState.toggleLocationSharing();
                  }
                } else {
                  appState.showSnackBar('تم إيقاف مشاركة الموقع', Colors.green);
                }
              },
              tooltip: 'تبديل مشاركة الموقع',
            ),
          ],
          flexibleSpace: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  theme.primaryColor,
                  theme.primaryColor.withOpacity(0.8)
                ],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ),
            ),
          ),
        ),
        body: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [Colors.grey[900]!, Colors.grey[850]!],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.fromLTRB(
                    12.0, kToolbarHeight + 12.0, 12.0, 8.0),
                child: TextField(
                  controller: _searchController,
                  style: const TextStyle(
                      color: Colors.white, fontFamily: 'Tajawal'),
                  decoration: InputDecoration(
                    hintText: 'ابحث عن صديق...',
                    hintStyle: TextStyle(
                        color: Colors.white.withOpacity(0.6),
                        fontFamily: 'Tajawal'),
                    filled: true,
                    fillColor: Colors.grey[800],
                    prefixIcon: const Icon(Icons.search, color: Colors.white),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(30),
                      borderSide: BorderSide.none,
                    ),
                  ),
                ),
              ),
              const Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
                child: Text(
                  'الأصدقاء (متابعين متبادلين)',
                  style: TextStyle(
                    fontFamily: 'Tajawal',
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              Expanded(
                child: isLoading
                    ? Center(
                        child: CircularProgressIndicator(
                            color: theme.primaryColor))
                    : hasError
                        ? Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                  'خطأ: $errorMessage',
                                  style: const TextStyle(
                                      color: Colors.red, fontFamily: 'Tajawal'),
                                  textAlign: TextAlign.center,
                                ),
                                const SizedBox(height: 16),
                                ElevatedButton(
                                  onPressed: fetchFriends,
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: theme.primaryColor,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                  ),
                                  child: const Text(
                                    'إعادة المحاولة',
                                    style: TextStyle(
                                        color: Colors.white,
                                        fontFamily: 'Tajawal'),
                                  ),
                                ),
                              ],
                            ),
                          )
                        : RefreshIndicator(
                            onRefresh: fetchFriends,
                            color: theme.primaryColor,
                            child: filteredFriends.isEmpty
                                ? const Center(
                                    child: Text(
                                      'لا يوجد أصدقاء مطابقين',
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontFamily: 'Tajawal',
                                        fontSize: 18,
                                      ),
                                    ),
                                  )
                                : ListView.builder(
                                    padding: const EdgeInsets.all(16),
                                    itemCount: filteredFriends.length,
                                    itemBuilder: (context, idx) {
                                      final friend = filteredFriends[idx];
                                      return FadeInUp(
                                        duration:
                                            const Duration(milliseconds: 300),
                                        child: GestureDetector(
                                          onTap: () => _openChat(
                                              context, friend.id, friend.name),
                                          onLongPress: () =>
                                              _toggleFavorite(friend),
                                          child: Card(
                                            elevation: 5,
                                            shape: RoundedRectangleBorder(
                                              borderRadius:
                                                  BorderRadius.circular(15),
                                            ),
                                            color: Theme.of(context).cardColor,
                                            child: ListTile(
                                              contentPadding:
                                                  const EdgeInsets.symmetric(
                                                horizontal: 16,
                                                vertical: 8,
                                              ),
                                              leading: Stack(
                                                children: [
                                                  CircleAvatar(
                                                    backgroundImage:
                                                        NetworkImage(
                                                            friend.avatarUrl),
                                                    radius: 24,
                                                  ),
                                                  if (friend.isOnline)
                                                    Positioned(
                                                      right: 0,
                                                      bottom: 0,
                                                      child: Container(
                                                        width: 12,
                                                        height: 12,
                                                        decoration:
                                                            const BoxDecoration(
                                                          color: Colors.green,
                                                          shape:
                                                              BoxShape.circle,
                                                          border: Border(
                                                            left: BorderSide(
                                                                color: Colors
                                                                    .white,
                                                                width: 2),
                                                            top: BorderSide(
                                                                color: Colors
                                                                    .white,
                                                                width: 2),
                                                            right: BorderSide(
                                                                color: Colors
                                                                    .white,
                                                                width: 2),
                                                            bottom: BorderSide(
                                                                color: Colors
                                                                    .white,
                                                                width: 2),
                                                          ),
                                                        ),
                                                      ),
                                                    ),
                                                ],
                                              ),
                                              title: Text(
                                                friend.name,
                                                style: const TextStyle(
                                                  fontFamily: 'Tajawal',
                                                  color: Colors.white,
                                                  fontWeight: FontWeight.bold,
                                                ),
                                              ),
                                              subtitle: Row(
                                                children: [
                                                  Expanded(
                                                    child: Text(
                                                      friend.job,
                                                      style: TextStyle(
                                                        fontFamily: 'Tajawal',
                                                        color: Colors.white
                                                            .withOpacity(0.6),
                                                      ),
                                                    ),
                                                  ),
                                                  if (friend.shareLocation &&
                                                      friend.latitude != 0.0 &&
                                                      friend.longitude != 0.0)
                                                    GestureDetector(
                                                      onTap: () => _openMap(
                                                          friend.latitude,
                                                          friend.longitude,
                                                          friend.name),
                                                      child: Text(
                                                        '(${friend.latitude.toStringAsFixed(2)}, ${friend.longitude.toStringAsFixed(2)})',
                                                        style: TextStyle(
                                                            color: theme
                                                                .primaryColor,
                                                            fontFamily:
                                                                'Tajawal'),
                                                      ),
                                                    ),
                                                ],
                                              ),
                                              trailing: Icon(
                                                friend.isFavorite
                                                    ? Icons.star
                                                    : Icons.star_border,
                                                color: friend.isFavorite
                                                    ? Colors.yellow
                                                    : Colors.grey,
                                              ),
                                            ),
                                          ),
                                        ),
                                      );
                                    },
                                  ),
                          ),
              ),
            ],
          ),
        ),
        floatingActionButton: ZoomIn(
          duration: const Duration(milliseconds: 500),
          child: FloatingActionButton(
            onPressed: fetchFriends,
            backgroundColor: theme.primaryColor,
            child: const Icon(Icons.refresh, color: Colors.white),
          ),
        ),
      ),
    );
  }
}

class AddGroupDialog extends StatefulWidget {
  final Function(Group) onGroupAdded;

  const AddGroupDialog({required this.onGroupAdded, super.key});

  @override
  _AddGroupDialogState createState() => _AddGroupDialogState();
}

class _AddGroupDialogState extends State<AddGroupDialog> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();

  @override
  void dispose() {
    _nameController.dispose();
    super.dispose();
  }

  Future<void> _saveGroup() async {
    if (_formKey.currentState!.validate()) {
      final group = Group(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        name: _nameController.text,
        iconUrl: 'https://via.placeholder.com/150',
        membersCount: 1,
      );
      try {
        final appState = Provider.of<AppState>(context, listen: false);
        final response = await http.post(
          Uri.parse('${AppState.getBackendUrl()}/api/groups'),
          headers: {
            'Authorization': 'Bearer ${appState.token}',
            'Content-Type': 'application/json',
          },
          body: jsonEncode(group.toJson()),
        );
        if (response.statusCode == 201) {
          widget.onGroupAdded(group);
          Navigator.pop(context);
          appState.showSnackBar('تم إضافة المجموعة بنجاح', Colors.green);
        } else {
          throw Exception('فشل في إضافة المجموعة: ${response.body}');
        }
      } catch (e) {
        Provider.of<AppState>(context, listen: false)
            .showSnackBar('خطأ في إضافة المجموعة: $e', Colors.red);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return AlertDialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      backgroundColor: Colors.grey[800],
      title: Text('إضافة مجموعة جديدة',
          style: TextStyle(color: theme.primaryColor, fontFamily: 'Tajawal')),
      content: Form(
        key: _formKey,
        child: TextFormField(
          controller: _nameController,
          style: const TextStyle(color: Colors.white, fontFamily: 'Tajawal'),
          decoration: InputDecoration(
            labelText: 'اسم المجموعة',
            labelStyle: TextStyle(
                color: Colors.white.withOpacity(0.6), fontFamily: 'Tajawal'),
            filled: true,
            fillColor: Colors.grey[800],
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide.none,
            ),
          ),
          validator: (value) =>
              value == null || value.isEmpty ? 'الاسم مطلوب' : null,
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('إلغاء',
              style: TextStyle(color: Colors.grey, fontFamily: 'Tajawal')),
        ),
        ElevatedButton(
          onPressed: _saveGroup,
          style: ElevatedButton.styleFrom(
            backgroundColor: theme.primaryColor,
            shape:
                RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          ),
          child: const Text('إضافة',
              style: TextStyle(color: Colors.white, fontFamily: 'Tajawal')),
        ),
      ],
    );
  }
}

class ChatPage extends StatefulWidget {
  final String recipientId;
  final String userName;
  final String currentUserId;
  final io.Socket socket;

  const ChatPage({
    super.key,
    required this.recipientId,
    required this.userName,
    required this.currentUserId,
    required this.socket,
  });

  @override
  _ChatPageState createState() => _ChatPageState();
}

class _ChatPageState extends State<ChatPage> with TickerProviderStateMixin {
  final TextEditingController _messageController = TextEditingController();
  final List<Message> _messages = [];
  bool _isLoading = true;
  bool _isTyping = false;
  String? _typingUser;
  File? _selectedFile;
  late AnimationController _emojiPickerController;
  bool _showEmojiPicker = false;

  @override
  void initState() {
    super.initState();
    _emojiPickerController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    _fetchMessages();
    _initializeSocketListeners();
  }

  void _initializeSocketListeners() {
    widget.socket.on('private_message', (data) {
      if (data['senderId'] == widget.recipientId ||
          data['senderId'] == widget.currentUserId) {
        setState(() {
          _messages.insert(0, Message.fromJson(data));
        });
      }
    });

    widget.socket.on('typing', (data) {
      if (data['senderId'] == widget.recipientId &&
          data['recipientId'] == widget.currentUserId) {
        setState(() {
          _isTyping = true;
          _typingUser = widget.userName;
        });
        Future.delayed(const Duration(seconds: 3), () {
          if (mounted) {
            setState(() {
              _isTyping = false;
              _typingUser = null;
            });
          }
        });
      }
    });
  }

  Future<void> _fetchMessages() async {
    setState(() => _isLoading = true);
    try {
      final appState = Provider.of<AppState>(context, listen: false);
      final response = await http.get(
        Uri.parse(
            '${AppState.getBackendUrl()}/api/messages/${widget.currentUserId}/${widget.recipientId}'),
        headers: {'Authorization': 'Bearer ${appState.token}'},
      );
      if (response.statusCode == 200) {
        final List<dynamic> messageData = json.decode(response.body);
        setState(() {
          _messages.addAll(
              messageData.map((data) => Message.fromJson(data)).toList());
          _isLoading = false;
        });
      } else {
        throw Exception('فشل في جلب الرسائل: ${response.body}');
      }
    } catch (e) {
      Provider.of<AppState>(context, listen: false)
          .showSnackBar('خطأ في جلب الرسائل: $e', Colors.red);
      setState(() => _isLoading = false);
    }
  }

  Future<void> _pickImage() async {
    final picker = ImagePicker();
    final pickedFile = await picker.pickImage(source: ImageSource.gallery);
    if (pickedFile != null) {
      setState(() {
        _selectedFile = File(pickedFile.path);
      });
      await _sendMessage(type: 'image');
    }
  }

  void _notifyTyping() {
    widget.socket.emit('typing', {
      'senderId': widget.currentUserId,
      'recipientId': widget.recipientId,
    });
  }

  Future<void> _sendMessage({String type = 'text'}) async {
    final content = _messageController.text.trim();
    if (content.isEmpty && _selectedFile == null && type == 'text') return;

    final message = Message(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      senderId: widget.currentUserId,
      content: content,
      timestamp: DateTime.now(),
      readBy: [widget.currentUserId],
    );

    widget.socket.emit('private_message', message.toJson());

    setState(() {
      _messages.insert(0, message);
      _messageController.clear();
      _selectedFile = null;
    });
  }

  @override
  void dispose() {
    _messageController.dispose();
    _emojiPickerController.dispose();
    widget.socket.off('private_message');
    widget.socket.off('typing');
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        title: Row(
          children: [
            const CircleAvatar(
              backgroundImage: NetworkImage('https://via.placeholder.com/150'),
              radius: 18,
            ),
            const SizedBox(width: 8),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(widget.userName,
                    style: const TextStyle(
                        fontSize: 18,
                        color: Colors.white,
                        fontFamily: 'Tajawal')),
                if (_isTyping)
                  Text(
                    '$_typingUser يكتب...',
                    style: TextStyle(
                        fontSize: 12,
                        color: Colors.white.withOpacity(0.6),
                        fontFamily: 'Tajawal'),
                  ),
              ],
            ),
          ],
        ),
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [theme.primaryColor, theme.primaryColor.withOpacity(0.8)],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
          ),
        ),
      ),
      body: Container(
        color: Theme.of(context).scaffoldBackgroundColor,
        child: _isLoading
            ? Center(
                child: CircularProgressIndicator(color: theme.primaryColor))
            : Column(
                children: [
                  Expanded(
                    child: ListView.builder(
                      reverse: true,
                      itemCount: _messages.length,
                      itemBuilder: (context, index) {
                        final message = _messages[index];
                        final isMe = message.senderId == widget.currentUserId;
                        return FadeInUp(
                          duration: const Duration(milliseconds: 300),
                          child: Align(
                            alignment: isMe
                                ? Alignment.centerRight
                                : Alignment.centerLeft,
                            child: Container(
                              margin: const EdgeInsets.symmetric(
                                  vertical: 6, horizontal: 12),
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                color: isMe
                                    ? theme.primaryColor.withOpacity(0.9)
                                    : Colors.grey[800],
                                borderRadius: BorderRadius.circular(20),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.2),
                                    blurRadius: 8,
                                    offset: const Offset(2, 2),
                                  ),
                                ],
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    isMe
                                        ? widget.currentUserId
                                        : widget.userName,
                                    style: const TextStyle(
                                      fontWeight: FontWeight.bold,
                                      color: Colors.white,
                                      fontFamily: 'Tajawal',
                                    ),
                                  ),
                                  const SizedBox(height: 4),
                                  Text(
                                    message.content,
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontSize: 16,
                                      fontFamily: 'Tajawal',
                                    ),
                                  ),
                                  const SizedBox(height: 4),
                                  Text(
                                    message.timestamp
                                        .toString()
                                        .substring(11, 16),
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: theme.textTheme.bodyMedium?.color
                                              ?.withOpacity(0.8) ??
                                          Colors.grey[600],
                                      fontFamily: 'Tajawal',
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                  if (_selectedFile != null)
                    Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.grey[800],
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Row(
                          children: [
                            Expanded(
                              child: Text(
                                _selectedFile!.path.split('/').last,
                                style: const TextStyle(
                                    color: Colors.white, fontFamily: 'Tajawal'),
                              ),
                            ),
                            IconButton(
                              icon:
                                  const Icon(Icons.close, color: Colors.white),
                              onPressed: () =>
                                  setState(() => _selectedFile = null),
                            ),
                          ],
                        ),
                      ),
                    ),
                  Padding(
                    padding: const EdgeInsets.fromLTRB(8, 8, 8, 16),
                    child: Row(
                      children: [
                        IconButton(
                          icon: const Icon(Icons.attach_file,
                              color: Colors.white),
                          onPressed: _pickImage,
                        ),
                        IconButton(
                          icon: Icon(
                            _showEmojiPicker
                                ? Icons.keyboard
                                : Icons.emoji_emotions,
                            color: Colors.white,
                          ),
                          onPressed: () {
                            setState(() {
                              _showEmojiPicker = !_showEmojiPicker;
                              if (_showEmojiPicker) {
                                _emojiPickerController.forward();
                              } else {
                                _emojiPickerController.reverse();
                              }
                            });
                          },
                        ),
                        Expanded(
                          child: TextField(
                            controller: _messageController,
                            style: const TextStyle(
                                color: Colors.white, fontFamily: 'Tajawal'),
                            decoration: InputDecoration(
                              hintText: 'اكتب رسالة...',
                              hintStyle: TextStyle(
                                  color: Colors.white.withOpacity(0.6),
                                  fontFamily: 'Tajawal'),
                              filled: true,
                              fillColor: Colors.grey[800],
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(30),
                                borderSide: BorderSide.none,
                              ),
                              contentPadding: const EdgeInsets.symmetric(
                                horizontal: 20,
                                vertical: 10,
                              ),
                            ),
                            onChanged: (value) {
                              if (value.isNotEmpty) _notifyTyping();
                            },
                          ),
                        ),
                        const SizedBox(width: 8),
                        FloatingActionButton(
                          mini: true,
                          backgroundColor: theme.primaryColor,
                          onPressed: () => _sendMessage(),
                          child: const Icon(Icons.send, color: Colors.white),
                        ),
                      ],
                    ),
                  ),
                  SizeTransition(
                    sizeFactor: _emojiPickerController,
                    child: SizedBox(
                      height: 250,
                      child: EmojiPicker(
                        onEmojiSelected: (category, emoji) {
                          _messageController.text += emoji.emoji;
                          _notifyTyping();
                        },
                        config: Config(
                          columns: 7,
                          emojiSizeMax: 32,
                          bgColor: Colors.grey[900]!,
                          indicatorColor: theme.primaryColor,
                          iconColor: Colors.white,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
      ),
    );
  }
}

class GroupChatPage extends StatefulWidget {
  final String groupId;
  final String groupName;
  final String currentUserId;
  final io.Socket socket;

  const GroupChatPage({
    super.key,
    required this.groupId,
    required this.groupName,
    required this.currentUserId,
    required this.socket,
  });

  @override
  State<GroupChatPage> createState() => _GroupChatPageState();
}

class _GroupChatPageState extends State<GroupChatPage>
    with TickerProviderStateMixin {
  final TextEditingController _messageController = TextEditingController();
  final AudioPlayer _audioPlayer = AudioPlayer();
  final Map<String, ChewieController> _chewieControllers = {};
  final Map<String, String> _reactions = {};
  final Map<String, String> senderNames = {};
  final List<Message> _messages = [];
  File? _selectedFile;
  String? _replyToMessageId;
  bool _showEmojiPicker = false;
  bool _isTyping = false;
  bool _isLoading = false;
  final Map<String, String> _typingUsers = {};
  late AnimationController _emojiPickerController;

  @override
  void initState() {
    super.initState();
    _emojiPickerController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    _initializeSocketListeners();
    _fetchMessages();
  }

  void _initializeSocketListeners() {
    widget.socket.on('group_message', (data) {
      setState(() {
        _messages.insert(0, Message.fromJson(data));
      });
    });

    widget.socket.on('typing_group', (data) {
      if (data['groupId'] == widget.groupId &&
          data['userId'] != widget.currentUserId) {
        setState(() {
          _typingUsers[data['userId']] = data['username'] ?? 'جارٍ التحميل...';
          _isTyping = true;
        });
        Future.delayed(const Duration(seconds: 3), () {
          if (mounted) {
            setState(() {
              _typingUsers.remove(data['userId']);
              _isTyping = _typingUsers.isNotEmpty;
            });
          }
        });
      }
      widget.socket.on('message_reaction', (data) {
        if (data['groupId'] == widget.groupId) {
          setState(() {
            _reactions[data['messageId']] = data['reaction'];
          });
        }
      });

      widget.socket.on('group_message_read', (data) {
        if (data['groupId'] == widget.groupId) {
          setState(() {
            final messageId = data['messageId'];
            final userId = data['userId'];
            final message = _messages.firstWhere(
              (m) => m.id == messageId,
              orElse: () => Message(
                id: '',
                senderId: '',
                content: '',
                timestamp: DateTime.now(),
                readBy: [],
              ),
            );
            if (message.id.isNotEmpty && !message.readBy.contains(userId)) {
              message.readBy.add(userId);
            }
          });
        }
      });
    });
  }

  Future<void> _fetchMessages() async {
    setState(() => _isLoading = true);
    try {
      final appState = Provider.of<AppState>(context, listen: false);
      final response = await http.get(
        Uri.parse(
            '${AppState.getBackendUrl()}/api/groups/${widget.groupId}/messages'),
        headers: {'Authorization': 'Bearer ${appState.token}'},
      );
      if (response.statusCode == 200) {
        final List<dynamic> messageData = json.decode(response.body);
        setState(() {
          _messages.addAll(
              messageData.map((data) => Message.fromJson(data)).toList());
          for (var message in _messages) {
            _fetchSenderName(message.senderId);
          }
          _isLoading = false;
        });
      } else {
        throw Exception('فشل في جلب الرسائل: ${response.body}');
      }
    } catch (e) {
      Provider.of<AppState>(context, listen: false)
          .showSnackBar('خطأ في جلب الرسائل: $e', Colors.red);
      setState(() => _isLoading = false);
    }
  }

  Future<void> _fetchSenderName(String userId) async {
    if (senderNames.containsKey(userId)) return;
    try {
      final appState = Provider.of<AppState>(context, listen: false);
      final response = await http.get(
        Uri.parse('${AppState.getBackendUrl()}/api/users/$userId'),
        headers: {'Authorization': 'Bearer ${appState.token}'},
      );
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        setState(() {
          senderNames[userId] = data['username'] ?? 'مستخدم غير معروف';
        });
      }
    } catch (e) {
      setState(() {
        senderNames[userId] = 'مستخدم غير معروف';
      });
    }
  }

  Future<void> _pickImage() async {
    final picker = ImagePicker();
    final pickedFile = await picker.pickImage(source: ImageSource.gallery);
    if (pickedFile != null) {
      setState(() {
        _selectedFile = File(pickedFile.path);
      });
      await _sendMessage(type: 'image');
    }
  }

  Future<void> _pickVideo() async {
    final picker = ImagePicker();
    final pickedFile = await picker.pickVideo(source: ImageSource.gallery);
    if (pickedFile != null) {
      setState(() {
        _selectedFile = File(pickedFile.path);
      });
      await _sendMessage(type: 'video');
    }
  }

  Future<void> _pickFile() async {
    final result = await FilePicker.platform.pickFiles();
    if (result != null && result.files.single.path != null) {
      setState(() {
        _selectedFile = File(result.files.single.path!);
      });
      await _sendMessage(type: 'file');
    }
  }

  Future<void> _recordAudio() async {
    final picker = ImagePicker();
    final pickedFile = await picker.pickImage(source: ImageSource.gallery);

    if (pickedFile != null) {
      context
          .read<AppState>()
          .showSnackBar('تم تسجيل رسالة صوتية', Colors.green);
      await _sendMessage(type: 'audio');
    } else {
      context.read<AppState>().showSnackBar('تم إلغاء تسجيل الصوت', Colors.red);
    }
  }

  Future<AudioService> _getAudioService() async {
    final audioService = AudioService();
    await audioService.checkPermission();
    return audioService;
  }

  void _startCall(bool isVideo) async {
    final appState = context.read<AppState>();

    context.read<AppState>().showSnackBar(
        isVideo
            ? 'تم بدء مكالمة فيديو مع ${widget.groupName}'
            : 'تم بدء مكالمة صوتية مع ${widget.groupName}',
        Colors.green);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(isVideo ? 'مكالمة فيديو' : 'مكالمة صوتية',
            style: const TextStyle(fontFamily: 'Tajawal')),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              isVideo ? Icons.videocam : Icons.call,
              size: 50,
              color: Colors.green,
            ),
            const SizedBox(height: 20),
            Text('مكالمة مع ${widget.groupName}',
                style: const TextStyle(fontFamily: 'Tajawal')),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إنهاء المكالمة',
                style: TextStyle(fontFamily: 'Tajawal', color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _addReaction(String messageId, String reaction) {
    setState(() {
      _reactions[messageId] = reaction;
    });
    widget.socket.emit('message_reaction', {
      'groupId': widget.groupId,
      'messageId': messageId,
      'reaction': reaction,
    });
  }

  void _replyTo(String messageId) {
    setState(() {
      _replyToMessageId = messageId;
    });
  }

  void _notifyTyping() {
    widget.socket.emit('typing_group', {
      'groupId': widget.groupId,
      'userId': widget.currentUserId,
      'username': widget.currentUserId,
    });
  }

  void _notifyRead() {
    if (_messages.isNotEmpty) {
      widget.socket.emit('group_message_read', {
        'groupId': widget.groupId,
        'messageId': _messages.first.id,
        'userId': widget.currentUserId,
      });
    }
  }

  Future<void> _sendMessage({String type = 'text'}) async {
    final content = _messageController.text.trim();
    if (content.isEmpty && _selectedFile == null && type == 'text') return;

    final message = Message(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      senderId: widget.currentUserId,
      content: content,
      timestamp: DateTime.now(),
      readBy: [widget.currentUserId],
    );

    widget.socket.emit('group_message', {
      'groupId': widget.groupId,
      ...message.toJson(),
      'type': type,
    });

    setState(() {
      _messages.insert(0, message);
      _messageController.clear();
      _selectedFile = null;
      _replyToMessageId = null;
    });
  }

  @override
  void dispose() {
    _messageController.dispose();
    _audioPlayer.dispose();
    _chewieControllers.forEach((key, controller) => controller.dispose());
    _emojiPickerController.dispose();
    widget.socket.off('group_message');
    widget.socket.off('typing_group');
    widget.socket.off('message_reaction');
    widget.socket.off('group_message_read');
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        title: Row(
          children: [
            const CircleAvatar(
              backgroundImage: NetworkImage('https://via.placeholder.com/150'),
              radius: 18,
            ),
            const SizedBox(width: 8),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(widget.groupName,
                    style: const TextStyle(
                        fontSize: 18,
                        color: Colors.white,
                        fontFamily: 'Tajawal')),
                if (_isTyping)
                  Text(
                    '${_typingUsers.values.join(', ')} يكتب...',
                    style: TextStyle(
                        fontSize: 12,
                        color: Colors.white.withOpacity(0.6),
                        fontFamily: 'Tajawal'),
                  ),
              ],
            ),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.videocam, color: Colors.white),
            onPressed: () => _startCall(true),
          ),
          IconButton(
            icon: const Icon(Icons.call, color: Colors.white),
            onPressed: () => _startCall(false),
          ),
          IconButton(
            icon: const Icon(Icons.gamepad, color: Colors.white),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const GamesPage()),
              );
            },
            tooltip: 'الألعاب',
          ),
        ],
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [theme.primaryColor, theme.primaryColor.withOpacity(0.8)],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
          ),
        ),
      ),
      body: Container(
        color: Theme.of(context).scaffoldBackgroundColor,
        child: _isLoading
            ? Center(
                child: CircularProgressIndicator(color: theme.primaryColor))
            : Column(
                children: [
                  Expanded(
                    child: ListView.builder(
                      reverse: true,
                      itemCount: _messages.length,
                      itemBuilder: (context, index) {
                        final message = _messages[index];
                        final isMe = message.senderId == widget.currentUserId;
                        final reaction = _reactions[message.id];
                        final senderName =
                            senderNames[message.senderId] ?? 'جارٍ التحميل...';
                        return FadeInUp(
                          duration: const Duration(milliseconds: 300),
                          child: GestureDetector(
                            onLongPress: () {
                              showModalBottomSheet(
                                context: context,
                                backgroundColor: Colors.grey[800],
                                shape: const RoundedRectangleBorder(
                                  borderRadius: BorderRadius.vertical(
                                      top: Radius.circular(20)),
                                ),
                                builder: (context) => Column(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    ListTile(
                                      leading: const Icon(Icons.reply,
                                          color: Colors.white),
                                      title: const Text('الرد',
                                          style: TextStyle(
                                              color: Colors.white,
                                              fontFamily: 'Tajawal')),
                                      onTap: () {
                                        _replyTo(message.id);
                                        Navigator.pop(context);
                                      },
                                    ),
                                    ListTile(
                                      leading: const Icon(Icons.favorite,
                                          color: Colors.white),
                                      title: const Text('إضافة رد فعل',
                                          style: TextStyle(
                                              color: Colors.white,
                                              fontFamily: 'Tajawal')),
                                      onTap: () {
                                        _addReaction(message.id, '❤️');
                                        Navigator.pop(context);
                                      },
                                    ),
                                  ],
                                ),
                              );
                            },
                            child: Align(
                              alignment: isMe
                                  ? Alignment.centerRight
                                  : Alignment.centerLeft,
                              child: Container(
                                margin: const EdgeInsets.symmetric(
                                    vertical: 6, horizontal: 12),
                                padding: const EdgeInsets.all(12),
                                decoration: BoxDecoration(
                                  color: isMe
                                      ? theme.primaryColor.withOpacity(0.9)
                                      : Colors.grey[800],
                                  borderRadius: BorderRadius.circular(20),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.black.withOpacity(0.2),
                                      blurRadius: 8,
                                      offset: const Offset(2, 2),
                                    ),
                                  ],
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      isMe ? widget.currentUserId : senderName,
                                      style: const TextStyle(
                                        fontWeight: FontWeight.bold,
                                        color: Colors.white,
                                        fontFamily: 'Tajawal',
                                      ),
                                    ),
                                    const SizedBox(height: 4),
                                    if (_replyToMessageId == message.id)
                                      Container(
                                        padding: const EdgeInsets.all(8),
                                        margin:
                                            const EdgeInsets.only(bottom: 8),
                                        decoration: BoxDecoration(
                                          color: Colors.grey[700]!
                                              .withOpacity(0.5),
                                          borderRadius:
                                              BorderRadius.circular(12),
                                        ),
                                        child: Text(
                                          'يرد على: ${message.content}',
                                          style: TextStyle(
                                            color:
                                                Colors.white.withOpacity(0.6),
                                            fontStyle: FontStyle.italic,
                                            fontFamily: 'Tajawal',
                                          ),
                                        ),
                                      ),
                                    Text(
                                      message.content,
                                      style: const TextStyle(
                                        color: Colors.white,
                                        fontSize: 16,
                                        fontFamily: 'Tajawal',
                                      ),
                                    ),
                                    const SizedBox(height: 4),
                                    Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Text(
                                          message.timestamp
                                              .toString()
                                              .substring(11, 16),
                                          style: TextStyle(
                                            fontSize: 12,
                                            color:
                                                Colors.white.withOpacity(0.6),
                                            fontFamily: 'Tajawal',
                                          ),
                                        ),
                                        if (reaction != null) ...[
                                          const SizedBox(width: 8),
                                          Text(
                                            reaction,
                                            style:
                                                const TextStyle(fontSize: 16),
                                          ),
                                        ],
                                        if (isMe && message.readBy.isNotEmpty)
                                          const Icon(Icons.done_all,
                                              size: 16, color: Colors.blue),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                  if (_selectedFile != null)
                    Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.grey[800],
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Row(
                          children: [
                            Expanded(
                              child: Text(
                                _selectedFile!.path.split('/').last,
                                style: const TextStyle(
                                    color: Colors.white, fontFamily: 'Tajawal'),
                              ),
                            ),
                            IconButton(
                              icon:
                                  const Icon(Icons.close, color: Colors.white),
                              onPressed: () =>
                                  setState(() => _selectedFile = null),
                            ),
                          ],
                        ),
                      ),
                    ),
                  if (_replyToMessageId != null)
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 8.0),
                      child: Row(
                        children: [
                          Expanded(
                            child: Text(
                              'يرد على رسالة...',
                              style: TextStyle(
                                  color: Colors.white.withOpacity(0.6),
                                  fontFamily: 'Tajawal'),
                            ),
                          ),
                          IconButton(
                            icon: const Icon(Icons.close, color: Colors.white),
                            onPressed: () =>
                                setState(() => _replyToMessageId = null),
                          ),
                        ],
                      ),
                    ),
                  Padding(
                    padding: const EdgeInsets.fromLTRB(8, 8, 8, 16),
                    child: Row(
                      children: [
                        IconButton(
                          icon: const Icon(Icons.attach_file,
                              color: Colors.white),
                          onPressed: _pickFile,
                        ),
                        IconButton(
                          icon: const Icon(Icons.image, color: Colors.white),
                          onPressed: _pickImage,
                        ),
                        IconButton(
                          icon: const Icon(Icons.videocam, color: Colors.white),
                          onPressed: _pickVideo,
                        ),
                        IconButton(
                          icon: const Icon(Icons.mic, color: Colors.white),
                          onPressed: _recordAudio,
                        ),
                        IconButton(
                          icon: Icon(
                            _showEmojiPicker
                                ? Icons.keyboard
                                : Icons.emoji_emotions,
                            color: Colors.white,
                          ),
                          onPressed: () {
                            setState(() {
                              _showEmojiPicker = !_showEmojiPicker;
                              if (_showEmojiPicker) {
                                _emojiPickerController.forward();
                              } else {
                                _emojiPickerController.reverse();
                              }
                            });
                          },
                        ),
                        Expanded(
                          child: TextField(
                            controller: _messageController,
                            style: const TextStyle(
                                color: Colors.white, fontFamily: 'Tajawal'),
                            decoration: InputDecoration(
                              hintText: 'اكتب رسالة...',
                              hintStyle: TextStyle(
                                  color: Colors.white.withOpacity(0.6),
                                  fontFamily: 'Tajawal'),
                              filled: true,
                              fillColor: Colors.grey[800],
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(30),
                                borderSide: BorderSide.none,
                              ),
                              contentPadding: const EdgeInsets.symmetric(
                                horizontal: 20,
                                vertical: 10,
                              ),
                            ),
                            onChanged: (value) {
                              if (value.isNotEmpty) _notifyTyping();
                            },
                          ),
                        ),
                        const SizedBox(width: 8),
                        FloatingActionButton(
                          mini: true,
                          backgroundColor: theme.primaryColor,
                          onPressed: () => _sendMessage(),
                          child: const Icon(Icons.send, color: Colors.white),
                        ),
                      ],
                    ),
                  ),
                  SizeTransition(
                    sizeFactor: _emojiPickerController,
                    child: SizedBox(
                      height: 250,
                      child: EmojiPicker(
                        onEmojiSelected: (category, emoji) {
                          _messageController.text += emoji.emoji;
                          _notifyTyping();
                        },
                        config: Config(
                          columns: 7,
                          emojiSizeMax: 32,
                          bgColor: Colors.grey[900]!,
                          indicatorColor: theme.primaryColor,
                          iconColor: Colors.white,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
      ),
    );
  }
}
