// religious.js
const express = require('express');
const router = express.Router();
const religiousController = require('../controllers/religiouscontroller');

// Quran endpoint: /api/religious/quran?surah=1&ayah=1
router.get('/quran', religiousController.getQuran);
// Bible endpoint: /api/religious/bible?reference=John%203:16
router.get('/bible', religiousController.getBible);

module.exports = router;
