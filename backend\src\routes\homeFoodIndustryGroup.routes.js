const express = require('express');
const router = express.Router();
const controller = require('../controllers/HomeFoodIndustryGroup.controller');
const { protect } = require('../middleware/authmiddleware');
const { body } = require('express-validator');

// التحقق من صحة البيانات
const validateGroup = [
  body('name').notEmpty().withMessage('الاسم مطلوب'),
  body('imageUrl').notEmpty().withMessage('رابط الصورة مطلوب'),
  body('videos').isArray().withMessage('الفيديوهات يجب أن تكون مصفوفة'),
  body('articles').isArray().withMessage('المقالات يجب أن تكون مصفوفة'),
  body('faqs').isArray().withMessage('الأسئلة الشائعة يجب أن تكون مصفوفة')
];

router.get('/', controller.getAllGroups);
router.get('/:id', controller.getGroupById);
router.post('/', protect, validateGroup, controller.createGroup);
router.put('/:id', protect, validateGroup, controller.updateGroup);
router.delete('/:id', protect, controller.deleteGroup);

module.exports = router;
