import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'social_post_model.dart';

class SocialFeedProvider with ChangeNotifier {
  List<SocialPost> _posts = [];
  bool _isLoading = false;
  bool _isConnected = true;
  String? _currentUserId;
  String? _currentUserName;
  String? _currentUserAvatar;

  // Getters
  List<SocialPost> get posts => _posts;
  bool get isLoading => _isLoading;
  bool get isConnected => _isConnected;
  String? get currentUserId => _currentUserId;
  String? get currentUserName => _currentUserName;
  String? get currentUserAvatar => _currentUserAvatar;

  // Initialize the provider
  Future<void> initialize() async {
    await _loadUserData();
    await checkConnectivity();
    if (_isConnected) {
      await fetchPosts();
    }
  }

  // Load user data from SharedPreferences
  Future<void> _loadUserData() async {
    final prefs = await SharedPreferences.getInstance();
    _currentUserId = prefs.getString('userId');
    _currentUserName = prefs.getString('userName');
    _currentUserAvatar = prefs.getString('userAvatar');
    notifyListeners();
  }

  // Check internet connectivity
  Future<void> checkConnectivity() async {
    try {
      final response = await http.get(Uri.parse('https://www.google.com'));
      _isConnected = response.statusCode == 200;
    } catch (e) {
      _isConnected = false;
    }
    notifyListeners();
  }

  // Fetch posts from the server
  Future<void> fetchPosts() async {
    if (_isLoading) return;

    _isLoading = true;
    notifyListeners();

    try {
      // TODO: Replace with your actual API endpoint
      final response = await http.get(
        Uri.parse('YOUR_API_ENDPOINT/posts'),
        headers: {
          'Authorization': 'Bearer YOUR_AUTH_TOKEN',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        _posts = data.map((post) => SocialPost.fromJson(post)).toList();
      } else {
        throw Exception('Failed to load posts');
      }
    } catch (e) {
      _isConnected = false;
      // Load from local storage if available
      final prefs = await SharedPreferences.getInstance();
      final cachedPosts = prefs.getString('cached_posts');
      if (cachedPosts != null) {
        final List<dynamic> data = json.decode(cachedPosts);
        _posts = data.map((post) => SocialPost.fromJson(post)).toList();
      }
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Create a new post
  Future<void> createPost({
    required String content,
    List<String>? mediaUrls,
    String? location,
  }) async {
    if (_currentUserId == null || _currentUserName == null) return;

    final newPost = SocialPost(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      userId: _currentUserId!,
      userName: _currentUserName!,
      userAvatar: _currentUserAvatar,
      content: content,
      mediaUrls: mediaUrls,
      location: location,
    );

    _posts.insert(0, newPost);
    notifyListeners();

    try {
      // TODO: Replace with your actual API endpoint
      final response = await http.post(
        Uri.parse('YOUR_API_ENDPOINT/posts'),
        headers: {
          'Authorization': 'Bearer YOUR_AUTH_TOKEN',
          'Content-Type': 'application/json',
        },
        body: json.encode(newPost.toJson()),
      );

      if (response.statusCode != 201) {
        throw Exception('Failed to create post');
      }
    } catch (e) {
      // Revert on error
      _posts.removeAt(0);
      rethrow;
    } finally {
      notifyListeners();
    }
  }

  // Like or unlike a post
  Future<void> toggleLike(String postId) async {
    if (_currentUserId == null) return;

    final postIndex = _posts.indexWhere((post) => post.id == postId);
    if (postIndex == -1) return;

    final post = _posts[postIndex];
    final isLiked = post.isLikedBy(_currentUserId!);
    final updatedLikes = List<String>.from(post.likes);

    if (isLiked) {
      updatedLikes.remove(_currentUserId);
    } else {
      updatedLikes.add(_currentUserId!);
    }

    _posts[postIndex] = post.copyWith(likes: updatedLikes);
    notifyListeners();

    try {
      // TODO: Replace with your actual API endpoint
      final response = await http.post(
        Uri.parse('YOUR_API_ENDPOINT/posts/$postId/like'),
        headers: {
          'Authorization': 'Bearer YOUR_AUTH_TOKEN',
          'Content-Type': 'application/json',
        },
        body: json.encode({'userId': _currentUserId, 'like': !isLiked}),
      );

      if (response.statusCode != 200) {
        throw Exception('Failed to update like status');
      }
    } catch (e) {
      // Revert on error
      _posts[postIndex] = post;
      notifyListeners();
      rethrow;
    }
  }

  // Add a comment to a post
  Future<void> addComment(String postId, String content) async {
    if (_currentUserId == null || _currentUserName == null) return;

    final postIndex = _posts.indexWhere((post) => post.id == postId);
    if (postIndex == -1) return;

    final post = _posts[postIndex];
    final newComment = Comment(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      userId: _currentUserId!,
      userName: _currentUserName!,
      userAvatar: _currentUserAvatar,
      content: content,
    );

    final updatedComments = List<Comment>.from(post.comments)..add(newComment);
    _posts[postIndex] = post.copyWith(comments: updatedComments);
    notifyListeners();

    try {
      // TODO: Replace with your actual API endpoint
      final response = await http.post(
        Uri.parse('YOUR_API_ENDPOINT/posts/$postId/comments'),
        headers: {
          'Authorization': 'Bearer YOUR_AUTH_TOKEN',
          'Content-Type': 'application/json',
        },
        body: json.encode({
          'userId': _currentUserId,
          'userName': _currentUserName,
          'userAvatar': _currentUserAvatar,
          'content': content,
        }),
      );

      if (response.statusCode != 201) {
        throw Exception('Failed to add comment');
      }
    } catch (e) {
      // Revert on error
      updatedComments.removeLast();
      _posts[postIndex] = post;
      notifyListeners();
      rethrow;
    }
  }

  // Delete a post
  Future<void> deletePost(String postId) async {
    final postIndex = _posts.indexWhere((post) => post.id == postId);
    if (postIndex == -1) return;

    final post = _posts[postIndex];
    _posts.removeAt(postIndex);
    notifyListeners();

    try {
      // TODO: Replace with your actual API endpoint
      final response = await http.delete(
        Uri.parse('YOUR_API_ENDPOINT/posts/$postId'),
        headers: {
          'Authorization': 'Bearer YOUR_AUTH_TOKEN',
        },
      );

      if (response.statusCode != 200) {
        throw Exception('Failed to delete post');
      }
    } catch (e) {
      // Revert on error
      _posts.insert(postIndex, post);
      notifyListeners();
      rethrow;
    }
  }

  // Like or unlike a comment
  Future<void> toggleCommentLike(String postId, String commentId) async {
    if (_currentUserId == null) return;

    final postIndex = _posts.indexWhere((post) => post.id == postId);
    if (postIndex == -1) return;

    final post = _posts[postIndex];
    final commentIndex = post.comments.indexWhere((comment) => comment.id == commentId);
    if (commentIndex == -1) return;

    final comment = post.comments[commentIndex];
    final updatedLikes = List<String>.from(comment.likes);
    final isLiked = updatedLikes.contains(_currentUserId);

    if (isLiked) {
      updatedLikes.remove(_currentUserId);
    } else {
      updatedLikes.add(_currentUserId!);
    }

    final updatedComment = Comment(
      id: comment.id,
      userId: comment.userId,
      userName: comment.userName,
      userAvatar: comment.userAvatar,
      content: comment.content,
      timestamp: comment.timestamp,
      likes: updatedLikes,
    );

    final updatedComments = List<Comment>.from(post.comments);
    updatedComments[commentIndex] = updatedComment;
    _posts[postIndex] = post.copyWith(comments: updatedComments);
    notifyListeners();

    try {
      // TODO: Replace with your actual API endpoint
      final response = await http.post(
        Uri.parse('YOUR_API_ENDPOINT/posts/$postId/comments/$commentId/like'),
        headers: {
          'Authorization': 'Bearer YOUR_AUTH_TOKEN',
          'Content-Type': 'application/json',
        },
        body: json.encode({'userId': _currentUserId, 'like': !isLiked}),
      );

      if (response.statusCode != 200) {
        throw Exception('Failed to update comment like status');
      }
    } catch (e) {
      // Revert on error
      updatedComments[commentIndex] = comment;
      _posts[postIndex] = post;
      notifyListeners();
      rethrow;
    }
  }
}

// This is a workaround for the XFeedProvider error in main.dart
class XFeedProvider extends SocialFeedProvider {}
