const Message = require('../models/message.model');
const Group = require('../models/Groupmodel');
const asyncHandler = require('../middleware/asyncmiddleware');
const ErrorResponse = require('../utils/errorResponse');

// جلب رسائل مجموعة
exports.getGroupMessages = asyncHandler(async (req, res, next) => {
  const groupId = req.params.groupId;
  const messages = await Message.find({ receiverId: groupId }).sort({ timestamp: 1 });
  res.status(200).json({ success: true, count: messages.length, data: messages });
});

// إرسال رسالة إلى مجموعة
exports.sendGroupMessage = asyncHandler(async (req, res, next) => {
  const { content, type, attachmentUrl } = req.body;
  const groupId = req.params.groupId;
  if (!content && !attachmentUrl) {
    return next(new ErrorResponse('يجب إدخال محتوى أو مرفق', 400));
  }
  const message = await Message.create({
    senderId: req.user.id,
    receiverId: groupId,
    content: content || '',
    type: type || 'text',
    attachmentUrl: attachmentUrl || '',
    readBy: [req.user.id]
  });
  res.status(201).json({ success: true, data: message });
});
