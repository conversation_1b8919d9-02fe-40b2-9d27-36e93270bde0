version: '3.8'

services:
  app:
    build:
      context: .
      target: development
    container_name: fulk-backend
    restart: unless-stopped
    env_file: .env
    environment:
      - NODE_ENV=development
      - PORT=3000
      - MONGO_URI=mongodb://mongo:27017/fulk
    ports:
      - "3000:3000"
    volumes:
      - .:/app
      - /app/node_modules
    depends_on:
      - mongo
    networks:
      - fulk-network
    command: npm run dev

  mongo:
    image: mongo:6.0
    container_name: fulk-mongo
    restart: always
    environment:
      - MONGO_INITDB_DATABASE=fulk
      - MONGO_INITDB_ROOT_USERNAME=root
      - MONGO_INITDB_ROOT_PASSWORD=example
    volumes:
      - mongodb_data:/data/db
    ports:
      - "27017:27017"
    networks:
      - fulk-network

  mongo-express:
    image: mongo-express:1.0.0
    container_name: mongo-express
    restart: always
    ports:
      - "8081:8081"
    environment:
      - ME_CONFIG_MONGODB_ADMINUSERNAME=root
      - ME_CONFIG_MONGODB_ADMINPASSWORD=example
      - ME_CONFIG_MONGODB_SERVER=mongo
    depends_on:
      - mongo
    networks:
      - fulk-network

networks:
  fulk-network:
    driver: bridge

volumes:
  mongodb_data:
    driver: local
