{"swagger": "2.0", "info": {"title": "Social App Backend API", "description": "API documentation for a multi-functional social application with features like posts, chats, groups, and more.", "version": "1.0.0"}, "host": "http://localhost:3000", "basePath": "/", "schemes": ["http"], "securityDefinitions": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}, "paths": {"/register": {"post": {"description": "", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "properties": {"username": {"example": "any"}, "email": {"example": "any"}, "phoneNumber": {"example": "any"}, "password": {"example": "any"}}}}], "responses": {"default": {"description": ""}}}}, "/login": {"post": {"description": "", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "properties": {"email": {"example": "any"}, "phoneNumber": {"example": "any"}, "password": {"example": "any"}}}}], "responses": {"default": {"description": ""}}}}, "/me": {"get": {"description": "", "parameters": [{"name": "authorization", "in": "header", "type": "string"}, {"name": "x-auth-token", "in": "header", "type": "string"}], "responses": {"200": {"description": "OK"}}}}, "/": {"get": {"description": "", "parameters": [{"name": "authorization", "in": "header", "type": "string"}, {"name": "x-auth-token", "in": "header", "type": "string"}], "responses": {"200": {"description": "OK"}, "500": {"description": "Internal Server Error"}}}, "post": {"description": "", "parameters": [{"name": "authorization", "in": "header", "type": "string"}, {"name": "x-auth-token", "in": "header", "type": "string"}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"user": {"example": "any"}}}}], "responses": {"201": {"description": "Created"}, "400": {"description": "Bad Request"}, "500": {"description": "Internal Server Error"}, "default": {"description": ""}}}, "delete": {"description": "", "parameters": [{"name": "authorization", "in": "header", "type": "string"}, {"name": "x-auth-token", "in": "header", "type": "string"}], "responses": {"200": {"description": "OK"}}}}, "/{id}": {"get": {"description": "", "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK"}}}, "put": {"description": "", "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}, {"name": "authorization", "in": "header", "type": "string"}, {"name": "x-auth-token", "in": "header", "type": "string"}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"image": {"example": "any"}}}}], "responses": {"200": {"description": "OK"}, "default": {"description": ""}}}, "delete": {"description": "", "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}, {"name": "authorization", "in": "header", "type": "string"}, {"name": "x-auth-token", "in": "header", "type": "string"}], "responses": {"200": {"description": "OK"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/{clubId}/matches": {"get": {"description": "", "parameters": [{"name": "clubId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK"}}}, "post": {"description": "", "parameters": [{"name": "clubId", "in": "path", "required": true, "type": "string"}, {"name": "authorization", "in": "header", "type": "string"}, {"name": "x-auth-token", "in": "header", "type": "string"}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"club": {"example": "any"}}}}], "responses": {"201": {"description": "Created"}}}}, "/{clubId}/members": {"get": {"description": "", "parameters": [{"name": "clubId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK"}}}, "post": {"description": "", "parameters": [{"name": "clubId", "in": "path", "required": true, "type": "string"}, {"name": "authorization", "in": "header", "type": "string"}, {"name": "x-auth-token", "in": "header", "type": "string"}], "responses": {"200": {"description": "OK"}}}}, "/{clubId}/members/{userId}": {"delete": {"description": "", "parameters": [{"name": "clubId", "in": "path", "required": true, "type": "string"}, {"name": "userId", "in": "path", "required": true, "type": "string"}, {"name": "authorization", "in": "header", "type": "string"}, {"name": "x-auth-token", "in": "header", "type": "string"}], "responses": {"200": {"description": "OK"}}}}, "/{clubId}/posts": {"get": {"description": "", "parameters": [{"name": "clubId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK"}}}}, "/{clubId}/live-stream": {"get": {"description": "", "parameters": [{"name": "clubId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK"}}}}, "/{id}/join": {"post": {"description": "", "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"userId": {"example": "any"}}}}], "responses": {"200": {"description": "OK"}, "400": {"description": "Bad Request"}, "404": {"description": "Not Found"}}}}, "/{id}/leave": {"post": {"description": "", "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"userId": {"example": "any"}}}}], "responses": {"200": {"description": "OK"}, "400": {"description": "Bad Request"}, "404": {"description": "Not Found"}}}}, "/{id}/messages": {"get": {"description": "", "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}], "responses": {"default": {"description": ""}}}, "post": {"description": "", "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}], "responses": {"default": {"description": ""}}}}, "/{id}/like": {"put": {"description": "", "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK"}, "default": {"description": ""}}}}, "/{id}/comment": {"post": {"description": "", "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}], "responses": {"201": {"description": "Created"}, "default": {"description": ""}}}}, "/{id}/complete": {"put": {"description": "", "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/{id}/message": {"post": {"description": "", "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"sender": {"example": "any"}, "content": {"example": "any"}}}}], "responses": {"200": {"description": "OK"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/{userId}/messages": {"get": {"description": "", "parameters": [{"name": "userId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK"}, "500": {"description": "Internal Server Error"}}}}, "/groups/{groupId}/messages": {"get": {"description": "", "parameters": [{"name": "groupId", "in": "path", "required": true, "type": "string"}], "responses": {"default": {"description": ""}}}}, "/{speciesTitle}": {"get": {"description": "", "parameters": [{"name": "speciesTitle", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK"}, "500": {"description": "Internal Server Error"}}}}, "/{id}/read": {"put": {"description": "", "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}, {"name": "authorization", "in": "header", "type": "string"}, {"name": "x-auth-token", "in": "header", "type": "string"}], "responses": {"200": {"description": "OK"}}}}}, "definitions": {"User": {"type": "object", "properties": {"username": {"type": "string", "example": "exampleUser"}, "phoneNumber": {"type": "string", "example": "1234567890"}, "job": {"type": "string", "example": "developer"}, "location": {"type": "string", "example": "Cairo"}, "avatarUrl": {"type": "string", "example": "/uploads/default-avatar.jpg"}, "isOnline": {"type": "boolean", "example": false}}}, "Post": {"type": "object", "properties": {"content": {"type": "string", "example": "This is a sample post"}, "media": {"type": "string", "example": "http://s3.amazonaws.com/sample.jpg"}, "mediaType": {"type": "string", "example": "image"}, "likes": {"type": "number", "example": 0}, "comments": {"type": "array", "items": {"type": "object", "properties": {"username": {"type": "string", "example": "user1"}, "text": {"type": "string", "example": "Great post!"}}}}, "createdAt": {"type": "string", "example": "2025-03-29T12:00:00Z"}}}, "City": {"type": "object", "properties": {"name": {"type": "string", "example": "Cairo"}, "image": {"type": "string", "example": "http://s3.amazonaws.com/cairo.jpg"}, "villages": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string", "example": "Village1"}, "image": {"type": "string", "example": "http://s3.amazonaws.com/village1.jpg"}}}}}}, "Error": {"type": "object", "properties": {"message": {"type": "string", "example": "Error message"}}}}}