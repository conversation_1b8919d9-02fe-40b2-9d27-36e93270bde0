import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:socket_io_client/socket_io_client.dart' as io;
import '../appstate.dart';
import '../config/x_feed_config.dart';

// نماذج البيانات للمنشورات
class PostUser {
  final String id;
  final String name;
  final String? avatarUrl;
  final String? handle;

  PostUser({
    required this.id,
    required this.name,
    this.avatarUrl,
    this.handle,
  });

  factory PostUser.fromJson(Map<String, dynamic> json) {
    return PostUser(
      id: json['_id'] ?? json['id'] ?? '',
      name: json['name'] ?? 'مستخدم غير معروف',
      avatarUrl: json['avatarUrl'],
      handle: json['handle'] ??
          '@${json['name']?.replaceAll(' ', '_').toLowerCase()}',
    );
  }
}

class PostComment {
  final String id;
  final String username;
  final String content;
  final DateTime date;

  PostComment({
    required this.id,
    required this.username,
    required this.content,
    required this.date,
  });

  factory PostComment.fromJson(Map<String, dynamic> json) {
    return PostComment(
      id: json['_id'] ?? DateTime.now().millisecondsSinceEpoch.toString(),
      username: json['username'] ?? 'مستخدم غير معروف',
      content: json['content'] ?? '',
      date:
          json['date'] != null ? DateTime.parse(json['date']) : DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'username': username,
      'content': content,
      'date': date.toIso8601String(),
    };
  }
}

class PostModel {
  final String id;
  final String userId;
  final String content;
  final String? mediaUrl;
  final String? mediaType;
  final int likes;
  final int loves;
  final int haha;
  final List<PostComment> comments;
  final DateTime createdAt;
  final PostUser? author;
  final bool promoted;
  final String privacy;
  final List<String> hashtags;
  final double? latitude;
  final double? longitude;

  // حالات محلية للواجهة
  bool isLiked;
  bool isLoved;
  bool isHaha;
  bool isSaved;

  PostModel({
    required this.id,
    required this.userId,
    required this.content,
    this.mediaUrl,
    this.mediaType,
    required this.likes,
    required this.loves,
    required this.haha,
    required this.comments,
    required this.createdAt,
    this.author,
    required this.promoted,
    required this.privacy,
    required this.hashtags,
    this.latitude,
    this.longitude,
    this.isLiked = false,
    this.isLoved = false,
    this.isHaha = false,
    this.isSaved = false,
  });

  factory PostModel.fromJson(Map<String, dynamic> json) {
    return PostModel(
      id: json['_id'] ?? json['id'] ?? '',
      userId: json['userId'] ?? '',
      content: json['content'] ?? '',
      mediaUrl: json['mediaUrl'],
      mediaType: json['mediaType'],
      likes: json['likes'] ?? 0,
      loves: json['loves'] ?? 0,
      haha: json['haha'] ?? 0,
      comments: (json['comments'] as List<dynamic>?)
              ?.map((comment) => PostComment.fromJson(comment))
              .toList() ??
          [],
      createdAt: json['createdAt'] != null
          ? DateTime.parse(json['createdAt'])
          : DateTime.now(),
      author: json['author'] != null ? PostUser.fromJson(json['author']) : null,
      promoted: json['promoted'] ?? false,
      privacy: json['privacy'] ?? 'public',
      hashtags: (json['hashtags'] as List<dynamic>?)
              ?.map((tag) => tag.toString())
              .toList() ??
          [],
      latitude: json['latitude']?.toDouble(),
      longitude: json['longitude']?.toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'content': content,
      'mediaUrl': mediaUrl,
      'mediaType': mediaType,
      'privacy': privacy,
      'latitude': latitude,
      'longitude': longitude,
    };
  }

  int get totalInteractions => likes + loves + haha + comments.length;

  String get formattedTime {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inHours < 1) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inDays < 1) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} يوم';
    } else {
      return '${createdAt.day}/${createdAt.month}/${createdAt.year}';
    }
  }

  PostModel copyWith({
    int? likes,
    int? loves,
    int? haha,
    List<PostComment>? comments,
    bool? isLiked,
    bool? isLoved,
    bool? isHaha,
    bool? isSaved,
  }) {
    return PostModel(
      id: id,
      userId: userId,
      content: content,
      mediaUrl: mediaUrl,
      mediaType: mediaType,
      likes: likes ?? this.likes,
      loves: loves ?? this.loves,
      haha: haha ?? this.haha,
      comments: comments ?? this.comments,
      createdAt: createdAt,
      author: author,
      promoted: promoted,
      privacy: privacy,
      hashtags: hashtags,
      latitude: latitude,
      longitude: longitude,
      isLiked: isLiked ?? this.isLiked,
      isLoved: isLoved ?? this.isLoved,
      isHaha: isHaha ?? this.isHaha,
      isSaved: isSaved ?? this.isSaved,
    );
  }
}

class PostService {
  static String get baseUrl => AppState.getBackendUrl();

  // الحصول على المنشورات
  static Future<List<PostModel>> getPosts({
    int page = 1,
    int limit = 10,
    String sortBy = 'latest',
    String? userId,
    String? privacy,
    bool? promoted,
  }) async {
    try {
      final queryParams = <String, String>{
        'page': page.toString(),
        'limit': limit.toString(),
        'sortBy': sortBy,
      };

      if (userId != null) queryParams['userId'] = userId;
      if (privacy != null) queryParams['privacy'] = privacy;
      if (promoted != null) queryParams['promoted'] = promoted.toString();

      final uri =
          Uri.parse('$baseUrl/api/posts').replace(queryParameters: queryParams);

      final response = await http.get(
        uri,
        headers: _getHeaders(),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final posts = (data['posts'] as List)
            .map((post) => PostModel.fromJson(post))
            .toList();
        return posts;
      } else {
        throw Exception('فشل في جلب المنشورات: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }

  // إنشاء منشور جديد
  static Future<PostModel> createPost({
    required String content,
    String privacy = 'public',
    File? mediaFile,
    double? latitude,
    double? longitude,
  }) async {
    try {
      final request = http.MultipartRequest(
        'POST',
        Uri.parse('$baseUrl/api/posts'),
      );

      request.headers.addAll(_getHeaders());
      request.fields['content'] = content;
      request.fields['privacy'] = privacy;

      if (latitude != null) request.fields['latitude'] = latitude.toString();
      if (longitude != null) request.fields['longitude'] = longitude.toString();

      if (mediaFile != null) {
        request.files.add(await http.MultipartFile.fromPath(
          'media',
          mediaFile.path,
        ));
      }

      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);

      if (response.statusCode == 201) {
        final data = json.decode(response.body);
        return PostModel.fromJson(data['post']);
      } else {
        throw Exception('فشل في إنشاء المنشور: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('خطأ في إنشاء المنشور: $e');
    }
  }

  // التفاعل مع منشور (إعجاب، حب، ضحك)
  static Future<void> reactToPost(String postId, String reactionType) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/api/posts/$postId/like'),
        headers: _getHeaders(),
        body: json.encode({'reactionType': reactionType}),
      );

      if (response.statusCode != 200) {
        throw Exception('فشل في التفاعل مع المنشور: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('خطأ في التفاعل: $e');
    }
  }

  // إضافة تعليق
  static Future<PostComment> addComment(String postId, String content) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/api/posts/$postId/comment'),
        headers: _getHeaders(),
        body: json.encode({'content': content}),
      );

      if (response.statusCode == 201) {
        final data = json.decode(response.body);
        return PostComment.fromJson(data['comment']);
      } else {
        throw Exception('فشل في إضافة التعليق: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('خطأ في إضافة التعليق: $e');
    }
  }

  // البحث في المنشورات
  static Future<List<PostModel>> searchPosts(String query) async {
    try {
      final response = await http.get(
        Uri.parse(
            '$baseUrl/api/posts/search?query=${Uri.encodeComponent(query)}'),
        headers: _getHeaders(),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return (data['data'] as List)
            .map((post) => PostModel.fromJson(post))
            .toList();
      } else {
        throw Exception('فشل في البحث: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('خطأ في البحث: $e');
    }
  }

  // حذف منشور
  static Future<void> deletePost(String postId) async {
    try {
      final response = await http.delete(
        Uri.parse('$baseUrl/api/posts/$postId'),
        headers: _getHeaders(),
      );

      if (response.statusCode != 200) {
        throw Exception('فشل في حذف المنشور: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('خطأ في حذف المنشور: $e');
    }
  }

  // الحصول على headers المطلوبة
  static Map<String, String> _getHeaders() {
    // محاولة الحصول على التوكن من AppState
    String? token;
    try {
      // يمكن تحديث هذا حسب طريقة الحصول على التوكن في AppState
      token = null; // سيتم تحديثه لاحقاً
    } catch (e) {
      print('خطأ في الحصول على التوكن: $e');
    }

    return {
      'Content-Type': 'application/json',
      if (token != null) 'Authorization': 'Bearer $token',
    };
  }
}
