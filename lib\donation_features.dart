import 'package:flutter/material.dart';
import 'package:share_plus/share_plus.dart';
import 'package:url_launcher/url_launcher.dart';

enum DonationType {
  money,
  time,
  goods,
  crowdfunding,
}

class DonationFeature {
  final String id;
  final String title;
  final String description;
  final IconData icon;
  final DonationType type;
  final Color color;

  const DonationFeature({
    required this.id,
    required this.title,
    required this.description,
    required this.icon,
    required this.type,
    this.color = Colors.blue,
  });
}

class DonationFeatures {
  static List<DonationFeature> get allFeatures => [
        thankYouCertificate,
        remainingAmount,
        successStories,
        volunteerTime,
        donateGoods,
        crowdfunding,
        donationChallenges,
        donationHistory,
      ];

  static final thankYouCertificate = DonationFeature(
    id: 'thank_you_certificate',
    title: 'شهادة شكر',
    description: 'احصل على شهادة شكر قابلة للمشاركة بعد التبرع',
    icon: Icons.verified,
    type: DonationType.money,
    color: Colors.amber,
  );

  static final remainingAmount = DonationFeature(
    id: 'remaining_amount',
    title: 'حاسبة المبلغ المتبقي',
    description: 'تابع المبلغ المتبقي للوصول إلى الهدف',
    icon: Icons.calculate,
    type: DonationType.money,
    color: Colors.green,
  );

  static final successStories = DonationFeature(
    id: 'success_stories',
    title: 'قصص النجاح',
    description: 'اكتشف قصص المشاريع الممولة بنجاح',
    icon: Icons.emoji_events,
    type: DonationType.money,
    color: Colors.purple,
  );

  static final volunteerTime = DonationFeature(
    id: 'volunteer_time',
    title: 'التطوع بالوقت',
    description: 'تبرع بوقتك ومهاراتك للمساعدة',
    icon: Icons.access_time,
    type: DonationType.time,
    color: Colors.blue,
  );

  static final donateGoods = DonationFeature(
    id: 'donate_goods',
    title: 'التبرع بالسلع',
    description: 'تبرع بالسلع عوضاً عن المال',
    icon: Icons.card_giftcard,
    type: DonationType.goods,
    color: Colors.orange,
  );

  static final crowdfunding = DonationFeature(
    id: 'crowdfunding',
    title: 'التمويل الجماعي',
    description: 'انضم لحملات التمويل الجماعي',
    icon: Icons.people_alt,
    type: DonationType.crowdfunding,
    color: Colors.teal,
  );

  static final donationChallenges = DonationFeature(
    id: 'donation_challenges',
    title: 'تحديات التبرع',
    description: 'تحدى أصدقائك في التبرع',
    icon: Icons.emoji_events,
    type: DonationType.money,
    color: Colors.pink,
  );

  static final donationHistory = DonationFeature(
    id: 'donation_history',
    title: 'سجل التبرعات',
    description: 'تابع سجل تبرعاتك السابقة',
    icon: Icons.history,
    type: DonationType.money,
    color: Colors.blueGrey,
  );
}

class DonationFeatureCard extends StatelessWidget {
  final DonationFeature feature;
  final VoidCallback? onTap;

  const DonationFeatureCard({
    Key? key,
    required this.feature,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: feature.color.withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(feature.icon, color: feature.color, size: 28),
              ),
              const SizedBox(height: 12),
              Text(
                feature.title,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 4),
              Text(
                feature.description,
                style: Theme.of(context).textTheme.bodySmall,
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class DonationFeaturesGrid extends StatelessWidget {
  final List<DonationFeature> features;
  final Function(DonationFeature)? onFeatureTap;

  const DonationFeaturesGrid({
    Key? key,
    required this.features,
    this.onFeatureTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: const EdgeInsets.all(8.0),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 0.9,
        crossAxisSpacing: 8.0,
        mainAxisSpacing: 8.0,
      ),
      itemCount: features.length,
      itemBuilder: (context, index) {
        final feature = features[index];
        return DonationFeatureCard(
          feature: feature,
          onTap: () => onFeatureTap?.call(feature),
        );
      },
    );
  }
}

// Utility functions for donation features
class DonationUtils {
  static String formatAmount(double amount) {
    return '${amount.toStringAsFixed(2)} ر.س';
  }

  static double calculateRemainingAmount(double target, double raised) {
    return target > raised ? target - raised : 0;
  }

  static double calculateProgress(double target, double raised) {
    if (target <= 0) return 0;
    final progress = (raised / target).clamp(0.0, 1.0);
    return progress;
  }

  static Future<void> shareDonation(String message) async {
    await Share.share(
      message,
      subject: 'تبرع خيري',
    );
  }

  static Future<void> launchDonationUrl(String url) async {
    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url));
    } else {
      throw 'Could not launch $url';
    }
  }
}
