const express = require('express');
const router = express.Router();
const controller = require('../controllers/sharingGroup.controller');

// Basic CRUD routes
router.get('/', controller.getSharingGroups);
router.get('/:id', controller.getSharingGroupById);
router.post('/', controller.createSharingGroup);
router.put('/:id', controller.updateSharingGroup);
router.delete('/:id', controller.deleteSharingGroup);

// Member management
router.post('/:id/members', controller.addMember);
router.delete('/:id/members', controller.removeMember);

// Item management
router.post('/:id/items', controller.addItem);
router.delete('/:id/items', controller.removeItem);

module.exports = router;
