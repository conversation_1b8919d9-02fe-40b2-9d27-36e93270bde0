import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:provider/provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:http/http.dart' as http;
import 'package:socket_io_client/socket_io_client.dart' as io;
import 'dart:async';
import 'dart:typed_data';
import 'dart:convert';
import 'appstate.dart';
import 'services/post_service.dart';
import 'models/x_feed_models.dart';

// نموذج المستخدم
class PostUser {
  final String id;
  final String name;
  final String? avatarUrl;
  final String? handle;

  PostUser({
    required this.id,
    required this.name,
    this.avatarUrl,
    this.handle,
  });

  factory PostUser.fromJson(Map<String, dynamic> json) {
    return PostUser(
      id: json['_id'] ?? json['id'] ?? '',
      name: json['name'] ?? 'مستخدم غير معروف',
      avatarUrl: json['avatarUrl'],
      handle: json['handle'] ??
          '@${json['name']?.replaceAll(' ', '_').toLowerCase()}',
    );
  }
}

// نموذج التعليق
class PostComment {
  final String id;
  final String username;
  final String content;
  final DateTime date;

  PostComment({
    required this.id,
    required this.username,
    required this.content,
    required this.date,
  });

  factory PostComment.fromJson(Map<String, dynamic> json) {
    return PostComment(
      id: json['_id'] ?? DateTime.now().millisecondsSinceEpoch.toString(),
      username: json['username'] ?? 'مستخدم غير معروف',
      content: json['content'] ?? '',
      date:
          json['date'] != null ? DateTime.parse(json['date']) : DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'username': username,
      'content': content,
      'date': date.toIso8601String(),
    };
  }
}

// نموذج المنشور المحدث
class Post {
  final String id;
  final String userId;
  final String content;
  final String? mediaUrl;
  final String? mediaType;
  final int likes;
  final int loves;
  final int haha;
  final List<PostComment> comments;
  final DateTime createdAt;
  final PostUser? author;
  final bool promoted;
  final String privacy;
  final List<String> hashtags;
  final double? latitude;
  final double? longitude;

  // حالات محلية للواجهة
  bool isLiked;
  bool isLoved;
  bool isHaha;
  bool isSaved;
  bool isPinned;

  Post({
    required this.id,
    required this.userId,
    required this.content,
    this.mediaUrl,
    this.mediaType,
    required this.likes,
    required this.loves,
    required this.haha,
    required this.comments,
    required this.createdAt,
    this.author,
    required this.promoted,
    required this.privacy,
    required this.hashtags,
    this.latitude,
    this.longitude,
    this.isLiked = false,
    this.isLoved = false,
    this.isHaha = false,
    this.isSaved = false,
    this.isPinned = false,
  });

  factory Post.fromJson(Map<String, dynamic> json) {
    return Post(
      id: json['_id'] ?? json['id'] ?? '',
      userId: json['userId'] ?? '',
      content: json['content'] ?? '',
      mediaUrl: json['mediaUrl'],
      mediaType: json['mediaType'],
      likes: json['likes'] ?? 0,
      loves: json['loves'] ?? 0,
      haha: json['haha'] ?? 0,
      comments: (json['comments'] as List<dynamic>?)
              ?.map((comment) => PostComment.fromJson(comment))
              .toList() ??
          [],
      createdAt: json['createdAt'] != null
          ? DateTime.parse(json['createdAt'])
          : DateTime.now(),
      author: json['author'] != null
          ? PostUser.fromJson(json['author'])
          : (json['userId'] != null
              ? PostUser.fromJson(
                  {'_id': json['userId'], 'name': 'مستخدم غير معروف'})
              : null),
      promoted: json['promoted'] ?? false,
      privacy: json['privacy'] ?? 'public',
      hashtags: (json['hashtags'] as List<dynamic>?)
              ?.map((tag) => tag.toString())
              .toList() ??
          [],
      latitude: json['latitude']?.toDouble(),
      longitude: json['longitude']?.toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'content': content,
      'mediaUrl': mediaUrl,
      'mediaType': mediaType,
      'privacy': privacy,
      'latitude': latitude,
      'longitude': longitude,
    };
  }

  // الحصول على عدد التفاعلات الإجمالي
  int get totalInteractions => likes + loves + haha + comments.length;

  // الحصول على الوقت المنسق
  String get formattedTime {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inHours < 1) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inDays < 1) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} يوم';
    } else {
      return '${createdAt.day}/${createdAt.month}/${createdAt.year}';
    }
  }

  // نسخ المنشور مع تحديث بعض القيم
  Post copyWith({
    int? likes,
    int? loves,
    int? haha,
    List<PostComment>? comments,
    bool? isLiked,
    bool? isLoved,
    bool? isHaha,
    bool? isSaved,
    bool? isPinned,
  }) {
    return Post(
      id: id,
      userId: userId,
      content: content,
      mediaUrl: mediaUrl,
      mediaType: mediaType,
      likes: likes ?? this.likes,
      loves: loves ?? this.loves,
      haha: haha ?? this.haha,
      comments: comments ?? this.comments,
      createdAt: createdAt,
      author: author,
      promoted: promoted,
      privacy: privacy,
      hashtags: hashtags,
      latitude: latitude,
      longitude: longitude,
      isLiked: isLiked ?? this.isLiked,
      isLoved: isLoved ?? this.isLoved,
      isHaha: isHaha ?? this.isHaha,
      isSaved: isSaved ?? this.isSaved,
      isPinned: isPinned ?? this.isPinned,
    );
  }
}

class XFeedProvider with ChangeNotifier {
  List<XPost> _posts = [];
  bool _isLoading = false;
  bool _hasError = false;
  String _errorMessage = '';
  int _currentPage = 1;
  bool _hasMorePosts = true;
  io.Socket? _socket;

  // Getters
  List<XPost> get posts => _posts;
  bool get isLoading => _isLoading;
  bool get hasError => _hasError;
  String get errorMessage => _errorMessage;
  bool get hasMorePosts => _hasMorePosts;
  bool _isConnected = true;
  bool get isConnected => _isConnected;

  // تهيئة الاتصال
  void initialize() {
    _connectSocket();
    loadPosts();
  }

  // تحميل المنشورات من الباك إند
  Future<void> loadPosts({bool refresh = false}) async {
    if (_isLoading) return;

    try {
      _setLoading(true);
      _setError(false, '');

      if (refresh) {
        _currentPage = 1;
        _hasMorePosts = true;
      }

      final newPosts = await PostService.getPosts(
        page: _currentPage,
        limit: 10,
        sortBy: 'latest',
      );

      if (refresh) {
        _posts = _convertPostModelsToXPost(newPosts);
      } else {
        _posts.addAll(_convertPostModelsToXPost(newPosts));
      }

      _hasMorePosts = newPosts.length >= 10;
      _currentPage++;
    } catch (e) {
      _setError(true, e.toString());
    } finally {
      _setLoading(false);
    }
  }

  // تحويل PostModel إلى XPost للتوافق مع الواجهة الحالية
  List<XPost> _convertPostModelsToXPost(List<PostModel> postModels) {
    return postModels
        .map((postModel) => XPost(
              id: postModel.id,
              userId: postModel.userId,
              content: postModel.content,
              mediaUrl: postModel.mediaUrl,
              mediaType: postModel.mediaType,
              likes: postModel.likes,
              loves: postModel.loves,
              haha: postModel.haha,
              comments: postModel.comments
                  .map((comment) => XPostComment(
                        id: comment.id,
                        username: comment.username,
                        content: comment.content,
                        date: comment.date,
                      ))
                  .toList(),
              createdAt: postModel.createdAt,
              author: postModel.author != null
                  ? XPostUser(
                      id: postModel.author!.id,
                      name: postModel.author!.name,
                      avatarUrl: postModel.author!.avatarUrl,
                      handle: postModel.author!.handle,
                    )
                  : null,
              promoted: postModel.promoted,
              privacy: postModel.privacy,
              hashtags: postModel.hashtags,
              latitude: postModel.latitude,
              longitude: postModel.longitude,
              isLiked: postModel.isLiked,
              isLoved: postModel.isLoved,
              isHaha: postModel.isHaha,
              isSaved: postModel.isSaved,
            ))
        .toList();
  }

  // التفاعل مع منشور
  Future<void> toggleLike(String postId) async {
    final index = _posts.indexWhere((post) => post.id == postId);
    if (index == -1) return;

    try {
      // تحديث محلي فوري
      final post = _posts[index];
      final newPost = post.copyWith(
        isLiked: !post.isLiked,
        likes: post.isLiked ? post.likes - 1 : post.likes + 1,
      );
      _posts[index] = newPost;
      notifyListeners();

      // إرسال إلى الباك إند
      await PostService.reactToPost(postId, 'like');
    } catch (e) {
      // إعادة التحديث في حالة الخطأ
      final post = _posts[index];
      final revertPost = post.copyWith(
        isLiked: !post.isLiked,
        likes: post.isLiked ? post.likes - 1 : post.likes + 1,
      );
      _posts[index] = revertPost;
      notifyListeners();
      _setError(true, 'فشل في الإعجاب: $e');
    }
  }

  // حفظ منشور
  void toggleSave(String postId) {
    final index = _posts.indexWhere((post) => post.id == postId);
    if (index != -1) {
      final post = _posts[index];
      _posts[index] = post.copyWith(isSaved: !post.isSaved);
      notifyListeners();
    }
  }

  // إضافة منشور جديد
  Future<void> addPost(String content) async {
    try {
      _setLoading(true);

      final newPostModel = await PostService.createPost(
        content: content,
        privacy: 'public',
      );

      final newPost = _convertPostModelsToXPost([newPostModel]).first;
      _posts.insert(0, newPost);
      notifyListeners();
    } catch (e) {
      _setError(true, 'فشل في إنشاء المنشور: $e');
    } finally {
      _setLoading(false);
    }
  }

  // إضافة تعليق
  Future<void> addReply(String postId, String content) async {
    try {
      final newComment = await PostService.addComment(postId, content);

      final index = _posts.indexWhere((post) => post.id == postId);
      if (index != -1) {
        final post = _posts[index];
        final updatedComments = List<XPostComment>.from(post.comments)
          ..add(XPostComment(
            id: newComment.id,
            username: newComment.username,
            content: newComment.content,
            date: newComment.date,
          ));
        _posts[index] = post.copyWith(comments: updatedComments);
        notifyListeners();
      }
    } catch (e) {
      _setError(true, 'فشل في إضافة التعليق: $e');
    }
  }

  // تحميل المزيد من المنشورات
  Future<void> loadMorePosts() async {
    if (!_hasMorePosts || _isLoading) return;
    await loadPosts();
  }

  // فحص الاتصال
  void checkConnectivity() async {
    await Future.delayed(const Duration(seconds: 1));
    _isConnected = true;
    notifyListeners();
  }

  // إعداد Socket.IO
  void _connectSocket() {
    try {
      _socket = io.io(AppState.getBackendUrl(), <String, dynamic>{
        'transports': ['websocket'],
        'autoConnect': false,
      });

      _socket?.connect();

      _socket?.on('connect', (_) {
        print('متصل بـ Socket.IO');
        _isConnected = true;
        notifyListeners();
      });

      _socket?.on('disconnect', (_) {
        print('انقطع الاتصال مع Socket.IO');
        _isConnected = false;
        notifyListeners();
      });

      _socket?.on('new_post', (data) {
        try {
          final newPostModel = PostModel.fromJson(data);
          final newPost = _convertPostModelsToXPost([newPostModel]).first;
          _posts.insert(0, newPost);
          notifyListeners();
        } catch (e) {
          print('خطأ في استقبال منشور جديد: $e');
        }
      });

      _socket?.on('post_updated', (data) {
        try {
          final updatedPostModel = PostModel.fromJson(data);
          final index =
              _posts.indexWhere((post) => post.id == updatedPostModel.id);
          if (index != -1) {
            final updatedPost =
                _convertPostModelsToXPost([updatedPostModel]).first;
            _posts[index] = updatedPost;
            notifyListeners();
          }
        } catch (e) {
          print('خطأ في تحديث المنشور: $e');
        }
      });
    } catch (e) {
      print('خطأ في الاتصال بـ Socket.IO: $e');
      _isConnected = false;
    }
  }

  // دوال مساعدة
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(bool hasError, String message) {
    _hasError = hasError;
    _errorMessage = message;
    notifyListeners();
  }

  @override
  void dispose() {
    _socket?.disconnect();
    _socket?.dispose();
    super.dispose();
  }
}

class XFeedPage extends StatefulWidget {
  const XFeedPage({super.key});

  @override
  State<XFeedPage> createState() => _XFeedPageState();
}

class _XFeedPageState extends State<XFeedPage> with TickerProviderStateMixin {
  late ThemeData _currentTheme;
  final ScrollController _scrollController = ScrollController();
  double _scrollProgress = 0.0;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _currentTheme = Theme.of(context);
  }

  @override
  void initState() {
    super.initState();
    // Use WidgetsBinding to ensure the first frame is rendered before accessing provider
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final provider = Provider.of<XFeedProvider>(context, listen: false);
      provider.initialize();

      _scrollController.addListener(() {
        if (!_scrollController.hasClients) return;

        final maxScroll = _scrollController.position.maxScrollExtent;
        final currentScroll = _scrollController.position.pixels;

        if (currentScroll >= maxScroll * 0.8) {
          // Load more when 80% scrolled
          provider.loadMorePosts();
        }

        if (mounted) {
          setState(() {
            _scrollProgress = maxScroll > 0 ? currentScroll / maxScroll : 0.0;
          });
        }
      });
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    // Remove the nested ChangeNotifierProvider since we're providing it in main.dart
    return Directionality(
      textDirection: TextDirection.rtl,
      child: DefaultTabController(
        length: 2,
        child: Scaffold(
          appBar: AppBar(
            title: Text('X', style: theme.appBarTheme.titleTextStyle),
            leading: CircleAvatar(
              backgroundImage:
                  CachedNetworkImageProvider('https://via.placeholder.com/50'),
            ),
            actions: [
              IconButton(
                icon: const Icon(Icons.search),
                onPressed: () {
                  showSearch(context: context, delegate: XSearchDelegate());
                },
              ),
            ],
            backgroundColor: theme.appBarTheme.backgroundColor,
            foregroundColor: theme.appBarTheme.titleTextStyle?.color,
            elevation: theme.appBarTheme.elevation ?? 1,
            bottom: TabBar(
              tabs: [
                Tab(
                  text: 'للمتابعين',
                  icon: const Icon(Icons.people),
                  iconMargin: const EdgeInsets.only(bottom: 4.0),
                ),
                Tab(
                  text: 'الجميع',
                  icon: const Icon(Icons.public),
                  iconMargin: const EdgeInsets.only(bottom: 4.0),
                ),
              ],
              labelColor: theme.tabBarTheme.labelColor ?? colorScheme.primary,
              unselectedLabelColor: theme.tabBarTheme.unselectedLabelColor ??
                  colorScheme.onSurfaceVariant,
              indicatorColor:
                  theme.tabBarTheme.indicatorColor ?? colorScheme.primary,
            ),
          ),
          body: Stack(
            children: [
              Consumer<XFeedProvider>(
                builder: (context, provider, child) {
                  return provider.isConnected
                      ? TabBarView(
                          children: [
                            _buildFeed(context, provider, true),
                            _buildFeed(context, provider, false),
                          ],
                        )
                      : Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(Icons.signal_wifi_off,
                                  size: 48, color: colorScheme.error),
                              const SizedBox(height: 16),
                              Text(
                                'لا يوجد اتصال بالإنترنت',
                                style: theme.textTheme.titleLarge?.copyWith(
                                  color: colorScheme.error,
                                ),
                              ),
                              const SizedBox(height: 8),
                              Text(
                                'تفقد اتصالك بالإنترنت وحاول مرة أخرى',
                                style: theme.textTheme.bodyMedium?.copyWith(
                                  color: colorScheme.onSurfaceVariant,
                                ),
                              ),
                              const SizedBox(height: 16),
                              FilledButton.tonal(
                                onPressed: () => provider.checkConnectivity(),
                                child: Text('إعادة المحاولة',
                                    style: theme.textTheme.labelLarge),
                              ),
                            ],
                          ),
                        );
                },
              ),
              if (_scrollProgress.isFinite && _scrollProgress > 0)
                Positioned(
                  bottom: 0,
                  left: 0,
                  right: 0,
                  child: LinearProgressIndicator(
                    value: _scrollProgress.clamp(0.0, 1.0),
                    backgroundColor: colorScheme.surface,
                    color: colorScheme.primary,
                  ),
                ),
            ],
          ),
          floatingActionButton: FloatingActionButton(
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => ComposePostPage(
                    onPost: (content) {
                      Provider.of<XFeedProvider>(context, listen: false)
                          .addPost(content);
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('تم نشر التغريدة')),
                      );
                    },
                  ),
                ),
              );
            },
            backgroundColor: theme.floatingActionButtonTheme.backgroundColor ??
                colorScheme.primary,
            foregroundColor: theme.floatingActionButtonTheme.foregroundColor ??
                colorScheme.onPrimary,
            child: const Icon(Icons.edit),
          ),
        ),
      ),
    );
  }

  Widget _buildFeed(
      BuildContext context, XFeedProvider provider, bool isFollowing) {
    // Using provider parameter to avoid lint warning about unused parameter
    final _ = isFollowing;
    return RefreshIndicator(
      color: _currentTheme.colorScheme.primary,
      displacement: 20,
      edgeOffset: 20,
      onRefresh: () async {
        await provider.loadPosts(refresh: true);
        if (provider.hasError) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('خطأ في التحديث: ${provider.errorMessage}')),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('تم تحديث المحتوى')),
          );
        }
      },
      child: AnimationLimiter(
        child: ListView.builder(
          controller: _scrollController,
          itemCount: provider.posts.length,
          itemBuilder: (context, index) {
            return AnimationConfiguration.staggeredList(
              position: index,
              duration: const Duration(milliseconds: 375),
              child: SlideAnimation(
                verticalOffset: 50.0,
                child: FadeInAnimation(
                  child: _buildPostCard(context, provider.posts[index], index),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildPostCard(BuildContext context, XPost post, int index) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;

    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => PostDetailsPage(post: post),
          ),
        );
      },
      child: Card(
        elevation: 4,
        margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
          side: post.isPinned
              ? BorderSide(color: theme.colorScheme.primary, width: 1)
              : BorderSide.none,
        ),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  CircleAvatar(
                    backgroundImage: CachedNetworkImageProvider(post.avatar),
                    radius: 20,
                  ),
                  const SizedBox(width: 10),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          post.username,
                          style: textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                          semanticsLabel: 'اسم المستخدم: ${post.username}',
                        ),
                        Text(
                          post.handle,
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                          semanticsLabel: 'معرف المستخدم: ${post.handle}',
                        ),
                      ],
                    ),
                  ),
                  Text(
                    post.time,
                    style: textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                    semanticsLabel: 'الوقت: ${post.time}',
                  ),
                  if (post.isPinned) ...[
                    const SizedBox(width: 8),
                    Icon(
                      Icons.push_pin,
                      size: 16,
                      color: theme.colorScheme.primary,
                      semanticLabel: 'تغريدة مثبتة',
                    ),
                  ],
                ],
              ),
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 12),
                child: _buildRichContent(context, post.content),
              ),
              if (post.media != null)
                ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: CachedNetworkImage(
                    imageUrl: post.media!,
                    width: double.infinity,
                    height: 200,
                    fit: BoxFit.cover,
                    placeholder: (context, url) =>
                        const Center(child: CircularProgressIndicator()),
                    errorWidget: (context, url, error) =>
                        const Icon(Icons.error),
                    imageBuilder: (context, imageProvider) => Image(
                      image: imageProvider,
                      semanticLabel: 'صورة التغريدة',
                    ),
                  ),
                ),
              const SizedBox(height: 12),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildInteraction(
                    Icons.favorite_border,
                    post.likes,
                    post.isLiked,
                    () => Provider.of<XFeedProvider>(context, listen: false)
                        .toggleLike(post.id),
                    semanticLabel: post.isLiked ? 'إلغاء الإعجاب' : 'إعجاب',
                  ),
                  _buildInteraction(
                    Icons.comment,
                    post.comments.length,
                    false,
                    () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => RepliesPage(post: post),
                        ),
                      );
                    },
                    semanticLabel: 'التعليقات',
                  ),
                  _buildInteraction(
                    Icons.repeat,
                    post.retweets,
                    false,
                    () {}, // Retweet functionality placeholder
                    semanticLabel: 'إعادة التغريد',
                  ),
                  _buildInteraction(
                    Icons.share,
                    null,
                    false,
                    () => Share.share('${post.content} ${post.handle}'),
                    semanticLabel: 'مشاركة',
                  ),
                  _buildInteraction(
                    Icons.bookmark_border,
                    null,
                    post.isSaved,
                    () => Provider.of<XFeedProvider>(context, listen: false)
                        .toggleSave(post.id),
                    semanticLabel: post.isSaved ? 'إلغاء الحفظ' : 'حفظ',
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInteraction(
    IconData icon,
    int? count,
    bool isActive,
    VoidCallback onTap, {
    required String semanticLabel,
  }) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return GestureDetector(
      onTap: onTap,
      child: Semantics(
        label: semanticLabel,
        child: Row(
          children: [
            Icon(
              isActive && icon == Icons.favorite_border
                  ? Icons.favorite
                  : isActive && icon == Icons.bookmark_border
                      ? Icons.bookmark
                      : icon,
              color:
                  isActive ? colorScheme.primary : colorScheme.onSurfaceVariant,
              size: 20,
            ),
            if (count != null) ...[
              const SizedBox(width: 4),
              Text(
                count.toString(),
                style: theme.textTheme.bodySmall?.copyWith(
                  color: isActive
                      ? colorScheme.primary
                      : colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildRichContent(BuildContext context, String content) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final parts = content.split(RegExp(r'(\s+)'));

    return RichText(
      text: TextSpan(
        children: parts.map((part) {
          if (part.startsWith('#') || part.startsWith('@')) {
            return TextSpan(
              text: part,
              style: TextStyle(
                color: colorScheme.primary,
                fontSize: theme.textTheme.bodyLarge!.fontSize,
                decoration: TextDecoration.underline,
              ),
              recognizer: (TapGestureRecognizer()
                ..onTap = () {
                  // Navigate to hashtag or user profile
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('فتح: $part')),
                  );
                }),
            );
          }
          return TextSpan(
            text: part,
            style: theme.textTheme.bodyLarge,
          );
        }).toList(),
      ),
    );
  }
}

class ComposePostPage extends StatefulWidget {
  final Function(String) onPost;

  const ComposePostPage({super.key, required this.onPost});

  @override
  State<ComposePostPage> createState() => _ComposePostPageState();
}

class _ComposePostPageState extends State<ComposePostPage> {
  final _controller = TextEditingController();
  final _focusNode = FocusNode();

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      appBar: AppBar(
        title: const Text('تغريدة جديدة'),
        actions: [
          TextButton(
            onPressed: _controller.text.trim().isEmpty
                ? null
                : () {
                    widget.onPost(_controller.text);
                    Navigator.pop(context);
                  },
            child: const Text('نشر'),
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Expanded(
              child: TextField(
                controller: _controller,
                focusNode: _focusNode,
                maxLines: null,
                expands: true,
                textDirection: TextDirection.rtl,
                style: TextStyle(
                  fontSize: 16 * MediaQuery.textScalerOf(context).scale(1.0),
                ),
                decoration: const InputDecoration(
                  hintText: 'ما الذي يحدث؟',
                  border: InputBorder.none,
                ),
                onChanged: (value) => setState(() {}),
              ),
            ),
            const Divider(height: 1),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                IconButton(
                  icon: const Icon(Icons.photo_library),
                  onPressed: () {
                    // Add image functionality placeholder
                  },
                  tooltip: 'إضافة صورة',
                ),
                FilledButton(
                  onPressed: _controller.text.trim().isEmpty
                      ? null
                      : () {
                          widget.onPost(_controller.text);
                          Navigator.pop(context);
                        },
                  child: const Text('تغريد'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class PostDetailsPage extends StatelessWidget {
  final XPost post;

  const PostDetailsPage({super.key, required this.post});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      appBar: AppBar(
        title: const Text('التفاصيل'),
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      CircleAvatar(
                        backgroundImage:
                            CachedNetworkImageProvider(post.avatar),
                        radius: 24,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              post.username,
                              style: theme.textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                              semanticsLabel: 'اسم المستخدم: ${post.username}',
                            ),
                            Text(
                              post.handle,
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: colorScheme.onSurfaceVariant,
                              ),
                              semanticsLabel: 'معرف المستخدم: ${post.handle}',
                            ),
                          ],
                        ),
                      ),
                      Text(
                        post.time,
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                        semanticsLabel: 'الوقت: ${post.time}',
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  _buildRichContent(context, post.content),
                  if (post.media != null) ...[
                    const SizedBox(height: 16),
                    ClipRRect(
                      borderRadius: BorderRadius.circular(12),
                      child: CachedNetworkImage(
                        imageUrl: post.media!,
                        width: double.infinity,
                        height: 200,
                        fit: BoxFit.cover,
                        placeholder: (context, url) =>
                            const Center(child: CircularProgressIndicator()),
                        errorWidget: (context, url, error) =>
                            const Icon(Icons.error),
                      ),
                    ),
                  ],
                  const SizedBox(height: 16),
                  Text(
                    '${post.time} · تطبيق X',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: colorScheme.onSurfaceVariant,
                    ),
                  ),
                  const Divider(height: 32),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      _buildStatItem(Icons.comment, '${post.comments}'),
                      _buildStatItem(Icons.repeat, '${post.retweets}'),
                      _buildStatItem(Icons.favorite_border, '${post.likes}'),
                      _buildStatItem(Icons.share, ''),
                    ],
                  ),
                ],
              ),
            ),
            const Divider(height: 1),
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: Text('الردود'),
            ),
            if (post.replies.isEmpty)
              Padding(
                padding: const EdgeInsets.all(32.0),
                child: Column(
                  children: [
                    Icon(
                      Icons.comment_outlined,
                      size: 48,
                      color: Colors.grey[400],
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'لا توجد ردود بعد',
                      style: theme.textTheme.titleMedium?.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'كن أول من يعلق على هذه التغريدة',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              )
            else
              AnimationLimiter(
                child: ListView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: post.replies.length,
                  itemBuilder: (context, index) {
                    final reply = post.replies[index];
                    return AnimationConfiguration.staggeredList(
                      position: index,
                      duration: const Duration(milliseconds: 375),
                      child: SlideAnimation(
                        verticalOffset: 50.0,
                        child: FadeInAnimation(
                          child: ListTile(
                            leading: CircleAvatar(
                              backgroundColor: Colors.grey[200],
                              child: Text(reply['username']![0]),
                            ),
                            title: Text(reply['username']!),
                            subtitle: Text(reply['content']!),
                            trailing: Text(
                              reply['time']!,
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: colorScheme.onSurfaceVariant,
                              ),
                            ),
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
          ],
        ),
      ),
      bottomNavigationBar: BottomAppBar(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
          child: Row(
            children: [
              IconButton(
                icon: const Icon(Icons.comment_outlined),
                onPressed: () {},
                tooltip: 'كتابة رد',
              ),
              const SizedBox(width: 8),
              Expanded(
                child: TextField(
                  decoration: InputDecoration(
                    hintText: 'اكتب ردًا...',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(20.0),
                      borderSide: BorderSide.none,
                    ),
                    filled: true,
                    contentPadding:
                        const EdgeInsets.symmetric(horizontal: 16.0),
                  ),
                  onSubmitted: (value) {
                    if (value.trim().isNotEmpty) {
                      Provider.of<XFeedProvider>(context, listen: false)
                          .addReply(post.id, value);
                    }
                  },
                ),
              ),
              IconButton(
                icon: const Icon(Icons.send),
                onPressed: () {
                  // Send reply functionality handled by TextField onSubmitted
                },
                tooltip: 'إرسال الرد',
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRichContent(BuildContext context, String content) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final parts = content.split(RegExp(r'(\s+)'));

    return RichText(
      text: TextSpan(
        children: parts.map((part) {
          if (part.startsWith('#') || part.startsWith('@')) {
            return TextSpan(
              text: part,
              style: TextStyle(
                color: colorScheme.primary,
                fontSize: theme.textTheme.bodyLarge!.fontSize,
                decoration: TextDecoration.underline,
              ),
              recognizer: TapGestureRecognizer()
                ..onTap = () {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('فتح: $part')),
                  );
                },
            );
          }
          return TextSpan(
            text: part,
            style: theme.textTheme.bodyLarge,
          );
        }).toList(),
      ),
    );
  }

  Widget _buildStatItem(IconData icon, String count) {
    return Row(
      children: [
        Icon(icon, size: 20, color: Colors.grey[600]),
        const SizedBox(width: 4),
        Text(
          count,
          style: TextStyle(color: Colors.grey[600]),
        ),
      ],
    );
  }
}

class RepliesPage extends StatelessWidget {
  final XPost post;

  const RepliesPage({super.key, required this.post});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      appBar: AppBar(
        title: const Text('الردود'),
      ),
      body: post.replies.isEmpty
          ? Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.comment_outlined,
                    size: 64,
                    color: Colors.grey[400],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'لا توجد ردود بعد',
                    style: theme.textTheme.titleLarge?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            )
          : AnimationLimiter(
              child: ListView.builder(
                itemCount: post.replies.length,
                itemBuilder: (context, index) {
                  final reply = post.replies[index];
                  return AnimationConfiguration.staggeredList(
                    position: index,
                    duration: const Duration(milliseconds: 375),
                    child: SlideAnimation(
                      verticalOffset: 50.0,
                      child: FadeInAnimation(
                        child: ListTile(
                          leading: CircleAvatar(
                            backgroundColor: Colors.grey[200],
                            child: Text(reply['username']![0]),
                          ),
                          title: Text(reply['username']!),
                          subtitle: Text(reply['content']!),
                          trailing: Text(
                            reply['time']!,
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: colorScheme.onSurfaceVariant,
                            ),
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
    );
  }
}

class XSearchDelegate extends SearchDelegate<String> {
  final List<Map<String, dynamic>> _recentSearches = [
    {'text': 'Flutter', 'type': 'بحث سابق'},
    {'text': 'Dart', 'type': 'بحث سابق'},
    {'text': 'برمجة', 'type': 'بحث سابق'},
  ];

  final List<Map<String, dynamic>> _suggestions = [
    {'text': 'Flutter', 'type': 'اقتراح'},
    {'text': 'Dart', 'type': 'لغة برمجة'},
    {'text': 'برمجة', 'type': 'موضوع'},
    {'text': 'تطوير تطبيقات', 'type': 'موضوع'},
    {'text': 'واجهة المستخدم', 'type': 'تصميم'},
  ];

  @override
  List<Widget> buildActions(BuildContext context) {
    return [
      IconButton(
        icon: const Icon(Icons.clear),
        onPressed: () {
          query = '';
        },
        tooltip: 'مسح البحث',
      ),
    ];
  }

  @override
  Widget buildLeading(BuildContext context) {
    return IconButton(
      icon: const Icon(Icons.arrow_back),
      onPressed: () {
        close(context, '');
      },
      tooltip: 'رجوع',
    );
  }

  @override
  Widget buildResults(BuildContext context) {
    final results = _suggestions
        .where(
            (item) => item['text'].toLowerCase().contains(query.toLowerCase()))
        .toList();

    return ListView.builder(
      itemCount: results.length,
      itemBuilder: (context, index) {
        final item = results[index];
        return ListTile(
          title: Text(item['text']),
          subtitle: Text(item['type']),
          onTap: () {
            close(context, item['text']);
          },
        );
      },
    );
  }

  @override
  Widget buildSuggestions(BuildContext context) {
    final suggestions = query.isEmpty
        ? _recentSearches
        : _suggestions
            .where((item) =>
                item['text'].toLowerCase().contains(query.toLowerCase()))
            .toList();

    return ListView.builder(
      itemCount: suggestions.length,
      itemBuilder: (context, index) {
        final item = suggestions[index];
        return ListTile(
          leading: query.isEmpty ? const Icon(Icons.history) : null,
          title: Text(item['text']),
          subtitle: Text(item['type']),
          onTap: () {
            query = item['text'];
            showResults(context);
          },
        );
      },
    );
  }
}
