# Rating System API Endpoints

## نظرة عامة

هذا الملف يوثق نقاط النهاية المطلوبة لنظام التقييم في التطبيق.

## نقاط النهاية

### 1. جلب تقييمات إعلان معين

**GET** `/api/ratings/:adId`

**المعاملات:**
- `page` (query): رقم الصفحة (افتراضي: 1)
- `limit` (query): عدد العناصر في الصفحة (افتراضي: 10)
- `sortBy` (query): طريقة الترتيب (newest, oldest, highest, lowest, helpful)

**الاستجابة:**
```json
{
  "success": true,
  "ratings": [
    {
      "_id": "rating_id",
      "adId": "ad_id",
      "donorId": "donor_id",
      "donorName": "اسم المتبرع",
      "donorAvatarUrl": "https://example.com/avatar.jpg",
      "value": 4.5,
      "comment": "تعليق التقييم",
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z",
      "isVerified": true,
      "helpfulCount": 5
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 25,
    "pages": 3
  }
}
```

### 2. جلب إحصائيات التقييم

**GET** `/api/ratings/:adId/stats`

**الاستجابة:**
```json
{
  "success": true,
  "averageRating": 4.2,
  "totalRatings": 15,
  "ratingDistribution": {
    "1": 0,
    "2": 1,
    "3": 2,
    "4": 8,
    "5": 4
  },
  "totalComments": 12
}
```

### 3. إضافة تقييم جديد

**POST** `/api/ratings`

**Headers:**
```
Authorization: Bearer <token>
Content-Type: application/json
```

**Body:**
```json
{
  "adId": "ad_id",
  "donorId": "donor_id",
  "value": 4.5,
  "comment": "تعليق التقييم (اختياري)"
}
```

**الاستجابة:**
```json
{
  "success": true,
  "rating": {
    "_id": "rating_id",
    "adId": "ad_id",
    "donorId": "donor_id",
    "donorName": "اسم المتبرع",
    "donorAvatarUrl": "https://example.com/avatar.jpg",
    "value": 4.5,
    "comment": "تعليق التقييم",
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": null,
    "isVerified": false,
    "helpfulCount": 0
  }
}
```

### 4. تحديث تقييم موجود

**PUT** `/api/ratings/:ratingId`

**Headers:**
```
Authorization: Bearer <token>
Content-Type: application/json
```

**Body:**
```json
{
  "value": 5.0,
  "comment": "تعليق محدث"
}
```

**الاستجابة:**
```json
{
  "success": true,
  "rating": {
    "_id": "rating_id",
    "adId": "ad_id",
    "donorId": "donor_id",
    "donorName": "اسم المتبرع",
    "donorAvatarUrl": "https://example.com/avatar.jpg",
    "value": 5.0,
    "comment": "تعليق محدث",
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T01:00:00.000Z",
    "isVerified": false,
    "helpfulCount": 0
  }
}
```

### 5. حذف تقييم

**DELETE** `/api/ratings/:ratingId`

**Headers:**
```
Authorization: Bearer <token>
```

**الاستجابة:**
```json
{
  "success": true,
  "message": "تم حذف التقييم بنجاح"
}
```

### 6. تسجيل التقييم كمفيد

**POST** `/api/ratings/:ratingId/helpful`

**Headers:**
```
Authorization: Bearer <token>
```

**الاستجابة:**
```json
{
  "success": true,
  "helpfulCount": 6,
  "message": "تم تسجيل التقييم كمفيد"
}
```

## نموذج قاعدة البيانات

### Rating Schema

```javascript
const ratingSchema = new mongoose.Schema({
  adId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'DonationAd',
    required: true
  },
  donorId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  donorName: {
    type: String,
    required: true
  },
  donorAvatarUrl: {
    type: String
  },
  value: {
    type: Number,
    required: true,
    min: 1,
    max: 5
  },
  comment: {
    type: String,
    maxlength: 500
  },
  isVerified: {
    type: Boolean,
    default: false
  },
  helpfulCount: {
    type: Number,
    default: 0
  },
  helpfulUsers: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }]
}, {
  timestamps: true
});

// Index for efficient queries
ratingSchema.index({ adId: 1, createdAt: -1 });
ratingSchema.index({ donorId: 1, adId: 1 }, { unique: true });
```

## التحقق من الصلاحيات

### Middleware للتحقق من الصلاحيات

```javascript
const checkRatingPermission = async (req, res, next) => {
  try {
    const { ratingId } = req.params;
    const rating = await Rating.findById(ratingId);
    
    if (!rating) {
      return res.status(404).json({ 
        success: false, 
        message: 'التقييم غير موجود' 
      });
    }
    
    // التحقق من أن المستخدم هو صاحب التقييم أو مدير
    if (rating.donorId.toString() !== req.user.id && req.user.role !== 'admin') {
      return res.status(403).json({ 
        success: false, 
        message: 'غير مصرح لك بتعديل هذا التقييم' 
      });
    }
    
    req.rating = rating;
    next();
  } catch (error) {
    res.status(500).json({ 
      success: false, 
      message: 'خطأ في الخادم' 
    });
  }
};
```

## معالجة الأخطاء

### رموز الحالة

- `200`: نجح الطلب
- `201`: تم إنشاء المورد بنجاح
- `400`: بيانات غير صحيحة
- `401`: غير مصرح (مشكلة في المصادقة)
- `403`: ممنوع (مشكلة في الصلاحيات)
- `404`: المورد غير موجود
- `409`: تضارب (مثل تقييم مكرر)
- `500`: خطأ في الخادم

### نموذج رسالة الخطأ

```json
{
  "success": false,
  "message": "رسالة الخطأ",
  "errors": [
    {
      "field": "value",
      "message": "قيمة التقييم يجب أن تكون بين 1 و 5"
    }
  ]
}
```

## التحقق من صحة البيانات

### Validation Rules

```javascript
const validateRating = (req, res, next) => {
  const { value, comment } = req.body;
  
  // التحقق من قيمة التقييم
  if (!value || value < 1 || value > 5) {
    return res.status(400).json({
      success: false,
      message: 'قيمة التقييم يجب أن تكون بين 1 و 5'
    });
  }
  
  // التحقق من طول التعليق
  if (comment && comment.length > 500) {
    return res.status(400).json({
      success: false,
      message: 'التعليق يجب أن يكون أقل من 500 حرف'
    });
  }
  
  next();
};
```

## التحديثات المباشرة (WebSocket)

### أحداث WebSocket

```javascript
// عند إضافة تقييم جديد
io.emit('newRating', {
  adId: rating.adId,
  rating: rating
});

// عند تحديث إحصائيات التقييم
io.emit('ratingStatsUpdated', {
  adId: rating.adId,
  stats: updatedStats
});
```

## الأمان

### حماية من التقييمات المكررة

```javascript
// التحقق من عدم وجود تقييم مكرر
const existingRating = await Rating.findOne({
  adId: req.body.adId,
  donorId: req.user.id
});

if (existingRating) {
  return res.status(409).json({
    success: false,
    message: 'لقد قمت بتقييم هذا الإعلان من قبل'
  });
}
```

### حماية من التقييمات المزيفة

```javascript
// التحقق من أن المستخدم قد تبرع للإعلان
const donation = await Donation.findOne({
  adId: req.body.adId,
  donorId: req.user.id
});

if (!donation) {
  return res.status(403).json({
    success: false,
    message: 'يمكنك تقييم الإعلانات التي تبرعت لها فقط'
  });
}
``` 