const MedicalRecord = require('../models/MedicalRecordmodel');
const User = require('../models/user.model');
const ErrorResponse = require('../utils/errorResponse');
const asyncHandler = require('../middleware/asyncmiddleware');
const path = require('path');
const fs = require('fs');
const multer = require('multer');

// إعداد تخزين الملفات
const storage = multer.diskStorage({
  destination: function(req, file, cb) {
    const uploadPath = './public/uploads/medical';
    if (!fs.existsSync(uploadPath)) {
      fs.mkdirSync(uploadPath, { recursive: true });
    }
    cb(null, uploadPath);
  },
  filename: function(req, file, cb) {
    cb(null, `medical_${Date.now()}${path.extname(file.originalname)}`);
  }
});

// فلترة الملفات
const fileFilter = (req, file, cb) => {
  // قبول الصور والملفات PDF والمستندات
  if (
    file.mimetype.startsWith('image') ||
    file.mimetype === 'application/pdf' ||
    file.mimetype.includes('document')
  ) {
    cb(null, true);
  } else {
    cb(new ErrorResponse('نوع الملف غير مدعوم', 400), false);
  }
};

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 10000000 // 10 ميجابايت
  },
  fileFilter: fileFilter
});

// @desc    الحصول على جميع السجلات الطبية
// @route   GET /api/medicalRecords
// @access  Private (للمستخدمين الطبيين فقط)
exports.getMedicalRecords = asyncHandler(async (req, res, next) => {
  // التحقق من نوع المستخدم
  if (req.user.userType !== 'medical' && req.user.role !== 'admin') {
    return next(new ErrorResponse('غير مصرح لك بالوصول إلى السجلات الطبية', 403));
  }

  const records = await MedicalRecord.find();

  res.status(200).json({
    success: true,
    count: records.length,
    data: records
  });
});

// @desc    إنشاء سجل طبي جديد
// @route   POST /api/medicalRecords
// @access  Private (للمستخدمين الطبيين فقط)
exports.createMedicalRecord = asyncHandler(async (req, res, next) => {
  // التحقق من نوع المستخدم
  if (req.user.userType !== 'medical' && req.user.role !== 'admin') {
    return next(new ErrorResponse('غير مصرح لك بإضافة سجلات طبية', 403));
  }

  // التعامل مع تحميل الملف
  const uploadMiddleware = upload.single('file');

  uploadMiddleware(req, res, async (err) => {
    if (err) {
      return next(new ErrorResponse(`خطأ في تحميل الملف: ${err.message}`, 400));
    }

    // التحقق من وجود ملف
    if (!req.file) {
      return next(new ErrorResponse('الرجاء تحميل ملف للسجل الطبي', 400));
    }

    // التحقق من وجود اسم المستخدم للمريض
    if (!req.body.patientUsername) {
      return next(new ErrorResponse('الرجاء تحديد اسم المستخدم للمريض', 400));
    }

    // تحديد نوع الملف
    let fileType = 'other';
    if (req.file.mimetype.startsWith('image')) {
      fileType = req.body.fileType || 'image';
    } else if (req.file.mimetype === 'application/pdf') {
      fileType = 'pdf';
    } else if (req.file.mimetype.includes('document')) {
      fileType = 'doc';
    }

    // إنشاء السجل الطبي
    const record = await MedicalRecord.create({
      patientUsername: req.body.patientUsername,
      fileUrl: `/uploads/medical/${req.file.filename}`,
      fileType: fileType,
      addedBy: req.user.name
    });

    res.status(201).json({
      success: true,
      data: record
    });
  });
});

// @desc    الحصول على سجل طبي واحد
// @route   GET /api/medicalRecords/:id
// @access  Private (للمستخدمين الطبيين فقط)
exports.getMedicalRecord = asyncHandler(async (req, res, next) => {
  // التحقق من نوع المستخدم
  if (req.user.userType !== 'medical' && req.user.role !== 'admin') {
    return next(new ErrorResponse('غير مصرح لك بالوصول إلى السجلات الطبية', 403));
  }

  const record = await MedicalRecord.findById(req.params.id);

  if (!record) {
    return next(new ErrorResponse(`لم يتم العثور على سجل طبي بالمعرف ${req.params.id}`, 404));
  }

  res.status(200).json({
    success: true,
    data: record
  });
});

// @desc    حذف سجل طبي
// @route   DELETE /api/medicalRecords/:id
// @access  Private (للمستخدمين الطبيين فقط)
exports.deleteMedicalRecord = asyncHandler(async (req, res, next) => {
  // التحقق من نوع المستخدم
  if (req.user.userType !== 'medical' && req.user.role !== 'admin') {
    return next(new ErrorResponse('غير مصرح لك بحذف السجلات الطبية', 403));
  }

  const record = await MedicalRecord.findById(req.params.id);

  if (!record) {
    return next(new ErrorResponse(`لم يتم العثور على سجل طبي بالمعرف ${req.params.id}`, 404));
  }

  // حذف ملف السجل الطبي
  const filePath = path.join(__dirname, '../../public', record.fileUrl);
  if (fs.existsSync(filePath)) {
    fs.unlinkSync(filePath);
  }

  await record.remove();

  res.status(200).json({
    success: true,
    data: {}
  });
});

// @desc    الحصول على السجلات الطبية لمريض معين
// @route   GET /api/medicalRecords/patient/:username
// @access  Private (للمستخدمين الطبيين فقط)
exports.getPatientRecords = asyncHandler(async (req, res, next) => {
  // التحقق من نوع المستخدم
  if (req.user.userType !== 'medical' && req.user.role !== 'admin') {
    return next(new ErrorResponse('غير مصرح لك بالوصول إلى السجلات الطبية', 403));
  }

  const records = await MedicalRecord.find({ patientUsername: req.params.username });

  res.status(200).json({
    success: true,
    count: records.length,
    data: records
  });
});
