import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../appstate.dart';
import '../widgets/rewards_badge.dart';

class RewardsScreen extends StatelessWidget {
  const RewardsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final appState = Provider.of<AppState>(context);
    return Scaffold(
      appBar: AppBar(
        title: const Text('نظام المكافآت'),
        backgroundColor: Colors.teal,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            RewardsBadge(
              badgeAsset: appState.rewardBadgeAsset,
              levelName: appState.rewardLevelName,
              points: appState.points,
            ),
            const SizedBox(height: 24),
            const Text('سجل الإنجازات:', style: TextStyle(fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),
            Expanded(
              child: appState.rewardsHistory.isEmpty
                  ? const Center(child: Text('لا يوجد إنجازات بعد'))
                  : ListView.builder(
                      itemCount: appState.rewardsHistory.length,
                      itemBuilder: (context, i) {
                        final item = appState.rewardsHistory[appState.rewardsHistory.length - 1 - i];
                        return ListTile(
                          leading: const Icon(Icons.emoji_events, color: Colors.amber),
                          title: Text('حصلت على ${item['points']} نقطة (${item['action']})'),
                          subtitle: Text(item['date'].toString()),
                          trailing: item['note'] != '' ? Text(item['note']) : null,
                        );
                      },
                    ),
            ),
          ],
        ),
      ),
    );
  }
}
