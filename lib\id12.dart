import 'dart:async';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';
import 'package:provider/provider.dart';
import 'package:timeago/timeago.dart' as timeago;
import 'health_service.dart';
import 'appstate.dart';
import 'models/disease_model.dart';
import 'models/notification_model.dart';
import 'course_details_page.dart';
import 'repositories/disease_repository.dart';

void setupTimeago() => timeago.setLocaleMessages('ar', timeago.ArMessages());

class Rayan12 extends StatefulWidget {
  const Rayan12({super.key});

  @override
  _Rayan12State createState() => _Rayan12State();
}

class _Rayan12State extends State<Rayan12> with SingleTickerProviderStateMixin {
  final HealthService _healthService = HealthService();
  final TextEditingController _searchController = TextEditingController();
  List<Disease> _chronicDiseases = [];
  List<Disease> _immuneDiseases = [];
  List<Disease> _commonDiseases = [];
  bool _isLoading = true;
  Timer? _debounceTimer;
  late AnimationController _fabAnimationController;
  late Animation<double> _fabAnimation;

  final List<String> _searchSuggestions = [
    'السكري',
    'الضغط',
    'الربو',
    'التهاب المفاصل',
    'الحساسية',
  ];

  @override
  void initState() {
    super.initState();
    setupTimeago();
    _fetchData();
    _searchController.addListener(_onSearchChanged);

    _fabAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 2),
    )..repeat(reverse: true);
    _fabAnimation = Tween<double>(begin: 1.0, end: 1.2).animate(
      CurvedAnimation(
        parent: _fabAnimationController,
        curve: Curves.easeInOut,
      ),
    );

    final appState = Provider.of<AppState>(context, listen: false);
    if (appState.socket != null) {
      appState.socket!.on('new_notification', (data) {
        if (mounted) {
          print('Received new_notification: $data');
          final notification = NotificationModel(
            id: data['notificationId'] ?? DateTime.now().toString(),
            message: data['message'] ?? 'إشعار جديد',
            date: DateTime.now().toIso8601String(),
            isRead: false,
          );
          appState.addNotification(notification);
        }
      });
    }
  }

  Future<void> _fetchData() async {
    if (!mounted) return;

    setState(() => _isLoading = true);

    final diseaseRepo = DiseaseRepository();
    bool hasErrors = false;

    try {
      final futures = [
        diseaseRepo.getDiseasesByType('chronic').then((response) {
          if (response.isSuccess && response.data != null && mounted) {
            setState(() {
              _chronicDiseases = response.data!;
              print('Fetched ${_chronicDiseases.length} chronic diseases');
            });
          } else {
            hasErrors = true;
            print('Error fetching chronic diseases: ${response.message}');
          }
        }).catchError((e) {
          hasErrors = true;
          print('Exception fetching chronic diseases: $e');
        }),
        diseaseRepo.getDiseasesByType('immune').then((response) {
          if (response.isSuccess && response.data != null && mounted) {
            setState(() {
              _immuneDiseases = response.data!;
              print('Fetched ${_immuneDiseases.length} immune diseases');
            });
          } else {
            hasErrors = true;
            print('Error fetching immune diseases: ${response.message}');
          }
        }).catchError((e) {
          hasErrors = true;
          print('Exception fetching immune diseases: $e');
        }),
        diseaseRepo.getDiseasesByType('common').then((response) {
          if (response.isSuccess && response.data != null && mounted) {
            setState(() {
              _commonDiseases = response.data!;
              print('Fetched ${_commonDiseases.length} common diseases');
            });
          } else {
            hasErrors = true;
            print('Error fetching common diseases: ${response.message}');
          }
        }).catchError((e) {
          hasErrors = true;
          print('Exception fetching common diseases: $e');
        }),
      ];

      await Future.wait(futures, eagerError: false);

      if (hasErrors && mounted) {
        _showSnackBar('تم استخدام البيانات المحلية بسبب مشاكل في الاتصال', Colors.orange);
      }
    } catch (e) {
      print('Unexpected error in _fetchData: $e');
      if (mounted) {
        _showSnackBar('استخدام البيانات المحلية بسبب خطأ: $e', Colors.orange);
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
          print('Fetch completed, isLoading: $_isLoading');
        });
      }
    }
  }

  void _onSearchChanged() {
    _debounce(() {
      if (mounted) {
        setState(() {
          print('Search query changed: ${_searchController.text}');
        });
      }
    });
  }

  void _debounce(VoidCallback callback) {
    const duration = Duration(milliseconds: 300);
    _debounceTimer?.cancel();
    _debounceTimer = Timer(duration, callback);
  }

  @override
  void dispose() {
    _searchController.dispose();
    _debounceTimer?.cancel();
    _fabAnimationController.dispose();
    super.dispose();
  }

  void _showSnackBar(String message, [Color? backgroundColor, SnackBarAction? action]) {
    if (!mounted) return;
    try {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: backgroundColor ?? Colors.blue,
          behavior: SnackBarBehavior.floating,
          action: action,
          elevation: 6,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        ),
      );
    } catch (e) {
      print('Error showing SnackBar: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    print('Building Rayan12 widget');
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final appState = Provider.of<AppState>(context);
    final notificationCount = appState.notifications.length;
    final roleName = appState.currentUser?.role.name.toLowerCase() ?? '';
    final userType = appState.userType?.toLowerCase() ?? '';
    final isHokama = roleName == 'hokama' || roleName == 'حكيم' || userType == 'hokama' || userType == 'حكيم';

    final filteredChronic = _chronicDiseases
        .where((d) => d.title.toLowerCase().contains(_searchController.text.toLowerCase()))
        .toList();
    final filteredImmune = _immuneDiseases
        .where((d) => d.title.toLowerCase().contains(_searchController.text.toLowerCase()))
        .toList();
    final filteredCommon = _commonDiseases
        .where((d) => d.title.toLowerCase().contains(_searchController.text.toLowerCase()))
        .toList();

    print(
        'chronic: ${filteredChronic.length}, immune: ${filteredImmune.length}, common: ${filteredCommon.length}, isLoading: $_isLoading');

    return Scaffold(
      floatingActionButton: isHokama
          ? FloatingActionButton(
        heroTag: 'add_disease_fab_${DateTime.now().millisecondsSinceEpoch}',
        onPressed: () => _showCreateDiseaseDialog(context),
        backgroundColor: Colors.amber,
        tooltip: 'إضافة مرض جديد',
        child: const Icon(Icons.add),
      )
          : null,
      body: Stack(
        children: [
          _isLoading
              ? _buildSkeletonLoader()
              : RefreshIndicator(
            onRefresh: _fetchData,
            color: Colors.blue,
            child: CustomScrollView(
              slivers: [
                _buildSliverAppBar(isDarkMode, notificationCount),
                SliverToBoxAdapter(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: TextField(
                      controller: _searchController,
                      decoration: InputDecoration(
                        hintText: 'البحث عن الأمراض...',
                        prefixIcon: const Icon(Icons.search),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        contentPadding: const EdgeInsets.symmetric(
                          vertical: 12,
                          horizontal: 16,
                        ),
                      ),
                      onTap: () {
                        if (_searchController.text.isNotEmpty) {
                          _showSearchSuggestions(context, _searchController.text);
                        }
                      },
                    ),
                  ),
                ),
                SliverList(
                  delegate: SliverChildListDelegate([
                    if (filteredChronic.isNotEmpty)
                      _buildCategorySection(context, "الأمراض المزمنة", filteredChronic),
                    if (filteredImmune.isNotEmpty)
                      _buildCategorySection(context, "الأمراض المناعية", filteredImmune),
                    if (filteredCommon.isNotEmpty)
                      _buildCategorySection(context, "الأمراض الشائعة", filteredCommon),
                  ]),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategorySection(BuildContext context, String title, List<Disease> diseases) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
          child: Text(
            title,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.blue,
            ),
          ),
        ),
        const SizedBox(height: 8),
        SizedBox(
          height: 220,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: diseases.length,
            itemBuilder: (context, index) {
              return DiseaseCard(
                disease: diseases[index],
                isMobile: true,
                healthService: _healthService,
              );
            },
          ),
        ),
        const SizedBox(height: 16),
      ],
    );
  }

  Widget _buildSkeletonLoader() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return CustomScrollView(
      slivers: [
        SliverAppBar(
          expandedHeight: 120,
          flexibleSpace: FlexibleSpaceBar(
            background: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: isDarkMode
                      ? [Colors.blue[900]!, Colors.blue[700]!]
                      : [Colors.blue, Colors.blueAccent],
                ),
              ),
              child: SafeArea(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Container(
                    height: 40,
                    decoration: BoxDecoration(
                      color: isDarkMode ? Colors.grey[800] : Colors.grey[300],
                      borderRadius: BorderRadius.circular(30),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
        SliverList(
          delegate: SliverChildBuilderDelegate(
                (context, index) => Card(
              margin: const EdgeInsets.all(16),
              child: Padding(
                padding: const EdgeInsets.all(12.0),
                child: Column(
                  children: [
                    Container(height: 20, width: 100, color: Colors.grey[300]),
                    const SizedBox(height: 10),
                    Container(height: 100, color: Colors.grey[300]),
                  ],
                ),
              ),
            ),
            childCount: 3,
          ),
        ),
      ],
    );
  }

  void _showNotifications(BuildContext context) {
    final appState = Provider.of<AppState>(context, listen: false);
    final notifications = appState.notifications;

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.7,
        decoration: BoxDecoration(
          color: Theme.of(context).scaffoldBackgroundColor,
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: const BoxDecoration(
                color: Colors.blue,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(20),
                  topRight: Radius.circular(20),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'الإشعارات',
                    style: TextStyle(color: Colors.white, fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  IconButton(
                    icon: const Icon(Icons.close, color: Colors.white),
                    onPressed: () => Navigator.pop(context),
                  ),
                ],
              ),
            ),
            Expanded(
              child: notifications.isEmpty
                  ? const Center(child: Text('لا توجد إشعارات'))
                  : ListView.builder(
                itemCount: notifications.length,
                itemBuilder: (context, index) {
                  final notification = notifications[index];
                  return ListTile(
                    leading: const CircleAvatar(
                      backgroundColor: Colors.blue,
                      child: Icon(Icons.notifications, color: Colors.white),
                    ),
                    title: Text(notification.message),
                    subtitle: Text(
                      timeago.format(DateTime.parse(notification.date), locale: 'ar'),
                    ),
                    trailing: notification.isRead
                        ? null
                        : Container(
                      width: 10,
                      height: 10,
                      decoration: const BoxDecoration(
                        color: Colors.red,
                        shape: BoxShape.circle,
                      ),
                    ),
                    onTap: () {
                      if (!notification.isRead) {
                        appState.markNotificationAsRead(notification.id);
                      }
                      Navigator.pop(context);
                    },
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  SliverAppBar _buildSliverAppBar(bool isDarkMode, int notificationCount) {
    return SliverAppBar(
      expandedHeight: 120,
      flexibleSpace: FlexibleSpaceBar(
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: isDarkMode
                  ? [Colors.blue[900]!, Colors.blue[700]!]
                  : [Colors.blue, Colors.blueAccent],
            ),
          ),
          child: SafeArea(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Column(
                children: [
                  TextField(
                    controller: _searchController,
                    decoration: InputDecoration(
                      hintText: 'بحث عن مرض...',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(30),
                        borderSide: BorderSide.none,
                      ),
                      hintStyle: TextStyle(color: isDarkMode ? Colors.white70 : Colors.white),
                      prefixIcon: Icon(Icons.search, color: isDarkMode ? Colors.white : Colors.white),
                      suffixIcon: _searchController.text.isNotEmpty
                          ? IconButton(
                        icon: Icon(Icons.clear, color: isDarkMode ? Colors.white : Colors.white),
                        onPressed: () {
                          _searchController.clear();
                          setState(() {});
                        },
                      )
                          : null,
                      filled: true,
                      fillColor: isDarkMode ? Colors.white12 : Colors.white24,
                    ),
                    style: TextStyle(color: isDarkMode ? Colors.white : Colors.white),
                    onChanged: (value) {
                      if (value.isNotEmpty) {
                        _showSearchSuggestions(context, value);
                      }
                    },
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
      actions: [
        Stack(
          children: [
            IconButton(
              icon: const Icon(Icons.notifications, color: Colors.white),
              onPressed: () => _showNotifications(context),
            ),
            if (notificationCount > 0)
              Positioned(
                right: 8,
                top: 8,
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 300),
                  padding: const EdgeInsets.all(4),
                  decoration: const BoxDecoration(color: Colors.red, shape: BoxShape.circle),
                  constraints: const BoxConstraints(minWidth: 16, minHeight: 16),
                  child: Text(
                    '$notificationCount',
                    style: const TextStyle(color: Colors.white, fontSize: 10),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
          ],
        ),
      ],
      pinned: true,
    );
  }

  void _showSearchSuggestions(BuildContext context, String query) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        height: 200,
        padding: const EdgeInsets.all(16),
        child: ListView(
          children: _searchSuggestions
              .where((suggestion) => suggestion.toLowerCase().contains(query.toLowerCase()))
              .map((suggestion) => ListTile(
            title: Text(suggestion),
            onTap: () {
              _searchController.text = suggestion;
              setState(() {});
              Navigator.pop(context);
            },
          ))
              .toList(),
        ),
      ),
    );
  }

  @override


  void _showCreateDiseaseDialog(BuildContext context) {
    final appState = Provider.of<AppState>(context, listen: false);
    // Debug: Print current user role and type
    print('role.name: [32m[1m[4m[7m${appState.currentUser?.role.name ?? 'null'}\u001b[0m');
    print('userType: [32m[1m[4m[7m${appState.userType ?? 'null'}\u001b[0m');
    final isHokama = (appState.currentUser?.role.name.toLowerCase() == 'hokama' ||
        appState.currentUser?.role.name == 'حكيم' ||
        appState.userType?.toLowerCase() == 'hokama' ||
        appState.userType == 'حكيم');

    // التحقق من أن المستخدم لديه دور "hokama"
    if (!isHokama) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('غير مصرح'),
          content: const Text('هذه الميزة متاحة فقط للحكماء'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('إغلاق'),
            ),
          ],
        ),
      );
      return;
    }

    // متغيرات للنموذج
    final titleController = TextEditingController();
    final descriptionController = TextEditingController();
    final symptomsController = TextEditingController();
    final treatmentController = TextEditingController();
    String selectedType = 'common'; // القيمة الافتراضية

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إضافة مرض جديد'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: titleController,
                decoration: const InputDecoration(
                  labelText: 'اسم المرض',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 10),
              DropdownButtonFormField<String>(
                value: selectedType,
                decoration: const InputDecoration(
                  labelText: 'نوع المرض',
                  border: OutlineInputBorder(),
                ),
                items: const [
                  DropdownMenuItem(value: 'common', child: Text('شائع')),
                  DropdownMenuItem(value: 'chronic', child: Text('مزمن')),
                  DropdownMenuItem(value: 'immune', child: Text('مناعي')),
                ],
                onChanged: (value) {
                  selectedType = value!;
                },
              ),
              const SizedBox(height: 10),
              TextField(
                controller: descriptionController,
                decoration: const InputDecoration(
                  labelText: 'وصف المرض',
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
              ),
              const SizedBox(height: 10),
              TextField(
                controller: symptomsController,
                decoration: const InputDecoration(
                  labelText: 'الأعراض',
                  border: OutlineInputBorder(),
                ),
                maxLines: 2,
              ),
              const SizedBox(height: 10),
              TextField(
                controller: treatmentController,
                decoration: const InputDecoration(
                  labelText: 'العلاج',
                  border: OutlineInputBorder(),
                ),
                maxLines: 2,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              // التحقق من البيانات
              if (titleController.text.isEmpty) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('يرجى إدخال اسم المرض')),
                );
                return;
              }

              final newDisease = Disease(
                id: DateTime.now().millisecondsSinceEpoch.toString(), // ID único basado en timestamp
                title: titleController.text,
                description: descriptionController.text,
                image: '', // Imagen por defecto
                symptoms: symptomsController.text.split('\n').where((s) => s.trim().isNotEmpty).toList(),
                causes: const [], // Lista vacía para causas
                tests: const [], // Lista vacía para pruebas
                treatments: treatmentController.text.split('\n').where((t) => t.trim().isNotEmpty).toList(),
                suitableFoods: const [], // Lista vacía para alimentos adecuados
                unsuitableFoods: const [], // Lista vacía para alimentos inadecuados
                isLocal: true, // Marcar como local
              );

              setState(() {
                if (selectedType == 'مزمن' || selectedType.toLowerCase() == 'chronic') {
                  _chronicDiseases.add(newDisease);
                } else if (selectedType == 'مناعي' || selectedType.toLowerCase() == 'immune') {
                  _immuneDiseases.add(newDisease);
                } else {
                  _commonDiseases.add(newDisease);
                }
              });

              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('تمت إضافة المرض بنجاح')),
              );
            },
            child: const Text('إضافة'),
          ),
        ],
      ),
    );
  }
}

class DiseaseCard extends StatelessWidget {
  final Disease disease;
  final bool isMobile;
  final HealthService healthService;

  const DiseaseCard({
    super.key,
    required this.disease,
    required this.isMobile,
    required this.healthService,
  });

  String _buildImageUrl(String baseUrl, String imagePath) {
    if (disease.isLocal) {
      return imagePath; // Use local asset path directly
    }
    // Ensure baseUrl ends with '/' and imagePath doesn't start with '/'
    final sanitizedBase = baseUrl.endsWith('/') ? baseUrl : '$baseUrl/';
    final sanitizedPath = imagePath.startsWith('/') ? imagePath.substring(1) : imagePath;
    try {
      final uri = Uri.parse('$sanitizedBase$sanitizedPath');
      return uri.toString();
    } catch (e) {
      print('Error building image URL for ${disease.title}: $e');
      return 'https://via.placeholder.com/150x200';
    }
  }

  @override
  Widget build(BuildContext context) {
    final imageUrl = disease.image.isNotEmpty
        ? _buildImageUrl(healthService.baseUrl, disease.image)
        : 'https://via.placeholder.com/150x200';

    return GestureDetector(
      onTap: () {
        print('Navigating to details for ${disease.title}');
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => CourseDetailsPage(
              courseTitle: disease.title,
              disease: disease,
            ),
          ),
        );
      },
      child: Container(
        width: 150,
        margin: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.blue.withOpacity(0.2),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Stack(
          children: [
            disease.isLocal
                ? Image.asset(
              imageUrl,
              fit: BoxFit.cover,
              width: 150,
              height: 200,
              errorBuilder: (context, error, stackTrace) {
                print('Image load error for ${disease.title}: $error');
                return const Icon(
                  Icons.broken_image,
                  size: 50,
                  color: Colors.blue,
                );
              },
            )
                : CachedNetworkImage(
              imageUrl: imageUrl,
              fit: BoxFit.cover,
              width: 150,
              height: 200,
              placeholder: (context, url) => Lottie.asset(
                'assets/animations/loading.json',
                width: 150,
                height: 200,
              ),
              errorWidget: (context, url, error) {
                print('Image load error for ${disease.title}: $error');
                return const Icon(
                  Icons.broken_image,
                  size: 50,
                  color: Colors.blue,
                );
              },
            ),
            Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                gradient: LinearGradient(
                  colors: [
                    Colors.black.withOpacity(0.6),
                    Colors.transparent,
                  ],
                  begin: Alignment.bottomCenter,
                  end: Alignment.topCenter,
                ),
              ),
            ),
            Align(
              alignment: Alignment.bottomCenter,
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Text(
                  disease.title,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
