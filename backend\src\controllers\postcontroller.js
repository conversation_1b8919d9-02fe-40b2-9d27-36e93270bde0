const Post = require('../models/Postmodel');
const User = require('../models/user.model.new');
const ErrorResponse = require('../utils/errorResponse');
const asyncHandler = require('../middleware/asyncmiddleware');
const path = require('path');
const fs = require('fs');
const multer = require('multer');

// إعداد تخزين الملفات
const storage = multer.diskStorage({
  destination: function(req, file, cb) {
    const uploadPath = './public/uploads/posts';
    if (!fs.existsSync(uploadPath)) {
      fs.mkdirSync(uploadPath, { recursive: true });
    }
    cb(null, uploadPath);
  },
  filename: function(req, file, cb) {
    cb(null, `post_${Date.now()}${path.extname(file.originalname)}`);
  }
});

// فلترة الملفات
const fileFilter = (req, file, cb) => {
  // قبول الصور والفيديوهات فقط
  if (file.mimetype.startsWith('image') || file.mimetype.startsWith('video')) {
    cb(null, true);
  } else {
    cb(new ErrorResponse('يرجى تحميل صورة أو فيديو فقط', 400), false);
  }
};

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 10000000 // 10 ميجابايت
  },
  fileFilter: fileFilter
});

// @desc    البحث عن منشورات
// @route   GET /api/posts/search
// @access  Public
exports.searchPosts = asyncHandler(async (req, res, next) => {
  const query = req.query.query || '';
  if (!query) {
    return res.status(200).json({ success: true, count: 0, data: [] });
  }
  const posts = await Post.find({
    $or: [
      { content: { $regex: query, $options: 'i' } },
      { location: { $regex: query, $options: 'i' } },
      { tags: { $regex: query, $options: 'i' } }
    ]
  }).populate({ path: 'userId', select: 'name avatarUrl role' });
  res.status(200).json({
    success: true,
    count: posts.length,
    data: posts
  });
});

// @desc    الحصول على جميع المنشورات
// @route   GET /api/posts
// @access  Private
exports.getPosts = asyncHandler(async (req, res, next) => {
  // ترتيب مخصص
  const sortBy = req.query.sortBy || 'latest'; // latest | popular
  let sortOption = { createdAt: -1 };
  if (sortBy === 'popular') {
    // الترتيب حسب مجموع التفاعلات (الإعجابات + التعليقات)
    sortOption = { $expr: { $add: ["$likes", "$loves", "$haha", { $size: "$comments" }] } };
  }

  // تصفية
  const filter = {};
  if (req.query.userId) {
    filter.userId = req.query.userId;
  }
  if (req.query.privacy) {
    filter.privacy = req.query.privacy;
  }
  if (req.query.promoted !== undefined) {
    filter.promoted = req.query.promoted === 'true';
  }

  const page = parseInt(req.query.page, 10) || 1;
  const limit = parseInt(req.query.limit, 10) || 10;
  const startIndex = (page - 1) * limit;
  const endIndex = page * limit;
  const total = await Post.countDocuments(filter);

  let posts;
  if (sortBy === 'popular') {
    posts = await Post.aggregate([
      { $addFields: { interactions: { $add: ["$likes", "$loves", "$haha", { $size: "$comments" }] } } },
      { $sort: { promoted: -1, interactions: -1, createdAt: -1 } },
      { $skip: startIndex },
      { $limit: limit }
    ]);
  } else {
    posts = await Post.find(filter)
      .sort(sortOption)
      .sort({ promoted: -1, createdAt: -1 })
      .skip(startIndex)
      .limit(limit)
      .populate({
        path: 'userId',
        select: 'name avatarUrl'
      });
  }

  // معلومات الصفحات
  const pagination = {};

  if (endIndex < total) {
    pagination.next = {
      page: page + 1,
      limit
    };
  }

  if (startIndex > 0) {
    pagination.prev = {
      page: page - 1,
      limit
    };
  }

  // تحويل المنشورات إلى التنسيق المطلوب للواجهة الأمامية
  const formattedPosts = posts.map(post => ({
    id: post._id,
    content: post.content,
    mediaUrl: post.mediaUrl,
    mediaType: post.mediaType,
    createdAt: post.createdAt,
    likes: post.likes,
    loves: post.loves,
    haha: post.haha,
    comments: post.comments,
    author: {
      username: post.userId ? post.userId.name : 'غير معروف',
      avatarUrl: post.userId ? post.userId.avatarUrl : null
    },
    isLiked: false // سيتم تحديثه في الواجهة الأمامية
  }));

  res.status(200).json({
    success: true,
    count: posts.length,
    pagination,
    posts: formattedPosts
  });
});

// @desc    إنشاء منشور جديد
// @route   POST /api/posts
// @access  Private
exports.createPost = asyncHandler(async (req, res, next) => {
  // استخراج الوسوم تلقائياً من النص
  if (req.body.content) {
    req.body.hashtags = Array.from(new Set((req.body.content.match(/#\w+/g) || []).map(tag => tag.replace('#', ''))));
  }

  // إضافة معرف المستخدم إلى الطلب
  req.body.userId = req.user.id;

  // التعامل مع تحميل الوسائط
  const uploadMiddleware = upload.single('media');

  uploadMiddleware(req, res, async (err) => {
    if (err) {
      return next(new ErrorResponse(`خطأ في تحميل الملف: ${err.message}`, 400));
    }

    // إنشاء المنشور
    const postData = {
      userId: req.user.id,
      content: req.body.content
    };

    // إضافة معلومات الوسائط إذا تم تحميل ملف
    if (req.file) {
      postData.mediaUrl = `/uploads/posts/${req.file.filename}`;
      postData.mediaType = req.file.mimetype.startsWith('image') ? 'image' : 'video';
    }

    const post = await Post.create(postData);

    // إشعارات mention عند ذكر مستخدمين بصيغة @username
    if (req.body.content) {
      const mentionedUsernames = Array.from(new Set((req.body.content.match(/@(\w+)/g) || []).map(tag => tag.replace('@', ''))));
      if (mentionedUsernames.length > 0) {
        const mentionedUsers = await User.find({ name: { $in: mentionedUsernames } });
        for (const user of mentionedUsers) {
          if (user._id.toString() !== req.user.id) {
            if (user.notificationsSettings?.mention !== false) {
              user.notifications.push({
                type: 'mention',
              text: `${req.user.name} أشار إليك في منشور`,
              link: `/posts/${post._id}`
            });
            await user.save();
            if (user.notificationsSettings?.mention !== false && user.fcmToken) {
              const axios = require('axios');
              await axios.post('https://fcm.googleapis.com/fcm/send', {
                to: user.fcmToken,
                notification: {
                  title: 'إشارة جديدة في منشور',
                  body: `${req.user.name} أشار إليك في منشور`,
                  click_action: `/posts/${post._id}`
                }
              }, {
                headers: {
                  'Authorization': `key=${process.env.FCM_SERVER_KEY}`,
                  'Content-Type': 'application/json'
                }
              }).catch(() => {});
            }
          }
        }
      }
    }

    // الحصول على المنشور مع معلومات المؤلف
    const populatedPost = await Post.findById(post._id).populate({
      path: 'userId',
      select: 'name avatarUrl'
    });

    // تنسيق الاستجابة
    const formattedPost = {
      id: populatedPost._id,
      content: populatedPost.content,
      mediaUrl: populatedPost.mediaUrl,
      mediaType: populatedPost.mediaType,
      createdAt: populatedPost.createdAt,
      likes: populatedPost.likes,
      loves: populatedPost.loves,
      haha: populatedPost.haha,
      comments: populatedPost.comments,
      author: {
        username: populatedPost.userId ? populatedPost.userId.name : 'غير معروف',
        avatarUrl: populatedPost.userId ? populatedPost.userId.avatarUrl : null
      }
    };

    res.status(201).json({
      success: true,
      data: formattedPost
    });
  });
}


// @desc    الحصول على منشور واحد
// @route   GET /api/posts/:id
// @access  Private
exports.getPost = asyncHandler(async (req, res, next) => {
  const post = await Post.findById(req.params.id).populate({
    path: 'userId',
    select: 'name avatarUrl'
  });

  if (!post) {
    return next(new ErrorResponse(`لم يتم العثور على منشور بالمعرف ${req.params.id}`, 404));
  }

  // تنسيق الاستجابة
  const formattedPost = {
    id: post._id,
    content: post.content,
    mediaUrl: post.mediaUrl,
    mediaType: post.mediaType,
    createdAt: post.createdAt,
    likes: post.likes,
    loves: post.loves,
    haha: post.haha,
    comments: post.comments,
    author: {
      username: post.userId ? post.userId.name : 'غير معروف',
      avatarUrl: post.userId ? post.userId.avatarUrl : null
    },
    isLiked: false // سيتم تحديثه في الواجهة الأمامية
  };

  res.status(200).json({
    success: true,
    data: formattedPost
  });
});

// @desc    مشاركة منشور على شبكات خارجية
// @route   POST /api/posts/:id/share
// @access  Private
exports.sharePostExternally = asyncHandler(async (req, res, next) => {
  // هنا يمكنك التكامل مع API خارجي مثل Facebook/Twitter/WhatsApp
  // حالياً فقط استجابة وهمية
  res.status(200).json({ success: true, message: 'تمت مشاركة المنشور خارجياً (وهمي)' });
});

// @desc    ربط منشور بموقع على خرائط Google
// @route   POST /api/posts/:id/map-location
// @access  Private
exports.addMapLocation = asyncHandler(async (req, res, next) => {
  // هنا يمكنك التكامل مع Google Maps API
  // حالياً فقط استجابة وهمية
  res.status(200).json({ success: true, message: 'تم ربط الموقع (وهمي)' });
});

// @desc    تحديث منشور
// @route   PUT /api/posts/:id
// @access  Private
exports.updatePost = asyncHandler(async (req, res, next) => {
  let post = await Post.findById(req.params.id);

  if (!post) {
    return next(new ErrorResponse(`لم يتم العثور على منشور بالمعرف ${req.params.id}`, 404));
  }

  // التحقق من ملكية المنشور
  if (post.userId.toString() !== req.user.id && req.user.role !== 'admin') {
    return next(new ErrorResponse('غير مصرح لك بتحديث هذا المنشور', 401));
  }

  post = await Post.findByIdAndUpdate(
    req.params.id,
    { content: req.body.content },
    { new: true, runValidators: true }
  ).populate({
    path: 'userId',
    select: 'name avatarUrl'
  });

  // تنسيق الاستجابة
  const formattedPost = {
    id: post._id,
    content: post.content,
    mediaUrl: post.mediaUrl,
    mediaType: post.mediaType,
    createdAt: post.createdAt,
    likes: post.likes,
    loves: post.loves,
    haha: post.haha,
    comments: post.comments,
    author: {
      username: post.userId ? post.userId.name : 'غير معروف',
      avatarUrl: post.userId ? post.userId.avatarUrl : null
    }
  };

  res.status(200).json({
    success: true,
    data: formattedPost
  });
});

// @desc    تحديث موقع المنشور
// @route   PUT /api/posts/:id/location
// @access  Private
exports.updatePostLocation = asyncHandler(async (req, res, next) => {
  const post = await Post.findById(req.params.id);
  if (!post) {
    return next(new ErrorResponse(`لم يتم العثور على منشور بالمعرف ${req.params.id}`, 404));
  }
  // السماح فقط لصاحب المنشور أو الأدمن
  if (post.userId.toString() !== req.user.id && req.user.role !== 'admin') {
    return next(new ErrorResponse('غير مصرح لك بتعديل موقع هذا المنشور', 401));
  }
  const { latitude, longitude } = req.body;
  if (typeof latitude !== 'number' || typeof longitude !== 'number') {
    return next(new ErrorResponse('يرجى إرسال إحداثيات صحيحة', 400));
  }
  post.latitude = latitude;
  post.longitude = longitude;
  await post.save();
  res.status(200).json({ success: true, data: post });
});

// @desc    حذف منشور
// @route   DELETE /api/posts/:id
// @access  Private
exports.deletePost = asyncHandler(async (req, res, next) => {
  const post = await Post.findById(req.params.id);

  if (!post) {
    return next(new ErrorResponse(`لم يتم العثور على منشور بالمعرف ${req.params.id}`, 404));
  }

  // التحقق من ملكية المنشور
  if (post.userId.toString() !== req.user.id && req.user.role !== 'admin') {
    return next(new ErrorResponse('غير مصرح لك بحذف هذا المنشور', 401));
  }

  // حذف ملف الوسائط إذا كان موجودًا
  if (post.mediaUrl) {
    const filePath = path.join(__dirname, '../../public', post.mediaUrl);
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }
  }

  await post.remove();

  res.status(200).json({
    success: true,
    data: {}
  });
});

// @desc    إضافة تعليق على منشور
// @route   POST /api/posts/:id/comment
// @access  Private
exports.addComment = asyncHandler(async (req, res, next) => {
  const post = await Post.findById(req.params.id);

  if (!post) {
    return next(new ErrorResponse(`لم يتم العثور على منشور بالمعرف ${req.params.id}`, 404));
  }

  // إنشاء التعليق
  const comment = {
    username: req.user.name,
    content: req.body.text,
    date: Date.now()
  };

  // إضافة التعليق إلى المنشور
  post.comments.push(comment);
  await post.save();

  // إشعارات mention في الردود على التعليقات (reply)
  if (req.body.replyTo && req.body.text) {
    // replyTo: رقم أو معرف التعليق الأصلي
    const parentComment = post.comments.id(req.body.replyTo);
    if (parentComment && parentComment.username) {
      // استخراج أسماء المستخدمين المذكورين في الرد
      const mentionedUsernames = Array.from(new Set((req.body.text.match(/@(\w+)/g) || []).map(tag => tag.replace('@', ''))));
      if (mentionedUsernames.length > 0) {
        const mentionedUsers = await User.find({ name: { $in: mentionedUsernames } });
        for (const user of mentionedUsers) {
          if (user._id.toString() !== req.user.id && user._id.toString() !== post.userId.toString() && user.name !== parentComment.username) {
            if (user.notificationsSettings?.mention !== false) {
              user.notifications.push({
                type: 'mention',
              text: `${req.user.name} أشار إليك في رد على تعليق منشور`,
              link: `/posts/${post._id}`
            });
            await user.save();
            if (user.notificationsSettings?.mention !== false && user.fcmToken) {
              const axios = require('axios');
              await axios.post('https://fcm.googleapis.com/fcm/send', {
                to: user.fcmToken,
                notification: {
                  title: 'إشارة جديدة في رد على تعليق',
                  body: `${req.user.name} أشار إليك في رد على تعليق منشور`,
                  click_action: `/posts/${post._id}`
                }
              }, {
                headers: {
                  'Authorization': `key=${process.env.FCM_SERVER_KEY}`,
                  'Content-Type': 'application/json'
                }
              }).catch(() => {});
            }
          }
        }
      }
    }
  }

  // إشعار لصاحب المنشور (إذا لم يكن هو نفسه المُعلّق)
  if (post.userId.toString() !== req.user.id) {
    const postOwner = await User.findById(post.userId);
    if (postOwner) {
      // أضف إشعار في notifications
      postOwner.notifications.push({
        type: 'comment',
        text: `${req.user.name} علق على منشورك`,
        link: `/posts/${post._id}`
      });
      await postOwner.save();
      // إرسال إشعار Push إذا كان لدى المستخدم fcmToken
      if (postOwner.notificationsSettings?.comment !== false && postOwner.fcmToken) {
        const axios = require('axios');
        await axios.post('https://fcm.googleapis.com/fcm/send', {
          to: postOwner.fcmToken,
          notification: {
            title: 'تعليق جديد',
            body: `${req.user.name} علق على منشورك`,
            click_action: `/posts/${post._id}`
          }
        }, {
          headers: {
            'Authorization': `key=${process.env.FCM_SERVER_KEY}`,
            'Content-Type': 'application/json'
          }
        }).catch(() => {});
      }
    }
  }

  // إشعارات mention عند ذكر مستخدمين في التعليق
  if (req.body.text) {
    const mentionedUsernames = Array.from(new Set((req.body.text.match(/@(\w+)/g) || []).map(tag => tag.replace('@', ''))));
    if (mentionedUsernames.length > 0) {
      const mentionedUsers = await User.find({ name: { $in: mentionedUsernames } });
      for (const user of mentionedUsers) {
        if (user._id.toString() !== req.user.id && user._id.toString() !== post.userId.toString()) {
          user.notifications.push({
            type: 'mention',
            text: `${req.user.name} أشار إليك في تعليق على منشور`,
            link: `/posts/${post._id}`
          });
          await user.save();
          if (user.notificationsSettings?.mention !== false && user.fcmToken) {
            const axios = require('axios');
            await axios.post('https://fcm.googleapis.com/fcm/send', {
              to: user.fcmToken,
              notification: {
                title: 'إشارة جديدة في تعليق',
                body: `${req.user.name} أشار إليك في تعليق على منشور`,
                click_action: `/posts/${post._id}`
              }
            }, {
              headers: {
                'Authorization': `key=${process.env.FCM_SERVER_KEY}`,
                'Content-Type': 'application/json'
              }
            }).catch(() => {});
          }
        }
      }
    }
  }

  res.status(201).json({
    success: true,
    data: comment
  });
}

// @desc    إضافة رد فعل على منشور
// @route   POST /api/posts/:id/like
// @access  Private
exports.reactToPost = asyncHandler(async (req, res, next) => {
  const post = await Post.findById(req.params.id);

  if (!post) {
    return next(new ErrorResponse(`لم يتم العثور على منشور بالمعرف ${req.params.id}`, 404));
  }

  const reaction = req.body.reaction || 'like';

  // زيادة عدد ردود الفعل
  if (reaction === 'like') {
    post.likes += 1;
  } else if (reaction === 'love') {
    post.loves += 1;
  } else if (reaction === 'haha') {
    post.haha += 1;
  }

  await post.save();

  // إشعار لصاحب المنشور (إذا لم يكن هو نفسه المتفاعل)
  if (post.userId.toString() !== req.user.id) {
    const postOwner = await User.findById(post.userId);
    if (postOwner) {
      // أضف إشعار في notifications
      postOwner.notifications.push({
        type: 'reaction',
        text: `${req.user.name} تفاعل مع منشورك (${reaction})`,
        link: `/posts/${post._id}`
      });
      await postOwner.save();
      // إرسال إشعار Push إذا كان لدى المستخدم fcmToken
      if (postOwner.notificationsSettings?.comment !== false && postOwner.fcmToken) {
        const axios = require('axios');
        await axios.post('https://fcm.googleapis.com/fcm/send', {
          to: postOwner.fcmToken,
          notification: {
            title: 'تفاعل جديد',
            body: `${req.user.name} تفاعل مع منشورك (${reaction})`,
            click_action: `/posts/${post._id}`
          }
        }, {
          headers: {
            'Authorization': `key=${process.env.FCM_SERVER_KEY}`,
            'Content-Type': 'application/json'
          }
        }).catch(() => {});
      }
    }
  }

  res.status(200).json({
    success: true,
    data: {
      likes: post.likes,
      loves: post.loves,
      haha: post.haha
    }
  });
});
