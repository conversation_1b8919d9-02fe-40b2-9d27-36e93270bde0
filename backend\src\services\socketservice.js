const User = require('../models/user.model.new');
const Message = require('../models/message.model.new');

module.exports = (io) => {
  // تخزين المستخدمين المتصلين
  const connectedUsers = {};

  io.on('connection', async (socket) => {
    console.log('مستخدم جديد متصل:', socket.id);

    // استخراج معرف المستخدم من التوكن
    let userId;
    try {
      const authHeader = socket.handshake.headers.authorization;
      if (authHeader && authHeader.startsWith('Bearer ')) {
        const token = authHeader.split(' ')[1];
        const decoded = require('jsonwebtoken').verify(token, process.env.JWT_SECRET);
        userId = decoded.id;

        // تحديث حالة المستخدم إلى متصل
        await User.findByIdAndUpdate(userId, { isOnline: true, lastActive: Date.now() });

        // تخزين معرف المستخدم مع معرف الاتصال
        connectedUsers[userId] = socket.id;
        socket.userId = userId;

        // إرسال قائمة المستخدمين المتصلين
        io.emit('userStatusUpdate', { userId, isOnline: true });
      }
    } catch (error) {
      console.error('خطأ في التحقق من التوكن:', error);
    }

    // الانضمام إلى غرفة خاصة بالمستخدم
    if (userId) {
      socket.join(userId);
      console.log(`المستخدم ${userId} انضم إلى غرفته الخاصة`);
    }

    // استقبال رسالة جديدة
    socket.on('sendMessage', async (data) => {
      try {
        const { receiverId, content, type, attachmentUrl } = data;

        // التحقق من وجود المستخدم المرسل والمستقبل
        if (!userId || !receiverId) {
          return socket.emit('error', { message: 'بيانات غير كاملة' });
        }

        // إنشاء رسالة جديدة في قاعدة البيانات
        const message = await Message.create({
          senderId: userId,
          receiverId,
          content,
          type: type || 'text',
          attachmentUrl,
          isRead: false
        });

        // الحصول على معلومات المرسل
        const sender = await User.findById(userId).select('name avatarUrl');

        // إرسال الرسالة إلى المستقبل إذا كان متصلاً
        if (connectedUsers[receiverId]) {
          io.to(receiverId).emit('newMessage', {
            message,
            sender: {
              id: userId,
              name: sender.name,
              avatarUrl: sender.avatarUrl
            }
          });
        }

        // إرسال تأكيد إلى المرسل
        socket.emit('messageSent', { message });
      } catch (error) {
        console.error('خطأ في إرسال الرسالة:', error);
        socket.emit('error', { message: 'حدث خطأ أثناء إرسال الرسالة' });
      }
    });

    // تحديث حالة قراءة الرسائل
    socket.on('markMessagesAsRead', async (data) => {
      try {
        const { senderId } = data;

        if (!userId || !senderId) {
          return socket.emit('error', { message: 'بيانات غير كاملة' });
        }

        // تحديث حالة الرسائل في قاعدة البيانات
        await Message.updateMany(
          { senderId, receiverId: userId, isRead: false },
          { isRead: true }
        );

        // إرسال تأكيد إلى المرسل الأصلي
        if (connectedUsers[senderId]) {
          io.to(senderId).emit('messagesRead', { by: userId });
        }

        socket.emit('messagesMarkedAsRead', { senderId });
      } catch (error) {
        console.error('خطأ في تحديث حالة القراءة:', error);
        socket.emit('error', { message: 'حدث خطأ أثناء تحديث حالة القراءة' });
      }
    });

    // إرسال إشعار بكتابة رسالة
    socket.on('typing', (data) => {
      const { receiverId } = data;

      if (!userId || !receiverId) {
        return;
      }

      if (connectedUsers[receiverId]) {
        io.to(receiverId).emit('userTyping', { userId });
      }
    });

    // إرسال إشعار بالتوقف عن الكتابة
    socket.on('stopTyping', (data) => {
      const { receiverId } = data;

      if (!userId || !receiverId) {
        return;
      }

      if (connectedUsers[receiverId]) {
        io.to(receiverId).emit('userStoppedTyping', { userId });
      }
    });

    // إرسال إشعار بمنشور جديد
    socket.on('newPost', async (data) => {
      try {
        const { postId } = data;

        // إرسال الإشعار إلى جميع المستخدمين المتصلين
        socket.broadcast.emit('newPostNotification', {
          postId,
          userId,
          message: 'تم إضافة منشور جديد'
        });
      } catch (error) {
        console.error('خطأ في إرسال إشعار المنشور:', error);
      }
    });

    // الانضمام إلى غرفة مجموعة
    socket.on('joinGroup', (data) => {
      const { groupId } = data;

      if (!groupId) {
        return socket.emit('error', { message: 'معرف المجموعة مطلوب' });
      }

      socket.join(`group:${groupId}`);
      console.log(`المستخدم ${userId} انضم إلى المجموعة ${groupId}`);
    });

    // مغادرة غرفة مجموعة
    socket.on('leaveGroup', (data) => {
      const { groupId } = data;

      if (!groupId) {
        return socket.emit('error', { message: 'معرف المجموعة مطلوب' });
      }

      socket.leave(`group:${groupId}`);
      console.log(`المستخدم ${userId} غادر المجموعة ${groupId}`);
    });

    // إرسال رسالة إلى مجموعة
    socket.on('sendGroupMessage', (data) => {
      const { groupId, message } = data;

      if (!groupId || !message) {
        return socket.emit('error', { message: 'بيانات غير كاملة' });
      }

      // إرسال الرسالة إلى جميع أعضاء المجموعة
      io.to(`group:${groupId}`).emit('newGroupMessage', {
        groupId,
        message,
        sender: {
          id: userId
        }
      });
    });

    // قطع الاتصال
    socket.on('disconnect', async () => {
      console.log('مستخدم قطع الاتصال:', socket.id);

      if (userId) {
        // تحديث حالة المستخدم إلى غير متصل
        await User.findByIdAndUpdate(userId, { isOnline: false, lastActive: Date.now() });

        // إزالة المستخدم من قائمة المتصلين
        delete connectedUsers[userId];

        // إرسال إشعار بقطع الاتصال
        io.emit('userStatusUpdate', { userId, isOnline: false });
      }
    });
  });
};
