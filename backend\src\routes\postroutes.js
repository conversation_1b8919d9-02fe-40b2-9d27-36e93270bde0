const express = require('express');
const router = express.Router();
const {
  getPosts,
  createPost,
  getPost,
  updatePost,
  deletePost,
  addComment,
  reactToPost,
  getPostsByHashtag
} = require('../controllers/postcontroller');
const { protect } = require('../middleware/authmiddleware');

// مسارات المنشورات
router.route('/')
  .get(protect, getPosts)
  .post(protect, createPost);

// جلب منشورات حسب وسم معين
router.get('/hashtag/:tag', getPostsByHashtag);

router.route('/:id')
  .get(protect, getPost)
  .put(protect, updatePost)
  .delete(protect, deletePost);

// مشاركة منشور على شبكات خارجية
router.post('/:id/share', protect, sharePostExternally);
// ربط منشور بموقع على الخريطة
router.post('/:id/map-location', protect, addMapLocation);

// تحديث موقع المنشور
router.put('/:id/location', protect, require('../controllers/postcontroller').updatePostLocation);

// البحث عن المنشورات
router.get('/search', require('../controllers/postcontroller').searchPosts);

router.post('/:id/comment', protect, addComment);
router.post('/:id/like', protect, reactToPost);

module.exports = router;
