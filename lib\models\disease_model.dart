import 'package:equatable/equatable.dart';

class Disease extends Equatable {
  final String id;
  final String title;
  final String description;
  final String image;
  final List<String> symptoms;
  final List<String> causes;
  final List<String> tests;
  final List<String> treatments;
  final List<String> suitableFoods;
  final List<String> unsuitableFoods;
  final bool isLocal; // New flag to indicate local asset

  const Disease({
    required this.id,
    required this.title,
    required this.description,
    required this.image,
    required this.symptoms,
    required this.causes,
    required this.tests,
    required this.treatments,
    required this.suitableFoods,
    required this.unsuitableFoods,
    this.isLocal = false, // Default to false (backend)
  });

  factory Disease.fromJson(Map<String, dynamic> json) {
    return Disease(
      id: json['id']?.toString() ?? '',
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      image: (json['image'] ?? '').replaceFirst(RegExp(r'^/+'), ''), // Remove leading slashes
      symptoms: List<String>.from(json['symptoms'] ?? []),
      causes: List<String>.from(json['causes'] ?? []),
      tests: List<String>.from(json['tests'] ?? []),
      treatments: List<String>.from(json['treatments'] ?? []),
      suitableFoods: List<String>.from(json['suitableFoods'] ?? []),
      unsuitableFoods: List<String>.from(json['unsuitableFoods'] ?? []),
      isLocal: false, // Backend data
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'image': image,
      'symptoms': symptoms,
      'causes': causes,
      'tests': tests,
      'treatments': treatments,
      'suitableFoods': suitableFoods,
      'unsuitableFoods': unsuitableFoods,
    };
  }

  @override
  List<Object?> get props => [
    id,
    title,
    description,
    image,
    symptoms,
    causes,
    tests,
    treatments,
    suitableFoods,
    unsuitableFoods,
    isLocal,
  ];
}