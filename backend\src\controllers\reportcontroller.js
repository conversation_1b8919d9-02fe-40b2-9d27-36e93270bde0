// تحكم البلاغات
const Report = require('../models/Reportmodel');

// إنشاء بلاغ جديد
exports.createReport = async (req, res) => {
  try {
    const report = await Report.create(req.body);
    res.status(201).json(report);
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
};

// جلب جميع البلاغات (للمشرف)
exports.getAllReports = async (req, res) => {
  try {
    const reports = await Report.find().sort({ createdAt: -1 });
    res.json(reports);
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};

// حذف بلاغ
exports.deleteReport = async (req, res) => {
  try {
    await Report.findByIdAndDelete(req.params.id);
    res.json({ message: 'تم حذف البلاغ' });
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};
