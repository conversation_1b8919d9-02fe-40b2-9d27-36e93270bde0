// نموذج إعلان التبرع
class DonationAd {
  final String id;
  final String title;
  final String description;
  final String location;
  final String amount;
  final String category;
  final String fileUrl;
  final DateTime date;
  final bool isFeatured;
  final double raisedAmount;
  final String donationStatus;
  final String creatorId;

  DonationAd({
    required this.id,
    required this.title,
    required this.description,
    required this.location,
    required this.amount,
    required this.category,
    required this.fileUrl,
    required this.date,
    required this.isFeatured,
    required this.raisedAmount,
    required this.donationStatus,
    required this.creatorId,
  });

  factory DonationAd.fromJson(Map<String, dynamic> json) {
    return DonationAd(
      id: json['_id'] ?? '',
      title: json['title'] ?? 'بدون عنوان',
      description: json['description'] ?? 'بدون وصف',
      location: json['location'] ?? 'غير محدد',
      amount: json['amount'] ?? 'غير محدد',
      category: json['category'] ?? 'غير مصنف',
      fileUrl: json['fileUrl'] ?? '',
      date: DateTime.parse(json['date'] ?? DateTime.now().toString()),
      isFeatured: json['isFeatured'] ?? false,
      raisedAmount: (json['raisedAmount'] as num?)?.toDouble() ?? 0.0,
      donationStatus: json['donationStatus'] ?? 'not_completed',
      creatorId: json['creatorId'] ?? '',
    );
  }
}
