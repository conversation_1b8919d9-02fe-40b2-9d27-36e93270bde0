class Poll {
  final String id;
  final String question;
  final List<String> options;
  final List<int> votes;
  final DateTime endTime;

  Poll({
    required this.id,
    required this.question,
    required this.options,
    required this.votes,
    required this.endTime,
  });

  factory Poll.fromJson(Map<String, dynamic> json) {
    return Poll(
      id: json['id'] ?? json['_id'] ?? '',
      question: json['question'] ?? '',
      options: List<String>.from(json['options'] ?? []),
      votes: List<int>.from(json['votes'] ?? []),
      endTime: DateTime.parse(json['endTime'] ?? DateTime.now().toString()),
    );
  }
}
