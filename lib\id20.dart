import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:share_plus/share_plus.dart';
import 'package:animated_text_kit/animated_text_kit.dart';
import 'package:confetti/confetti.dart';
import 'package:geolocator/geolocator.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:shimmer/shimmer.dart';

class HomeFoodIndustryGroup {
  final String id;
  final String name;
  final String imageUrl;
  final List<Video> videos;
  final List<Article> articles;
  final List<FAQ> faqs;

  HomeFoodIndustryGroup({
    required this.id,
    required this.name,
    required this.imageUrl,
    required this.videos,
    required this.articles,
    required this.faqs,
  });
}

class Video {
  final String title;
  final String url;

  Video({required this.title, required this.url});
}

class Article {
  final String title;
  final String content;

  Article({required this.title, required this.content});
}

class FAQ {
  final String question;
  final String answer;

  FAQ({required this.question, required this.answer});
}

class Post {
  final String user;
  final String content;
  final String? imageUrl;

  Post({required this.user, required this.content, this.imageUrl});
}

class MarketItem {
  final String id;
  final String user;
  final String type;
  final List<String> imageUrls;
  final double quantity;
  final double price;
  final String address;
  final DateTime date;
  final String validity;
  final String contactNumber;
  final String details;
  final String? nutritionalContent;

  MarketItem({
    required this.id,
    required this.user,
    required this.type,
    required this.imageUrls,
    required this.quantity,
    required this.price,
    required this.address,
    required this.date,
    required this.validity,
    required this.contactNumber,
    required this.details,
    this.nutritionalContent,
  });
}

class HomeFoodIndustriesPage extends StatefulWidget {
  const HomeFoodIndustriesPage({super.key});

  @override
  State<HomeFoodIndustriesPage> createState() => _HomeFoodIndustriesPageState();
}

class _HomeFoodIndustriesPageState extends State<HomeFoodIndustriesPage> with SingleTickerProviderStateMixin {
  final List<HomeFoodIndustryGroup> industries = [
    HomeFoodIndustryGroup(
      id: '1',
      name: 'صناعة المربى',
      imageUrl: 'https://cdn-icons-png.flaticon.com/512/2674/2674081.png',
      videos: [
        Video(title: 'كيفية صنع مربى الفراولة', url: 'https://www.youtube.com/watch?v=jam1'),
        Video(title: 'أخطاء شائعة في صناعة المربى', url: 'https://www.youtube.com/watch?v=jam2'),
      ],
      articles: [
        Article(title: 'دليل صناعة المربى المنزلي', content: 'كل ما تحتاجه لصناعة المربى في المنزل...'),
      ],
      faqs: [
        FAQ(question: 'ما هي أفضل الفواكه لصناعة المربى؟', answer: 'الفراولة، الخوخ، والتين من الخيارات الممتازة...'),
      ],
    ),
    HomeFoodIndustryGroup(
      id: '2',
      name: 'صناعة المخللات',
      imageUrl: 'https://cdn-icons-png.flaticon.com/512/2674/2674082.png',
      videos: [
        Video(title: 'طريقة تحضير المخلل المنزلي', url: 'https://www.youtube.com/watch?v=pickle1'),
        Video(title: 'أسرار المخلل المقرمش', url: 'https://www.youtube.com/watch?v=pickle2'),
      ],
      articles: [
        Article(title: 'نصائح لصناعة المخللات', content: 'كيفية الحفاظ على نكهة المخللات...'),
        Article(title: 'فوائد المخلل للصحة', content: 'تعرف على الفوائد الصحية للمخللات...'),
      ],
      faqs: [
        FAQ(question: 'كم يستغرق تحضير المخلل؟', answer: 'عادة من أسبوع إلى شهر حسب النوع...'),
        FAQ(question: 'كيفة منع العفن في المخلل؟', answer: 'استخدام الملح المناسب والتأكد من تغطية الخضار بالمحلول الملحي...'),
      ],
    ),
    HomeFoodIndustryGroup(
      id: '3',
      name: 'صناعة الخبز المنزلي',
      imageUrl: 'https://cdn-icons-png.flaticon.com/512/2674/2674087.png',
      videos: [
        Video(title: 'طريقة عمل الخبز العربي', url: 'https://www.youtube.com/watch?v=bread1'),
        Video(title: 'خبز التوست المنزلي', url: 'https://www.youtube.com/watch?v=bread2'),
      ],
      articles: [
        Article(title: 'أساسيات خبز المنزل', content: 'كيفية تحضير عجينة الخبز المثالية...'),
      ],
      faqs: [
        FAQ(question: 'ما نوع الطحين الأفضل للخبز؟', answer: 'الطحين متعدد الاستخدامات أو طحين الخبز عالي البروتين...'),
      ],
    ),
    HomeFoodIndustryGroup(
      id: '4',
      name: 'صناعة الحلويات',
      imageUrl: 'https://cdn-icons-png.flaticon.com/512/2674/2674088.png',
      videos: [
        Video(title: 'تحضير البقلاوة المنزلية', url: 'https://www.youtube.com/watch?v=sweets1'),
        Video(title: 'كيفية صنع الكوكيز', url: 'https://www.youtube.com/watch?v=sweets2'),
      ],
      articles: [
        Article(title: 'نصائح لصنع الحلويات', content: 'استخدام مكونات عالية الجودة للحلويات...'),
      ],
      faqs: [
        FAQ(question: 'كيفية الحفاظ على قوام الحلويات؟', answer: 'التحكم في درجة الحرارة والمكونات بدقة...'),
      ],
    ),
    HomeFoodIndustryGroup(
      id: '5',
      name: 'صناعة المشروبات',
      imageUrl: 'https://cdn-icons-png.flaticon.com/512/2674/2674089.png',
      videos: [
        Video(title: 'تحضير عصير الليمون بالنعناع', url: 'https://www.youtube.com/watch?v=drink1'),
        Video(title: 'صنع القهوة الباردة', url: 'https://www.youtube.com/watch?v=drink2'),
      ],
      articles: [
        Article(title: 'دليل المشروبات المنزلية', content: 'كيفية تحضير مشروبات منعشة وصحية...'),
      ],
      faqs: [
        FAQ(question: 'ما هي أفضل المكونات للمشروبات؟', answer: 'استخدام فواكه طازجة ومحليات طبيعية...'),
      ],
    ),
    HomeFoodIndustryGroup(
      id: '6',
      name: 'صناعة الأجبان',
      imageUrl: 'https://cdn-icons-png.flaticon.com/512/2674/2674090.png',
      videos: [
        Video(title: 'تحضير جبنة الموزاريلا المنزلية', url: 'https://www.youtube.com/watch?v=cheese1'),
        Video(title: 'صنع جبن القريش', url: 'https://www.youtube.com/watch?v=cheese2'),
      ],
      articles: [
        Article(title: 'أساسيات صناعة الأجبان', content: 'كيفية اختيار الحليب المناسب للأجبان...'),
      ],
      faqs: [
        FAQ(question: 'ما هي الأدوات اللازمة لصنع الجبن؟', answer: 'وعاء مقاوم للحرارة، منفحة، وقوالب جبن...'),
      ],
    ),
  ];

  final List<Post> posts = [
    Post(user: 'علي', content: 'صنعت مربى الفراولة وكانت رائعة!', imageUrl: 'https://cdn-icons-png.flaticon.com/512/2674/2674081.png'),
    Post(user: 'سارة', content: 'نصائح لتحضير مخلل الخيار.'),
    Post(user: 'محمد', content: 'جربت خبز التوست المنزلي، ممتاز!', imageUrl: 'https://cdn-icons-png.flaticon.com/512/2674/2674087.png'),
  ];

  final List<MarketItem> foodItems = [];
  final List<String> foodTypes = [
    'مربى الفراولة',
    'مربى الخوخ',
    'مربى التين',
    'مربى المشمش',
    'مخلل الخيار',
    'مخلل الجزر',
    'مخلل اللفت',
    'مخلل متنوع',
    'خبز عربي',
    'خبز توست',
    'بقلاوة',
    'كوكيز',
    'عصير ليمون',
    'قهوة باردة',
    'جبنة موزاريلا',
    'جبن قريش',
  ];

  final TextEditingController _postController = TextEditingController();
  final ConfettiController _confettiController = ConfettiController(duration: const Duration(seconds: 3));
  late AnimationController _fabAnimationController;
  late Animation<double> _fabScaleAnimation;
  ImageProvider? _selectedMedia;

  String selectedMarketFilter = 'الكل';
  int userPoints = 100;

  @override
  void initState() {
    super.initState();
    _fabAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 600),
    )..repeat(reverse: true);
    _fabScaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(parent: _fabAnimationController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _confettiController.dispose();
    _fabAnimationController.dispose();
    _postController.dispose();
    super.dispose();
  }

  Widget _buildShimmerPlaceholder() {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: Container(
        width: 120,
        height: 120,
        color: Colors.white,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Scaffold(
          appBar: AppBar(
            title: AnimatedTextKit(
              animatedTexts: [
                TypewriterAnimatedText(
                  'موسوعة الصناعات الغذائية المنزلية',
                  textStyle: const TextStyle(fontSize: 22, fontWeight: FontWeight.bold, color: Colors.white),
                  speed: const Duration(milliseconds: 100),
                ),
              ],
              totalRepeatCount: 1,
            ),
            backgroundColor: Colors.orange.shade700,
            flexibleSpace: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [Colors.orange.shade700, Colors.orange.shade300],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
              ),
            ),
          ),
          body: RefreshIndicator(
            onRefresh: () async {
              await Future.delayed(const Duration(seconds: 1));
              setState(() {});
              _confettiController.play();
              Fluttertoast.showToast(msg: 'تم تحديث الصفحة', backgroundColor: Colors.orange.shade700);
            },
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: AnimationLimiter(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: AnimationConfiguration.toStaggeredList(
                    duration: const Duration(milliseconds: 500),
                    childAnimationBuilder: (widget) => ScaleAnimation(
                      scale: 0.8,
                      child: FadeInAnimation(child: widget),
                    ),
                    children: [
                      _buildSectionTitle('أنواع الصناعات الغذائية'),
                      const SizedBox(height: 16),
                      _buildIndustryCards(),
                      const SizedBox(height: 24),
                      _buildSectionTitle('سوق بيع وشراء المنتجات'),
                      const SizedBox(height: 12),
                      _buildMarketplace(),
                      const SizedBox(height: 24),
                      _buildSectionTitle('منشورات المجتمع'),
                      const SizedBox(height: 12),
                      _buildPostInput(),
                      const SizedBox(height: 12),
                      _buildPostsList(),
                    ],
                  ),
                ),
              ),
            ),
          ),
          floatingActionButton: ScaleTransition(
            scale: _fabScaleAnimation,
            child: FloatingActionButton(
              onPressed: () => _showQuickActions(context),
              tooltip: 'إجراءات سريعة',
              backgroundColor: Colors.orange.shade700,
              child: const Icon(Icons.add, color: Colors.white),
            ),
          ),
        ),
        Align(
          alignment: Alignment.topCenter,
          child: ConfettiWidget(
            confettiController: _confettiController,
            blastDirectionality: BlastDirectionality.explosive,
            particleDrag: 0.05,
            emissionFrequency: 0.05,
            numberOfParticles: 50,
            gravity: 0.05,
            colors: const [Colors.orange, Colors.red, Colors.yellow, Colors.green],
          ),
        ),
      ],
    );
  }

  Widget _buildSectionTitle(String title) {
    return AnimatedTextKit(
      animatedTexts: [
        FadeAnimatedText(
          title,
          textStyle: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: Colors.orange.shade700,
          ),
        ),
      ],
      totalRepeatCount: 1,
    );
  }

  Widget _buildIndustryCards() {
    return SizedBox(
      height: 200,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: industries.length,
        itemBuilder: (context, index) {
          final industry = industries[index];
          return AnimationConfiguration.staggeredList(
            position: index,
            duration: const Duration(milliseconds: 600),
            child: SlideAnimation(
              horizontalOffset: 50.0,
              child: FadeInAnimation(
                child: Padding(
                  padding: const EdgeInsets.only(right: 12.0),
                  child: GestureDetector(
                    onTap: () => _showIndustryDetails(context, industry),
                    child: Card(
                      elevation: 8,
                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
                      child: Container(
                        width: 150,
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [Colors.orange.shade200, Colors.orange.shade400],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ),
                          borderRadius: BorderRadius.circular(16),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.orange.withOpacity(0.3),
                              blurRadius: 10,
                              offset: const Offset(0, 5),
                            ),
                          ],
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            ClipRRect(
                              borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
                              child: CachedNetworkImage(
                                imageUrl: industry.imageUrl,
                                height: 100,
                                width: double.infinity,
                                fit: BoxFit.cover,
                                placeholder: (context, url) => _buildShimmerPlaceholder(),
                                errorWidget: (context, url, error) => const Icon(Icons.error),
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: AnimatedTextKit(
                                animatedTexts: [
                                  TypewriterAnimatedText(
                                    industry.name,
                                    textStyle: const TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.white,
                                    ),
                                    speed: const Duration(milliseconds: 100),
                                  ),
                                ],
                                totalRepeatCount: 1,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildMarketplace() {
    final filteredItems = selectedMarketFilter == 'الكل'
        ? foodItems
        : foodItems.where((item) => item.type == selectedMarketFilter).toList();

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            colors: [Colors.orange.shade50, Colors.orange.shade100],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'سوق المنتجات الغذائية',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(color: Colors.orange.shade700),
              ),
              const SizedBox(height: 12),
              DropdownButton<String>(
                value: selectedMarketFilter,
                isExpanded: true,
                items: ['الكل', ...foodTypes].map((filter) {
                  return DropdownMenuItem<String>(
                    value: filter,
                    child: Text(filter),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    selectedMarketFilter = value!;
                    _confettiController.play();
                    Fluttertoast.showToast(msg: 'تم تحديث الفلتر', backgroundColor: Colors.orange.shade700);
                  });
                },
                dropdownColor: Colors.orange.shade50,
                borderRadius: BorderRadius.circular(12),
              ),
              const SizedBox(height: 12),
              SizedBox(
                height: 250,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  itemCount: filteredItems.length,
                  itemBuilder: (context, index) {
                    final item = filteredItems[index];
                    return AnimationConfiguration.staggeredList(
                      position: index,
                      duration: const Duration(milliseconds: 600),
                      child: SlideAnimation(
                        horizontalOffset: 50.0,
                        child: FadeInAnimation(
                          child: Padding(
                            padding: const EdgeInsets.only(right: 12.0),
                            child: Card(
                              elevation: 3,
                              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                              child: Container(
                                width: 200,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(12),
                                  gradient: LinearGradient(
                                    colors: [Colors.orange.shade100, Colors.orange.shade200],
                                    begin: Alignment.topLeft,
                                    end: Alignment.bottomRight,
                                  ),
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    ClipRRect(
                                      borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
                                      child: CachedNetworkImage(
                                        imageUrl: item.imageUrls.first,
                                        width: 200,
                                        height: 120,
                                        fit: BoxFit.cover,
                                        placeholder: (context, url) => _buildShimmerPlaceholder(),
                                        errorWidget: (context, url, error) => const Icon(Icons.error),
                                      ),
                                    ),
                                    Padding(
                                      padding: const EdgeInsets.all(8.0),
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Text(item.type, style: TextStyle(fontWeight: FontWeight.bold, color: Colors.orange.shade700)),
                                          Text('الكمية: ${item.quantity} كجم', style: TextStyle(color: Colors.orange.shade700)),
                                          Text('السعر: ${item.price} ريال', style: TextStyle(color: Colors.orange.shade700)),
                                          Text('الصلاحية: ${item.validity}', style: TextStyle(color: Colors.orange.shade700)),
                                          Text('التواصل: ${item.contactNumber}', style: TextStyle(color: Colors.orange.shade700)),
                                          if (item.nutritionalContent != null)
                                            Text('المحتوى: ${item.nutritionalContent}', style: TextStyle(color: Colors.orange.shade700)),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
              const SizedBox(height: 12),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  ElevatedButton(
                    onPressed: () => _showAddFoodDialog(context),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange.shade700,
                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                    ),
                    child: const Text('إضافة منتج غذائي', style: TextStyle(color: Colors.white)),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showAddFoodDialog(BuildContext context) {
    final quantityController = TextEditingController();
    final priceController = TextEditingController();
    final addressController = TextEditingController();
    final contactController = TextEditingController();
    final detailsController = TextEditingController();
    final nutritionalContentController = TextEditingController();
    String selectedFoodType = foodTypes.first;
    String selectedValidity = 'أسبوع';
    List<String> imageUrls = [];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('إضافة منتج غذائي', style: TextStyle(color: Colors.orange.shade700)),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              DropdownButton<String>(
                value: selectedFoodType,
                isExpanded: true,
                items: foodTypes.map((String type) {
                  return DropdownMenuItem<String>(
                    value: type,
                    child: Text(type),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  setState(() {
                    selectedFoodType = newValue!;
                  });
                },
                dropdownColor: Colors.orange.shade50,
              ),
              const SizedBox(height: 8),
              TextField(
                controller: quantityController,
                decoration: InputDecoration(
                  hintText: 'الكمية (كجم)',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.orange.shade50,
                ),
                keyboardType: TextInputType.number,
              ),
              const SizedBox(height: 8),
              TextField(
                controller: priceController,
                decoration: InputDecoration(
                  hintText: 'السعر (ريال)',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.orange.shade50,
                ),
                keyboardType: TextInputType.number,
              ),
              const SizedBox(height: 8),
              TextField(
                controller: addressController,
                decoration: InputDecoration(
                  hintText: 'العنوان',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.orange.shade50,
                  suffixIcon: IconButton(
                    icon: Icon(Icons.location_on, color: Colors.orange.shade700),
                    onPressed: () async {
                      try {
                        Position position = await Geolocator.getCurrentPosition();
                        addressController.text = 'موقع جغرافي: ${position.latitude}, ${position.longitude}';
                      } catch (e) {
                        Fluttertoast.showToast(msg: 'خطأ في تحديد الموقع', backgroundColor: Colors.red);
                      }
                    },
                  ),
                ),
              ),
              const SizedBox(height: 8),
              DropdownButton<String>(
                value: selectedValidity,
                isExpanded: true,
                items: ['أسبوع', 'أسبوعان', 'شهر'].map((String validity) {
                  return DropdownMenuItem<String>(
                    value: validity,
                    child: Text(validity),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  setState(() {
                    selectedValidity = newValue!;
                  });
                },
                dropdownColor: Colors.orange.shade50,
              ),
              const SizedBox(height: 8),
              TextField(
                controller: contactController,
                decoration: InputDecoration(
                  hintText: 'رقم التواصل',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.orange.shade50,
                ),
                keyboardType: TextInputType.phone,
              ),
              const SizedBox(height: 8),
              TextField(
                controller: nutritionalContentController,
                decoration: InputDecoration(
                  hintText: 'المحتوى الغذائي (مثل: ملح 5%)',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.orange.shade50,
                ),
              ),
              const SizedBox(height: 8),
              TextField(
                controller: detailsController,
                decoration: InputDecoration(
                  hintText: 'تفاصيل المنتج',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.orange.shade50,
                ),
                maxLines: 3,
              ),
              const SizedBox(height: 8),
              ElevatedButton(
                onPressed: () {
                  imageUrls = List.generate(4, (index) => 'https://cdn-icons-png.flaticon.com/512/2674/267408${index % 2 + 1}.png');
                  if (imageUrls.length < 4) {
                    Fluttertoast.showToast(msg: 'يرجى رفع 4 صور على الأقل', backgroundColor: Colors.red);
                    return;
                  }
                  if (quantityController.text.isEmpty ||
                      priceController.text.isEmpty ||
                      addressController.text.isEmpty ||
                      contactController.text.isEmpty ||
                      detailsController.text.isEmpty) {
                    Fluttertoast.showToast(msg: 'يرجى ملء جميع الحقول', backgroundColor: Colors.red);
                    return;
                  }
                  setState(() {
                    foodItems.add(MarketItem(
                      id: DateTime.now().millisecondsSinceEpoch.toString(),
                      user: 'Current User',
                      type: selectedFoodType,
                      imageUrls: imageUrls,
                      quantity: double.parse(quantityController.text),
                      price: double.parse(priceController.text),
                      address: addressController.text,
                      date: DateTime.now(),
                      validity: selectedValidity,
                      contactNumber: contactController.text,
                      details: detailsController.text,
                      nutritionalContent: nutritionalContentController.text.isNotEmpty ? nutritionalContentController.text : null,
                    ));
                    userPoints += 15;
                    _confettiController.play();
                    Fluttertoast.showToast(msg: 'تمت إضافة المنتج', backgroundColor: Colors.orange.shade700);
                  });
                  Navigator.pop(context);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange.shade700,
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                ),
                child: const Text('إضافة', style: TextStyle(color: Colors.white)),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
        ],
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        backgroundColor: Colors.orange.shade50,
      ),
    );
  }

  Widget _buildPostInput() {
    return Card(
      elevation: 6,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CircleAvatar(
                  backgroundColor: Colors.orange.shade200,
                  child: const Icon(Icons.person, color: Colors.white, size: 20),
                  radius: 20,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: TextField(
                    controller: _postController,
                    decoration: InputDecoration(
                      hintText: 'ما الذي تفكر به؟',
                      hintStyle: TextStyle(color: Colors.grey.shade600, fontSize: 15),
                      border: InputBorder.none,
                      contentPadding: const EdgeInsets.symmetric(vertical: 8),
                    ),
                    maxLines: 5,
                    minLines: 1,
                    style: const TextStyle(fontSize: 15),
                  ),
                ),
              ],
            ),
            if (_selectedMedia != null)
              Container(
                margin: const EdgeInsets.only(top: 12),
                height: 200,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  image: DecorationImage(
                    image: _selectedMedia!,
                    fit: BoxFit.cover,
                  ),
                ),
                child: Stack(
                  children: [
                    Positioned(
                      top: 8,
                      right: 8,
                      child: GestureDetector(
                        onTap: () {
                          setState(() {
                            _selectedMedia = null;
                          });
                        },
                        child: Container(
                          padding: const EdgeInsets.all(4),
                          decoration: const BoxDecoration(
                            color: Colors.black54,
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(Icons.close, color: Colors.white, size: 18),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            Padding(
              padding: const EdgeInsets.only(top: 12.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  TextButton.icon(
                    onPressed: () async {
                      Fluttertoast.showToast(
                        msg: 'إضافة صورة',
                        backgroundColor: Colors.orange.shade800,
                        textColor: Colors.white,
                      );
                    },
                    icon: Icon(Icons.image, color: Colors.green.shade700, size: 22),
                    label: Text(
                      'صورة',
                      style: TextStyle(
                        color: Colors.grey.shade800,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    style: TextButton.styleFrom(
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(20),
                      ),
                      backgroundColor: Colors.green.shade50,
                    ),
                  ),
                  TextButton.icon(
                    onPressed: () {
                      Fluttertoast.showToast(
                        msg: 'إضافة فيديو',
                        backgroundColor: Colors.orange.shade800,
                        textColor: Colors.white,
                      );
                    },
                    icon: Icon(Icons.videocam, color: Colors.red.shade600, size: 22),
                    label: Text(
                      'فيديو',
                      style: TextStyle(
                        color: Colors.grey.shade800,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    style: TextButton.styleFrom(
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(20),
                      ),
                      backgroundColor: Colors.red.shade50,
                    ),
                  ),
                  TextButton.icon(
                    onPressed: () {
                      Fluttertoast.showToast(
                        msg: 'إضافة قصة',
                        backgroundColor: Colors.orange.shade800,
                        textColor: Colors.white,
                      );
                    },
                    icon: Icon(Icons.book, color: Colors.purple.shade600, size: 22),
                    label: Text(
                      'قصة',
                      style: TextStyle(
                        color: Colors.grey.shade800,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    style: TextButton.styleFrom(
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(20),
                      ),
                      backgroundColor: Colors.purple.shade50,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 12),
            ElevatedButton(
              onPressed: () {
                if (_postController.text.isNotEmpty || _selectedMedia != null) {
                  setState(() {
                    posts.insert(
                      0,
                      Post(
                        user: 'Current User',
                        content: _postController.text,
                        imageUrl: _selectedMedia != null ? 'https://example.com/placeholder.jpg' : null,
                      ),
                    );
                    userPoints += 10;
                    _confettiController.play();
                    Fluttertoast.showToast(
                      msg: 'تم نشر المنشور',
                      backgroundColor: Colors.orange.shade700,
                    );
                    _postController.clear();
                    _selectedMedia = null;
                  });
                } else {
                  Fluttertoast.showToast(
                    msg: 'الرجاء إدخال نص أو إضافة صورة',
                    backgroundColor: Colors.red,
                  );
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange.shade700,
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                elevation: 2,
              ),
              child: const Text(
                'نشر',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPostsList() {
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: posts.length,
      itemBuilder: (context, index) {
        final post = posts[index];
        return AnimationConfiguration.staggeredList(
          position: index,
          duration: const Duration(milliseconds: 600),
          child: SlideAnimation(
            verticalOffset: 50.0,
            child: FadeInAnimation(
              child: Card(
                margin: const EdgeInsets.symmetric(vertical: 8),
                elevation: 4,
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    gradient: LinearGradient(
                      colors: [Colors.orange.shade50, Colors.orange.shade100],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            CircleAvatar(backgroundColor: Colors.orange.shade700, child: Text(post.user[0])),
                            const SizedBox(width: 8),
                            Text(post.user, style: TextStyle(fontWeight: FontWeight.bold, color: Colors.orange.shade700)),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Text(post.content, style: TextStyle(color: Colors.orange.shade700)),
                        if (post.imageUrl != null) ...[
                          const SizedBox(height: 8),
                          ClipRRect(
                            borderRadius: BorderRadius.circular(12),
                            child: CachedNetworkImage(
                              imageUrl: post.imageUrl!,
                              height: 150,
                              width: double.infinity,
                              fit: BoxFit.cover,
                              placeholder: (context, url) => _buildShimmerPlaceholder(),
                              errorWidget: (context, url, error) => const Icon(Icons.error),
                            ),
                          ),
                        ],
                        const SizedBox(height: 8),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            IconButton(
                              icon: Icon(Icons.thumb_up_alt, color: Colors.orange.shade700),
                              onPressed: () {
                                setState(() {
                                  userPoints += 2;
                                  _confettiController.play();
                                  Fluttertoast.showToast(msg: 'تم الإعجاب بالمنشور', backgroundColor: Colors.orange.shade700);
                                });
                              },
                            ),
                            IconButton(
                              icon: Icon(Icons.comment, color: Colors.orange.shade700),
                              onPressed: () {
                                Fluttertoast.showToast(msg: 'التعليقات قيد التطوير', backgroundColor: Colors.orange.shade700);
                              },
                            ),
                            IconButton(
                              icon: Icon(Icons.share, color: Colors.orange.shade700),
                              onPressed: () {
                                Share.share('تحقق من هذا المنشور: ${post.content}');
                                _confettiController.play();
                                Fluttertoast.showToast(msg: 'تم مشاركة المنشور', backgroundColor: Colors.orange.shade700);
                              },
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  void _showIndustryDetails(BuildContext context, HomeFoodIndustryGroup industry) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.9,
        minChildSize: 0.5,
        maxChildSize: 0.95,
        builder: (context, scrollController) => Container(
          decoration: BoxDecoration(
            color: Theme.of(context).scaffoldBackgroundColor,
            borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
            boxShadow: [
              BoxShadow(
                color: Colors.orange.withOpacity(0.3),
                blurRadius: 10,
                offset: const Offset(0, -5),
              ),
            ],
          ),
          child: DefaultTabController(
            length: 3,
            child: Column(
              children: [
                Container(
                  margin: const EdgeInsets.symmetric(vertical: 8),
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: Colors.grey,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                TabBar(
                  tabs: const [
                    Tab(text: 'الفيديوهات'),
                    Tab(text: 'المقالات'),
                    Tab(text: 'الأسئلة الشائعة'),
                  ],
                  labelColor: Colors.orange.shade700,
                  unselectedLabelColor: Colors.grey,
                  indicatorColor: Colors.orange.shade700,
                ),
                Expanded(
                  child: TabBarView(
                    children: [
                      _buildVideosTab(industry.videos),
                      _buildArticlesTab(industry.articles),
                      _buildFAQsTab(industry.faqs),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
    Fluttertoast.showToast(msg: 'تم عرض تفاصيل ${industry.name}', backgroundColor: Colors.orange.shade700);
  }

  Widget _buildVideosTab(List<Video> videos) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: videos.length,
      itemBuilder: (context, index) {
        final video = videos[index];
        return AnimationConfiguration.staggeredList(
          position: index,
          duration: const Duration(milliseconds: 600),
          child: SlideAnimation(
            verticalOffset: 50.0,
            child: FadeInAnimation(
              child: ListTile(
                leading: Icon(Icons.videocam, color: Colors.orange.shade700),
                title: Text(video.title, style: TextStyle(color: Colors.orange.shade700)),
                onTap: () {
                  ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('تشغيل ${video.title}')));
                  _confettiController.play();
                  Fluttertoast.showToast(msg: 'جارٍ تشغيل الفيديو', backgroundColor: Colors.orange.shade700);
                },
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildArticlesTab(List<Article> articles) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: articles.length,
      itemBuilder: (context, index) {
        final article = articles[index];
        return AnimationConfiguration.staggeredList(
          position: index,
          duration: const Duration(milliseconds: 600),
          child: SlideAnimation(
            verticalOffset: 50.0,
            child: FadeInAnimation(
              child: ExpansionTile(
                leading: Icon(Icons.article, color: Colors.orange.shade700),
                title: Text(article.title, style: TextStyle(color: Colors.orange.shade700)),
                tilePadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                collapsedBackgroundColor: Colors.orange.shade50,
                backgroundColor: Colors.orange.shade100,
                children: [
                  Padding(
                    padding: const EdgeInsets.all(16),
                    child: Text(article.content, style: TextStyle(color: Colors.orange.shade700)),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildFAQsTab(List<FAQ> faqs) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: faqs.length,
      itemBuilder: (context, index) {
        final faq = faqs[index];
        return AnimationConfiguration.staggeredList(
          position: index,
          duration: const Duration(milliseconds: 600),
          child: SlideAnimation(
            verticalOffset: 50.0,
            child: FadeInAnimation(
              child: ExpansionTile(
                leading: Icon(Icons.question_answer, color: Colors.orange.shade700),
                title: Text(faq.question, style: TextStyle(color: Colors.orange.shade700)),
                tilePadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                collapsedBackgroundColor: Colors.orange.shade50,
                backgroundColor: Colors.orange.shade100,
                children: [
                  Padding(
                    padding: const EdgeInsets.all(16),
                    child: Text(faq.answer, style: TextStyle(color: Colors.orange.shade700)),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  void _showQuickActions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Theme.of(context).scaffoldBackgroundColor,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
          boxShadow: [
            BoxShadow(
              color: Colors.orange.withOpacity(0.3),
              blurRadius: 10,
              offset: const Offset(0, -5),
            ),
          ],
        ),
        child: SingleChildScrollView(
          padding: EdgeInsets.only(
            bottom: MediaQuery.of(context).viewInsets.bottom,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Card(
                elevation: 4,
                margin: const EdgeInsets.symmetric(vertical: 8),
                child: ListTile(
                  leading: Icon(Icons.post_add, color: Colors.orange.shade700),
                  title: Text(
                    'إنشاء منشور',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.orange.shade700,
                    ),
                  ),
                  onTap: () {
                    Navigator.pop(context);
                    _postController.text = '';
                    FocusScope.of(context).requestFocus(FocusNode());
                    _confettiController.play();
                    Fluttertoast.showToast(
                      msg: 'جاهز لإنشاء منشور',
                      backgroundColor: Colors.orange.shade700,
                      textColor: Colors.white,
                    );
                  },
                ),
              ),
              ListTile(
                leading: Icon(Icons.add_shopping_cart, color: Colors.orange.shade700),
                title: const Text('إضافة منتج غذائي'),
                onTap: () {
                  Navigator.pop(context);
                  _showAddFoodDialog(context);
                  _confettiController.play();
                  Fluttertoast.showToast(msg: 'جاهز لإضافة منتج غذائي', backgroundColor: Colors.orange.shade700);
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
