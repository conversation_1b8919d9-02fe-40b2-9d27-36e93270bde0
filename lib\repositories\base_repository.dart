import '../services/api_service.dart';

/// واجهة أساسية للمستودعات
abstract class BaseRepository<T> {
  /// جلب عنصر واحد بواسطة المعرف
  Future<ApiResponse<T>> getById(String id);
  
  /// جلب قائمة من العناصر
  Future<ApiResponse<List<T>>> getAll([Map<String, dynamic>? params]);
  
  /// إنشاء عنصر جديد
  Future<ApiResponse<T>> create(Map<String, dynamic> data);
  
  /// تحديث عنصر موجود
  Future<ApiResponse<T>> update(String id, Map<String, dynamic> data);
  
  /// حذف عنصر
  Future<ApiResponse<bool>> delete(String id);
}