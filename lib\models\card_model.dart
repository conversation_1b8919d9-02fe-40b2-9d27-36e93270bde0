// نموذج البطاقة (Card)
class New {
  final String id;
  final String image;
  final String title;
  final String description;

  const New({
    required this.id,
    required this.image,
    required this.title,
    required this.description,
  });

  factory New.fromJson(Map<String, dynamic> json) {
    return New(
      id: json['_id'] ?? '',
      image: json['image'] ?? '',
      title: json['title'] ?? '',
      description: json['description'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'image': image,
      'title': title,
      'description': description,
    };
  }
}
