import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:connectivity_plus/connectivity_plus.dart';
import '../appstate.dart';

enum ApiStatus { success, error, noInternet }

class ApiResponse<T> {
  final ApiStatus status;
  final T? data;
  final String? message;

  ApiResponse.success(this.data)
      : status = ApiStatus.success,
        message = null;

  ApiResponse.error(this.message)
      : status = ApiStatus.error,
        data = null;

  ApiResponse.noInternet()
      : status = ApiStatus.noInternet,
        data = null,
        message = 'لا يوجد اتصال بالإنترنت';

  bool get isSuccess => status == ApiStatus.success;
  bool get isError => status == ApiStatus.error;
  bool get isNoInternet => status == ApiStatus.noInternet;
}

class ApiService {
  static final ApiService _instance = ApiService._internal();
  factory ApiService() => _instance;
  ApiService._internal();

  final Connectivity _connectivity = Connectivity();
  final Duration _timeout = const Duration(seconds: 15);
  final int _maxRetries = 3;
  final String _baseUrl = 'http://197.63.231.187:3000/'; // Ends with '/'

  /// التحقق من وجود اتصال بالإنترنت
  Future<bool> hasInternetConnection() async {
    try {
      final connectivityResult = await _connectivity.checkConnectivity();
      if (connectivityResult == ConnectivityResult.none) {
        print('No connectivity detected');
        return false;
      }

      // التحقق من الاتصال بالإنترنت عن طريق الاتصال بخادم Google
      final response = await http
          .get(Uri.parse('https://www.google.com'))
          .timeout(_timeout, onTimeout: () {
        throw TimeoutException('Internet check timed out');
      });
      print('Internet check response: ${response.statusCode}');
      return response.statusCode == 200;
    } catch (e) {
      print('Internet check failed: $e');
      return false;
    }
  }

  /// طلب GET مع إعادة المحاولة والتعامل مع الأخطاء
  Future<ApiResponse<T>> get<T>(
      String endpoint, {
        Map<String, String>? headers,
        required T Function(dynamic data) parser,
        bool useToken = true,
      }) async {
    return _request<T>(
      'GET',
      endpoint,
      headers: headers,
      parser: parser,
      useToken: useToken,
    );
  }

  /// طلب POST مع إعادة المحاولة والتعامل مع الأخطاء
  Future<ApiResponse<T>> post<T>(
      String endpoint, {
        Map<String, String>? headers,
        dynamic body,
        required T Function(dynamic data) parser,
        bool useToken = true,
      }) async {
    return _request<T>(
      'POST',
      endpoint,
      headers: headers,
      body: body,
      parser: parser,
      useToken: useToken,
    );
  }

  /// طلب PUT مع إعادة المحاولة والتعامل مع الأخطاء
  Future<ApiResponse<T>> put<T>(
      String endpoint, {
        Map<String, String>? headers,
        dynamic body,
        required T Function(dynamic data) parser,
        bool useToken = true,
      }) async {
    return _request<T>(
      'PUT',
      endpoint,
      headers: headers,
      body: body,
      parser: parser,
      useToken: useToken,
    );
  }

  /// طلب DELETE مع إعادة المحاولة والتعامل مع الأخطاء
  Future<ApiResponse<T>> delete<T>(
      String endpoint, {
        Map<String, String>? headers,
        dynamic body,
        required T Function(dynamic data) parser,
        bool useToken = true,
      }) async {
    return _request<T>(
      'DELETE',
      endpoint,
      headers: headers,
      body: body,
      parser: parser,
      useToken: useToken,
    );
  }

  /// الدالة الأساسية لإرسال الطلبات مع إعادة المحاولة والتعامل مع الأخطاء
  Future<ApiResponse<T>> _request<T>(
      String method,
      String endpoint, {
        Map<String, String>? headers,
        dynamic body,
        required T Function(dynamic data) parser,
        bool useToken = true,
      }) async {
    // التحقق من وجود اتصال بالإنترنت
    if (!await hasInternetConnection()) {
      print('No internet connection for $method $endpoint');
      return ApiResponse<T>.noInternet();
    }

    // تجهيز الرأس (headers)
    final Map<String, String> requestHeaders = {
      'Content-Type': 'application/json',
      ...?headers,
    };

    // إضافة رمز المصادقة إذا كان مطلوبًا
    if (useToken) {
      final appState = AppState();
      final token = appState.token;
      if (token != null && token.isNotEmpty) {
        requestHeaders['x-auth-token'] = token;
      } else {
        print('No token available for $method $endpoint');
      }
    }

    // تجهيز عنوان URL
    final url = Uri.parse('$_baseUrl$endpoint');
    print('Request: $method $url');

    // تنفيذ الطلب مع إعادة المحاولة
    int retries = 0;
    while (retries < _maxRetries) {
      try {
        http.Response response;

        switch (method) {
          case 'GET':
            response = await http
                .get(url, headers: requestHeaders)
                .timeout(_timeout, onTimeout: () {
              throw TimeoutException('Request timed out: $method $endpoint');
            });
            break;
          case 'POST':
            response = await http
                .post(url, headers: requestHeaders, body: jsonEncode(body))
                .timeout(_timeout, onTimeout: () {
              throw TimeoutException('Request timed out: $method $endpoint');
            });
            break;
          case 'PUT':
            response = await http
                .put(url, headers: requestHeaders, body: jsonEncode(body))
                .timeout(_timeout, onTimeout: () {
              throw TimeoutException('Request timed out: $method $endpoint');
            });
            break;
          case 'DELETE':
            response = await http
                .delete(
              url,
              headers: requestHeaders,
              body: body != null ? jsonEncode(body) : null,
            )
                .timeout(_timeout, onTimeout: () {
              throw TimeoutException('Request timed out: $method $endpoint');
            });
            break;
          default:
            return ApiResponse<T>.error('طريقة غير مدعومة: $method');
        }

        // تسجيل استجابة الخادم
        print('Response: ${response.statusCode} for $method $endpoint');
        print('Raw response body: ${response.body}');

        // التحقق من الاستجابة
        if (response.statusCode >= 200 && response.statusCode < 300) {
          try {
            final responseData = jsonDecode(response.body);
            if (responseData is Map && responseData['success'] == false) {
              return ApiResponse<T>.error(
                  responseData['message'] ?? 'استجابة الخادم تحتوي على خطأ');
            }
            final parsedData = parser(responseData);
            print('Parsed data: $parsedData');
            return ApiResponse<T>.success(parsedData);
          } catch (e) {
            print('JSON parsing error for $method $endpoint: $e');
            return ApiResponse<T>.error('خطأ في تحليل استجابة الخادم: $e');
          }
        } else {
          // خطأ من الخادم
          String errorMessage;
          try {
            final errorData = jsonDecode(response.body);
            errorMessage = errorData['message'] ?? 'حدث خطأ في الخادم';
          } catch (e) {
            errorMessage = 'حدث خطأ في الخادم: ${response.statusCode}';
          }
          print('Server error: $errorMessage for $method $endpoint');

          // إعادة المحاولة فقط للأخطاء 5xx (أخطاء الخادم)
          if (response.statusCode >= 500) {
            retries++;
            print('Retrying ($retries/$_maxRetries) for $method $endpoint');
            await Future.delayed(Duration(seconds: retries));
            continue;
          }

          return ApiResponse<T>.error(errorMessage);
        }
      } on TimeoutException catch (e) {
        retries++;
        print('Timeout ($retries/$_maxRetries) for $method $endpoint: $e');
        await Future.delayed(Duration(seconds: retries));
      } on SocketException catch (e) {
        print('Socket error for $method $endpoint: $e');
        return ApiResponse<T>.noInternet();
      } catch (e) {
        print('Unexpected error for $method $endpoint: $e');
        return ApiResponse<T>.error('حدث خطأ غير متوقع: $e');
      }
    }

    // فشلت جميع المحاولات
    print('All retries failed for $method $endpoint');
    return ApiResponse<T>.error('فشلت جميع محاولات الاتصال بالخادم');
  }
}