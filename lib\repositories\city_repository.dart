import '../services/api_service.dart';
import 'base_repository.dart';
import '../id11.dart'; // للوصول إلى البيانات المحلية egyptCities

/// مستودع المدن
class CityRepository implements BaseRepository<City> {
  static final CityRepository _instance = CityRepository._internal();
  factory CityRepository() => _instance;
  CityRepository._internal();

  final ApiService _apiService = ApiService();
  
  // تخزين مؤقت للمدن
  List<City>? _cachedCities;
  DateTime? _lastCacheTime;
  
  // مدة صلاحية التخزين المؤقت (30 دقيقة)
  final Duration _cacheDuration = const Duration(minutes: 30);
  
  // التحقق من صلاحية التخزين المؤقت
  bool get _isCacheValid => 
      _cachedCities != null && 
      _lastCacheTime != null &&
      DateTime.now().difference(_lastCacheTime!) < _cacheDuration;
  
  @override
  Future<ApiResponse<City>> getById(String id) async {
    // محاولة استخدام التخزين المؤقت أولاً
    if (_isCacheValid && _cachedCities != null) {
      final city = _cachedCities!.firstWhere(
        (city) => city.id == id,
        orElse: () => City(id: '', name: '', image: '', villages: []),
      );
      
      if (city.id.isNotEmpty) {
        return ApiResponse<City>.success(city);
      }
    }
    
    // محاولة جلب البيانات من الخادم
    final response = await _apiService.get<City>(
      'cities/$id',
      parser: (data) => City.fromJson(data),
    );
    
    // في حالة فشل الاتصال، البحث في البيانات المحلية
    if (!response.isSuccess) {
      final localCity = egyptCities.firstWhere(
        (city) => city.id == id,
        orElse: () => City(id: '', name: '', image: '', villages: []),
      );
      
      if (localCity.id.isNotEmpty) {
        return ApiResponse<City>.success(localCity);
      }
      
      return response;
    }
    
    return response;
  }
  
  @override
  Future<ApiResponse<List<City>>> getAll([Map<String, dynamic>? params]) async {
    // التحقق من التخزين المؤقت
    if (_isCacheValid && _cachedCities != null) {
      return ApiResponse<List<City>>.success(_cachedCities!);
    }
    
    // محاولة جلب البيانات من الخادم
    final response = await _apiService.get<List<City>>(
      'cities',
      parser: (data) => (data as List).map((item) => City.fromJson(item)).toList(),
    );
    
    // في حالة النجاح، تحديث التخزين المؤقت
    if (response.isSuccess && response.data != null) {
      _cachedCities = response.data;
      _lastCacheTime = DateTime.now();
      return response;
    }
    
    // في حالة فشل الاتصال، استخدام البيانات المحلية
    _cachedCities = egyptCities;
    _lastCacheTime = DateTime.now();
    return ApiResponse<List<City>>.success(egyptCities);
  }
  
  @override
  Future<ApiResponse<City>> create(Map<String, dynamic> data) async {
    final response = await _apiService.post<City>(
      'cities',
      body: data,
      parser: (data) => City.fromJson(data),
    );
    
    // في حالة النجاح، تحديث التخزين المؤقت
    if (response.isSuccess && response.data != null) {
      _cachedCities = null; // إبطال التخزين المؤقت
    }
    
    return response;
  }
  
  @override
  Future<ApiResponse<City>> update(String id, Map<String, dynamic> data) async {
    final response = await _apiService.put<City>(
      'cities/$id',
      body: data,
      parser: (data) => City.fromJson(data),
    );
    
    // في حالة النجاح، تحديث التخزين المؤقت
    if (response.isSuccess && response.data != null) {
      _cachedCities = null; // إبطال التخزين المؤقت
    }
    
    return response;
  }
  
  @override
  Future<ApiResponse<bool>> delete(String id) async {
    final response = await _apiService.delete<bool>(
      'cities/$id',
      parser: (data) => data['success'] ?? false,
    );
    
    // في حالة النجاح، تحديث التخزين المؤقت
    if (response.isSuccess && response.data == true) {
      _cachedCities = null; // إبطال التخزين المؤقت
    }
    
    return response;
  }
  
  /// مسح التخزين المؤقت
  void clearCache() {
    _cachedCities = null;
    _lastCacheTime = null;
  }
}