const express = require('express');
const router = express.Router();
const controller = require('../controllers/shareBorrowItem.controller');
const multer = require('multer');

// Configure multer for file uploads
const storage = multer.memoryStorage();
const upload = multer({ storage });

// Basic CRUD routes
router.get('/', controller.getItems);
router.get('/:id', controller.getItemById);
router.post('/', upload.single('file'), controller.createItem);
router.put('/:id', controller.updateItem);
router.delete('/:id', controller.deleteItem);

// Additional routes
router.post('/verification-media', upload.single('file'), controller.uploadVerificationMedia);
router.post('/borrow-request', controller.requestBorrow);
router.get('/:itemId/borrow-history', controller.getBorrowHistory);

module.exports = router;
