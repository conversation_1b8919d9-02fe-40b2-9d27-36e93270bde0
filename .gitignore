# ============================================
# Flutter/Dart/Pub
# ============================================

# Flutter/Dart/Pub related
**/doc/api/
**/ios/Flutter/.last_build_id
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.pub-cache/
.pub/
/build/

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

# Android Studio will place build artifacts here
/android/app/debug
/android/app/profile
/android/app/release

# ============================================
# Node.js
# ============================================

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Dependency directories
node_modules/
.pnp
.pnp.js

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Build output
/dist
/build
/coverage

# Debug logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# IDE and editor files
.idea/
.vscode/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# ============================================
# Testing
# ============================================
/coverage
.nyc_output

# ============================================
# Misc
# ============================================
*.pem
*.p12
*.key
*.crt
*.cert
*.cer
*.p7b
*.p7c
*.p7s
*.crl
*.crl.srl
*.srl
*.srl.key
*.sig
*.csr
*.csr.pem
*.csr.der
*.csr.der.pem
*.key
*.key.pem
*.key.der
*.key.der.pem
*.p12
*.pfx
*.p12.pem
*.pfx.pem
*.p12.der
*.pfx.der
*.p12.der.pem
*.pfx.der.pem
*.p7b
*.p7c
*.p7s
*.p7m
*.spc
*.p7r
*.p7s.p7m
*.p7s.p7c
*.p7s.p7b
*.p7s.p7s
*.p7s.p7r
