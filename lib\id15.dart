import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:socket_io_client/socket_io_client.dart' as io;
import 'package:animate_do/animate_do.dart';
import 'package:provider/provider.dart';
import 'package:youtube_player_flutter/youtube_player_flutter.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:timeago/timeago.dart' as timeago;
import 'package:file_picker/file_picker.dart';
import 'appstate.dart';

// Initialize timeago for Arabic
void setupTimeago() => timeago.setLocaleMessages('ar', timeago.ArMessages());

class ClubService {
  final String baseUrl;

  ClubService() : baseUrl = AppState.getBackendUrl();

  Future<List<Map<String, dynamic>>> fetchClubs(String token) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/api/clubs'),
        headers: {'x-auth-token': token},
      ).timeout(const Duration(seconds: 10));
      if (response.statusCode == 200) {
        return List<Map<String, dynamic>>.from(jsonDecode(response.body));
      }
      throw Exception('فشل في جلب الأندية');
    } catch (e) {
      throw Exception('خطأ: $e');
    }
  }

  Future<List<Map<String, dynamic>>> fetchAds(String token) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/api/ads'),
        headers: {'x-auth-token': token},
      ).timeout(const Duration(seconds: 10));
      if (response.statusCode == 200) {
        return List<Map<String, dynamic>>.from(jsonDecode(response.body));
      }
      return [
        {
          'image': 'https://via.placeholder.com/300x100',
          'text': 'إعلان افتراضي'
        }
      ];
    } catch (e) {
      return [
        {
          'image': 'https://via.placeholder.com/300x100',
          'text': 'إعلان افتراضي'
        }
      ];
    }
  }

  Future<List<Map<String, dynamic>>> fetchMatches(
      String token, String clubName) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/api/matches/$clubName'),
        headers: {'x-auth-token': token},
      ).timeout(const Duration(seconds: 10));
      if (response.statusCode == 200) {
        return List<Map<String, dynamic>>.from(jsonDecode(response.body));
      }
      throw Exception('فشل في جلب المباريات');
    } catch (e) {
      throw Exception('خطأ: $e');
    }
  }

  Future<String?> fetchLiveStreamUrl(String token, String clubName) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/api/live-stream/$clubName'),
        headers: {'x-auth-token': token},
      ).timeout(const Duration(seconds: 10));
      if (response.statusCode == 200) return jsonDecode(response.body)['url'];
      return null;
    } catch (e) {
      return null;
    }
  }

  Future<List<Map<String, dynamic>>> fetchNotifications(String token) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/api/notifications'),
        headers: {'x-auth-token': token},
      ).timeout(const Duration(seconds: 10));
      if (response.statusCode == 200) {
        return List<Map<String, dynamic>>.from(jsonDecode(response.body));
      }
      throw Exception('فشل في جلب الإشعارات');
    } catch (e) {
      throw Exception('خطأ: $e');
    }
  }

  Future<void> markNotificationRead(String token, String notificationId) async {
    try {
      final response = await http.put(
        Uri.parse('$baseUrl/api/notifications/$notificationId/read'),
        headers: {'x-auth-token': token},
      ).timeout(const Duration(seconds: 10));
      if (response.statusCode != 200) throw Exception('فشل في تحديث الإشعار');
    } catch (e) {
      throw Exception('خطأ: $e');
    }
  }

  Future<List<Map<String, dynamic>>> fetchPosts(String token, String clubName,
      {int page = 1}) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/api/posts/$clubName?page=$page&limit=10'),
        headers: {'x-auth-token': token},
      ).timeout(const Duration(seconds: 10));
      if (response.statusCode == 200) {
        return List<Map<String, dynamic>>.from(jsonDecode(response.body));
      }
      throw Exception('فشل في جلب المنشورات');
    } catch (e) {
      throw Exception('خطأ: $e');
    }
  }

  Future<void> addPost(
      String token, String clubName, String content, File? image) async {
    try {
      var request =
      http.MultipartRequest('POST', Uri.parse('$baseUrl/api/posts'));
      request.headers['x-auth-token'] = token;
      request.fields['clubName'] = clubName;
      request.fields['content'] = content;
      if (image != null) {
        request.files
            .add(await http.MultipartFile.fromPath('image', image.path));
      }
      final response =
      await request.send().timeout(const Duration(seconds: 10));
      if (response.statusCode != 201) throw Exception('فشل في إضافة المنشور');
    } catch (e) {
      throw Exception('خطأ: $e');
    }
  }

  Future<void> toggleLike(String token, String postId) async {
    try {
      final response = await http.put(
        Uri.parse('$baseUrl/api/posts/$postId/like'),
        headers: {'x-auth-token': token},
      ).timeout(const Duration(seconds: 10));
      if (response.statusCode != 200) throw Exception('فشل في تحديث الإعجاب');
    } catch (e) {
      throw Exception('خطأ: $e');
    }
  }

  Future<void> addComment(String token, String postId, String comment) async {
    try {
      final response = await http
          .post(
        Uri.parse('$baseUrl/api/posts/$postId/comment'),
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': token
        },
        body: jsonEncode({'text': comment}),
      )
          .timeout(const Duration(seconds: 10));
      if (response.statusCode != 200) throw Exception('فشل في إضافة التعليق');
    } catch (e) {
      throw Exception('خطأ: $e');
    }
  }
}

class Rayan15 extends StatefulWidget {
  const Rayan15({super.key});

  @override
  _Rayan15State createState() => _Rayan15State();
}

class _Rayan15State extends State<Rayan15> with TickerProviderStateMixin {
  final ClubService _clubService = ClubService();
  List<dynamic> _clubs = [];
  List<Map<String, String>> _ads = [];
  List<Map<String, dynamic>> _notifications = [];
  late PageController _pageController;
  int _currentPage = 0;
  Timer? _timer;
  bool _isLoading = true;
  final TextEditingController _searchController = TextEditingController();
  Timer? _debounce;
  late AnimationController _searchAnimationController;
  late Animation<double> _searchAnimation;

  @override
  void initState() {
    super.initState();
    setupTimeago();
    _pageController = PageController();
    _startAutoScroll();
    _fetchData();
    _searchController.addListener(_onSearch);

    _searchAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    _searchAnimation = Tween<double>(begin: 0.9, end: 1.0).animate(
      CurvedAnimation(
        parent: _searchAnimationController,
        curve: Curves.easeInOut,
      ),
    );

    final appState = Provider.of<AppState>(context, listen: false);
    appState.socket?.on('new_notification', (data) {
      setState(() {
        _notifications.insert(0, {
          '_id': data['notificationId'] ?? '',
          'message': data['message'] ?? 'إشعار جديد',
          'date': DateTime.now().toString(),
          'read': false,
        });
      });
    });
  }

  void _startAutoScroll() {
    _timer = Timer.periodic(const Duration(seconds: 3), (timer) {
      if (_ads.isEmpty) return;
      _currentPage = (_currentPage + 1) % _ads.length;
      _pageController.animateToPage(
        _currentPage,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeIn,
      );
    });
  }

  Future<void> _fetchData() async {
    setState(() => _isLoading = true);
    final appState = Provider.of<AppState>(context, listen: false);
    try {
      final results = await Future.wait([
        _clubService.fetchClubs(appState.token ?? ''),
        _clubService.fetchAds(appState.token ?? ''),
        _clubService.fetchNotifications(appState.token ?? ''),
      ]);
      setState(() {
        _clubs = results[0];
        _ads = List<Map<String, String>>.from(results[1]);
        _notifications = results[2];
        _isLoading = false;
      });
    } catch (e) {
      _showSnackBar('خطأ في جلب البيانات: $e', Colors.red);
      setState(() => _isLoading = false);
    }
  }

  void _onSearch() {
    if (_debounce?.isActive ?? false) _debounce!.cancel();
    _debounce = Timer(const Duration(milliseconds: 500), () {
      setState(() {
        _searchAnimationController.forward().then((_) =>
            _searchAnimationController.reverse());
      });
    });
  }

  @override
  void dispose() {
    _timer?.cancel();
    _pageController.dispose();
    _searchController.dispose();
    _debounce?.cancel();
    _searchAnimationController.dispose();
    final appState = Provider.of<AppState>(context, listen: false);
    appState.socket?.off('new_notification');
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final filteredClubs = _clubs
        .where((club) => club['name']
        .toString()
        .toLowerCase()
        .contains(_searchController.text.toLowerCase()))
        .toList();

    return Scaffold(
      appBar: AppBar(
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: isDarkMode
                  ? [Colors.blue[900]!, Colors.blue[700]!]
                  : [Colors.blue, Colors.blueAccent],
            ),
          ),
        ),
        title: ScaleTransition(
          scale: _searchAnimation,
          child: TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'ابحث عن نادٍ...',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(20),
                borderSide: BorderSide.none,
              ),
              filled: true,
              fillColor: isDarkMode ? Colors.grey[800] : Colors.white.withOpacity(0.9),
              hintStyle: TextStyle(color: isDarkMode ? Colors.grey[400] : Colors.grey),
              prefixIcon: const Icon(Icons.search, color: Colors.grey),
              suffixIcon: _searchController.text.isNotEmpty
                  ? IconButton(
                icon: const Icon(Icons.clear, color: Colors.grey),
                onPressed: () {
                  _searchController.clear();
                  setState(() {});
                },
              )
                  : null,
            ),
            style: TextStyle(color: isDarkMode ? Colors.white : Colors.black87),
            textDirection: TextDirection.rtl,
          ),
        ),
        actions: [
          Stack(
            children: [
              IconButton(
                icon: const Icon(Icons.notifications),
                onPressed: () => _showNotifications(context),
              ),
              if (_notifications.isNotEmpty)
                Positioned(
                  right: 8,
                  top: 8,
                  child: ElasticIn(
                    child: Container(
                      padding: const EdgeInsets.all(2),
                      decoration: const BoxDecoration(
                          color: Colors.red, shape: BoxShape.circle),
                      constraints:
                      const BoxConstraints(minWidth: 16, minHeight: 16),
                      child: Text(
                        '${_notifications.length}',
                        style:
                        const TextStyle(color: Colors.white, fontSize: 10),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                )
            ],
          ),
        ],
      ),
      floatingActionButton: Consumer<AppState>(
        builder: (context, appState, _) {
          final isHokama = appState.userRole == 'hokama' ||
              (appState.currentUser?.role.name?.toLowerCase() == 'hokama');
          return isHokama
              ? FloatingActionButton(
                  onPressed: () => _showAddClubDialog(context),
                  backgroundColor: Colors.green,
                  tooltip: 'إضافة نادي جديد',
                  child: const Icon(Icons.add),
                )
              : const SizedBox.shrink();
        },
      ),
      body: _isLoading
          ? _buildSkeletonLoader()
          : RefreshIndicator(
        onRefresh: _fetchData,
        color: Colors.blue,
        child: CustomScrollView(
          slivers: [
            if (_ads.isNotEmpty)
              SliverToBoxAdapter(
                child: FadeInDown(
                  child: SizedBox(
                    height: 120,
                    child: Stack(
                      children: [
                        PageView.builder(
                          controller: _pageController,
                          itemCount: _ads.length,
                          onPageChanged: (index) =>
                              setState(() => _currentPage = index),
                          itemBuilder: (context, index) => GestureDetector(
                            onTap: () => _showSnackBar(
                                'إعلان: ${_ads[index]['text']}'),
                            child: Container(
                              margin: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(12),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.2),
                                    blurRadius: 6,
                                    offset: const Offset(0, 3),
                                  ),
                                ],
                              ),
                              child: ClipRRect(
                                borderRadius: BorderRadius.circular(12),
                                child: Stack(
                                  children: [
                                    CachedNetworkImage(
                                      imageUrl: _ads[index]['image']!,
                                      fit: BoxFit.cover,
                                      width: double.infinity,
                                      height: 120,
                                      placeholder: (context, url) =>
                                          Container(
                                            color: isDarkMode
                                                ? Colors.grey[800]
                                                : Colors.grey[300],
                                          ),
                                      errorWidget: (context, url, error) =>
                                      const Icon(Icons.error,
                                          color: Colors.grey),
                                    ),
                                    Positioned(
                                      bottom: 0,
                                      left: 0,
                                      right: 0,
                                      child: Container(
                                        padding: const EdgeInsets.all(8),
                                        color: Colors.black.withOpacity(0.6),
                                        child: Text(
                                          _ads[index]['text']!,
                                          style: const TextStyle(
                                            color: Colors.white,
                                            fontWeight: FontWeight.bold,
                                            fontSize: 14,
                                          ),
                                          textAlign: TextAlign.center,
                                          textDirection: TextDirection.rtl,
                                          maxLines: 2,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                        Positioned(
                          bottom: 8,
                          left: 0,
                          right: 0,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: List.generate(
                              _ads.length,
                                  (index) => Container(
                                margin: const EdgeInsets.symmetric(
                                    horizontal: 4),
                                width: 8,
                                height: 8,
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  color: _currentPage == index
                                      ? Colors.blueAccent
                                      : Colors.grey.withOpacity(0.5),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            SliverList(
              delegate: SliverChildBuilderDelegate(
                    (context, index) => FadeInUp(
                  child: ClubCard(
                    clubName: filteredClubs[index]['name'],
                    clubLogo:
                    '$_clubService.baseUrl${filteredClubs[index]['logo']}',
                    onTap: () => Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => ClubScreen(
                            clubName: filteredClubs[index]['name']),
                      ),
                    ),
                  ),
                ),
                childCount: filteredClubs.length,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showAddClubDialog(BuildContext context) {
    final TextEditingController _clubNameController = TextEditingController();
    final TextEditingController _clubLogoController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إضافة نادي جديد'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: _clubNameController,
              decoration: const InputDecoration(labelText: 'اسم النادي'),
            ),
            TextField(
              controller: _clubLogoController,
              decoration: const InputDecoration(labelText: 'رابط الشعار'),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              // هنا يمكنك إضافة منطق لحفظ النادي الجديد
              final clubName = _clubNameController.text;
              final clubLogo = _clubLogoController.text;
              // إضافة النادي إلى القائمة
              setState(() {
                _clubs.add({
                  'name': clubName,
                  'logo': clubLogo,
                });
              });
              Navigator.of(context).pop();
            },
            child: const Text('إضافة'),
          ),
        ],
      ),
    );
  }

  Widget _buildSkeletonLoader() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return CustomScrollView(
      slivers: [
        SliverToBoxAdapter(
          child: Container(
            height: 120,
            margin: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: isDarkMode ? Colors.grey[800] : Colors.grey[300],
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        ),
        SliverList(
          delegate: SliverChildBuilderDelegate(
                (context, index) => Container(
              height: 150,
              margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
              decoration: BoxDecoration(
                color: isDarkMode ? Colors.grey[800] : Colors.grey[300],
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            childCount: 5,
          ),
        ),
      ],
    );
  }

  void _showNotifications(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.5,
        maxChildSize: 0.9,
        builder: (context, scrollController) => Container(
          decoration: BoxDecoration(
            color: isDarkMode ? Colors.grey[900] : Colors.white,
            borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: Column(
            children: [
              Container(
                margin: const EdgeInsets.only(top: 8, bottom: 16),
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey[400],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              const Text(
                'الإشعارات',
                style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              ),
              Expanded(
                child: _notifications.isEmpty
                    ? Center(
                  child: Text(
                    'لا توجد إشعارات',
                    style: TextStyle(
                        color: isDarkMode
                            ? Colors.grey[400]
                            : Colors.grey[600]),
                  ),
                )
                    : ListView.builder(
                  controller: scrollController,
                  itemCount: _notifications.length,
                  itemBuilder: (context, index) {
                    final notification = _notifications[index];
                    return Dismissible(
                      key: Key(notification['_id']),
                      onDismissed: (direction) async {
                        final appState = Provider.of<AppState>(context,
                            listen: false);
                        try {
                          await _clubService.markNotificationRead(
                              appState.token ?? '', notification['_id']);
                          setState(() {
                            _notifications.removeAt(index);
                          });
                        } catch (e) {
                          _showSnackBar('خطأ في تحديث الإشعار', Colors.red);
                        }
                      },
                      background: Container(
                        color: Colors.red,
                        alignment: Alignment.centerRight,
                        padding: const EdgeInsets.only(right: 16),
                        child: const Icon(Icons.delete, color: Colors.white),
                      ),
                      child: FadeIn(
                        child: ListTile(
                          leading: ElasticIn(
                            child: const Icon(
                              Icons.notifications_active,
                              color: Colors.blue,
                              size: 30,
                            ),
                          ),
                          title: Text(
                            notification['message'] ?? 'إشعار',
                            style: TextStyle(
                              fontWeight: !(notification['read'] ?? false)
                                  ? FontWeight.bold
                                  : FontWeight.normal,
                              color: isDarkMode
                                  ? Colors.white
                                  : Colors.black87,
                            ),
                          ),
                          subtitle: Text(
                            timeago.format(
                                DateTime.parse(notification['date'] ??
                                    DateTime.now().toString()),
                                locale: 'ar'),
                            style: TextStyle(
                                color: isDarkMode
                                    ? Colors.grey[400]
                                    : Colors.grey[600]),
                          ),
                          trailing: notification['read']
                              ? null
                              : ElasticIn(
                            child: const Icon(Icons.circle,
                                color: Colors.blue, size: 10),
                          ),
                          onTap: () async {
                            if (!notification['read']) {
                              final appState = Provider.of<AppState>(
                                  context,
                                  listen: false);
                              try {
                                await _clubService.markNotificationRead(
                                    appState.token ?? '',
                                    notification['_id']);
                                setState(() {
                                  notification['read'] = true;
                                });
                              } catch (e) {
                                _showSnackBar('خطأ: $e', Colors.red);
                              }
                            }
                          },
                        ),
                      ),
                    );
                  },
                ),
              ),
              FadeInUp(
                child: ElevatedButton(
                  onPressed: () {
                    setState(() => _notifications.clear());
                    Navigator.pop(context);
                    _showSnackBar('تم مسح جميع الإشعارات');
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10)),
                    minimumSize: const Size(double.infinity, 50),
                  ),
                  child: const Text(
                    'مسح الكل',
                    style: TextStyle(color: Colors.white),
                  ),
                ),
              ),
              const SizedBox(height: 16),
            ],
          ),
        ),
      ),
    );
  }

  void _showSnackBar(String message, [Color? backgroundColor]) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: backgroundColor ?? Colors.blue,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        elevation: 6,
      ),
    );
  }
}

class ClubCard extends StatelessWidget {
  final String clubName;
  final String clubLogo;
  final VoidCallback onTap;

  const ClubCard(
      {super.key,
        required this.clubName,
        required this.clubLogo,
        required this.onTap});

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return GestureDetector(
      onTap: onTap,
      child: FadeInRight(
        child: Container(
          height: 150,
          margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.blue.withOpacity(0.2),
                blurRadius: 6,
                offset: const Offset(0, 3),
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(12),
            child: Stack(
              fit: StackFit.expand,
              children: [
                CachedNetworkImage(
                  imageUrl: clubLogo,
                  fit: BoxFit.cover,
                  placeholder: (context, url) => Container(
                    color: isDarkMode ? Colors.grey[800] : Colors.grey[300],
                  ),
                  errorWidget: (context, url, error) =>
                  const Icon(Icons.error, color: Colors.grey),
                ),
                Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Colors.black.withOpacity(0.5),
                        Colors.transparent,
                      ],
                      begin: Alignment.bottomCenter,
                      end: Alignment.topCenter,
                    ),
                  ),
                ),
                Center(
                  child: Text(
                    clubName,
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                      shadows: [
                        Shadow(
                          color: Colors.black.withOpacity(0.5),
                          offset: const Offset(1, 1),
                          blurRadius: 3,
                        ),
                      ],
                    ),
                    textDirection: TextDirection.rtl,
                    textAlign: TextAlign.center,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class ClubScreen extends StatefulWidget {
  final String clubName;

  const ClubScreen({super.key, required this.clubName});

  @override
  _ClubScreenState createState() => _ClubScreenState();
}

class _ClubScreenState extends State<ClubScreen>
    with SingleTickerProviderStateMixin {
  final ClubService _clubService = ClubService();
  final TextEditingController _postController = TextEditingController();
  final TextEditingController _liveChatController = TextEditingController();
  List<Map<String, dynamic>> _memberPosts = [];
  final List<Map<String, dynamic>> _liveChatMessages = [];
  List<Map<String, dynamic>> _matches = [];
  bool _isLiveStreamActive = false;
  String? _liveStreamUrl;
  late io.Socket _socket;
  bool _isLoading = true;
  late TabController _tabController;
  File? _postImage;
  int _postPage = 1;
  bool _hasMorePosts = true;
  late AnimationController _liveStreamAnimationController;
  late Animation<double> _liveStreamAnimation;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _liveStreamAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    _liveStreamAnimation = Tween<double>(begin: 1.0, end: 1.2).animate(
      CurvedAnimation(
        parent: _liveStreamAnimationController,
        curve: Curves.elasticIn,
      ),
    );
    _fetchData();
    _connectSocket();
  }

  void _connectSocket() {
    final appState = Provider.of<AppState>(context, listen: false);
    _socket = io.io(
      _clubService.baseUrl,
      io.OptionBuilder()
          .setTransports(['websocket'])
          .setExtraHeaders({'x-auth-token': appState.token ?? ''})
          .build(),
    );
    _socket.onConnect((_) => _socket.emit('join_club', widget.clubName));
    _socket.on('receive_message', (data) {
      setState(() {
        _liveChatMessages.insert(0, {
          'username': data['username'] ?? 'مستخدم',
          'message': data['message'] ?? '',
          'timestamp': data['timestamp'] ?? DateTime.now().toString(),
        });
      });
    });
    _socket.on('new_post', (_) => _fetchPosts(reset: true));
    _socket.onDisconnect((_) => _showSnackBar('تم قطع الاتصال', Colors.red));
    _socket.onConnectError((_) => _showSnackBar('خطأ في الاتصال', Colors.red));
  }

  Future<void> _fetchData() async {
    setState(() => _isLoading = true);
    final appState = Provider.of<AppState>(context, listen: false);
    try {
      final results = await Future.wait([
        _clubService.fetchPosts(appState.token ?? '', widget.clubName),
        _clubService.fetchMatches(appState.token ?? '', widget.clubName),
        _clubService
            .fetchLiveStreamUrl(appState.token ?? '', widget.clubName)
            .then((url) => _liveStreamUrl = url),
      ]);
      setState(() {
        _memberPosts = List<Map<String, dynamic>>.from(results[0] as List);
        _matches = List<Map<String, dynamic>>.from(results[1] as List);
        _isLoading = false;
      });
    } catch (e) {
      _showSnackBar('خطأ في جلب البيانات: $e', Colors.red);
      setState(() => _isLoading = false);
    }
  }

  Future<void> _fetchPosts({bool reset = false}) async {
    if (!_hasMorePosts && !reset) return;
    final appState = Provider.of<AppState>(context, listen: false);
    try {
      if (reset) {
        _postPage = 1;
        _memberPosts.clear();
        _hasMorePosts = true;
      }
      final posts = await _clubService
          .fetchPosts(appState.token ?? '', widget.clubName, page: _postPage);
      setState(() {
        _memberPosts.addAll(posts);
        _postPage++;
        if (posts.length < 10) _hasMorePosts = false;
      });
    } catch (e) {
      _showSnackBar('خطأ في جلب المنشورات: $e', Colors.red);
    }
  }

  Future<void> _addPost() async {
    if (_postController.text.isEmpty && _postImage == null) {
      _showSnackBar('يرجى إضافة محتوى أو صورة', Colors.red);
      return;
    }
    final appState = Provider.of<AppState>(context, listen: false);
    try {
      await _clubService.addPost(appState.token ?? '', widget.clubName,
          _postController.text, _postImage);
      _postController.clear();
      setState(() => _postImage = null);
      await _fetchPosts(reset: true);
      _socket.emit('new_post', {'clubName': widget.clubName});
      _showSnackBar('تم إضافة المنشور', Colors.green);
    } catch (e) {
      _showSnackBar('خطأ: $e', Colors.red);
    }
  }

  Future<void> _toggleLike(String postId) async {
    final appState = Provider.of<AppState>(context, listen: false);
    try {
      await _clubService.toggleLike(appState.token ?? '', postId);
      await _fetchPosts();
    } catch (e) {
      _showSnackBar('خطأ: $e', Colors.red);
    }
  }

  Future<void> _addComment(String postId, String comment) async {
    final appState = Provider.of<AppState>(context, listen: false);
    try {
      await _clubService.addComment(appState.token ?? '', postId, comment);
      await _fetchPosts();
    } catch (e) {
      _showSnackBar('خطأ: $e', Colors.red);
    }
  }

  void _addLiveChatMessage() {
    if (_liveChatController.text.isNotEmpty && _isLiveStreamActive) {
      final appState = Provider.of<AppState>(context, listen: false);
      _socket.emit('send_message', {
        'clubName': widget.clubName,
        'username': appState.username ?? 'مستخدم',
        'message': _liveChatController.text,
        'timestamp': DateTime.now().toString(),
      });
      _liveChatController.clear();
    }
  }

  void _toggleLiveStream() {
    setState(() {
      _isLiveStreamActive = !_isLiveStreamActive;
      if (!_isLiveStreamActive) _liveChatMessages.clear();
      _liveStreamAnimationController.forward().then((_) =>
          _liveStreamAnimationController.reverse());
      _showSnackBar(
          _isLiveStreamActive ? 'البث بدأ!' : 'البث انتهى!',
          _isLiveStreamActive ? Colors.green : Colors.red);
    });
  }

  @override
  void dispose() {
    _socket.disconnect();
    _postController.dispose();
    _liveChatController.dispose();
    _tabController.dispose();
    _liveStreamAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return Scaffold(
      appBar: AppBar(
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: isDarkMode
                  ? [Colors.blue[900]!, Colors.blue[700]!]
                  : [Colors.blue, Colors.blueAccent],
            ),
          ),
        ),
        title: Text(widget.clubName, textDirection: TextDirection.rtl),
        actions: [
          ScaleTransition(
            scale: _liveStreamAnimation,
            child: IconButton(
              icon:
              Icon(_isLiveStreamActive ? Icons.videocam : Icons.videocam_off),
              onPressed: _toggleLiveStream,
            ),
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(text: "المباريات"),
            Tab(text: "البث المباشر"),
            Tab(text: "المنشورات"),
          ],
        ),
      ),
      body: _isLoading
          ? _buildSkeletonLoader()
          : TabBarView(
        controller: _tabController,
        children: [
          _buildMatchesTab(),
          _buildLiveStreamTab(),
          _buildPostsTab(),
        ],
      ),
      floatingActionButton: ElasticIn(
        child: FloatingActionButton(
          onPressed: () => _tabController.animateTo(2),
          backgroundColor: Colors.blue,
          child: const Icon(Icons.post_add),
        ),
      ),
    );
  }

  Widget _buildSkeletonLoader() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return CustomScrollView(
      slivers: [
        SliverToBoxAdapter(
          child: Container(
            height: 200,
            color: isDarkMode ? Colors.grey[800] : Colors.grey[300],
          ),
        ),
        SliverList(
          delegate: SliverChildBuilderDelegate(
                (context, index) => Container(
              height: 150,
              margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
              decoration: BoxDecoration(
                color: isDarkMode ? Colors.grey[800] : Colors.grey[300],
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            childCount: 3,
          ),
        ),
      ],
    );
  }

  Widget _buildMatchesTab() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return RefreshIndicator(
      onRefresh: _fetchData,
      color: Colors.blue,
      child: CustomScrollView(
        slivers: [
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: FadeInUp(
                child: Text(
                  'جدول المباريات',
                  style: TextStyle(
                    fontSize: 22,
                    fontWeight: FontWeight.bold,
                    color: isDarkMode ? Colors.white : Colors.black87,
                  ),
                ),
              ),
            ),
          ),
          SliverToBoxAdapter(
            child: SizedBox(
              height: 100,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: _matches.length,
                itemBuilder: (context, index) => FadeInRight(
                  child: MatchCard(
                    date: _matches[index]['date'] ?? 'غير محدد',
                    opponent: _matches[index]['opponent'] ?? 'غير معروف',
                  ),
                ),
              ),
            ),
          ),
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: FadeInUp(
                child: Text(
                  'المباريات السابقة',
                  style: TextStyle(
                    fontSize: 22,
                    fontWeight: FontWeight.bold,
                    color: isDarkMode ? Colors.white : Colors.black87,
                  ),
                ),
              ),
            ),
          ),
          SliverToBoxAdapter(
            child: FadeIn(
              child: SizedBox(
                height: 200,
                width: double.infinity,
                child: YoutubePlayer(
                  controller: YoutubePlayerController(
                    initialVideoId: 'dQw4w9WgXcQ',
                    flags: const YoutubePlayerFlags(autoPlay: false),
                  ),
                  showVideoProgressIndicator: true,
                  progressIndicatorColor: Colors.blueAccent,
                  thumbnail: CachedNetworkImage(
                    imageUrl: 'https://img.youtube.com/vi/dQw4w9WgXcQ/0.jpg',
                    fit: BoxFit.cover,
                    placeholder: (context, url) => Container(
                      height: 200,
                      color: isDarkMode ? Colors.grey[800] : Colors.grey[300],
                    ),
                    errorWidget: (context, url, error) =>
                    const Icon(Icons.error, color: Colors.grey),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLiveStreamTab() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return CustomScrollView(
      slivers: [
        SliverToBoxAdapter(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: FadeInUp(
              child: Text(
                'البث المباشر',
                style: TextStyle(
                  fontSize: 22,
                  fontWeight: FontWeight.bold,
                  color: isDarkMode ? Colors.white : Colors.black87,
                ),
              ),
            ),
          ),
        ),
        SliverToBoxAdapter(
          child: SizedBox(
            height: 200,
            width: double.infinity,
            child: Stack(
              children: [
                FadeIn(
                  child: YoutubePlayer(
                    controller: YoutubePlayerController(
                      initialVideoId:
                      _isLiveStreamActive && _liveStreamUrl != null
                          ? YoutubePlayer.convertUrlToId(_liveStreamUrl!) ??
                          'dQw4w9WgXcQ'
                          : 'dQw4w9WgXcQ',
                      flags: YoutubePlayerFlags(autoPlay: _isLiveStreamActive),
                    ),
                    showVideoProgressIndicator: true,
                    progressIndicatorColor: Colors.blueAccent,
                    thumbnail: CachedNetworkImage(
                      imageUrl: _isLiveStreamActive && _liveStreamUrl != null
                          ? 'https://img.youtube.com/vi/${YoutubePlayer.convertUrlToId(_liveStreamUrl!) ?? 'dQw4w9WgXcQ'}/0.jpg'
                          : 'https://img.youtube.com/vi/dQw4w9WgXcQ/0.jpg',
                      fit: BoxFit.cover,
                      placeholder: (context, url) => Container(
                        height: 200,
                        color: isDarkMode ? Colors.grey[800] : Colors.grey[300],
                      ),
                      errorWidget: (context, url, error) =>
                      const Icon(Icons.error, color: Colors.grey),
                    ),
                  ),
                ),
                if (_isLiveStreamActive)
                  Positioned(
                    bottom: 0,
                    left: 0,
                    right: 0,
                    child: Container(
                      height: 150,
                      decoration: BoxDecoration(
                        color: Colors.black.withOpacity(0.7),
                        borderRadius:
                        const BorderRadius.vertical(top: Radius.circular(12)),
                      ),
                      child: Column(
                        children: [
                          Expanded(
                            child: ListView.builder(
                              reverse: true,
                              itemCount: _liveChatMessages.length,
                              itemBuilder: (context, index) => FadeIn(
                                child: Padding(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 8, vertical: 4),
                                  child: Text(
                                    '${_liveChatMessages[index]['username']}: ${_liveChatMessages[index]['message']}',
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 14,
                                      shadows: [
                                        Shadow(
                                          color: Colors.black.withOpacity(0.5),
                                          offset: const Offset(1, 1),
                                          blurRadius: 2,
                                        ),
                                      ],
                                    ),
                                    textDirection: TextDirection.rtl,
                                  ),
                                ),
                              ),
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsets.all(8),
                            child: Row(
                              children: [
                                Expanded(
                                  child: TextField(
                                    controller: _liveChatController,
                                    decoration: InputDecoration(
                                      hintText: 'اكتب تعليقًا...',
                                      hintStyle: TextStyle(
                                          color: Colors.white.withOpacity(0.7)),
                                      filled: true,
                                      fillColor: Colors.black.withOpacity(0.3),
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(20),
                                        borderSide: BorderSide.none,
                                      ),
                                    ),
                                    style: const TextStyle(color: Colors.white),
                                    textDirection: TextDirection.rtl,
                                  ),
                                ),
                                ElasticIn(
                                  child: IconButton(
                                    icon: const Icon(Icons.send,
                                        color: Colors.white),
                                    onPressed: _addLiveChatMessage,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPostsTab() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return RefreshIndicator(
      onRefresh: () => _fetchPosts(reset: true),
      color: Colors.blue,
      child: NotificationListener<ScrollNotification>(
        onNotification: (scrollInfo) {
          if (_hasMorePosts &&
              scrollInfo.metrics.pixels == scrollInfo.metrics.maxScrollExtent) {
            _fetchPosts();
          }
          return false;
        },
        child: CustomScrollView(
          slivers: [
            SliverToBoxAdapter(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: FadeInUp(
                  child: Text(
                    'إضافة منشور',
                    style: TextStyle(
                      fontSize: 22,
                      fontWeight: FontWeight.bold,
                      color: isDarkMode ? Colors.white : Colors.black87,
                    ),
                  ),
                ),
              ),
            ),
            SliverToBoxAdapter(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: FadeIn(
                  child: Column(
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: TextField(
                              controller: _postController,
                              decoration: InputDecoration(
                                hintText: 'شارك رأيك...',
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                filled: true,
                                fillColor: isDarkMode
                                    ? Colors.grey[800]
                                    : Colors.grey[100],
                              ),
                              style: TextStyle(
                                  color: isDarkMode
                                      ? Colors.white
                                      : Colors.black87),
                              textDirection: TextDirection.rtl,
                              maxLines: 3,
                            ),
                          ),
                          const SizedBox(width: 8),
                          ElasticIn(
                            child: IconButton(
                              icon: const Icon(Icons.image, color: Colors.blue),
                              onPressed: () async {
                                final result = await FilePicker.platform
                                    .pickFiles(type: FileType.image);
                                if (result != null) {
                                  setState(() => _postImage =
                                      File(result.files.single.path!));
                                }
                              },
                            ),
                          ),
                        ],
                      ),
                      if (_postImage != null)
                        Padding(
                          padding: const EdgeInsets.symmetric(vertical: 8),
                          child: FadeIn(
                            child: Stack(
                              children: [
                                Image.file(
                                  _postImage!,
                                  height: 100,
                                  width: 100,
                                  fit: BoxFit.cover,
                                ),
                                Positioned(
                                  top: 0,
                                  right: 0,
                                  child: ElasticIn(
                                    child: IconButton(
                                      icon: const Icon(Icons.close,
                                          color: Colors.red),
                                      onPressed: () =>
                                          setState(() => _postImage = null),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      const SizedBox(height: 8),
                      ElevatedButton(
                        onPressed: _addPost,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue,
                          shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(10)),
                          minimumSize: const Size(double.infinity, 50),
                        ),
                        child: const Text(
                          'نشر',
                          style: TextStyle(color: Colors.white),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            SliverToBoxAdapter(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: FadeInUp(
                  child: Text(
                    'منشورات الأعضاء',
                    style: TextStyle(
                      fontSize: 22,
                      fontWeight: FontWeight.bold,
                      color: isDarkMode ? Colors.white : Colors.black87,
                    ),
                  ),
                ),
              ),
            ),
            SliverList(
              delegate: SliverChildBuilderDelegate(
                    (context, index) => FadeInUp(
                  child: MemberPostWidget(
                    post: _memberPosts[index],
                    onLike: () => _toggleLike(_memberPosts[index]['_id']),
                    onComment: (comment) =>
                        _addComment(_memberPosts[index]['_id'], comment),
                  ),
                ),
                childCount: _memberPosts.length,
              ),
            ),
            if (_hasMorePosts)
              const SliverToBoxAdapter(
                child: Padding(
                  padding: EdgeInsets.all(16),
                  child: Center(
                    child: CircularProgressIndicator(color: Colors.blue),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  void _showSnackBar(String message, [Color? backgroundColor]) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: backgroundColor ?? Colors.blue,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        elevation: 6,
      ),
    );
  }
}

class MatchCard extends StatelessWidget {
  final String date;
  final String opponent;

  const MatchCard({super.key, required this.date, required this.opponent});

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 8),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      elevation: 4,
      color: isDarkMode ? Colors.grey[800] : Colors.white,
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              date,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: isDarkMode ? Colors.white : Colors.black87,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              opponent,
              style: TextStyle(
                color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
              ),
              textDirection: TextDirection.rtl,
            ),
          ],
        ),
      ),
    );
  }
}

class MemberPostWidget extends StatefulWidget {
  final Map<String, dynamic> post;
  final VoidCallback onLike;
  final Function(String) onComment;

  const MemberPostWidget(
      {super.key,
        required this.post,
        required this.onLike,
        required this.onComment});

  @override
  _MemberPostWidgetState createState() => _MemberPostWidgetState();
}

class _MemberPostWidgetState extends State<MemberPostWidget>
    with SingleTickerProviderStateMixin {
  final TextEditingController _commentController = TextEditingController();
  bool _showComments = false;
  late AnimationController _likeAnimationController;
  late Animation<double> _likeAnimation;

  @override
  void initState() {
    super.initState();
    _likeAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    _likeAnimation = Tween<double>(begin: 1.0, end: 1.3).animate(
      CurvedAnimation(
        parent: _likeAnimationController,
        curve: Curves.elasticIn,
      ),
    );
  }

  @override
  void dispose() {
    _commentController.dispose();
    _likeAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final appState = Provider.of<AppState>(context, listen: false);
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final isLiked =
        (widget.post['likedBy'] as List<dynamic>?)?.contains(appState.userId) ??
            false;

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      elevation: 4,
      color: isDarkMode ? Colors.grey[900] : Colors.white,
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  backgroundColor: Colors.blue,
                  child: Text(
                    widget.post['username']?[0] ?? 'U',
                    style: const TextStyle(color: Colors.white),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    widget.post['username'] ?? 'مستخدم',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: isDarkMode ? Colors.white : Colors.black87,
                    ),
                  ),
                ),
                Text(
                  timeago.format(
                      DateTime.parse(widget.post['timestamp'] ??
                          DateTime.now().toString()),
                      locale: 'ar'),
                  style: TextStyle(
                    color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
                    fontSize: 12,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              widget.post['content'] ?? '',
              textDirection: TextDirection.rtl,
              style: TextStyle(
                color: isDarkMode ? Colors.white : Colors.black87,
                fontSize: 16,
              ),
            ),
            if (widget.post['imageUrl'] != null &&
                widget.post['imageUrl'].isNotEmpty)
              Padding(
                padding: const EdgeInsets.only(top: 8),
                child: GestureDetector(
                  onTap: () => _showFullImage(context, widget.post['imageUrl']),
                  child: CachedNetworkImage(
                    imageUrl: widget.post['imageUrl'],
                    height: 150,
                    width: double.infinity,
                    fit: BoxFit.cover,
                    placeholder: (context, url) => Container(
                      height: 150,
                      color: isDarkMode ? Colors.grey[800] : Colors.grey[300],
                    ),
                    errorWidget: (context, url, error) =>
                    const Icon(Icons.error, color: Colors.grey),
                  ),
                ),
              ),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    ScaleTransition(
                      scale: _likeAnimation,
                      child: IconButton(
                        icon: Icon(
                          isLiked ? Icons.favorite : Icons.favorite_border,
                          color: isLiked ? Colors.red : Colors.grey,
                        ),
                        onPressed: () {
                          widget.onLike();
                          _likeAnimationController.forward().then((_) =>
                              _likeAnimationController.reverse());
                        },
                      ),
                    ),
                    Text(
                      '${widget.post['likes'] ?? 0}',
                      style: TextStyle(
                        color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
                      ),
                    ),
                  ],
                ),
                TextButton(
                  onPressed: () =>
                      setState(() => _showComments = !_showComments),
                  child: Text(
                    'تعليقات (${(widget.post['comments'] as List?)?.length ?? 0})',
                    style: TextStyle(
                      color: isDarkMode ? Colors.blue[300] : Colors.blue,
                    ),
                  ),
                ),
              ],
            ),
            if (_showComments) ...[
              const Divider(),
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: (widget.post['comments'] as List?)?.length ?? 0,
                itemBuilder: (context, index) {
                  final comment = (widget.post['comments'] as List)[index];
                  return FadeIn(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(vertical: 4),
                      child: Row(
                        children: [
                          CircleAvatar(
                            radius: 12,
                            backgroundColor: Colors.blue,
                            child: Text(
                              comment['username']?[0] ?? 'U',
                              style: const TextStyle(color: Colors.white),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              '${comment['username']}: ${comment['text']}',
                              style: TextStyle(
                                fontSize: 14,
                                color: isDarkMode
                                    ? Colors.white
                                    : Colors.black87,
                              ),
                              textDirection: TextDirection.rtl,
                            ),
                          ),
                          Text(
                            timeago.format(
                                DateTime.parse(comment['timestamp'] ??
                                    DateTime.now().toString()),
                                locale: 'ar'),
                            style: TextStyle(
                              color: isDarkMode
                                  ? Colors.grey[400]
                                  : Colors.grey[600],
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 8),
                child: Row(
                  children: [
                    Expanded(
                      child: TextField(
                        controller: _commentController,
                        decoration: InputDecoration(
                          hintText: 'أضف تعليقًا...',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          filled: true,
                          fillColor: isDarkMode
                              ? Colors.grey[800]
                              : Colors.grey[100],
                        ),
                        style: TextStyle(
                            color: isDarkMode ? Colors.white : Colors.black87),
                        textDirection: TextDirection.rtl,
                      ),
                    ),
                    ElasticIn(
                      child: IconButton(
                        icon: const Icon(Icons.send, color: Colors.blue),
                        onPressed: () {
                          if (_commentController.text.isNotEmpty) {
                            widget.onComment(_commentController.text);
                            _commentController.clear();
                          }
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  void _showFullImage(BuildContext context, String imageUrl) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        child: Container(
          padding: const EdgeInsets.all(16),
          constraints: const BoxConstraints(maxHeight: 400, maxWidth: 300),
          child: Stack(
            children: [
              CachedNetworkImage(
                imageUrl: imageUrl,
                fit: BoxFit.contain,
                placeholder: (context, url) => Container(
                  height: 400,
                  color: isDarkMode ? Colors.grey[800] : Colors.grey[300],
                ),
                errorWidget: (context, url, error) =>
                const Icon(Icons.error, color: Colors.grey),
              ),
              Positioned(
                top: 0,
                right: 0,
                child: ElasticIn(
                  child: IconButton(
                    icon: const Icon(Icons.close, color: Colors.white),
                    onPressed: () => Navigator.pop(context),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
