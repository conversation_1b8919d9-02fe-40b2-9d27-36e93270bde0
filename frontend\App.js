import React from "react";
import { <PERSON><PERSON>er<PERSON>outer as Router, Routes, Route } from "react-router-dom";
import NotificationsPage from "./NotificationsPage";
import AdminPanel from "./AdminPanel";
import FriendsPage from "./FriendsPage";
import MapPage from "./MapPage";
import SearchPage from "./SearchPage";
import HomePage from "./HomePage";

function App() {
  return (
    <Router>
      <Routes>
        <Route path="/" element={<HomePage />} />
        <Route path="/notifications" element={<NotificationsPage />} />
        <Route path="/admin" element={<AdminPanel />} />
        <Route path="/friends" element={<FriendsPage />} />
        <Route path="/map" element={<MapPage />} />
        <Route path="/search" element={<SearchPage />} />
      </Routes>
    </Router>
  );
}

export default App;
