import '../models/disease_model.dart';
import '../services/api_service.dart';
import 'base_repository.dart';

/// مستودع الأمراض
class DiseaseRepository implements BaseRepository<Disease> {
  static final DiseaseRepository _instance = DiseaseRepository._internal();
  factory DiseaseRepository() => _instance;
  DiseaseRepository._internal();

  final ApiService _apiService = ApiService();

  // تخزين مؤقت للأمراض حسب النوع
  final Map<String, List<Disease>> _cachedDiseases = {};
  final Map<String, DateTime> _lastCacheTimes = {};

  // مدة صلاحية التخزين المؤقت (30 دقيقة)
  final Duration _cacheDuration = const Duration(minutes: 30);

  // التحقق من صلاحية التخزين المؤقت لنوع معين
  bool _isCacheValid(String type) =>
      _cachedDiseases.containsKey(type) &&
          _lastCacheTimes.containsKey(type) &&
          DateTime.now().difference(_lastCacheTimes[type]!) < _cacheDuration;

  // الحصول على البيانات المحلية للأمراض حسب النوع
  List<Disease> _getLocalDiseases(String type) {
    final diseases = <Disease>[];
    switch (type) {
      case 'chronic':
        diseases.addAll([
          const Disease(
            id: '1',
            title: 'نزلات البرد',
            description: 'عدوى فيروسية شائعة تصيب الجهاز التنفسي العلوي.',
            image: 'assets/images/cold.png',
            symptoms: ['سيلان الأنف', 'عطس', 'احتقان', 'سعال'],
            causes: ['فيروسات'],
            tests: ['فحص سريري'],
            treatments: ['راحة', 'سوائل', 'خافض للحرارة'],
            suitableFoods: ['شوربة الدجاج', 'الأعشاب الدافئة'],
            unsuitableFoods: ['منتجات الألبان', 'الأطعمة المقلية'],
            isLocal: true, // Mark as local
          ),
          const Disease(
            id: '2',
            title: 'الإنفلونزا',
            description: 'مرض فيروسي موسمي يسبب الحمى والسعال وآلام الجسم.',
            image: 'assets/images/flu.png',
            symptoms: ['حمى', 'سعال', 'آلام عضلية', 'تعب'],
            causes: ['فيروس الإنفلونزا'],
            tests: ['اختبار الإنفلونزا السريع'],
            treatments: ['مضادات الفيروسات', 'راحة'],
            suitableFoods: ['شوربة الدجاج', 'الأعشاب الدافئة'],
            unsuitableFoods: ['منتجات الألبان', 'الأطعمة المقلية'],
            isLocal: true,
          ),
        ]);
        break;
      case 'immune':
        diseases.addAll([
          const Disease(
            id: '3',
            title: 'السكري',
            description: 'مرض مزمن يؤثر على كيفية استخدام الجسم للسكر.',
            image: 'assets/images/diabetes.png',
            symptoms: ['العطش الشديد', 'التعب', 'فقدان الوزن غير المبرر'],
            causes: ['وراثي', 'نمط حياة غير صحي'],
            tests: ['اختبار سكر الدم'],
            treatments: ['الأنسولين', 'تغييرات في النظام الغذائي'],
            suitableFoods: ['الخضروات', 'الفواكه'],
            unsuitableFoods: ['السكر', 'الأطعمة الغنية بالدهون'],
            isLocal: true,
          ),
          const Disease(
            id: '4',
            title: 'ارتفاع ضغط الدم',
            description: 'حالة طبية تؤدي إلى ضغط دم مرتفع باستمرار.',
            image: 'assets/images/hypertension.png',
            symptoms: ['صداع', 'دوخة', 'نزيف الأنف'],
            causes: ['وراثي', 'نمط حياة غير صحي'],
            tests: ['قياس ضغط الدم'],
            treatments: ['الأدوية', 'تغييرات في النظام الغذائي'],
            suitableFoods: ['الفواكه', 'الخضروات'],
            unsuitableFoods: ['الملح', 'الأطعمة المصنعة'],
            isLocal: true,
          ),
        ]);
        break;
      case 'common':
      default:
        diseases.addAll([
          const Disease(
            id: '5',
            title: 'الربو',
            description: 'مرض تنفسي مزمن يؤثر على الشعب الهوائية.',
            image: 'assets/images/asthma.png',
            symptoms: ['صعوبة في التنفس', 'صفير عند التنفس'],
            causes: ['حساسية', 'تلوث الهواء'],
            tests: ['اختبار وظائف الرئة'],
            treatments: ['الأدوية', 'تجنب المحفزات'],
            suitableFoods: ['الأطعمة الغنية بالأوميغا 3'],
            unsuitableFoods: ['الأطعمة المقلية', 'الأطعمة الحارة'],
            isLocal: true,
          ),
          const Disease(
            id: '6',
            title: 'الحساسية',
            description: 'استجابة مفرطة للجهاز المناعي تجاه مادة معينة.',
            image: 'assets/images/allergy.png',
            symptoms: ['طفح جلدي', 'حكة', 'صعوبة في التنفس'],
            causes: ['حبوب اللقاح', 'الغبار'],
            tests: ['اختبار الحساسية'],
            treatments: ['الأدوية المضادة للهستامين'],
            suitableFoods: ['الأطعمة الغنية بالفيتامينات'],
            unsuitableFoods: ['الأطعمة المسببة للحساسية'],
            isLocal: true,
          ),
        ]);
    }
    return diseases;
  }

  @override
  Future<ApiResponse<Disease>> getById(String id) async {
    for (final type in _cachedDiseases.keys) {
      if (_isCacheValid(type)) {
        final disease = _cachedDiseases[type]!.firstWhere(
              (disease) => disease.id == id,
          orElse: () => const Disease(
            id: '',
            title: '',
            image: '',
            symptoms: [],
            causes: [],
            tests: [],
            treatments: [],
            suitableFoods: [],
            unsuitableFoods: [],
            description: '',
          ),
        );
        if (disease.id.isNotEmpty) {
          return ApiResponse<Disease>.success(disease);
        }
      }
    }

    final response = await _apiService.get<Disease>(
      'diseases/$id',
      parser: (data) => Disease.fromJson(data),
    );

    if (!response.isSuccess) {
      final types = ['chronic', 'immune', 'common'];
      for (final type in types) {
        final localDiseases = _getLocalDiseases(type);
        final disease = localDiseases.firstWhere(
              (disease) => disease.id == id,
          orElse: () => const Disease(
            id: '',
            title: '',
            image: '',
            symptoms: [],
            causes: [],
            tests: [],
            treatments: [],
            suitableFoods: [],
            unsuitableFoods: [],
            description: '',
          ),
        );
        if (disease.id.isNotEmpty) {
          return ApiResponse<Disease>.success(disease);
        }
      }
      return response;
    }

    return response;
  }

  Future<ApiResponse<List<Disease>>> getDiseasesByType(String type) async {
    if (_isCacheValid(type)) {
      return ApiResponse<List<Disease>>.success(_cachedDiseases[type]!);
    }

    final response = await _apiService.get<List<Disease>>(
      'diseases?type=$type',
      parser: (data) => (data as List).map((item) => Disease.fromJson(item)).toList(),
    );

    if (response.isSuccess && response.data != null) {
      _cachedDiseases[type] = response.data!;
      _lastCacheTimes[type] = DateTime.now();
      return response;
    }

    final localDiseases = _getLocalDiseases(type);
    _cachedDiseases[type] = localDiseases;
    _lastCacheTimes[type] = DateTime.now();
    return ApiResponse<List<Disease>>.success(localDiseases);
  }

  @override
  Future<ApiResponse<List<Disease>>> getAll([Map<String, dynamic>? params]) async {
    final type = params?['type'] as String? ?? 'common';
    return getDiseasesByType(type);
  }

  @override
  Future<ApiResponse<Disease>> create(Map<String, dynamic> data) async {
    final response = await _apiService.post<Disease>(
      'diseases',
      body: data,
      parser: (data) => Disease.fromJson(data),
    );

    if (response.isSuccess && response.data != null) {
      clearCache();
    }

    return response;
  }

  @override
  Future<ApiResponse<Disease>> update(String id, Map<String, dynamic> data) async {
    final response = await _apiService.put<Disease>(
      'diseases/$id',
      body: data,
      parser: (data) => Disease.fromJson(data),
    );

    if (response.isSuccess && response.data != null) {
      clearCache();
    }

    return response;
  }

  @override
  Future<ApiResponse<bool>> delete(String id) async {
    final response = await _apiService.delete<bool>(
      'diseases/$id',
      parser: (data) => data['success'] ?? false,
    );

    if (response.isSuccess && response.data == true) {
      clearCache();
    }

    return response;
  }

  void clearCache() {
    _cachedDiseases.clear();
    _lastCacheTimes.clear();
  }
}