// نموذج الحظر بين المستخدمين
class BlockModel {
  final String id;
  final String blockerId; // من قام بالحظر
  final String blockedId; // من تم حظره
  final DateTime createdAt;

  BlockModel({
    required this.id,
    required this.blockerId,
    required this.blockedId,
    required this.createdAt,
  });

  factory BlockModel.fromJson(Map<String, dynamic> json) {
    return BlockModel(
      id: json['id'],
      blockerId: json['blockerId'],
      blockedId: json['blockedId'],
      createdAt: DateTime.parse(json['createdAt']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'blockerId': blockerId,
      'blockedId': blockedId,
      'createdAt': createdAt.toIso8601String(),
    };
  }
}
