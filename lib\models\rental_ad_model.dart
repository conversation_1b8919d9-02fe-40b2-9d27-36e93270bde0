// نموذج إعلان التأجير
class RentalAd {
  final String id;
  final String title;
  final String description;
  final String location;
  final String price;
  final String category;
  final String fileUrl;
  final DateTime date;
  final bool isFeatured;
  bool isFavorite; // خاصية مفضلة قابلة للتغيير

  RentalAd({
    required this.id,
    required this.title,
    required this.description,
    required this.location,
    required this.price,
    required this.category,
    required this.fileUrl,
    required this.date,
    required this.isFeatured,
    this.isFavorite = false,
  });

  factory RentalAd.fromJson(Map<String, dynamic> json) {
    return RentalAd(
      id: json['_id'] ?? '',
      title: json['title'] ?? 'بدون عنوان',
      description: json['description'] ?? 'بدون وصف',
      location: json['location'] ?? 'غير محدد',
      price: json['price'] ?? 'غير محدد',
      category: json['category'] ?? 'غير مصنف',
      fileUrl: json['fileUrl'] ?? '',
      date: DateTime.parse(json['date'] ?? DateTime.now().toString()),
      isFeatured: json['isFeatured'] ?? false,
      isFavorite: json['isFavorite'] ?? false,
    );
  }
}
