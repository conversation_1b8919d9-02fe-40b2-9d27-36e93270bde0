import 'dart:convert';
import 'package:http/http.dart' as http;

// API configuration
class ApiConfig {
  static const String baseUrl = 'http://localhost:3000/api';
}

class FishBreedingService {
  final String baseUrl = ApiConfig.baseUrl;

  // Get all fish breeding groups
  Future<List<dynamic>> getAllGroups() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/fish-breeding-groups'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception('Failed to load fish breeding groups: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error connecting to server: $e');
    }
  }

  // Get a single fish breeding group by ID
  Future<dynamic> getGroupById(String id) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/fish-breeding-groups/$id'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception('Failed to load fish breeding group: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error connecting to server: $e');
    }
  }

  // Create a new fish breeding group
  Future<dynamic> createGroup(Map<String, dynamic> data) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/fish-breeding-groups'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(data),
      );

      if (response.statusCode == 201) {
        return jsonDecode(response.body);
      } else {
        throw Exception('Failed to create fish breeding group: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error connecting to server: $e');
    }
  }

  // Update a fish breeding group
  Future<dynamic> updateGroup(String id, Map<String, dynamic> data) async {
    try {
      final response = await http.put(
        Uri.parse('$baseUrl/fish-breeding-groups/$id'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(data),
      );

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception('Failed to update fish breeding group: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error connecting to server: $e');
    }
  }

  // Delete a fish breeding group
  Future<void> deleteGroup(String id) async {
    try {
      final response = await http.delete(
        Uri.parse('$baseUrl/fish-breeding-groups/$id'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode != 200) {
        throw Exception('Failed to delete fish breeding group: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error connecting to server: $e');
    }
  }
}