const mongoose = require('mongoose');
const Profession = require('../models/profession.model');
const Message = require('../models/message.model');
const Contribution = require('../models/contribution.model');
const Challenge = require('../models/challenge.model');
const User = require('../models/user.model');
const Notification = require('../models/notification.model');
const PointsTransaction = require('../models/pointstransaction.model');

// Create new profession
exports.createProfession = async (req, res) => {
  try {
    const { title, description, category, image, averageSalary, demand, requiredSkills } = req.body;

    // Create new profession
    const profession = new Profession({
      title,
      description,
      category,
      image,
      averageSalary: averageSalary || 0,
      demand: demand || 'medium',
      requiredSkills: requiredSkills || [],
      createdBy: req.user.id
    });

    await profession.save();

    // Create notification for administrators
    const notification = new Notification({
      message: `تم إنشاء مهنة جديدة: ${title}`,
      type: 'profession_created',
      createdBy: req.user.id,
      referenceId: profession._id
    });
    await notification.save();

    res.status(201).json(profession);
  } catch (error) {
    console.error('Error creating profession:', error);
    res.status(500).json({ message: 'خطأ في إنشاء المهنة' });
  }
};

// Update profession
exports.updateProfession = async (req, res) => {
  try {
    const { title, description, category, image, averageSalary, demand, requiredSkills } = req.body;

    const profession = await Profession.findById(req.params.id);
    if (!profession) {
      return res.status(404).json({ message: 'المهنة غير موجودة' });
    }

    // Update fields
    profession.title = title || profession.title;
    profession.description = description || profession.description;
    profession.category = category || profession.category;
    profession.image = image || profession.image;
    profession.averageSalary = averageSalary || profession.averageSalary;
    profession.demand = demand || profession.demand;
    profession.requiredSkills = requiredSkills || profession.requiredSkills;
    profession.updatedAt = new Date();

    await profession.save();

    // Create notification
    const notification = new Notification({
      message: `تم تحديث مهنة: ${profession.title}`,
      type: 'profession_updated',
      createdBy: req.user.id,
      referenceId: profession._id
    });
    await notification.save();

    res.json(profession);
  } catch (error) {
    console.error('Error updating profession:', error);
    res.status(500).json({ message: 'خطأ في تحديث المهنة' });
  }
};

// Delete profession
exports.deleteProfession = async (req, res) => {
  try {
    const profession = await Profession.findById(req.params.id);
    if (!profession) {
      return res.status(404).json({ message: 'المهنة غير موجودة' });
    }

    // Delete related content
    await Message.deleteMany({ professionId: profession._id });
    await Contribution.deleteMany({ professionId: profession._id });

    // Delete the profession
    await Profession.deleteOne({ _id: profession._id });

    // Create notification
    const notification = new Notification({
      message: `تم حذف مهنة: ${profession.title}`,
      type: 'profession_deleted',
      createdBy: req.user.id,
      referenceId: profession._id
    });
    await notification.save();

    res.json({ message: 'تم حذف المهنة بنجاح' });
  } catch (error) {
    console.error('Error deleting profession:', error);
    res.status(500).json({ message: 'خطأ في حذف المهنة' });
  }
};

// Get all professions
exports.getProfessions = async (req, res) => {
  try {
    const professions = await Profession.find({}).sort({ title: 1 });
    res.json(professions);
  } catch (error) {
    console.error('Error fetching professions:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Get profession by ID
exports.getProfessionById = async (req, res) => {
  try {
    const profession = await Profession.findById(req.params.id);
    if (!profession) {
      return res.status(404).json({ message: 'Profession not found' });
    }
    res.json(profession);
  } catch (error) {
    console.error('Error fetching profession:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Get community messages for a profession
exports.getCommunityMessages = async (req, res) => {
  try {
    const messages = await Message.find({ professionId: req.params.professionId })
      .populate('userId', 'name avatar')
      .sort({ createdAt: -1 });
    res.json(messages);
  } catch (error) {
    console.error('Error fetching messages:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Send a message to community
exports.sendMessage = async (req, res) => {
  try {
    const { content } = req.body;
    const message = new Message({
      content,
      userId: req.user.id,
      professionId: req.params.professionId
    });
    await message.save();
    
    const populatedMessage = await Message.findById(message._id)
      .populate('userId', 'name avatar');
      
    res.status(201).json(populatedMessage);
  } catch (error) {
    console.error('Error sending message:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Get contributions for a profession
exports.getContributions = async (req, res) => {
  try {
    const contributions = await Contribution.find({
      professionId: req.params.professionId,
      isApproved: true
    })
    .populate('userId', 'name')
    .sort({ createdAt: -1 });
    
    res.json(contributions);
  } catch (error) {
    console.error('Error fetching contributions:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Add a contribution
exports.addContribution = async (req, res) => {
  try {
    const { title, description, type, url } = req.body;
    
    const contribution = new Contribution({
      title,
      description,
      type,
      url,
      userId: req.user.id,
      professionId: req.params.professionId
    });
    
    await contribution.save();
    
    // In a real app, you might want to notify admins for approval
    
    res.status(201).json(contribution);
  } catch (error) {
    console.error('Error adding contribution:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Get all challenges
exports.getChallenges = async (req, res) => {
  try {
    const challenges = await Challenge.find({
      isActive: true,
      endDate: { $gt: new Date() }
    }).sort({ endDate: 1 });
    
    // Add user's progress to each challenge
    const challengesWithProgress = await Promise.all(challenges.map(async challenge => {
      const userProgress = challenge.participants.find(
        p => p.userId && p.userId.toString() === req.user.id
      );
      
      return {
        ...challenge.toObject(),
        progress: userProgress ? userProgress.progress : 0,
        completed: userProgress ? userProgress.completed : false
      };
    }));
    
    res.json(challengesWithProgress);
  } catch (error) {
    console.error('Error fetching challenges:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Get leaderboard
exports.getLeaderboard = async (req, res) => {
  try {
    // This is a simplified example - you'd want to implement your own leaderboard logic
    // based on points, contributions, or other metrics
    const leaderboard = await User.aggregate([
      { $sort: { points: -1 } },
      { $limit: 50 },
      { $project: { name: 1, avatar: 1, points: 1, _id: 0 } }
    ]);
    
    res.json(leaderboard);
  } catch (error) {
    console.error('Error fetching leaderboard:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Participate in a challenge
exports.participateInChallenge = async (req, res) => {
  const session = await mongoose.startSession();
  session.startTransaction();
  
  try {
    const { progress } = req.body;
    const challenge = await Challenge.findById(req.params.challengeId).session(session);
    
    if (!challenge) {
      await session.abortTransaction();
      return res.status(404).json({ message: 'Challenge not found' });
    }
    
    if (new Date() > new Date(challenge.endDate)) {
      await session.abortTransaction();
      return res.status(400).json({ message: 'Challenge has ended' });
    }
    
    const participantIndex = challenge.participants.findIndex(
      p => p.userId && p.userId.toString() === req.user.id
    );
    
    if (participantIndex === -1) {
      // New participant
      challenge.participants.push({
        userId: req.user.id,
        progress: Math.min(progress, challenge.target),
        completed: progress >= challenge.target,
        completedAt: progress >= challenge.target ? new Date() : null
      });
    } else {
      // Update existing participant
      const participant = challenge.participants[participantIndex];
      participant.progress = Math.min(progress, challenge.target);
      if (!participant.completed && participant.progress >= challenge.target) {
        participant.completed = true;
        participant.completedAt = new Date();
        
        // Award points for challenge completion
        const user = await User.findById(req.user.id).session(session);
        user.points = (user.points || 0) + challenge.reward;
        await user.save({ session });
        
        // Create notification
        const notification = new Notification({
          userId: req.user.id,
          message: `Congratulations! You've completed the "${challenge.title}" challenge and earned ${challenge.reward} points!`,
          type: 'challenge_completed',
          referenceId: challenge._id
        });
        await notification.save({ session });
      }
    }
    
    await challenge.save({ session });
    await session.commitTransaction();
    
    res.json({
      progress: Math.min(progress, challenge.target),
      completed: progress >= challenge.target,
      pointsAwarded: progress >= challenge.target ? challenge.reward : 0
    });
  } catch (error) {
    await session.abortTransaction();
    console.error('Error participating in challenge:', error);
    res.status(500).json({ message: 'Server error' });
  } finally {
    session.endSession();
  }
};

// Rate a contribution
exports.rateContribution = async (req, res) => {
  try {
    const { rating, comment } = req.body;
    const contribution = await Contribution.findById(req.params.contributionId);
    
    if (!contribution) {
      return res.status(404).json({ message: 'Contribution not found' });
    }
    
    // Check if user already rated
    const existingRatingIndex = contribution.ratings.findIndex(
      r => r.userId.toString() === req.user.id
    );
    
    const ratingObj = {
      userId: req.user.id,
      rating,
      comment: comment || '',
      createdAt: new Date()
    };
    
    if (existingRatingIndex === -1) {
      contribution.ratings.push(ratingObj);
    } else {
      contribution.ratings[existingRatingIndex] = ratingObj;
    }
    
    // Calculate average rating
    const totalRatings = contribution.ratings.reduce((sum, r) => sum + r.rating, 0);
    contribution.averageRating = totalRatings / contribution.ratings.length;
    
    await contribution.save();
    
    // Update contributor's points (if it's a new rating)
    if (existingRatingIndex === -1) {
      await User.findByIdAndUpdate(contribution.userId, {
        $inc: { points: 2 } // Award 2 points for receiving a rating
      });
      
      // Notify the contributor
      const notification = new Notification({
        userId: contribution.userId,
        message: `Your contribution "${contribution.title}" received a new ${rating}-star rating!`,
        type: 'contribution_rated',
        referenceId: contribution._id
      });
      await notification.save();
    }
    
    res.json({
      averageRating: contribution.averageRating,
      totalRatings: contribution.ratings.length
    });
  } catch (error) {
    console.error('Error rating contribution:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Track download of a contribution
exports.trackDownload = async (req, res) => {
  try {
    const contribution = await Contribution.findById(req.params.contributionId);
    
    if (!contribution) {
      return res.status(404).json({ message: 'Contribution not found' });
    }
    
    // Increment download count
    contribution.downloadCount = (contribution.downloadCount || 0) + 1;
    await contribution.save();
    
    // Award points to the contributor for each download
    if (contribution.userId.toString() !== req.user.id) {
      await User.findByIdAndUpdate(contribution.userId, {
        $inc: { points: 1 } // 1 point per download
      });
      
      // Notify the contributor
      const notification = new Notification({
        userId: contribution.userId,
        message: `Your contribution "${contribution.title}" was downloaded!`,
        type: 'contribution_downloaded',
        referenceId: contribution._id
      });
      await notification.save();
    }
    
    res.json({ downloadCount: contribution.downloadCount });
  } catch (error) {
    console.error('Error tracking download:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Get user points
exports.getUserPoints = async (req, res) => {
  try {
    const user = await User.findById(req.user.id).select('points');
    res.json({ points: user.points || 0 });
  } catch (error) {
    console.error('Error fetching user points:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Update user points
exports.updateUserPoints = async (req, res) => {
  const session = await mongoose.startSession();
  session.startTransaction();
  
  try {
    const { points, reason } = req.body;
    
    // Update user's points
    const user = await User.findByIdAndUpdate(
      req.user.id,
      { $inc: { points: points } },
      { new: true, session }
    );
    
    // Log the points transaction
    const pointsTransaction = new PointsTransaction({
      userId: req.user.id,
      points: points,
      type: points > 0 ? 'earned' : 'spent',
      reason: reason,
      referenceId: req.body.referenceId,
      referenceType: req.body.referenceType
    });
    await pointsTransaction.save({ session });
    
    // Create notification for significant point changes
    if (Math.abs(points) >= 10) {
      const notification = new Notification({
        userId: req.user.id,
        message: points > 0 
          ? `You've earned ${points} points! ${reason || ''}`
          : `You've spent ${Math.abs(points)} points. ${reason || ''}`,
        type: points > 0 ? 'points_earned' : 'points_spent',
        referenceId: req.body.referenceId
      });
      await notification.save({ session });
    }
    
    await session.commitTransaction();
    
    res.json({ points: user.points });
  } catch (error) {
    await session.abortTransaction();
    console.error('Error updating user points:', error);
    res.status(500).json({ message: 'Server error' });
  } finally {
    session.endSession();
  }
};

// Create new profession
exports.createProfession = async (req, res) => {
  try {
    const { title, description, category, image, averageSalary, demand, requiredSkills } = req.body;

    // Create new profession
    const profession = new Profession({
      title,
      description,
      category,
      image,
      averageSalary: averageSalary || 0,
      demand: demand || 'medium',
      requiredSkills: requiredSkills || [],
      createdBy: req.user.id
    });

    await profession.save();

    // Create notification for administrators
    const notification = new Notification({
      message: `تم إنشاء مهنة جديدة: ${title}`,
      type: 'profession_created',
      createdBy: req.user.id,
      referenceId: profession._id
    });
    await notification.save();

    res.status(201).json(profession);
  } catch (error) {
    console.error('Error creating profession:', error);
    res.status(500).json({ message: 'خطأ في إنشاء المهنة' });
  }
};

// Update profession
exports.updateProfession = async (req, res) => {
  try {
    const { title, description, category, image, averageSalary, demand, requiredSkills } = req.body;

    const profession = await Profession.findById(req.params.id);
    if (!profession) {
      return res.status(404).json({ message: 'المهنة غير موجودة' });
    }

    // Update fields
    profession.title = title || profession.title;
    profession.description = description || profession.description;
    profession.category = category || profession.category;
    profession.image = image || profession.image;
    profession.averageSalary = averageSalary || profession.averageSalary;
    profession.demand = demand || profession.demand;
    profession.requiredSkills = requiredSkills || profession.requiredSkills;
    profession.updatedAt = new Date();

    await profession.save();

    // Create notification
    const notification = new Notification({
      message: `تم تحديث مهنة: ${profession.title}`,
      type: 'profession_updated',
      createdBy: req.user.id,
      referenceId: profession._id
    });
    await notification.save();

    res.json(profession);
  } catch (error) {
    console.error('Error updating profession:', error);
    res.status(500).json({ message: 'خطأ في تحديث المهنة' });
  }
};

// Delete profession
exports.deleteProfession = async (req, res) => {
  try {
    const profession = await Profession.findById(req.params.id);
    if (!profession) {
      return res.status(404).json({ message: 'المهنة غير موجودة' });
    }

    // Delete related content (messages, contributions, etc.)
    await Message.deleteMany({ professionId: profession._id });
    await Contribution.deleteMany({ professionId: profession._id });

    // Delete the profession
    await profession.remove();

    // Create notification
    const notification = new Notification({
      message: `تم حذف مهنة: ${profession.title}`,
      type: 'profession_deleted',
      createdBy: req.user.id,
      referenceId: profession._id
    });
    await notification.save();

    res.json({ message: 'تم حذف المهنة بنجاح' });
  } catch (error) {
    console.error('Error deleting profession:', error);
    res.status(500).json({ message: 'خطأ في حذف المهنة' });
  }
};
