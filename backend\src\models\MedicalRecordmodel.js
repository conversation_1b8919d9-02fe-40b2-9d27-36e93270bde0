const mongoose = require('mongoose');

const MedicalRecordSchema = new mongoose.Schema({
  patientUsername: {
    type: String,
    required: [true, 'الرجاء إدخال اسم المستخدم للمريض']
  },
  fileUrl: {
    type: String,
    required: [true, 'الرجاء إدخال رابط الملف']
  },
  fileType: {
    type: String,
    required: [true, 'الرجاء تحديد نوع الملف'],
    enum: ['image', 'pdf', 'doc', 'xray', 'lab', 'other']
  },
  addedBy: {
    type: String,
    required: [true, 'الرجاء تحديد من أضاف السجل']
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
});

module.exports = mongoose.model('MedicalRecord', MedicalRecordSchema);
