import 'package:flutter/material.dart';

class SearchPage extends StatefulWidget {
  const SearchPage({super.key});

  @override
  State<SearchPage> createState() => _SearchPageState();
}

class _SearchPageState extends State<SearchPage> {
  final TextEditingController controller = TextEditingController();
  List<Map<String, String>> results = [];
  bool isLoading = false;

  Future<void> handleSearch(String q) async {
    setState(() => isLoading = true);
    // TODO: Replace with actual API call
    await Future.delayed(const Duration(seconds: 1));
    setState(() {
      results = [
        {'name': 'أحمد', 'email': '<EMAIL>'},
        {'name': 'سارة', 'email': '<EMAIL>'},
      ];
      isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('بحث عن مستخدمين', style: TextStyle(fontFamily: '<PERSON><PERSON><PERSON>'))),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: controller,
                    decoration: const InputDecoration(
                      hintText: 'ابحث بالاسم أو البريد',
                      border: OutlineInputBorder(),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: () => handleSearch(controller.text),
                  child: const Text('بحث'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (isLoading)
              const CircularProgressIndicator()
            else
              Expanded(
                child: ListView.builder(
                  itemCount: results.length,
                  itemBuilder: (context, idx) => ListTile(
                    leading: const Icon(Icons.person),
                    title: Text(results[idx]['name'] ?? '', style: const TextStyle(fontFamily: 'Tajawal')),
                    subtitle: Text(results[idx]['email'] ?? ''),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
