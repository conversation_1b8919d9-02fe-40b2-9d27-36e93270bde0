const Card = require('../models/Cardmodel');
const ErrorResponse = require('../utils/errorResponse');
const asyncHandler = require('../middleware/asyncmiddleware');

// @desc    Get all cards
// @route   GET /api/cards
// @access  Private
exports.getCards = asyncHandler(async (req, res, next) => {
  const cards = await Card.find().sort({ createdAt: -1 });
  res.status(200).json(cards);
});

// @desc    Create a card
// @route   POST /api/cards
// @access  Private
exports.createCard = asyncHandler(async (req, res, next) => {
  const { title, description, image } = req.body;
  const card = await Card.create({ title, description, image });
  res.status(201).json(card);
});
