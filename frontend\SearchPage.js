import React, { useState } from "react";
import axios from "axios";

const SearchPage = () => {
  const [q, setQ] = useState("");
  const [results, setResults] = useState([]);
  const handleSearch = async e => {
    e.preventDefault();
    const res = await axios.get(`/api/users/advanced-search?q=${q}`);
    setResults(res.data.data);
  };
  return (
    <div style={{ padding: 32 }}>
      <h2>بحث عن مستخدمين</h2>
      <form onSubmit={handleSearch} style={{ marginBottom: 16 }}>
        <input value={q} onChange={e => setQ(e.target.value)} placeholder="ابحث بالاسم أو البريد" />
        <button type="submit">بحث</button>
      </form>
      <ul>
        {results.map(u => (
          <li key={u._id}>{u.name} ({u.email})</li>
        ))}
      </ul>
    </div>
  );
};
export default SearchPage;
