const User = require('../models/user.model');
const ErrorResponse = require('../utils/errorResponse');
const asyncHandler = require('../middleware/asyncmiddleware');

// @desc    تسجيل مستخدم جديد
// @route   POST /api/auth/register
// @access  Public
exports.register = asyncHandler(async (req, res, next) => {
  const { name, email, password, role, job } = req.body;
    // التحقق من صحة الدور
  const validRoles = ['hokama', 'olama', 'mogtahdin', 'medical', 'member', 'guest'];
  const assignedRole = validRoles.includes(role) ? role : 'member';

  // إنشاء مستخدم جديد
  const user = await User.create({
    name,
    email,
    password,
    role: assignedRole,
    job
  });

  sendTokenResponse(user, 201, res);
});

// @desc    تسجيل الدخول
// @route   POST /api/auth/login
// @access  Public
exports.login = asyncHandler(async (req, res, next) => {
  const { email, password } = req.body;

  // التحقق من وجود البريد الإلكتروني وكلمة المرور
  if (!email || !password) {
    return next(new ErrorResponse('الرجاء إدخال البريد الإلكتروني وكلمة المرور', 400));
  }

  // التحقق من وجود المستخدم
  const user = await User.findOne({ email }).select('+password');
  if (!user) {
    return next(new ErrorResponse('بيانات الاعتماد غير صالحة', 401));
  }

  // التحقق من صحة كلمة المرور
  const isMatch = await user.matchPassword(password);
  if (!isMatch) {
    return next(new ErrorResponse('بيانات الاعتماد غير صالحة', 401));
  }

  // تحديث حالة الاتصال
  user.isOnline = true;
  user.lastActive = Date.now();
  await user.save({ validateBeforeSave: false });

  sendTokenResponse(user, 200, res);
});

// @desc    تسجيل الخروج
// @route   GET /api/auth/logout
// @access  Private
exports.logout = asyncHandler(async (req, res, next) => {
  // تحديث حالة الاتصال
  req.user.isOnline = false;
  req.user.lastActive = Date.now();
  await req.user.save({ validateBeforeSave: false });

  res.status(200).json({
    success: true,
    data: {}
  });
});

// @desc    الحصول على المستخدم الحالي
// @route   GET /api/auth/me
// @access  Private
exports.getMe = asyncHandler(async (req, res, next) => {
  const user = await User.findById(req.user.id);

  res.status(200).json({
    success: true,
    data: user
  });
});

// @desc    تحديث تفاصيل المستخدم
// @route   PUT /api/auth/updatedetails
// @access  Private
exports.updateDetails = asyncHandler(async (req, res, next) => {
  const fieldsToUpdate = {
    name: req.body.name,
    email: req.body.email,
    bio: req.body.bio,
    interests: req.body.interests,
    job: req.body.job
  };

  // حذف الحقول الفارغة
  Object.keys(fieldsToUpdate).forEach(key => {
    if (fieldsToUpdate[key] === undefined) {
      delete fieldsToUpdate[key];
    }
  });

  const user = await User.findByIdAndUpdate(req.user.id, fieldsToUpdate, {
    new: true,
    runValidators: true
  });

  res.status(200).json({
    success: true,
    data: user
  });
});

// @desc    تحديث كلمة المرور
// @route   PUT /api/auth/updatepassword
// @access  Private
exports.updatePassword = asyncHandler(async (req, res, next) => {
  const user = await User.findById(req.user.id).select('+password');

  // التحقق من كلمة المرور الحالية
  if (!(await user.matchPassword(req.body.currentPassword))) {
    return next(new ErrorResponse('كلمة المرور الحالية غير صحيحة', 401));
  }

  user.password = req.body.newPassword;
  await user.save();

  sendTokenResponse(user, 200, res);
});

// @desc    تحديث موقع المستخدم
// @route   PUT /api/auth/updatelocation
// @access  Private
exports.updateLocation = asyncHandler(async (req, res, next) => {
  const { latitude, longitude } = req.body;

  if (!latitude || !longitude) {
    return next(new ErrorResponse('الرجاء إدخال خط العرض وخط الطول', 400));
  }

  const user = await User.findByIdAndUpdate(
    req.user.id,
    { latitude, longitude },
    { new: true, runValidators: true }
  );

  res.status(200).json({
    success: true,
    data: user
  });
});

// إرسال التوكن في الاستجابة
const sendTokenResponse = (user, statusCode, res) => {
  // إنشاء التوكن
  const token = user.getSignedJwtToken();

  res.status(statusCode).json({
    success: true,
    token,
    user: {
      id: user._id,
      name: user.name,
      email: user.email,
      role: user.role,
      avatarUrl: user.avatarUrl,
      userType: user.userType,
      isOnline: user.isOnline
    }
  });
};
