import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:animate_do/animate_do.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';

import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:http/http.dart' as http;
import 'package:image_picker/image_picker.dart';
import 'package:provider/provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:timeago/timeago.dart' as timeago_pkg;

import 'appstate.dart';

// تهيئة Timeago للغة العربية
void setupTimeago() {
  timeago_pkg.setLocaleMessages('ar', timeago_pkg.ArMessages());
}

enum PostPrivacy {
  public('عام'),
  groupMembers('أعضاء المجموعة فقط'),
  specificMembers('أعضاء محددين'),
  onlyMe('أنا فقط');

  final String label;
  const PostPrivacy(this.label);

  static PostPrivacy fromString(String value) {
    return PostPrivacy.values.firstWhere(
          (e) => e.name == value,
      orElse: () => PostPrivacy.public,
    );
  }
}

class Group {
  final String id;
  final String name;
  final String description;
  final String image;
  final String type; // Islamic or Christian
  final List<String> members;
  final bool isJoined;

  Group({
    required this.id,
    required this.name,
    required this.description,
    required this.image,
    required this.type,
    required this.members,
    required this.isJoined,
  });

  factory Group.fromJson(Map<String, dynamic> json) {
    return Group(
      id: json['_id'] ?? '',
      name: json['name'] ?? 'بدون اسم',
      description: json['description'] ?? '',
      image: json['image'] ?? '',
      type: json['type'] ?? 'Islamic',
      members: List<String>.from(json['members'] ?? []),
      isJoined: json['isJoined'] ?? false,
    );
  }
}

class Post {
  final String id;
  final String groupId;
  final String user;
  final String content;
  final String? image;
  int likes;
  final List<String> comments;
  final int views;
  final DateTime createdAt;
  bool isLiked;
  final PostPrivacy privacy;
  final List<String>? visibleToUserIds;

  Post({
    required this.id,
    required this.groupId,
    required this.user,
    required this.content,
    this.image,
    required this.likes,
    required this.isLiked,
    required this.comments,
    required this.views,
    required this.createdAt,
    this.privacy = PostPrivacy.groupMembers,
    this.visibleToUserIds,
  });

  factory Post.fromJson(Map<String, dynamic> json) {
    return Post(
      id: json['_id'] ?? '',
      groupId: json['groupId'] ?? '',
      user: json['user'] ?? 'مجهول',
      content: json['content'] ?? '',
      image: json['image'],
      likes: json['likes'] ?? 0,
      isLiked: json['isLiked'] ?? false,
      comments: List<String>.from(json['comments'] ?? []),
      views: json['views'] ?? 0,
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toString()),
      privacy: PostPrivacy.fromString(json['privacy'] ?? 'groupMembers'),
      visibleToUserIds: json['visibleToUserIds'] != null
          ? List<String>.from(json['visibleToUserIds'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'groupId': groupId,
      'user': user,
      'content': content,
      'image': image,
      'likes': likes,
      'isLiked': isLiked,
      'comments': comments,
      'views': views,
      'createdAt': createdAt.toIso8601String(),
      'privacy': privacy.name,
      'visibleToUserIds': visibleToUserIds,
    };
  }
}

class GroupService {
  final String baseUrl;

  GroupService() : baseUrl = AppState.getBackendUrl();

  Future<List<Group>> fetchGroups(String token, {String? type}) async {
    try {
      final uri = Uri.parse('$baseUrl/groups').replace(
        queryParameters: type != null ? {'type': type} : null,
      );
      final response = await http.get(
        uri,
        headers: {'x-auth-token': token},
      );
      if (response.statusCode == 200) {
        return (jsonDecode(response.body) as List)
            .map((json) => Group.fromJson(json))
            .toList();
      }
      throw Exception('فشل في جلب المجموعات: ${response.body}');
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }

  Future<void> joinGroup(String token, String groupId) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/groups/$groupId/join'),
        headers: {'x-auth-token': token},
      );
      if (response.statusCode != 200) {
        throw Exception('فشل في الانضمام إلى المجموعة: ${response.body}');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }

  Future<void> leaveGroup(String token, String groupId) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/groups/$groupId/leave'),
        headers: {'x-auth-token': token},
      );
      if (response.statusCode != 200) {
        throw Exception('فشل في مغادرة المجموعة: ${response.body}');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }
}

class PostService {
  final String baseUrl;

  PostService() : baseUrl = AppState.getBackendUrl();

  Future<Map<String, dynamic>> fetchPosts(
      String token, String groupId, int page, int limit,
      {String? currentUserId}) async {
    try {
      final params = {
        'groupId': groupId,
        'page': page.toString(),
        'limit': limit.toString(),
        if (currentUserId != null) 'currentUserId': currentUserId,
      };
      final uri = Uri.parse('$baseUrl/group-posts').replace(queryParameters: params);
      final response = await http.get(uri, headers: {'x-auth-token': token});
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return {
          'posts': (data['posts'] as List)
              .map((json) => Post.fromJson(json))
              .toList(),
          'total': data['total'] as int,
        };
      }
      throw Exception('فشل في جلب المنشورات: ${response.body}');
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }

  Future<void> addPost(
      String token,
      String groupId,
      String content,
      File? image, {
        PostPrivacy privacy = PostPrivacy.groupMembers,
        List<String>? visibleToUserIds,
      }) async {
    try {
      var request = http.MultipartRequest('POST', Uri.parse('$baseUrl/group-posts'));
      request.headers['x-auth-token'] = token;
      request.fields['groupId'] = groupId;
      request.fields['userId'] = 'user1'; // TODO: Replace with actual user ID
      request.fields['content'] = content;
      request.fields['privacy'] = privacy.name;
      if (visibleToUserIds != null && visibleToUserIds.isNotEmpty) {
        request.fields['visibleToUserIds'] = jsonEncode(visibleToUserIds);
      }
      if (image != null) {
        if (await image.length() > 5 * 1024 * 1024) {
          throw Exception('حجم الصورة كبير جدًا (يجب أن يكون أقل من 5 ميجابايت)');
        }
        request.files.add(await http.MultipartFile.fromPath('image', image.path));
      }
      final response = await request.send();
      if (response.statusCode != 201) {
        throw Exception('فشل في نشر المنشور: ${response.reasonPhrase}');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }

  Future<void> likePost(String token, String postId, bool isLiked) async {
    try {
      final response = await http.put(
        Uri.parse('$baseUrl/group-posts/$postId/like'),
        headers: {'x-auth-token': token, 'Content-Type': 'application/json'},
        body: jsonEncode({'isLiked': isLiked}),
      );
      if (response.statusCode != 200) {
        throw Exception('فشل في تحديث الإعجاب: ${response.body}');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }

  Future<void> commentPost(String token, String postId, String comment) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/group-posts/$postId/comment'),
        headers: {'Content-Type': 'application/json', 'x-auth-token': token},
        body: jsonEncode({'comment': comment}),
      );
      if (response.statusCode != 200) {
        throw Exception('فشل في إضافة التعليق: ${response.body}');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }

  Future<List<Map<String, dynamic>>> fetchNotifications(String token) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/notifications'),
        headers: {'x-auth-token': token},
      );
      if (response.statusCode == 200) {
        return List<Map<String, dynamic>>.from(jsonDecode(response.body));
      }
      throw Exception('فشل في جلب الإشعارات: ${response.body}');
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }

  Future<void> updateNotification(String token, String notificationId) async {
    try {
      final response = await http.put(
        Uri.parse('$baseUrl/notifications/$notificationId'),
        headers: {'x-auth-token': token},
      );
      if (response.statusCode != 200) {
        throw Exception('فشل في تحديث الإشعار: ${response.body}');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }
}

class GroupsPage extends StatefulWidget {
  const GroupsPage({super.key});

  @override
  _GroupsPageState createState() => _GroupsPageState();
}

class _GroupsPageState extends State<GroupsPage> with SingleTickerProviderStateMixin {
  final GroupService _groupService = GroupService();
  late AnimationController _fabAnimationController;
  late Animation<double> _fabAnimation;
  final TextEditingController _searchController = TextEditingController();
  bool _isLoading = true;
  List<Group> groups = [];
  List<Map<String, dynamic>> _notifications = [];

  final List<String> _searchSuggestions = [
    'السنة',
    'الشيعة',
    'الصوفية',
    'الأرثوذكس',
    'الكاثوليك',
  ];

  @override
  void initState() {
    super.initState();
    setupTimeago();
    _fabAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    _fabAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fabAnimationController, curve: Curves.easeOut),
    );
    _fabAnimationController.forward();
    _fetchGroups();
    _fetchNotifications();
    _searchController.addListener(_onSearchChanged);

    final appState = Provider.of<AppState>(context, listen: false);
    if (appState.socket != null) {
      appState.socket!.on('new_post', (data) {
        _fetchGroups();
        setState(() {
          _notifications.add({
            'message': 'منشور جديد في ${data['groupName']}: ${data['content']}',
            'date': DateTime.now().toString(),
            'read': false,
            '_id': data['notificationId'] ?? DateTime.now().toString(),
          });
        });
      });
    }
  }

  Future<void> _fetchGroups() async {
    setState(() => _isLoading = true);
    final appState = Provider.of<AppState>(context, listen: false);
    try {
      final fetchedGroups = await _groupService.fetchGroups(appState.token ?? '');
      setState(() {
        groups = fetchedGroups;
        _isLoading = false;
      });
    } catch (e) {
      _showSnackBar('خطأ في جلب المجموعات: $e', Colors.red);
      setState(() => _isLoading = false);
    }
  }

  Future<void> _fetchNotifications() async {
    final appState = Provider.of<AppState>(context, listen: false);
    try {
      _notifications = await PostService().fetchNotifications(appState.token ?? '');
      setState(() {});
    } catch (e) {
      _showSnackBar('خطأ في جلب الإشعارات: $e', Colors.red);
    }
  }

  void _onSearchChanged() {
    _debounce(() => setState(() {}));
  }

  Timer? _debounceTimer;
  void _debounce(VoidCallback callback) {
    const duration = Duration(milliseconds: 300);
    _debounceTimer?.cancel();
    _debounceTimer = Timer(duration, callback);
  }

  @override
  void dispose() {
    _fabAnimationController.dispose();
    _searchController.dispose();
    _debounceTimer?.cancel();
    super.dispose();
  }

  void _showSnackBar(String message, [Color? backgroundColor, SnackBarAction? action]) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: backgroundColor ?? Colors.teal,
        behavior: SnackBarBehavior.floating,
        action: action,
        elevation: 6,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final filteredGroups = groups
        .where((group) =>
        group.name.toLowerCase().contains(_searchController.text.toLowerCase()))
        .toList();

    return Scaffold(
      body: _isLoading
          ? _buildSkeletonLoader()
          : RefreshIndicator(
        onRefresh: () async {
          await _fetchGroups();
          await _fetchNotifications();
        },
        color: Colors.teal,
        child: CustomScrollView(
          slivers: [
            _buildSliverAppBar(isDarkMode),
            SliverToBoxAdapter(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: FadeInDown(
                  child: Text(
                    "المجموعات الدينية",
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: isDarkMode ? Colors.teal[300] : Colors.teal,
                    ),
                  ),
                ),
              ),
            ),
            SliverList(
              delegate: SliverChildBuilderDelegate(
                    (context, index) => FadeInUp(
                  child: _buildGroupCard(context, filteredGroups[index]),
                ),
    childCount: filteredGroups.length,
      ),
    ),
          ],
        ),
      ),
      floatingActionButton: Consumer<AppState>(
        builder: (context, appState, _) {
          final roleName = appState.userRole.toLowerCase();
          final isHokama = roleName == 'hokama' || roleName == 'حكيم';
          if (!isHokama) return const SizedBox.shrink();
          return FloatingActionButton(
            onPressed: () => _showCreateGroupDialog(context),
            backgroundColor: Colors.teal,
            tooltip: 'إنشاء مجموعة',
            elevation: 8,
            child: const Icon(Icons.group_add, size: 30, color: Colors.white),
          );
        },
      ),
    );
  }

  Widget _buildSkeletonLoader() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return CustomScrollView(
      slivers: [
        SliverAppBar(
          expandedHeight: 120,
          flexibleSpace: FlexibleSpaceBar(
            background: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: isDarkMode
                      ? [Colors.teal[900]!, Colors.teal[700]!]
                      : [Colors.teal, Colors.tealAccent],
                ),
              ),
              child: SafeArea(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Container(
                    height: 40,
                    decoration: BoxDecoration(
                      color: isDarkMode ? Colors.grey[800] : Colors.grey[300],
                      borderRadius: BorderRadius.circular(30),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
        SliverList(
          delegate: SliverChildBuilderDelegate(
                (context, index) => Pulse(
              child: Card(
                margin: const EdgeInsets.all(16),
                child: Padding(
                  padding: const EdgeInsets.all(12.0),
                  child: Column(
                    children: [
                      Container(height: 20, width: 100, color: Colors.grey[300]),
                      const SizedBox(height: 10),
                      Container(height: 100, color: Colors.grey[300]),
                    ],
                  ),
                ),
              ),
            ),
            childCount: 5,
          ),
        ),
      ],
    );
  }

  SliverAppBar _buildSliverAppBar(bool isDarkMode) {
    return SliverAppBar(
      expandedHeight: 120,
      flexibleSpace: FlexibleSpaceBar(
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: isDarkMode
                  ? [Colors.teal[900]!, Colors.teal[700]!]
                  : [Colors.teal, Colors.tealAccent],
            ),
          ),
          child: SafeArea(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Column(
                children: [
                  TextField(
                    controller: _searchController,
                    decoration: InputDecoration(
                      hintText: 'بحث عن مجموعة...',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(30),
                        borderSide: BorderSide.none,
                      ),
                      hintStyle: TextStyle(color: isDarkMode ? Colors.white70 : Colors.white),
                      prefixIcon: Icon(Icons.search, color: isDarkMode ? Colors.white : Colors.white),
                      suffixIcon: _searchController.text.isNotEmpty
                          ? IconButton(
                        icon: Icon(Icons.clear, color: isDarkMode ? Colors.white : Colors.white),
                        onPressed: () {
                          _searchController.clear();
                          setState(() {});
                        },
                      )
                          : null,
                      filled: true,
                      fillColor: isDarkMode ? Colors.white12 : Colors.white24,
                    ),
                    style: TextStyle(color: isDarkMode ? Colors.white : Colors.white),
                    onChanged: (value) {
                      if (value.isNotEmpty) {
                        _showSearchSuggestions(context, value);
                      }
                    },
                  ),
                ],
              ),
            ),
          ),
        ),
      ),

      pinned: true,
    );
  }

  void _showSearchSuggestions(BuildContext context, String query) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        height: 200,
        padding: const EdgeInsets.all(16),
        child: ListView(
          children: _searchSuggestions
              .where((suggestion) =>
              suggestion.toLowerCase().contains(query.toLowerCase()))
              .map((suggestion) => ListTile(
            title: Text(suggestion),
            onTap: () {
              _searchController.text = suggestion;
              setState(() {});
              Navigator.pop(context);
            },
          ))
              .toList(),
        ),
      ),
    );
  }

  Widget _buildGroupCard(BuildContext context, Group group) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return GestureDetector(
      onTap: () => Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => GroupPostsPage(
            groupId: group.id,
            groupName: group.name,
          ),
        ),
      ),
      child: Card(
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        elevation: 6,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            image: DecorationImage(
              image: CachedNetworkImageProvider(
                group.image.isNotEmpty
                    ? '$_groupService.baseUrl${group.image}'
                    : 'https://via.placeholder.com/400x200',
              ),
              fit: BoxFit.cover,
              colorFilter: ColorFilter.mode(
                Colors.black.withOpacity(0.4),
                BlendMode.dstATop,
              ),
            ),
          ),
          padding: const EdgeInsets.all(12.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      const Icon(Icons.group, color: Colors.white, size: 28),
                      const SizedBox(width: 8),
                      Text(
                        group.name,
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ],
                  ),
                  IconButton(
                    icon: Icon(
                      group.isJoined ? Icons.star : Icons.star_border,
                      color: Colors.white,
                    ),
                    onPressed: () => _toggleGroupMembership(group),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                group.description,
                style: const TextStyle(color: Colors.white70),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 8),
              Text(
                'الأعضاء: ${group.members.length}',
                style: const TextStyle(color: Colors.white70),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _toggleGroupMembership(Group group) async {
    final appState = Provider.of<AppState>(context, listen: false);
    try {
      if (group.isJoined) {
        await _groupService.leaveGroup(appState.token ?? '', group.id);
        _showSnackBar('تم مغادرة المجموعة', Colors.orange);
      } else {
        await _groupService.joinGroup(appState.token ?? '', group.id);
        _showSnackBar('تم الانضمام إلى المجموعة', Colors.green);
      }
      await _fetchGroups();
    } catch (e) {
      _showSnackBar('خطأ: $e', Colors.red);
    }
  }

  void _showCreateGroupDialog(BuildContext context) {
    final appState = Provider.of<AppState>(context, listen: false);
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final groupNameController = TextEditingController();
    final groupDescriptionController = TextEditingController();
    String groupType = 'Islamic';

    if (appState.userRole != 'admin') {
      _showSnackBar('عذراً، هذه الميزة متاحة فقط للإداريين', Colors.red);
      return;
    }

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('إنشاء مجموعة جديدة'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: groupNameController,
                  decoration: const InputDecoration(
                    labelText: 'اسم المجموعة',
                    border: OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: groupDescriptionController,
                  decoration: const InputDecoration(
                    labelText: 'وصف المجموعة',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 3,
                ),
                const SizedBox(height: 16),
                DropdownButtonFormField<String>(
                  value: groupType,
                  decoration: const InputDecoration(
                    labelText: 'نوع المجموعة',
                    border: OutlineInputBorder(),
                  ),
                  items: ['Islamic', 'Christian'].map((type) {
                    return DropdownMenuItem(
                      value: type,
                      child: Text(type == 'Islamic' ? 'إسلامية' : 'مسيحية'),
                    );
                  }).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      setState(() => groupType = value);
                    }
                  },
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(
                'إلغاء',
                style: TextStyle(color: isDarkMode ? Colors.white70 : Colors.teal),
              ),
            ),
            ElevatedButton(
              onPressed: () async {
                if (groupNameController.text.isEmpty) {
                  _showSnackBar('يرجى إدخال اسم المجموعة', Colors.red);
                  return;
                }
                try {
                  // TODO: Implement group creation API call
                  Navigator.pop(context);
                  _showSnackBar('تم إنشاء المجموعة بنجاح', Colors.green);
                  await _fetchGroups();
                } catch (e) {
                  _showSnackBar('خطأ في إنشاء المجموعة: $e', Colors.red);
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.teal,
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
              ),
              child: const Text('إنشاء', style: TextStyle(color: Colors.white)),
            ),
          ],
        ),
      ),
    );
  }

  void _showNotifications() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: isDarkMode ? Colors.grey[900] : Colors.white,
        shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
        topLeft: Radius.circular(20),
    topRight: Radius.circular(20),
    ),),
    builder: (context) => DraggableScrollableSheet(
    initialChildSize: 0.5,
    minChildSize: 0.3,
    maxChildSize: 0.8,
    builder: (context, scrollController) => Container(
    padding: const EdgeInsets.all(16),
    child: Column(
    children: [
    Text(
    'الإشعارات',
    style: TextStyle(
    fontSize: 20,
    fontWeight: FontWeight.bold,
    color: isDarkMode ? Colors.teal[400] : Colors.teal,
    ),
    ),
    Expanded(
    child: _notifications.isEmpty
    ? Center(
    child: Text(
    'لا توجد إشعارات',
    style: TextStyle(
    color: isDarkMode ? Colors.grey[400] : Colors.grey,
    ),
    ),
    )
        : ListView.builder(
    controller: scrollController,
    itemCount: _notifications.length,
    itemBuilder: (context, index) => Dismissible(
    key: Key(_notifications[index]['_id']),
    background: Container(
    color: Colors.red,
    alignment: Alignment.centerRight,
    padding: const EdgeInsets.only(right: 16),
    child: const Icon(Icons.delete, color: Colors.white),
    ),
    onDismissed: (direction) async {
    final notificationId = _notifications[index]['_id'];
    final appState = Provider.of<AppState>(context, listen: false);
    try {
    await PostService().updateNotification(appState.token!, notificationId);
    setState(() => _notifications.removeAt(index));
    } catch (e) {
    _showSnackBar('خطأ في حذف الإشعار: $e', Colors.red);
    }
    },
    child: ListTile(
    leading: const Icon(Icons.notifications_active, color: Colors.teal),
    title: Text(
    _notifications[index]['message'] ?? 'غير معروف',
    style: TextStyle(
    color: isDarkMode ? Colors.white : Colors.black87,
    ),
    ),
    subtitle: Text(
    timeago_pkg.format(
    DateTime.parse(_notifications[index]['date'] ?? DateTime.now().toString()),
    locale: 'ar',
    ),
    style: TextStyle(
    color: isDarkMode ? Colors.grey[400] : Colors.grey,
    ),
    ),
    trailing: _notifications[index]['read'] ? null : const Icon(Icons.circle, color: Colors.teal, size: 10),
    onTap: () async {
    if (!_notifications[index]['read']) {
    final appState = Provider.of<AppState>(context, listen: false);
    try {
    await PostService().updateNotification(
    appState.token!,
    _notifications[index]['_id']);
    setState(() => _notifications[index]['read'] = true);
    } catch (e) {
    _showSnackBar('خطأ في تحديث الإشعار: $e', Colors.red);
    }
    }
    },
    ),
    ),
    ),),
    ElevatedButton(
    onPressed: () {
    setState(() => _notifications.clear());
    Navigator.pop(context);
    },
    style: ElevatedButton.styleFrom(
    backgroundColor: Colors.red,
    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
    ),
    child: const Text('مسح الكل', style: TextStyle(color: Colors.white)),
    ),
    ],
    ),
    ),
    ),
    );
  }
}

class GroupPostsPage extends StatefulWidget {
  final String groupId;
  final String groupName;

  const GroupPostsPage({super.key, required this.groupId, required this.groupName});

  @override
  _GroupPostsPageState createState() => _GroupPostsPageState();
}

class _GroupPostsPageState extends State<GroupPostsPage> with SingleTickerProviderStateMixin {
  final PostService _postService = PostService();
  final ScrollController _scrollController = ScrollController();
  final List<String> _ads = [
    '${AppState.getBackendUrl()}/uploads/group-ads/ad1.jpg',
    '${AppState.getBackendUrl()}/uploads/group-ads/ad2.jpg',
    '${AppState.getBackendUrl()}/uploads/group-ads/ad3.jpg',
  ];
  late PageController _pageController;
  int _currentAdIndex = 0;
  List<Post> _posts = [];
  int _page = 1;
  int _totalPosts = 0;
  final int _limit = 10;
  bool _isLoading = true;
  bool _isLoadingMore = false;
  bool _isGridView = false;
  Timer? _timer;
  late AnimationController _fabAnimationController;
  late Animation<double> _fabAnimation;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    _scrollController.addListener(_scrollListener);
    _fetchPosts();
    _startAutoScroll();

    _fabAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 2),
    )..repeat(reverse: true);
    _fabAnimation = Tween<double>(begin: 1.0, end: 1.2).animate(
      CurvedAnimation(parent: _fabAnimationController, curve: Curves.easeInOut),
    );
  }

  void _startAutoScroll() {
    _timer = Timer.periodic(const Duration(seconds: 5), (Timer timer) {
      if (!mounted) return;
      setState(() {
        _currentAdIndex = (_currentAdIndex < _ads.length - 1) ? _currentAdIndex + 1 : 0;
        _pageController.animateToPage(
          _currentAdIndex,
          duration: const Duration(milliseconds: 500),
          curve: Curves.easeInOut,
        );
      });
    });
  }

  void _scrollListener() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 50 &&
        !_isLoadingMore &&
        _posts.length < _totalPosts) {
      _loadMorePosts();
    }
  }

  Future<void> _fetchPosts() async {
    setState(() => _isLoading = true);
    final appState = Provider.of<AppState>(context, listen: false);
    try {
      final data = await _postService.fetchPosts(
        appState.token ?? '',
        widget.groupId,
        _page,
        _limit,
        currentUserId: appState.userId,
      );
      setState(() {
        _posts = data['posts'] as List<Post>;
        _totalPosts = data['total'] as int;
        _isLoading = false;
      });
    } catch (e) {
      _showSnackBar('خطأ في جلب المنشورات: $e', Colors.red);
      setState(() => _isLoading = false);
    }
  }

  Future<void> _loadMorePosts() async {
    if (_isLoadingMore) return;
    setState(() => _isLoadingMore = true);
    final appState = Provider.of<AppState>(context, listen: false);
    try {
      final data = await _postService.fetchPosts(
        appState.token ?? '',
        widget.groupId,
        _page + 1,
        _limit,
        currentUserId: appState.userId,
      );
      setState(() {
        _posts.addAll(data['posts'] as List<Post>);
        _totalPosts = data['total'] as int;
        _page += 1;
        _isLoadingMore = false;
      });
    } catch (e) {
      _showSnackBar('خطأ في جلب المزيد من المنشورات: $e', Colors.red);
      setState(() => _isLoadingMore = false);
    }
  }

  void _showSnackBar(String message, [Color? backgroundColor, SnackBarAction? action]) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: backgroundColor ?? Colors.teal,
        behavior: SnackBarBehavior.floating,
        action: action,
        elevation: 6,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      ),
    );
  }

  @override
  void dispose() {
    _timer?.cancel();
    _pageController.dispose();
    _scrollController.dispose();
    _fabAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return Scaffold(
      body: _isLoading
          ? _buildSkeletonLoader()
          : RefreshIndicator(
        onRefresh: _fetchPosts,
        color: Colors.teal,
        child: CustomScrollView(
          controller: _scrollController,
          slivers: [
            _buildSliverAppBar(isDarkMode),
            SliverToBoxAdapter(child: _buildAdsSection()),
            SliverToBoxAdapter(child: _buildPostsSection()),
            if (_isLoadingMore)
              const SliverToBoxAdapter(
                child: Padding(
                  padding: EdgeInsets.all(16),
                  child: CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.teal),
                  ),
                ),
              ),
          ],
        ),
      ),
      floatingActionButton: ScaleTransition(
        scale: _fabAnimation,
        child: FloatingActionButton(
          onPressed: () => _showCreatePostDialog(context),
          backgroundColor: Colors.teal,
          tooltip: 'إضافة منشور',
          elevation: 8,
          child: const Icon(Icons.post_add, size: 30, color: Colors.white),
        ),
      ),
    );
  }

  SliverAppBar _buildSliverAppBar(bool isDarkMode) {
    return SliverAppBar(
      expandedHeight: 100,
      flexibleSpace: FlexibleSpaceBar(
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: isDarkMode
                  ? [Colors.teal[900]!, Colors.teal[700]!]
                  : [Colors.teal, Colors.tealAccent],
            ),
          ),
          child: SafeArea(
            child: Center(
              child: Text(
                "منشورات ${widget.groupName}",
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ),
          ),
        ),
      ),
      actions: [
        IconButton(
          icon: Icon(_isGridView ? Icons.list : Icons.grid_view, color: Colors.white),
          onPressed: () => setState(() => _isGridView = !_isGridView),
        ),
      ],
      pinned: true,
    );
  }

  Widget _buildSkeletonLoader() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return CustomScrollView(
      slivers: [
        SliverAppBar(
          expandedHeight: 100,
          flexibleSpace: FlexibleSpaceBar(
            background: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: isDarkMode
                      ? [Colors.teal[900]!, Colors.teal[700]!]
                      : [Colors.teal, Colors.tealAccent],
                ),
              ),
            ),
          ),
        ),
        SliverToBoxAdapter(
          child: Container(
            height: 150,
            margin: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: isDarkMode ? Colors.grey[800] : Colors.grey[300],
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        ),
        SliverList(
          delegate: SliverChildBuilderDelegate(
                (context, index) => Pulse(
              child: Card(
                margin: const EdgeInsets.all(16),
                child: Padding(
                  padding: const EdgeInsets.all(12),
                  child: Column(
                    children: [
                      Container(height: 20, width: 100, color: Colors.grey[300]),
                      const SizedBox(height: 10),
                      Container(height: 100, width: double.infinity, color: Colors.grey[300]),
                    ],
                  ),
                ),
              ),
            ),
            childCount: 5,
          ),
        ),
      ],
    );
  }

  Widget _buildAdsSection() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return Column(
      children: [
        SizedBox(
          height: 150,
          child: PageView.builder(
            controller: _pageController,
            itemCount: _ads.length,
            onPageChanged: (index) => setState(() => _currentAdIndex = index),
            itemBuilder: (context, index) => GestureDetector(
              onTap: () => _showSnackBar('تم النقر على الإعلان ${index + 1}'),
              child: Transform(
                transform: Matrix4.identity()
                  ..setEntry(3, 2, 0.001)
                  ..rotateX(0.1 * (_currentAdIndex - index)),
                alignment: Alignment.center,
                child: Container(
                  margin: const EdgeInsets.symmetric(horizontal: 8),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.2),
                        spreadRadius: 2,
                        blurRadius: 8,
                      ),
                    ],
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(12),
                    child: CachedNetworkImage(
                      imageUrl: _ads[index],
                      fit: BoxFit.cover,
                      placeholder: (context, url) => Container(
                        color: isDarkMode ? Colors.grey[800] : Colors.grey[300],
                        child: const Center(
                          child: CircularProgressIndicator(
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.teal),
                          ),
                        ),
                      ),
                      errorWidget: (context, url, error) => const Icon(
                        Icons.error,
                        color: Colors.teal,
                        size: 50,
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
        const SizedBox(height: 8),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: List.generate(
            _ads.length,
                (index) => AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              margin: const EdgeInsets.symmetric(horizontal: 4),
              width: _currentAdIndex == index ? 20 : 8,
              height: 8,
              decoration: BoxDecoration(
                color: _currentAdIndex == index
                    ? Colors.teal
                    : (isDarkMode ? Colors.grey[600] : Colors.grey),
                borderRadius: BorderRadius.circular(4),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPostsSection() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return _isGridView
        ? _buildGridPosts(isDarkMode)
        : _buildListPosts(isDarkMode);
  }

  Widget _buildGridPosts(bool isDarkMode) {
    return Padding(
      padding: const EdgeInsets.all(8),
      child: MasonryGridView.count(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        crossAxisCount: 2,
        mainAxisSpacing: 8,
        crossAxisSpacing: 8,
        itemCount: _posts.length,
        itemBuilder: (context, index) => FadeInUp(
          child: _buildPostCard(_posts[index], isGrid: true, isDarkMode: isDarkMode),
        ),
      ),
    );
  }

  Widget _buildListPosts(bool isDarkMode) {
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: _posts.length,
      itemBuilder: (context, index) => FadeInUp(
        child: _buildPostCard(_posts[index], isDarkMode: isDarkMode),
      ),
    );
  }

  Widget _buildPostCard(Post post, {bool isGrid = false, required bool isDarkMode}) {
    final TextEditingController commentController = TextEditingController();
    bool isCommentsExpanded = false;

    return GestureDetector(
      onDoubleTap: () => _likePost(post),
      child: Card(
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        elevation: 6,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        color: isDarkMode ? Colors.grey[850] : Colors.white,
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          transform: Matrix4.identity()..scale(post.isLiked ? 1.05 : 1.0),
          padding: const EdgeInsets.all(12.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  ElasticIn(
                    child: CircleAvatar(
                      radius: 20,
                      child: Text(post.user.isNotEmpty ? post.user[0] : 'P'),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          post.user,
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: isDarkMode ? Colors.white : Colors.black87,
                          ),
                        ),
                        Text(
                          timeago_pkg.format(post.createdAt, locale: 'ar'),
                          style: TextStyle(
                            fontSize: 12,
                            color: isDarkMode ? Colors.grey[400] : Colors.grey,
                          ),
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.more_vert, color: Colors.teal),
                    onPressed: () => _showPostOptions(post),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              if (post.image != null)
                ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: CachedNetworkImage(
                    imageUrl: '${_postService.baseUrl}${post.image!}',
                    height: isGrid ? 100 : 200,
                    width: double.infinity,
                    fit: BoxFit.cover,
                    placeholder: (context, url) => Container(
                      color: isDarkMode ? Colors.grey[800] : Colors.grey[300],
                      child: const Center(
                        child: CircularProgressIndicator(
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.teal),
                        ),
                      ),
                    ),
                    errorWidget: (context, url, error) => const Icon(
                      Icons.error,
                      color: Colors.teal,
                      size: 50,
                    ),
                  ),
                ),
              const SizedBox(height: 12),
              Text(
                post.content,
                style: TextStyle(
                  fontSize: 14,
                  color: isDarkMode ? Colors.white70 : Colors.black87,
                ),
              ),
              const SizedBox(height: 12),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      const Icon(Icons.visibility, size: 18, color: Colors.grey),
                      const SizedBox(width: 4),
                      Text(
                        '${post.views}',
                        style: TextStyle(
                          color: isDarkMode ? Colors.grey[400] : Colors.grey,
                        ),
                      ),
                    ],
                  ),
                  Row(
                    children: [
                      IconButton(
                        icon: AnimatedSwitcher(
                          duration: const Duration(milliseconds: 300),
                          child: Icon(
                            post.isLiked ? Icons.thumb_up : Icons.thumb_up_outlined,
                            key: ValueKey(post.isLiked),
                            color: post.isLiked ? Colors.teal : Colors.grey,
                          ),
                        ),
                        onPressed: () => _likePost(post),
                      ),
                      Text(
                        '${post.likes}',
                        style: TextStyle(
                          color: isDarkMode ? Colors.white70 : Colors.black87,
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.share, color: Colors.teal),
                        onPressed: () => _sharePost(post),
                      ),
                    ],
                  ),
                ],
              ),
              const Divider(),
              GestureDetector(
                onTap: () => setState(() => isCommentsExpanded = !isCommentsExpanded),
                child: Text(
                  "التعليقات (${post.comments.length})",
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: isDarkMode ? Colors.teal[300] : Colors.teal,
                  ),
                ),
              ),
              const SizedBox(height: 8),
              if (isCommentsExpanded || !isGrid)
                post.comments.isEmpty
                    ? Text(
                  'لا توجد تعليقات بعد',
                  style: TextStyle(
                    color: isDarkMode ? Colors.grey[400] : Colors.grey,
                  ),
                )
                    : AnimatedContainer(
                  duration: const Duration(milliseconds: 300),
                  constraints: BoxConstraints(
                    maxHeight: isCommentsExpanded ? 200 : 50,
                  ),
                  child: ListView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: isCommentsExpanded
                        ? post.comments.length
                        : (isGrid ? 1 : post.comments.length),
                    itemBuilder: (context, index) => Padding(
                      padding: const EdgeInsets.symmetric(vertical: 4.0),
                      child: Row(
                        children: [
                          const CircleAvatar(radius: 12, child: Text("U")),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              post.comments[index],
                              maxLines: isGrid ? 1 : null,
                              overflow: isGrid ? TextOverflow.ellipsis : null,
                              style: TextStyle(
                                color: isDarkMode ? Colors.white70 : Colors.black87,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              if (isCommentsExpanded || !isGrid) const SizedBox(height: 8),
              Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: commentController,
                      decoration: InputDecoration(
                        hintText: "أضف تعليقك...",
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(20),
                        ),
                        filled: true,
                        fillColor: isDarkMode ? Colors.grey[800] : Colors.teal[50],
                      ),
                      style: TextStyle(
                        color: isDarkMode ? Colors.white : Colors.black87,
                      ),
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.send, color: Colors.teal),
                    onPressed: () async {
                      if (commentController.text.isNotEmpty) {
                        try {
                          await _postService.commentPost(
                            Provider.of<AppState>(context, listen: false).token ?? '',
                            post.id,
                            commentController.text,
                          );
                          await _fetchPosts();
                          commentController.clear();
                          setState(() => isCommentsExpanded = true);
                        } catch (e) {
                          _showSnackBar('خطأ في إضافة التعليق: $e', Colors.red);
                        }
                      }
                    },
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _likePost(Post post) async {
    final originalIsLiked = post.isLiked;
    final originalLikes = post.likes;
    try {
      setState(() {
        post.isLiked = !post.isLiked;
        post.likes += post.isLiked ? 1 : -1;
      });
      await _postService.likePost(
        Provider.of<AppState>(context, listen: false).token ?? '',
        post.id,
        post.isLiked,
      );
    } catch (e) {
      setState(() {
        post.isLiked = originalIsLiked;
        post.likes = originalLikes;
      });
      _showSnackBar('خطأ في تحديث الإعجاب: $e', Colors.red);
    }
  }

  void _showPostOptions(Post post) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.report, color: Colors.red),
              title: const Text('الإبلاغ عن المنشور'),
              onTap: () {
                Navigator.pop(context);
                _showSnackBar('تم الإبلاغ عن المنشور', Colors.orange);
              },
            ),
            ListTile(
              leading: const Icon(Icons.bookmark_border, color: Colors.teal),
              title: const Text('حفظ المنشور'),
              onTap: () {
                Navigator.pop(context);
                _showSnackBar('تم حفظ المنشور', Colors.green);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showCreatePostDialog(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final TextEditingController contentController = TextEditingController();
    File? imageFile;
    String? selectedImageName;
    final appState = Provider.of<AppState>(context, listen: false);
    PostPrivacy selectedPrivacy = PostPrivacy.groupMembers;
    List<String> selectedUserIds = [];

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
          backgroundColor: isDarkMode ? Colors.grey[900] : Colors.white,
          title: Container(
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: isDarkMode
                    ? [Colors.teal[900]!, Colors.teal[700]!]
                    : [Colors.teal, Colors.tealAccent],
              ),
              borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
            ),
            child: const Text(
              "إضافة منشور جديد",
              style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
          ),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: contentController,
                  decoration: InputDecoration(
                    hintText: "اكتب منشورك هنا...",
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                    filled: true,
                    fillColor: isDarkMode ? Colors.grey[800] : Colors.grey[100],
                  ),
                  maxLines: 3,
                  style: TextStyle(
                    color: isDarkMode ? Colors.white : Colors.black87,
                  ),
                ),
                const SizedBox(height: 10),
                DropdownButtonFormField<PostPrivacy>(
                  value: selectedPrivacy,
                  decoration: InputDecoration(
                    labelText: 'خصوصية المنشور',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                    filled: true,
                    fillColor: isDarkMode ? Colors.grey[800] : Colors.grey[100],
                  ),
                  items: PostPrivacy.values.map((privacy) {
                    return DropdownMenuItem(
                      value: privacy,
                      child: Text(
                        privacy.label,
                        style: TextStyle(
                          color: isDarkMode ? Colors.white : Colors.black87,
                        ),
                      ),
                    );
                  }).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      setDialogState(() => selectedPrivacy = value);
                    }
                  },
                ),
                if (selectedPrivacy == PostPrivacy.specificMembers)
                  Padding(
                    padding: const EdgeInsets.only(top: 10),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'اختر الأعضاء الذين يمكنهم رؤية المنشور',
                          style: TextStyle(
                            color: isDarkMode ? Colors.white70 : Colors.black87,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            border: Border.all(
                              color: isDarkMode ? Colors.grey[700]! : Colors.grey[400]!,
                            ),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: selectedUserIds.isEmpty
                              ? const Text('لم يتم اختيار أي أعضاء بعد')
                              : Wrap(
                            spacing: 8,
                            children: List.generate(
                              selectedUserIds.length,
                                  (index) => Chip(
                                label: Text(selectedUserIds[index]),
                                onDeleted: () {
                                  setDialogState(() => selectedUserIds.removeAt(index));
                                },
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(height: 8),
                        TextButton.icon(
                          onPressed: () {
                            // TODO: Implement member selection dialog
                            _showSnackBar('سيتم تنفيذ اختيار الأعضاء قريبًا', Colors.blue);
                          },
                          icon: const Icon(Icons.person_add, size: 20),
                          label: const Text('اختر أعضاء'),
                        ),
                      ],
                    ),
                  ),
                const SizedBox(height: 10),
                ElevatedButton.icon(
                  onPressed: () async {
                    final picker = ImagePicker();
                    final pickedFile = await picker.pickImage(source: ImageSource.gallery);
                    if (pickedFile != null) {
                      final File file = File(pickedFile.path);
                      if (await file.length() > 5 * 1024 * 1024) {
                        _showSnackBar(
                          'حجم الصورة كبير جدًا (يجب أن يكون أقل من 5 ميجابايت)',
                          Colors.red,
                        );
                        return;
                      }
                      setDialogState(() {
                        imageFile = file;
                        selectedImageName = pickedFile.name;
                      });
                    }
                  },
                  icon: const Icon(Icons.photo, color: Colors.white),
                  label: Text(
                    selectedImageName ?? 'Select Image',
                    style: const TextStyle(color: Colors.white),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.teal,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                ),
                if (imageFile != null)
                  Padding(
                    padding: const EdgeInsets.only(top: 8),
                    child: Text(
                      'Selected image: $selectedImageName',
                      style: const TextStyle(color: Colors.green),
                    ),
                  ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(
                'إلغاء',
                style: TextStyle(
                  color: isDarkMode ? Colors.white70 : Colors.teal,
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () async {
                if (contentController.text.isEmpty && imageFile == null) {
                  _showSnackBar('الرجاء إدخال نص أو صورة', Colors.red);
                  return;
                }
                if (selectedPrivacy == PostPrivacy.specificMembers && selectedUserIds.isEmpty) {
                  _showSnackBar('الرجاء اختيار الأعضاء الذين يمكنهم رؤية المنشور', Colors.red);
                  return;
                }
                try {
                  await _postService.addPost(
                    appState.token ?? '',
                    widget.groupId,
                    contentController.text,
                    imageFile,
                    privacy: selectedPrivacy,
                    visibleToUserIds: selectedPrivacy == PostPrivacy.specificMembers ? selectedUserIds : null,
                  );
                  await _fetchPosts();
                  Navigator.pop(context);
                  _showSnackBar('تم النشر بنجاح', Colors.green);
                } catch (e) {
                  _showSnackBar('خطأ في النشر: $e', Colors.red);
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.teal,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text(
                'نشر',
                style: TextStyle(color: Colors.white),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _sharePost(Post post) {
    final shareText =
        'منشور من ${post.user} في مجموعة ${widget.groupName}: ${post.content}${post.image != null ? '\n${_postService.baseUrl}${post.image}' : ''}';
    Share.share(shareText);
    _showSnackBar(
      'تمت المشاركة بنجاح',
      Colors.green,
      SnackBarAction(
        label: 'مشاركة مرة أخرى',
        textColor: Colors.white,
        onPressed: () => Share.share(shareText),
      ),
    );
  }
}
