import 'dart:async';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';

/// مدير حالة الاتصال بالإنترنت
class ConnectivityManager {
  static final ConnectivityManager _instance = ConnectivityManager._internal();
  factory ConnectivityManager() => _instance;
  ConnectivityManager._internal();

  final Connectivity _connectivity = Connectivity();
  final StreamController<bool> _connectionStatusController = StreamController<bool>.broadcast();
  bool _isConnected = true;
  StreamSubscription<ConnectivityResult>? _connectivitySubscription;

  /// تدفق حالة الاتصال بالإنترنت
  Stream<bool> get connectionStatus => _connectionStatusController.stream;

  /// حالة الاتصال الحالية
  bool get isConnected => _isConnected;

  /// بدء مراقبة الاتصال بالإنترنت
  void initialize() {
    _checkInitialConnection();
    _connectivitySubscription = _connectivity.onConnectivityChanged.listen(_updateConnectionStatus);
  }

  /// التحقق من الاتصال الأولي
  Future<void> _checkInitialConnection() async {
    final connectivityResult = await _connectivity.checkConnectivity();
    _updateConnectionStatus(connectivityResult);
  }

  /// تحديث حالة الاتصال
  void _updateConnectionStatus(ConnectivityResult result) {
    _isConnected = result != ConnectivityResult.none;
    _connectionStatusController.add(_isConnected);
  }

  /// إيقاف مراقبة الاتصال بالإنترنت
  void dispose() {
    _connectivitySubscription?.cancel();
    _connectionStatusController.close();
  }

  /// عرض شريط إشعار بحالة الاتصال
  static void showConnectivitySnackBar(BuildContext context, bool isConnected) {
    final snackBar = SnackBar(
      content: Text(
        isConnected 
            ? 'تم استعادة الاتصال بالإنترنت'
            : 'لا يوجد اتصال بالإنترنت. سيتم استخدام البيانات المحلية.',
      ),
      backgroundColor: isConnected ? Colors.green : Colors.red,
      duration: const Duration(seconds: 3),
      behavior: SnackBarBehavior.floating,
    );
    
    ScaffoldMessenger.of(context).showSnackBar(snackBar);
  }
}
