class Idea {
  final String id;
  final String title;
  final String description;
  final String category;
  final String? filePath;
  final DateTime createdAt;
  final bool isFeatured;
  int likes;
  bool isLiked;

  Idea({
    required this.id,
    required this.title,
    required this.description,
    required this.category,
    this.filePath,
    required this.createdAt,
    this.isFeatured = false,
    this.likes = 0,
    this.isLiked = false,
  });

  factory Idea.fromJson(Map<String, dynamic> json) {
    return Idea(
      id: json['_id'] ?? '',
      title: json['title'] ?? 'بدون عنوان',
      description: json['description'] ?? 'بدون وصف',
      category: json['category'] ?? 'غير محدد',
      filePath: json['fileUrl'],
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toString()),
      isFeatured: json['isFeatured'] ?? false,
      likes: json['likes'] ?? 0,
      isLiked: json['isLiked'] ?? false,
    );
  }
}
