// Disease Controller
const DiseaseModel = require('../models/Disease.model');

// جلب الأمراض حسب التصنيف
exports.getDiseasesByCategory = async (req, res) => {
  try {
    const category = req.query.category;
    if (!category) {
      return res.status(400).json({ message: 'يرجى تحديد التصنيف' });
    }
    // ابحث عن الأمراض حسب التصنيف
    const diseases = await DiseaseModel.find({ category });
    res.json(diseases);
  } catch (err) {
    res.status(500).json({ message: 'حدث خطأ أثناء جلب الأمراض', error: err.message });
  }
};
