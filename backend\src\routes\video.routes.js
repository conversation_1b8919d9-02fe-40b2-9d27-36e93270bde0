const express = require('express');
const router = express.Router();
const { protect } = require('../middleware/authmiddleware');
const { trackActivity, ACTIVITY_POINTS } = require('../middleware/activity.middleware');
const {
  getVideos,
  createVideo,
  getVideo,
  updateVideo,
  deleteVideo,
  addComment,
  likeVideo,
  recordVideoWatch,
  getVideoById
} = require('../controllers/video.controller');

// Track video watching activity with points
router.post(
  '/:videoId/watch',
  protect,
  trackActivity(ACTIVITY_POINTS.WATCH_VIDEO, {
    referenceId: 'videoId',
    referenceType: 'video',
    description: 'Watched an educational video'
  }),
  recordVideoWatch
);

// Video routes
router.route('/')
  .get(protect, getVideos)
  .post(protect, createVideo);

router.route('/:id')
  .get(protect, getVideo || getVideoById) // Use the appropriate function based on your controller
  .put(protect, updateVideo)
  .delete(protect, deleteVideo);

// Comments and likes
router.post('/:id/comment', protect, addComment);
router.post('/:id/like', protect, likeVideo);

module.exports = router;
