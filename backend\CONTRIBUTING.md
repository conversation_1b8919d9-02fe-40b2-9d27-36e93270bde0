# Contributing to FULK Backend

Thank you for your interest in contributing to the FULK Backend project! We welcome all contributions from the community.

## Table of Contents

- [Code of Conduct](#code-of-conduct)
- [Getting Started](#getting-started)
- [Development Workflow](#development-workflow)
- [Code Style](#code-style)
- [Commit Message Guidelines](#commit-message-guidelines)
- [Pull Request Process](#pull-request-process)
- [Reporting Issues](#reporting-issues)
- [Feature Requests](#feature-requests)
- [License](#license)

## Code of Conduct

This project adheres to the [Contributor Covenant Code of Conduct](CODE_OF_CONDUCT.md). By participating, you are expected to uphold this code.

## Getting Started

1. **Fork the Repository**
   - Click the "Fork" button on the top right of the repository page

2. **Clone Your Fork**
   ```bash
   git clone https://github.com/your-username/fulk-backend.git
   cd fulk-backend
   ```

3. **Set Upstream Remote**
   ```bash
   git remote add upstream https://github.com/original-owner/fulk-backend.git
   ```

4. **Install Dependencies**
   ```bash
   npm install
   ```

5. **Set Up Environment Variables**
   ```bash
   cp .env.example .env
   # Update the .env file with your configuration
   ```

6. **Start Development Server**
   ```bash
   npm run dev
   ```

## Development Workflow

1. **Create a Branch**
   ```bash
   git checkout -b feature/your-feature-name
   # or
   git checkout -b fix/your-bug-fix
   ```

2. **Make Your Changes**
   - Follow the code style guidelines
   - Write tests for new features
   - Update documentation as needed

3. **Run Tests**
   ```bash
   npm test
   ```

4. **Lint Your Code**
   ```bash
   npm run lint
   ```

5. **Format Your Code**
   ```bash
   npm run format
   ```

6. **Commit Your Changes**
   ```bash
   git add .
   git commit -m "feat: add new feature"
   ```

7. **Push to Your Fork**
   ```bash
   git push origin your-branch-name
   ```

8. **Create a Pull Request**
   - Go to the original repository
   - Click "New Pull Request"
   - Select your branch
   - Fill out the PR template
   - Submit the PR

## Code Style

- Follow [Airbnb JavaScript Style Guide](https://github.com/airbnb/javascript)
- Use ES6+ features
- Use async/await instead of promises when possible
- Use meaningful variable and function names
- Write JSDoc comments for all public functions
- Keep functions small and focused

## Commit Message Guidelines

We follow the [Conventional Commits](https://www.conventionalcommits.org/) specification:

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

### Types

- `feat`: A new feature
- `fix`: A bug fix
- `docs`: Documentation only changes
- `style`: Changes that do not affect the meaning of the code (white-space, formatting, etc.)
- `refactor`: A code change that neither fixes a bug nor adds a feature
- `perf`: A code change that improves performance
- `test`: Adding missing tests or correcting existing tests
- `chore`: Changes to the build process or auxiliary tools and libraries

### Examples

```
feat(auth): add login with Google

Add Google OAuth authentication support

Closes #123
```

```
fix(api): handle null user in middleware

Prevent null reference error when user is not authenticated

Fixes #456
```

## Pull Request Process

1. Ensure all tests pass
2. Update the README.md with details of changes if needed
3. Update the CHANGELOG.md with details of changes
4. The PR must be reviewed by at least one maintainer
5. All CI checks must pass before merging
6. Use squash and merge when the PR is approved

## Reporting Issues

When reporting issues, please include:

1. A clear and descriptive title
2. Steps to reproduce the issue
3. Expected behavior
4. Actual behavior
5. Screenshots if applicable
6. Environment information (OS, Node.js version, etc.)

## Feature Requests

For feature requests, please:

1. Explain the problem you're trying to solve
2. Describe the proposed solution
3. Provide any alternative solutions you've considered
4. Include any additional context

## License

By contributing, you agree that your contributions will be licensed under the [MIT License](LICENSE).