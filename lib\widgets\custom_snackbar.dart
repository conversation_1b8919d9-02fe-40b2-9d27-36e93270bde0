import 'package:flutter/material.dart';

class CustomSnackbar extends StatelessWidget {
  final String message;
  final Color backgroundColor;
  final Color textColor;
  final IconData? icon;
  final Duration? duration;
  final double borderRadius;
  final double elevation;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final double fontSize;
  final FontWeight fontWeight;

  const CustomSnackbar({
    super.key,
    required this.message,
    this.backgroundColor = Colors.green,
    this.textColor = Colors.white,
    this.icon,
    this.duration,
    this.borderRadius = 8,
    this.elevation = 0,
    this.padding,
    this.margin,
    this.fontSize = 14,
    this.fontWeight = FontWeight.w500,
  });

  static const double kDefaultPadding = 16;
  static const double kDefaultMargin = 16;

  @override
  Widget build(BuildContext context) {
    final effectivePadding = padding ?? 
      const EdgeInsets.symmetric(horizontal: kDefaultPadding, vertical: kDefaultPadding / 2);
    
    final effectiveMargin = margin ?? 
      const EdgeInsets.all(kDefaultMargin);

    return Container(
      padding: effectivePadding,
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(borderRadius),
        boxShadow: [
          BoxShadow(
            color: backgroundColor.withOpacity(0.3),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          if (icon != null)
            Icon(
              icon,
              color: textColor,
              size: fontSize * 1.4,
            ),
          if (icon != null) const SizedBox(width: 8),
          Expanded(
            child: Text(
              message,
              style: TextStyle(
                color: textColor,
                fontSize: fontSize,
                fontWeight: fontWeight,
              ),
              maxLines: 3,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  static void show(BuildContext context, {
    required String message,
    Color backgroundColor = Colors.green,
    Color textColor = Colors.white,
    IconData? icon,
    Duration? duration,
    double borderRadius = 8,
    double elevation = 0,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
    double fontSize = 14,
    FontWeight fontWeight = FontWeight.w500,
  }) {
    if (message.isEmpty) return;

    // Sanitize message
    final sanitizedMessage = message.replaceAll(RegExp(r'[<>]'), '');

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: CustomSnackbar(
          message: sanitizedMessage,
          backgroundColor: backgroundColor,
          textColor: textColor,
          icon: icon,
          duration: duration,
          borderRadius: borderRadius,
          elevation: elevation,
          padding: padding,
          margin: margin,
          fontSize: fontSize,
          fontWeight: fontWeight,
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        behavior: SnackBarBehavior.floating,
        margin: margin ?? const EdgeInsets.all(kDefaultMargin),
        duration: duration ?? const Duration(seconds: 3),
      ),
    );
  }

  static void showError(BuildContext context, String message, {
    IconData? icon = Icons.error,
    Color backgroundColor = Colors.red,
    Color textColor = Colors.white,
    Duration? duration,
  }) {
    show(
      context,
      message: message,
      backgroundColor: backgroundColor,
      textColor: textColor,
      icon: icon,
      duration: duration,
    );
  }

  static void showSuccess(BuildContext context, String message, {
    IconData? icon = Icons.check_circle,
    Color backgroundColor = Colors.green,
    Color textColor = Colors.white,
    Duration? duration,
  }) {
    show(
      context,
      message: message,
      backgroundColor: backgroundColor,
      textColor: textColor,
      icon: icon,
      duration: duration,
    );
  }

  static void showInfo(BuildContext context, String message, {
    IconData? icon = Icons.info,
    Color backgroundColor = Colors.blue,
    Color textColor = Colors.white,
    Duration? duration,
  }) {
    show(
      context,
      message: message,
      backgroundColor: backgroundColor,
      textColor: textColor,
      icon: icon,
      duration: duration,
    );
  }
}
