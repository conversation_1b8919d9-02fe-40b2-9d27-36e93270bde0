// نقطة تجميع جميع المسارات
const express = require('express');
const router = express.Router();

router.use('/posts', require('./postroutes'));
router.use('/blocks', require('./blockroutes'));
router.use('/reports', require('./reportroutes'));
// Notification routes
router.use('/notifications', require('./notification.routes'));
router.use('/groupmessages', require('./groupmessageroutes'));
router.use('/religious', require('./religious'));

// مسارات مجموعات التربية والصناعات الغذائية
router.use('/fish-breeding-groups', require('./fishBreedingGroup.routes'));
router.use('/bird-breeding-groups', require('./birdBreedingPageroup.routes'));
router.use('/agricultural-plant-groups', require('./agriculturalPlantGroup.routes'));
router.use('/animal-breeding-groups', require('./animalBreedingGroup.routes'));
router.use('/home-food-industry-groups', require('./homeFoodIndustryGroup.routes'));
router.use('/maintenance-categories', require('./maintenanceCategory.routes'));
router.use('/diseases', require('./diseaseroutes'));
router.use('/poll', require('./pollroutes'));
router.use('/tasks', require('./taskroutes'));
router.use('/content', require('./contentroutes'));
router.use('/courses', require('./coursesroutes'));
// Profession routes
router.use('/professions', require('./professions.routes'));

// Share-Borrow System Routes
router.use('/share-borrow-items', require('./shareBorrowItem.routes'));
router.use('/sharing-groups', require('./sharingGroup.routes'));
router.use('/analytics', require('./analyticsEvent.routes'));
router.use('/uploads', require('./upload.routes'));
// User routes
router.use('/users', require('./user.routes.new'));
// Video routes
router.use('/videos', require('./video.routes.new'));
router.use('/classifieds', require('./classifiedsroutes'));
router.use('/members', require('./membersroutes'));
router.use('/favorites', require('./favoritesroutes'));
router.use('/skills', require('./skillsroutes'));
router.use('/matches', require('./matchesroutes'));
router.use('/clubs', require('./clubsroutes'));
router.use('/ads', require('./adsroutes'));

module.exports = router;
