const { ACTIVITY_POINTS, awardPointsForActivity } = require('../controllers/activity.controller');

// Middleware to track user activities and award points
const trackActivity = (activityType, options = {}) => {
  return async (req, res, next) => {
    try {
      // Skip if not authenticated or activity type not defined
      if (!req.user || !req.user.id || !activityType) {
        return next();
      }

      // Get reference ID from request params or body
      const referenceId = options.referenceId 
        ? (req.params[options.referenceId] || req.body[options.referenceId])
        : null;

      // Prepare activity options
      const activityOptions = {
        referenceId,
        referenceType: options.referenceType,
        description: options.description
      };

      // Award points for the activity
      const result = await awardPointsForActivity(
        req.user.id,
        activityType,
        activityOptions
      );

      // Attach points info to the request for potential use in the route handler
      if (result.success) {
        req.pointsAwarded = result.pointsAwarded;
        req.currentPoints = result.points;
      }
    } catch (error) {
      console.error('Activity tracking error:', error);
      // Don't fail the request if activity tracking fails
    }
    
    next();
  };
};

// Activity types mapping to HTTP methods and routes
const ACTIVITY_ROUTES = {
  [ACTIVITY_POINTS.CREATE_POST]: {
    method: 'POST',
    path: '/api/posts',
    referenceId: 'postId',
    referenceType: 'post',
    description: 'Creating a new post'
  },
  [ACTIVITY_POINTS.COMMENT]: {
    method: 'POST',
    path: '/api/comments',
    referenceId: 'commentId',
    referenceType: 'comment',
    description: 'Adding a comment'
  },
  [ACTIVITY_POINTS.LIKE]: {
    method: 'POST',
    path: '/api/likes',
    referenceId: 'likeId',
    referenceType: 'like',
    description: 'Liking content'
  },
  [ACTIVITY_POINTS.SHARE]: {
    method: 'POST',
    path: '/api/shares',
    referenceId: 'shareId',
    referenceType: 'share',
    description: 'Sharing content'
  },
  [ACTIVITY_POINTS.WATCH_VIDEO]: {
    method: 'POST',
    path: '/api/videos/:videoId/watch',
    referenceId: 'videoId',
    referenceType: 'video',
    description: 'Watching an educational video'
  },
  [ACTIVITY_POINTS.UPLOAD_CONTENT]: {
    method: 'POST',
    path: '/api/content',
    referenceId: 'contentId',
    referenceType: 'content',
    description: 'Uploading educational content'
  }
};

// Middleware factory to create activity tracking middleware for specific routes
const createActivityTracker = () => {
  return (req, res, next) => {
    // Find matching activity for the current route
    for (const [activityType, config] of Object.entries(ACTIVITY_ROUTES)) {
      if (
        req.method === config.method && 
        req.path.startsWith(config.path)
      ) {
        return trackActivity(activityType, {
          referenceId: config.referenceId,
          referenceType: config.referenceType,
          description: config.description
        })(req, res, next);
      }
    }
    
    // No matching activity, continue
    next();
  };
};

module.exports = {
  trackActivity,
  createActivityTracker,
  ACTIVITY_POINTS
};
