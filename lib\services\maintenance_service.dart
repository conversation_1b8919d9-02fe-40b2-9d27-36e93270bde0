import 'dart:convert';
import 'package:http/http.dart' as http;

// API configuration
class ApiConfig {
  static const String baseUrl = 'http://localhost:3000/api';
}

class MaintenanceService {
  final String baseUrl = ApiConfig.baseUrl;

  // Get all maintenance categories
  Future<List<dynamic>> getAllCategories() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/maintenance-categories'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception('Failed to load maintenance categories: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error connecting to server: $e');
    }
  }

  // Get a single maintenance category by ID
  Future<dynamic> getCategoryById(String id) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/maintenance-categories/$id'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception('Failed to load maintenance category: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error connecting to server: $e');
    }
  }

  // Create a new maintenance category
  Future<dynamic> createCategory(Map<String, dynamic> data) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/maintenance-categories'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(data),
      );

      if (response.statusCode == 201) {
        return jsonDecode(response.body);
      } else {
        throw Exception('Failed to create maintenance category: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error connecting to server: $e');
    }
  }

  // Update a maintenance category
  Future<dynamic> updateCategory(String id, Map<String, dynamic> data) async {
    try {
      final response = await http.put(
        Uri.parse('$baseUrl/maintenance-categories/$id'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(data),
      );

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception('Failed to update maintenance category: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error connecting to server: $e');
    }
  }

  // Delete a maintenance category
  Future<void> deleteCategory(String id) async {
    try {
      final response = await http.delete(
        Uri.parse('$baseUrl/maintenance-categories/$id'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode != 200) {
        throw Exception('Failed to delete maintenance category: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error connecting to server: $e');
    }
  }

  // Get tools by category ID
  Future<List<dynamic>> getToolsByCategoryId(String id) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/maintenance-categories/$id/tools'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception('Failed to load tools: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error connecting to server: $e');
    }
  }

  // Add tool to category
  Future<dynamic> addToolToCategory(String categoryId, Map<String, dynamic> toolData) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/maintenance-categories/$categoryId/tools'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(toolData),
      );

      if (response.statusCode == 201) {
        return jsonDecode(response.body);
      } else {
        throw Exception('Failed to add tool: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error connecting to server: $e');
    }
  }
}
