const express = require('express');
const router = express.Router();
const {
  getMedicalRecords,
  createMedicalRecord,
  getMedicalRecord,
  deleteMedicalRecord,
  getPatientRecords
} = require('../controllers/medicalRecordcontroller');
const { protect } = require('../middleware/authmiddleware');

// مسارات السجلات الطبية
router.route('/')
  .get(protect, getMedicalRecords)
  .post(protect, createMedicalRecord);

router.route('/:id')
  .get(protect, getMedicalRecord)
  .delete(protect, deleteMedicalRecord);

router.get('/patient/:username', protect, getPatientRecords);

module.exports = router;
