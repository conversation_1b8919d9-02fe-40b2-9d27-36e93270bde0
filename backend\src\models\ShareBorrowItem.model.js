const mongoose = require('mongoose');

const ImpactMediaSchema = new mongoose.Schema({
  url: { type: String, required: true },
  type: { type: String, required: true },
  uploadedAt: { type: Date, default: Date.now },
  description: { type: String, required: true }
});

const VerificationMediaSchema = new mongoose.Schema({
  url: { type: String, required: true },
  type: { type: String, required: true },
  uploadedAt: { type: Date, default: Date.now },
  uploaderId: { type: String, required: true }
});

const ShareBorrowItemSchema = new mongoose.Schema({
  title: { type: String, required: true },
  description: { type: String, required: true },
  location: { type: String, required: true },
  category: { type: String, required: true },
  fileUrl: { type: String, required: true },
  condition: { type: String, required: true },
  isAvailable: { type: Boolean, default: true },
  creatorId: { type: String, required: true },
  date: { type: Date, default: Date.now },
  maxBorrowDays: { type: Number, default: 7 },
  latitude: { type: Number, required: true },
  longitude: { type: Number, required: true },
  impactMedia: [ImpactMediaSchema],
  verificationMedia: [VerificationMediaSchema]
});

module.exports = mongoose.model('ShareBorrowItem', ShareBorrowItemSchema);