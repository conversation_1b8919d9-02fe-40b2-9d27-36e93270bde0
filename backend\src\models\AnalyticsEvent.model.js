const mongoose = require('mongoose');

const AnalyticsEventSchema = new mongoose.Schema({
  type: { 
    type: String, 
    required: true,
    enum: ['view', 'borrow', 'share', 'favorite', 'verify']
  },
  itemId: { type: String, required: true },
  userId: { type: String, required: true },
  timestamp: { type: Date, default: Date.now },
  metadata: { type: Object, default: {} }
});

module.exports = mongoose.model('AnalyticsEvent', AnalyticsEventSchema);
