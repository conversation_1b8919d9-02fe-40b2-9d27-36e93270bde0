class ApiConfig {
  // Base URL for API endpoints
  static const String baseUrl = String.fromEnvironment(
    'API_URL',
    defaultValue: 'http://localhost:8000/api'
  );

  // WebSocket URL
  static const String wsUrl = String.fromEnvironment(
    'WS_URL',
    defaultValue: 'ws://localhost:8000'
  );

  // API endpoints
  static const String authEndpoint = '$baseUrl/auth';
  static const String usersEndpoint = '$baseUrl/users';
  static const String postsEndpoint = '$baseUrl/posts';
  static const String messagesEndpoint = '$baseUrl/messages';
  static const String notificationsEndpoint = '$baseUrl/notifications';
  static const String uploadsEndpoint = '$baseUrl/uploads';
}
