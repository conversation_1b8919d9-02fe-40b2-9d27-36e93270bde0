import 'package:agora_rtc_engine/agora_rtc_engine.dart';
import 'package:permission_handler/permission_handler.dart';

class CallService {
  static const String appId = 'YOUR_AGORA_APP_ID'; // قم بتغيير هذا إلى معرف التطبيق الخاص بك من Agora.io
  
  RtcEngine? engine;
  bool _isInitialized = false;
  bool _isInCall = false;
  int? _currentCallId;
  
  bool get isInCall => _isInCall;
  int? get currentCallId => _currentCallId;

  // تهيئة محرك Agora
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    // التحقق من الأذونات
    await [Permission.microphone, Permission.camera].request();
    
    // إنشاء محرك RTC
    engine = createAgoraRtcEngine();
    await engine!.initialize(const RtcEngineContext(
      appId: appId,
      channelProfile: ChannelProfileType.channelProfileLiveBroadcasting,
    ));
    
    _isInitialized = true;
  }

  // بدء مكالمة جديدة
  Future<void> startCall({
    required String channelName,
    required bool isVideo,
    required Function(String userId) onUserJoined,
    required Function(String userId) onUserLeft,
    required Function() onLocalUserJoined,
    required Function(String error) onError,
  }) async {
    if (!_isInitialized) {
      await initialize();
    }
    
    try {
      // إعداد أحداث المكالمة
      engine!.registerEventHandler(RtcEngineEventHandler(
        onJoinChannelSuccess: (connection, elapsed) {
          _isInCall = true;
          onLocalUserJoined();
        },
        onUserJoined: (connection, remoteUid, elapsed) {
          onUserJoined(remoteUid.toString());
        },
        onUserOffline: (connection, remoteUid, reason) {
          onUserLeft(remoteUid.toString());
        },
        onError: (err, msg) {
          onError('خطأ: $err - $msg');
        },
      ));
      
      // تكوين المكالمة
      await engine!.setClientRole(role: ClientRoleType.clientRoleBroadcaster);
      
      if (isVideo) {
        await engine!.enableVideo();
        await engine!.startPreview();
      } else {
        await engine!.disableVideo();
      }
      
      // الانضمام إلى القناة
      await engine!.joinChannel(
        token: '', // استخدم رمز مميز إذا كان الأمان ممكّنًا
        channelId: channelName,
        uid: 0, // 0 يعني أن SDK سيقوم بتعيين معرف فريد
        options: const ChannelMediaOptions(
          channelProfile: ChannelProfileType.channelProfileLiveBroadcasting,
          clientRoleType: ClientRoleType.clientRoleBroadcaster,
        ),
      );
      
      _currentCallId = DateTime.now().millisecondsSinceEpoch;
    } catch (e) {
      onError('فشل بدء المكالمة: $e');
    }
  }

  // إنهاء المكالمة الحالية
  Future<void> endCall() async {
    if (_isInCall && engine != null) {
      await engine!.leaveChannel();
      await engine!.stopPreview();
      _isInCall = false;
      _currentCallId = null;
    }
  }

  // تبديل الكاميرا الأمامية/الخلفية
  Future<void> switchCamera() async {
    if (_isInCall && engine != null) {
      await engine!.switchCamera();
    }
  }

  // كتم/إلغاء كتم الصوت
  Future<void> toggleMute(bool mute) async {
    if (_isInCall && engine != null) {
      await engine!.muteLocalAudioStream(mute);
    }
  }

  // تفعيل/تعطيل الفيديو
  Future<void> toggleVideo(bool videoEnabled) async {
    if (_isInCall && engine != null) {
      if (videoEnabled) {
        await engine!.enableVideo();
        await engine!.startPreview();
      } else {
        await engine!.disableVideo();
        await engine!.stopPreview();
      }
    }
  }

  // التخلص من الموارد
  void dispose() {
    if (_isInCall) {
      endCall();
    }
    engine?.release();
    engine = null;
    _isInitialized = false;
  }
}
