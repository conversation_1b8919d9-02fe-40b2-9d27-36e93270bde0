import 'dart:convert';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:http/http.dart' as http;
import 'package:provider/provider.dart';
import 'appstate.dart';
import 'package:confetti/confetti.dart';
import 'package:animate_do/animate_do.dart';
import 'package:intl/intl.dart';
import 'package:flutter/foundation.dart';

// ===============================
// نماذج البيانات - Data Models
// ===============================

/// نموذج الشارة
class Badge {
  final String id;
  final String name;
  final String description;
  final String imageUrl;
  final String category;
  final BadgeRarity rarity;
  final List<BadgeCriteria> criteria;
  final int points;
  final bool isActive;
  final DateTime createdAt;

  Badge({
    required this.id,
    required this.name,
    required this.description,
    required this.imageUrl,
    required this.category,
    required this.rarity,
    required this.criteria,
    required this.points,
    this.isActive = true,
    required this.createdAt,
  });

  factory Badge.fromJson(Map<String, dynamic> json) {
    return Badge(
      id: json['_id'] ?? '',
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      imageUrl: json['imageUrl'] ?? '',
      category: json['category'] ?? '',
      rarity: BadgeRarity.values.firstWhere(
        (r) => r.name == json['rarity'],
        orElse: () => BadgeRarity.common,
      ),
      criteria: (json['criteria'] as List?)
              ?.map((item) => BadgeCriteria.fromJson(item))
              .toList() ??
          [],
      points: json['points'] ?? 0,
      isActive: json['isActive'] ?? true,
      createdAt: DateTime.tryParse(json['createdAt'] ?? '') ?? DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'name': name,
      'description': description,
      'imageUrl': imageUrl,
      'category': category,
      'rarity': rarity.name,
      'criteria': criteria.map((c) => c.toJson()).toList(),
      'points': points,
      'isActive': isActive,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  Color get rarityColor {
    switch (rarity) {
      case BadgeRarity.common:
        return Colors.grey;
      case BadgeRarity.uncommon:
        return Colors.green;
      case BadgeRarity.rare:
        return Colors.blue;
      case BadgeRarity.epic:
        return Colors.purple;
      case BadgeRarity.legendary:
        return Colors.orange;
    }
  }

  String get rarityText {
    switch (rarity) {
      case BadgeRarity.common:
        return 'عادية';
      case BadgeRarity.uncommon:
        return 'غير عادية';
      case BadgeRarity.rare:
        return 'نادرة';
      case BadgeRarity.epic:
        return 'ملحمية';
      case BadgeRarity.legendary:
        return 'أسطورية';
    }
  }
}

/// تصنيف ندرة الشارة
enum BadgeRarity {
  common,
  uncommon,
  rare,
  epic,
  legendary,
}

/// معايير الحصول على الشارة
class BadgeCriteria {
  final String type;
  final String field;
  final String operator;
  final dynamic value;
  final String description;

  BadgeCriteria({
    required this.type,
    required this.field,
    required this.operator,
    required this.value,
    required this.description,
  });

  factory BadgeCriteria.fromJson(Map<String, dynamic> json) {
    return BadgeCriteria(
      type: json['type'] ?? '',
      field: json['field'] ?? '',
      operator: json['operator'] ?? '',
      value: json['value'],
      description: json['description'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'type': type,
      'field': field,
      'operator': operator,
      'value': value,
      'description': description,
    };
  }
}

/// نموذج الإنجاز المكتسب
class EarnedBadge {
  final String id;
  final String badgeId;
  final String donorId;
  final DateTime earnedAt;
  final Map<String, dynamic> progress;
  final bool isNotified;
  final Badge? badge;

  EarnedBadge({
    required this.id,
    required this.badgeId,
    required this.donorId,
    required this.earnedAt,
    this.progress = const {},
    this.isNotified = false,
    this.badge,
  });

  factory EarnedBadge.fromJson(Map<String, dynamic> json) {
    return EarnedBadge(
      id: json['_id'] ?? '',
      badgeId: json['badgeId'] ?? '',
      donorId: json['donorId'] ?? '',
      earnedAt: DateTime.tryParse(json['earnedAt'] ?? '') ?? DateTime.now(),
      progress: Map<String, dynamic>.from(json['progress'] ?? {}),
      isNotified: json['isNotified'] ?? false,
      badge: json['badge'] != null ? Badge.fromJson(json['badge']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'badgeId': badgeId,
      'donorId': donorId,
      'earnedAt': earnedAt.toIso8601String(),
      'progress': progress,
      'isNotified': isNotified,
      'badge': badge?.toJson(),
    };
  }
}

/// إحصائيات المتبرع
class DonorStats {
  final String donorId;
  final int totalDonations;
  final double totalAmount;
  final int totalBadges;
  final int totalPoints;
  final int currentStreak;
  final int longestStreak;
  final DateTime firstDonation;
  final DateTime lastDonation;
  final Map<String, int> categoryDonations;
  final List<EarnedBadge> recentBadges;

  DonorStats({
    required this.donorId,
    required this.totalDonations,
    required this.totalAmount,
    required this.totalBadges,
    required this.totalPoints,
    required this.currentStreak,
    required this.longestStreak,
    required this.firstDonation,
    required this.lastDonation,
    this.categoryDonations = const {},
    this.recentBadges = const [],
  });

  factory DonorStats.empty() {
    final now = DateTime.now();
    return DonorStats(
      donorId: '',
      totalDonations: 0,
      totalAmount: 0,
      totalBadges: 0,
      totalPoints: 0,
      currentStreak: 0,
      longestStreak: 0,
      firstDonation: now,
      lastDonation: now,
    );
  }

  factory DonorStats.fromJson(Map<String, dynamic> json) {
    return DonorStats(
      donorId: json['donorId'] ?? '',
      totalDonations: json['totalDonations'] ?? 0,
      totalAmount: (json['totalAmount'] as num?)?.toDouble() ?? 0.0,
      totalBadges: json['totalBadges'] ?? 0,
      totalPoints: json['totalPoints'] ?? 0,
      currentStreak: json['currentStreak'] ?? 0,
      longestStreak: json['longestStreak'] ?? 0,
      firstDonation:
          DateTime.tryParse(json['firstDonation'] ?? '') ?? DateTime.now(),
      lastDonation:
          DateTime.tryParse(json['lastDonation'] ?? '') ?? DateTime.now(),
      categoryDonations: Map<String, int>.from(json['categoryDonations'] ?? {}),
      recentBadges: (json['recentBadges'] as List?)
              ?.map((item) => EarnedBadge.fromJson(item))
              .toList() ??
          [],
    );
  }

  double get averageDonation =>
      totalDonations > 0 ? totalAmount / totalDonations : 0.0;

  int get daysSinceFirstDonation =>
      DateTime.now().difference(firstDonation).inDays;

  String get donorLevel {
    if (totalPoints < 100) return 'مبتدئ';
    if (totalPoints < 500) return 'متوسط';
    if (totalPoints < 1000) return 'متقدم';
    if (totalPoints < 5000) return 'خبير';
    return 'أسطورة';
  }

  Color get levelColor {
    if (totalPoints < 100) return Colors.grey;
    if (totalPoints < 500) return Colors.green;
    if (totalPoints < 1000) return Colors.blue;
    if (totalPoints < 5000) return Colors.purple;
    return Colors.orange;
  }
}

/// تقدم الشارة
class BadgeProgress {
  final String badgeId;
  final Badge badge;
  final Map<String, dynamic> currentProgress;
  final Map<String, dynamic> requiredProgress;
  final double progressPercentage;
  final bool isCompleted;
  final DateTime? estimatedCompletion;

  BadgeProgress({
    required this.badgeId,
    required this.badge,
    required this.currentProgress,
    required this.requiredProgress,
    required this.progressPercentage,
    required this.isCompleted,
    this.estimatedCompletion,
  });

  factory BadgeProgress.fromJson(Map<String, dynamic> json) {
    return BadgeProgress(
      badgeId: json['badgeId'] ?? '',
      badge: Badge.fromJson(json['badge'] ?? {}),
      currentProgress: Map<String, dynamic>.from(json['currentProgress'] ?? {}),
      requiredProgress:
          Map<String, dynamic>.from(json['requiredProgress'] ?? {}),
      progressPercentage:
          (json['progressPercentage'] as num?)?.toDouble() ?? 0.0,
      isCompleted: json['isCompleted'] ?? false,
      estimatedCompletion: json['estimatedCompletion'] != null
          ? DateTime.tryParse(json['estimatedCompletion'])
          : null,
    );
  }
}

/// Service for managing badges and achievements
class BadgesService extends ChangeNotifier {
  final String baseUrl;
  final String authToken;
  
  // State
  bool _isLoading = false;
  List<EarnedBadge> _earnedBadges = [];
  List<BadgeProgress> _badgeProgress = [];
  DonorStats? _donorStats;
  List<Badge> _availableBadges = [];
  Map<String, BadgeProgress> _badgeProgressMap = {};
  String? _error;

  // Getters
  List<Badge> get availableBadges => _availableBadges;
  List<EarnedBadge> get earnedBadges => _earnedBadges;
  DonorStats? get donorStats => _donorStats;
  Map<String, BadgeProgress> get badgeProgress => _badgeProgressMap;
  bool get isLoading => _isLoading;
  String? get error => _error;

  BadgesService({required this.baseUrl, required this.authToken});
  
  // Notify listeners helper
  void _updateState() {
    notifyListeners();
  }
  
  // Set loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// جلب جميع الشارات المتاحة
  Future<List<Badge>> fetchAvailableBadges({
    String? category,
    BadgeRarity? rarity,
  }) async {
    try {
      final queryParams = <String, String>{};

      if (category != null && category.isNotEmpty) {
        queryParams['category'] = category;
      }

      if (rarity != null) {
        queryParams['rarity'] = rarity.name;
      }

      final uri =
          Uri.parse('$baseUrl/badges').replace(queryParameters: queryParams);

      final response = await http.get(
        uri,
        headers: {
          'Authorization': 'Bearer $authToken',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return (data['badges'] as List)
            .map((item) => Badge.fromJson(item))
            .toList();
      } else {
        throw Exception('فشل في جلب الشارات: ${response.body}');
      }
    } catch (e) {
      throw Exception('خطأ في الشبكة: $e');
    }
  }

  /// جلب الشارات المكتسبة للمتبرع
  Future<List<EarnedBadge>> fetchEarnedBadges(String donorId) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/donors/$donorId/badges'),
        headers: {
          'Authorization': 'Bearer $authToken',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return (data['badges'] as List)
            .map((item) => EarnedBadge.fromJson(item))
            .toList();
      } else {
        throw Exception('فشل في جلب الشارات المكتسبة: ${response.body}');
      }
    } catch (e) {
      throw Exception('خطأ في الشبكة: $e');
    }
  }

  /// جلب إحصائيات المتبرع
  Future<DonorStats> fetchDonorStats(String donorId) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/donors/$donorId/stats'),
        headers: {
          'Authorization': 'Bearer $authToken',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        return DonorStats.fromJson(jsonDecode(response.body));
      } else {
        throw Exception('فشل في جلب إحصائيات المتبرع: ${response.body}');
      }
    } catch (e) {
      throw Exception('خطأ في الشبكة: $e');
    }
  }

  /// جلب تقدم الشارات
  Future<List<BadgeProgress>> fetchBadgeProgress(String donorId) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/donors/$donorId/badge-progress'),
        headers: {
          'Authorization': 'Bearer $authToken',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return (data['progress'] as List)
            .map((item) => BadgeProgress.fromJson(item))
            .toList();
      } else {
        throw Exception('فشل في جلب تقدم الشارات: ${response.body}');
      }
    } catch (e) {
      throw Exception('خطأ في الشبكة: $e');
    }
  }

  /// تسجيل قراءة إشعار الشارة
  Future<void> markBadgeNotificationRead(String earnedBadgeId) async {
    try {
      final response = await http.put(
        Uri.parse('$baseUrl/earned-badges/$earnedBadgeId/read'),
        headers: {
          'Authorization': 'Bearer $authToken',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode != 200) {
        throw Exception('فشل في تسجيل قراءة الإشعار: ${response.body}');
      }
    } catch (e) {
      throw Exception('خطأ في الشبكة: $e');
    }
  }

  /// جلب لوحة المتصدرين
  Future<List<DonorStats>> fetchLeaderboard({
    String sortBy = 'totalPoints',
    int limit = 50,
  }) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/leaderboard?sortBy=$sortBy&limit=$limit'),
        headers: {
          'Authorization': 'Bearer $authToken',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return (data['leaderboard'] as List)
            .map((item) => DonorStats.fromJson(item))
            .toList();
      } else {
        throw Exception('فشل في جلب لوحة المتصدرين: ${response.body}');
      }
    } catch (e) {
      throw Exception('خطأ في الشبكة: $e');
    }
  }
}

// ===============================
// مكونات واجهة المستخدم - UI Components
// ===============================

/// مكون عرض الشارة
class BadgeWidget extends StatelessWidget {
  final Badge badge;
  final bool isEarned;
  final DateTime? earnedDate;
  final VoidCallback? onTap;
  final double size;

  const BadgeWidget({
    Key? key,
    required this.badge,
    this.isEarned = false,
    this.earnedDate,
    this.onTap,
    this.size = 80.0,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          border: Border.all(
            color: badge.rarityColor,
            width: 3,
          ),
          boxShadow: isEarned
              ? [
                  BoxShadow(
                    color: badge.rarityColor.withOpacity(0.3),
                    blurRadius: 8,
                    spreadRadius: 2,
                  ),
                ]
              : null,
        ),
        child: Stack(
          children: [
            // Badge image
            ClipOval(
              child: Container(
                width: size,
                height: size,
                color: isEarned ? null : Colors.grey[300],
                child: badge.imageUrl.isNotEmpty
                    ? Image.network(
                        badge.imageUrl,
                        fit: BoxFit.cover,
                        color: isEarned ? null : Colors.grey,
                        colorBlendMode: isEarned ? null : BlendMode.saturation,
                        errorBuilder: (context, error, stackTrace) =>
                            _buildDefaultBadgeIcon(),
                      )
                    : _buildDefaultBadgeIcon(),
              ),
            ),

            // Rarity indicator
            Positioned(
              top: 0,
              right: 0,
              child: Container(
                width: size * 0.3,
                height: size * 0.3,
                decoration: BoxDecoration(
                  color: badge.rarityColor,
                  shape: BoxShape.circle,
                  border: Border.all(color: Colors.white, width: 1),
                ),
                child: Icon(
                  _getRarityIcon(),
                  color: Colors.white,
                  size: size * 0.15,
                ),
              ),
            ),

            // Lock overlay for unearned badges
            if (!isEarned)
              Container(
                width: size,
                height: size,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: Colors.black.withOpacity(0.5),
                ),
                child: Icon(
                  Icons.lock,
                  color: Colors.white,
                  size: size * 0.3,
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildDefaultBadgeIcon() {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: badge.rarityColor.withOpacity(0.1),
      ),
      child: Icon(
        Icons.emoji_events,
        color: badge.rarityColor,
        size: size * 0.5,
      ),
    );
  }

  IconData _getRarityIcon() {
    switch (badge.rarity) {
      case BadgeRarity.common:
        return Icons.circle;
      case BadgeRarity.uncommon:
        return Icons.star;
      case BadgeRarity.rare:
        return Icons.diamond;
      case BadgeRarity.epic:
        return Icons.auto_awesome;
      case BadgeRarity.legendary:
        return Icons.emoji_events;
    }
  }
}

/// مكون عرض تقدم الشارة
class BadgeProgressWidget extends StatelessWidget {
  final BadgeProgress progress;

  const BadgeProgressWidget({
    Key? key,
    required this.progress,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                BadgeWidget(
                  badge: progress.badge,
                  isEarned: progress.isCompleted,
                  size: 60,
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        progress.badge.name,
                        style:
                            Theme.of(context).textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        progress.badge.description,
                        style: Theme.of(context).textTheme.bodySmall,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Chip(
                            label: Text(progress.badge.rarityText),
                            backgroundColor:
                                progress.badge.rarityColor.withOpacity(0.1),
                            labelStyle: TextStyle(
                              color: progress.badge.rarityColor,
                              fontSize: 12,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            '${progress.badge.points} نقطة',
                            style:
                                Theme.of(context).textTheme.bodySmall?.copyWith(
                                      fontWeight: FontWeight.w500,
                                    ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Progress bar
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'التقدم',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.w500,
                          ),
                    ),
                    Text(
                      '${(progress.progressPercentage * 100).toStringAsFixed(1)}%',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.w500,
                            color: progress.isCompleted ? Colors.green : null,
                          ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                LinearProgressIndicator(
                  value: progress.progressPercentage,
                  backgroundColor: Colors.grey[300],
                  valueColor: AlwaysStoppedAnimation<Color>(
                    progress.isCompleted
                        ? Colors.green
                        : progress.badge.rarityColor,
                  ),
                ),
                if (progress.estimatedCompletion != null &&
                    !progress.isCompleted) ...[
                  const SizedBox(height: 8),
                  Text(
                    'الإنجاز المتوقع: ${DateFormat('dd/MM/yyyy').format(progress.estimatedCompletion!)}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.grey[600],
                        ),
                  ),
                ],
              ],
            ),

            // Progress details
            if (progress.currentProgress.isNotEmpty) ...[
              const SizedBox(height: 16),
              const Divider(),
              const SizedBox(height: 8),
              ...progress.currentProgress.entries.map((entry) {
                final required = progress.requiredProgress[entry.key] ?? 0;
                final current = entry.value ?? 0;

                return Padding(
                  padding: const EdgeInsets.symmetric(vertical: 4),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        _getProgressLabel(entry.key),
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                      Text(
                        '$current / $required',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              fontWeight: FontWeight.w500,
                            ),
                      ),
                    ],
                  ),
                );
              }).toList(),
            ],
          ],
        ),
      ),
    );
  }

  String _getProgressLabel(String key) {
    switch (key) {
      case 'totalDonations':
        return 'إجمالي التبرعات';
      case 'totalAmount':
        return 'إجمالي المبلغ';
      case 'consecutiveDays':
        return 'أيام متتالية';
      case 'categoriesCount':
        return 'عدد الفئات';
      default:
        return key;
    }
  }
}

/// مكون إحصائيات المتبرع
class DonorStatsWidget extends StatelessWidget {
  final DonorStats stats;

  const DonorStatsWidget({
    Key? key,
    required this.stats,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Level and points
            Row(
              children: [
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: stats.levelColor,
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    stats.donorLevel,
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  '${stats.totalPoints} نقطة',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
                const Spacer(),
                Icon(
                  Icons.emoji_events,
                  color: stats.levelColor,
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Stats grid
            GridView.count(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisCount: 2,
              childAspectRatio: 2.5,
              mainAxisSpacing: 8,
              crossAxisSpacing: 8,
              children: [
                _buildStatItem(
                  context,
                  'إجمالي التبرعات',
                  stats.totalDonations.toString(),
                  Icons.volunteer_activism,
                  Colors.blue,
                ),
                _buildStatItem(
                  context,
                  'إجمالي المبلغ',
                  '${stats.totalAmount.toStringAsFixed(0)} ريال',
                  Icons.attach_money,
                  Colors.green,
                ),
                _buildStatItem(
                  context,
                  'الشارات المكتسبة',
                  stats.totalBadges.toString(),
                  Icons.military_tech,
                  Colors.orange,
                ),
                _buildStatItem(
                  context,
                  'السلسلة الحالية',
                  '${stats.currentStreak} يوم',
                  Icons.local_fire_department,
                  Colors.red,
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Additional stats
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildAdditionalStat(
                  context,
                  'متوسط التبرع',
                  '${stats.averageDonation.toStringAsFixed(0)} ريال',
                ),
                _buildAdditionalStat(
                  context,
                  'أطول سلسلة',
                  '${stats.longestStreak} يوم',
                ),
                _buildAdditionalStat(
                  context,
                  'منذ البداية',
                  '${stats.daysSinceFirstDonation} يوم',
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(
    BuildContext context,
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 4),
          Text(
            value,
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
          ),
          Text(
            label,
            style: Theme.of(context).textTheme.bodySmall,
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildAdditionalStat(
    BuildContext context,
    String label,
    String value,
  ) {
    return Column(
      children: [
        Text(
          value,
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
              ),
        ),
      ],
    );
  }
}

/// شاشة الشارات والإنجازات
class BadgesScreen extends StatefulWidget {
  final String donorId;

  const BadgesScreen({
    Key? key,
    required this.donorId,
  }) : super(key: key);

  @override
  State<BadgesScreen> createState() => _BadgesScreenState();
}

class _BadgesScreenState extends State<BadgesScreen> with TickerProviderStateMixin {
  late final TabController _tabController;
  late final ConfettiController _confettiController;
  late final BadgesService _badgesService;
  bool _isLoading = false;
  String? _error;
  
  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _confettiController = ConfettiController(duration: const Duration(seconds: 3));
    
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final appState = Provider.of<AppState>(context, listen: false);
      _badgesService = BadgesService(
        baseUrl: 'https://your-api-url.com/api',
        authToken: appState.authToken ?? '',
      );
      _loadData();
    });
  }
  
  @override
  void dispose() {
    _tabController.dispose();
    _confettiController.dispose();
    super.dispose();
  }



  Future<void> _loadData() async {
    if (!mounted) return;
    
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final badgesService = Provider.of<BadgesService>(context, listen: false);
      await Future.wait([
        badgesService.fetchEarnedBadges(widget.donorId),
        badgesService.fetchBadgeProgress(widget.donorId),
        badgesService.fetchDonorStats(widget.donorId),
      ]);

      if (!mounted) return;
      
      // Check for new badges and show celebration
      _checkForNewBadges(badgesService.earnedBadges);
      
      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      if (!mounted) return;
      
      setState(() {
        _isLoading = false;
        _error = e.toString();
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في جلب البيانات: $e')),
        );
      }
    }
  }

  void _checkForNewBadges(List<EarnedBadge> earnedBadges) {
    if (!mounted) return;
    
    final newBadges = earnedBadges.where((badge) => !badge.isNotified).toList();
    if (newBadges.isNotEmpty) {
      _showBadgeEarnedDialog(newBadges.first);
      _confettiController.play();
    }
  }

  void _showBadgeEarnedDialog(EarnedBadge earnedBadge) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            FadeInDown(
              child: BadgeWidget(
                badge: earnedBadge.badge!,
                isEarned: true,
                size: 120,
              ),
            ),
            const SizedBox(height: 16),
            FadeInUp(
              child: Text(
                'تهانينا!',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: earnedBadge.badge!.rarityColor,
                    ),
              ),
            ),
            const SizedBox(height: 8),
            FadeInUp(
              delay: const Duration(milliseconds: 200),
              child: Text(
                'لقد حصلت على شارة "${earnedBadge.badge!.name}"',
                style: Theme.of(context).textTheme.titleMedium,
                textAlign: TextAlign.center,
              ),
            ),
            const SizedBox(height: 8),
            FadeInUp(
              delay: const Duration(milliseconds: 400),
              child: Text(
                earnedBadge.badge!.description,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.grey[600],
                    ),
                textAlign: TextAlign.center,
              ),
            ),
            const SizedBox(height: 16),
            FadeInUp(
              delay: const Duration(milliseconds: 600),
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: earnedBadge.badge!.rarityColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  '+${earnedBadge.badge!.points} نقطة',
                  style: TextStyle(
                    color: earnedBadge.badge!.rarityColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ],
        ),
        actions: [
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _markBadgeAsRead(earnedBadge);
            },
            child: const Text('رائع!'),
          ),
        ],
      ),
    );
  }

  Future<void> _markBadgeAsRead(EarnedBadge earnedBadge) async {
    try {
      await _badgesService.markBadgeNotificationRead(earnedBadge.id);
      // The BadgesService will update its state and notify listeners
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم تحديث حالة الإشعار')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تحديث حالة الإشعار: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer2<BadgesService, AppState>(
      builder: (context, badgesService, appState, _) {
        return Scaffold(
          appBar: AppBar(
            title: const Text('الشارات والإنجازات'),
            bottom: TabBar(
              controller: _tabController,
              tabs: const [
                Tab(icon: Icon(Icons.emoji_events), text: 'الشارات'),
                Tab(icon: Icon(Icons.assessment), text: 'التقدم'),
                Tab(icon: Icon(Icons.analytics), text: 'إحصائياتي'),
              ],
            ),
          ),
          body: TabBarView(
            controller: _tabController,
            children: [
              _buildEarnedBadgesTab(badgesService.earnedBadges),
              _buildProgressTab(badgesService.badgeProgress.values.toList()),
              _buildStatsTab(badgesService.donorStats ?? DonorStats.empty()),
            ],
          ),
        );
      },
    );
  }

  Widget _buildStatsTab(DonorStats donorStats) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          DonorStatsWidget(stats: donorStats),
          if (donorStats.recentBadges.isNotEmpty) ...[
            const SizedBox(height: 16),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'الشارات الأخيرة',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                    const SizedBox(height: 12),
                    SizedBox(
                      height: 100,
                      child: ListView.builder(
                        scrollDirection: Axis.horizontal,
                        itemCount: donorStats.recentBadges.length,
                        itemBuilder: (context, index) {
                          final earnedBadge = donorStats.recentBadges[index];
                          return Padding(
                            padding: const EdgeInsets.only(right: 12),
                            child: Column(
                              children: [
                                BadgeWidget(
                                  badge: earnedBadge.badge!,
                                  isEarned: true,
                                  earnedDate: earnedBadge.earnedAt,
                                  size: 60,
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  DateFormat('dd/MM')
                                      .format(earnedBadge.earnedAt),
                                  style: Theme.of(context).textTheme.bodySmall,
                                ),
                              ],
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildEarnedBadgesTab(List<EarnedBadge> earnedBadges) {
    if (earnedBadges.isEmpty) {
      return const Center(child: Text('لا توجد شارات مكتسبة بعد'));
    }

    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        childAspectRatio: 0.8,
        mainAxisSpacing: 16,
        crossAxisSpacing: 16,
      ),
      itemCount: earnedBadges.length,
      itemBuilder: (context, index) {
        final earnedBadge = earnedBadges[index];
        return FadeInUp(
          delay: Duration(milliseconds: index * 100),
          child: Column(
            children: [
              BadgeWidget(
                badge: earnedBadge.badge!,
                isEarned: true,
                earnedDate: earnedBadge.earnedAt,
                onTap: () => _showBadgeDetails(earnedBadge),
              ),
              const SizedBox(height: 8),
              Text(
                earnedBadge.badge!.name,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              Text(
                DateFormat('dd/MM/yyyy').format(earnedBadge.earnedAt),
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                    ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildProgressTab(List<BadgeProgress> badgeProgress) {
    if (badgeProgress.isEmpty) {
      return const Center(child: Text('لا توجد شارات متاحة للتقدم'));
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: badgeProgress.length,
      itemBuilder: (context, index) {
        final progress = badgeProgress[index];
        return FadeInUp(
          delay: Duration(milliseconds: index * 100),
          child: Padding(
            padding: const EdgeInsets.only(bottom: 16),
            child: BadgeProgressWidget(progress: progress),
          ),
        );
      },
    );
  }

  void _showBadgeDetails(EarnedBadge earnedBadge) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            BadgeWidget(
              badge: earnedBadge.badge!,
              isEarned: true,
              size: 100,
            ),
            const SizedBox(height: 16),
            Text(
              earnedBadge.badge!.name,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              earnedBadge.badge!.description,
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                Column(
                  children: [
                    Text(
                      earnedBadge.badge!.rarityText,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: earnedBadge.badge!.rarityColor,
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                    const Text('الندرة'),
                  ],
                ),
                Column(
                  children: [
                    Text(
                      '${earnedBadge.badge!.points}',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                    const Text('النقاط'),
                  ],
                ),
                Column(
                  children: [
                    Text(
                      DateFormat('dd/MM/yyyy').format(earnedBadge.earnedAt),
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                    const Text('تاريخ الحصول'),
                  ],
                ),
              ],
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }
}
