const RideRequest = require('../models/rideRequest.model');

exports.createRideRequest = async (req, res) => {
  try {
    const ride = await RideRequest.create(req.body);
    res.status(201).json(ride);
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
};

exports.getAllRides = async (req, res) => {
  try {
    const rides = await RideRequest.find();
    res.json(rides);
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};

exports.updateRideStatus = async (req, res) => {
  try {
    const { status, driverId } = req.body;
    const ride = await RideRequest.findByIdAndUpdate(
      req.params.id,
      { status, driverId },
      { new: true }
    );
    res.json(ride);
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
};
