import 'package:flutter/material.dart';
import 'donation_features.dart';

class DonationFeaturesScreen extends StatelessWidget {
  const DonationFeaturesScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('ميزات التبرع'),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            _buildHeader(),
            const SizedBox(height: 16),
            DonationFeaturesGrid(
              features: DonationFeatures.allFeatures,
              onFeatureTap: (feature) => _handleFeatureTap(context, feature),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(16),
          bottomRight: Radius.circular(16),
        ),
      ),
      child: Column(
        children: [
          const Text(
            'اكتشف طرق التبرع المتاحة',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.blue,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          const Text(
            'اختر طريقة التبرع التي تناسبك وساهم في إحداث فرق',
            style: TextStyle(
              fontSize: 14,
              color: Colors.blueGrey,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: () {
              // Navigate to donation history
            },
            icon: const Icon(Icons.history, size: 20),
            label: const Text('سجل التبرعات السابقة'),
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(24),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _handleFeatureTap(BuildContext context, DonationFeature feature) {
    switch (feature.id) {
      case 'thank_you_certificate':
        _showThankYouCertificate(context);
        break;
      case 'remaining_amount':
        _showRemainingAmount(context);
        break;
      case 'success_stories':
        _showSuccessStories(context);
        break;
      case 'volunteer_time':
        _showVolunteerOpportunities(context);
        break;
      case 'donate_goods':
        _showDonateGoods(context);
        break;
      case 'crowdfunding':
        _showCrowdfundingCampaigns(context);
        break;
      case 'donation_challenges':
        _showDonationChallenges(context);
        break;
      case 'donation_history':
        _showDonationHistory(context);
        break;
    }
  }

  void _showThankYouCertificate(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('شهادة شكر'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(Icons.verified, size: 60, color: Colors.green),
            const SizedBox(height: 16),
            const Text(
              'شكراً لتبرعك السخي!',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            const Text(
              'لقد ساهمت في إحداث فرق حقيقي في حياة المحتاجين.',
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                TextButton(
                  onPressed: () {
                    // Share certificate
                    Navigator.pop(context);
                  },
                  child: const Text('مشاركة الشهادة'),
                ),
                ElevatedButton(
                  onPressed: () {
                    // Download certificate
                    Navigator.pop(context);
                  },
                  child: const Text('حفظ الشهادة'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _showRemainingAmount(BuildContext context) {
    // Implement remaining amount calculator
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'حاسبة المبلغ المتبقي',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            TextField(
              decoration: const InputDecoration(
                labelText: 'الهدف المطلوب',
                prefixIcon: Icon(Icons.flag),
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.number,
            ),
            const SizedBox(height: 12),
            TextField(
              decoration: const InputDecoration(
                labelText: 'المبلغ المحقق',
                prefixIcon: Icon(Icons.attach_money),
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.number,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                // Calculate and show result
                Navigator.pop(context);
              },
              child: const Text('احسب المبلغ المتبقي'),
            ),
          ],
        ),
      ),
    );
  }

  void _showSuccessStories(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => Scaffold(
          appBar: AppBar(title: const Text('قصص النجاح')),
          body: ListView(
            padding: const EdgeInsets.all(16),
            children: [
              _buildSuccessStoryCard(
                'مشروع توفير المياه النظيفة',
                'تم توفير المياه النظيفة لأكثر من 1000 عائلة في المناطق النائية.',
                'assets/images/water_project.jpg',
                '75,000 ر.س',
              ),
              const SizedBox(height: 16),
              _buildSuccessStoryCard(
                'بناء مدرسة القرية',
                'تم بناء مدرسة جديدة تستوعب 200 طالب في قرية نائية.',
                'assets/images/school_project.jpg',
                '500,000 ر.س',
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSuccessStoryCard(
    String title,
    String description,
    String imageUrl,
    String amount,
  ) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          ClipRRect(
            borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
            child: Image.asset(
              imageUrl,
              height: 150,
              fit: BoxFit.cover,
              errorBuilder: (_, __, ___) => Container(
                height: 150,
                color: Colors.grey[200],
                child: const Icon(Icons.image_not_supported, size: 48),
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(description),
                const SizedBox(height: 12),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'المبلغ المحصل: $amount',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.green,
                      ),
                    ),
                    const Text(
                      'تم التمويل بالكامل',
                      style: TextStyle(
                        color: Colors.green,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                const LinearProgressIndicator(
                  value: 1.0,
                  backgroundColor: Colors.grey,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.green),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _showVolunteerOpportunities(BuildContext context) {
    // Implement volunteer opportunities screen
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('فرص التطوع المتاحة'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView(
            shrinkWrap: true,
            children: [
              _buildVolunteerOpportunity(
                'التدريس للأطفال',
                'مطلوب متطوعين للتدريس في مركز تعليمي',
                Icons.school,
              ),
              const Divider(),
              _buildVolunteerOpportunity(
                'توزيع الطعام',
                'مساعدة في توزيع وجبات على الأسر المحتاجة',
                Icons.restaurant,
              ),
              const Divider(),
              _buildVolunteerOpportunity(
                'تنظيف الحدائق',
                'حملة تنظيف للحدائق العامة',
                Icons.eco,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  Widget _buildVolunteerOpportunity(
    String title,
    String description,
    IconData icon,
  ) {
    return ListTile(
      leading: Icon(icon, size: 32, color: Colors.blue),
      title: Text(title, style: const TextStyle(fontWeight: FontWeight.bold)),
      subtitle: Text(description),
      trailing: ElevatedButton(
        onPressed: () {
          // Handle volunteer sign up
        },
        style: ElevatedButton.styleFrom(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        ),
        child: const Text('تطوع الآن'),
      ),
    );
  }

  void _showDonateGoods(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => Scaffold(
          appBar: AppBar(title: const Text('التبرع بالسلع')),
          body: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                const Text(
                  'ما الذي ترغب في التبرع به؟',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 16),
                Expanded(
                  child: GridView.count(
                    crossAxisCount: 2,
                    childAspectRatio: 1.2,
                    crossAxisSpacing: 16,
                    mainAxisSpacing: 16,
                    children: [
                      _buildDonationItem('ملابس', Icons.checkroom, Colors.blue),
                      _buildDonationItem('أثاث', Icons.chair, Colors.orange),
                      _buildDonationItem('أجهزة كهربائية', Icons.kitchen, Colors.green),
                      _buildDonationItem('كتب', Icons.menu_book, Colors.purple),
                      _buildDonationItem('ألعاب أطفال', Icons.toys, Colors.pink),
                      _buildDonationItem('أخرى', Icons.more_horiz, Colors.grey),
                    ],
                  ),
                ),
                const SizedBox(height: 16),
                ElevatedButton.icon(
                  onPressed: () {
                    // Navigate to donation form
                  },
                  icon: const Icon(Icons.add_circle_outline),
                  label: const Text('إضافة تبرع جديد'),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDonationItem(String title, IconData icon, Color color) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: () {
          // Handle item tap
        },
        borderRadius: BorderRadius.circular(12),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 40, color: color),
            const SizedBox(height: 8),
            Text(
              title,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ],
        ),
      ),
    );
  }

  void _showCrowdfundingCampaigns(BuildContext context) {
    // Implement crowdfunding campaigns screen
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.9,
        minChildSize: 0.5,
        maxChildSize: 0.9,
        expand: false,
        builder: (context, scrollController) => Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              const Center(
                child: Text(
                  'حملات التمويل الجماعي',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              const SizedBox(height: 16),
              Expanded(
                child: ListView(
                  controller: scrollController,
                  children: [
                    _buildCampaignCard(
                      'بناء مستشفى',
                      'توفير الرعاية الصحية للمناطق النائية',
                      '2,500,000 ر.س',
                      '1,200,000 ر.س',
                      0.48,
                    ),
                    const SizedBox(height: 12),
                    _buildCampaignCard(
                      'تعليم الأطفال',
                      'توفير التعليم للأطفال المحرومين',
                      '500,000 ر.س',
                      '350,000 ر.س',
                      0.7,
                    ),
                    const SizedBox(height: 12),
                    _buildCampaignCard(
                      'مشروع مياه نظيفة',
                      'حفر آبار للمجتمعات المحرومة',
                      '1,000,000 ر.س',
                      '750,000 ر.س',
                      0.75,
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () {
                  // Start a new campaign
                },
                child: const Text('إطلاق حملة جديدة'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCampaignCard(
    String title,
    String description,
    String target,
    String raised,
    double progress,
  ) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(description),
            const SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('الهدف', style: TextStyle(color: Colors.grey)),
                    Text(target, style: const TextStyle(fontWeight: FontWeight.bold)),
                  ],
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    const Text('المحصل', style: TextStyle(color: Colors.grey)),
                    Text(raised, style: const TextStyle(fontWeight: FontWeight.bold)),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 8),
            LinearProgressIndicator(
              value: progress,
              backgroundColor: Colors.grey[200],
              valueColor: const AlwaysStoppedAnimation<Color>(Colors.green),
            ),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '${(progress * 100).toInt()}%',
                  style: const TextStyle(color: Colors.green, fontWeight: FontWeight.bold),
                ),
                Text(
                  '${progress == 1 ? 'تم' : 'باقي'} ${(progress == 1) ? '' : '${target.split(' ')[0]}'}',
                  style: const TextStyle(color: Colors.grey),
                ),
              ],
            ),
            const SizedBox(height: 8),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () {
                  // Donate to campaign
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                ),
                child: const Text('تبرع الآن'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showDonationChallenges(BuildContext context) {
    // Implement donation challenges screen
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تحديات التبرع'),
        content: SizedBox(
          width: double.maxFinite,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text('تحدى أصدقائك في التبرع وساهموا معاً في إنجاز مشاريع خيرية'),
              const SizedBox(height: 16),
              _buildChallengeCard(
                'تحدي رمضان',
                'تبرع بقيمة 10 ريال يومياً طوال شهر رمضان',
                '300 ر.س',
                Icons.nights_stay,
                Colors.purple,
              ),
              const SizedBox(height: 12),
              _buildChallengeCard(
                'تحدي المليون',
                'ساعدنا في جمع مليون ريال لعلاج المرضى',
                '1,000,000 ر.س',
                Icons.favorite,
                Colors.red,
              ),
              const SizedBox(height: 12),
              _buildChallengeCard(
                'تحدي التعليم',
                'ساهم في تعليم 1000 طالب',
                '500,000 ر.س',
                Icons.school,
                Colors.blue,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
          ElevatedButton(
            onPressed: () {
              // Create new challenge
              Navigator.pop(context);
            },
            child: const Text('إنشاء تحد جديد'),
          ),
        ],
      ),
    );
  }

  Widget _buildChallengeCard(
    String title,
    String description,
    String target,
    IconData icon,
    Color color,
  ) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: color.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(icon, color: color, size: 24),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      Text(
                        'الهدف: $target',
                        style: const TextStyle(
                          color: Colors.green,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(description),
            const SizedBox(height: 12),
            Row(
              children: [
                const CircleAvatar(
                  radius: 16,
                  backgroundImage: AssetImage('assets/images/user1.jpg'),
                ),
                const SizedBox(width: 8),
                const CircleAvatar(
                  radius: 16,
                  backgroundImage: AssetImage('assets/images/user2.jpg'),
                ),
                const SizedBox(width: 8),
                const CircleAvatar(
                  radius: 16,
                  backgroundColor: Colors.grey,
                  child: Text('+5', style: TextStyle(fontSize: 12)),
                ),
                const Spacer(),
                ElevatedButton(
                  onPressed: () {
                    // Join challenge
                  },
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  ),
                  child: const Text('انضم للتحدي'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _showDonationHistory(BuildContext context) {
    // Implement donation history screen
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => Scaffold(
          appBar: AppBar(title: const Text('سجل التبرعات')),
          body: ListView(
            padding: const EdgeInsets.all(16),
            children: [
              _buildDonationHistoryItem(
                'تبرع شهري',
                '1 يناير 2024',
                '100 ر.س',
                Icons.receipt,
                Colors.green,
              ),
              const Divider(),
              _buildDonationHistoryItem(
                'تبرع لبناء مدرسة',
                '15 ديسمبر 2023',
                '500 ر.س',
                Icons.school,
                Colors.blue,
              ),
              const Divider(),
              _buildDonationHistoryItem(
                'تبرع طارئ',
                '1 نوفمبر 2023',
                '200 ر.س',
                Icons.emergency,
                Colors.red,
              ),
              const Divider(),
              _buildDonationHistoryItem(
                'تبرع شهري',
                '1 أكتوبر 2023',
                '100 ر.س',
                Icons.receipt,
                Colors.green,
              ),
              const Divider(),
              _buildDonationHistoryItem(
                'تبرع لعلاج المرضى',
                '15 سبتمبر 2023',
                '300 ر.س',
                Icons.medical_services,
                Colors.purple,
              ),
            ],
          ),
          floatingActionButton: FloatingActionButton.extended(
            onPressed: () {
              // Show donation options
            },
            icon: const Icon(Icons.add),
            label: const Text('تبرع جديد'),
          ),
        ),
      ),
    );
  }

  Widget _buildDonationHistoryItem(
    String title,
    String date,
    String amount,
    IconData icon,
    Color color,
  ) {
    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(icon, color: color, size: 24),
      ),
      title: Text(title, style: const TextStyle(fontWeight: FontWeight.bold)),
      subtitle: Text(date, style: const TextStyle(color: Colors.grey)),
      trailing: Text(
        amount,
        style: const TextStyle(
          color: Colors.green,
          fontWeight: FontWeight.bold,
          fontSize: 16,
        ),
      ),
      onTap: () {
        // Show donation details
      },
    );
  }
}
