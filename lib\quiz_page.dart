import 'package:flutter/material.dart';
import 'health_service.dart';

class QuizPage extends StatefulWidget {
  final String disease;
  final List<dynamic> quizzes;

  const QuizPage({super.key, required this.disease, required this.quizzes});

  @override
  _QuizPageState createState() => _QuizPageState();
}

class _QuizPageState extends State<QuizPage> {
  final HealthService _healthService = HealthService();
  int _currentQuestionIndex = 0;
  int _score = 0;
  String? _selectedAnswer;
  bool _isSubmitted = false;

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    if (widget.quizzes.isEmpty) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('اختبار'),
          backgroundColor: isDarkMode ? Colors.blue[900] : Colors.blue,
        ),
        body: const Center(child: Text('لا توجد أسئلة متاحة')),
      );
    }

    final currentQuestion = widget.quizzes[_currentQuestionIndex];
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار'),
        backgroundColor: isDarkMode ? Colors.blue[900] : Colors.blue,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'سؤال ${_currentQuestionIndex + 1} من ${widget.quizzes.length}',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: isDarkMode ? Colors.blue[300] : Colors.blue,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              currentQuestion['question'] ?? 'سؤال غير معروف',
              style: TextStyle(
                fontSize: 16,
                color: isDarkMode ? Colors.white : Colors.black87,
              ),
            ),
            const SizedBox(height: 16),
            ...List.generate(
              currentQuestion['options'].length,
                  (index) => RadioListTile<String>(
                title: Text(
                  currentQuestion['options'][index],
                  style: TextStyle(
                    color: isDarkMode ? Colors.white70 : Colors.black87,
                  ),
                ),
                value: currentQuestion['options'][index],
                groupValue: _selectedAnswer,
                onChanged: _isSubmitted
                    ? null
                    : (value) => setState(() => _selectedAnswer = value),
              ),
            ),
            const SizedBox(height: 16),
            if (!_isSubmitted)
              ElevatedButton(
                onPressed: () {
                  if (_selectedAnswer != null) {
                    setState(() {
                      _isSubmitted = true;
                      if (_selectedAnswer == currentQuestion['correctAnswer']) {
                        _score += 10; // Award points for correct answer
                      }
                    });
                  } else {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('يرجى اختيار إجابة'),
                        backgroundColor: Colors.orange,
                      ),
                    );
                  }
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
                ),
                child: const Text(
                  'إرسال',
                  style: TextStyle(color: Colors.white),
                ),
              ),
            if (_isSubmitted)
              Column(
                children: [
                  Text(
                    _selectedAnswer == currentQuestion['correctAnswer']
                        ? 'إجابة صحيحة! +10 نقاط'
                        : 'إجابة خاطئة',
                    style: TextStyle(
                      color: _selectedAnswer == currentQuestion['correctAnswer']
                          ? Colors.green
                          : Colors.red,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      setState(() {
                        _currentQuestionIndex++;
                        _selectedAnswer = null;
                        _isSubmitted = false;
                      });
                      if (_currentQuestionIndex >= widget.quizzes.length) {
                        _showQuizResult();
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
                    ),
                    child: Text(
                      _currentQuestionIndex < widget.quizzes.length - 1
                          ? 'السؤال التالي'
                          : 'إنهاء الاختبار',
                      style: const TextStyle(color: Colors.white),
                    ),
                  ),
                ],
              ),
          ],
        ),
      ),
    );
  }

  void _showQuizResult() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('نتيجة الاختبار'),
        content: Text('لقد حصلت على $_score نقاط!'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.pop(context);
            },
            child: const Text('إغلاق', style: TextStyle(color: Colors.blue)),
          ),
        ],
      ),
    );
  }
}