const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');

/**
 * نموذج المستخدم الموحد
 * يجمع بين ميزات النظام الأساسي ونظام المشاركة والاستعارة ونظام النقاط والمستويات
 */
const UserSchema = new mongoose.Schema(
  {
    // إعدادات الإشعارات
    notificationsSettings: {
      mention: { type: Boolean, default: true },
      comment: { type: Boolean, default: true },
      like: { type: Boolean, default: true },
      follow: { type: Boolean, default: true },
      message: { type: Boolean, default: true },
      admin: { type: Boolean, default: true },
      video_comment: { type: Boolean, default: true },
      video_like: { type: Boolean, default: true }
    },
    
    // المعلومات الأساسية
    name: {
      type: String,
      required: [true, 'الرجاء إدخال الاسم'],
      trim: true,
      maxlength: [50, 'الاسم لا يمكن أن يتجاوز 50 حرفًا']
    },
    email: {
      type: String,
      required: [true, 'الرجاء إدخال البريد الإلكتروني'],
      unique: true,
      match: [
        /^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/,
        'الرجاء إدخال بريد إلكتروني صحيح'
      ],
      lowercase: true,
      trim: true
    },
    password: {
      type: String,
      required: [true, 'الرجاء إدخال كلمة المرور'],
      minlength: [6, 'كلمة المرور يجب أن تكون على الأقل 6 أحرف'],
      select: false
    },
    
    // الصورة والمظهر
    avatar: {
      type: String,
      default: '/uploads/default-avatar.png'
    },
    
    // الأدوار والصلاحيات
    role: {
      type: String,
      enum: ['hokama', 'olama', 'mogtahdin', 'medical', 'member', 'guest', 'admin'],
      default: 'member'
    },
    
    // معلومات إضافية
    bio: {
      type: String,
      maxlength: [500, 'الوصف لا يمكن أن يتجاوز 500 حرف']
    },
    phone: {
      type: String,
      match: [/^[0-9]{10,15}$/, 'الرجاء إدخال رقم هاتف صحيح']
    },
    
    // الموقع والعنوان
    address: {
      street: String,
      city: String,
      state: String,
      zipCode: String,
      country: String,
      location: {
        type: {
          type: String,
          enum: ['Point'],
          default: 'Point'
        },
        coordinates: {
          type: [Number],
          index: '2dsphere'
        }
      }
    },
    
    // إعدادات الحساب
    isVerified: {
      type: Boolean,
      default: false
    },
    isActive: {
      type: Boolean,
      default: true
    },
    isBanned: {
      type: Boolean,
      default: false
    },
    banReason: String,
    
    // نظام النقاط والمستويات
    points: {
      type: Number,
      default: 0,
      min: 0
    },
    level: {
      type: Number,
      default: 1,
      min: 1
    },
    experience: {
      type: Number,
      default: 0,
      min: 0
    },
    
    // إحصائيات المستخدم
    stats: {
      posts: { type: Number, default: 0 },
      followers: { type: Number, default: 0 },
      following: { type: Number, default: 0 },
      shares: { type: Number, default: 0 },
      borrowedItems: { type: Number, default: 0 },
      borrowedItemsCount: { type: Number, default: 0 },
      sharedItems: { type: Number, default: 0 },
      sharedItemsCount: { type: Number, default: 0 },
      ratingsReceived: { type: Number, default: 0 },
      ratingAverage: { type: Number, default: 0 }
    },
    
    // التواريخ المهمة
    lastLogin: Date,
    passwordChangedAt: Date,
    
    // إعادة تعيين كلمة المرور
    resetPasswordToken: String,
    resetPasswordExpire: Date,
    
    // وسائل التواصل الاجتماعي
    social: {
      facebook: String,
      twitter: String,
      instagram: String,
      linkedin: String
    },
    
    // التفضيلات
    preferences: {
      language: { type: String, default: 'ar' },
      theme: { type: String, default: 'light' },
      notifications: { type: Boolean, default: true },
      emailNotifications: { type: Boolean, default: true },
      pushNotifications: { type: Boolean, default: true }
    },
    
    // الحقول المحسوبة
    fullAddress: {
      type: String,
      get: function() {
        return `${this.address.street || ''}, ${this.address.city || ''}, ${this.address.state || ''} ${this.address.zipCode || ''}, ${this.address.country || ''}`.replace(/,\s+,/g, ', ').replace(/^\s*,\s*|\s*,\s*$/g, '');
      }
    }
  },
  {
    timestamps: true,
    toJSON: { virtuals: true, getters: true },
    toObject: { virtuals: true, getters: true }
  }
);

// فهرسة الحقول المستخدمة في البحث
UserSchema.index({ name: 'text', email: 'text', 'address.city': 'text', 'address.country': 'text' });

// تشفير كلمة المرور قبل الحفظ
UserSchema.pre('save', async function(next) {
  if (!this.isModified('password')) {
    return next();
  }
  
  const salt = await bcrypt.genSalt(10);
  this.password = await bcrypt.hash(this.password, salt);
  
  // تحديث تاريخ تغيير كلمة المرور إذا لم يتم إنشاؤها حديثًا
  if (!this.isNew && this.isModified('password')) {
    this.passwordChangedAt = Date.now() - 1000; // ناقص ثانية للتأكد من أن الرمز المميز صالح دائمًا
  }
});

// تحديث الحقول المحسوبة قبل الحفظ
UserSchema.pre('save', function(next) {
  // حساب متوسط التقييم
  if (this.stats.ratingsReceived > 0) {
    this.stats.ratingAverage = Math.round((this.stats.ratingSum / this.stats.ratingsReceived) * 10) / 10;
  }
  next();
});

// حذف المساهمات والرسائل المرتبطة عند حذف المستخدم
UserSchema.pre('remove', async function(next) {
  await this.model('Contribution').deleteMany({ userId: this._id });
  await this.model('Message').deleteMany({ userId: this._id });
  await this.model('Post').deleteMany({ user: this._id });
  await this.model('Comment').deleteMany({ user: this._id });
  await this.model('Like').deleteMany({ user: this._id });
  next();
});

// إنشاء توكن JWT
UserSchema.methods.getSignedJwtToken = function() {
  return jwt.sign(
    { id: this._id, role: this.role },
    process.env.JWT_SECRET,
    { expiresIn: process.env.JWT_EXPIRE || '30d' }
  );
};

// مقارنة كلمة المرور المدخلة مع المشفرة
UserSchema.methods.matchPassword = async function(enteredPassword) {
  return await bcrypt.compare(enteredPassword, this.password);
};

// الحصول على ملف تعريف المستخدم
UserSchema.methods.getProfile = function() {
  return {
    _id: this._id,
    name: this.name,
    email: this.email,
    avatar: this.avatar,
    role: this.role,
    bio: this.bio,
    phone: this.phone,
    address: this.address,
    stats: this.stats,
    preferences: this.preferences,
    social: this.social,
    isVerified: this.isVerified,
    isActive: this.isActive,
    createdAt: this.createdAt,
    updatedAt: this.updatedAt
  };
};

// الحصول على ملف تعريف المستخدم لنظام المشاركة والاستعارة
UserSchema.methods.getShareBorrowProfile = function() {
  return {
    _id: this._id,
    name: this.name,
    avatar: this.avatar,
    rating: this.stats.ratingAverage,
    ratingsCount: this.stats.ratingsReceived,
    sharedItemsCount: this.stats.sharedItemsCount,
    borrowedItemsCount: this.stats.borrowedItemsCount,
    memberSince: this.createdAt,
    lastActive: this.lastLogin,
    address: this.fullAddress
  };
};

// تصدير النموذج
module.exports = mongoose.model('User', UserSchema);
