// تحميل متغيرات البيئة بناءً على بيئة التشغيل
const path = require('path');
const dotenv = require('dotenv');

// تحديد ملف البيئة المناسب
const envFile = process.env.NODE_ENV === 'production' ? '.env.production' : '.env.development';
console.log(`🔧 Loading environment from ${envFile}`);

// تحميل متغيرات البيئة من الملف المناسب
dotenv.config({ path: path.resolve(process.cwd(), envFile) });

// استيراد التطبيق والخادم من ملف app.js
const { app, server } = require('./app');

// الاتصال بقاعدة البيانات - تم نقله إلى app.js لتجنب التكرار

// تم نقل تهيئة المجلد العام إلى app.js

// استيراد المسارات
const authRoutes = require('./backend/src/routes/authroutes');
const userRoutes = require('./backend/src/routes/userroutes');
const postRoutes = require('./backend/src/routes/postroutes');
const messageRoutes = require('./backend/src/routes/messageroutes');
const groupRoutes = require('./backend/src/routes/grouproutes');
const storyRoutes = require('./backend/src/routes/storyroutes');
const videoRoutes = require('./backend/src/routes/videoroutes');
const medicalRecordRoutes = require('./backend/src/routes/medicalRecordroutes');
const cardRoutes = require('./backend/src/routes/cardsroutes');
const calculusRoutes = require('./backend/src/routes/calculus.routes');
const professionRoutes = require('./backend/src/routes/professions.routes');
const notificationRoutes = require('./backend/src/routes/notification.routes');
const pointsRoutes = require('./backend/src/routes/points.routes');

// Initialize activity tracker middleware
const activityTracker = require('./backend/src/middleware/activity.middleware').createActivityTracker();

// API Routes with activity tracking
app.use('/api', [
  // Activity tracking middleware
  activityTracker,
  
  // Route handlers
  authRoutes,
  userRoutes,
  postRoutes,
  messageRoutes,
  groupRoutes,
  storyRoutes,
  videoRoutes,
  medicalRecordRoutes,
  cardRoutes,
  calculusRoutes,
  professionRoutes,
  notificationRoutes,
  pointsRoutes
]);

// مسارات نظام المشاركة والاستعارة
app.use('/api/share-borrow-items', require('./backend/src/routes/shareBorrowItem.routes'));
app.use('/api/sharing-groups', require('./backend/src/routes/sharingGroup.routes'));
app.use('/api/analytics', require('./backend/src/routes/analyticsEvent.routes'));
app.use('/api/uploads', require('./backend/src/routes/upload.routes'));

// نقاط نهاية الرحلات والسائقين
app.use('/api/rides', require('./backend/src/routes/rideRequest.routes'));
app.use('/api/drivers', require('./backend/src/routes/driver.routes'));

// مسارات المهن والوظائف
app.use('/api/professions', require('./backend/src/routes/professions.routes'));

// مسارات الإشعارات
app.use('/api/notifications', require('./backend/src/routes/notification.routes'));

// الصفحة الرئيسية
app.get('/', (req, res) => {
  res.send('مرحباً بك في واجهة برمجة التطبيقات الخاصة بتطبيق فلك');
});

// وسيط معالجة الأخطاء
const errorHandler = require('./backend/src/middleware/errormiddleware');
app.use(errorHandler);

// تشغيل الخادم
const PORT = process.env.PORT || 8000;
const HOST = process.env.HOST || '0.0.0.0';

// معالجة أخطاء غير متوقعة
process.on('uncaughtException', (err) => {
  console.error('❌  خطأ غير متوقع:', err);
  // إغلاق الخادم بشكل صحيح في حالة حدوث خطأ غير متوقع
  server.close(() => {
    process.exit(1);
  });
});

// معالجة رفض وعود غير معالجة
process.on('unhandledRejection', (err) => {
  console.error('❌  وعد مرفوض غير معالج:', err);
  // إغلاق الخادم بشكل صحيح في حالة رفض وعد غير معالج
  server.close(() => {
    process.exit(1);
  });
});

// تهيئة متغيرات التشغيل
let isShuttingDown = false;

/**
 * بدء تشغيل الخادم
 */
async function startServer() {
  try {
    // التحقق من متغيرات البيئة المطلوبة
    const requiredEnvVars = [
      'MONGODB_URI',
      'JWT_SECRET',
      'JWT_EXPIRES_IN',
      'JWT_COOKIE_EXPIRES_IN',
      'NODE_ENV'
    ];

    // التحقق من وجود متغيرات البيئة المطلوبة
    const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
    if (missingVars.length > 0) {
      throw new Error(`متغيرات البيئة المفقودة: ${missingVars.join(', ')}`);
    }

        // بدء تشغيل الخادم
    await new Promise((resolve, reject) => {
      server.on('error', reject);
      
      server.listen(PORT, HOST, () => {
        server.off('error', reject);
        
        // تسجيل معلومات بدء التشغيل
        console.log('\n🚀  =================================');
        console.log(`   الخادم يعمل على: http://${HOST}:${PORT}`);
        console.log(`   بيئة التشغيل: ${process.env.NODE_ENV || 'development'}`);
        console.log(`   وقت بدء التشغيل: ${new Date().toLocaleString()}`);
        console.log(`   معالجو النظام: ${require('os').cpus().length} نواة`);
        console.log(`   ذاكرة النظام: ${Math.round(require('os').totalmem() / (1024 * 1024))} ميجابايت`);
        console.log('=================================\n');
        
        // تسجيل حالة قاعدة البيانات
        const mongoose = require('mongoose');
        if (mongoose.connection.readyState === 1) {
          console.log('✅  تم الاتصال بنجاح بقاعدة البيانات');
        } else {
          console.warn('⚠️  تحذير: لا يوجد اتصال نشط بقاعدة البيانات');
        }
        
        // معلومات إضافية عن التطبيق
        console.log(`🔍  الوضع: ${process.env.NODE_ENV || 'development'}`);
        console.log(`🌍  النطاقات المسموح بها: ${process.env.ALLOWED_ORIGINS || 'الكل'}`);
        console.log(`📊  اتصال قاعدة البيانات: ${process.env.MONGODB_URI ? 'تم التهيئة' : 'غير مهيأ'}`);
        
        resolve();
      });
    });
  } catch (error) {
    console.error('❌  فشل بدء تشغيل الخادم:', error);
    process.exit(1);
  }
}

/**
 * إيقاف الخادم بشكل آمن
 * @param {string} signal - إشارة الإيقاف المستلمة
 * @param {number} [exitCode=0] - كود الخروج الافتراضي
 */
const shutdown = async (signal, exitCode = 0) => {
  if (isShuttingDown) {
    console.log('⏳  جاري بالفعل إيقاف الخادم...');
    return;
  }
  
  isShuttingDown = true;
  const startTime = Date.now();
  
  console.log(`\n🛑  استلام إشارة ${signal}، جاري إيقاف الخادم...`);
  
  try {
    // إعطاء مهلة للإغلاق (30 ثانية كحد أقصى)
    const SHUTDOWN_TIMEOUT = 30000; // 30 ثانية
    const shutdownPromise = new Promise((_, reject) => {
      setTimeout(() => {
        reject(new Error(`⚠️  تجاوزت عملية الإغلاق المهلة المحددة (${SHUTDOWN_TIMEOUT}ms)`));
      }, SHUTDOWN_TIMEOUT);
    });
    
    // تنفيذ عمليات الإغلاق مع مهلة زمنية
    await Promise.race([
      (async () => {
        try {
          // 1. إغلاق خادم HTTP
          if (server.listening) {
            console.log('⏳  جاري إيقاف خادم HTTP...');
            await new Promise((resolve) => server.close(resolve));
            console.log('✅  تم إيقاف خادم HTTP');
          }
          
          // 2. إغلاق اتصالات قاعدة البيانات
          const mongoose = require('mongoose');
          if (mongoose.connection.readyState === 1) {
            console.log('⏳  جاري إغلاق اتصال قاعدة البيانات...');
            await mongoose.connection.close();
            console.log('✅  تم إغلاق اتصال قاعدة البيانات');
          }
          
          // 3. إغلاق اتصالات Socket.io
          if (io) {
            console.log('⏳  جاري إغلاق اتصالات Socket.io...');
            await new Promise((resolve) => {
              io.close(() => {
                console.log('✅  تم إغلاق اتصالات Socket.io');
                resolve();
              });
            });
          }
          
          // 4. إغلاق أي اتصالات أخرى (مثل Redis، إلخ)
          // يمكن إضافة المزيد من عمليات التنظيف هنا
          
          const shutdownTime = ((Date.now() - startTime) / 1000).toFixed(2);
          console.log(`\n✅  تم إيقاف التطبيق بنجاح خلال ${shutdownTime} ثانية`);
          console.log('👋  مع السلامة!');
          
          process.exit(exitCode);
        } catch (error) {
          console.error('❌  خطأ أثناء إيقاف الخادم:', error);
          process.exit(1);
        }
      })(),
      shutdownPromise
    ]);
  } catch (error) {
    console.error('❌  فشل الإغلاق النظيف:', error);
    process.exit(1);
  }
};

// بدء تشغيل الخادم
startServer();

// معالجة إشارات الإغلاق
process.on('SIGTERM', () => shutdown('SIGTERM'));
process.on('SIGINT', () => shutdown('SIGINT'));

// ==========================================
// معالجة الأخطاء والاستثناءات
// ==========================================


/**
 * معالجة الأخطاء غير المتوقعة (غير المعالجة)
 * @param {Error} err - كائن الخطأ
 */
process.on('uncaughtException', async (err) => {
  const errorInfo = {
    message: err.message,
    stack: err.stack,
    name: err.name,
    time: new Date().toISOString(),
    pid: process.pid,
    memoryUsage: process.memoryUsage(),
    uptime: process.uptime()
  };
  
  console.error('\n❌  ===== خطأ غير متوقع =====');
  console.error('📝  الرسالة:', errorInfo.message);
  console.error('🔍  النوع:', errorInfo.name);
  console.error('📋  التتبع:', errorInfo.stack);
  console.error('⏰  الوقت:', errorInfo.time);
  console.error('🆔  معرف العملية:', errorInfo.pid);
  console.error('💾  استخدام الذاكرة:', JSON.stringify(errorInfo.memoryUsage, null, 2));
  console.error('⏱️  وقت التشغيل:', Math.floor(errorInfo.uptime / 60) + ' دقائق ' + 
                Math.floor(errorInfo.uptime % 60) + ' ثواني');
  console.error('==============================\n');
  
  // محاولة إرسال تنبيه بالخطأ
  try {
    // يمكنك إضافة إرسال إشعار بالخطأ هنا (مثل إرسال بريد إلكتروني أو إشعار إلى خدمة مراقبة)
    // مثال: await sendErrorNotification(errorInfo);
    console.error('⚠️  تم إرسال إشعار بالخطأ');
  } catch (notifyError) {
    console.error('❌  فشل في إرسال إشعار الخطأ:', notifyError);
  }
  
  // إعادة تشغيل العملية بعد التأكد من إغلاق الموارد
  if (!isShuttingDown) {
    await shutdown('uncaughtException', 1);
  } else {
    // إذا كنا بالفعل في عملية إغلاق، نخرج فوراً
    process.exit(1);
  }
});

/**
 * معالجة رفض الوعود غير المعالجة
 * @param {*} reason - سبب الرفض
 * @param {Promise} promise - الوعد المرفوض
 */
process.on('unhandledRejection', (reason, promise) => {
  console.error('\n❌  ===== وعد مرفوض غير معالج =====');
  console.error('📝  السبب:', reason);
  
  // تسجيل مكدس الاستدعاءات إذا كان متاحاً
  if (reason instanceof Error) {
    console.error('📋  التتبع:', reason.stack || 'غير متوفر');
  }
  
  console.error('⏰  الوقت:', new Date().toISOString());
  console.error('==============================\n');
  
  // يمكنك اختيارياً إنهاء العملية في بيئة الإنتاج
  // if (process.env.NODE_ENV === 'production') {
  //   process.exit(1);
  // }
});

/**
 * معالجة تحذيرات الرفض المتأخر
 * @param {Promise} promise - الوعد الذي تم معالجة رفضه لاحقاً
 */
process.on('rejectionHandled', (promise) => {
  console.warn('\n⚠️  ===== تم معالجة وعد مرفوض متأخراً =====');
  console.warn('ℹ️  تأكد من معالجة جميع الوعود المرفوضة بشكل صحيح');
  console.warn('⏰  الوقت:', new Date().toISOString());
  console.warn('==============================\n');
});

/**
 * معالجة تحذيرات Node.js
 * @param {Error} warning - كائن التحذير
 */
process.on('warning', (warning) => {
  console.warn('\n⚠️  ===== تحذير Node.js =====');
  console.warn('📝  الرسالة:', warning.message);
  console.warn('🔍  النوع:', warning.name);
  console.warn('📋  التتبع:', warning.stack);
  console.warn('⏰  الوقت:', new Date().toISOString());
  console.warn('========================\n');
});

// ==========================================
// معالجة إشارات النظام
// ==========================================

// إغلاق أنيق عند استلام إشارة SIGTERM (إشارة الإنهاء الافتراضية)
process.on('SIGTERM', () => {
  console.log('\n🛑  تم استلام إشارة SIGTERM');
  shutdown('SIGTERM');
});

// إغلاق أنيق عند الضغط على Ctrl+C (SIGINT)
process.on('SIGINT', () => {
  console.log('\n🛑  تم الضغط على Ctrl+C');
  shutdown('SIGINT');
});

// معالجة إعادة تشغيل التطبيق (مفيد مع أدوات مثل nodemon)
process.once('SIGUSR2', () => {
  console.log('\n🔄  إعادة تشغيل التطبيق...');
  shutdown('SIGUSR2').then(() => {
    process.kill(process.pid, 'SIGUSR2');
  });
});