import 'package:flutter/material.dart';

class ManageReportsPage extends StatelessWidget {
  const ManageReportsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('مراجعة البلاغات', style: TextStyle(fontFamily: 'Tajawal')),
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          ListTile(
            leading: const Icon(Icons.report),
            title: const Text('بلاغ 1', style: TextStyle(fontFamily: 'Tajawal')),
            subtitle: const Text('تفاصيل البلاغ الأول...'),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                IconButton(icon: const Icon(Icons.check), onPressed: () {}),
                IconButton(icon: const Icon(Icons.close), onPressed: () {}),
              ],
            ),
          ),
          ListTile(
            leading: const Icon(Icons.report),
            title: const Text('بلاغ 2', style: TextStyle(fontFamily: 'Tajawal')),
            subtitle: const Text('تفاصيل البلاغ الثاني...'),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                IconButton(icon: const Icon(Icons.check), onPressed: () {}),
                IconButton(icon: const Icon(Icons.close), onPressed: () {}),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
