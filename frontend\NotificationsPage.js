import React, { useEffect, useState } from "react";
import axios from "axios";

const NotificationsPage = () => {
  const [notifs, setNotifs] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    axios.get("/api/users/notifications").then(res => {
      setNotifs(res.data.data);
      setLoading(false);
    });
  }, []);

  if (loading) return <div>جاري تحميل الإشعارات...</div>;

  return (
    <div style={{ padding: 32 }}>
      <h2>الإشعارات</h2>
      {Object.entries(notifs).map(([type, arr]) => (
        <div key={type} style={{ marginBottom: 24 }}>
          <h4>{type}</h4>
          <ul>
            {arr.map(n => (
              <li key={n._id} style={{ color: n.isRead ? '#888' : '#000', fontWeight: n.isRead ? 'normal' : 'bold' }}>
                {n.text} <span style={{ fontSize: 12, color: '#666' }}>({n.relativeTime})</span>
              </li>
            ))}
          </ul>
        </div>
      ))}
    </div>
  );
};

export default NotificationsPage;
