import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:share_plus/share_plus.dart';
import 'package:animated_text_kit/animated_text_kit.dart';
import 'package:confetti/confetti.dart';
import 'package:image_picker/image_picker.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:geolocator/geolocator.dart';
import 'package:intl/intl.dart';
import 'package:shimmer/shimmer.dart';
import 'package:provider/provider.dart';
import 'appstate.dart';

class BirdBreedingGroup {
  final String id;
  final String name;
  final String imageUrl;
  final List<Video> videos;
  final List<Article> articles;
  final List<FAQ> faqs;
  final String nutritionGuide;
  final List<String> galleryImages;

  BirdBreedingGroup({
    required this.id,
    required this.name,
    required this.imageUrl,
    required this.videos,
    required this.articles,
    required this.faqs,
    required this.nutritionGuide,
    required this.galleryImages,
  });
}

class Video {
  final String title;
  final String url;

  Video({required this.title, required this.url});
}

class Article {
  final String title;
  final String content;

  Article({required this.title, required this.content});
}

class FAQ {
  final String question;
  final String answer;

  FAQ({required this.question, required this.answer});
}

class Post {
  final String user;
  final String content;
  final String? imageUrl;
  final int likes;
  final List<String> comments;

  Post({
    required this.user,
    required this.content,
    this.imageUrl,
    this.likes = 0,
    this.comments = const [],
  });
}

class ChatMessage {
  final String userId;
  final String userName;
  final String text;

  ChatMessage({required this.userId, required this.userName, required this.text});
}

class NotificationItem {
  final String title;
  final String description;
  final DateTime date;

  NotificationItem({
    required this.title,
    required this.description,
    required this.date,
  });
}

class MarketItem {
  final String id;
  final String user;
  final String type;
  final List<String> imageUrls;
  final double quantity;
  final double price;
  final String address;
  final DateTime date;
  final String validity;
  final String contactNumber;
  final String details;
  final String? breed;
  final String? nutritionalContent;

  MarketItem({
    required this.id,
    required this.user,
    required this.type,
    required this.imageUrls,
    required this.quantity,
    required this.price,
    required this.address,
    required this.date,
    required this.validity,
    required this.contactNumber,
    required this.details,
    this.breed,
    this.nutritionalContent,
  });
}

class BirdBreedingPage extends StatefulWidget {
  const BirdBreedingPage({super.key});

  @override
  State<BirdBreedingPage> createState() => _BirdBreedingPageState();
}

class _BirdBreedingPageState extends State<BirdBreedingPage> with SingleTickerProviderStateMixin {
  late ConfettiController _confettiController;
  final List<BirdBreedingGroup> birds = [
    BirdBreedingGroup(
      id: '1',
      name: 'الببغاء',
      imageUrl: 'https://cdn-icons-png.flaticon.com/512/616/616408.png',
      videos: [
        Video(title: 'طرق تربية الببغاء', url: 'https://www.youtube.com/watch?v=parrot1'),
        Video(title: 'أمراض شائعة للببغاء', url: 'https://www.youtube.com/watch?v=parrot2'),
      ],
      articles: [
        Article(title: 'دليل العناية بالببغاء', content: 'ك Rhodanir للعناية بالببغاء...'),
      ],
      faqs: [
        FAQ(question: 'ماذا يأكل الببغاء؟', answer: 'يأكل البذور والفواكه والخضروات...'),
      ],
      nutritionGuide: 'بذور مختلطة، فواكه طازجة (تفاح، موز)، خضروات (جزر، سبانخ)، مكملات الكالسيوم.',
      galleryImages: [
        'https://cdn-icons-png.flaticon.com/512/616/616408.png',
        'https://cdn-icons-png.flaticon.com/512/616/616408.png',
      ],
    ),
    BirdBreedingGroup(
      id: '2',
      name: 'الكناري',
      imageUrl: 'https://cdn-icons-png.flaticon.com/512/616/616490.png',
      videos: [
        Video(title: 'تربية الكناري', url: 'https://www.youtube.com/watch?v=canary1'),
      ],
      articles: [
        Article(title: 'صحة الكناري', content: 'نصائح للحفاظ على صحة الكناري...'),
      ],
      faqs: [
        FAQ(question: 'كيف أعرف أن الكناري مريض؟', answer: 'راقب نشاطه وريشه وشهيته...'),
      ],
      nutritionGuide: 'بذور الكناري، خضروات ورقية، فواكه مجففة، بيض مسلوق للبروتين.',
      galleryImages: [
        'https://cdn-icons-png.flaticon.com/512/616/616490.png',
        'https://cdn-icons-png.flaticon.com/512/616/616490.png',
      ],
    ),
  ];

  final List<Post> posts = [
    Post(
      user: 'علي',
      content: 'ببغائي تعلم كلمة جديدة!',
      imageUrl: 'https://cdn-icons-png.flaticon.com/512/616/616408.png',
      likes: 10,
      comments: ['رائع!', 'ما هي الكلمة؟'],
    ),
    Post(user: 'سارة', content: 'نصائح لأقفاص الكناري.', likes: 5, comments: []),
  ];

  final List<MarketItem> birdItems = [
    MarketItem(
      id: '1',
      user: 'علي',
      type: 'ببغاء رمادي',
      imageUrls: ['https://cdn-icons-png.flaticon.com/512/616/616408.png'],
      quantity: 1,
      price: 500,
      address: 'الرياض',
      date: DateTime.now(),
      validity: 'أسبوع',
      contactNumber: '0551234567',
      details: 'ببغاء رمادي صحي، عمره سنة واحدة',
      breed: 'رمادي أفريقي',
    ),
  ];

  final List<MarketItem> supplyItems = [
    MarketItem(
      id: '2',
      user: 'سارة',
      type: 'قفص طيور',
      imageUrls: ['https://cdn-icons-png.flaticon.com/512/3063/3063174.png'],
      quantity: 1,
      price: 200,
      address: 'جدة',
      date: DateTime.now(),
      validity: 'أسبوع',
      contactNumber: '0559876543',
      details: 'قفص كبير مناسب للببغاء',
    ),
  ];

  final List<MarketItem> feedItems = [
    MarketItem(
      id: '3',
      user: 'محمد',
      type: 'بذور مختلطة',
      imageUrls: ['https://cdn-icons-png.flaticon.com/512/2921/2921914.png'],
      quantity: 5,
      price: 50,
      address: 'الدمام',
      date: DateTime.now(),
      validity: 'شهر',
      contactNumber: '0555555555',
      details: 'بذور عالية الجودة للطيور',
      nutritionalContent: 'بروتين 15%، دهون 10%',
    ),
  ];

  final List<String> birdTypes = ['ببغاء', 'كناري', 'حمام', 'عصافير'];
  final List<String> supplyTypes = ['قفص', 'مغذية', 'مشربية', 'ألعاب'];
  final List<String> feedTypes = ['بذور', 'فواكه مجففة', 'مكملات غذائية'];

  final TextEditingController _postController = TextEditingController();
  final TextEditingController _searchController = TextEditingController();
  final TextEditingController _commentController = TextEditingController();

  late AnimationController _fabAnimationController;
  late Animation<double> _fabScaleAnimation;

  String selectedMarketFilter = 'الكل';
  int userPoints = 100;

  @override
  void initState() {
    super.initState();
    _confettiController = ConfettiController(duration: const Duration(seconds: 1));
    _fabAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 600),
    )..repeat(reverse: true);
    _fabScaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(parent: _fabAnimationController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _confettiController.dispose();
    _fabAnimationController.dispose();
    _postController.dispose();
    _searchController.dispose();
    _commentController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Scaffold(
          appBar: AppBar(
            title: AnimatedTextKit(
              animatedTexts: [
                TypewriterAnimatedText(
                  'موسوعة تربية الطيور',
                  textStyle: const TextStyle(fontSize: 22, fontWeight: FontWeight.bold),
                  speed: const Duration(milliseconds: 100),
                ),
              ],
              totalRepeatCount: 1,
            ),
            backgroundColor: Colors.blue.shade700,
            actions: [
              IconButton(
                icon: const Icon(Icons.search),
                onPressed: () => _showSearchDialog(context),
                tooltip: 'بحث',
              ),
            ],
            flexibleSpace: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [Colors.blue.shade700, Colors.blue.shade300],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
              ),
            ),
          ),
          body: RefreshIndicator(
            onRefresh: () async {
              await Future.delayed(const Duration(seconds: 1));
              setState(() {});
              Fluttertoast.showToast(msg: 'تم تحديث الصفحة', backgroundColor: Colors.blue.shade700);
            },
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: AnimationLimiter(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: AnimationConfiguration.toStaggeredList(
                    duration: const Duration(milliseconds: 600),
                    childAnimationBuilder: (widget) => SlideAnimation(
                      verticalOffset: 50.0,
                      child: FadeInAnimation(child: widget),
                    ),
                    children: [
                      _buildSectionTitle('أنواع الطيور'),
                      const SizedBox(height: 16),
                      _buildBirdCards(),
                      const SizedBox(height: 24),
                      _buildSectionTitle('سوق الطيور'),
                      const SizedBox(height: 12),
                      _buildBirdsMarketplace(),
                      const SizedBox(height: 24),
                      _buildSectionTitle('سوق المستلزمات'),
                      const SizedBox(height: 12),
                      _buildSuppliesMarketplace(),
                      const SizedBox(height: 24),
                      _buildSectionTitle('سوق الأعلاف'),
                      const SizedBox(height: 12),
                      _buildFeedMarketplace(),
                      const SizedBox(height: 24),
                      _buildSectionTitle('منشورات المجتمع'),
                      const SizedBox(height: 12),
                      _buildPostInput(),
                      const SizedBox(height: 12),
                      _buildPostsList(),
                    ],
                  ),
                ),
              ),
            ),
          ),
          floatingActionButton: ScaleTransition(
            scale: _fabScaleAnimation,
            child: FloatingActionButton(
              onPressed: () => _showQuickActions(context),
              tooltip: 'إجراءات سريعة',
              backgroundColor: Colors.blue.shade700,
              child: const Icon(Icons.add),
            ),
          ),
        ),
        Align(
          alignment: Alignment.topCenter,
          child: ConfettiWidget(
            confettiController: _confettiController,
            blastDirectionality: BlastDirectionality.explosive,
            particleDrag: 0.05,
            emissionFrequency: 0.05,
            numberOfParticles: 50,
            gravity: 0.05,
            colors: const [Colors.blue, Colors.green, Colors.yellow, Colors.red],
          ),
        ),
      ],
    );
  }

  Widget _buildSectionTitle(String title) {
    return AnimatedTextKit(
      animatedTexts: [
        FadeAnimatedText(
          title,
          textStyle: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: Colors.blue.shade700,
          ),
        ),
      ],
      totalRepeatCount: 1,
    );
  }

  Widget _buildShimmerPlaceholder() {
    return Shimmer.fromColors(
      baseColor: Colors.grey.shade300,
      highlightColor: Colors.grey.shade100,
      child: Container(
        color: Colors.white,
        width: double.infinity,
        height: double.infinity,
      ),
    );
  }

  Widget _buildBirdCards() {
    return SizedBox(
      height: 220,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: birds.length,
        itemBuilder: (context, index) {
          final bird = birds[index];
          return AnimationConfiguration.staggeredList(
            position: index,
            duration: const Duration(milliseconds: 600),
            child: SlideAnimation(
              horizontalOffset: 50.0,
              child: FadeInAnimation(
                child: GestureDetector(
                  onTap: () => _showBirdDetails(context, bird),
                  child: Padding(
                    padding: const EdgeInsets.only(right: 16.0, left: 8.0),
                    child: Card(
                      elevation: 8,
                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
                      child: Container(
                        width: 160,
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [Colors.blue.shade50, Colors.blue.shade100],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ),
                          borderRadius: BorderRadius.circular(16),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.blue.withOpacity(0.3),
                              blurRadius: 10,
                              offset: const Offset(0, 5),
                            ),
                          ],
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            ClipRRect(
                              borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
                              child: CachedNetworkImage(
                                imageUrl: bird.imageUrl,
                                height: 100,
                                width: double.infinity,
                                fit: BoxFit.cover,
                                placeholder: (context, url) => _buildShimmerPlaceholder(),
                                errorWidget: (context, url, error) => const Icon(Icons.error),
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: AnimatedTextKit(
                                animatedTexts: [
                                  TypewriterAnimatedText(
                                    bird.name,
                                    textStyle: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.blue.shade700,
                                    ),
                                    speed: const Duration(milliseconds: 100),
                                  ),
                                ],
                                totalRepeatCount: 1,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildBirdsMarketplace() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            colors: [Colors.blue.shade50, Colors.blue.shade100],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Stack(
          children: [
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'سوق الطيور',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(color: Colors.blue.shade700),
                  ),
                  const SizedBox(height: 12),
                  DropdownButton<String>(
                    value: selectedMarketFilter,
                    isExpanded: true,
                    items: ['الكل', ...birdTypes].map((filter) {
                      return DropdownMenuItem<String>(
                        value: filter,
                        child: Text(filter),
                      );
                    }).toList(),
                    onChanged: (value) {
                      setState(() {
                        selectedMarketFilter = value!;
                        Fluttertoast.showToast(msg: 'تم تحديث الفلتر', backgroundColor: Colors.blue.shade700);
                      });
                    },
                    dropdownColor: Colors.blue.shade50,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  const SizedBox(height: 12),
                  SizedBox(
                    height: 280,
                    child: ListView.builder(
                      scrollDirection: Axis.horizontal,
                      itemCount: birdItems.length,
                      itemBuilder: (context, index) {
                        final item = birdItems[index];
                        return AnimationConfiguration.staggeredList(
                          position: index,
                          duration: const Duration(milliseconds: 600),
                          child: SlideAnimation(
                            horizontalOffset: 50.0,
                            child: FadeInAnimation(
                              child: Padding(
                                padding: const EdgeInsets.only(right: 12.0),
                                child: Card(
                                  elevation: 3,
                                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                                  child: Container(
                                    width: 200,
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(12),
                                      gradient: LinearGradient(
                                        colors: [Colors.blue.shade50, Colors.blue.shade100],
                                        begin: Alignment.topLeft,
                                        end: Alignment.bottomRight,
                                      ),
                                    ),
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        ClipRRect(
                                          borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
                                          child: CachedNetworkImage(
                                            imageUrl: item.imageUrls.first,
                                            width: 200,
                                            height: 120,
                                            fit: BoxFit.cover,
                                            placeholder: (context, url) => _buildShimmerPlaceholder(),
                                            errorWidget: (context, url, error) => const Icon(Icons.error),
                                          ),
                                        ),
                                        Expanded(
                                          child: SingleChildScrollView(
                                            padding: const EdgeInsets.all(8.0),
                                            child: Column(
                                              crossAxisAlignment: CrossAxisAlignment.start,
                                              children: [
                                                Text(item.type,
                                                    style: TextStyle(
                                                        fontWeight: FontWeight.bold,
                                                        color: Colors.blue.shade700,
                                                        fontSize: 14)),
                                                const SizedBox(height: 4),
                                                Text('السعر: ${item.price} ريال',
                                                    style: TextStyle(color: Colors.blue.shade700, fontSize: 12)),
                                                Text('الكمية: ${item.quantity} وحدة',
                                                    style: TextStyle(color: Colors.blue.shade700, fontSize: 12)),
                                                Text('العنوان: ${item.address}',
                                                    style: TextStyle(color: Colors.blue.shade700, fontSize: 12)),
                                                if (item.breed != null)
                                                  Text('السلالة: ${item.breed}',
                                                      style: TextStyle(color: Colors.blue.shade700, fontSize: 12)),
                                                Text('التواصل: ${item.contactNumber}',
                                                    style: TextStyle(color: Colors.blue.shade700, fontSize: 12)),
                                                Text(
                                                  'التاريخ: ${DateFormat('dd/MM/yyyy').format(item.date)}',
                                                  style: TextStyle(color: Colors.blue.shade700, fontSize: 12),
                                                ),
                                                Text('الصلاحية: ${item.validity}',
                                                    style: TextStyle(color: Colors.blue.shade700, fontSize: 12)),
                                                Text('التفاصيل: ${item.details}',
                                                    style: TextStyle(color: Colors.blue.shade700, fontSize: 12),
                                                    maxLines: 2,
                                                    overflow: TextOverflow.ellipsis),
                                              ],
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
            Positioned(
              top: 16,
              left: 16,
              child: FloatingActionButton(
                mini: true,
                onPressed: () => _showAddBirdDialog(context),
                backgroundColor: Colors.blue.shade700,
                child: const Icon(Icons.add),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSuppliesMarketplace() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            colors: [Colors.blue.shade50, Colors.blue.shade100],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Stack(
          children: [
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'سوق المستلزمات',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(color: Colors.blue.shade700),
                  ),
                  const SizedBox(height: 12),
                  DropdownButton<String>(
                    value: selectedMarketFilter,
                    isExpanded: true,
                    items: ['الكل', ...supplyTypes].map((filter) {
                      return DropdownMenuItem<String>(
                        value: filter,
                        child: Text(filter),
                      );
                    }).toList(),
                    onChanged: (value) {
                      setState(() {
                        selectedMarketFilter = value!;
                        Fluttertoast.showToast(msg: 'تم تحديث الفلتر', backgroundColor: Colors.blue.shade700);
                      });
                    },
                    dropdownColor: Colors.blue.shade50,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  const SizedBox(height: 12),
                  SizedBox(
                    height: 280,
                    child: ListView.builder(
                      scrollDirection: Axis.horizontal,
                      itemCount: supplyItems.length,
                      itemBuilder: (context, index) {
                        final item = supplyItems[index];
                        return AnimationConfiguration.staggeredList(
                          position: index,
                          duration: const Duration(milliseconds: 600),
                          child: SlideAnimation(
                            horizontalOffset: 50.0,
                            child: FadeInAnimation(
                              child: Padding(
                                padding: const EdgeInsets.only(right: 12.0),
                                child: Card(
                                  elevation: 3,
                                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                                  child: Container(
                                    width: 200,
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(12),
                                      gradient: LinearGradient(
                                        colors: [Colors.blue.shade50, Colors.blue.shade100],
                                        begin: Alignment.topLeft,
                                        end: Alignment.bottomRight,
                                      ),
                                    ),
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        ClipRRect(
                                          borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
                                          child: CachedNetworkImage(
                                            imageUrl: item.imageUrls.first,
                                            width: 200,
                                            height: 120,
                                            fit: BoxFit.cover,
                                            placeholder: (context, url) => _buildShimmerPlaceholder(),
                                            errorWidget: (context, url, error) => const Icon(Icons.error),
                                          ),
                                        ),
                                        Expanded(
                                          child: SingleChildScrollView(
                                            padding: const EdgeInsets.all(8.0),
                                            child: Column(
                                              crossAxisAlignment: CrossAxisAlignment.start,
                                              children: [
                                                Text(item.type,
                                                    style: TextStyle(
                                                        fontWeight: FontWeight.bold,
                                                        color: Colors.blue.shade700,
                                                        fontSize: 14)),
                                                Text('السعر: ${item.price} ريال',
                                                    style: TextStyle(color: Colors.blue.shade700, fontSize: 12)),
                                                Text('الكمية: ${item.quantity} وحدة',
                                                    style: TextStyle(color: Colors.blue.shade700, fontSize: 12)),
                                                Text('العنوان: ${item.address}',
                                                    style: TextStyle(color: Colors.blue.shade700, fontSize: 12)),
                                                Text('التواصل: ${item.contactNumber}',
                                                    style: TextStyle(color: Colors.blue.shade700, fontSize: 12)),
                                                Text(
                                                  'التاريخ: ${DateFormat('dd/MM/yyyy').format(item.date)}',
                                                  style: TextStyle(color: Colors.blue.shade700, fontSize: 12),
                                                ),
                                                Text('الصلاحية: ${item.validity}',
                                                    style: TextStyle(color: Colors.blue.shade700, fontSize: 12)),
                                                Text('التفاصيل: ${item.details}',
                                                    style: TextStyle(color: Colors.blue.shade700, fontSize: 12),
                                                    maxLines: 2,
                                                    overflow: TextOverflow.ellipsis),
                                              ],
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
            Positioned(
              top: 16,
              left: 16,
              child: FloatingActionButton(
                mini: true,
                onPressed: () => _showAddSupplyDialog(context),
                backgroundColor: Colors.blue.shade700,
                child: const Icon(Icons.add),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeedMarketplace() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            colors: [Colors.blue.shade50, Colors.blue.shade100],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Stack(
          children: [
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'سوق الأعلاف',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(color: Colors.blue.shade700),
                  ),
                  const SizedBox(height: 12),
                  DropdownButton<String>(
                    value: selectedMarketFilter,
                    isExpanded: true,
                    items: ['الكل', ...feedTypes].map((filter) {
                      return DropdownMenuItem<String>(
                        value: filter,
                        child: Text(filter),
                      );
                    }).toList(),
                    onChanged: (value) {
                      setState(() {
                        selectedMarketFilter = value!;
                        Fluttertoast.showToast(msg: 'تم تحديث الفلتر', backgroundColor: Colors.blue.shade700);
                      });
                    },
                    dropdownColor: Colors.blue.shade50,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  const SizedBox(height: 12),
                  SizedBox(
                    height: 280,
                    child: ListView.builder(
                      scrollDirection: Axis.horizontal,
                      itemCount: feedItems.length,
                      itemBuilder: (context, index) {
                        final item = feedItems[index];
                        return AnimationConfiguration.staggeredList(
                          position: index,
                          duration: const Duration(milliseconds: 600),
                          child: SlideAnimation(
                            horizontalOffset: 50.0,
                            child: FadeInAnimation(
                              child: Padding(
                                padding: const EdgeInsets.only(right: 12.0),
                                child: Card(
                                  elevation: 3,
                                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                                  child: Container(
                                    width: 200,
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(12),
                                      gradient: LinearGradient(
                                        colors: [Colors.blue.shade50, Colors.blue.shade100],
                                        begin: Alignment.topLeft,
                                        end: Alignment.bottomRight,
                                      ),
                                    ),
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        ClipRRect(
                                          borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
                                          child: CachedNetworkImage(
                                            imageUrl: item.imageUrls.first,
                                            width: 200,
                                            height: 120,
                                            fit: BoxFit.cover,
                                            placeholder: (context, url) => _buildShimmerPlaceholder(),
                                            errorWidget: (context, url, error) => const Icon(Icons.error),
                                          ),
                                        ),
                                        Expanded(
                                          child: SingleChildScrollView(
                                            padding: const EdgeInsets.all(8.0),
                                            child: Column(
                                              crossAxisAlignment: CrossAxisAlignment.start,
                                              children: [
                                                Text(item.type,
                                                    style: TextStyle(
                                                        fontWeight: FontWeight.bold,
                                                        color: Colors.blue.shade700,
                                                        fontSize: 14)),
                                                Text('السعر: ${item.price} ريال',
                                                    style: TextStyle(color: Colors.blue.shade700, fontSize: 12)),
                                                Text('الكمية: ${item.quantity} كجم',
                                                    style: TextStyle(color: Colors.blue.shade700, fontSize: 12)),
                                                Text('العنوان: ${item.address}',
                                                    style: TextStyle(color: Colors.blue.shade700, fontSize: 12)),
                                                if (item.nutritionalContent != null)
                                                  Text('المحتوى الغذائي: ${item.nutritionalContent}',
                                                      style: TextStyle(color: Colors.blue.shade700, fontSize: 12)),
                                                Text('التواصل: ${item.contactNumber}',
                                                    style: TextStyle(color: Colors.blue.shade700, fontSize: 12)),
                                                Text(
                                                  'التاريخ: ${DateFormat('dd/MM/yyyy').format(item.date)}',
                                                  style: TextStyle(color: Colors.blue.shade700, fontSize: 12),
                                                ),
                                                Text('الصلاحية: ${item.validity}',
                                                    style: TextStyle(color: Colors.blue.shade700, fontSize: 12)),
                                                Text('التفاصيل: ${item.details}',
                                                    style: TextStyle(color: Colors.blue.shade700, fontSize: 12),
                                                    maxLines: 2,
                                                    overflow: TextOverflow.ellipsis),
                                              ],
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
            Positioned(
              top: 16,
              left: 16,
              child: FloatingActionButton(
                mini: true,
                onPressed: () => _showAddFeedDialog(context),
                backgroundColor: Colors.blue.shade700,
                child: const Icon(Icons.add),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showAddBirdDialog(BuildContext context) {
    final quantityController = TextEditingController();
    final priceController = TextEditingController();
    final addressController = TextEditingController();
    final contactController = TextEditingController();
    final detailsController = TextEditingController();
    final breedController = TextEditingController();
    String selectedBirdType = birdTypes.first;
    String selectedValidity = 'أسبوع';
    List<String> imageUrls = [];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('إضافة طائر', style: TextStyle(color: Colors.blue.shade700)),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              DropdownButton<String>(
                value: selectedBirdType,
                isExpanded: true,
                items: birdTypes.map((String type) {
                  return DropdownMenuItem<String>(
                    value: type,
                    child: Text(type),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  setState(() {
                    selectedBirdType = newValue!;
                  });
                },
                dropdownColor: Colors.blue.shade50,
              ),
              const SizedBox(height: 8),
              TextField(
                controller: quantityController,
                decoration: InputDecoration(
                  hintText: 'الكمية (وحدة)',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.blue.shade50,
                ),
                keyboardType: TextInputType.number,
              ),
              const SizedBox(height: 8),
              TextField(
                controller: priceController,
                decoration: InputDecoration(
                  hintText: 'السعر (ريال)',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.blue.shade50,
                ),
                keyboardType: TextInputType.number,
              ),
              const SizedBox(height: 8),
              TextField(
                controller: addressController,
                decoration: InputDecoration(
                  hintText: 'العنوان',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.blue.shade50,
                  suffixIcon: IconButton(
                    icon: Icon(Icons.location_on, color: Colors.blue.shade700),
                    onPressed: () async {
                      try {
                        Position position = await Geolocator.getCurrentPosition();
                        addressController.text = 'موقع جغرافي: ${position.latitude}, ${position.longitude}';
                      } catch (e) {
                        Fluttertoast.showToast(msg: 'خطأ في تحديد الموقع', backgroundColor: Colors.red);
                      }
                    },
                  ),
                ),
              ),
              const SizedBox(height: 8),
              DropdownButton<String>(
                value: selectedValidity,
                isExpanded: true,
                items: ['أسبوع', 'أسبوعان', 'شهر'].map((String validity) {
                  return DropdownMenuItem<String>(
                    value: validity,
                    child: Text(validity),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  setState(() {
                    selectedValidity = newValue!;
                  });
                },
                dropdownColor: Colors.blue.shade50,
              ),
              const SizedBox(height: 8),
              TextField(
                controller: contactController,
                decoration: InputDecoration(
                  hintText: 'رقم التواصل',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.blue.shade50,
                ),
                keyboardType: TextInputType.phone,
              ),
              const SizedBox(height: 8),
              TextField(
                controller: breedController,
                decoration: InputDecoration(
                  hintText: 'السلالة (مثل: رمادي أفريقي)',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.blue.shade50,
                ),
              ),
              const SizedBox(height: 8),
              TextField(
                controller: detailsController,
                decoration: InputDecoration(
                  hintText: 'تفاصيل الطائر',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.blue.shade50,
                ),
                maxLines: 3,
              ),
              const SizedBox(height: 8),
              ElevatedButton(
                onPressed: () {
                  imageUrls = List.generate(4, (index) => 'https://cdn-icons-png.flaticon.com/512/616/616408.png');
                  if (imageUrls.length < 4) {
                    Fluttertoast.showToast(msg: 'يرجى رفع 4 صور على الأقل', backgroundColor: Colors.red);
                    return;
                  }
                  if (quantityController.text.isEmpty ||
                      priceController.text.isEmpty ||
                      addressController.text.isEmpty ||
                      contactController.text.isEmpty ||
                      detailsController.text.isEmpty) {
                    Fluttertoast.showToast(msg: 'يرجى ملء جميع الحقول', backgroundColor: Colors.red);
                    return;
                  }
                  setState(() {
                    birdItems.add(MarketItem(
                      id: DateTime.now().millisecondsSinceEpoch.toString(),
                      user: 'Current User',
                      type: selectedBirdType,
                      imageUrls: imageUrls,
                      quantity: double.parse(quantityController.text),
                      price: double.parse(priceController.text),
                      address: addressController.text,
                      date: DateTime.now(),
                      validity: selectedValidity,
                      contactNumber: contactController.text,
                      details: detailsController.text,
                      breed: breedController.text.isNotEmpty ? breedController.text : null,
                    ));
                    userPoints += 15;
                    Fluttertoast.showToast(msg: 'تمت إضافة الطائر', backgroundColor: Colors.blue.shade700);
                    _confettiController.play();
                  });
                  Navigator.pop(context);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue.shade700,
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                ),
                child: const Text('إضافة'),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
        ],
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        backgroundColor: Colors.blue.shade50,
      ),
    );
  }

  void _showAddSupplyDialog(BuildContext context) {
    final quantityController = TextEditingController();
    final priceController = TextEditingController();
    final addressController = TextEditingController();
    final contactController = TextEditingController();
    final detailsController = TextEditingController();
    String selectedSupplyType = supplyTypes.first;
    String selectedValidity = 'أسبوع';
    List<String> imageUrls = [];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('إضافة مستلزمات', style: TextStyle(color: Colors.blue.shade700)),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              DropdownButton<String>(
                value: selectedSupplyType,
                isExpanded: true,
                items: supplyTypes.map((String type) {
                  return DropdownMenuItem<String>(
                    value: type,
                    child: Text(type),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  setState(() {
                    selectedSupplyType = newValue!;
                  });
                },
                dropdownColor: Colors.blue.shade50,
              ),
              const SizedBox(height: 8),
              TextField(
                controller: quantityController,
                decoration: InputDecoration(
                  hintText: 'الكمية (وحدة)',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.blue.shade50,
                ),
                keyboardType: TextInputType.number,
              ),
              const SizedBox(height: 8),
              TextField(
                controller: priceController,
                decoration: InputDecoration(
                  hintText: 'السعر (ريال)',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.blue.shade50,
                ),
                keyboardType: TextInputType.number,
              ),
              const SizedBox(height: 8),
              TextField(
                controller: addressController,
                decoration: InputDecoration(
                  hintText: 'العنوان',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.blue.shade50,
                  suffixIcon: IconButton(
                    icon: Icon(Icons.location_on, color: Colors.blue.shade700),
                    onPressed: () async {
                      try {
                        Position position = await Geolocator.getCurrentPosition();
                        addressController.text = 'موقع جغرافي: ${position.latitude}, ${position.longitude}';
                      } catch (e) {
                        Fluttertoast.showToast(msg: 'خطأ في تحديد الموقع', backgroundColor: Colors.red);
                      }
                    },
                  ),
                ),
              ),
              const SizedBox(height: 8),
              DropdownButton<String>(
                value: selectedValidity,
                isExpanded: true,
                items: ['أسبوع', 'أسبوعان', 'شهر'].map((String validity) {
                  return DropdownMenuItem<String>(
                    value: validity,
                    child: Text(validity),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  setState(() {
                    selectedValidity = newValue!;
                  });
                },
                dropdownColor: Colors.blue.shade50,
              ),
              const SizedBox(height: 8),
              TextField(
                controller: contactController,
                decoration: InputDecoration(
                  hintText: 'رقم التواصل',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.blue.shade50,
                ),
                keyboardType: TextInputType.phone,
              ),
              const SizedBox(height: 8),
              TextField(
                controller: detailsController,
                decoration: InputDecoration(
                  hintText: 'تفاصيل المستلزمات',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.blue.shade50,
                ),
                maxLines: 3,
              ),
              const SizedBox(height: 8),
              ElevatedButton(
                onPressed: () {
                  imageUrls = List.generate(4, (index) => 'https://cdn-icons-png.flaticon.com/512/3063/3063174.png');
                  if (imageUrls.length < 4) {
                    Fluttertoast.showToast(msg: 'يرجى رفع 4 صور على الأقل', backgroundColor: Colors.red);
                    return;
                  }
                  if (quantityController.text.isEmpty ||
                      priceController.text.isEmpty ||
                      addressController.text.isEmpty ||
                      contactController.text.isEmpty ||
                      detailsController.text.isEmpty) {
                    Fluttertoast.showToast(msg: 'يرجى ملء جميع الحقول', backgroundColor: Colors.red);
                    return;
                  }
                  setState(() {
                    supplyItems.add(MarketItem(
                      id: DateTime.now().millisecondsSinceEpoch.toString(),
                      user: 'Current User',
                      type: selectedSupplyType,
                      imageUrls: imageUrls,
                      quantity: double.parse(quantityController.text),
                      price: double.parse(priceController.text),
                      address: addressController.text,
                      date: DateTime.now(),
                      validity: selectedValidity,
                      contactNumber: contactController.text,
                      details: detailsController.text,
                    ));
                    userPoints += 15;
                    Fluttertoast.showToast(msg: 'تمت إضافة المستلزمات', backgroundColor: Colors.blue.shade700);
                    _confettiController.play();
                  });
                  Navigator.pop(context);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue.shade700,
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                ),
                child: const Text('إضافة'),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
        ],
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        backgroundColor: Colors.blue.shade50,
      ),
    );
  }

  void _showAddFeedDialog(BuildContext context) {
    final quantityController = TextEditingController();
    final priceController = TextEditingController();
    final addressController = TextEditingController();
    final contactController = TextEditingController();
    final detailsController = TextEditingController();
    final nutritionalContentController = TextEditingController();
    String selectedFeedType = feedTypes.first;
    String selectedValidity = 'أسبوع';
    List<String> imageUrls = [];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('إضافة علف', style: TextStyle(color: Colors.blue.shade700)),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              DropdownButton<String>(
                value: selectedFeedType,
                isExpanded: true,
                items: feedTypes.map((String type) {
                  return DropdownMenuItem<String>(
                    value: type,
                    child: Text(type),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  setState(() {
                    selectedFeedType = newValue!;
                  });
                },
                dropdownColor: Colors.blue.shade50,
              ),
              const SizedBox(height: 8),
              TextField(
                controller: quantityController,
                decoration: InputDecoration(
                  hintText: 'الكمية (كجم)',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.blue.shade50,
                ),
                keyboardType: TextInputType.number,
              ),
              const SizedBox(height: 8),
              TextField(
                controller: priceController,
                decoration: InputDecoration(
                  hintText: 'السعر (ريال)',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.blue.shade50,
                ),
                keyboardType: TextInputType.number,
              ),
              const SizedBox(height: 8),
              TextField(
                controller: addressController,
                decoration: InputDecoration(
                  hintText: 'العنوان',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.blue.shade50,
                  suffixIcon: IconButton(
                    icon: Icon(Icons.location_on, color: Colors.blue.shade700),
                    onPressed: () async {
                      try {
                        Position position = await Geolocator.getCurrentPosition();
                        addressController.text = 'موقع جغرافي: ${position.latitude}, ${position.longitude}';
                      } catch (e) {
                        Fluttertoast.showToast(msg: 'خطأ في تحديد الموقع', backgroundColor: Colors.red);
                      }
                    },
                  ),
                ),
              ),
              const SizedBox(height: 8),
              DropdownButton<String>(
                value: selectedValidity,
                isExpanded: true,
                items: ['أسبوع', 'أسبوعان', 'شهر'].map((String validity) {
                  return DropdownMenuItem<String>(
                    value: validity,
                    child: Text(validity),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  setState(() {
                    selectedValidity = newValue!;
                  });
                },
                dropdownColor: Colors.blue.shade50,
              ),
              const SizedBox(height: 8),
              TextField(
                controller: contactController,
                decoration: InputDecoration(
                  hintText: 'رقم التواصل',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.blue.shade50,
                ),
                keyboardType: TextInputType.phone,
              ),
              const SizedBox(height: 8),
              TextField(
                controller: nutritionalContentController,
                decoration: InputDecoration(
                  hintText: 'المحتوى الغذائي (مثل: بروتين 15%)',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.blue.shade50,
                ),
              ),
              const SizedBox(height: 8),
              TextField(
                controller: detailsController,
                decoration: InputDecoration(
                  hintText: 'تفاصيل العلف',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.blue.shade50,
                ),
                maxLines: 3,
              ),
              const SizedBox(height: 8),
              ElevatedButton(
                onPressed: () {
                  imageUrls = List.generate(4, (index) => 'https://cdn-icons-png.flaticon.com/512/2921/2921914.png');
                  if (imageUrls.length < 4) {
                    Fluttertoast.showToast(msg: 'يرجى رفع 4 صور على الأقل', backgroundColor: Colors.red);
                    return;
                  }
                  if (quantityController.text.isEmpty ||
                      priceController.text.isEmpty ||
                      addressController.text.isEmpty ||
                      contactController.text.isEmpty ||
                      detailsController.text.isEmpty) {
                    Fluttertoast.showToast(msg: 'يرجى ملء جميع الحقول', backgroundColor: Colors.red);
                    return;
                  }
                  setState(() {
                    feedItems.add(MarketItem(
                      id: DateTime.now().millisecondsSinceEpoch.toString(),
                      user: 'Current User',
                      type: selectedFeedType,
                      imageUrls: imageUrls,
                      quantity: double.parse(quantityController.text),
                      price: double.parse(priceController.text),
                      address: addressController.text,
                      date: DateTime.now(),
                      validity: selectedValidity,
                      contactNumber: contactController.text,
                      details: detailsController.text,
                      nutritionalContent:
                      nutritionalContentController.text.isNotEmpty ? nutritionalContentController.text : null,
                    ));
                    userPoints += 15;
                    Fluttertoast.showToast(msg: 'تمت إضافة العلف', backgroundColor: Colors.blue.shade700);
                    _confettiController.play();
                  });
                  Navigator.pop(context);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue.shade700,
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                ),
                child: const Text('إضافة'),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
        ],
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        backgroundColor: Colors.blue.shade50,
      ),
    );
  }

  void _showBirdDetails(BuildContext context, BirdBreedingGroup bird) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.9,
        minChildSize: 0.5,
        maxChildSize: 0.95,
        builder: (context, scrollController) => Container(
          decoration: BoxDecoration(
            color: Theme.of(context).scaffoldBackgroundColor,
            borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
            boxShadow: [
              BoxShadow(
                color: Colors.blue.withOpacity(0.3),
                blurRadius: 10,
                offset: const Offset(0, -5),
              ),
            ],
          ),
          child: DefaultTabController(
            length: 6,
            child: Column(
              children: [
                Container(
                  margin: const EdgeInsets.symmetric(vertical: 8),
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: Colors.grey,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                TabBar(
                  tabs: const [
                    Tab(text: 'الفيديوهات'),
                    Tab(text: 'المقالات'),
                    Tab(text: 'الأسئلة الشائعة'),
                    Tab(text: 'الأمراض'),
                    Tab(text: 'التغذية'),
                    Tab(text: 'معرض الصور'),
                  ],
                  labelColor: Colors.blue.shade700,
                  unselectedLabelColor: Colors.grey,
                  indicatorColor: Colors.blue.shade700,
                ),
                Expanded(
                  child: TabBarView(
                    children: [
                      _buildVideosTab(bird.videos),
                      _buildArticlesTab(bird.articles),
                      _buildFAQsTab(bird.faqs),
                      _buildDiseasesTab(),
                      _buildNutritionTab(bird.nutritionGuide),
                      _buildGalleryTab(bird.galleryImages),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
    Fluttertoast.showToast(msg: 'تم عرض تفاصيل ${bird.name}', backgroundColor: Colors.blue.shade700);
  }

  Widget _buildVideosTab(List<Video> videos) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: videos.length,
      itemBuilder: (context, index) {
        final video = videos[index];
        return AnimationConfiguration.staggeredList(
          position: index,
          duration: const Duration(milliseconds: 600),
          child: SlideAnimation(
            verticalOffset: 50.0,
            child: FadeInAnimation(
              child: ListTile(
                leading: Icon(Icons.videocam, color: Colors.blue.shade700),
                title: Text(video.title, style: TextStyle(color: Colors.blue.shade700)),
                onTap: () {
                  ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('تشغيل ${video.title}')));
                  Fluttertoast.showToast(msg: 'جارٍ تشغيل الفيديو', backgroundColor: Colors.blue.shade700);
                },
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildArticlesTab(List<Article> articles) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: articles.length,
      itemBuilder: (context, index) {
        final article = articles[index];
        return AnimationConfiguration.staggeredList(
          position: index,
          duration: const Duration(milliseconds: 600),
          child: SlideAnimation(
            verticalOffset: 50.0,
            child: FadeInAnimation(
              child: ExpansionTile(
                leading: Icon(Icons.article, color: Colors.blue.shade700),
                title: Text(article.title, style: TextStyle(color: Colors.blue.shade700)),
                tilePadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                collapsedBackgroundColor: Colors.blue.shade50,
                backgroundColor: Colors.blue.shade100,
                children: [
                  Padding(
                    padding: const EdgeInsets.all(16),
                    child: Text(article.content, style: TextStyle(color: Colors.blue.shade700)),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildFAQsTab(List<FAQ> faqs) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: faqs.length,
      itemBuilder: (context, index) {
        final faq = faqs[index];
        return AnimationConfiguration.staggeredList(
          position: index,
          duration: const Duration(milliseconds: 600),
          child: SlideAnimation(
            verticalOffset: 50.0,
            child: FadeInAnimation(
              child: ExpansionTile(
                leading: Icon(Icons.question_answer, color: Colors.blue.shade700),
                title: Text(faq.question, style: TextStyle(color: Colors.blue.shade700)),
                tilePadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                collapsedBackgroundColor: Colors.blue.shade50,
                backgroundColor: Colors.blue.shade100,
                children: [
                  Padding(
                    padding: const EdgeInsets.all(16),
                    child: Text(faq.answer, style: TextStyle(color: Colors.blue.shade700)),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildDiseasesTab() {
    final List<Map<String, String>> diseases = [
      {
        'title': 'الإسهال',
        'description': 'سببها التغذية السيئة أو العدوى. العلاج: تحسين النظام الغذائي وزيارة الطبيب البيطري.'
      },
      {
        'title': 'فقدان الريش',
        'description': 'قد يكون بسبب الإجهاد أو نقص التغذية. العلاج: توفير بيئة هادئة ومكملات غذائية.'
      },
    ];

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: diseases.length,
      itemBuilder: (context, index) {
        final disease = diseases[index];
        return AnimationConfiguration.staggeredList(
          position: index,
          duration: const Duration(milliseconds: 600),
          child: SlideAnimation(
            verticalOffset: 50.0,
            child: FadeInAnimation(
              child: ExpansionTile(
                leading: Icon(Icons.medical_services, color: Colors.blue.shade700),
                title: Text(disease['title']!, style: TextStyle(color: Colors.blue.shade700)),
                tilePadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                collapsedBackgroundColor: Colors.blue.shade50,
                backgroundColor: Colors.blue.shade100,
                children: [
                  Padding(
                    padding: const EdgeInsets.all(16),
                    child: Text(disease['description']!, style: TextStyle(color: Colors.blue.shade700)),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildNutritionTab(String nutritionGuide) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Text(
        'دليل التغذية:\n$nutritionGuide\n\n'
            'نصائح إضافية:\n'
            '- توفير مياه نظيفة يومياً.\n'
            '- تجنب الأطعمة الدهنية أو المالحة.\n'
            '- استشارة الطبيب البيطري لجدول تطعيمات.',
        style: TextStyle(color: Colors.blue.shade700),
      ),
    );
  }

  Widget _buildGalleryTab(List<String> galleryImages) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      scrollDirection: Axis.horizontal,
      itemCount: galleryImages.length,
      itemBuilder: (context, index) {
        final imageUrl = galleryImages[index];
        return AnimationConfiguration.staggeredList(
          position: index,
          duration: const Duration(milliseconds: 600),
          child: SlideAnimation(
            horizontalOffset: 50.0,
            child: FadeInAnimation(
              child: Padding(
                padding: const EdgeInsets.only(right: 12.0),
                child: GestureDetector(
                  onTap: () {
                    Share.share('تحقق من هذه الصورة: $imageUrl');
                    Fluttertoast.showToast(msg: 'تم مشاركة الصورة', backgroundColor: Colors.blue.shade700);
                  },
                  child: CachedNetworkImage(
                    imageUrl: imageUrl,
                    width: 120,
                    height: 120,
                    fit: BoxFit.cover,
                    placeholder: (context, url) => _buildShimmerPlaceholder(),
                    errorWidget: (context, url, error) => const Icon(Icons.error),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
  }) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, size: 20),
      label: Text(label, style: const TextStyle(fontSize: 14)),
      style: ElevatedButton.styleFrom(
        foregroundColor: Colors.blue.shade700, 
        backgroundColor: Colors.blue.shade50,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),
    );
  }

  Widget _buildPostInput() {
    return Card(
      elevation: 8,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            TextField(
              controller: _postController,
              decoration: InputDecoration(
                hintText: 'ما الذي تفكر به؟',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: Theme.of(context).scaffoldBackgroundColor,
                hintStyle: const TextStyle(fontFamily: 'Tajawal'),
              ),
              maxLines: 3,
              onSubmitted: (value) {
                if (value.isNotEmpty) {
                  setState(() {
                    posts.insert(
                      0,
                      Post(user: 'Current User', content: value),
                    );
                    userPoints += 10;
                    Fluttertoast.showToast(msg: 'تم نشر المنشور', backgroundColor: Colors.blue.shade700);
                    _confettiController.play();
                    _postController.clear();
                  });
                }
              },
            ),
            const SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildActionButton(
                  icon: Icons.image,
                  label: 'صورة',
                  onPressed: () async {
                    final picker = ImagePicker();
                    final image = await picker.pickImage(source: ImageSource.gallery);
                    if (image != null) {
                      setState(() {
                        posts.insert(
                          0,
                          Post(
                            user: 'Current User',
                            content: 'منشور مع صورة',
                            imageUrl: image.path,
                          ),
                        );
                        userPoints += 15;
                        Fluttertoast.showToast(
                          msg: 'تم نشر الصورة',
                          backgroundColor: Colors.blue.shade700,
                        );
                        _confettiController.play();
                      });
                    }
                  },
                ),
                _buildActionButton(
                  icon: Icons.videocam,
                  label: 'فيديو',
                  onPressed: () async {
                    final connectivityResult = await Connectivity().checkConnectivity();
                    if (connectivityResult == ConnectivityResult.none) {
                      Fluttertoast.showToast(
                        msg: 'لا يوجد اتصال بالإنترنت',
                        backgroundColor: Colors.red,
                      );
                      return;
                    }
                    final picker = ImagePicker();
                    final video = await picker.pickVideo(source: ImageSource.gallery);
                    if (video != null) {
                      // Handle video post
                      Fluttertoast.showToast(
                        msg: 'تم رفع الفيديو',
                        backgroundColor: Colors.blue.shade700,
                      );
                    }
                  },
                ),
                _buildActionButton(
                  icon: Icons.camera_alt,
                  label: 'قصة',
                  onPressed: () async {
                    final picker = ImagePicker();
                    final image = await picker.pickImage(source: ImageSource.camera);
                    if (image != null) {
                      // Handle story
                      Fluttertoast.showToast(
                        msg: 'تم إنشاء قصة جديدة',
                        backgroundColor: Colors.blue.shade700,
                      );
                    }
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPostsList() {
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: posts.length,
      itemBuilder: (context, index) {
        final post = posts[index];
        return AnimationConfiguration.staggeredList(
          position: index,
          duration: const Duration(milliseconds: 600),
          child: SlideAnimation(
            verticalOffset: 50.0,
            child: FadeInAnimation(
              child: Card(
                margin: const EdgeInsets.symmetric(vertical: 8),
                elevation: 4,
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    gradient: LinearGradient(
                      colors: [Colors.blue.shade50, Colors.blue.shade100],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            CircleAvatar(
                              backgroundColor: Colors.blue.shade700,
                              child: Text(post.user[0]),
                            ),
                            const SizedBox(width: 8),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(post.user,
                                    style: TextStyle(fontWeight: FontWeight.bold, color: Colors.blue.shade700)),
                                Text(
                                  DateFormat('dd/MM/yyyy HH:mm').format(DateTime.now()),
                                  style: TextStyle(fontSize: 12, color: Colors.blue.shade700),
                                ),
                              ],
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Text(post.content, style: TextStyle(color: Colors.blue.shade700)),
                        if (post.imageUrl != null) ...[
                          const SizedBox(height: 8),
                          ClipRRect(
                            borderRadius: BorderRadius.circular(12),
                            child: CachedNetworkImage(
                              imageUrl: post.imageUrl!,
                              height: 150,
                              width: double.infinity,
                              fit: BoxFit.cover,
                              placeholder: (context, url) => _buildShimmerPlaceholder(),
                              errorWidget: (context, url, error) => const Icon(Icons.error),
                            ),
                          ),
                        ],
                        const SizedBox(height: 8),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Row(
                              children: [
                                Icon(Icons.thumb_up_alt, color: Colors.blue.shade700, size: 20),
                                const SizedBox(width: 4),
                                Text('${post.likes}', style: TextStyle(color: Colors.blue.shade700)),
                              ],
                            ),
                            Text('${post.comments.length} تعليقات', style: TextStyle(color: Colors.blue.shade700)),
                          ],
                        ),
                        const Divider(),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                          children: [
                            TextButton.icon(
                              icon: Icon(Icons.thumb_up_alt_outlined, color: Colors.blue.shade700),
                              label: Text('إعجاب', style: TextStyle(color: Colors.blue.shade700)),
                              onPressed: () {
                                setState(() {
                                  posts[index] = Post(
                                    user: post.user,
                                    content: post.content,
                                    imageUrl: post.imageUrl,
                                    likes: post.likes + 1,
                                    comments: post.comments,
                                  );
                                  userPoints += 2;
                                  Fluttertoast.showToast(
                                      msg: 'تم الإعجاب بالمنشور', backgroundColor: Colors.blue.shade700);
                                });
                              },
                            ),
                            TextButton.icon(
                              icon: Icon(Icons.comment, color: Colors.blue.shade700),
                              label: Text('تعليق', style: TextStyle(color: Colors.blue.shade700)),
                              onPressed: () => _showCommentDialog(context, index),
                            ),
                            TextButton.icon(
                              icon: Icon(Icons.share, color: Colors.blue.shade700),
                              label: Text('مشاركة', style: TextStyle(color: Colors.blue.shade700)),
                              onPressed: () {
                                Share.share('تحقق من هذا المنشور: ${post.content}');
                                Fluttertoast.showToast(msg: 'تم مشاركة المنشور', backgroundColor: Colors.blue.shade700);
                              },
                            ),
                          ],
                        ),
                        if (post.comments.isNotEmpty) ...[
                          const Divider(),
                          ListView.builder(
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                            itemCount: post.comments.length,
                            itemBuilder: (context, commentIndex) {
                              return Padding(
                                padding: const EdgeInsets.symmetric(vertical: 4),
                                child: Row(
                                  children: [
                                    CircleAvatar(
                                      backgroundColor: Colors.blue.shade700,
                                      radius: 15,
                                      child: Text('U', style: TextStyle(color: Colors.white)),
                                    ),
                                    const SizedBox(width: 8),
                                    Expanded(
                                      child: Text(
                                        post.comments[commentIndex],
                                        style: TextStyle(color: Colors.blue.shade700),
                                      ),
                                    ),
                                  ],
                                ),
                              );
                            },
                          ),
                        ],
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  void _showCommentDialog(BuildContext context, int postIndex) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('إضافة تعليق', style: TextStyle(color: Colors.blue.shade700)),
        content: TextField(
          controller: _commentController,
          decoration: InputDecoration(
            hintText: 'اكتب تعليقك...',
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
            filled: true,
            fillColor: Colors.blue.shade50,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              if (_commentController.text.isNotEmpty) {
                setState(() {
                  final updatedComments = List<String>.from(posts[postIndex].comments)
                    ..add(_commentController.text);
                  posts[postIndex] = Post(
                    user: posts[postIndex].user,
                    content: posts[postIndex].content,
                    imageUrl: posts[postIndex].imageUrl,
                    likes: posts[postIndex].likes,
                    comments: updatedComments,
                  );
                  userPoints += 5;
                  Fluttertoast.showToast(msg: 'تم إضافة التعليق', backgroundColor: Colors.blue.shade700);
                  _commentController.clear();
                });
                Navigator.pop(context);
              } else {
                Fluttertoast.showToast(msg: 'يرجى إدخال نص التعليق', backgroundColor: Colors.red);
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue.shade700,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            ),
            child: const Text('إرسال'),
          ),
        ],
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        backgroundColor: Colors.blue.shade50,
      ),
    );
  }

  void _showSearchDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('بحث عن الطيور', style: TextStyle(color: Colors.blue.shade700)),
        content: TextField(
          controller: _searchController,
          decoration: InputDecoration(
            hintText: 'أدخل اسم الطائر...',
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
            filled: true,
            fillColor: Colors.blue.shade50,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () {
              String query = _searchController.text.toLowerCase();
              var results = birds.where((bird) => bird.name.toLowerCase().contains(query)).toList();
              Navigator.pop(context);
              if (results.isNotEmpty) {
                _showBirdDetails(context, results.first);
                Fluttertoast.showToast(msg: 'تم العثور على الطائر', backgroundColor: Colors.blue.shade700);
              } else {
                ScaffoldMessenger.of(context)
                    .showSnackBar(const SnackBar(content: Text('لم يتم العثور على طيور')));
                Fluttertoast.showToast(msg: 'لا توجد نتائج', backgroundColor: Colors.red);
              }
            },
            child: const Text('بحث'),
          ),
        ],
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        backgroundColor: Colors.blue.shade50,
      ),
    );
  }

  void _showQuickActions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.85,
        ),
        decoration: BoxDecoration(
          color: Theme.of(context).scaffoldBackgroundColor,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
          boxShadow: [
            BoxShadow(
              color: Colors.blue.withOpacity(0.3),
              blurRadius: 10,
              offset: const Offset(0, -5),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              margin: const EdgeInsets.symmetric(vertical: 8),
              width: 40,
              height: 5,
              decoration: BoxDecoration(
                color: Colors.grey.shade400,
                borderRadius: BorderRadius.circular(2.5),
              ),
            ),
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    _buildQuickActionTile(
                      context,
                      icon: Icons.post_add,
                      title: 'إنشاء منشور',
                      onTap: () {
                        Navigator.pop(context);
                        _postController.text = '';
                        FocusScope.of(context).requestFocus(FocusNode());
                        Fluttertoast.showToast(msg: 'جاهز لإنشاء منشور', backgroundColor: Colors.blue.shade700);
                      },
                    ),
                    _buildQuickActionTile(
                      context,
                      icon: Icons.camera_alt,
                      title: 'مشاركة صورة',
                      onTap: () {
                        Navigator.pop(context);
                        ScaffoldMessenger.of(context)
                            .showSnackBar(const SnackBar(content: Text('مشاركة الصور قيد التطوير')));
                        Fluttertoast.showToast(msg: 'مشاركة الصور قريبًا', backgroundColor: Colors.blue.shade700);
                      },
                    ),
                    _buildQuickActionTile(
                      context,
                      icon: Icons.pets,
                      title: 'إضافة طائر للسوق',
                      onTap: () {
                        Navigator.pop(context);
                        _showAddBirdDialog(context);
                        Fluttertoast.showToast(msg: 'جاهز لإضافة طائر', backgroundColor: Colors.blue.shade700);
                      },
                    ),
                    _buildQuickActionTile(
                      context,
                      icon: Icons.build,
                      title: 'إضافة مستلزمات للسوق',
                      onTap: () {
                        Navigator.pop(context);
                        _showAddSupplyDialog(context);
                        Fluttertoast.showToast(msg: 'جاهز لإضافة مستلزمات', backgroundColor: Colors.blue.shade700);
                      },
                    ),
                    _buildQuickActionTile(
                      context,
                      icon: Icons.food_bank,
                      title: 'إضافة علف للسوق',
                      onTap: () {
                        Navigator.pop(context);
                        _showAddFeedDialog(context);
                        Fluttertoast.showToast(msg: 'جاهز لإضافة علف', backgroundColor: Colors.blue.shade700);
                      },
                    ),
                    if (Provider.of<AppState>(context, listen: false).currentUser?.role.name == 'hokama' ||
                        Provider.of<AppState>(context, listen: false).userType == 'hokama')
                      _buildQuickActionTile(
                        context,
                        icon: Icons.add_circle,
                        title: 'إضافة نوع طائر جديد',
                        onTap: () {
                          Navigator.pop(context);
                          _showAddNewBirdTypeDialog(context);
                        },
                      ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionTile(
      BuildContext context, {
        required IconData icon,
        required String title,
        required VoidCallback onTap,
      }) {
    return ListTile(
      leading: Icon(icon, color: Colors.blue.shade700, size: 24),
      title: Text(
        title,
        style: const TextStyle(
          fontFamily: 'Tajawal',
          fontSize: 16,
          fontWeight: FontWeight.w500,
        ),
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 4),
      onTap: onTap,
    );
  }

  void _showAddNewBirdTypeDialog(BuildContext context) {
    final TextEditingController nameController = TextEditingController();
    final TextEditingController descriptionController = TextEditingController();
    final TextEditingController videosController = TextEditingController();
    final TextEditingController articlesController = TextEditingController();
    final TextEditingController faqController = TextEditingController();
    final TextEditingController nutritionController = TextEditingController();
    final TextEditingController galleryController = TextEditingController();
    List<String> galleryImages = [];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('إضافة نوع طائر جديد', style: TextStyle(color: Colors.blue.shade700)),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: nameController,
                decoration: InputDecoration(
                  hintText: 'اسم الطائر',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.blue.shade50,
                ),
              ),
              const SizedBox(height: 8),
              TextField(
                controller: descriptionController,
                decoration: InputDecoration(
                  hintText: 'الوصف',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.blue.shade50,
                ),
                maxLines: 3,
              ),
              const SizedBox(height: 8),
              TextField(
                controller: videosController,
                decoration: InputDecoration(
                  hintText: 'روابط الفيديوهات (مفصولة بفواصل)',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.blue.shade50,
                ),
              ),
              const SizedBox(height: 8),
              TextField(
                controller: articlesController,
                decoration: InputDecoration(
                  hintText: 'المقالات (عنوان:محتوى, عنوان:محتوى)',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.blue.shade50,
                ),
                maxLines: 3,
              ),
              const SizedBox(height: 8),
              TextField(
                controller: faqController,
                decoration: InputDecoration(
                  hintText: 'الأسئلة الشائعة (سؤال:إجابة, سؤال:إجابة)',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.blue.shade50,
                ),
                maxLines: 3,
              ),
              const SizedBox(height: 8),
              TextField(
                controller: nutritionController,
                decoration: InputDecoration(
                  hintText: 'دليل التغذية',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.blue.shade50,
                ),
                maxLines: 3,
              ),
              const SizedBox(height: 8),
              TextField(
                controller: galleryController,
                decoration: InputDecoration(
                  hintText: 'روابط الصور (مفصولة بفواصل)',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.blue.shade50,
                ),
              ),
              const SizedBox(height: 8),
              ElevatedButton(
                onPressed: () {
                  if (nameController.text.isEmpty ||
                      descriptionController.text.isEmpty ||
                      nutritionController.text.isEmpty) {
                    Fluttertoast.showToast(msg: 'يرجى ملء الحقول الأساسية', backgroundColor: Colors.red);
                    return;
                  }
                  setState(() {
                    final newBird = BirdBreedingGroup(
                      id: DateTime.now().millisecondsSinceEpoch.toString(),
                      name: nameController.text,
                      imageUrl: galleryController.text.isNotEmpty
                          ? galleryController.text.split(',').first
                          : 'https://cdn-icons-png.flaticon.com/512/616/616408.png',
                      videos: videosController.text.isNotEmpty
                          ? videosController.text
                          .split(',')
                          .asMap()
                          .entries
                          .map((e) => Video(title: 'فيديو ${e.key + 1}', url: e.value.trim()))
                          .toList()
                          : [],
                      articles: articlesController.text.isNotEmpty
                          ? articlesController.text.split(',').map((a) {
                        final parts = a.split(':');
                        return Article(
                            title: parts[0].trim(), content: parts.length > 1 ? parts[1].trim() : '');
                      }).toList()
                          : [],
                      faqs: faqController.text.isNotEmpty
                          ? faqController.text.split(',').map((f) {
                        final parts = f.split(':');
                        return FAQ(question: parts[0].trim(), answer: parts.length > 1 ? parts[1].trim() : '');
                      }).toList()
                          : [],
                      nutritionGuide: nutritionController.text,
                      galleryImages: galleryController.text.isNotEmpty
                          ? galleryController.text.split(',').map((e) => e.trim()).toList()
                          : [],
                    );
                    birds.add(newBird);
                    Fluttertoast.showToast(msg: 'تمت إضافة نوع الطائر', backgroundColor: Colors.blue.shade700);
                    _confettiController.play();
                  });
                  Navigator.pop(context);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue.shade700,
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                ),
                child: const Text('إضافة'),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
        ],
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        backgroundColor: Colors.blue.shade50,
      ),
    );
  }
}
