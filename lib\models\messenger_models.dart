class User {
  final String id;
  final String name;
  final String job;
  final double latitude;
  final double longitude;
  final String avatarUrl;
  bool isOnline;  // تم تغييرها من final إلى متغيرة
  bool isFavorite;
  bool shareLocation; // إضافة الخاصية الجديدة

  User({
    required this.id,
    required this.name,
    required this.job,
    required this.latitude,
    required this.longitude,
    required this.avatarUrl,
    this.isOnline = false,
    this.isFavorite = false,
    this.shareLocation = false, // قيمة افتراضية
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'] ?? json['_id'] ?? '',
      name: json['name'] ?? json['username'] ?? 'مستخدم غير معروف',
      job: json['job'] ?? 'غير محدد',
      latitude: (json['latitude'] ?? 0.0).toDouble(),
      longitude: (json['longitude'] ?? 0.0).toDouble(),
      avatarUrl: json['avatarUrl'] ?? 'https://via.placeholder.com/150',
      isOnline: json['isOnline'] ?? false,
      isFavorite: json['isFavorite'] ?? false,
      shareLocation: json['shareLocation'] ?? false, // إضافة للتحميل من JSON
    );
  }

  // إضافة دالة copyWith لتعديل الحالة بسهولة
  User copyWith({
    String? id,
    String? name,
    String? job,
    double? latitude,
    double? longitude,
    String? avatarUrl,
    bool? isOnline,
    bool? isFavorite,
    bool? shareLocation, // إضافة المعامل الجديد
  }) {
    return User(
      id: id ?? this.id,
      name: name ?? this.name,
      job: job ?? this.job,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      isOnline: isOnline ?? this.isOnline,
      isFavorite: isFavorite ?? this.isFavorite,
      shareLocation: shareLocation ?? this.shareLocation, // إضافة النسخ
    );
  }

  // يمكنك إضافة دالة لتحويل الكائن لـ Map إذا كنت بحاجة إليها
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'job': job,
      'latitude': latitude,
      'longitude': longitude,
      'avatarUrl': avatarUrl,
      'isOnline': isOnline,
      'isFavorite': isFavorite,
      'shareLocation': shareLocation, // إضافة للتحويل إلى JSON
    };
  }
}