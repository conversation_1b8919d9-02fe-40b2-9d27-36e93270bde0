import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:animate_do/animate_do.dart';
import 'package:http/http.dart' as http;
import 'package:video_player/video_player.dart';
import 'package:file_picker/file_picker.dart';
import 'package:encrypt/encrypt.dart' as encrypt;
import 'package:url_launcher/url_launcher.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:provider/provider.dart';
import 'dart:async';
import 'dart:convert';
import 'dart:typed_data';
import 'package:flutter/foundation.dart' show kDebugMode, kIsWeb;
import 'package:intl/intl.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:easy_localization/easy_localization.dart';
import 'appstate.dart';

// Constants for theme and colors
const Color primaryColor = Color(0xFF4A90E2);
const Color secondaryColor = Color(0xFF3498DB);
const Color darkPrimaryColor = Color(0xFF2C3E50);
const Color darkSecondaryColor = Color(0xFF34495E);

// Error messages
const String networkError = 'Network error occurred';
const String loadingError = 'Error loading data';
const String unknownError = 'An unexpected error occurred';

// Connectivity checker
Future<bool> checkConnectivity() async {
  var connectivityResult = await Connectivity().checkConnectivity();
  return connectivityResult != ConnectivityResult.none;
}

// State management with Provider
class ProfileProvider with ChangeNotifier {
  String _username = "مستخدم";
  String? _profileImagePath = 'https://via.placeholder.com/150';
  String? _profileBannerUrl = 'https://via.placeholder.com/600x200'; // صورة غلاف افتراضية
  final String _userStatus = "Online";
  String? _userType = 'medical';
  List<Map<String, dynamic>> _secureFiles = [];
  List<Post> _posts = [];
  String? _aboutContent;
  List<dynamic> _skills = [];
  List<dynamic> _projects = [];
  final List<Map<String, dynamic>> _achievements = [];
  bool _isLoadingProfile = true;
  bool _isLoadingPosts = true;
  bool _isLoadingSecureFiles = true;
  bool _isLoadingAbout = true;
  bool _isLoadingSkills = true;
  bool _isLoadingProjects = true;
  String _errorMessage = '';
  int _stars = 0;

  // Getters
  String get username => _username;
  String? get profileImagePath => _profileImagePath;
  String? get profileBannerUrl => _profileBannerUrl;
  String get userStatus => _userStatus;
  String? get userType => _userType;
  List<Map<String, dynamic>> get secureFiles => _secureFiles;
  List<Post> get posts => _posts;
  String? get aboutContent => _aboutContent;
  List<dynamic> get skills => _skills;
  List<dynamic> get projects => _projects;
  List<Map<String, dynamic>> get achievements => _achievements;
  bool get isLoadingProfile => _isLoadingProfile;
  bool get isLoadingPosts => _isLoadingPosts;
  bool get isLoadingSecureFiles => _isLoadingSecureFiles;
  bool get isLoadingAbout => _isLoadingAbout;
  bool get isLoadingSkills => _isLoadingSkills;
  bool get isLoadingProjects => _isLoadingProjects;
  String get errorMessage => _errorMessage;
  int get stars => _stars;

  // تغيير صورة الملف الشخصي
  Future<void> changeProfileImage(String imagePath) async {
    _profileImagePath = imagePath;
    notifyListeners();
  }
  // تغيير صورة الغلاف
  Future<void> changeBannerImage(String imagePath) async {
    _profileBannerUrl = imagePath;
    notifyListeners();
  }

  // Methods to update state
  void setProfileData(Map<String, dynamic> data) {
    // تحديث صورة الغلاف إذا وجدت
    if (data['bannerUrl'] != null) _profileBannerUrl = data['bannerUrl'];
    _username = data['name'] ?? data['username'] ?? _username;
    _profileImagePath = data['avatarUrl'] ?? _profileImagePath;
    _profileBannerUrl = data['bannerUrl'];
    _userType = data['userType'] ?? _userType;
    _stars = data['stars'] ?? _stars;
    _isLoadingProfile = false;
    notifyListeners();
  }

  void setPosts(List<Post> posts) {
    _posts = posts;
    _isLoadingPosts = false;
    notifyListeners();
  }

  void addPost(Post post) {
    _posts.insert(0, post);
    _addAchievement('New Post', FontAwesomeIcons.pen);
    notifyListeners();
  }

  void setSecureFiles(List<Map<String, dynamic>> files) {
    _secureFiles = files;
    _isLoadingSecureFiles = false;
    notifyListeners();
  }

  void addSecureFile(Map<String, dynamic> file) {
    _secureFiles.add(file);
    _addAchievement('Uploaded Secure File', FontAwesomeIcons.fileShield);
    notifyListeners();
  }

  void setAbout(String content) {
    _aboutContent = content;
    _isLoadingAbout = false;
    notifyListeners();
  }

  void setSkills(List<dynamic> skills) {
    _skills = skills;
    _isLoadingSkills = false;
    notifyListeners();
  }

  void addSkill(String name) {
    _skills.add({'name': name});
    _addAchievement('Added Skill', FontAwesomeIcons.brain);
    notifyListeners();
  }

  // تقييم الملف الشخصي
  void rateProfile(int stars) {
    _stars = stars;
    notifyListeners();
  }

  void setProjects(List<dynamic> projects) {
    _projects = projects;
    _isLoadingProjects = false;
    notifyListeners();
  }

  void addProject(Map<String, dynamic> project) {
    _projects.add(project);
    _addAchievement('Added Project', FontAwesomeIcons.projectDiagram);
    notifyListeners();
  }

  void setError(String message) {
    _errorMessage = message;
    notifyListeners();
  }

  void clearError() {
    _errorMessage = '';
    notifyListeners();
  }

  void _addAchievement(String title, IconData icon) {
    _achievements.add({
      'title': title,
      'icon': icon,
      'date': DateTime.now().toIso8601String().split('T')[0]
    });
    if (_achievements.length > 10) _achievements.removeAt(0); // Limit to 10
    notifyListeners();
  }

  // Method to update stars on the backend
  Future<void> updateStarsOnBackend(int starsToAdd, String token, String backendBaseUrl) async {
    if (token.isEmpty) {
      _errorMessage = 'Authentication token not found.';
      notifyListeners();
      return;
    }

    final url = Uri.parse('$backendBaseUrl/api/users/stars');
    try {
      final response = await http.put(
        url,
        headers: {
          'Content-Type': 'application/json; charset=UTF-8',
          'Authorization': 'Bearer $token',
        },
        body: jsonEncode({'starsToAdd': starsToAdd}),
      );

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        if (responseData['success'] == true && responseData['data'] != null && responseData['data']['stars'] != null) {
          _stars = responseData['data']['stars'];
          _errorMessage = ''; // Clear previous errors
        } else {
          _errorMessage = responseData['message'] ?? 'Failed to update stars: Unexpected response format.';
        }
      } else {
        final errorData = jsonDecode(response.body);
        _errorMessage = errorData['message'] ?? 'Failed to update stars: Server error ${response.statusCode}.';
        // Potentially revert optimistic update here if we were doing one in AppState
      }
    } catch (e) {
      _errorMessage = 'Failed to update stars: An error occurred. $e';
    }
    notifyListeners();
  }
}

// Post class definition
class Post {
  final String userName;
  final String userImage;
  final String time;
  final String content;
  final String? postImage;

  const Post({
    required this.userName,
    required this.userImage,
    required this.time,
    required this.content,
    this.postImage,
  });

  factory Post.fromJson(Map<String, dynamic> json) {
    return Post(
      userName: json['userName'] as String,
      userImage: json['userImage'] as String,
      time: json['time'] as String,
      content: json['content'] as String,
      postImage: json['postImage'] as String?,
    );
  }
}


class IconGridScreen extends StatefulWidget {
  const IconGridScreen({super.key});

  @override
  _IconGridScreenState createState() => _IconGridScreenState();
}

class _IconGridScreenState extends State<IconGridScreen>
    with SingleTickerProviderStateMixin {
  late VideoPlayerController _videoController;
  bool _isVideoPlaying = true;
  bool _isDarkMode = false;
  bool _showProfileVideo = true;
  bool _showPosts = true;
  String _timelineFilter = 'all';
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  final TextEditingController _postController = TextEditingController();
  final TextEditingController _secretCodeController = TextEditingController();
  final TextEditingController _aboutController = TextEditingController();
  final TextEditingController _skillController = TextEditingController();
  final TextEditingController _projectNameController = TextEditingController();
  final TextEditingController _projectDescController = TextEditingController();
  final TextEditingController _patientUsernameController =
  TextEditingController();
  final TextEditingController _shareWithUsernameController =
  TextEditingController();
  static const String _correctSecretCode = '123456'; // For demo; make dynamic

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
        duration: const Duration(milliseconds: 800), vsync: this);
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _initDefaultVideo();
    _animationController.forward();
    Timer.periodic(const Duration(minutes: 5), (timer) {
      if (mounted) {
        context.read<ProfileProvider>().setProfileData({
          'userStatus':
          DateTime.now().minute % 2 == 0 ? "Online" : "Last seen recently"
        });
      }
    });
  }

  @override
  void dispose() {
    _videoController.dispose();
    _postController.dispose();
    _secretCodeController.dispose();
    _aboutController.dispose();
    _skillController.dispose();
    _projectNameController.dispose();
    _projectDescController.dispose();
    _patientUsernameController.dispose();
    _shareWithUsernameController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  void _initDefaultVideo() {
    _videoController =
    VideoPlayerController.network('https://via.placeholder.com/video.mp4')
      ..initialize().then((_) {
        if (mounted) {
          setState(() {});
          if (_showProfileVideo) _videoController.play();
          _videoController.setLooping(true);
        }
      }).catchError((error) {
        _showSnackBar(context, 'Error initializing default video: $error');
      });
  }

  Future<void> _fetchProfileData() async {
    final provider = context.read<ProfileProvider>();
    provider.setError('');
    final prefs = await SharedPreferences.getInstance();
    // Check cache first
    final cachedProfile = prefs.getString('profile_data');
    if (cachedProfile != null) {
      provider.setProfileData(json.decode(cachedProfile));
    }
    try {
      final token = await AppState.getAuthToken();
      final response = await http.get(
        Uri.parse('${AppState.getBackendUrl()}/profile'),
        headers: {'x-auth-token': token},
      ).timeout(const Duration(seconds: 10));
      if (response.statusCode == 200) {
        final profileData = json.decode(response.body);
        await prefs.setString('profile_data', json.encode(profileData));
        if (mounted) {
          provider.setProfileData(profileData);
          _videoController.dispose();
          _videoController = VideoPlayerController.network(
            profileData['profileVideoUrl'] ??
                'https://via.placeholder.com/video.mp4',
          )..initialize().then((_) {
            if (mounted) {
              setState(() {});
              if (_showProfileVideo) _videoController.play();
              _videoController.setLooping(true);
            }
          }).catchError((error) {
            _showSnackBar(context, 'Error initializing profile video: $error');
          });
        }
      } else {
        provider.setError('Failed to fetch profile: ${response.statusCode}');
      }
    } catch (e) {
      provider.setError('Error fetching profile: $e');
    }
  }

  Future<void> _fetchPosts() async {
    final provider = context.read<ProfileProvider>();
    provider.setError('');
    try {
      final token = await AppState.getAuthToken();
      final response = await http.get(
        Uri.parse('${AppState.getBackendUrl()}/posts'),
        headers: {'x-auth-token': token},
      ).timeout(const Duration(seconds: 10));
      if (response.statusCode == 200) {
        final List<dynamic> postData = json.decode(response.body);
        if (mounted) {
          provider.setPosts(postData.map((data) => Post.fromJson(data)).toList());
        }
      } else {
        provider.setError('Failed to fetch posts: ${response.statusCode}');
      }
    } catch (e) {
      provider.setError('Error fetching posts: $e');
    }
  }

  Future<void> _fetchSecureFiles() async {
    final provider = context.read<ProfileProvider>();
    provider.setError('');
    try {
      final token = await AppState.getAuthToken();
      final response = await http.get(
        Uri.parse('${AppState.getBackendUrl()}/secure-files'),
        headers: {'x-auth-token': token},
      ).timeout(const Duration(seconds: 10));
      if (response.statusCode == 200) {
        if (mounted) {
          provider.setSecureFiles(
              List<Map<String, dynamic>>.from(json.decode(response.body)));
        }
      } else {
        provider.setError('Failed to fetch secure files: ${response.statusCode}');
      }
    } catch (e) {
      provider.setError('Error fetching secure files: $e');
    }
  }

  Future<void> _fetchAbout() async {
    final provider = context.read<ProfileProvider>();
    provider.setError('');
    try {
      final token = await AppState.getAuthToken();
      final response = await http.get(
        Uri.parse('${AppState.getBackendUrl()}/about'),
        headers: {'x-auth-token': token},
      ).timeout(const Duration(seconds: 10));
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (mounted) {
          provider.setAbout(data['content']);
        }
      } else {
        provider.setError('Failed to fetch about: ${response.statusCode}');
      }
    } catch (e) {
      provider.setError('Error fetching about: $e');
    }
  }

  Future<void> _updateAbout(String content) async {
    final provider = context.read<ProfileProvider>();
    try {
      final token = await AppState.getAuthToken();
      final response = await http.post(
        Uri.parse('${AppState.getBackendUrl()}/about'),
        headers: {'x-auth-token': token, 'Content-Type': 'application/json'},
        body: json.encode({'content': content}),
      );
      if (response.statusCode == 200) {
        if (mounted) {
          provider.setAbout(content);
          _showSnackBar(context, Intl.message('About updated successfully',
              name: 'about_updated_success'));
        }
      } else {
        _showSnackBar(context,
            'Failed to update about: ${response.statusCode}');
      }
    } catch (e) {
      _showSnackBar(context, 'Error updating about: $e');
    }
  }

  Future<void> _fetchSkills() async {
    final provider = context.read<ProfileProvider>();
    provider.setError('');
    try {
      final token = await AppState.getAuthToken();
      final response = await http.get(
        Uri.parse('${AppState.getBackendUrl()}/skills'),
        headers: {'x-auth-token': token},
      ).timeout(const Duration(seconds: 10));
      if (response.statusCode == 200) {
        if (mounted) {
          provider.setSkills(json.decode(response.body));
        }
      } else {
        provider.setError('Failed to fetch skills: ${response.statusCode}');
      }
    } catch (e) {
      provider.setError('Error fetching skills: $e');
    }
  }

  Future<void> _addSkill(String name) async {
    final provider = context.read<ProfileProvider>();
    try {
      final token = await AppState.getAuthToken();
      final response = await http.post(
        Uri.parse('${AppState.getBackendUrl()}/skills'),
        headers: {'x-auth-token': token, 'Content-Type': 'application/json'},
        body: json.encode({'name': name}),
      );
      if (response.statusCode == 201) {
        if (mounted) {
          provider.addSkill(name);
          _showSnackBar(context, Intl.message('Skill added successfully',
              name: 'skill_added_success'));
        }
      } else {
        _showSnackBar(context,
            'Failed to add skill: ${response.statusCode}');
      }
    } catch (e) {
      _showSnackBar(context, 'Error adding skill: $e');
    }
  }

  Future<void> _fetchProjects() async {
    final provider = context.read<ProfileProvider>();
    provider.setError('');
    try {
      final token = await AppState.getAuthToken();
      final response = await http.get(
        Uri.parse('${AppState.getBackendUrl()}/projects'),
        headers: {'x-auth-token': token},
      ).timeout(const Duration(seconds: 10));
      if (response.statusCode == 200) {
        if (mounted) {
          provider.setProjects(json.decode(response.body));
        }
      } else {
        provider.setError('Failed to fetch projects: ${response.statusCode}');
      }
    } catch (e) {
      provider.setError('Error fetching projects: $e');
    }
  }

  Future<void> _addProject(String name, String description) async {
    final provider = context.read<ProfileProvider>();
    try {
      final token = await AppState.getAuthToken();
      final response = await http.post(
        Uri.parse('${AppState.getBackendUrl()}/projects'),
        headers: {'x-auth-token': token, 'Content-Type': 'application/json'},
        body: json.encode({'name': name, 'description': description}),
      );
      if (response.statusCode == 201) {
        if (mounted) {
          provider.addProject({'name': name, 'description': description});
          _showSnackBar(context, Intl.message('Project added successfully',
              name: 'project_added_success'));
        }
      } else {
        _showSnackBar(context,
            'Failed to add project: ${response.statusCode}');
      }
    } catch (e) {
      _showSnackBar(context, 'Error adding project: $e');
    }
  }

  Future<void> _decryptFile(String fileId, String secretCode) async {
    try {
      final token = await AppState.getAuthToken();
      final response = await http.post(
        Uri.parse('${AppState.getBackendUrl()}/secure-files/decrypt'),
        headers: {'x-auth-token': token, 'Content-Type': 'application/json'},
        body: json.encode({'fileId': fileId, 'secretCode': secretCode}),
      );
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        _showSnackBar(context,
            Intl.message('File decrypted: ${data['content']}', name: 'file_decrypted'));
      } else {
        _showSnackBar(context,
            'Failed to decrypt: ${response.statusCode}');
      }
    } catch (e) {
      _showSnackBar(context, 'Error decrypting file: $e');
    }
  }

  Future<void> _uploadProfileVideo() async {
    FilePickerResult? result =
    await FilePicker.platform.pickFiles(type: FileType.video);
    if (result != null) {
      var request = http.MultipartRequest(
          'POST', Uri.parse('${AppState.getBackendUrl()}/profile/video'));
      final token = await AppState.getAuthToken();
      request.headers['x-auth-token'] = token;
      if (kIsWeb) {
        final bytes = result.files.single.bytes;
        if (bytes != null) {
          request.files.add(http.MultipartFile.fromBytes('video', bytes,
              filename: result.files.single.name));
        }
      } else {
        final path = result.files.single.path;
        if (path != null) {
          request.files.add(await http.MultipartFile.fromPath('video', path));
        }
      }
      try {
        final response = await request.send();
        if (response.statusCode == 200) {
          final responseData = await response.stream.bytesToString();
          final videoUrl = json.decode(responseData)['videoUrl'];
          if (mounted) {
            setState(() {
              _videoController.dispose();
              _videoController = VideoPlayerController.network(videoUrl)
                ..initialize().then((_) {
                  if (mounted) {
                    setState(() {});
                    if (_showProfileVideo) _videoController.play();
                    _videoController.setLooping(true);
                  }
                }).catchError((error) {
                  _showSnackBar(context, 'Error initializing new video: $error');
                });
            });
            _showSnackBar(context, Intl.message('Video uploaded successfully',
                name: 'video_uploaded_success'));
          }
        } else {
          _showSnackBar(context,
              'Failed to upload video: ${response.statusCode}');
        }
      } catch (e) {
        _showSnackBar(context, 'Error uploading video: $e');
      }
    }
  }

  Future<void> _uploadProfileBanner() async {
    FilePickerResult? result =
    await FilePicker.platform.pickFiles(type: FileType.image);
    if (result != null) {
      // Crop the image
      final croppedFile = await ImageCropper().cropImage(
        sourcePath: kIsWeb ? result.files.single.path! : result.files.single.path!,
        aspectRatio: const CropAspectRatio(ratioX: 16, ratioY: 9),
        uiSettings: [
          AndroidUiSettings(
            toolbarTitle: Intl.message('Crop Image', name: 'crop_image'),
            toolbarColor: Colors.teal[700],
            toolbarWidgetColor: Colors.white,
          ),
          IOSUiSettings(
            title: Intl.message('Crop Image', name: 'crop_image'),
          ),
        ],
      );
      if (croppedFile != null) {
        var request = http.MultipartRequest(
            'POST', Uri.parse('${AppState.getBackendUrl()}/profile/banner'));
        final token = await AppState.getAuthToken();
        request.headers['x-auth-token'] = token;
        if (kIsWeb) {
          final bytes = result.files.single.bytes;
          if (bytes != null) {
            request.files.add(http.MultipartFile.fromBytes('banner', bytes,
                filename: result.files.single.name));
          }
        } else {
          request.files.add(await http.MultipartFile.fromPath('banner', croppedFile.path));
        }
        try {
          final response = await request.send();
          if (response.statusCode == 200) {
            final responseData = await response.stream.bytesToString();
            if (mounted) {
              context
                  .read<ProfileProvider>()
                  .setProfileData({'bannerUrl': json.decode(responseData)['bannerUrl']});
              _showSnackBar(context, Intl.message('Banner uploaded successfully',
                  name: 'banner_uploaded_success'));
            }
          } else {
            _showSnackBar(context,
                'Failed to upload banner: ${response.statusCode}');
          }
        } catch (e) {
          _showSnackBar(context, 'Error uploading banner: $e');
        }
      }
    }
  }

  Future<void> _submitPost() async {
    final provider = context.read<ProfileProvider>();
    if (_postController.text.isNotEmpty) {
      try {
        final token = await AppState.getAuthToken();
        final response = await http.post(
          Uri.parse('${AppState.getBackendUrl()}/posts'),
          headers: {'x-auth-token': token, 'Content-Type': 'application/json'},
          body: json.encode({
            'userName': provider.username,
            'userImage': provider.profileImagePath,
            'content': _postController.text,
            'time': 'Now',
          }),
        );
        if (response.statusCode == 201) {
          final newPost = Post.fromJson(json.decode(response.body));
          if (mounted) {
            provider.addPost(newPost);
            _postController.clear();
            _showSnackBar(context, Intl.message('Post published successfully',
                name: 'post_published_success'));
          }
        } else {
          _showSnackBar(context,
              'Failed to publish post: ${response.statusCode}');
        }
      } catch (e) {
        _showSnackBar(context, 'Error publishing post: $e');
      }
    } else {
      _showSnackBar(context, Intl.message('Please enter post content',
          name: 'enter_post_content'));
    }
  }

  Future<void> _uploadSecureFile({String? patientUsername}) async {
    final provider = context.read<ProfileProvider>();
    FilePickerResult? result = await FilePicker.platform.pickFiles(
      type: FileType.custom,
      allowedExtensions: ['jpg', 'png', 'pdf'],
    );
    if (result != null) {
      final PlatformFile file = result.files.single;
      final Uint8List? fileBytes = file.bytes ?? (kIsWeb ? file.bytes : null);
      if (fileBytes != null || !kIsWeb) {
        // Dynamic encryption key (simulate fetching from secure storage)
        final key = encrypt.Key.fromUtf8(
            'dynamic_key_${DateTime.now().millisecondsSinceEpoch}'.substring(0, 32));
        final iv = encrypt.IV.fromLength(16);
        final encrypter = encrypt.Encrypter(encrypt.AES(key));
        final encryptedContent =
        encrypter.encryptBytes(fileBytes ?? [], iv: iv);
        var request = http.MultipartRequest(
            'POST', Uri.parse('${AppState.getBackendUrl()}/secure-files'));
        final token = await AppState.getAuthToken();
        request.headers['x-auth-token'] = token;
        request.fields['name'] = file.name;
        request.fields['encryptedContent'] = encryptedContent.base64;
        request.fields['iv'] = iv.base64;
        if (patientUsername != null) {
          request.fields['patientUsername'] = patientUsername;
        }
        if (kIsWeb) {
          if (fileBytes != null) {
            request.files.add(http.MultipartFile.fromBytes('file', fileBytes,
                filename: file.name));
          }
        } else {
          final path = file.path;
          if (path != null) {
            request.files.add(await http.MultipartFile.fromPath('file', path));
          }
        }
        try {
          var response = await request.send();
          if (response.statusCode == 201) {
            final responseData = await response.stream.bytesToString();
            final newFile = json.decode(responseData);
            if (mounted) {
              provider.addSecureFile(newFile);
              _showSnackBar(context, Intl.message('Secure file uploaded successfully',
                  name: 'secure_file_uploaded_success'));
            }
          } else {
            _showSnackBar(context,
                'Failed to upload file: ${response.statusCode}');
          }
        } catch (e) {
          _showSnackBar(context, 'Error uploading file: $e');
        }
      } else {
        _showSnackBar(context, Intl.message('Failed to read file',
            name: 'failed_to_read_file'));
      }
    }
  }

  Future<void> _uploadMedicalReport() async {
    if (_patientUsernameController.text.isNotEmpty) {
      await _uploadSecureFile(patientUsername: _patientUsernameController.text);
      if (mounted) {
        _patientUsernameController.clear();
      }
    } else {
      _showSnackBar(context, Intl.message('Please enter patient username',
          name: 'enter_patient_username'));
    }
  }

  Future<void> _shareSecureFiles(String targetUsername) async {
    try {
      final token = await AppState.getAuthToken();
      final response = await http.post(
        Uri.parse('${AppState.getBackendUrl()}/secure-files/share'),
        headers: {'x-auth-token': token, 'Content-Type': 'application/json'},
        body: json.encode({'targetUsername': targetUsername}),
      );
      if (response.statusCode == 200) {
        _showSnackBar(context,
            Intl.message('Secure files shared with $targetUsername', name: 'files_shared'));
      } else {
        _showSnackBar(context,
            'Failed to share files: ${response.statusCode}');
      }
    } catch (e) {
      _showSnackBar(context, 'Error sharing files: $e');
    }
  }

  void _toggleVideoPlay() {
    if (mounted) {
      setState(() {
        _isVideoPlaying = !_isVideoPlaying;
        _isVideoPlaying ? _videoController.play() : _videoController.pause();
      });
    }
  }

  void _toggleProfileVideoVisibility() {
    if (mounted) {
      setState(() {
        _showProfileVideo = !_showProfileVideo;
        if (_showProfileVideo && _isVideoPlaying) {
          _videoController.play();
        } else {
          _videoController.pause();
        }
      });
    }
  }

  void _togglePostsVisibility() {
    if (mounted) {
      setState(() => _showPosts = !_showPosts);
    }
  }

  void _toggleDarkMode() {
    if (mounted) {
      setState(() => _isDarkMode = !_isDarkMode);
    }
  }

  void _shareProfile() async {
    final provider = context.read<ProfileProvider>();
    final profileUrl = '${AppState.getBackendUrl()}/profile/${provider.username}';
    final shareText = Intl.message('Check out my profile on Fulk: $profileUrl',
        name: 'share_profile_text');
    final Uri shareUri =
    Uri.parse('https://wa.me/?text=${Uri.encodeComponent(shareText)}');
    if (await canLaunchUrl(shareUri)) {
      await launchUrl(shareUri);
    } else {
      _showSnackBar(context, Intl.message('Cannot open sharing app',
          name: 'cannot_open_sharing_app'));
    }
  }

  void _viewSecureFiles() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        title: Text(Intl.message('Enter Secret Code', name: 'enter_secret_code'),
            style: const TextStyle(color: Colors.teal)),
        content: TextField(
          controller: _secretCodeController,
          obscureText: true,
          decoration: InputDecoration(
            hintText: Intl.message('Enter secret code', name: 'enter_secret_code_hint'),
            filled: true,
            fillColor: Colors.grey[100],
            border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide.none),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(Intl.message('Cancel', name: 'cancel'),
                style: const TextStyle(color: Colors.grey)),
          ),
          ElevatedButton(
            onPressed: () async {
              if (_secretCodeController.text == _correctSecretCode) {
                Navigator.pop(context);
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => SecureFilesPage(
                      secureFiles: context.read<ProfileProvider>().secureFiles,
                      onDecrypt: _decryptFile,
                      userType: context.read<ProfileProvider>().userType,
                      onShare: _shareSecureFiles,
                    ),
                  ),
                );
              } else {
                _showSnackBar(context, Intl.message('Incorrect secret code',
                    name: 'incorrect_secret_code'));
              }
              _secretCodeController.clear();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.teal[700],
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12)),
            ),
            child: Text(Intl.message('Confirm', name: 'confirm'),
                style: const TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final provider = context.watch<ProfileProvider>();
    final theme = _isDarkMode
        ? ThemeData.dark().copyWith(
      primaryColor: Colors.teal[700],
      scaffoldBackgroundColor: Colors.grey[900],
      cardColor: Colors.grey[800],
      textTheme:
      const TextTheme(bodyMedium: TextStyle(color: Colors.white)),
    )
        : ThemeData.light().copyWith(
      primaryColor: Colors.teal[700],
      scaffoldBackgroundColor: Colors.teal[50],
      cardColor: Colors.white,
    );

    return Theme(
      data: theme,
      child: Scaffold(
        body: Stack(
          children: [
            Positioned.fill(
              child: provider.profileBannerUrl != null
                  ? ParallaxBackground(imageUrl: provider.profileBannerUrl!)
                  : Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Colors.teal[700]!, Colors.teal[300]!],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                ),
              ),
            ),
            SafeArea(
              child: provider.isLoadingProfile ||
                  provider.isLoadingPosts ||
                  provider.isLoadingSecureFiles ||
                  provider.isLoadingAbout ||
                  provider.isLoadingSkills ||
                  provider.isLoadingProjects
                  ? const Center(
                  child: CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation(Colors.teal)))
                  : provider.errorMessage.isNotEmpty
                  ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(provider.errorMessage,
                        style: const TextStyle(color: Colors.red)),
                    ElevatedButton(
                      onPressed: () {
                        provider.clearError();
                        _fetchProfileData();
                        _fetchPosts();
                        _fetchSecureFiles();
                        _fetchAbout();
                        _fetchSkills();
                        _fetchProjects();
                      },
                      child: Text(Intl.message('Retry', name: 'retry')),
                    ),
                  ],
                ),
              )
                  : FadeTransition(
                opacity: _fadeAnimation,
                child: RefreshIndicator(
                  onRefresh: () async {
                    await Future.wait([
                      _fetchProfileData(),
                      _fetchPosts(),
                      _fetchSecureFiles(),
                      _fetchAbout(),
                      _fetchSkills(),
                      _fetchProjects(),
                    ]);
                  },
                  child: SingleChildScrollView(
                    child: Column(
                      children: [
                        ZoomIn(
                          child: Container(
                            height: 250,
                            decoration: BoxDecoration(
                              borderRadius: const BorderRadius.only(
                                bottomLeft: Radius.circular(30),
                                bottomRight: Radius.circular(30),
                              ),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withOpacity(0.3),
                                  blurRadius: 10,
                                  offset: const Offset(0, 5),
                                ),
                              ],
                            ),
                            child: Stack(
                              children: [
                                ClipRRect(
                                  borderRadius: const BorderRadius.only(
                                    bottomLeft: Radius.circular(30),
                                    bottomRight: Radius.circular(30),
                                  ),
                                  child: _videoController
                                      .value.isInitialized &&
                                      _showProfileVideo
                                      ? AspectRatio(
                                    aspectRatio: _videoController
                                        .value.aspectRatio,
                                    child: VideoPlayer(
                                        _videoController),
                                  )
                                      : Container(
                                    color: Colors.grey[200],
                                    child: const Icon(
                                        Icons.video_library,
                                        size: 50,
                                        color: Colors.teal),
                                  ),
                                ),
                                Positioned(
                                  top: 10,
                                  right: 10,
                                  child: GradientButton(
                                    onPressed: _toggleVideoPlay,
                                    child: FaIcon(
                                      _isVideoPlaying
                                          ? FontAwesomeIcons.pause
                                          : FontAwesomeIcons.play,
                                      color: Colors.white,
                                    ),
                                  ),
                                ),
                                Positioned(
                                  top: 10,
                                  left: 10,
                                  child: GradientButton(
                                    onPressed: _uploadProfileVideo,
                                    child: const FaIcon(
                                        FontAwesomeIcons.upload,
                                        color: Colors.white),
                                  ),
                                ),
                                Positioned(
                                  top: 10,
                                  left: 60,
                                  child: GradientButton(
                                    onPressed:
                                    _toggleProfileVideoVisibility,
                                    child: FaIcon(
                                      _showProfileVideo
                                          ? FontAwesomeIcons.eyeSlash
                                          : FontAwesomeIcons.eye,
                                      color: Colors.white,
                                    ),
                                  ),
                                ),
                                Positioned(
                                  top: 10,
                                  left: 110,
                                  child: GradientButton(
                                    onPressed: _uploadProfileBanner,
                                    child: const FaIcon(
                                        FontAwesomeIcons.image,
                                        color: Colors.white),
                                  ),
                                ),
                                Positioned(
                                  top: 10,
                                  right: 60,
                                  child: GradientButton(
                                    onPressed: _toggleDarkMode,
                                    child: FaIcon(
                                      _isDarkMode
                                          ? FontAwesomeIcons.sun
                                          : FontAwesomeIcons.moon,
                                      color: Colors.white,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                        Transform.translate(
                          offset: const Offset(0, -75),
                          child: FadeInUp(
                            child: Column(
                              children: [
                                Container(
                                  width: 150,
                                  height: 150,
                                  decoration: BoxDecoration(
                                    borderRadius:
                                    BorderRadius.circular(20),
                                    color: Colors.white,
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black
                                            .withOpacity(0.3),
                                        blurRadius: 10,
                                        offset: const Offset(5, 5),
                                      ),
                                    ],
                                    image: DecorationImage(
                                        image: CachedNetworkImageProvider(
                                            provider.profileImagePath!),
                                        fit: BoxFit.cover),
                                  ),
                                ),
                                const SizedBox(height: 10),
                                Row(
                                  mainAxisAlignment:
                                  MainAxisAlignment.center,
                                  children: [
                                    Text(provider.username,
                                        style: const TextStyle(
                                            fontSize: 24,
                                            fontWeight:
                                            FontWeight.bold)),
                                    const SizedBox(width: 10),
                                    Container(
                                      padding: const EdgeInsets
                                          .symmetric(
                                          horizontal: 8,
                                          vertical: 4),
                                      decoration: BoxDecoration(
                                        color:
                                        provider.userStatus == "Online"
                                            ? Colors.green
                                            : Colors.grey,
                                        borderRadius:
                                        BorderRadius.circular(12),
                                      ),
                                      child: Text(
                                        provider.userStatus,
                                        style: const TextStyle(
                                            color: Colors.white,
                                            fontSize: 12),
                                      ),
                                    ),
                                  ],
                                ),
                                GradientButton(
                                  onPressed: _shareProfile,
                                  child: Text(
                                      Intl.message('Share Profile',
                                          name: 'share_profile'),
                                      style: const TextStyle(
                                          color: Colors.white)),
                                ),
                              ],
                            ),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.only(top: 20),
                          child: Column(
                            children: [
                              FadeInUp(
                                  child: _buildStatsSection(context)),
                              const SizedBox(height: 20),
                              FadeInUp(
                                  child: _buildAchievementsSection(
                                      context)),
                              const SizedBox(height: 20),
                              FadeInUp(
                                  child:
                                  _buildHorizontalCards(context)),
                              const SizedBox(height: 20),
                              FadeInUp(
                                child: _buildTimelineSection(
                                    context)),
                              const SizedBox(height: 20),
                              FadeInUp(
                                child: _buildPostInputSection(context),
                              ),
                              const SizedBox(height: 20),
                              FadeInUp(
                                child: GradientButton(
                                  onPressed: _togglePostsVisibility,
                                  child: Text(
                                    _showPosts
                                        ? Intl.message('Hide Posts',
                                        name: 'hide_posts')
                                        : Intl.message('Show Posts',
                                        name: 'show_posts'),
                                    style: const TextStyle(
                                        color: Colors.white),
                                  ),
                                ),
                              ),
                              const SizedBox(height: 20),
                              if (_showPosts)
                                FadeInUp(
                                    child:
                                    _buildPostsSection(context)),
                              const SizedBox(height: 20),
                              if (provider.userType == 'medical')
                                FadeInUp(
                                    child: _buildMedicalReportSection(
                                        context)),
                              const SizedBox(height: 20),
                              FadeInUp(
                                  child: _buildSecureFilesSection(
                                      context)),
                              const SizedBox(height: 20),
                              FadeInUp(
                                  child:
                                  _buildContactSection(context)),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
        floatingActionButton: FloatingActionButton(
          onPressed: _uploadSecureFile,
          backgroundColor: Colors.teal[700],
          elevation: 8,
          tooltip: Intl.message('Upload Secure File', name: 'upload_secure_file'),
          child: const FaIcon(FontAwesomeIcons.upload, color: Colors.white),
        ),
      ),
    );
  }

  Widget _buildStatsSection(BuildContext context) {
    final provider = context.watch<ProfileProvider>();
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 15),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(Intl.message('Statistics', name: 'statistics'),
              style: const TextStyle(
                  fontSize: 18, fontWeight: FontWeight.bold, color: Colors.teal)),
          const SizedBox(height: 10),
          SizedBox(
            height: 200,
            child: PieChart(
              PieChartData(
                sections: [
                  PieChartSectionData(
                    value: provider.posts.length.toDouble(),
                    title: Intl.message('Posts', name: 'posts'),
                    color: Colors.teal,
                  ),
                  PieChartSectionData(
                    value: provider.projects.length.toDouble(),
                    title: Intl.message('Projects', name: 'projects'),
                    color: Colors.purple,
                  ),
                  PieChartSectionData(
                    value: provider.secureFiles.length.toDouble(),
                    title: Intl.message('Files', name: 'files'),
                    color: Colors.orange,
                  ),
                ],
                centerSpaceRadius: 40,
                sectionsSpace: 2,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAchievementsSection(BuildContext context) {
    final provider = context.watch<ProfileProvider>();
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 15),
          child: Text(Intl.message('Achievements', name: 'achievements'),
              style: const TextStyle(
                  fontSize: 18, fontWeight: FontWeight.bold, color: Colors.teal)),
        ),
        SizedBox(
          height: 120,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 15),
            itemCount: provider.achievements.length,
            itemBuilder: (context, index) {
              return ZoomIn(
                child: Container(
                  width: 100,
                  margin: const EdgeInsets.only(right: 10),
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: _isDarkMode ? Colors.grey[800] : Colors.white,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.2),
                        blurRadius: 5,
                        offset: const Offset(2, 2),
                      ),
                    ],
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      FaIcon(provider.achievements[index]['icon'],
                          color: Colors.teal[700], size: 30),
                      const SizedBox(height: 5),
                      Text(
                        provider.achievements[index]['title'],
                        style: TextStyle(
                            fontSize: 12,
                            color: _isDarkMode ? Colors.white : Colors.black),
                        textAlign: TextAlign.center,
                      ),
                      Text(
                        provider.achievements[index]['date'],
                        style: TextStyle(
                            fontSize: 10,
                            color: _isDarkMode
                                ? Colors.grey[400]
                                : Colors.grey[600]),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildTimelineSection(BuildContext context) {
    final provider = context.watch<ProfileProvider>();
    final timelineItems = [
      ...provider.posts.map((post) => {
        'type': 'post',
        'title': post.content.length > 20
            ? '${post.content.substring(0, 20)}...'
            : post.content,
        'date': post.time,
        'icon': FontAwesomeIcons.pen,
      }),
      ...provider.projects.map((project) => {
        'type': 'project',
        'title': project['name'],
        'date': DateTime.now().toIso8601String().split('T')[0],
        'icon': FontAwesomeIcons.projectDiagram,
      }),
    ].where((item) =>
    _timelineFilter == 'all' ||
        item['type'] == _timelineFilter).toList()
      ..sort((a, b) => b['date'].compareTo(a['date']));

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 15),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(Intl.message('Timeline', name: 'timeline'),
                  style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.teal)),
              DropdownButton<String>(
                value: _timelineFilter,
                items: [
                  DropdownMenuItem(
                    value: 'all',
                    child: Text(Intl.message('All', name: 'all')),
                  ),
                  DropdownMenuItem(
                    value: 'post',
                    child: Text(Intl.message('Posts', name: 'posts')),
                  ),
                  DropdownMenuItem(
                    value: 'project',
                    child: Text(Intl.message('Projects', name: 'projects')),
                  ),
                ],
                onChanged: (value) {
                  if (value != null) {
                    setState(() => _timelineFilter = value);
                  }
                },
              ),
            ],
          ),
        ),
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: timelineItems.length,
          itemBuilder: (context, index) {
            return FadeInUp(
              child: TimelineTile(
                title: timelineItems[index]['title'],
                date: timelineItems[index]['date'],
                icon: timelineItems[index]['icon'],
                isFirst: index == 0,
                isLast: index == timelineItems.length - 1,
              ),
            );
          },
        ),
      ],
    );
  }

  Widget _buildHorizontalCards(BuildContext context) {
    final provider = context.watch<ProfileProvider>();
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      padding: const EdgeInsets.symmetric(horizontal: 15),
      child: Row(
        children: [
          ZoomIn(
            child: _buildClickableCard(
              context,
              title: Intl.message('About Me', name: 'about_me'),
              content: provider.aboutContent ??
                  Intl.message('Professional app developer with 5 years experience',
                      name: 'default_about'),
              color: Colors.teal[400]!,
              page: AboutPage(
                  aboutContent: provider.aboutContent, onUpdate: _updateAbout),
            ),
          ),
          const SizedBox(width: 10),
          ZoomIn(
            child: _buildClickableCard(
              context,
              title: Intl.message('Skills', name: 'skills'),
              content: provider.skills.isNotEmpty
                  ? provider.skills.map((s) => s['name']).join(', ')
                  : 'Flutter - Dart',
              color: Colors.green[400]!,
              page: SkillsPage(
                  skills: provider.skills,
                  onAddSkill: _addSkill,
                  onSearch: (query) {
                    // Implement search in SkillsPage
                  }),
            ),
          ),
          const SizedBox(width: 10),
          ZoomIn(
            child: _buildClickableCard(
              context,
              title: Intl.message('Projects', name: 'projects'),
              content: provider.projects.isNotEmpty
                  ? provider.projects.map((p) => p['name']).join(', ')
                  : Intl.message('E-commerce App', name: 'default_project'),
              color: Colors.purple[400]!,
              page: ProjectsPage(
                  projects: provider.projects, onAddProject: _addProject),
            ),
          ),
          const SizedBox(width: 10),
          ZoomIn(
            child: _buildClickableCard(
              context,
              title: Intl.message('Settings', name: 'settings'),
              content: Intl.message('Change password and manage account',
                  name: 'settings_content'),
              color: Colors.orange[400]!,
              page: const SettingsPage(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildClickableCard(BuildContext context,
      {required String title,
        required String content,
        required Color color,
        required Widget page}) {
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: InkWell(
        onTap: () => Navigator.push(
            context, MaterialPageRoute(builder: (context) => page)),
        hoverColor: color.withOpacity(0.8),
        splashColor: Colors.white.withOpacity(0.3),
        borderRadius: BorderRadius.circular(15),
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          width: 200,
          padding: const EdgeInsets.all(15),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [color, color.withOpacity(0.7)],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(15),
            boxShadow: [
              BoxShadow(
                color: color.withOpacity(0.5),
                blurRadius: 10,
                offset: const Offset(3, 3),
              ),
              BoxShadow(
                color: Colors.white.withOpacity(0.2),
                blurRadius: 10,
                offset: const Offset(-3, -3),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: const TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 10),
              Text(
                content,
                style: const TextStyle(color: Colors.white),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPostInputSection(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 15),
      child: Column(
        children: [
          TextField(
            controller: _postController,
            decoration: InputDecoration(
              hintText: Intl.message('What\'s on your mind?', name: 'post_hint'),
              filled: true,
              fillColor: _isDarkMode ? Colors.grey[700] : Colors.grey[100],
              border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide.none),
            ),
            maxLines: 3,
          ),
          const SizedBox(height: 10),
          GradientButton(
            onPressed: _submitPost,
            child: Text(Intl.message('Post', name: 'post'),
                style: const TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  Widget _buildPostsSection(BuildContext context) {
    final provider = context.watch<ProfileProvider>();
    return Column(
      children: provider.posts
          .map((post) => FadeInUp(
        child: Card(
          margin:
          const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
          shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16)),
          elevation: 8,
          color: _isDarkMode ? Colors.grey[800] : Colors.white,
          child: Padding(
            padding: const EdgeInsets.all(15),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    CircleAvatar(
                        backgroundImage:
                        CachedNetworkImageProvider(post.userImage)),
                    const SizedBox(width: 10),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(post.userName,
                            style: const TextStyle(
                                fontWeight: FontWeight.bold)),
                        Text(post.time,
                            style: TextStyle(color: Colors.grey[600])),
                      ],
                    ),
                  ],
                ),
                const SizedBox(height: 10),
                Text(post.content),
                if (post.postImage != null) ...[
                  const SizedBox(height: 10),
                  ClipRRect(
                    borderRadius: BorderRadius.circular(12),
                    child: CachedNetworkImage(
                      imageUrl: '${AppState.getBackendUrl()}${post.postImage!}',
                      placeholder: (context, url) =>
                      const CircularProgressIndicator(),
                      errorWidget: (context, url, error) =>
                      const Icon(Icons.error),
                    ),
                  ),
                ],
                const SizedBox(height: 10),
                GradientButton(
                  onPressed: () async {
                    final shareText =
                        '${Intl.message('Post by', name: 'post_by')} ${provider.username}: ${post.content}';
                    final Uri shareUri = Uri.parse(
                        'https://twitter.com/intent/tweet?text=${Uri.encodeComponent(shareText)}');
                    if (await canLaunchUrl(shareUri)) {
                      await launchUrl(shareUri);
                    } else {
                      _showSnackBar(context,
                          Intl.message('Cannot open sharing app',
                              name: 'cannot_open_sharing_app'));
                    }
                  },
                  child: Text(
                      Intl.message('Share Post', name: 'share_post'),
                      style: const TextStyle(color: Colors.white)),
                ),
              ],
            ),
          ),
        ),
      ))
          .toList(),
    );
  }

  Widget _buildMedicalReportSection(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 15),
      child: Column(
        children: [
          Text(
              Intl.message('Send medical report to patient\'s secure file',
                  name: 'send_medical_report'),
              style: const TextStyle(
                  fontSize: 18, fontWeight: FontWeight.bold, color: Colors.teal)),
          const SizedBox(height: 10),
          TextField(
            controller: _patientUsernameController,
            decoration: InputDecoration(
              hintText: Intl.message('Patient username', name: 'patient_username'),
              filled: true,
              fillColor: _isDarkMode ? Colors.grey[700] : Colors.grey[100],
              border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide.none),
            ),
          ),
          const SizedBox(height: 10),
          GradientButton(
            onPressed: _uploadMedicalReport,
            child: Text(Intl.message('Upload Medical Report', name: 'upload_medical_report'),
                style: const TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  Widget _buildSecureFilesSection(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 15),
      child: GradientButton(
        onPressed: _viewSecureFiles,
        child: Text(Intl.message('View Secure Files', name: 'view_secure_files'),
            style: const TextStyle(color: Colors.white)),
      ),
    );
  }

  Widget _buildContactSection(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(15),
      child: Column(
        children: [
          Text(Intl.message('Contact Me', name: 'contact_me'),
              style: const TextStyle(
                  fontSize: 18, fontWeight: FontWeight.bold, color: Colors.teal)),
          const SizedBox(height: 20),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _build3DIconButton(
                icon: FontAwesomeIcons.message,
                color: Colors.orange[400]!,
                onPressed: () => _showSnackBar(context,
                    Intl.message('Messages opened', name: 'messages_opened')),
              ),
              const SizedBox(width: 20),
              _build3DIconButton(
                icon: FontAwesomeIcons.phone,
                color: Colors.green[400]!,
                onPressed: () async {
                  final Uri phoneUri = Uri(scheme: 'tel', path: '************');
                  if (await canLaunchUrl(phoneUri)) {
                    await launchUrl(phoneUri);
                  } else {
                    _showSnackBar(context,
                        Intl.message('Cannot make call', name: 'cannot_make_call'));
                  }
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _build3DIconButton(
      {required IconData icon,
        required Color color,
        required VoidCallback onPressed}) {
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: GestureDetector(
        onTap: onPressed,
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          padding: const EdgeInsets.all(15),
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: LinearGradient(
              colors: [color, color.withOpacity(0.7)],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            boxShadow: [
              BoxShadow(
                color: color.withOpacity(0.5),
                blurRadius: 10,
                offset: const Offset(3, 3),
              ),
              BoxShadow(
                color: Colors.white.withOpacity(0.2),
                blurRadius: 10,
                offset: const Offset(-3, -3),
              ),
            ],
          ),
          child: FaIcon(icon, size: 30, color: Colors.white),
        ),
      ),
    );
  }

  void _showSnackBar(BuildContext context, String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.teal[700],
          behavior: SnackBarBehavior.floating,
          shape:
          RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        ),
      );
    }
  }
}

class ParallaxBackground extends StatelessWidget {
  final String imageUrl;

  const ParallaxBackground({super.key, required this.imageUrl});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 250, // يمكنك تغيير الارتفاع حسب الحاجة
      child: LayoutBuilder(
        builder: (context, constraints) {
          return Stack(
            children: [
              Positioned.fill(
                child: CachedNetworkImage(
                  imageUrl: imageUrl,
                  fit: BoxFit.cover,
                  alignment: const Alignment(0, -0.3),
                  placeholder: (context, url) => const CircularProgressIndicator(),
                  errorWidget: (context, url, error) => const Icon(Icons.error),
                ),
              ),
              Positioned.fill(
                child: Container(
                  color: Colors.black.withOpacity(0.3),
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}

class GradientButton extends StatelessWidget {
  final VoidCallback onPressed;
  final Widget child;

  const GradientButton(
      {super.key, required this.onPressed, required this.child});

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        backgroundColor: Colors.transparent,
        shadowColor: Colors.transparent,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [Colors.teal[700]!, Colors.teal[300]!],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(12),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: child,
      ),
    );
  }
}

class TimelineTile extends StatelessWidget {
  final String title;
  final String date;
  final IconData icon;
  final bool isFirst;
  final bool isLast;

  const TimelineTile({
    super.key,
    required this.title,
    required this.date,
    required this.icon,
    required this.isFirst,
    required this.isLast,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Column(
          children: [
            if (!isFirst) const SizedBox(height: 10),
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.teal[700],
              ),
              child: Icon(icon, color: Colors.white, size: 20),
            ),
            if (!isLast)
              Container(
                width: 2,
                height: 50,
                color: Colors.teal[700],
              ),
          ],
        ),
        const SizedBox(width: 10),
        Expanded(
          child: Card(
            margin: const EdgeInsets.symmetric(vertical: 5),
            shape:
            RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            elevation: 4,
            child: ListTile(
              title: Text(title,
                  style: const TextStyle(fontWeight: FontWeight.bold)),
              subtitle: Text(date,
                  style: const TextStyle(fontSize: 12, color: Colors.grey)),
            ),
          ),
        ),
      ],
    );
  }
}

class SecureFilesPage extends StatelessWidget {
  final List<Map<String, dynamic>> secureFiles;
  final Function(String, String) onDecrypt;
  final String? userType;
  final Function(String) onShare;

  const SecureFilesPage({
    super.key,
    required this.secureFiles,
    required this.onDecrypt,
    this.userType,
    required this.onShare,
  });

  @override
  Widget build(BuildContext context) {
    final TextEditingController shareController = TextEditingController();
    return Scaffold(
      appBar: AppBar(
        title: Text(Intl.message('Secure Files', name: 'secure_files')),
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [Colors.teal[700]!, Colors.teal[300]!],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
        ),
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [Colors.teal[50]!, Colors.white],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
        ),
        child: Column(
          children: [
            Expanded(
              child: ListView.builder(
                padding: const EdgeInsets.all(15),
                itemCount: secureFiles.length,
                itemBuilder: (context, index) {
                  return Card(
                    margin: const EdgeInsets.symmetric(vertical: 10),
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16)),
                    elevation: 8,
                    child: ListTile(
                      leading: FaIcon(
                        secureFiles[index]['name'].endsWith('.pdf')
                            ? FontAwesomeIcons.filePdf
                            : FontAwesomeIcons.fileImage,
                        color: Colors.teal[700],
                      ),
                      title: Text(secureFiles[index]['name']),
                      subtitle: Text(Intl.message('Encrypted and secure',
                          name: 'encrypted_and_secure')),
                      trailing: IconButton(
                        icon: const FaIcon(FontAwesomeIcons.lockOpen,
                            color: Colors.teal),
                        onPressed: () {
                          TextEditingController codeController =
                          TextEditingController();
                          showDialog(
                            context: context,
                            builder: (context) => AlertDialog(
                              shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(20)),
                              title: Text(
                                  Intl.message('Decrypt File', name: 'decrypt_file'),
                                  style: const TextStyle(color: Colors.teal)),
                              content: TextField(
                                controller: codeController,
                                obscureText: true,
                                decoration: InputDecoration(
                                  hintText: Intl.message('Enter decryption code',
                                      name: 'enter_decryption_code'),
                                  filled: true,
                                  fillColor: Colors.grey[100],
                                  border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(12),
                                      borderSide: BorderSide.none),
                                ),
                              ),
                              actions: [
                                TextButton(
                                    onPressed: () => Navigator.pop(context),
                                    child: Text(Intl.message('Cancel', name: 'cancel'),
                                        style: const TextStyle(color: Colors.grey))),
                                ElevatedButton(
                                  onPressed: () {
                                    onDecrypt(secureFiles[index]['_id'],
                                        codeController.text);
                                    Navigator.pop(context);
                                  },
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.teal[700],
                                    shape: RoundedRectangleBorder(
                                        borderRadius:
                                        BorderRadius.circular(12)),
                                  ),
                                  child: Text(
                                      Intl.message('Decrypt', name: 'decrypt'),
                                      style: const TextStyle(color: Colors.white)),
                                ),
                              ],
                            ),
                          );
                        },
                      ),
                    ),
                  );
                },
              ),
            ),
            if (userType == 'patient')
              Padding(
                padding: const EdgeInsets.all(15),
                child: Column(
                  children: [
                    Text(
                        Intl.message('Share Secure Files',
                            name: 'share_secure_files'),
                        style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.teal)),
                    const SizedBox(height: 10),
                    TextField(
                      controller: shareController,
                      decoration: InputDecoration(
                        hintText: Intl.message('Username to share with',
                            name: 'username_to_share_with'),
                        filled: true,
                        fillColor: Colors.grey[100],
                        border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide.none),
                      ),
                    ),
                    const SizedBox(height: 10),
                    ElevatedButton(
                      onPressed: () {
                        if (shareController.text.isNotEmpty) {
                          onShare(shareController.text);
                          shareController.clear();
                        }
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.teal[700],
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12)),
                      ),
                      child: Text(Intl.message('Share', name: 'share'),
                          style: const TextStyle(color: Colors.white)),
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }
}

class AboutPage extends StatefulWidget {
  final String? aboutContent;
  final Function(String) onUpdate;

  const AboutPage({super.key, this.aboutContent, required this.onUpdate});

  @override
  _AboutPageState createState() => _AboutPageState();
}

class _AboutPageState extends State<AboutPage> {
  TextEditingController controller = TextEditingController();
  String? mediaUrl;
  bool _showMedia = true;

  @override
  void initState() {
    super.initState();
    controller.text = widget.aboutContent ?? '';
  }

  Future<void> _uploadMedia() async {
    FilePickerResult? result = await FilePicker.platform.pickFiles(
      type: FileType.custom,
      allowedExtensions: ['jpg', 'png', 'mp4'],
    );
    if (result != null) {
      final croppedFile = await ImageCropper().cropImage(
        sourcePath: kIsWeb ? result.files.single.path! : result.files.single.path!,
        aspectRatio: const CropAspectRatio(ratioX: 1, ratioY: 1),
        uiSettings: [
          AndroidUiSettings(
            toolbarTitle: Intl.message('Crop Media', name: 'crop_media'),
            toolbarColor: Colors.teal[700],
            toolbarWidgetColor: Colors.white,
          ),
          IOSUiSettings(
            title: Intl.message('Crop Media', name: 'crop_media'),
          ),
        ],
      );
      if (croppedFile != null) {
        var request = http.MultipartRequest(
            'POST', Uri.parse('${AppState.getBackendUrl()}/about/media'));
        final token = await AppState.getAuthToken();
        request.headers['x-auth-token'] = token;
        if (kIsWeb) {
          final bytes = result.files.single.bytes;
          if (bytes != null) {
            request.files.add(http.MultipartFile.fromBytes('media', bytes,
                filename: result.files.single.name));
          }
        } else {
          request.files.add(
              await http.MultipartFile.fromPath('media', croppedFile.path));
        }
        try {
          final response = await request.send();
          if (response.statusCode == 200) {
            final responseData = await response.stream.bytesToString();
            if (mounted) {
              setState(() => mediaUrl = json.decode(responseData)['mediaUrl']);
              _showSnackBar(context,
                  Intl.message('Media uploaded successfully', name: 'media_uploaded_success'));
            }
          } else {
            _showSnackBar(context,
                'Failed to upload media: ${response.statusCode}');
          }
        } catch (e) {
          _showSnackBar(context, 'Error uploading media: $e');
        }
      }
    }
  }

  void _toggleMediaVisibility() {
    if (mounted) {
      setState(() => _showMedia = !_showMedia);
    }
  }

  void _showSnackBar(BuildContext context, String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.teal[700],
          behavior: SnackBarBehavior.floating,
          shape:
          RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(Intl.message('About Me', name: 'about_me')),
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [Colors.teal[700]!, Colors.teal[300]!],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(15),
        child: Column(
          children: [
            TextField(
              controller: controller,
              maxLines: 5,
              decoration: InputDecoration(
                labelText: Intl.message('About Me', name: 'about_me'),
                filled: true,
                fillColor: Colors.grey[100],
                border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide.none),
              ),
            ),
            const SizedBox(height: 20),
            if (mediaUrl != null && _showMedia)
              mediaUrl!.endsWith('.mp4')
                  ? SizedBox(
                height: 200,
                child: VideoPlayer(
                  VideoPlayerController.network(mediaUrl!)
                    ..initialize().then((_) {
                      if (mounted) setState(() {});
                    }),
                ),
              )
                  : CachedNetworkImage(
                imageUrl: mediaUrl!,
                height: 200,
                fit: BoxFit.cover,
                placeholder: (context, url) =>
                const CircularProgressIndicator(),
                errorWidget: (context, url, error) =>
                const Icon(Icons.error),
              ),
            const SizedBox(height: 20),
            GradientButton(
              onPressed: _uploadMedia,
              child: Text(
                  Intl.message('Attach Image or Video', name: 'attach_media'),
                  style: const TextStyle(color: Colors.white)),
            ),
            if (mediaUrl != null) ...[
              const SizedBox(height: 20),
              GradientButton(
                onPressed: _toggleMediaVisibility,
                child: Text(
                    _showMedia
                        ? Intl.message('Hide Media', name: 'hide_media')
                        : Intl.message('Show Media', name: 'show_media'),
                    style: const TextStyle(color: Colors.white)),
              ),
            ],
            const SizedBox(height: 20),
            GradientButton(
              onPressed: () => widget.onUpdate(controller.text),
              child: Text(Intl.message('Update', name: 'update'),
                  style: const TextStyle(color: Colors.white)),
            ),
          ],
        ),
      ),
    );
  }
}

class SkillsPage extends StatelessWidget {
  final List<dynamic> skills;
  final Function(String) onAddSkill;
  final Function(String) onSearch;

  const SkillsPage(
      {super.key,
        required this.skills,
        required this.onAddSkill,
        required this.onSearch});

  @override
  Widget build(BuildContext context) {
    TextEditingController controller = TextEditingController();
    TextEditingController searchController = TextEditingController();
    return Scaffold(
        appBar: AppBar(
        title: Text(Intl.message('Skills', name: 'skills')),
    flexibleSpace: Container(
    decoration: BoxDecoration(
    gradient: LinearGradient(
    colors: [Colors.green[700]!, Colors.green[300]!],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    ),
    ),
    ),
    ),
    body: Padding(
    padding: const EdgeInsets.all(15),
    child: Column(
    children: [
    TextField(
    controller: searchController,
    decoration: InputDecoration(
    labelText: Intl.message('Search for skill', name: 'search_skill'),
    filled: true,
    fillColor: Colors.grey[100],
    border: OutlineInputBorder(
    borderRadius: BorderRadius.circular(12),
    borderSide: BorderSide.none),
    prefixIcon: const Icon(Icons.search),
    ),
    onChanged: onSearch,
    ),
    const SizedBox(height: 20),
    TextField(
    controller: controller,
    decoration: InputDecoration(
    labelText: Intl.message('Add new skill', name: 'add_new_skill'),
    filled: true,
    fillColor: Colors.grey[100],
    border: OutlineInputBorder(
    borderRadius: BorderRadius.circular(12),
    borderSide: BorderSide.none),
    ),
    ),
    const SizedBox(height: 20),
    GradientButton(
    onPressed: () {
    if (controller.text.isNotEmpty) {
    onAddSkill(controller.text);
    controller.clear();
    }
    },
    child: Text(Intl.message('Add', name: 'add'),
        style: const TextStyle(color: Colors.white)),
    ),
      const SizedBox(height: 20),
      Expanded(
        child: ListView.builder(
          itemCount: skills.length,
          itemBuilder: (context, index) {
            return FadeInUp(
              child: Card(
                margin: const EdgeInsets.symmetric(vertical: 5),
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12)),
                elevation: 4,
                child: ListTile(
                  title: Text(skills[index]['name']),
                  trailing: IconButton(
                    icon: const FaIcon(FontAwesomeIcons.trash,
                        color: Colors.red),
                    onPressed: () {
                      // Simulate skill deletion (requires backend API)
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(Intl.message(
                              'Skill deletion not implemented',
                              name: 'skill_deletion_not_implemented')),
                          backgroundColor: Colors.teal[700],
                        ),
                      );
                    },
                  ),
                ),
              ),
            );
          },
        ),
      ),
    ],
    ),
    ),
    );
  }
}

class ProjectsPage extends StatelessWidget {
  final List<dynamic> projects;
  final Function(String, String) onAddProject;

  const ProjectsPage(
      {super.key, required this.projects, required this.onAddProject});

  @override
  Widget build(BuildContext context) {
    final TextEditingController nameController = TextEditingController();
    final TextEditingController descController = TextEditingController();

    return Scaffold(
      appBar: AppBar(
        title: Text(Intl.message('Projects', name: 'projects')),
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [Colors.purple[700]!, Colors.purple[300]!],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(15),
        child: Column(
          children: [
            TextField(
              controller: nameController,
              decoration: InputDecoration(
                labelText: Intl.message('Project Name', name: 'project_name'),
                filled: true,
                fillColor: Colors.grey[100],
                border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide.none),
              ),
            ),
            const SizedBox(height: 10),
            TextField(
              controller: descController,
              decoration: InputDecoration(
                labelText:
                Intl.message('Project Description', name: 'project_description'),
                filled: true,
                fillColor: Colors.grey[100],
                border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide.none),
              ),
              maxLines: 3,
            ),
            const SizedBox(height: 20),
            GradientButton(
              onPressed: () {
                if (nameController.text.isNotEmpty &&
                    descController.text.isNotEmpty) {
                  onAddProject(nameController.text, descController.text);
                  nameController.clear();
                  descController.clear();
                } else {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(Intl.message(
                          'Please fill all fields',
                          name: 'fill_all_fields')),
                      backgroundColor: Colors.teal[700],
                    ),
                  );
                }
              },
              child: Text(Intl.message('Add Project', name: 'add_project'),
                  style: const TextStyle(color: Colors.white)),
            ),
            const SizedBox(height: 20),
            Expanded(
              child: ListView.builder(
                itemCount: projects.length,
                itemBuilder: (context, index) {
                  return FadeInUp(
                    child: Card(
                      margin: const EdgeInsets.symmetric(vertical: 5),
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12)),
                      elevation: 4,
                      child: ListTile(
                        title: Text(projects[index]['name']),
                        subtitle: Text(projects[index]['description']),
                        trailing: IconButton(
                          icon: const FaIcon(FontAwesomeIcons.trash,
                              color: Colors.red),
                          onPressed: () {
                            // Simulate project deletion (requires backend API)
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text(Intl.message(
                                    'Project deletion not implemented',
                                    name: 'project_deletion_not_implemented')),
                                backgroundColor: Colors.teal[700],
                              ),
                            );
                          },
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class SettingsPage extends StatefulWidget {
  const SettingsPage({super.key});

  @override
  _SettingsPageState createState() => _SettingsPageState();
}

class _SettingsPageState extends State<SettingsPage> {
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _confirmPasswordController =
  TextEditingController();

  Future<void> _changePassword() async {
    if (_passwordController.text == _confirmPasswordController.text &&
        _passwordController.text.isNotEmpty) {
      try {
        final token = await AppState.getAuthToken();
        final response = await http.post(
          Uri.parse('${AppState.getBackendUrl()}/settings/change-password'),
          headers: {'x-auth-token': token, 'Content-Type': 'application/json'},
          body: json.encode({'newPassword': _passwordController.text}),
        );
        if (response.statusCode == 200) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(Intl.message(
                    'Password changed successfully',
                    name: 'password_changed_success')),
                backgroundColor: Colors.teal[700],
              ),
            );
            _passwordController.clear();
            _confirmPasswordController.clear();
          }
        } else {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(Intl.message(
                    'Failed to change password: ${response.statusCode}',
                    name: 'password_change_failed')),
                backgroundColor: Colors.teal[700],
              ),
            );
          }
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(Intl.message(
                  'Error changing password: $e',
                  name: 'password_change_error')),
              backgroundColor: Colors.teal[700],
            ),
          );
        }
      }
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(Intl.message(
                'Passwords do not match or are empty',
                name: 'passwords_do_not_match')),
            backgroundColor: Colors.teal[700],
          ),
        );
      }
    }
  }

  @override
  void dispose() {
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(Intl.message('Settings', name: 'settings')),
        actions: [
          IconButton(
            icon: Icon(Icons.share),
            onPressed: () async {
              final url = 'https://mysite.com/profile/${Provider.of<ProfileProvider>(context, listen: false).username}';
              await launchUrl(Uri.parse(url));
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('تم نسخ رابط الصفحة!')),
              );
            },
            tooltip: 'مشاركة الصفحة',
          ),
        ],
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [Theme.of(context).primaryColor, Theme.of(context).colorScheme.secondary],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(15),
        child: Consumer<ProfileProvider>(
          builder: (context, provider, child) {
            if (provider.isLoadingProfile) {
              return Center(child: CircularProgressIndicator());
            }
            return ListView(
              children: [
                // صورة الغلاف
                GestureDetector(
                  onTap: () async {
                    FilePickerResult? result = await FilePicker.platform.pickFiles(type: FileType.image);
                    if (result != null) {
                      provider.changeBannerImage(result.files.single.path!);
                      ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('تم تحديث صورة الغلاف!')));
                    }
                  },
                  child: Stack(
                    alignment: Alignment.bottomRight,
                    children: [
                      Container(
                        height: 150,
                        decoration: BoxDecoration(
                          image: DecorationImage(
                            image: NetworkImage(provider.profileBannerUrl ?? ''),
                            fit: BoxFit.cover,
                          ),
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: CircleAvatar(
                          backgroundColor: Colors.black54,
                          child: Icon(Icons.edit, color: Colors.white),
                        ),
                      )
                    ],
                  ),
                ),
                const SizedBox(height: 10),
                // صورة الملف الشخصي
                Center(
                  child: GestureDetector(
                    onTap: () async {
                      FilePickerResult? result = await FilePicker.platform.pickFiles(type: FileType.image);
                      if (result != null) {
                        provider.changeProfileImage(result.files.single.path!);
                        ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('تم تحديث الصورة الشخصية!')));
                      }
                    },
                    child: CircleAvatar(
                      radius: 50,
                      backgroundImage: NetworkImage(provider.profileImagePath ?? ''),
                      child: Align(
                        alignment: Alignment.bottomRight,
                        child: CircleAvatar(
                          radius: 15,
                          backgroundColor: Colors.black54,
                          child: Icon(Icons.edit, color: Colors.white, size: 18),
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 10),
                // اسم المستخدم
                Center(
                  child: Text(
                    provider.username,
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                ),
                const SizedBox(height: 10),
                // تقييم النجوم
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: List.generate(5, (index) => IconButton(
                    icon: Icon(
                      index < provider.stars ? Icons.star : Icons.star_border,
                      color: Colors.amber,
                    ),
                    onPressed: () => provider.rateProfile(index + 1),
                  )),
                ),
                const SizedBox(height: 10),
                // الإنجازات
                Text('الإنجازات', style: Theme.of(context).textTheme.titleMedium),
                SizedBox(
                  height: 90,
                  child: ListView(
                    scrollDirection: Axis.horizontal,
                    children: provider.achievements.map((ach) => Card(
                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                      child: Padding(
                        padding: const EdgeInsets.all(12.0),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(ach['icon'], color: Colors.orange, size: 30),
                            const SizedBox(height: 5),
                            Text(ach['title'], style: TextStyle(fontSize: 12)),
                          ],
                        ),
                      ),
                    )).toList(),
                  ),
                ),
                const SizedBox(height: 10),
                // باقي الإعدادات والحقول كما هي ...
                // ...
                Text(
                  Intl.message('Change Password', name: 'change_password'),
                  style: const TextStyle(
                      fontSize: 18, fontWeight: FontWeight.bold, color: Colors.teal),
                ),
                const SizedBox(height: 10),
                TextField(
                  controller: _passwordController,
                  obscureText: true,
                  decoration: InputDecoration(
                    labelText: Intl.message('New Password', name: 'new_password'),
                    filled: true,
                    fillColor: Colors.grey[100],
                    border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: BorderSide.none),
                  ),
                ),
                const SizedBox(height: 10),
                TextField(
                  controller: _confirmPasswordController,
                  obscureText: true,
                  decoration: InputDecoration(
                    labelText:
                    Intl.message('Confirm Password', name: 'confirm_password'),
                    filled: true,
                    fillColor: Colors.grey[100],
                    border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: BorderSide.none),
                  ),
                ),
                const SizedBox(height: 20),
                GradientButton(
                  onPressed: _changePassword,
                  child: Text(Intl.message('Change Password', name: 'change_password'),
                      style: const TextStyle(color: Colors.white)),
                ),
                const SizedBox(height: 20),
                Text(
                  Intl.message('Other Settings', name: 'other_settings'),
                  style: const TextStyle(
                      fontSize: 18, fontWeight: FontWeight.bold, color: Colors.teal),
                ),
                const SizedBox(height: 10),
                ListTile(
                  title: Text(Intl.message('Notifications', name: 'notifications')),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(Intl.message(
                            'Notifications settings not implemented',
                            name: 'notifications_not_implemented')),
                        backgroundColor: Colors.teal[700],
                      ),
                    );
                  },
                ),
                ListTile(
                  title: Text(Intl.message('Privacy', name: 'privacy')),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(Intl.message(
                            'Privacy settings not implemented',
                            name: 'privacy_not_implemented')),
                        backgroundColor: Colors.teal[700],
                      ),
                    );
                  },
                ),
              ],
            );
          },
        ),
      ),
    );
  }
}

// تم إضافة دعم رفع وتغيير صورة الملف الشخصي والغلاف، نظام تقييم النجوم، زر مشاركة الصفحة، تحسين عرض الإنجازات، دعم الوضع الليلي، ولودر متحرك أثناء التحميل.
// إذا رغبت بمزيد من الميزات أو تخصيص إضافي أخبرني بذلك!