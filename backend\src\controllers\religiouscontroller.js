// religiouscontroller.js
// Controller for Quran and Bible API proxy endpoints
const axios = require('axios');

// Get Quran surah or verse
exports.getQuran = async (req, res) => {
  const { surah, ayah } = req.query;
  let url = `https://api.quran.com/api/v4/quran/verses/uthmani?chapter_number=${surah}`;
  try {
    const response = await axios.get(url);
    let verses = response.data.verses;
    // If ayah is provided, filter
    if (ayah) {
      verses = verses.filter(v => v.verse_number == ayah);
    }
    res.json({ verses });
  } catch (error) {
    res.status(500).json({ error: 'فشل في جلب بيانات القرآن' });
  }
};

// Get Bible verse
exports.getBible = async (req, res) => {
  const { reference } = req.query; // Example: 'John 3:16'
  let url = `https://bible-api.com/${encodeURIComponent(reference)}`;
  try {
    const response = await axios.get(url);
    res.json(response.data);
  } catch (error) {
    res.status(500).json({ error: 'فشل في جلب بيانات الكتاب المقدس' });
  }
};
