const mongoose = require('mongoose');

const VideoSchema = new mongoose.Schema({
  title: { type: String, required: true },
  url: { type: String, required: true }
});

const ArticleSchema = new mongoose.Schema({
  title: { type: String, required: true },
  content: { type: String, required: true }
});

const FAQSchema = new mongoose.Schema({
  question: { type: String, required: true },
  answer: { type: String, required: true }
});

const FishBreedingGroupSchema = new mongoose.Schema({
  name: { type: String, required: true },
  imageUrl: { type: String, required: true },
  videos: [VideoSchema],
  articles: [ArticleSchema],
  faqs: [FAQSchema]
});

module.exports = mongoose.model('FishBreedingGroup', FishBreedingGroupSchema);
