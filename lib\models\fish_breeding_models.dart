class FishBreedingGroup {
  final String id;
  final String name;
  final String imageUrl;
  final List<Video> videos;
  final List<Article> articles;
  final List<FAQ> faqs;
  final String habitat;
  final String diet;
  final String lifespan;
  final String careTips;

  FishBreedingGroup({
    required this.id,
    required this.name,
    required this.imageUrl,
    required this.videos,
    required this.articles,
    required this.faqs,
    required this.habitat,
    required this.diet,
    required this.lifespan,
    required this.careTips,
  });

  // Convert from JSON
  factory FishBreedingGroup.fromJson(Map<String, dynamic> json) {
    return FishBreedingGroup(
      id: json['id'] as String,
      name: json['name'] as String,
      imageUrl: json['imageUrl'] as String,
      videos: (json['videos'] as List).map((v) => Video(
        title: v['title'] as String,
        url: v['url'] as String,
      )).toList(),
      articles: (json['articles'] as List).map((a) => Article(
        title: a['title'] as String,
        content: a['content'] as String,
      )).toList(),
      faqs: (json['faqs'] as List).map((f) => FAQ(
        question: f['question'] as String,
        answer: f['answer'] as String,
      )).toList(),
      habitat: json['habitat'] as String? ?? '',
      diet: json['diet'] as String? ?? '',
      lifespan: json['lifespan'] as String? ?? '',
      careTips: json['careTips'] as String? ?? '',
    );
  }

  // Convert to JSON
  Map<String, dynamic> toJson() => {
    'id': id,
    'name': name,
    'imageUrl': imageUrl,
    'videos': videos.map((v) => {
      'title': v.title,
      'url': v.url,
    }).toList(),
    'articles': articles.map((a) => {
      'title': a.title,
      'content': a.content,
    }).toList(),
    'faqs': faqs.map((f) => {
      'question': f.question,
      'answer': f.answer,
    }).toList(),
    'habitat': habitat,
    'diet': diet,
    'lifespan': lifespan,
    'careTips': careTips,
  };
}

class Video {
  final String title;
  final String url;

  Video({required this.title, required this.url});
}

class Article {
  final String title;
  final String content;

  Article({required this.title, required this.content});
}

class FAQ {
  final String question;
  final String answer;

  FAQ({required this.question, required this.answer});
}

class Post {
  final String user;
  final String content;
  final String? imageUrl;
  int likes;
  int comments;
  int shares;

  Post({
    required this.user, 
    required this.content, 
    this.imageUrl,
    this.likes = 0,
    this.comments = 0,
    this.shares = 0,
  });
}

class ChatMessage {
  final String userId;
  final String userName;
  final String text;

  ChatMessage({required this.userId, required this.userName, required this.text});
}

class NotificationItem {
  final String title;
  final String description;
  final DateTime date;

  NotificationItem({required this.title, required this.description, required this.date});
}
