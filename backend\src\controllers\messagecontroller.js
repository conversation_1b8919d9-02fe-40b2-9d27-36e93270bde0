const Message = require('../models/message.model');
const User = require('../models/user.model');
const ErrorResponse = require('../utils/errorResponse');
const asyncHandler = require('../middleware/asyncmiddleware');
const path = require('path');
const fs = require('fs');
const multer = require('multer');

// إعداد تخزين الملفات
const storage = multer.diskStorage({
  destination: function(req, file, cb) {
    const uploadPath = './public/uploads/messages';
    if (!fs.existsSync(uploadPath)) {
      fs.mkdirSync(uploadPath, { recursive: true });
    }
    cb(null, uploadPath);
  },
  filename: function(req, file, cb) {
    cb(null, `message_${Date.now()}${path.extname(file.originalname)}`);
  }
});

// فلترة الملفات
const fileFilter = (req, file, cb) => {
  // قبول الصور والصوتيات والفيديوهات والملفات
  if (
    file.mimetype.startsWith('image') ||
    file.mimetype.startsWith('audio') ||
    file.mimetype.startsWith('video') ||
    file.mimetype.startsWith('application')
  ) {
    cb(null, true);
  } else {
    cb(new ErrorResponse('نوع الملف غير مدعوم', 400), false);
  }
};

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 10000000 // 10 ميجابايت
  },
  fileFilter: fileFilter
});

// @desc    الحصول على رسائل المحادثة بين مستخدمين
// @route   GET /api/messages/:userId
// @access  Private
exports.getMessages = asyncHandler(async (req, res, next) => {
  const messages = await Message.find({
    $or: [
      { senderId: req.user.id, receiverId: req.params.userId },
      { senderId: req.params.userId, receiverId: req.user.id }
    ]
  }).sort({ timestamp: 1 });

  // تحديث حالة القراءة للرسائل المستلمة
  await Message.updateMany(
    { senderId: req.params.userId, receiverId: req.user.id, isRead: false },
    { isRead: true }
  );

  res.status(200).json({
    success: true,
    count: messages.length,
    data: messages
  });
});

// @desc    إرسال رسالة جديدة
// @route   POST /api/messages
// @access  Private
exports.sendMessage = asyncHandler(async (req, res, next) => {
  // التحقق من وجود المستقبل
  const receiver = await User.findById(req.body.receiverId);
  if (!receiver) {
    return next(new ErrorResponse('المستقبل غير موجود', 404));
  }

  // التعامل مع تحميل المرفقات
  const uploadMiddleware = upload.single('attachment');

  uploadMiddleware(req, res, async (err) => {
    if (err) {
      return next(new ErrorResponse(`خطأ في تحميل الملف: ${err.message}`, 400));
    }

    // إنشاء الرسالة
    const messageData = {
      senderId: req.user.id,
      receiverId: req.body.receiverId,
      content: req.body.content,
      type: 'text'
    };

    // إضافة معلومات المرفق إذا تم تحميل ملف
    if (req.file) {
      messageData.attachmentUrl = `/uploads/messages/${req.file.filename}`;

      // تحديد نوع المرفق
      if (req.file.mimetype.startsWith('image')) {
        messageData.type = 'image';
      } else if (req.file.mimetype.startsWith('audio')) {
        messageData.type = 'audio';
      } else if (req.file.mimetype.startsWith('video')) {
        messageData.type = 'video';
      } else {
        messageData.type = 'file';
      }
    }

    const message = await Message.create(messageData);

    // إشعار للمستلم إذا لم يكن هو نفسه المرسل
    if (req.user.id !== receiver._id.toString()) {
      receiver.notifications.push({
        type: 'message',
        text: `لديك رسالة جديدة من ${req.user.name}`,
        link: `/messages/${req.user.id}`
      });
      await receiver.save();
      // إرسال إشعار Push إذا كان لدى المستخدم fcmToken
      if (receiver.fcmToken) {
        const axios = require('axios');
        await axios.post('https://fcm.googleapis.com/fcm/send', {
          to: receiver.fcmToken,
          notification: {
            title: 'رسالة خاصة جديدة',
            body: `لديك رسالة جديدة من ${req.user.name}`,
            click_action: `/messages/${req.user.id}`
          }
        }, {
          headers: {
            'Authorization': `key=${process.env.FCM_SERVER_KEY}`,
            'Content-Type': 'application/json'
          }
        }).catch(() => {});
      }
    }

    res.status(201).json({
      success: true,
      data: message
    });
  });
});

// @desc    الحصول على قائمة المحادثات
// @route   GET /api/messages/conversations
// @access  Private
exports.getConversations = asyncHandler(async (req, res, next) => {
  // الحصول على آخر رسالة من كل محادثة
  const conversations = await Message.aggregate([
    {
      $match: {
        $or: [
          { senderId: req.user._id },
          { receiverId: req.user._id }
        ]
      }
    },
    {
      $sort: { timestamp: -1 }
    },
    {
      $group: {
        _id: {
          $cond: [
            { $eq: ["$senderId", req.user._id] },
            "$receiverId",
            "$senderId"
          ]
        },
        lastMessage: { $first: "$$ROOT" },
        unreadCount: {
          $sum: {
            $cond: [
              { $and: [
                { $eq: ["$receiverId", req.user._id] },
                { $eq: ["$isRead", false] }
              ]},
              1,
              0
            ]
          }
        }
      }
    },
    {
      $lookup: {
        from: 'users',
        localField: '_id',
        foreignField: '_id',
        as: 'user'
      }
    },
    {
      $unwind: '$user'
    },
    {
      $project: {
        _id: 1,
        lastMessage: 1,
        unreadCount: 1,
        user: {
          _id: 1,
          name: 1,
          avatarUrl: 1,
          isOnline: 1
        }
      }
    }
  ]);

  res.status(200).json({
    success: true,
    count: conversations.length,
    data: conversations
  });
});

// @desc    تحديث حالة قراءة الرسائل
// @route   PUT /api/messages/read/:userId
// @access  Private
exports.markAsRead = asyncHandler(async (req, res, next) => {
  await Message.updateMany(
    { senderId: req.params.userId, receiverId: req.user.id, isRead: false },
    { isRead: true }
  );

  res.status(200).json({
    success: true,
    data: {}
  });
});
