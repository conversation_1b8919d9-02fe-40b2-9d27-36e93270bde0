import 'package:audioplayers/audioplayers.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';

class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  final FlutterLocalNotificationsPlugin _notificationsPlugin = FlutterLocalNotificationsPlugin();
  final AudioPlayer _audioPlayer = AudioPlayer();

  Future<void> initialize() async {
    const AndroidInitializationSettings initializationSettingsAndroid =
    AndroidInitializationSettings('app_icon');
    const InitializationSettings initializationSettings = InitializationSettings(
      android: initializationSettingsAndroid,
    );
    await _notificationsPlugin.initialize(initializationSettings);
  }

  Future<void> showNotification({
    required String title,
    required String body,
    required String notificationType, // text, sound, both
    required Set<String> selectedCategories, // christian, islamic, wisdom
  }) async {
    // تحديد ما إذا كان يجب تشغيل إشعار كتابي
    if (notificationType == 'text' || notificationType == 'both') {
      const AndroidNotificationDetails androidDetails = AndroidNotificationDetails(
        'fulk_channel',
        'Fulk Notifications',
        importance: Importance.max,
        priority: Priority.high,
      );
      const NotificationDetails notificationDetails = NotificationDetails(android: androidDetails);

      await _notificationsPlugin.show(
        DateTime.now().millisecondsSinceEpoch % 100000,
        title,
        body,
        notificationDetails,
      );
    }

    // تحديد ما إذا كان يجب تشغيل صوت
    if (notificationType == 'sound' || notificationType == 'both') {
      await _playNotificationSound(selectedCategories);
    }
  }

  Future<void> _playNotificationSound(Set<String> selectedCategories) async {
    String? soundPath;
    if (selectedCategories.contains('christian')) {
      soundPath = 'sounds/christian_notification.mp3';
    } else if (selectedCategories.contains('islamic')) {
      soundPath = 'sounds/islamic_notification.mp3';
    } else if (selectedCategories.contains('wisdom')) {
      soundPath = 'sounds/wisdom_notification.mp3';
    }

    if (soundPath != null) {
      await _audioPlayer.play(AssetSource(soundPath));
    }
  }
}