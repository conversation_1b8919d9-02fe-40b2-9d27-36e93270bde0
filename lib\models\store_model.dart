class Product {
  final String type;
  final String thumbnail;
  final String? videoThumbnail;
  final double price;
  final String intro;

  Product({
    required this.type,
    required this.thumbnail,
    this.videoThumbnail,
    required this.price,
    required this.intro,
  });

  factory Product.fromJson(Map<String, dynamic> json) {
    return Product(
      type: json['type'] ?? 'image',
      thumbnail: json['thumbnail'] ?? 'https://via.placeholder.com/200',
      videoThumbnail: json['videoThumbnail'],
      price: (json['price'] ?? 0).toDouble(),
      intro: json['intro'] ?? 'لا يوجد وصف',
    );
  }
}

class Store {
  final String id;
  final String name;
  final String description;
  final String phone;
  double _rating;
  final List<Product> products;
  bool isFavorited;

  Store({
    required this.id,
    required this.name,
    required this.description,
    required this.phone,
    required double rating,
    required this.products,
    this.isFavorited = false,
  }) : _rating = rating;

  double get rating => _rating;

  void setRating(double newRating) {
    _rating = newRating;
  }

  factory Store.fromJson(Map<String, dynamic> json) {
    return Store(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      phone: json['phone'] ?? '',
      rating: (json['rating'] ?? 0).toDouble(),
      products: (json['products'] as List<dynamic>?)
          ?.map((item) => Product.fromJson(item))
          .toList() ??
          [],
      isFavorited: json['isFavorited'] ?? false,
    );
  }
}
