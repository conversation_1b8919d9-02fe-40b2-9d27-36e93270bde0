import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:animate_do/animate_do.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:provider/provider.dart';
import 'package:http/http.dart' as http;
import '../appstate.dart';
import 'models/user_model.dart';
import 'domino_game.dart';

class GamesPage extends StatefulWidget {
  const GamesPage({super.key});

  @override
  _GamesPageState createState() => _GamesPageState();
}

class _GamesPageState extends State<GamesPage> {
  final TextEditingController _searchController = TextEditingController();
  List<Game> _games = [];
  List<Game> _filteredGames = [];
  String _selectedCategory = 'الكل';
  final List<String> _categories = ['الكل', 'لوحية', 'استراتيجية', 'تقليدية'];

  @override
  void initState() {
    super.initState();
    _initializeGames();
    _searchController.addListener(_filterGames);
  }

  void _initializeGames() {
    _games = [
      Game(
        id: '1',
        title: 'لودو',
        description: 'استمتع بلعبة لودو الكلاسيكية مع الأصدقاء!',
        imageUrl: 'https://images.unsplash.com/photo-1600585154340-be6161a56a0c',
        category: 'لوحية',
        url: 'https://www.ludogames.com/play',
        isLocal: false,
      ),
      Game(
        id: '2',
        title: 'الشطرنج',
        description: 'تحدَ أصدقاءك في لعبة الشطرنج الاستراتيجية.',
        imageUrl: 'https://images.unsplash.com/photo-1594026112284-02bb9f7d06c3',
        category: 'استراتيجية',
        url: 'https://www.chess.com/play',
        isLocal: false,
      ),
      Game(
        id: '3',
        title: 'الدومينو',
        description: 'استمعببلعبة الدومينو الكلاسيكية مع الأصدقاء!',
        imageUrl: 'https://images.unsplash.com/photo-1600585154340-be6161a56a0c',
        category: 'تقليدية',
        isLocal: true,
        route: '/domino',
      ),
      Game(
        id: '3',
        title: 'دومينو',
        description: 'جرب مهاراتك في لعبة الدومينو الممتعة.',
        imageUrl: 'https://images.unsplash.com/photo-1600585154340-be6161a56b0d', // صورة دومينو
        category: 'تقليدية',
        url: null,
        isLocal: true,
        route: '/game/domino',
      ),
    ];
    _filteredGames = List.from(_games);
  }

  void _filterGames() {
    final query = _searchController.text.toLowerCase();
    setState(() {
      _filteredGames = _games.where((game) {
        final matchesSearch = _selectedCategory == 'الكل' || game.category == _selectedCategory;
        final matchesText = game.title.toLowerCase().contains(query) ||
            game.description.toLowerCase().contains(query);
        return matchesSearch && matchesText;
      }).toList();
    });
  }

  Future<void> _launchGameUrl(String url, BuildContext context) async {
    final appState = Provider.of<AppState>(context, listen: false);
    if (await canLaunch(url)) {
      await launch(url);
    } else {
      appState.showSnackBar('لا يمكن فتح اللعبة', Colors.red);
    }
  }

  void _navigateToGame(BuildContext context, Game game) {
    final appState = Provider.of<AppState>(context, listen: false);
    if (game.isLocal && game.route != null) {
      switch (game.route) {
        case '/domino':
          Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => const DominoGamePage()),
          );
          break;
        default:
          appState.showSnackBar('مسار اللعبة غير معروف', Colors.red);
      }
    } else if (game.url != null) {
      _launchGameUrl(game.url!, context);
    }
  }

  void _inviteFriend(Game game) {
    final appState = Provider.of<AppState>(context, listen: false);
    showDialog(
      context: context,
      builder: (context) => InviteFriendDialog(
        game: game,
        onInvite: (friendId) {
          appState.socket?.emit('game_invite', {
            'gameId': game.id,
            'gameTitle': game.title,
            'senderId': appState.username,
            'recipientId': friendId,
          });
          appState.showSnackBar('تم إرسال دعوة للعب ${game.title}', Colors.green);
        },
      ),
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final appState = Provider.of<AppState>(context);

    return Container(
      color: theme.scaffoldBackgroundColor,
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.fromLTRB(12.0, 8.0, 12.0, 8.0),
            child: TextField(
              controller: _searchController,
              style: const TextStyle(color: Colors.white, fontFamily: 'Tajawal'),
              decoration: InputDecoration(
                hintText: 'ابحث عن لعبة...',
                hintStyle: TextStyle(
                    color: Colors.white.withOpacity(0.6), fontFamily: 'Tajawal'),
                filled: true,
                fillColor: Colors.grey[800],
                prefixIcon: const Icon(Icons.search, color: Colors.white),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(25),
                  borderSide: BorderSide.none,
                ),
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 12.0),
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: _categories.map((category) {
                  return Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 4.0),
                    child: ChoiceChip(
                      label: Text(
                        category,
                        style: TextStyle(
                          fontFamily: 'Tajawal',
                          color: _selectedCategory == category
                              ? Colors.white
                              : Colors.grey,
                        ),
                      ),
                      selected: _selectedCategory == category,
                      onSelected: (selected) {
                        if (selected) {
                          setState(() {
                            _selectedCategory = category;
                            _filterGames();
                          });
                        }
                      },
                      selectedColor: theme.primaryColor,
                      backgroundColor: Colors.grey[800],
                    ),
                  );
                }).toList(),
              ),
            ),
          ),
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'الألعاب المتاحة',
                    style: TextStyle(
                      fontFamily: 'Tajawal',
                      color: theme.textTheme.titleLarge?.color ?? Colors.white,
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  _filteredGames.isEmpty
                      ? Center(
                    child: Text(
                      'لا توجد ألعاب مطابقة',
                      style: _getTextStyle(context, fontSize: 18),
                    ),
                  )
                      : ListView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: _filteredGames.length,
                    itemBuilder: (context, index) {
                      final game = _filteredGames[index];
                      return FadeInUp(
                        duration: const Duration(milliseconds: 300),
                        child: Card(
                          color: theme.cardColor,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(15),
                          ),
                          elevation: 5,
                          margin: const EdgeInsets.symmetric(
                              horizontal: 12, vertical: 6),
                          child: ListTile(
                            leading: ClipRRect(
                              borderRadius: BorderRadius.circular(10),
                              child: Image.network(
                                game.imageUrl,
                                width: 60,
                                height: 60,
                                fit: BoxFit.cover,
                                errorBuilder:
                                    (context, error, stackTrace) =>
                                    Icon(
                                      Icons.gamepad,
                                      size: 60,
                                      color: theme.primaryColor,
                                    ),
                              ),
                            ),
                            title: Text(
                              game.title,
                              style:
                              _getTextStyle(context, isTitle: true),
                            ),
                            subtitle: Text(
                              game.description,
                              style: TextStyle(
                                color: theme.textTheme.bodyMedium?.color
                                    ?.withOpacity(0.8) ??
                                    Colors.grey[600],
                                fontFamily: 'Tajawal',
                              ),
                            ),
                            trailing: IconButton(
                              icon: const Icon(Icons.group_add,
                                  color: Colors.white),
                              onPressed: () => _inviteFriend(game),
                            ),
                            onTap: () => _navigateToGame(context, game),
                          ),
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  TextStyle _getTextStyle(BuildContext context,
      {bool isTitle = false, double? fontSize}) {
    final theme = Theme.of(context);
    if (isTitle) {
      return TextStyle(
        color: theme.textTheme.titleLarge?.color ?? Colors.white,
        fontFamily: 'Tajawal',
        fontWeight: FontWeight.bold,
        fontSize: fontSize ?? 18,
      );
    } else {
      return TextStyle(
        color: theme.textTheme.bodyLarge?.color ?? Colors.white,
        fontFamily: 'Tajawal',
        fontSize: fontSize ?? 16,
      );
    }
  }
}

class Game {
  final String id;
  final String title;
  final String description;
  final String imageUrl;
  final String category;
  final String? url;
  final bool isLocal;
  final String? route;

  Game({
    required this.id,
    required this.title,
    required this.description,
    required this.imageUrl,
    required this.category,
    this.url,
    this.isLocal = false,
    this.route,
  });
}

class InviteFriendDialog extends StatefulWidget {
  final Game game;
  final Function(String) onInvite;

  const InviteFriendDialog(
      {super.key, required this.game, required this.onInvite});

  @override
  _InviteFriendDialogState createState() => _InviteFriendDialogState();
}

class _InviteFriendDialogState extends State<InviteFriendDialog> {
  List<UserModel> friends = [];
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    _fetchFriends();
  }

  Future<void> _fetchFriends() async {
    final appState = Provider.of<AppState>(context, listen: false);
    try {
      final response = await http.get(
        Uri.parse('${AppState.getBackendUrl()}/api/friends'),
        headers: {'Authorization': 'Bearer ${appState.token}'},
      );
      if (response.statusCode == 200) {
        final List<dynamic> friendData = json.decode(response.body);
        setState(() {
          friends = friendData.map<UserModel>((data) => UserModel.fromJson({
            '_id': data['_id'],
            'name': data['username'] ?? 'مستخدم',
            'email': data['email'] ?? '',
            'avatarUrl': data['avatarUrl'] != null
                ? '${AppState.getBackendUrl()}${data['avatarUrl']}'
                : 'https://via.placeholder.com/150',
            'role': data['role'] ?? 'member',
            'latitude': data['latitude']?.toDouble() ?? 0.0,
            'longitude': data['longitude']?.toDouble() ?? 0.0,
            'phoneNumber': data['phoneNumber'],
            'bio': data['bio'],
            'isVerified': data['isVerified'] ?? false,
            'isFavorite': data['isFavorite'] ?? false,
            'shareLocation': data['shareLocation'] ?? false,
          }))
              .toList();
          isLoading = false;
        });
      } else {
        throw Exception('فشل في جلب الأصدقاء');
      }
    } catch (e) {
      Provider.of<AppState>(context, listen: false)
          .showSnackBar('خطأ في جلب الأصدقاء: $e', Colors.red);
      setState(() => isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return AlertDialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      backgroundColor: Colors.grey[800],
      title: Text('دعوة صديق للعب ${widget.game.title}',
          style: TextStyle(color: theme.primaryColor, fontFamily: 'Tajawal')),
      content: isLoading
          ? const Center(child: CircularProgressIndicator())
          : friends.isEmpty
          ? const Text('لا يوجد أصدقاء لدعوتهم',
          style: TextStyle(color: Colors.white, fontFamily: 'Tajawal'))
          : SizedBox(
        width: double.maxFinite,
        height: 200,
        child: ListView.builder(
          itemCount: friends.length,
          itemBuilder: (context, index) {
            final friend = friends[index];
            return ListTile(
              leading: CircleAvatar(
                backgroundImage: NetworkImage(friend.avatarUrl),
                radius: 20,
              ),
              title: Text(friend.name,
                  style: const TextStyle(
                      color: Colors.white, fontFamily: 'Tajawal')),
              onTap: () {
                widget.onInvite(friend.id);
                Navigator.pop(context);
              },
            );
          },
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('إلغاء',
              style: TextStyle(color: Colors.grey, fontFamily: 'Tajawal')),
        ),
      ],
    );
  }
}
