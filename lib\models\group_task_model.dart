import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';

class GroupTask {
  final String id;
  final String title;
  final String groupId;
  final String userId;
  final String category;
  final String points;
  final TimeOfDay startTime;
  final int duration;
  final bool isCompleted;
  final String? completionMediaPath;
  final String? description;
  final DateTime? dueDate;
  final List<String> collaborators; // أضيفت لعلاج خطأ collaborators
  final bool isCollaborative; // أضيفت لعلاج خطأ isCollaborative

  GroupTask({
    required this.id,
    required this.title,
    required this.groupId,
    required this.userId,
    required this.category,
    required this.points,
    required this.startTime,
    required this.duration,
    this.isCompleted = false,
    this.completionMediaPath,
    this.description,
    this.dueDate,
    this.collaborators = const [], // قيمة افتراضية
    this.isCollaborative = false, // قيمة افتراضية
  });

  factory GroupTask.fromJson(Map<String, dynamic> json) {
    return GroupTask(
      id: json['id'] ?? const Uuid().v4(),
      title: json['title'] ?? '',
      groupId: json['groupId'] ?? '',
      userId: json['userId'] ?? '',
      category: json['category'] ?? '',
      points: json['points'] ?? '0',
      startTime: TimeOfDay(
        hour: json['startTime']?['hour'] ?? 0,
        minute: json['startTime']?['minute'] ?? 0,
      ),
      duration: json['duration'] ?? 0,
      isCompleted: json['isCompleted'] ?? false,
      completionMediaPath: json['completionMediaPath'],
      description: json['description'],
      dueDate: json['dueDate'] != null ? DateTime.parse(json['dueDate']) : null,
      collaborators: List<String>.from(json['collaborators'] ?? []),
      isCollaborative: json['isCollaborative'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'groupId': groupId,
      'userId': userId,
      'category': category,
      'points': points,
      'startTime': {'hour': startTime.hour, 'minute': startTime.minute},
      'duration': duration,
      'isCompleted': isCompleted,
      'completionMediaPath': completionMediaPath,
      'description': description,
      'dueDate': dueDate?.toIso8601String(),
      'collaborators': collaborators,
      'isCollaborative': isCollaborative,
    };
  }

  GroupTask copyWith({
    String? id,
    String? title,
    String? groupId,
    String? userId,
    String? category,
    String? points,
    TimeOfDay? startTime,
    int? duration,
    bool? isCompleted,
    bool? isDone,
    String? completionMediaPath,
    String? description,
    DateTime? dueDate,
    List<String>? collaborators,
    bool? isCollaborative,
  }) {
    return GroupTask(
      id: id ?? this.id,
      title: title ?? this.title,
      groupId: groupId ?? this.groupId,
      userId: userId ?? this.userId,
      category: category ?? this.category,
      points: points ?? this.points,
      startTime: startTime ?? this.startTime,
      duration: duration ?? this.duration,
      isCompleted: isDone ?? isCompleted ?? this.isCompleted,
      completionMediaPath: completionMediaPath ?? this.completionMediaPath,
      description: description ?? this.description,
      dueDate: dueDate ?? this.dueDate,
      collaborators: collaborators ?? this.collaborators,
      isCollaborative: isCollaborative ?? this.isCollaborative,
    );
  }
}
