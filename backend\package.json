{"name": "fulk-backend", "version": "1.0.0", "description": "باك إند لتطبيق Fulk باستخدام Node.js", "main": "app.js", "type": "module", "scripts": {"start": "NODE_ENV=production node server.js", "dev": "NODE_ENV=development nodemon --inspect server.js", "debug": "NODE_ENV=development nodemon --inspect server.js", "test": "jest --detect<PERSON><PERSON><PERSON><PERSON><PERSON> --forceExit", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint . --ext .js", "lint:fix": "eslint . --ext .js --fix", "format": "prettier --write \"**/*.{js,json,md}\"", "seed": "node scripts/seed.js", "seed:videos": "node scripts/seedVideos.js", "migrate": "node scripts/migrate.js", "migrate:videos": "node scripts/migrateVideos.js"}, "keywords": ["nodejs", "express", "mongodb", "socket.io", "api", "restful", "jwt", "authentication"], "author": "", "license": "ISC", "dependencies": {"bcryptjs": "^2.4.3", "compression": "^1.7.4", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "dotenv": "^16.0.3", "express": "^4.18.2", "express-mongo-sanitize": "^2.2.0", "express-rate-limit": "^6.7.0", "express-validator": "^7.0.1", "helmet": "^7.0.0", "hpp": "^0.2.3", "http-status-codes": "^2.2.0", "jsonwebtoken": "^9.0.0", "mathjs": "^14.4.0", "mongoose": "^7.1.0", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "socket.io": "^4.6.1", "winston": "^3.8.2", "xss-clean": "^0.1.1"}, "devDependencies": {"eslint": "^8.31.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^8.6.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^4.2.1", "jest": "^29.3.1", "nodemon": "^2.0.22", "prettier": "^2.8.2", "supertest": "^6.3.3"}}