import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'dart:math';
import 'models/reel_models.dart';
import 'services/reel_service.dart';
import 'providers/reel_provider.dart';
import 'config/reel_config.dart';
import 'widgets/updated_reel_widget.dart';
import 'appstate.dart';

// الصفحة الرئيسية للفيديوهات التعليمية المحدثة
class UpdatedReelsScreen extends StatefulWidget {
  final VoidCallback? onToggleTheme;

  const UpdatedReelsScreen({super.key, this.onToggleTheme});

  @override
  State<UpdatedReelsScreen> createState() => _UpdatedReelsScreenState();
}

class _UpdatedReelsScreenState extends State<UpdatedReelsScreen>
    with TickerProviderStateMixin {
  final PageController _pageController = PageController();
  int _currentReel = 0;
  String _preferredQuality = ReelConfig.defaultQuality;
  final Map<String, VideoPlayerController> _controllers = {};
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;
  final Map<String, int> _retryCounts = {};
  ReelProvider? _reelProvider;

  @override
  void initState() {
    super.initState();
    _loadPreferredQuality();
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _fadeAnimation = CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    );
    _fadeController.forward();

    // تهيئة ReelProvider
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _reelProvider = Provider.of<ReelProvider>(context, listen: false);
      _reelProvider?.initialize();
    });
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _disposeControllers();
    super.dispose();
  }

  void _disposeControllers() {
    for (var controller in _controllers.values) {
      controller.dispose();
    }
    _controllers.clear();
  }

  void _loadPreferredQuality() async {
    // يمكن تحميل الجودة المفضلة من SharedPreferences
    setState(() {
      _preferredQuality = ReelConfig.defaultQuality;
    });
  }

  void _preloadVideos() async {
    if (_reelProvider == null) return;

    final reels = _reelProvider!.reels;
    if (reels.isEmpty) return;

    final connectivityResult = await Connectivity().checkConnectivity();
    if (connectivityResult == ConnectivityResult.none) return;

    // تحديد الجودة المناسبة حسب نوع الاتصال
    final isWifi = connectivityResult == ConnectivityResult.wifi;
    final quality = isWifi ? _preferredQuality : '360p';

    final preloadIndices = [
      _currentReel,
      if (_currentReel + 1 < reels.length) _currentReel + 1,
      if (_currentReel - 1 >= 0) _currentReel - 1,
    ];

    for (var index in preloadIndices) {
      if (index < reels.length) {
        final reel = reels[index];
        if (!_controllers.containsKey(reel.id)) {
          _initializeVideoController(reel, index, quality);
        }
      }
    }

    // إزالة الفيديوهات غير المطلوبة
    _controllers.removeWhere((id, controller) {
      if (!preloadIndices.contains(reels.indexWhere((reel) => reel.id == id))) {
        controller.pause();
        controller.dispose();
        return true;
      }
      return false;
    });
  }

  void _initializeVideoController(ReelVideo reel, int index, String quality,
      {int retryCount = 0}) {
    final videoUrl = reel.videoUrls[quality] ??
                    reel.videoUrls[ReelConfig.defaultQuality] ??
                    reel.videoUrls.values.first;

    final controller = retryCount >= 3 && reel.fallbackAsset != null
        ? VideoPlayerController.asset(reel.fallbackAsset!)
        : VideoPlayerController.networkUrl(Uri.parse(videoUrl));

    _controllers[reel.id] = controller;

    controller.initialize().then((_) {
      if (!mounted) return;
      setState(() {
        _retryCounts.remove(reel.id);
        if (index == _currentReel) {
          controller.play();
          // تسجيل المشاهدة
          _reelProvider?.recordView(reel.id);
        }
      });
    }).catchError((error) {
      if (retryCount < ReelConfig.maxRetries) {
        _retryCounts[reel.id] = retryCount + 1;
        Future.delayed(ReelConfig.retryDelay, () {
          if (mounted) {
            _initializeVideoController(reel, index, quality,
                retryCount: retryCount + 1);
          }
        });
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(ReelConfig.getErrorMessage('video_not_found')),
            backgroundColor: Colors.red,
          ),
        );
      }
    });
  }

  void _onPageChanged(int index) {
    setState(() {
      _currentReel = index;
    });

    // إيقاف جميع الفيديوهات
    for (var controller in _controllers.values) {
      controller.pause();
    }

    // تشغيل الفيديو الحالي
    final reels = _reelProvider?.reels ?? [];
    if (index < reels.length) {
      final currentReel = reels[index];
      _controllers[currentReel.id]?.play();
    }

    _preloadVideos();

    // تحميل المزيد من الفيديوهات عند الاقتراب من النهاية
    if (index >= reels.length - 2) {
      _reelProvider?.loadMoreReels();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Consumer<ReelProvider>(
        builder: (context, reelProvider, child) {
          // حالة الخطأ
          if (reelProvider.hasError) {
            return _buildErrorState(reelProvider);
          }

          // حالة التحميل
          if (reelProvider.isLoading && reelProvider.reels.isEmpty) {
            return _buildLoadingState();
          }

          // حالة عدم وجود فيديوهات
          final reels = reelProvider.reels;
          if (reels.isEmpty) {
            return _buildEmptyState(reelProvider);
          }

          // عرض الفيديوهات
          return _buildReelsView(reels, reelProvider);
        },
      ),
    );
  }

  Widget _buildErrorState(ReelProvider reelProvider) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, size: 64, color: Colors.red),
          const SizedBox(height: 16),
          const Text(
            'خطأ في تحميل الفيديوهات',
            style: TextStyle(color: Colors.white, fontSize: 18),
          ),
          const SizedBox(height: 8),
          Text(
            reelProvider.errorMessage,
            style: const TextStyle(color: Colors.white70),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () => reelProvider.loadReels(refresh: true),
            child: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(color: Colors.white),
          SizedBox(height: 16),
          Text(
            'جاري تحميل الفيديوهات...',
            style: TextStyle(color: Colors.white),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(ReelProvider reelProvider) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.video_library_outlined, size: 64, color: Colors.grey),
          const SizedBox(height: 16),
          const Text(
            'لا توجد فيديوهات متاحة',
            style: TextStyle(color: Colors.white, fontSize: 18),
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () => reelProvider.loadReels(refresh: true),
            child: const Text('تحديث'),
          ),
        ],
      ),
    );
  }

  Widget _buildReelsView(List<ReelVideo> reels, ReelProvider reelProvider) {
    return Stack(
      children: [
        // عرض الفيديوهات
        RefreshIndicator(
          onRefresh: () => reelProvider.loadReels(refresh: true),
          child: PageView.builder(
            scrollDirection: Axis.vertical,
            controller: _pageController,
            itemCount: reels.length,
            onPageChanged: _onPageChanged,
            itemBuilder: (context, index) {
              return FadeTransition(
                opacity: _fadeAnimation,
                child: UpdatedReelWidget(
                  reel: reels[index],
                  controller: _controllers[reels[index].id],
                ),
              );
            },
          ),
        ),

        // شريط علوي مع أزرار التحكم
        Positioned(
          top: MediaQuery.of(context).padding.top + 10,
          left: 16,
          right: 16,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // زر تبديل الثيم
              IconButton(
                icon: Icon(
                  Theme.of(context).brightness == Brightness.dark
                      ? Icons.light_mode
                      : Icons.dark_mode,
                  color: Colors.white,
                ),
                onPressed: widget.onToggleTheme,
              ),

              // مؤشر الاتصال
              if (!reelProvider.isConnected)
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.red,
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: const Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.wifi_off, color: Colors.white, size: 16),
                      SizedBox(width: 4),
                      Text('غير متصل', style: TextStyle(color: Colors.white, fontSize: 12)),
                    ],
                  ),
                ),

              // زر البحث
              IconButton(
                icon: const Icon(Icons.search, color: Colors.white),
                onPressed: () => _showSearchDialog(context, reelProvider),
              ),
            ],
          ),
        ),

        // مؤشر التحميل للمزيد
        if (reelProvider.isLoading && reels.isNotEmpty)
          const Positioned(
            bottom: 50,
            left: 0,
            right: 0,
            child: Center(
              child: CircularProgressIndicator(color: Colors.white),
            ),
          ),
      ],
    );
  }

  void _showSearchDialog(BuildContext context, ReelProvider reelProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('البحث في الفيديوهات'),
        content: TextField(
          decoration: const InputDecoration(
            hintText: 'ابحث عن فيديو...',
            prefixIcon: Icon(Icons.search),
          ),
          onSubmitted: (query) async {
            Navigator.pop(context);
            if (query.trim().isNotEmpty) {
              final results = await reelProvider.searchReels(query.trim());
              if (results.isNotEmpty) {
                // يمكن إظهار نتائج البحث في صفحة منفصلة
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('تم العثور على ${results.length} نتيجة')),
                );
              } else {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('لم يتم العثور على نتائج')),
                );
              }
            }
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }
}
