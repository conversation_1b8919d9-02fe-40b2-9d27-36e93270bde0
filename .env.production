# إعدادات التطبيق
NODE_ENV=production
PORT=8000
HOST=0.0.0.0

# إعدادات قاعدة البيانات
MONGODB_URI=mongodb+srv://username:<EMAIL>/fulk?retryWrites=true&w=majority

# إعدادات JWT
JWT_SECRET=production_jwt_secret_key_must_be_very_long_and_complex
JWT_EXPIRES_IN=15d
JWT_COOKIE_EXPIRES_IN=15

# إعدادات CORS
CLIENT_URL=https://fulk-app.com
FRONTEND_URL=https://fulk-app.com
ALLOWED_ORIGINS=https://fulk-app.com,https://api.fulk-app.com

# إعدادات Supabase
SUPABASE_URL=https://gfgdgbjkorzpjewrjnhc.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdmZ2RnYmprb3J6cGpld3JqbmhjIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcxNTk2ODQsImV4cCI6MjA2MjczNTY4NH0.Rr2y0rzdghPnHp0QAD-edhJLZ9EXHRKWL9wtLiCIHZs

# إعدادات Firebase (اختياري)
FIREBASE_API_KEY=your_production_firebase_api_key
FIREBASE_AUTH_DOMAIN=your_production_firebase_auth_domain
FIREBASE_PROJECT_ID=your_production_firebase_project_id
FIREBASE_STORAGE_BUCKET=your_production_firebase_storage_bucket
FIREBASE_MESSAGING_SENDER_ID=your_production_firebase_messaging_sender_id
FIREBASE_APP_ID=your_production_firebase_app_id

# إعدادات التخزين (اختياري)
AWS_ACCESS_KEY_ID=your_production_aws_access_key_id
AWS_SECRET_ACCESS_KEY=your_production_aws_secret_access_key
AWS_REGION=your_production_aws_region
AWS_BUCKET_NAME=your_production_aws_bucket_name

# إعدادات البريد الإلكتروني (اختياري)
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_EMAIL=<EMAIL>
SMTP_PASSWORD=your_production_email_password
FROM_EMAIL=<EMAIL>
FROM_NAME=Fulk App
