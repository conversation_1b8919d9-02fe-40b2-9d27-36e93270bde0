import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:share_plus/share_plus.dart';
import 'package:animated_text_kit/animated_text_kit.dart';
import 'package:confetti/confetti.dart';
import 'package:intl/intl.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:geolocator/geolocator.dart';
import 'package:image_picker/image_picker.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:provider/provider.dart';
import 'appstate.dart';

class AnimalBreedingGroup {
  final String id;
  final String name;
  final String imageUrl;
  final String description;
  final String habitat;
  final String diet;
  final String lifespan;
  final String healthTips;
  final List<Video> videos;
  final List<Article> articles;
  final List<FAQ> faqs;
  final List<String> galleryImages;

  AnimalBreedingGroup({
    required this.id,
    required this.name,
    required this.imageUrl,
    required this.description,
    required this.habitat,
    required this.diet,
    required this.lifespan,
    required this.healthTips,
    required this.videos,
    required this.articles,
    required this.faqs,
    required this.galleryImages,
  });
}

class Video {
  final String title;
  final String url;

  Video({required this.title, required this.url});
}

class Article {
  final String title;
  final String content;

  Article({required this.title, required this.content});
}

class FAQ {
  final String question;
  final String answer;

  FAQ({required this.question, required this.answer});
}

class Post {
  final String user;
  final String content;
  final String? imageUrl;
  final int likes;
  final int comments;

  Post({
    required this.user,
    required this.content,
    this.imageUrl,
    this.likes = 0,
    this.comments = 0,
  });
}

class MarketItem {
  final String id;
  final String user;
  final String type;
  final List<String> imageUrls;
  final double quantity;
  final double price;
  final String address;
  final DateTime date;
  final String validity;
  final String contactNumber;
  final String details;
  final String? breed;
  final String? healthStatus;
  final String? applicantStatus;
  final String? registrationType;
  final String? description;
  final String? economicActivity;
  final String? economicActivityDetails;
  final String? waterSource;
  final String? terrainType;
  final String? electricitySource;
  final String? structures;
  final String? structuresDetails;

  MarketItem({
    required this.id,
    required this.user,
    required this.type,
    required this.imageUrls,
    required this.quantity,
    required this.price,
    required this.address,
    required this.date,
    required this.validity,
    required this.contactNumber,
    required this.details,
    this.breed,
    this.healthStatus,
    this.applicantStatus,
    this.registrationType,
    this.description,
    this.economicActivity,
    this.economicActivityDetails,
    this.waterSource,
    this.terrainType,
    this.electricitySource,
    this.structures,
    this.structuresDetails,
  });
}

class AnimalBreedingPage extends StatefulWidget {
  const AnimalBreedingPage({super.key});

  @override
  State<AnimalBreedingPage> createState() => _AnimalBreedingPageState();
}

class _AnimalBreedingPageState extends State<AnimalBreedingPage>
    with SingleTickerProviderStateMixin {
  late ConfettiController _confettiController;
  final TextEditingController _postController = TextEditingController();
  final TextEditingController _searchController = TextEditingController();
  final TextEditingController _healthCheckController = TextEditingController();
  final ImagePicker _picker = ImagePicker();
  late AnimationController _fabAnimationController;
  late Animation<double> _fabScaleAnimation;

  final List<String> supplyTypes = ['أعلاف', 'أدوية', 'معدات', 'أخرى'];
  final List<String> animalTypes = [
    'قطط',
    'كلاب',
    'أغنام',
    'ماعز',
    'أبقار',
    'خيول',
    'دجاج',
    'أرانب'
  ];
  final List<String> terrainTypes = ['رملية', 'طينية', 'عشبية', 'صخرية'];

  int userPoints = 100;

  bool _canAddItems(AppState appState) {
    final roleName = appState.currentUser?.role.name ?? 'guest';
    final userType = appState.userType ?? 'guest';
    return roleName == 'admin' ||
        roleName == 'hokama' ||
        userType == 'admin' ||
        userType == 'hokama';
  }

  final List<AnimalBreedingGroup> animals = [
    AnimalBreedingGroup(
      id: '1',
      name: 'القطط',
      imageUrl: 'https://cdn-icons-png.flaticon.com/512/616/616430.png',
      description:
          'القطط حيوانات أليفة محبوبة، معروفة بطباعها المستقلة وغريزتها القوية في الصيد.',
      habitat:
          'تعيش في البيوت، الحدائق، أو المناطق الحضرية. تحتاج إلى مكان مريح وآمن.',
      diet:
          'تتغذى على الطعام الجاف والرطب المخصص للقطط، مع ضرورة توفير الماء النظيف.',
      lifespan: '12-18 سنة في المتوسط مع العناية الجيدة.',
      healthTips: 'فحوصات بيطرية دورية، تطعيمات ضد الأمراض، وتنظيف الأسنان.',
      videos: [
        Video(
            title: 'طرق تربية القطط',
            url: 'https://www.youtube.com/watch?v=cat1'),
        Video(
            title: 'أمراض شائعة للقطط',
            url: 'https://www.youtube.com/watch?v=cat2'),
      ],
      articles: [
        Article(
            title: 'دليل العناية بالقطط',
            content: 'كل ما تحتاجه للعناية بالقطط...'),
      ],
      faqs: [
        FAQ(
            question: 'ماذا تأكل القطط؟',
            answer: 'تأكل الطعام الجاف، الرطب، واللحوم...'),
      ],
      galleryImages: [
        'https://cdn.pixabay.com/photo/2019/05/08/21/21/cat-4189697_1280.jpg',
        'https://cdn.pixabay.com/photo/2014/03/29/02/18/kitten-300341_1280.jpg',
      ],
    ),
    AnimalBreedingGroup(
      id: '2',
      name: 'الكلاب',
      imageUrl: 'https://cdn-icons-png.flaticon.com/512/616/616548.png',
      description:
          'الكلاب حيوانات وفية وذكية، تُعرف بقدرتها على التكيف مع بيئات مختلفة.',
      habitat:
          'تناسب المنازل، المزارع، أو الأماكن المفتوحة. تحتاج إلى مساحة للعب.',
      diet: 'تتطلب طعامًا متوازنًا يحتوي على البروتينات والفيتامينات.',
      lifespan: '10-14 سنة حسب السلالة والعناية.',
      healthTips: 'تطعيمات دورية، تمارين يومية، وفحص الأمراض الجلدية.',
      videos: [
        Video(
            title: 'تربية الكلاب', url: 'https://www.youtube.com/watch?v=dog1'),
      ],
      articles: [
        Article(title: 'صحة الكلاب', content: 'نصائح للحفاظ على صحة الكلاب...'),
      ],
      faqs: [
        FAQ(
            question: 'كيف أعرف أن الكلب مريض؟',
            answer: 'راقب نشاطه وشهيته وسلوكه...'),
      ],
      galleryImages: [
        'https://cdn.pixabay.com/photo/2016/12/13/00/13/dog-1900565_1280.jpg',
        'https://cdn.pixabay.com/photo/2016/02/19/15/46/dog-1210559_1280.jpg',
      ],
    ),
  ];

  final List<Post> posts = [
    Post(
      user: 'علي',
      content: 'كلبي تعلم خدعة جديدة!',
      imageUrl:
          'https://cdn.pixabay.com/photo/2016/12/13/00/13/dog-1900565_1280.jpg',
      likes: 15,
      comments: 3,
    ),
    Post(
      user: 'سارة',
      content: 'نصائح لتدريب القطط.',
      likes: 10,
      comments: 2,
    ),
  ];

  final List<MarketItem> animalItems = [
    MarketItem(
      id: '1',
      user: 'محمد',
      type: 'قطط',
      imageUrls: [
        'https://cdn.pixabay.com/photo/2019/05/08/21/21/cat-4189697_1280.jpg'
      ],
      quantity: 1.0,
      price: 800.0,
      address: 'الرياض',
      date: DateTime.now().subtract(const Duration(days: 1)),
      validity: 'شهر',
      contactNumber: '**********',
      details: 'قط سيامي أصيل',
      breed: 'سيامي',
      healthStatus: 'مطعم وصحي',
    ),
  ];

  final List<MarketItem> landItems = [
    MarketItem(
      id: '2',
      user: 'خالد',
      type: 'للبيع',
      imageUrls: [
        'https://cdn.pixabay.com/photo/2017/03/28/12/11/field-2182088_1280.jpg'
      ],
      quantity: 1000.0,
      price: 100000.0,
      address: 'الدمام',
      date: DateTime.now().subtract(const Duration(days: 3)),
      validity: 'شهر',
      contactNumber: '**********',
      details: 'أرض مناسبة لتربية الحيوانات',
      applicantStatus: 'مالك',
      registrationType: 'شهر عقاري',
      description: 'أرض ممتازة لمزرعة حيوانات',
      economicActivity: 'نعم',
      economicActivityDetails: 'تربية حيوانات',
      waterSource: 'آبار',
      terrainType: 'رملية',
      electricitySource: 'طاقة شمسية',
      structures: 'نعم',
      structuresDetails: 'حظيرة صغيرة',
    ),
  ];

  final List<MarketItem> supplyItems = [
    MarketItem(
      id: '3',
      user: 'يوسف',
      type: 'أعلاف',
      imageUrls: [
        'https://cdn.pixabay.com/photo/2017/06/29/12/06/hay-2453616_1280.jpg'
      ],
      quantity: 200.0,
      price: 2000.0,
      address: 'مكة',
      date: DateTime.now().subtract(const Duration(days: 1)),
      validity: 'شهر',
      contactNumber: '1122334455',
      details: 'أعلاف عالية الجودة للحيوانات',
    ),
  ];

  @override
  void initState() {
    super.initState();
    _confettiController =
        ConfettiController(duration: const Duration(seconds: 1));
    _fabAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 600),
    )..repeat(reverse: true);
    _fabScaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(parent: _fabAnimationController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _confettiController.dispose();
    _fabAnimationController.dispose();
    _postController.dispose();
    _searchController.dispose();
    _healthCheckController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Scaffold(
          appBar: AppBar(
            title: AnimatedTextKit(
              animatedTexts: [
                TypewriterAnimatedText(
                  'موسوعة تربية الحيوانات',
                  textStyle: const TextStyle(
                    fontSize: 22,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                    fontFamily: 'Tajawal',
                  ),
                  speed: const Duration(milliseconds: 100),
                ),
              ],
              totalRepeatCount: 1,
            ),
            backgroundColor: Colors.green.shade700,
            actions: [
              IconButton(
                icon: const Icon(Icons.search, color: Colors.white),
                onPressed: () => _showSearchDialog(context),
                tooltip: 'بحث',
              ),
            ],
            flexibleSpace: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [Colors.green.shade700, Colors.green.shade300],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
              ),
            ),
            elevation: 4,
          ),
          body: RefreshIndicator(
            onRefresh: () async {
              await Future.delayed(const Duration(seconds: 1));
              setState(() {});
              Fluttertoast.showToast(
                msg: 'تم تحديث الصفحة',
                backgroundColor: Colors.green.shade700,
              );
            },
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: AnimationLimiter(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: AnimationConfiguration.toStaggeredList(
                    duration: const Duration(milliseconds: 500),
                    childAnimationBuilder: (widget) => SlideAnimation(
                      verticalOffset: 50.0,
                      child: FadeInAnimation(child: widget),
                    ),
                    children: [
                      _buildSectionTitle('أنواع الحيوانات'),
                      const SizedBox(height: 16),
                      _buildAnimalCards(),
                      const SizedBox(height: 24),
                      _buildSectionTitle('سوق المواشي'),
                      const SizedBox(height: 12),
                      _buildAnimalsMarketplace(),
                      const SizedBox(height: 24),
                      _buildSectionTitle('سوق الأراضي'),
                      const SizedBox(height: 12),
                      _buildLandsMarketplace(),
                      const SizedBox(height: 24),
                      _buildSectionTitle('سوق المستلزمات البيطرية'),
                      const SizedBox(height: 12),
                      _buildSuppliesMarketplace(),
                      const SizedBox(height: 24),
                      _buildSectionTitle('منشورات المجتمع'),
                      const SizedBox(height: 12),
                      _buildPostInput(),
                      const SizedBox(height: 12),
                      _buildPostsList(),
                    ],
                  ),
                ),
              ),
            ),
          ),
          floatingActionButton: Consumer<AppState>(
            builder: (context, appState, _) {
              final isHokama = _canAddItems(appState);
              if (!isHokama) return const SizedBox.shrink();

              return Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Add Animal Type Button
                  ScaleTransition(
                    scale: _fabScaleAnimation,
                    child: Container(
                      margin: const EdgeInsets.only(bottom: 16.0),
                      child: FloatingActionButton(
                        heroTag: 'add_animal_type',
                        onPressed: () => _showAddAnimalTypeDialog(context),
                        tooltip: 'إضافة نوع حيوان جديد',
                        backgroundColor: Colors.blue.shade700,
                        elevation: 6,
                        child: const Icon(Icons.pets, color: Colors.white),
                      ),
                    ),
                  ),
                  // Quick Actions Button
                  ScaleTransition(
                    scale: _fabScaleAnimation,
                    child: FloatingActionButton(
                      onPressed: () => _showQuickActions(context),
                      tooltip: 'إجراءات سريعة',
                      backgroundColor: Colors.green.shade700,
                      elevation: 6,
                      child: const Icon(Icons.add, color: Colors.white),
                    ),
                  ),
                ],
              );
            },
          ),
        ),
        Align(
          alignment: Alignment.topCenter,
          child: ConfettiWidget(
            confettiController: _confettiController,
            blastDirectionality: BlastDirectionality.explosive,
            particleDrag: 0.05,
            emissionFrequency: 0.05,
            numberOfParticles: 50,
            gravity: 0.05,
            colors: const [
              Colors.green,
              Colors.blue,
              Colors.yellow,
              Colors.red
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSectionTitle(String title) {
    return AnimatedTextKit(
      animatedTexts: [
        FadeAnimatedText(
          title,
          textStyle: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.green.shade700,
                fontFamily: 'Tajawal',
              ),
        ),
      ],
      totalRepeatCount: 1,
    );
  }

  Widget _buildAnimalCards() {
    return SizedBox(
      height: 260,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: animals.length,
        itemBuilder: (context, index) {
          final animal = animals[index];
          return AnimationConfiguration.staggeredList(
            position: index,
            duration: const Duration(milliseconds: 600),
            child: SlideAnimation(
              horizontalOffset: 50.0,
              child: FadeInAnimation(
                child: GestureDetector(
                  onTap: () => _showAnimalDetails(context, animal),
                  child: Padding(
                    padding: const EdgeInsets.only(right: 16.0, left: 8.0),
                    child: MouseRegion(
                      onEnter: (_) => setState(() {}),
                      onExit: (_) => setState(() {}),
                      child: Card(
                        elevation: 10,
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(20)),
                        child: Container(
                          width: 180,
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                Colors.green.shade200,
                                Colors.green.shade400
                              ],
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                            ),
                            borderRadius: BorderRadius.circular(20),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.green.withOpacity(0.4),
                                blurRadius: 12,
                                offset: const Offset(0, 6),
                              ),
                            ],
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              ClipRRect(
                                borderRadius: const BorderRadius.vertical(
                                    top: Radius.circular(20)),
                                child: CachedNetworkImage(
                                  imageUrl: animal.imageUrl,
                                  height: 120,
                                  width: double.infinity,
                                  fit: BoxFit.cover,
                                  placeholder: (context, url) =>
                                      _buildShimmerPlaceholder(),
                                  errorWidget: (context, url, error) =>
                                      const Icon(Icons.error),
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsets.all(12.0),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    AnimatedTextKit(
                                      animatedTexts: [
                                        TypewriterAnimatedText(
                                          animal.name,
                                          textStyle: const TextStyle(
                                            fontSize: 18,
                                            fontWeight: FontWeight.bold,
                                            color: Colors.white,
                                            fontFamily: 'Tajawal',
                                          ),
                                          speed:
                                              const Duration(milliseconds: 100),
                                        ),
                                      ],
                                      totalRepeatCount: 1,
                                    ),
                                    const SizedBox(height: 4),
                                    Text(
                                      animal.description,
                                      maxLines: 2,
                                      overflow: TextOverflow.ellipsis,
                                      style: const TextStyle(
                                        fontSize: 12,
                                        color: Colors.white70,
                                        fontFamily: 'Tajawal',
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildAnimalsMarketplace() {
    return Stack(
      children: [
        Card(
          elevation: 6,
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.green.shade100, Colors.green.shade50],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.pets, color: Colors.green.shade700, size: 30),
                      const SizedBox(width: 8),
                      Text(
                        'سوق المواشي',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              color: Colors.green.shade700,
                              fontFamily: 'Tajawal',
                            ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  SizedBox(
                    height: 320,
                    child: ListView.builder(
                      scrollDirection: Axis.horizontal,
                      itemCount: animalItems.length,
                      itemBuilder: (context, index) {
                        final item = animalItems[index];
                        return AnimationConfiguration.staggeredList(
                          position: index,
                          duration: const Duration(milliseconds: 600),
                          child: SlideAnimation(
                            horizontalOffset: 50.0,
                            child: FadeInAnimation(
                              child: _buildMarketCard(item),
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        if (_canAddItems(Provider.of<AppState>(context)))
          Positioned(
            bottom: 16,
            right: 16,
            child: ScaleTransition(
              scale: _fabScaleAnimation,
              child: FloatingActionButton(
                onPressed: () => _showAddAnimalDialog(context),
                backgroundColor: Colors.green.shade700,
                tooltip: 'إضافة حيوان',
                elevation: 6,
                child: const Icon(Icons.add, color: Colors.white),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildLandsMarketplace() {
    return Stack(
      children: [
        Card(
          elevation: 6,
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.green.shade100, Colors.green.shade50],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.landscape,
                          color: Colors.green.shade700, size: 30),
                      const SizedBox(width: 8),
                      Text(
                        'سوق الأراضي',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              color: Colors.green.shade700,
                              fontFamily: 'Tajawal',
                            ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  SizedBox(
                    height: 320,
                    child: ListView.builder(
                      scrollDirection: Axis.horizontal,
                      itemCount: landItems.length,
                      itemBuilder: (context, index) {
                        final item = landItems[index];
                        return AnimationConfiguration.staggeredList(
                          position: index,
                          duration: const Duration(milliseconds: 600),
                          child: SlideAnimation(
                            horizontalOffset: 50.0,
                            child: FadeInAnimation(
                              child: _buildMarketCard(item),
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        if (_canAddItems(Provider.of<AppState>(context)))
          Positioned(
            bottom: 16,
            right: 16,
            child: ScaleTransition(
              scale: _fabScaleAnimation,
              child: FloatingActionButton(
                onPressed: () => _showAddLandDialog(context),
                backgroundColor: Colors.green.shade700,
                tooltip: 'إضافة أرض',
                elevation: 6,
                child: const Icon(Icons.add, color: Colors.white),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildSuppliesMarketplace() {
    return Stack(
      children: [
        Card(
          elevation: 6,
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.green.shade100, Colors.green.shade50],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.local_pharmacy,
                          color: Colors.green.shade700, size: 30),
                      const SizedBox(width: 8),
                      Text(
                        'سوق المستلزمات البيطرية',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              color: Colors.green.shade700,
                              fontFamily: 'Tajawal',
                            ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: supplyTypes
                        .map((type) => Chip(
                              label: Text(type),
                              backgroundColor: Colors.green.shade100,
                            ))
                        .toList(),
                  ),
                  const SizedBox(height: 12),
                  SizedBox(
                    height: 320,
                    child: ListView.builder(
                      scrollDirection: Axis.horizontal,
                      itemCount: supplyItems.length,
                      itemBuilder: (context, index) {
                        final item = supplyItems[index];
                        return AnimationConfiguration.staggeredList(
                          position: index,
                          duration: const Duration(milliseconds: 600),
                          child: SlideAnimation(
                            horizontalOffset: 50.0,
                            child: FadeInAnimation(
                              child: _buildMarketCard(item),
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        if (_canAddItems(Provider.of<AppState>(context)))
          Positioned(
            bottom: 16,
            right: 16,
            child: ScaleTransition(
              scale: _fabScaleAnimation,
              child: FloatingActionButton(
                onPressed: () => _showAddSupplyDialog(context),
                backgroundColor: Colors.green.shade700,
                tooltip: 'إضافة مستلزمات',
                elevation: 6,
                child: const Icon(Icons.add, color: Colors.white),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildMarketCard(MarketItem item) {
    return Padding(
      padding: const EdgeInsets.only(right: 12.0),
      child: MouseRegion(
        onEnter: (_) => setState(() {}),
        onExit: (_) => setState(() {}),
        child: Card(
          elevation: 8,
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          child: Container(
            width: 220,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.green.withOpacity(0.3),
                  blurRadius: 10,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ClipRRect(
                  borderRadius:
                      const BorderRadius.vertical(top: Radius.circular(16)),
                  child: item.imageUrls.isNotEmpty
                      ? CachedNetworkImage(
                          imageUrl: item.imageUrls.first,
                          width: 220,
                          height: 140,
                          fit: BoxFit.cover,
                          placeholder: (context, url) =>
                              _buildShimmerPlaceholder(),
                          errorWidget: (context, url, error) =>
                              const Icon(Icons.error),
                        )
                      : Container(
                          width: 220,
                          height: 140,
                          color: Colors.grey.shade300,
                          child: Icon(Icons.image, color: Colors.grey.shade600),
                        ),
                ),
                Flexible(
                  child: SingleChildScrollView(
                    child: Padding(
                      padding: const EdgeInsets.all(12.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            item.type,
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                              fontFamily: 'Tajawal',
                            ),
                          ),
                          Text(
                            'الكمية: ${item.quantity} ${item.breed != null ? 'رأس' : item.applicantStatus != null ? 'متر مربع' : 'كجم'}',
                            style: const TextStyle(fontFamily: 'Tajawal'),
                          ),
                          Text(
                            'السعر: ${item.price} ريال',
                            style: const TextStyle(fontFamily: 'Tajawal'),
                          ),
                          Text(
                            'العنوان: ${item.address}',
                            style: const TextStyle(fontFamily: 'Tajawal'),
                          ),
                          Text(
                            'رقم التواصل: ${item.contactNumber}',
                            style: const TextStyle(fontFamily: 'Tajawal'),
                          ),
                          Text(
                            'التفاصيل: ${item.details}',
                            style: const TextStyle(fontFamily: 'Tajawal'),
                          ),
                          Text(
                            'التاريخ: ${DateFormat('dd/MM/yyyy').format(item.date)}',
                            style: const TextStyle(
                                fontSize: 12,
                                color: Colors.grey,
                                fontFamily: 'Tajawal'),
                          ),
                          if (item.breed != null) ...[
                            Text('السلالة: ${item.breed}',
                                style: const TextStyle(fontFamily: 'Tajawal')),
                            Text('الحالة الصحية: ${item.healthStatus}',
                                style: const TextStyle(fontFamily: 'Tajawal')),
                          ],
                          if (item.applicantStatus != null) ...[
                            Text('الحالة: ${item.applicantStatus}',
                                style: const TextStyle(fontFamily: 'Tajawal')),
                            Text('التسجيل: ${item.registrationType}',
                                style: const TextStyle(fontFamily: 'Tajawal')),
                            Text('مصدر المياه: ${item.waterSource}',
                                style: const TextStyle(fontFamily: 'Tajawal')),
                            Text('نوع الأرض: ${item.terrainType}',
                                style: const TextStyle(fontFamily: 'Tajawal')),
                            Text('الكهرباء: ${item.electricitySource}',
                                style: const TextStyle(fontFamily: 'Tajawal')),
                            Text('المنشآت: ${item.structures}',
                                style: const TextStyle(fontFamily: 'Tajawal')),
                          ],
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildShimmerPlaceholder() {
    return Container(
      width: double.infinity,
      height: 140,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.grey.shade300,
            Colors.grey.shade100,
            Colors.grey.shade300
          ],
          stops: const [0.1, 0.3, 0.4],
          begin: const Alignment(-1.0, -0.3),
          end: const Alignment(1.0, 0.3),
          tileMode: TileMode.repeated,
        ),
      ),
    );
  }

  void _showAnimalDetails(BuildContext context, AnimalBreedingGroup animal) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.9,
        minChildSize: 0.5,
        maxChildSize: 0.95,
        builder: (context, scrollController) => Container(
          decoration: BoxDecoration(
            color: Theme.of(context).scaffoldBackgroundColor,
            borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
            boxShadow: [
              BoxShadow(
                color: Colors.green.withOpacity(0.3),
                blurRadius: 10,
                offset: const Offset(0, -5),
              ),
            ],
          ),
          child: DefaultTabController(
            length: 6,
            child: Column(
              children: [
                Container(
                  margin: const EdgeInsets.symmetric(vertical: 8),
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: Colors.grey,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                TabBar(
                  isScrollable: true,
                  tabs: const [
                    Tab(text: 'نظرة عامة'),
                    Tab(text: 'الفيديوهات'),
                    Tab(text: 'المقالات'),
                    Tab(text: 'الأسئلة الشائعة'),
                    Tab(text: 'التغذية'),
                    Tab(text: 'معرض الصور'),
                  ],
                  labelColor: Colors.green.shade700,
                  unselectedLabelColor: Colors.grey,
                  indicatorColor: Colors.green.shade700,
                  labelStyle: const TextStyle(fontFamily: 'Tajawal'),
                ),
                Expanded(
                  child: TabBarView(
                    children: [
                      _buildOverviewTab(animal),
                      _buildVideosTab(animal.videos),
                      _buildArticlesTab(animal.articles),
                      _buildFAQsTab(animal.faqs),
                      _buildNutritionTab(animal),
                      _buildGalleryTab(animal.galleryImages),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildOverviewTab(AnimalBreedingGroup animal) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            animal.name,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontFamily: 'Tajawal',
                  color: Colors.green.shade700,
                ),
          ),
          const SizedBox(height: 12),
          Text('الوصف: ${animal.description}',
              style: const TextStyle(fontFamily: 'Tajawal')),
          const SizedBox(height: 8),
          Text('الموطن: ${animal.habitat}',
              style: const TextStyle(fontFamily: 'Tajawal')),
          const SizedBox(height: 8),
          Text('النظام الغذائي: ${animal.diet}',
              style: const TextStyle(fontFamily: 'Tajawal')),
          const SizedBox(height: 8),
          Text('العمر الافتراضي: ${animal.lifespan}',
              style: const TextStyle(fontFamily: 'Tajawal')),
          const SizedBox(height: 8),
          Text('نصائح صحية: ${animal.healthTips}',
              style: const TextStyle(fontFamily: 'Tajawal')),
        ],
      ),
    );
  }

  Widget _buildVideosTab(List<Video> videos) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: videos.length,
      itemBuilder: (context, index) {
        final video = videos[index];
        return AnimationConfiguration.staggeredList(
          position: index,
          duration: const Duration(milliseconds: 600),
          child: SlideAnimation(
            verticalOffset: 50.0,
            child: FadeInAnimation(
              child: ListTile(
                leading: Icon(Icons.videocam, color: Colors.green.shade700),
                title: Text(video.title,
                    style: const TextStyle(fontFamily: 'Tajawal')),
                onTap: () {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                        content: Text('تشغيل ${video.title}',
                            style: const TextStyle(fontFamily: 'Tajawal'))),
                  );
                },
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildArticlesTab(List<Article> articles) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: articles.length,
      itemBuilder: (context, index) {
        final article = articles[index];
        return AnimationConfiguration.staggeredList(
          position: index,
          duration: const Duration(milliseconds: 600),
          child: SlideAnimation(
            verticalOffset: 50.0,
            child: FadeInAnimation(
              child: ExpansionTile(
                leading: Icon(Icons.article, color: Colors.green.shade700),
                title: Text(article.title,
                    style: const TextStyle(fontFamily: 'Tajawal')),
                tilePadding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                collapsedBackgroundColor: Colors.green.shade50,
                backgroundColor: Colors.green.shade100,
                children: [
                  Padding(
                    padding: const EdgeInsets.all(16),
                    child: Text(article.content,
                        style: const TextStyle(fontFamily: 'Tajawal')),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildFAQsTab(List<FAQ> faqs) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: faqs.length,
      itemBuilder: (context, index) {
        final faq = faqs[index];
        return AnimationConfiguration.staggeredList(
          position: index,
          duration: const Duration(milliseconds: 600),
          child: SlideAnimation(
            verticalOffset: 50.0,
            child: FadeInAnimation(
              child: ExpansionTile(
                leading:
                    Icon(Icons.question_answer, color: Colors.green.shade700),
                title: Text(faq.question,
                    style: const TextStyle(fontFamily: 'Tajawal')),
                tilePadding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                collapsedBackgroundColor: Colors.green.shade50,
                backgroundColor: Colors.green.shade100,
                children: [
                  Padding(
                    padding: const EdgeInsets.all(16),
                    child: Text(faq.answer,
                        style: const TextStyle(fontFamily: 'Tajawal')),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildNutritionTab(AnimalBreedingGroup animal) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'دليل التغذية لـ ${animal.name}',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontFamily: 'Tajawal',
                  color: Colors.green.shade700,
                ),
          ),
          const SizedBox(height: 12),
          Text(
            'النظام الغذائي: ${animal.diet}\n'
            'نصائح التغذية:\n'
            '- توفير الماء النظيف باستمرار\n'
            '- استخدام أعلاف عالية الجودة\n'
            '- تجنب الأطعمة الضارة\n'
            'جدول التطعيمات:\n'
            '- التطعيمات السنوية ضد الأمراض الشائعة\n'
            '- فحوصات دورية للتأكد من الصحة',
            style: const TextStyle(fontFamily: 'Tajawal'),
          ),
        ],
      ),
    );
  }

  Widget _buildGalleryTab(List<String> galleryImages) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      scrollDirection: Axis.horizontal,
      itemCount: galleryImages.length,
      itemBuilder: (context, index) {
        final imageUrl = galleryImages[index];
        return AnimationConfiguration.staggeredList(
          position: index,
          duration: const Duration(milliseconds: 600),
          child: SlideAnimation(
            horizontalOffset: 50.0,
            child: FadeInAnimation(
              child: Padding(
                padding: const EdgeInsets.only(right: 12.0),
                child: GestureDetector(
                  onTap: () {
                    Share.share('تحقق من هذه الصورة: $imageUrl');
                  },
                  child: CachedNetworkImage(
                    imageUrl: imageUrl,
                    width: 150,
                    height: 150,
                    fit: BoxFit.cover,
                    placeholder: (context, url) => _buildShimmerPlaceholder(),
                    errorWidget: (context, url, error) =>
                        const Icon(Icons.error),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  void _showAddAnimalDialog(BuildContext context) {
    final appState = Provider.of<AppState>(context, listen: false);
    final roleName = appState.currentUser?.role.name ?? 'guest';
    final userType = appState.userType ?? 'guest';

    if (!(roleName == 'hokama' || userType == 'hokama')) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title:
              const Text('غير مصرح', style: TextStyle(fontFamily: 'Tajawal')),
          content: const Text('هذه الميزة متاحة فقط للحكماء',
              style: TextStyle(fontFamily: 'Tajawal')),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child:
                  const Text('إغلاق', style: TextStyle(fontFamily: 'Tajawal')),
            ),
          ],
        ),
      );
      return;
    }

    final quantityController = TextEditingController();
    final priceController = TextEditingController();
    final addressController = TextEditingController();
    final contactController = TextEditingController();
    final detailsController = TextEditingController();
    final breedController = TextEditingController();
    String selectedAnimalType = animalTypes.first;
    String selectedHealthStatus = 'مطعم وصحي';
    String selectedValidity = 'أسبوع';
    List<String> imageUrls = [];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('إضافة حيوان',
            style:
                TextStyle(color: Colors.green.shade700, fontFamily: 'Tajawal')),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              DropdownButton<String>(
                value: selectedAnimalType,
                isExpanded: true,
                items: animalTypes.map((String animal) {
                  return DropdownMenuItem<String>(
                    value: animal,
                    child: Text(animal,
                        style: const TextStyle(fontFamily: 'Tajawal')),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  setState(() {
                    selectedAnimalType = newValue!;
                  });
                },
                dropdownColor: Colors.green.shade50,
              ),
              const SizedBox(height: 8),
              TextField(
                controller: quantityController,
                decoration: InputDecoration(
                  hintText: 'الكمية (رأس)',
                  border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.green.shade50,
                  hintStyle: const TextStyle(fontFamily: 'Tajawal'),
                ),
                keyboardType: TextInputType.number,
              ),
              const SizedBox(height: 8),
              TextField(
                controller: priceController,
                decoration: InputDecoration(
                  hintText: 'السعر (ريال)',
                  border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.green.shade50,
                  hintStyle: const TextStyle(fontFamily: 'Tajawal'),
                ),
                keyboardType: TextInputType.number,
              ),
              const SizedBox(height: 8),
              TextField(
                controller: addressController,
                decoration: InputDecoration(
                  hintText: 'العنوان',
                  border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.green.shade50,
                  hintStyle: const TextStyle(fontFamily: 'Tajawal'),
                  suffixIcon: IconButton(
                    icon: Icon(Icons.location_on, color: Colors.green.shade700),
                    onPressed: () async {
                      try {
                        Position position =
                            await Geolocator.getCurrentPosition();
                        addressController.text =
                            'موقع جغرافي: ${position.latitude}, ${position.longitude}';
                      } catch (e) {
                        Fluttertoast.showToast(
                          msg: 'خطأ في تحديد الموقع',
                          backgroundColor: Colors.red,
                        );
                      }
                    },
                  ),
                ),
              ),
              const SizedBox(height: 8),
              DropdownButton<String>(
                value: selectedValidity,
                isExpanded: true,
                items: ['أسبوع', 'أسبوعان', 'شهر'].map((String validity) {
                  return DropdownMenuItem<String>(
                    value: validity,
                    child: Text(validity,
                        style: const TextStyle(fontFamily: 'Tajawal')),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  setState(() {
                    selectedValidity = newValue!;
                  });
                },
                dropdownColor: Colors.green.shade50,
              ),
              const SizedBox(height: 8),
              TextField(
                controller: contactController,
                decoration: InputDecoration(
                  hintText: 'رقم التواصل',
                  border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.green.shade50,
                  hintStyle: const TextStyle(fontFamily: 'Tajawal'),
                ),
                keyboardType: TextInputType.phone,
              ),
              const SizedBox(height: 8),
              TextField(
                controller: breedController,
                decoration: InputDecoration(
                  hintText: 'السلالة',
                  border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.green.shade50,
                  hintStyle: const TextStyle(fontFamily: 'Tajawal'),
                ),
              ),
              const SizedBox(height: 8),
              DropdownButton<String>(
                value: selectedHealthStatus,
                isExpanded: true,
                items: ['مطعم وصحي', 'تحت العلاج', 'غير مطعم']
                    .map((String status) {
                  return DropdownMenuItem<String>(
                    value: status,
                    child: Text(status,
                        style: const TextStyle(fontFamily: 'Tajawal')),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  setState(() {
                    selectedHealthStatus = newValue!;
                  });
                },
                dropdownColor: Colors.green.shade50,
              ),
              const SizedBox(height: 8),
              TextField(
                controller: detailsController,
                decoration: InputDecoration(
                  hintText: 'تفاصيل الحيوان',
                  border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.green.shade50,
                  hintStyle: const TextStyle(fontFamily: 'Tajawal'),
                ),
                maxLines: 3,
              ),
              const SizedBox(height: 8),
              ElevatedButton(
                onPressed: () {
                  if (imageUrls.length < 4) {
                    imageUrls.addAll(List.generate(
                      4 - imageUrls.length,
                      (index) =>
                          'https://cdn.pixabay.com/photo/2019/05/08/21/21/cat-4189697_1280.jpg',
                    ));
                  }
                  Fluttertoast.showToast(msg: 'تم رفع ${imageUrls.length} صور');
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green.shade700,
                  shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12)),
                ),
                child: Text('رفع الصور (${imageUrls.length}/4)',
                    style: const TextStyle(fontFamily: 'Tajawal')),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء', style: TextStyle(fontFamily: 'Tajawal')),
          ),
          TextButton(
            onPressed: () {
              if (quantityController.text.isNotEmpty &&
                  priceController.text.isNotEmpty &&
                  addressController.text.isNotEmpty &&
                  contactController.text.isNotEmpty &&
                  detailsController.text.isNotEmpty &&
                  breedController.text.isNotEmpty &&
                  imageUrls.length >= 4) {
                setState(() {
                  animalItems.add(MarketItem(
                    id: DateTime.now().millisecondsSinceEpoch.toString(),
                    user: 'Current User',
                    type: selectedAnimalType,
                    imageUrls: imageUrls,
                    quantity: double.parse(quantityController.text),
                    price: double.parse(priceController.text),
                    address: addressController.text,
                    date: DateTime.now(),
                    validity: selectedValidity,
                    contactNumber: contactController.text,
                    details: detailsController.text,
                    breed: breedController.text,
                    healthStatus: selectedHealthStatus,
                  ));
                });
                Fluttertoast.showToast(
                  msg: 'تم إضافة الحيوان بنجاح',
                  backgroundColor: Colors.green.shade700,
                );
                Navigator.pop(context);
              } else {
                Fluttertoast.showToast(
                  msg: 'يرجى ملء جميع الحقول ورفع 4 صور',
                  backgroundColor: Colors.red,
                );
              }
            },
            child: const Text('إضافة', style: TextStyle(fontFamily: 'Tajawal')),
          ),
        ],
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        backgroundColor: Colors.green.shade50,
      ),
    );
  }

  void _showAddLandDialog(BuildContext context) {
    final appState = Provider.of<AppState>(context, listen: false);
    final roleName = appState.currentUser?.role.name ?? 'guest';
    final userType = appState.userType ?? 'guest';

    if (!(roleName == 'hokama' || userType == 'hokama')) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title:
              const Text('غير مصرح', style: TextStyle(fontFamily: 'Tajawal')),
          content: const Text('هذه الميزة متاحة فقط للحكماء',
              style: TextStyle(fontFamily: 'Tajawal')),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child:
                  const Text('إغلاق', style: TextStyle(fontFamily: 'Tajawal')),
            ),
          ],
        ),
      );
      return;
    }

    final quantityController = TextEditingController();
    final priceController = TextEditingController();
    final addressController = TextEditingController();
    final contactController = TextEditingController();
    final descriptionController = TextEditingController();
    final economicActivityDetailsController = TextEditingController();
    final structuresDetailsController = TextEditingController();
    String selectedUnit = 'متر مربع';
    String selectedApplicantStatus = 'مالك';
    String selectedRegistrationType = 'شهر عقاري';
    String selectedEconomicActivity = 'نعم';
    String selectedWaterSource = 'آبار';
    String selectedTerrainType = terrainTypes.first;
    String selectedElectricitySource = 'خاص';
    String selectedStructures = 'نعم';
    String selectedSaleOrRent = 'للبيع';
    List<String> imageUrls = [];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('إضافة أرض',
            style:
                TextStyle(color: Colors.green.shade700, fontFamily: 'Tajawal')),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              DropdownButton<String>(
                value: selectedSaleOrRent,
                isExpanded: true,
                items: ['للبيع', 'للإيجار'].map((String value) {
                  return DropdownMenuItem<String>(
                    value: value,
                    child: Text(value,
                        style: const TextStyle(fontFamily: 'Tajawal')),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  setState(() {
                    selectedSaleOrRent = newValue!;
                  });
                },
                dropdownColor: Colors.green.shade50,
              ),
              const SizedBox(height: 8),
              DropdownButton<String>(
                value: selectedUnit,
                isExpanded: true,
                items: ['فدان', 'قيراط', 'متر مربع'].map((String unit) {
                  return DropdownMenuItem<String>(
                    value: unit,
                    child: Text(unit,
                        style: const TextStyle(fontFamily: 'Tajawal')),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  setState(() {
                    selectedUnit = newValue!;
                  });
                },
                dropdownColor: Colors.green.shade50,
              ),
              const SizedBox(height: 8),
              TextField(
                controller: quantityController,
                decoration: InputDecoration(
                  hintText: 'المساحة',
                  border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.green.shade50,
                  hintStyle: const TextStyle(fontFamily: 'Tajawal'),
                ),
                keyboardType: TextInputType.number,
              ),
              const SizedBox(height: 8),
              TextField(
                controller: priceController,
                decoration: InputDecoration(
                  hintText: 'السعر (ريال)',
                  border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.green.shade50,
                  hintStyle: const TextStyle(fontFamily: 'Tajawal'),
                ),
                keyboardType: TextInputType.number,
              ),
              const SizedBox(height: 8),
              TextField(
                controller: addressController,
                decoration: InputDecoration(
                  hintText: 'العنوان',
                  border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.green.shade50,
                  hintStyle: const TextStyle(fontFamily: 'Tajawal'),
                  suffixIcon: IconButton(
                    icon: Icon(Icons.location_on, color: Colors.green.shade700),
                    onPressed: () async {
                      try {
                        Position position =
                            await Geolocator.getCurrentPosition();
                        addressController.text =
                            'موقع جغرافي: ${position.latitude}, ${position.longitude}';
                      } catch (e) {
                        Fluttertoast.showToast(
                          msg: 'خطأ في تحديد الموقع',
                          backgroundColor: Colors.red,
                        );
                      }
                    },
                  ),
                ),
              ),
              const SizedBox(height: 8),
              TextField(
                controller: contactController,
                decoration: InputDecoration(
                  hintText: 'رقم التواصل',
                  border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.green.shade50,
                  hintStyle: const TextStyle(fontFamily: 'Tajawal'),
                ),
                keyboardType: TextInputType.phone,
              ),
              const SizedBox(height: 8),
              DropdownButton<String>(
                value: selectedApplicantStatus,
                isExpanded: true,
                items: ['مالك', 'وكيل'].map((String status) {
                  return DropdownMenuItem<String>(
                    value: status,
                    child: Text(status,
                        style: const TextStyle(fontFamily: 'Tajawal')),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  setState(() {
                    selectedApplicantStatus = newValue!;
                  });
                },
                dropdownColor: Colors.green.shade50,
              ),
              const SizedBox(height: 8),
              DropdownButton<String>(
                value: selectedRegistrationType,
                isExpanded: true,
                items: [
                  'شهر عقاري',
                  'أملاك دولة',
                  'هيئة تعمير',
                  'جمعيات زراعية',
                  'شركات'
                ].map((String type) {
                  return DropdownMenuItem<String>(
                    value: type,
                    child: Text(type,
                        style: const TextStyle(fontFamily: 'Tajawal')),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  setState(() {
                    selectedRegistrationType = newValue!;
                  });
                },
                dropdownColor: Colors.green.shade50,
              ),
              const SizedBox(height: 8),
              TextField(
                controller: descriptionController,
                decoration: InputDecoration(
                  hintText: 'الوصف',
                  border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.green.shade50,
                  hintStyle: const TextStyle(fontFamily: 'Tajawal'),
                ),
                maxLines: 3,
              ),
              const SizedBox(height: 8),
              DropdownButton<String>(
                value: selectedEconomicActivity,
                isExpanded: true,
                items: ['نعم', 'لا'].map((String value) {
                  return DropdownMenuItem<String>(
                    value: value,
                    child: Text(value,
                        style: const TextStyle(fontFamily: 'Tajawal')),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  setState(() {
                    selectedEconomicActivity = newValue!;
                  });
                },
                dropdownColor: Colors.green.shade50,
              ),
              if (selectedEconomicActivity == 'نعم') ...[
                const SizedBox(height: 8),
                TextField(
                  controller: economicActivityDetailsController,
                  decoration: InputDecoration(
                    hintText: 'تفاصيل الأنشطة الاقتصادية',
                    border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12)),
                    filled: true,
                    fillColor: Colors.green.shade50,
                    hintStyle: const TextStyle(fontFamily: 'Tajawal'),
                  ),
                  maxLines: 2,
                ),
              ],
              const SizedBox(height: 8),
              DropdownButton<String>(
                value: selectedWaterSource,
                isExpanded: true,
                items: ['آبار', 'بحاري'].map((String source) {
                  return DropdownMenuItem<String>(
                    value: source,
                    child: Text(source,
                        style: const TextStyle(fontFamily: 'Tajawal')),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  setState(() {
                    selectedWaterSource = newValue!;
                  });
                },
                dropdownColor: Colors.green.shade50,
              ),
              const SizedBox(height: 8),
              DropdownButton<String>(
                value: selectedTerrainType,
                isExpanded: true,
                items: terrainTypes.map((String terrain) {
                  return DropdownMenuItem<String>(
                    value: terrain,
                    child: Text(terrain,
                        style: const TextStyle(fontFamily: 'Tajawal')),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  setState(() {
                    selectedTerrainType = newValue!;
                  });
                },
                dropdownColor: Colors.green.shade50,
              ),
              const SizedBox(height: 8),
              DropdownButton<String>(
                value: selectedElectricitySource,
                isExpanded: true,
                items:
                    ['خاص', 'عام', 'طاقة شمسية', 'ديزل'].map((String source) {
                  return DropdownMenuItem<String>(
                    value: source,
                    child: Text(source,
                        style: const TextStyle(fontFamily: 'Tajawal')),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  setState(() {
                    selectedElectricitySource = newValue!;
                  });
                },
                dropdownColor: Colors.green.shade50,
              ),
              const SizedBox(height: 8),
              DropdownButton<String>(
                value: selectedStructures,
                isExpanded: true,
                items: ['نعم', 'لا'].map((String value) {
                  return DropdownMenuItem<String>(
                    value: value,
                    child: Text(value,
                        style: const TextStyle(fontFamily: 'Tajawal')),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  setState(() {
                    selectedStructures = newValue!;
                  });
                },
                dropdownColor: Colors.green.shade50,
              ),
              if (selectedStructures == 'نعم') ...[
                const SizedBox(height: 8),
                TextField(
                  controller: structuresDetailsController,
                  decoration: InputDecoration(
                    hintText: 'تفاصيل المنشآت',
                    border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12)),
                    filled: true,
                    fillColor: Colors.green.shade50,
                    hintStyle: const TextStyle(fontFamily: 'Tajawal'),
                  ),
                  maxLines: 2,
                ),
              ],
              const SizedBox(height: 8),
              ElevatedButton(
                onPressed: () {
                  if (imageUrls.length < 4) {
                    imageUrls.addAll(List.generate(
                      4 - imageUrls.length,
                      (index) =>
                          'https://cdn.pixabay.com/photo/2017/03/28/12/11/field-2182088_1280.jpg',
                    ));
                  }
                  Fluttertoast.showToast(msg: 'تم رفع ${imageUrls.length} صور');
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green.shade700,
                  shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12)),
                ),
                child: Text('رفع الصور (${imageUrls.length}/4)',
                    style: const TextStyle(fontFamily: 'Tajawal')),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء', style: TextStyle(fontFamily: 'Tajawal')),
          ),
          TextButton(
            onPressed: () {
              if (quantityController.text.isNotEmpty &&
                  priceController.text.isNotEmpty &&
                  addressController.text.isNotEmpty &&
                  contactController.text.isNotEmpty &&
                  descriptionController.text.isNotEmpty &&
                  imageUrls.length >= 4) {
                setState(() {
                  landItems.add(MarketItem(
                    id: DateTime.now().millisecondsSinceEpoch.toString(),
                    user: 'Current User',
                    type: selectedSaleOrRent,
                    imageUrls: imageUrls,
                    quantity: double.parse(quantityController.text),
                    price: double.parse(priceController.text),
                    address: addressController.text,
                    date: DateTime.now(),
                    validity: 'شهر',
                    contactNumber: contactController.text,
                    details: descriptionController.text,
                    applicantStatus: selectedApplicantStatus,
                    registrationType: selectedRegistrationType,
                    description: descriptionController.text,
                    economicActivity: selectedEconomicActivity,
                    economicActivityDetails:
                        economicActivityDetailsController.text,
                    waterSource: selectedWaterSource,
                    terrainType: selectedTerrainType,
                    electricitySource: selectedElectricitySource,
                    structures: selectedStructures,
                    structuresDetails: structuresDetailsController.text,
                  ));
                });
                Fluttertoast.showToast(
                  msg: 'تم إضافة الأرض بنجاح',
                  backgroundColor: Colors.green.shade700,
                );
                Navigator.pop(context);
              } else {
                Fluttertoast.showToast(
                  msg: 'يرجى ملء جميع الحقول ورفع 4 صور',
                  backgroundColor: Colors.red,
                );
              }
            },
            child: const Text('إضافة', style: TextStyle(fontFamily: 'Tajawal')),
          ),
        ],
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        backgroundColor: Colors.green.shade50,
      ),
    );
  }

  void _showAddSupplyDialog(BuildContext context) {
    final appState = Provider.of<AppState>(context, listen: false);
    final roleName = appState.currentUser?.role.name ?? 'guest';
    final userType = appState.userType ?? 'guest';

    if (!(roleName == 'hokama' || userType == 'hokama')) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title:
              const Text('غير مصرح', style: TextStyle(fontFamily: 'Tajawal')),
          content: const Text('هذه الميزة متاحة فقط للحكماء',
              style: TextStyle(fontFamily: 'Tajawal')),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child:
                  const Text('إغلاق', style: TextStyle(fontFamily: 'Tajawal')),
            ),
          ],
        ),
      );
      return;
    }

    final quantityController = TextEditingController();
    final priceController = TextEditingController();
    final addressController = TextEditingController();
    final contactController = TextEditingController();
    final detailsController = TextEditingController();
    String selectedSupplyType = supplyTypes.first;
    String selectedValidity = 'أسبوع';
    List<String> imageUrls = [];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('إضافة مستلزمات بيطرية',
            style:
                TextStyle(color: Colors.green.shade700, fontFamily: 'Tajawal')),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              DropdownButton<String>(
                value: selectedSupplyType,
                isExpanded: true,
                items: supplyTypes.map((String type) {
                  return DropdownMenuItem<String>(
                    value: type,
                    child: Text(type,
                        style: const TextStyle(fontFamily: 'Tajawal')),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  setState(() {
                    selectedSupplyType = newValue!;
                  });
                },
                dropdownColor: Colors.green.shade50,
              ),
              const SizedBox(height: 8),
              TextField(
                controller: quantityController,
                decoration: InputDecoration(
                  hintText: 'الكمية',
                  border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.green.shade50,
                  hintStyle: const TextStyle(fontFamily: 'Tajawal'),
                ),
                keyboardType: TextInputType.number,
              ),
              const SizedBox(height: 8),
              TextField(
                controller: priceController,
                decoration: InputDecoration(
                  hintText: 'السعر (ريال)',
                  border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.green.shade50,
                  hintStyle: const TextStyle(fontFamily: 'Tajawal'),
                ),
                keyboardType: TextInputType.number,
              ),
              const SizedBox(height: 8),
              TextField(
                controller: addressController,
                decoration: InputDecoration(
                  hintText: 'العنوان',
                  border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.green.shade50,
                  hintStyle: const TextStyle(fontFamily: 'Tajawal'),
                  suffixIcon: IconButton(
                    icon: Icon(Icons.location_on, color: Colors.green.shade700),
                    onPressed: () async {
                      try {
                        Position position =
                            await Geolocator.getCurrentPosition();
                        addressController.text =
                            'موقع جغرافي: ${position.latitude}, ${position.longitude}';
                      } catch (e) {
                        Fluttertoast.showToast(
                          msg: 'خطأ في تحديد الموقع',
                          backgroundColor: Colors.red,
                        );
                      }
                    },
                  ),
                ),
              ),
              const SizedBox(height: 8),
              DropdownButton<String>(
                value: selectedValidity,
                isExpanded: true,
                items: ['أسبوع', 'أسبوعان', 'شهر'].map((String validity) {
                  return DropdownMenuItem<String>(
                    value: validity,
                    child: Text(validity,
                        style: const TextStyle(fontFamily: 'Tajawal')),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  setState(() {
                    selectedValidity = newValue!;
                  });
                },
                dropdownColor: Colors.green.shade50,
              ),
              const SizedBox(height: 8),
              TextField(
                controller: contactController,
                decoration: InputDecoration(
                  hintText: 'رقم التواصل',
                  border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.green.shade50,
                  hintStyle: const TextStyle(fontFamily: 'Tajawal'),
                ),
                keyboardType: TextInputType.phone,
              ),
              const SizedBox(height: 8),
              TextField(
                controller: detailsController,
                decoration: InputDecoration(
                  hintText: 'تفاصيل المستلزمات',
                  border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.green.shade50,
                  hintStyle: const TextStyle(fontFamily: 'Tajawal'),
                ),
                maxLines: 3,
              ),
              const SizedBox(height: 8),
              ElevatedButton(
                onPressed: () {
                  if (imageUrls.length < 4) {
                    imageUrls.addAll(List.generate(
                      4 - imageUrls.length,
                      (index) =>
                          'https://cdn.pixabay.com/photo/2017/06/29/12/06/hay-2453616_1280.jpg',
                    ));
                  }
                  Fluttertoast.showToast(msg: 'تم رفع ${imageUrls.length} صور');
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green.shade700,
                  shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12)),
                ),
                child: Text('رفع الصور (${imageUrls.length}/4)',
                    style: const TextStyle(fontFamily: 'Tajawal')),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء', style: TextStyle(fontFamily: 'Tajawal')),
          ),
          TextButton(
            onPressed: () {
              if (quantityController.text.isNotEmpty &&
                  priceController.text.isNotEmpty &&
                  addressController.text.isNotEmpty &&
                  contactController.text.isNotEmpty &&
                  detailsController.text.isNotEmpty &&
                  imageUrls.length >= 4) {
                setState(() {
                  supplyItems.add(MarketItem(
                    id: DateTime.now().millisecondsSinceEpoch.toString(),
                    user: 'Current User',
                    type: selectedSupplyType,
                    imageUrls: imageUrls,
                    quantity: double.parse(quantityController.text),
                    price: double.parse(priceController.text),
                    address: addressController.text,
                    date: DateTime.now(),
                    validity: selectedValidity,
                    contactNumber: contactController.text,
                    details: detailsController.text,
                  ));
                });
                Fluttertoast.showToast(
                  msg: 'تم إضافة المستلزمات بنجاح',
                  backgroundColor: Colors.green.shade700,
                );
                Navigator.pop(context);
              } else {
                Fluttertoast.showToast(
                  msg: 'يرجى ملء جميع الحقول ورفع 4 صور',
                  backgroundColor: Colors.red,
                );
              }
            },
            child: const Text('إضافة', style: TextStyle(fontFamily: 'Tajawal')),
          ),
        ],
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        backgroundColor: Colors.green.shade50,
      ),
    );
  }

  void _showSearchDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('بحث',
            style:
                TextStyle(color: Colors.green.shade700, fontFamily: 'Tajawal')),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'ابحث عن حيوان، أرض، أو مستلزمات...',
                border:
                    OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                filled: true,
                fillColor: Colors.green.shade50,
                hintStyle: const TextStyle(fontFamily: 'Tajawal'),
              ),
            ),
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: () {
                if (_searchController.text.isNotEmpty) {
                  Fluttertoast.showToast(
                    msg: 'جاري البحث عن: ${_searchController.text}',
                    backgroundColor: Colors.green.shade700,
                  );
                  Navigator.pop(context);
                } else {
                  Fluttertoast.showToast(
                    msg: 'يرجى إدخال نص للبحث',
                    backgroundColor: Colors.red,
                  );
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green.shade700,
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12)),
              ),
              child: const Text('بحث', style: TextStyle(fontFamily: 'Tajawal')),
            ),
          ],
        ),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        backgroundColor: Colors.green.shade50,
      ),
    );
  }

  void _showQuickActions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: Colors.green.shade50,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: Icon(Icons.pets, color: Colors.green.shade700),
              title: const Text('إضافة حيوان',
                  style: TextStyle(fontFamily: 'Tajawal')),
              onTap: () {
                Navigator.pop(context);
                _showAddAnimalDialog(context);
              },
            ),
            ListTile(
              leading: Icon(Icons.landscape, color: Colors.green.shade700),
              title: const Text('إضافة أرض',
                  style: TextStyle(fontFamily: 'Tajawal')),
              onTap: () {
                Navigator.pop(context);
                _showAddLandDialog(context);
              },
            ),
            ListTile(
              leading: Icon(Icons.local_pharmacy, color: Colors.green.shade700),
              title: const Text('إضافة مستلزمات',
                  style: TextStyle(fontFamily: 'Tajawal')),
              onTap: () {
                Navigator.pop(context);
                _showAddSupplyDialog(context);
              },
            ),
            ListTile(
              leading:
                  Icon(Icons.medical_services, color: Colors.green.shade700),
              title: const Text('فحص صحة الحيوان',
                  style: TextStyle(fontFamily: 'Tajawal')),
              onTap: () {
                Navigator.pop(context);
                _showHealthCheckDialog(context);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showHealthCheckDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('فحص صحة الحيوان',
            style:
                TextStyle(color: Colors.green.shade700, fontFamily: 'Tajawal')),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: _healthCheckController,
              decoration: InputDecoration(
                hintText: 'أدخل أعراض الحيوان...',
                border:
                    OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                filled: true,
                fillColor: Colors.green.shade50,
                hintStyle: const TextStyle(fontFamily: 'Tajawal'),
              ),
              maxLines: 3,
            ),
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: () {
                if (_healthCheckController.text.isNotEmpty) {
                  Fluttertoast.showToast(
                    msg: 'تم إرسال الأعراض للتحليل',
                    backgroundColor: Colors.green.shade700,
                  );
                  Navigator.pop(context);
                } else {
                  Fluttertoast.showToast(
                    msg: 'يرجى إدخال الأعراض',
                    backgroundColor: Colors.red,
                  );
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green.shade700,
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12)),
              ),
              child:
                  const Text('تحليل', style: TextStyle(fontFamily: 'Tajawal')),
            ),
          ],
        ),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        backgroundColor: Colors.green.shade50,
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
    required Color color,
  }) {
    return Expanded(
      child: TextButton.icon(
        onPressed: onPressed,
        icon: Icon(icon, color: color),
        label: Text(label, style: TextStyle(color: Colors.grey[700])),
        style: TextButton.styleFrom(
          padding: const EdgeInsets.symmetric(vertical: 8.0),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8.0),
          ),
          backgroundColor: color.withOpacity(0.1),
        ),
      ),
    );
  }

  Widget _buildPostInput() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const CircleAvatar(
                  child: Icon(Icons.person),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: TextField(
                    controller: _postController,
                    maxLines: 3,
                    decoration: InputDecoration(
                      hintText: 'ما الذي تفكر به؟',
                      border: InputBorder.none,
                      hintStyle: TextStyle(color: Colors.grey[500]),
                    ),
                    onSubmitted: (value) {
                      if (_postController.text.isNotEmpty) {
                        _addPost();
                      }
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                _buildActionButton(
                  icon: Icons.image,
                  label: 'صورة',
                  onPressed: _pickImage,
                  color: Colors.green.shade700,
                ),
                const SizedBox(width: 8),
                _buildActionButton(
                  icon: Icons.videocam,
                  label: 'فيديو',
                  onPressed: _pickVideo,
                  color: Colors.red.shade700,
                ),
                const SizedBox(width: 8),
                _buildActionButton(
                  icon: Icons.article,
                  label: 'قصة',
                  onPressed: _createStory,
                  color: Colors.purple.shade700,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPostsList() {
    if (posts.isEmpty) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.all(16.0),
          child: Text('لا توجد منشورات بعد. كن أول من ينشر!'),
        ),
      );
    }

    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: posts.length,
      itemBuilder: (context, index) {
        final post = posts[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 16.0),
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ListTile(
                leading: const CircleAvatar(
                  child: Icon(Icons.person),
                ),
                title: Text(post.user),
                subtitle: const Text('منذ لحظات'),
              ),
              Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
                child: Text(post.content),
              ),
              if (post.imageUrl != null)
                ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Image.network(
                    post.imageUrl!,
                    width: double.infinity,
                    height: 200,
                    fit: BoxFit.cover,
                  ),
                ),
              Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    TextButton.icon(
                      onPressed: () {
                        setState(() {
                          posts[index] = Post(
                            user: post.user,
                            content: post.content,
                            imageUrl: post.imageUrl,
                            likes: post.likes + 1,
                            comments: post.comments,
                          );
                        });
                      },
                      icon: const Icon(Icons.thumb_up, size: 18),
                      label: Text('${post.likes}'),
                      style: TextButton.styleFrom(
                          foregroundColor: Colors.grey[600]),
                    ),
                    TextButton.icon(
                      onPressed: () {
                        // Show comments
                      },
                      icon: const Icon(Icons.comment, size: 18),
                      label: Text('${post.comments}'),
                      style: TextButton.styleFrom(
                          foregroundColor: Colors.grey[600]),
                    ),
                    TextButton.icon(
                      onPressed: () {
                        Share.share(
                            '${post.content}\n\n${post.imageUrl ?? ''}');
                      },
                      icon: const Icon(Icons.share, size: 18),
                      label: const Text('مشاركة'),
                      style: TextButton.styleFrom(
                          foregroundColor: Colors.grey[600]),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Future<void> _pickImage() async {
    try {
      final XFile? image = await _picker.pickImage(source: ImageSource.gallery);
      if (image != null) {
        Fluttertoast.showToast(
          msg: 'تم اختيار صورة: ${image.name}',
          backgroundColor: Colors.green.shade700,
        );
      }
    } catch (e) {
      Fluttertoast.showToast(
        msg: 'حدث خطأ أثناء اختيار الصورة',
        backgroundColor: Colors.red,
      );
    }
  }

  Future<void> _pickVideo() async {
    try {
      final connectivityResult = await Connectivity().checkConnectivity();
      if (connectivityResult == ConnectivityResult.none) {
        Fluttertoast.showToast(
          msg: 'لا يوجد اتصال بالإنترنت',
          backgroundColor: Colors.red,
        );
        return;
      }

      final XFile? video = await _picker.pickVideo(source: ImageSource.gallery);
      if (video != null) {
        Fluttertoast.showToast(
          msg: 'تم اختيار فيديو: ${video.name}',
          backgroundColor: Colors.green.shade700,
        );
      }
    } catch (e) {
      Fluttertoast.showToast(
        msg: 'حدث خطأ أثناء اختيار الفيديو',
        backgroundColor: Colors.red,
      );
    }
  }

  Future<void> _createStory() async {
    try {
      final XFile? image = await _picker.pickImage(source: ImageSource.camera);
      if (image != null) {
        Fluttertoast.showToast(
          msg: 'تم إنشاء قصة جديدة',
          backgroundColor: Colors.green.shade700,
        );
      }
    } catch (e) {
      Fluttertoast.showToast(
        msg: 'حدث خطأ أثناء إنشاء القصة',
        backgroundColor: Colors.red,
      );
    }
  }

  void _addPost() {
    if (_postController.text.trim().isEmpty) return;

    setState(() {
      posts.insert(
        0,
        Post(
          user: 'أنا',
          content: _postController.text,
          imageUrl: null,
          likes: 0,
          comments: 0,
        ),
      );
      _postController.clear();
      _confettiController.play();
      Fluttertoast.showToast(
        msg: 'تم نشر المنشور بنجاح',
        backgroundColor: Colors.green.shade700,
      );
    });
  }

  void _showAddAnimalTypeDialog(BuildContext context) {
    final nameController = TextEditingController();
    final descriptionController = TextEditingController();
    final habitatController = TextEditingController();
    final dietController = TextEditingController();
    final lifespanController = TextEditingController();
    final healthTipsController = TextEditingController();
    final imageUrlController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('إضافة نوع حيوان جديد',
            style: TextStyle(color: Colors.blue.shade700)),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: nameController,
                decoration: InputDecoration(
                  labelText: 'اسم النوع',
                  border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.blue.shade50,
                ),
              ),
              const SizedBox(height: 8),
              TextField(
                controller: descriptionController,
                decoration: InputDecoration(
                  labelText: 'الوصف',
                  border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.blue.shade50,
                ),
                maxLines: 2,
              ),
              const SizedBox(height: 8),
              TextField(
                controller: habitatController,
                decoration: InputDecoration(
                  labelText: 'الموطن',
                  border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.blue.shade50,
                ),
              ),
              const SizedBox(height: 8),
              TextField(
                controller: dietController,
                decoration: InputDecoration(
                  labelText: 'النظام الغذائي',
                  border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.blue.shade50,
                ),
              ),
              const SizedBox(height: 8),
              TextField(
                controller: lifespanController,
                decoration: InputDecoration(
                  labelText: 'متوسط العمر',
                  border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.blue.shade50,
                ),
              ),
              const SizedBox(height: 8),
              TextField(
                controller: healthTipsController,
                decoration: InputDecoration(
                  labelText: 'نصائح صحية',
                  border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.blue.shade50,
                ),
                maxLines: 2,
              ),
              const SizedBox(height: 8),
              TextField(
                controller: imageUrlController,
                decoration: InputDecoration(
                  labelText: 'رابط الصورة',
                  border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12)),
                  filled: true,
                  fillColor: Colors.blue.shade50,
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء', style: TextStyle(color: Colors.grey)),
          ),
          ElevatedButton(
            onPressed: () {
              if (nameController.text.isNotEmpty &&
                  descriptionController.text.isNotEmpty) {
                final newAnimal = AnimalBreedingGroup(
                  id: DateTime.now().millisecondsSinceEpoch.toString(),
                  name: nameController.text,
                  description: descriptionController.text,
                  imageUrl: imageUrlController.text.isNotEmpty
                      ? imageUrlController.text
                      : 'https://cdn-icons-png.flaticon.com/512/616/616408.png',
                  habitat: habitatController.text,
                  diet: dietController.text,
                  lifespan: lifespanController.text,
                  healthTips: healthTipsController.text,
                  videos: [],
                  articles: [],
                  faqs: [],
                  galleryImages: [],
                );

                setState(() {
                  animals.insert(0, newAnimal);
                });

                Fluttertoast.showToast(
                  msg: 'تمت إضافة ${nameController.text} بنجاح',
                  backgroundColor: Colors.green.shade700,
                );

                Navigator.pop(context);
              } else {
                Fluttertoast.showToast(
                  msg: 'الرجاء إدخال الاسم والوصف على الأقل',
                  backgroundColor: Colors.red,
                );
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue.shade700,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: const Text('إضافة'),
          ),
        ],
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        backgroundColor: Colors.blue.shade50,
      ),
    );
  }
}
