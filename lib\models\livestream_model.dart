// نموذج البث المباشر
class Livestream {
  final String id;
  final String user;
  final String title;
  final String? description;
  final String streamUrl;
  final bool isLive;
  final String startedAt;
  final String? endedAt;

  Livestream({
    required this.id,
    required this.user,
    required this.title,
    this.description,
    required this.streamUrl,
    this.isLive = true,
    required this.startedAt,
    this.endedAt,
  });

  factory Livestream.fromJson(Map<String, dynamic> json) {
    return Livestream(
      id: json['_id'] ?? '',
      user: json['user'] ?? '',
      title: json['title'] ?? '',
      description: json['description'],
      streamUrl: json['streamUrl'] ?? '',
      isLive: json['isLive'] ?? true,
      startedAt: json['startedAt'] ?? '',
      endedAt: json['endedAt'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user': user,
      'title': title,
      'description': description,
      'streamUrl': streamUrl,
      'isLive': isLive,
      'startedAt': startedAt,
      'endedAt': endedAt,
    };
  }
}
