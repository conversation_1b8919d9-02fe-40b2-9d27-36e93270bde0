import 'package:flutter/foundation.dart';

class Config {
  // عنوان API الأساسي
  static String get apiBaseUrl {
    if (kReleaseMode) {
      // عنوان API للإنتاج
      return 'https://api.yourdomain.com/api';
    } else {
      // عنوان API للتطوير
      return 'http://197.63.231.187:3000/api';
    }
  }

  // عنوان Socket.IO
  static String get socketUrl {
    if (kReleaseMode) {
      // عنوان Socket للإنتاج
      return 'https://api.yourdomain.com';
    } else {
      // عنوان Socket للتطوير
      return 'http://197.63.231.187:3000';
    }
  }

  // إعدادات المهلة الزمنية
  static const int receiveTimeout = 15000; // 15 ثانية
  static const int connectTimeout = 15000; // 15 ثانية

  // إعدادات التقسيم للصفحات
  static const int itemsPerPage = 10;

  // معلومات الإصدار
  static const String appVersion = '1.0.0';
  
  // أدوار المستخدمين
  static const String roleHokama = 'hokama';
  static const String roleUser = 'user';
  static const String roleAdmin = 'admin';
  
  // أنواع المحتوى المقيدة
  static const List<String> restrictedContentTypes = [
    'birds',
    'plants',
    'animals',
    'fish',
    'food_industries',
    'maintenance',
    'diseases',
    'cities',
    'hobbies',
    'professions',
    'courses'
  ];
}