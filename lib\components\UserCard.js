import React from "react";

const UserCard = ({ user, onFollow, onUnfollow }) => (
  <div style={{ border: '1px solid #ddd', borderRadius: 8, padding: 12, marginBottom: 8, display: 'flex', alignItems: 'center' }}>
    <img src={user.avatarUrl} alt={user.name} style={{ width: 40, height: 40, borderRadius: '50%', marginRight: 12 }} />
    <div style={{ flex: 1 }}>
      <div>{user.name}</div>
      <div style={{ fontSize: 12, color: '#888' }}>{user.email}</div>
    </div>
    {user.isFollowing ? (
      <button onClick={() => onUnfollow(user._id)}>إلغاء متابعة</button>
    ) : (
      <button onClick={() => onFollow(user._id)}>متابعة</button>
    )}
  </div>
);
export default UserCard;
