const Group = require('../models/Groupmodel');
const User = require('../models/user.model');
const Task = require('../models/Taskmodel');
const ErrorResponse = require('../utils/errorResponse');
const asyncHandler = require('../middleware/asyncmiddleware');
const path = require('path');
const fs = require('fs');
const multer = require('multer');

// إعداد تخزين الملفات
const storage = multer.diskStorage({
  destination: function(req, file, cb) {
    const uploadPath = './public/uploads/groups';
    if (!fs.existsSync(uploadPath)) {
      fs.mkdirSync(uploadPath, { recursive: true });
    }
    cb(null, uploadPath);
  },
  filename: function(req, file, cb) {
    cb(null, `group_${Date.now()}${path.extname(file.originalname)}`);
  }
});

// فلترة الملفات
const fileFilter = (req, file, cb) => {
  // قبول الصور والفيديوهات فقط
  if (file.mimetype.startsWith('image') || file.mimetype.startsWith('video')) {
    cb(null, true);
  } else {
    cb(new ErrorResponse('يرجى تحميل صورة أو فيديو فقط', 400), false);
  }
};

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 10000000 // 10 ميجابايت
  },
  fileFilter: fileFilter
});

// @desc    الحصول على جميع المجموعات
// @route   GET /api/groups
// @access  Private
exports.getGroups = asyncHandler(async (req, res, next) => {
  const groups = await Group.find()
    .populate({
      path: 'members',
      select: 'name avatarUrl'
    })
    .populate({
      path: 'creatorId',
      select: 'name avatarUrl'
    });

  res.status(200).json({
    success: true,
    count: groups.length,
    data: groups
  });
});

// @desc    إنشاء مجموعة جديدة
// @route   POST /api/groups
// @access  Private
exports.createGroup = asyncHandler(async (req, res, next) => {
  // التعامل مع تحميل الصورة
  const uploadMiddleware = upload.single('groupImage');

  uploadMiddleware(req, res, async (err) => {
    if (err) {
      return next(new ErrorResponse(`خطأ في تحميل الملف: ${err.message}`, 400));
    }

    // إنشاء المجموعة
    const groupData = {
      name: req.body.name,
      description: req.body.description,
      creatorId: req.user.id,
      type: req.body.type || 'other',
      members: [req.user.id], // إضافة المنشئ كعضو
      videoUrl: req.body.videoUrl || ''
    };

    // إضافة صورة المجموعة إذا تم تحميلها
    if (req.file) {
      groupData.groupImage = `/uploads/groups/${req.file.filename}`;
    }

    // إضافة الوسائط إذا تم توفيرها
    if (req.body.media) {
      try {
        groupData.media = JSON.parse(req.body.media);
      } catch (e) {
        return next(new ErrorResponse('تنسيق الوسائط غير صالح', 400));
      }
    }

    const group = await Group.create(groupData);

    // الحصول على المجموعة مع معلومات الأعضاء
    const populatedGroup = await Group.findById(group._id)
      .populate({
        path: 'members',
        select: 'name avatarUrl'
      })
      .populate({
        path: 'creatorId',
        select: 'name avatarUrl'
      });

    res.status(201).json({
      success: true,
      data: populatedGroup
    });
  });
});

// @desc    الحصول على مجموعة واحدة
// @route   GET /api/groups/:id
// @access  Private
exports.getGroup = asyncHandler(async (req, res, next) => {
  const group = await Group.findById(req.params.id)
    .populate({
      path: 'members',
      select: 'name avatarUrl'
    })
    .populate({
      path: 'creatorId',
      select: 'name avatarUrl'
    });

  if (!group) {
    return next(new ErrorResponse(`لم يتم العثور على مجموعة بالمعرف ${req.params.id}`, 404));
  }

  res.status(200).json({
    success: true,
    data: group
  });
});

// @desc    تحديث مجموعة
// @route   PUT /api/groups/:id
// @access  Private
exports.updateGroup = asyncHandler(async (req, res, next) => {
  let group = await Group.findById(req.params.id);

  if (!group) {
    return next(new ErrorResponse(`لم يتم العثور على مجموعة بالمعرف ${req.params.id}`, 404));
  }

  // التحقق من ملكية المجموعة
  if (group.creatorId.toString() !== req.user.id && req.user.role !== 'admin') {
    return next(new ErrorResponse('غير مصرح لك بتحديث هذه المجموعة', 401));
  }

  // تحديث المجموعة
  const fieldsToUpdate = {
    name: req.body.name,
    description: req.body.description,
    type: req.body.type,
    videoUrl: req.body.videoUrl
  };

  // حذف الحقول الفارغة
  Object.keys(fieldsToUpdate).forEach(key => {
    if (fieldsToUpdate[key] === undefined) {
      delete fieldsToUpdate[key];
    }
  });

  group = await Group.findByIdAndUpdate(
    req.params.id,
    fieldsToUpdate,
    { new: true, runValidators: true }
  ).populate({
    path: 'members',
    select: 'name avatarUrl'
  }).populate({
    path: 'creatorId',
    select: 'name avatarUrl'
  });

  res.status(200).json({
    success: true,
    data: group
  });
});

// @desc    حذف مجموعة
// @route   DELETE /api/groups/:id
// @access  Private
exports.deleteGroup = asyncHandler(async (req, res, next) => {
  const group = await Group.findById(req.params.id);

  if (!group) {
    return next(new ErrorResponse(`لم يتم العثور على مجموعة بالمعرف ${req.params.id}`, 404));
  }

  // التحقق من ملكية المجموعة
  if (group.creatorId.toString() !== req.user.id && req.user.role !== 'admin') {
    return next(new ErrorResponse('غير مصرح لك بحذف هذه المجموعة', 401));
  }

  // حذف صورة المجموعة إذا كانت موجودة
  if (group.groupImage && !group.groupImage.includes('default-group.png')) {
    const filePath = path.join(__dirname, '../../public', group.groupImage);
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }
  }

  // حذف المهام المرتبطة بالمجموعة
  await Task.deleteMany({ groupId: group._id });

  await group.remove();

  res.status(200).json({
    success: true,
    data: {}
  });
});

// @desc    الانضمام إلى مجموعة
// @route   PUT /api/groups/:id/join
// @access  Private
exports.joinGroup = asyncHandler(async (req, res, next) => {
  const group = await Group.findById(req.params.id);

  if (!group) {
    return next(new ErrorResponse(`لم يتم العثور على مجموعة بالمعرف ${req.params.id}`, 404));
  }

  // التحقق مما إذا كان المستخدم عضوًا بالفعل
  if (group.members.includes(req.user.id)) {
    return next(new ErrorResponse('أنت عضو بالفعل في هذه المجموعة', 400));
  }

  // إضافة المستخدم إلى المجموعة
  group.members.push(req.user.id);
  await group.save();

  const updatedGroup = await Group.findById(req.params.id)
    .populate({
      path: 'members',
      select: 'name avatarUrl'
    })
    .populate({
      path: 'creatorId',
      select: 'name avatarUrl'
    });

  res.status(200).json({
    success: true,
    data: updatedGroup
  });
});

// @desc    مغادرة مجموعة
// @route   PUT /api/groups/:id/leave
// @access  Private
exports.leaveGroup = asyncHandler(async (req, res, next) => {
  const group = await Group.findById(req.params.id);

  if (!group) {
    return next(new ErrorResponse(`لم يتم العثور على مجموعة بالمعرف ${req.params.id}`, 404));
  }

  // التحقق مما إذا كان المستخدم عضوًا
  if (!group.members.includes(req.user.id)) {
    return next(new ErrorResponse('أنت لست عضوًا في هذه المجموعة', 400));
  }

  // التحقق مما إذا كان المستخدم هو المنشئ
  if (group.creatorId.toString() === req.user.id) {
    return next(new ErrorResponse('لا يمكن لمنشئ المجموعة مغادرتها، يمكنك حذفها بدلاً من ذلك', 400));
  }

  // إزالة المستخدم من المجموعة
  group.members = group.members.filter(member => member.toString() !== req.user.id);
  await group.save();

  const updatedGroup = await Group.findById(req.params.id)
    .populate({
      path: 'members',
      select: 'name avatarUrl'
    })
    .populate({
      path: 'creatorId',
      select: 'name avatarUrl'
    });

  res.status(200).json({
    success: true,
    data: updatedGroup
  });
});

// @desc    إضافة مهمة إلى مجموعة
// @route   POST /api/groups/:id/tasks
// @access  Private
exports.addTask = asyncHandler(async (req, res, next) => {
  const group = await Group.findById(req.params.id);

  if (!group) {
    return next(new ErrorResponse(`لم يتم العثور على مجموعة بالمعرف ${req.params.id}`, 404));
  }

  // التحقق مما إذا كان المستخدم عضوًا
  if (!group.members.includes(req.user.id) && req.user.role !== 'admin') {
    return next(new ErrorResponse('يجب أن تكون عضوًا في المجموعة لإضافة مهام', 401));
  }

  // إنشاء المهمة
  const task = await Task.create({
    title: req.body.title,
    userId: req.body.userId || req.user.id,
    groupId: req.params.id,
    category: req.body.category || 'أخرى',
    points: req.body.points || '0',
    startTime: req.body.startTime || { hour: 0, minute: 0 },
    duration: req.body.duration || 0
  });

  res.status(201).json({
    success: true,
    data: task
  });
});

// @desc    الحصول على مهام المجموعة
// @route   GET /api/groups/:id/tasks
// @access  Private
exports.getGroupTasks = asyncHandler(async (req, res, next) => {
  const group = await Group.findById(req.params.id);

  if (!group) {
    return next(new ErrorResponse(`لم يتم العثور على مجموعة بالمعرف ${req.params.id}`, 404));
  }

  // التحقق مما إذا كان المستخدم عضوًا
  if (!group.members.includes(req.user.id) && req.user.role !== 'admin') {
    return next(new ErrorResponse('يجب أن تكون عضوًا في المجموعة للوصول إلى المهام', 401));
  }

  const tasks = await Task.find({ groupId: req.params.id })
    .populate({
      path: 'userId',
      select: 'name avatarUrl'
    });

  // تنظيم المهام حسب المستخدم
  const tasksByUser = {};
  tasks.forEach(task => {
    const userId = task.userId._id.toString();
    if (!tasksByUser[userId]) {
      tasksByUser[userId] = [];
    }
    tasksByUser[userId].push(task);
  });

  res.status(200).json({
    success: true,
    count: tasks.length,
    data: tasksByUser
  });
});
