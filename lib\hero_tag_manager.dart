
/// A utility class to manage hero tags across the app
/// This prevents conflicts when multiple FloatingActionButtons are used
class HeroTagManager {
  // Private constructor to prevent instantiation
  HeroTagManager._();
  
  // Map to track used hero tags and ensure uniqueness
  static final Set<String> _usedTags = {};
  
  /// Get a unique hero tag for a FloatingActionButton
  /// 
  /// [prefix] is typically the screen or component name
  /// [purpose] describes what the FAB does
  static String getUniqueTag(String prefix, String purpose) {
    final tag = '${prefix}_${purpose}_fab';
    _usedTags.add(tag);
    return tag;
  }
}
