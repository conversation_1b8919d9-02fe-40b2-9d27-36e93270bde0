import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:audioplayers/audioplayers.dart';
import 'package:table_calendar/table_calendar.dart';
import 'package:provider/provider.dart';
import 'package:timezone/timezone.dart' as tz;
import 'package:animate_do/animate_do.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter/services.dart';
import 'package:http/http.dart' as http;
import 'package:share_plus/share_plus.dart';
import 'dart:convert';
import 'appstate.dart';

// مفتاح API للأحاديث (يجب استبداله بمفتاحك الخاص من https://sunnah.api)
const String hadithApiKey = 'YOUR_SUNNAH_API_KEY'; // استبدل هذا بمفتاحك

class ReligiousHomePage extends StatefulWidget {
  const ReligiousHomePage({super.key});

  @override
  _ReligiousHomePageState createState() => _ReligiousHomePageState();
}

class _ReligiousHomePageState extends State<ReligiousHomePage> with SingleTickerProviderStateMixin {
  String _preferredSection = 'islamic_first';
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  final bool _isDayTime = DateTime.now().hour >= 6 && DateTime.now().hour < 18;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _loadPreferredSection();
    _animationController.forward();
    _initializeNotifications();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _loadPreferredSection() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _preferredSection = prefs.getString('preferred_section') ?? 'islamic_first';
    });
  }

  Future<void> _savePreferredSection(String value) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('preferred_section', value);
    setState(() {
      _preferredSection = value;
    });
    _animationController.forward(from: 0);
  }

  Future<void> _initializeNotifications() async {
    final flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();
    const initializationSettingsAndroid = AndroidInitializationSettings('app_icon');
    const initializationSettings = InitializationSettings(android: initializationSettingsAndroid);
    await flutterLocalNotificationsPlugin.initialize(initializationSettings);
  }

  Future<void> _scheduleNotification(String title, String body, DateTime scheduledTime) async {
    final flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();
    const androidDetails = AndroidNotificationDetails(
      'daily_reminder',
      'Daily Reminder',
      channelDescription: 'Daily religious reminders',
      importance: Importance.high,
      priority: Priority.high,
    );
    const notificationDetails = NotificationDetails(android: androidDetails);
    await flutterLocalNotificationsPlugin.zonedSchedule(
      0,
      title,
      body,
      tz.TZDateTime.from(scheduledTime, tz.local),
      notificationDetails,
      androidAllowWhileIdle: true,
      uiLocalNotificationDateInterpretation: UILocalNotificationDateInterpretation.absoluteTime,
    );
  }

  @override
  Widget build(BuildContext context) {
    final List<Widget> sections = _preferredSection == 'islamic_first'
        ? [
      _buildIslamicSection(context),
      _buildDivider(),
      _buildChristianSection(context),
      _buildDivider(),
      _buildVirtuesAndSinsSection(context),
    ]
        : [
      _buildChristianSection(context),
      _buildDivider(),
      _buildIslamicSection(context),
      _buildDivider(),
      _buildVirtuesAndSinsSection(context),
    ];

    return Scaffold(
      appBar: AppBar(
        title: const Text('تواصل مع الله', style: TextStyle(fontWeight: FontWeight.bold)),
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: _isDayTime
                  ? [Colors.teal[700]!, Colors.teal[300]!]
                  : [Colors.blueGrey[900]!, Colors.blueGrey[700]!],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
        ),
        actions: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: DropdownButton<String>(
              value: _preferredSection,
              icon: const Icon(Icons.arrow_drop_down, color: Colors.white),
              dropdownColor: _isDayTime ? Colors.teal[600] : Colors.blueGrey[600],
              style: const TextStyle(color: Colors.white, fontSize: 16),
              underline: Container(),
              onChanged: (String? newValue) {
                if (newValue != null) {
                  _savePreferredSection(newValue);
                }
              },
              items: <String>['islamic_first', 'christian_first']
                  .map<DropdownMenuItem<String>>((String value) {
                return DropdownMenuItem<String>(
                  value: value,
                  child: Text(value == 'islamic_first' ? 'القسم الإسلامي أولاً' : 'القسم المسيحي أولاً'),
                );
              }).toList(),
            ),
          ),
        ],
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: _isDayTime
                ? [Colors.teal[50]!, Colors.white, Colors.teal[50]!]
                : [Colors.blueGrey[800]!, Colors.blueGrey[900]!, Colors.blueGrey[800]!],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
        ),
        child: FadeTransition(
          opacity: _fadeAnimation,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: sections,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildIslamicSection(BuildContext context) {
    return FadeInUp(
      duration: const Duration(milliseconds: 600),
      child: Column(
        children: [
          const Text(
            'القسم الإسلامي',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.green,
              shadows: [Shadow(color: Colors.black26, blurRadius: 4, offset: Offset(2, 2))],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16.0),
          _buildFeatureCard(
            context: context,
            icon: FontAwesomeIcons.bookQuran,
            title: 'المصحف الإلكتروني',
            color: Colors.green[600]!,
            onTap: () => Navigator.push(context, MaterialPageRoute(builder: (context) => const QuranPage())),
          ),
          _buildFeatureCard(
            context: context,
            icon: FontAwesomeIcons.handsPraying,
            title: 'الأذكار اليومية',
            color: Colors.green[600]!,
            onTap: () => Navigator.push(context, MaterialPageRoute(builder: (context) => const AzkarPage())),
          ),
          _buildFeatureCard(
            context: context,
            icon: FontAwesomeIcons.mosque,
            title: 'مواقيت الصلاة',
            color: Colors.green[600]!,
            onTap: () => Navigator.push(context, MaterialPageRoute(builder: (context) => const IslamicPrayerTimesPage())),
          ),
          _buildFeatureCard(
            context: context,
            icon: FontAwesomeIcons.calendarAlt,
            title: 'التقويم الهجري',
            color: Colors.green[600]!,
            onTap: () => Navigator.push(context, MaterialPageRoute(builder: (context) => const HijriCalendarPage())),
          ),
          _buildFeatureCard(
            context: context,
            icon: FontAwesomeIcons.book,
            title: 'أحاديث البخاري ومسلم',
            color: Colors.green[600]!,
            onTap: () => Navigator.push(context, MaterialPageRoute(builder: (context) => const HadithPage())),
          ),
          _buildFeatureCard(
            context: context,
            icon: FontAwesomeIcons.bookOpen,
            title: 'الأحاديث القدسية',
            color: Colors.green[600]!,
            onTap: () => Navigator.push(context, MaterialPageRoute(builder: (context) => const HolyHadithPage())),
          ),
          _buildFeatureCard(
            context: context,
            icon: FontAwesomeIcons.bookmark,
            title: 'غريب القرآن',
            color: Colors.green[600]!,
            onTap: () => Navigator.push(context, MaterialPageRoute(builder: (context) => const StrangeQuranPage())),
          ),
          _buildFeatureCard(
            context: context,
            icon: FontAwesomeIcons.prayingHands,
            title: 'دعاء اليوم',
            color: Colors.green[600]!,
            onTap: () => Navigator.push(context, MaterialPageRoute(builder: (context) => const DailyDuaPage())),
          ),
        ],
      ),
    );
  }

  Widget _buildChristianSection(BuildContext context) {
    return FadeInUp(
      duration: const Duration(milliseconds: 600),
      child: Column(
        children: [
          const Text(
            'القسم المسيحي',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.blue,
              shadows: [Shadow(color: Colors.black26, blurRadius: 4, offset: Offset(2, 2))],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16.0),
          _buildFeatureCard(
            context: context,
            icon: FontAwesomeIcons.bookBible,
            title: 'الكتاب المقدس - العهد القديم',
            color: Colors.blue[600]!,
            onTap: () => Navigator.push(context, MaterialPageRoute(builder: (context) => const OldTestamentPage())),
          ),
          _buildFeatureCard(
            context: context,
            icon: FontAwesomeIcons.bookBible,
            title: 'الكتاب المقدس - العهد الجديد',
            color: Colors.blue[600]!,
            onTap: () => Navigator.push(context, MaterialPageRoute(builder: (context) => const NewTestamentPage())),
          ),
          _buildFeatureCard(
            context: context,
            icon: FontAwesomeIcons.church,
            title: 'أذكار الصلوات اليومية',
            color: Colors.blue[600]!,
            onTap: () => Navigator.push(context, MaterialPageRoute(builder: (context) => const PrayersPage())),
          ),
          _buildFeatureCard(
            context: context,
            icon: FontAwesomeIcons.cross,
            title: 'مواقيت الصلاة',
            color: Colors.blue[600]!,
            onTap: () => Navigator.push(context, MaterialPageRoute(builder: (context) => const ChristianPrayerTimesPage())),
          ),
          _buildFeatureCard(
            context: context,
            icon: FontAwesomeIcons.calendarAlt,
            title: 'التقويم الميلادي',
            color: Colors.blue[600]!,
            onTap: () => Navigator.push(context, MaterialPageRoute(builder: (context) => const GregorianCalendarPage())),
          ),
          _buildFeatureCard(
            context: context,
            icon: FontAwesomeIcons.dove,
            title: 'تأملات يومية',
            color: Colors.blue[600]!,
            onTap: () => Navigator.push(context, MaterialPageRoute(builder: (context) => const DailyMeditationPage())),
          ),
        ],
      ),
    );
  }

  Widget _buildVirtuesAndSinsSection(BuildContext context) {
    return FadeInUp(
      duration: const Duration(milliseconds: 600),
      child: Column(
        children: [
          const Text(
            'الخطايا والفضائل',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.purple,
              shadows: [Shadow(color: Colors.black26, blurRadius: 4, offset: Offset(2, 2))],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16.0),
          _buildFeatureCard(
            context: context,
            icon: FontAwesomeIcons.skullCrossbones,
            title: 'الخطايا السبع',
            color: Colors.red[600]!,
            onTap: () => Navigator.push(context, MaterialPageRoute(builder: (context) => const SevenSinsPage())),
          ),
          _buildFeatureCard(
            context: context,
            icon: FontAwesomeIcons.heart,
            title: 'الفضائل السبع',
            color: Colors.purple[600]!,
            onTap: () => Navigator.push(context, MaterialPageRoute(builder: (context) => const SevenVirtuesPage())),
          ),
        ],
      ),
    );
  }

  Widget _buildDivider() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 24.0),
      child: Divider(
        color: Colors.teal[300],
        thickness: 2,
        indent: 50,
        endIndent: 50,
      ),
    );
  }

  Widget _buildFeatureCard({
    required BuildContext context,
    required IconData icon,
    required String title,
    required Color color,
    required VoidCallback onTap,
  }) {
    return ZoomIn(
      duration: const Duration(milliseconds: 400),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        margin: const EdgeInsets.symmetric(vertical: 6.0),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [color.withOpacity(0.1), Colors.white],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: color.withOpacity(0.3),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(16),
          splashColor: color.withOpacity(0.3),
          child: ListTile(
            leading: Hero(tag: title, child: FaIcon(icon, color: color, size: 30)),
            title: Text(
              title,
              style: TextStyle(fontSize: 18, color: color, fontWeight: FontWeight.bold),
            ),
            trailing: const FaIcon(FontAwesomeIcons.chevronLeft, color: Colors.grey),
          ),
        ),
      ),
    );
  }
}

class QuranPage extends StatefulWidget {
  const QuranPage({super.key});

  @override
  _QuranPageState createState() => _QuranPageState();
}

class _QuranPageState extends State<QuranPage> {
  List<dynamic> verses = [];
  bool loading = false;
  String error = '';
  int selectedSurah = 1;
  int? selectedAyah;

  final List<Map<String, dynamic>> surahs = List.generate(114, (i) => {
    'number': i + 1,
    'name': _quranSurahNames[i],
  });

  Future<void> fetchQuran({int? surah, int? ayah}) async {
    setState(() {
      loading = true;
      error = '';
    });
    try {
      final url = Uri.parse(
          'https://api.alquran.cloud/v1/surah/${surah ?? selectedSurah}/ar');
      final response = await http.get(url);
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        List<dynamic> allVerses = data['data']['ayahs'] ?? [];
        if (ayah != null) {
          allVerses = allVerses
              .where((v) => v['numberInSurah'] == ayah)
              .toList();
        }
        setState(() {
          verses = allVerses;
        });
      } else {
        setState(() {
          error = 'فشل في جلب بيانات القرآن';
        });
      }
    } catch (e) {
      setState(() {
        error = 'خطأ في الاتصال بالخادم';
      });
    } finally {
      setState(() {
        loading = false;
      });
    }
  }

  void _onSurahChanged(int? surah) {
    if (surah != null) {
      setState(() {
        selectedSurah = surah;
        selectedAyah = null;
      });
      fetchQuran(surah: surah);
    }
  }

  void _onAyahChanged(String value) {
    final ayah = int.tryParse(value);
    setState(() {
      selectedAyah = ayah;
    });
    fetchQuran(surah: selectedSurah, ayah: ayah);
  }

  void _copyVerse(String text) {
    Clipboard.setData(ClipboardData(text: text));
    Provider.of<AppState>(context, listen: false)
        .showSnackBar('تم نسخ الآية!', Colors.green);
  }

  void _shareVerse(String text) {
    Share.share(text, subject: 'آية من القرآن الكريم');
  }

  @override
  void initState() {
    super.initState();
    fetchQuran();
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    return Scaffold(
      appBar: AppBar(
        title: const Text('القرآن الكريم'),
        backgroundColor: isDark ? Colors.teal[900] : Colors.teal,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Row(
              children: [
                Expanded(
                  child: DropdownButton<int>(
                    value: selectedSurah,
                    isExpanded: true,
                    icon: const Icon(Icons.arrow_drop_down),
                    items: surahs
                        .map((s) => DropdownMenuItem<int>(
                      value: s['number'],
                      child: Text('${s['number']}. ${s['name']}'),
                    ))
                        .toList(),
                    onChanged: _onSurahChanged,
                  ),
                ),
                const SizedBox(width: 12),
                SizedBox(
                  width: 80,
                  child: TextField(
                    decoration: const InputDecoration(
                      labelText: 'رقم الآية',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                    onSubmitted: _onAyahChanged,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (loading)
              const Center(child: CircularProgressIndicator()),
            if (error.isNotEmpty)
              Center(
                  child: Text(error, style: const TextStyle(color: Colors.red))),
            if (!loading && error.isEmpty)
              Expanded(
                child: ListView.builder(
                  itemCount: verses.length,
                  itemBuilder: (context, i) {
                    final v = verses[i];
                    return Card(
                      color: isDark ? Colors.teal[900] : Colors.teal[50],
                      margin: const EdgeInsets.symmetric(vertical: 6),
                      child: ListTile(
                        title: Text(
                          v['text'] ?? '',
                          style: const TextStyle(
                              fontSize: 22, fontFamily: 'Tajawal'),
                          textAlign: TextAlign.right,
                        ),
                        subtitle: Text('آية رقم: ${v['numberInSurah']}'),
                        trailing: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            IconButton(
                              icon: const Icon(Icons.copy),
                              onPressed: () => _copyVerse(v['text'] ?? ''),
                              tooltip: 'نسخ',
                            ),
                            IconButton(
                              icon: const Icon(Icons.share),
                              onPressed: () => _shareVerse(v['text'] ?? ''),
                              tooltip: 'مشاركة',
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
          ],
        ),
      ),
    );
  }
}

const List<String> _quranSurahNames = [
  'الفاتحة', 'البقرة', 'آل عمران', 'النساء', 'المائدة', 'الأنعام', 'الأعراف', 'الأنفال', 'التوبة', 'يونس',
  'هود', 'يوسف', 'الرعد', 'إبراهيم', 'الحجر', 'النحل', 'الإسراء', 'الكهف', 'مريم', 'طه',
  'الأنبياء', 'الحج', 'المؤمنون', 'النور', 'الفرقان', 'الشعراء', 'النمل', 'القصص', 'العنكبوت', 'الروم',
  'لقمان', 'السجدة', 'الأحزاب', 'سبأ', 'فاطر', 'يس', 'الصافات', 'ص', 'الزمر', 'غافر',
  'فصلت', 'الشورى', 'الزخرف', 'الدخان', 'الجاثية', 'الأحقاف', 'محمد', 'الفتح', 'الحجرات', 'ق',
  'الذاريات', 'الطور', 'النجم', 'القمر', 'الرحمن', 'الواقعة', 'الحديد', 'المجادلة', 'الحشر', 'الممتحنة',
  'الصف', 'الجمعة', 'المنافقون', 'التغابن', 'الطلاق', 'التحريم', 'الملك', 'القلم', 'الحاقة', 'المعارج',
  'نوح', 'الجن', 'المزمل', 'المدثر', 'القيامة', 'الإنسان', 'المرسلات', 'النبأ', 'النازعات', 'عبس',
  'التكوير', 'الانفطار', 'المطففين', 'الانشقاق', 'البروج', 'الطارق', 'الأعلى', 'الغاشية', 'الفجر', 'البلد',
  'الشمس', 'الليل', 'الضحى', 'الشرح', 'التين', 'العلق', 'القدر', 'البينة', 'الزلزلة', 'العاديات',
  'القارعة', 'التكاثر', 'العصر', 'الهمزة', 'الفيل', 'قريش', 'الماعون', 'الكوثر', 'الكافرون', 'النصر',
  'المسد', 'الإخلاص', 'الفلق', 'الناس',
];

class HadithPage extends StatefulWidget {
  const HadithPage({super.key});

  @override
  _HadithPageState createState() => _HadithPageState();
}

class _HadithPageState extends State<HadithPage> {
  List<dynamic> hadiths = [];
  bool loading = false;
  String error = '';
  String selectedCollection = 'bukhari';
  final TextEditingController _searchController = TextEditingController();

  final Map<String, String> collections = {
    'bukhari': 'صحيح البخاري',
    'muslim': 'صحيح مسلم',
  };

  Future<void> fetchHadith({String? query}) async {
    setState(() {
      loading = true;
      error = '';
    });
    try {
      final url = Uri.parse(
          'https://api.sunnah.com/v1/collections/$selectedCollection/hadiths${query != null ? '?query=$query' : ''}');
      final response = await http.get(
        url,
        headers: {'X-API-Key': hadithApiKey},
      );
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        setState(() {
          hadiths = data['hadiths'] ?? [];
        });
      } else {
        setState(() {
          error = 'فشل في جلب الأحاديث';
        });
      }
    } catch (e) {
      setState(() {
        error = 'خطأ في الاتصال بالخادم';
      });
    } finally {
      setState(() {
        loading = false;
      });
    }
  }

  void _onCollectionChanged(String? collection) {
    if (collection != null) {
      setState(() {
        selectedCollection = collection;
      });
      fetchHadith();
    }
  }

  void _onSearch() {
    final query = _searchController.text.trim();
    if (query.isNotEmpty) {
      fetchHadith(query: query);
    } else {
      fetchHadith();
    }
  }

  void _copyHadith(String text) {
    Clipboard.setData(ClipboardData(text: text));
    Provider.of<AppState>(context, listen: false)
        .showSnackBar('تم نسخ الحديث!', Colors.green);
  }

  void _shareHadith(String text) {
    Share.share(text, subject: 'حديث شريف');
  }

  @override
  void initState() {
    super.initState();
    fetchHadith();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('أحاديث البخاري ومسلم'),
        backgroundColor: Colors.green[600],
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () {
              showSearch(
                context: context,
                delegate: HadithSearchDelegate(fetchHadith: fetchHadith),
              );
            },
          ),
        ],
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [Colors.teal[50]!, Colors.white],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
        ),
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                children: [
                  Expanded(
                    child: DropdownButton<String>(
                      value: selectedCollection,
                      isExpanded: true,
                      icon: const Icon(Icons.arrow_drop_down),
                      items: collections.entries
                          .map((e) => DropdownMenuItem<String>(
                        value: e.key,
                        child: Text(e.value),
                      ))
                          .toList(),
                      onChanged: _onCollectionChanged,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: TextField(
                      controller: _searchController,
                      decoration: const InputDecoration(
                        labelText: 'ابحث في الأحاديث',
                        border: OutlineInputBorder(),
                      ),
                      onSubmitted: (_) => _onSearch(),
                    ),
                  ),
                ],
              ),
            ),
            if (loading)
              const Center(child: CircularProgressIndicator()),
            if (error.isNotEmpty)
              Center(
                  child: Text(error, style: const TextStyle(color: Colors.red))),
            if (!loading && error.isEmpty)
              Expanded(
                child: ListView.builder(
                  padding: const EdgeInsets.all(16.0),
                  itemCount: hadiths.length,
                  itemBuilder: (context, index) {
                    final hadith = hadiths[index];
                    return FadeInUp(
                      duration: const Duration(milliseconds: 400),
                      child: Card(
                        elevation: 4,
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12)),
                        child: ListTile(
                          title: Text(
                            hadith['hadith']?['body'] ?? 'غير متوفر',
                            style:
                            const TextStyle(fontSize: 18, color: Colors.green),
                          ),
                          subtitle: Text(
                            'المصدر: ${collections[selectedCollection]}',
                            style: const TextStyle(color: Colors.grey),
                          ),
                          trailing: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              IconButton(
                                icon: const Icon(Icons.copy),
                                onPressed: () =>
                                    _copyHadith(hadith['hadith']?['body'] ?? ''),
                                tooltip: 'نسخ',
                              ),
                              IconButton(
                                icon: const Icon(Icons.share),
                                onPressed: () =>
                                    _shareHadith(hadith['hadith']?['body'] ?? ''),
                                tooltip: 'مشاركة',
                              ),
                            ],
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
          ],
        ),
      ),
    );
  }
}

class HadithSearchDelegate extends SearchDelegate {
  final Future<void> Function({String? query}) fetchHadith;

  HadithSearchDelegate({required this.fetchHadith});

  @override
  List<Widget> buildActions(BuildContext context) {
    return [
      IconButton(
        icon: const Icon(Icons.clear),
        onPressed: () => query = '',
      ),
    ];
  }

  @override
  Widget buildLeading(BuildContext context) {
    return IconButton(
      icon: const Icon(Icons.arrow_back),
      onPressed: () => close(context, null),
    );
  }

  @override
  Widget buildResults(BuildContext context) {
    fetchHadith(query: query);
    return const Center(child: CircularProgressIndicator());
  }

  @override
  Widget buildSuggestions(BuildContext context) {
    return Container();
  }
}

class HolyHadithPage extends StatefulWidget {
  const HolyHadithPage({super.key});

  @override
  _HolyHadithPageState createState() => _HolyHadithPageState();
}

class _HolyHadithPageState extends State<HolyHadithPage> {
  List<dynamic> holyHadiths = [];
  bool loading = false;
  String error = '';

  // ملاحظة: جلب الأحاديث القدسية من Sunnah API
  Future<void> fetchHolyHadith() async {
    setState(() {
      loading = true;
      error = '';
    });
    try {
      final url = Uri.parse('https://api.sunnah.com/v1/collections/bukhari/hadiths');
      final response = await http.get(
        url,
        headers: {'X-API-Key': hadithApiKey},
      );
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        setState(() {
          holyHadiths = (data['hadiths'] ?? [])
              .where((h) => h['hadith']?['body']?.contains('قال الله') ?? false)
              .toList();
        });
      } else {
        setState(() {
          error = 'فشل في جلب الأحاديث القدسية';
        });
      }
    } catch (e) {
      setState(() {
        error = 'خطأ في الاتصال بالخادم';
      });
    } finally {
      setState(() {
        loading = false;
      });
    }
  }

  void _copyHadith(String text) {
    Clipboard.setData(ClipboardData(text: text));
    Provider.of<AppState>(context, listen: false)
        .showSnackBar('تم نسخ الحديث!', Colors.green);
  }

  void _shareHadith(String text) {
    Share.share(text, subject: 'حديث قدسي');
  }

  @override
  void initState() {
    super.initState();
    fetchHolyHadith();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الأحاديث القدسية'),
        backgroundColor: Colors.green[600],
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [Colors.teal[50]!, Colors.white],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
        ),
        child: Column(
          children: [
            if (loading)
              const Center(child: CircularProgressIndicator()),
            if (error.isNotEmpty)
              Center(
                  child: Text(error, style: const TextStyle(color: Colors.red))),
            if (!loading && error.isEmpty)
              Expanded(
                child: ListView.builder(
                  padding: const EdgeInsets.all(16.0),
                  itemCount: holyHadiths.length,
                  itemBuilder: (context, index) {
                    final hadith = holyHadiths[index];
                    return FadeInUp(
                      duration: const Duration(milliseconds: 400),
                      child: Card(
                        elevation: 4,
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12)),
                        child: ListTile(
                          title: Text(
                            hadith['hadith']?['body'] ?? 'غير متوفر',
                            style:
                            const TextStyle(fontSize: 18, color: Colors.green),
                          ),
                          trailing: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              IconButton(
                                icon: const Icon(Icons.copy),
                                onPressed: () =>
                                    _copyHadith(hadith['hadith']?['body'] ?? ''),
                                tooltip: 'نسخ',
                              ),
                              IconButton(
                                icon: const Icon(Icons.share),
                                onPressed: () =>
                                    _shareHadith(hadith['hadith']?['body'] ?? ''),
                                tooltip: 'مشاركة',
                              ),
                              IconButton(
                                icon: const Icon(Icons.bookmark_border),
                                onPressed: () {
                                  Provider.of<AppState>(context, listen: false)
                                      .showSnackBar(
                                      'تم إضافة الحديث إلى المفضلة',
                                      Colors.green);
                                },
                                tooltip: 'إضافة إلى المفضلة',
                              ),
                            ],
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
          ],
        ),
      ),
    );
  }
}

class OldTestamentPage extends StatefulWidget {
  const OldTestamentPage({super.key});

  @override
  _OldTestamentPageState createState() => _OldTestamentPageState();
}

class _OldTestamentPageState extends State<OldTestamentPage> {
  Map<String, dynamic>? bibleData;
  bool loading = false;
  String error = '';
  String reference = 'Genesis 1:1';
  final TextEditingController _controller =
  TextEditingController(text: 'Genesis 1:1');

  Future<void> fetchBible({String? ref}) async {
    setState(() {
      loading = true;
      error = '';
    });
    try {
      final url = Uri.parse(
          'https://bible-api.com/${Uri.encodeComponent(ref ?? reference)}');
      final response = await http.get(url);
      if (response.statusCode == 200) {
        setState(() {
          bibleData = json.decode(response.body);
        });
      } else {
        setState(() {
          error = 'فشل في جلب بيانات الكتاب المقدس';
        });
      }
    } catch (e) {
      setState(() {
        error = 'خطأ في الاتصال بالخادم';
      });
    } finally {
      setState(() {
        loading = false;
      });
    }
  }

  void _onSearch() {
    setState(() {
      reference = _controller.text;
    });
    fetchBible(ref: reference);
  }

  void _copyVerse(String text) {
    Clipboard.setData(ClipboardData(text: text));
    Provider.of<AppState>(context, listen: false)
        .showSnackBar('تم نسخ النص!', Colors.blue);
  }

  void _shareVerse(String text) {
    Share.share(text, subject: 'آية من الكتاب المقدس');
  }

  @override
  void initState() {
    super.initState();
    fetchBible();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    return Scaffold(
      appBar: AppBar(
        title: const Text('العهد القديم'),
        backgroundColor: isDark ? Colors.blueGrey[900] : Colors.blueGrey,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _controller,
                    decoration: const InputDecoration(
                      labelText: 'مثال: Genesis 1:1',
                      border: OutlineInputBorder(),
                    ),
                    onSubmitted: (_) => _onSearch(),
                  ),
                ),
                const SizedBox(width: 12),
                ElevatedButton(
                  onPressed: _onSearch,
                  child: const Text('بحث'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (loading)
              const Center(child: CircularProgressIndicator()),
            if (error.isNotEmpty)
              Center(
                  child: Text(error, style: const TextStyle(color: Colors.red))),
            if (!loading && error.isEmpty && bibleData != null)
              Expanded(
                child: ListView.builder(
                  itemCount: (bibleData!['verses'] as List).length,
                  itemBuilder: (context, i) {
                    final v = bibleData!['verses'][i];
                    return Card(
                      color: isDark ? Colors.blueGrey[900] : Colors.blueGrey[50],
                      margin: const EdgeInsets.symmetric(vertical: 6),
                      child: ListTile(
                        title: Text(
                          v['text'] ?? '',
                          style:
                          const TextStyle(fontSize: 20, fontFamily: 'Tajawal'),
                          textAlign: TextAlign.right,
                        ),
                        subtitle:
                        Text('${v['book_name']} ${v['chapter']}:${v['verse']}'),
                        trailing: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            IconButton(
                              icon: const Icon(Icons.copy),
                              onPressed: () => _copyVerse(v['text'] ?? ''),
                              tooltip: 'نسخ',
                            ),
                            IconButton(
                              icon: const Icon(Icons.share),
                              onPressed: () => _shareVerse(v['text'] ?? ''),
                              tooltip: 'مشاركة',
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
          ],
        ),
      ),
    );
  }
}

class NewTestamentPage extends StatefulWidget {
  const NewTestamentPage({super.key});

  @override
  _NewTestamentPageState createState() => _NewTestamentPageState();
}

class _NewTestamentPageState extends State<NewTestamentPage> {
  final AudioPlayer _audioPlayer = AudioPlayer();
  bool _isPlaying = false;
  Map<String, dynamic>? bibleData;
  bool loading = false;
  String error = '';
  String reference = 'Matthew 1:1';
  final TextEditingController _controller =
  TextEditingController(text: 'Matthew 1:1');

  Future<void> fetchBible({String? ref}) async {
    setState(() {
      loading = true;
      error = '';
    });
    try {
      final url = Uri.parse(
          'https://bible-api.com/${Uri.encodeComponent(ref ?? reference)}');
      final response = await http.get(url);
      if (response.statusCode == 200) {
        setState(() {
          bibleData = json.decode(response.body);
        });
      } else {
        setState(() {
          error = 'فشل في جلب بيانات الكتاب المقدس';
        });
      }
    } catch (e) {
      setState(() {
        error = 'خطأ في الاتصال بالخادم';
      });
    } finally {
      setState(() {
        loading = false;
      });
    }
  }

  void _onSearch() {
    setState(() {
      reference = _controller.text;
    });
    fetchBible(ref: reference);
  }

  void _copyVerse(String text) {
    Clipboard.setData(ClipboardData(text: text));
    Provider.of<AppState>(context, listen: false)
        .showSnackBar('تم نسخ النص!', Colors.blue);
  }

  void _shareVerse(String text) {
    Share.share(text, subject: 'آية من الكتاب المقدس');
  }

  void _playAudio(String url) async {
    try {
      await _audioPlayer.play(UrlSource(url));
      setState(() => _isPlaying = true);
    } catch (e) {
      Provider.of<AppState>(context, listen: false)
          .showSnackBar('خطأ في تشغيل الصوت: $e', Colors.red);
    }
  }

  void _pauseAudio() async {
    await _audioPlayer.pause();
    setState(() => _isPlaying = false);
  }

  @override
  void initState() {
    super.initState();
    fetchBible();
  }

  @override
  void dispose() {
    _controller.dispose();
    _audioPlayer.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    return Scaffold(
      appBar: AppBar(
        title: const Text('العهد الجديد'),
        backgroundColor: isDark ? Colors.blueGrey[900] : Colors.blueGrey,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _controller,
                    decoration: const InputDecoration(
                      labelText: 'مثال: Matthew 1:1',
                      border: OutlineInputBorder(),
                    ),
                    onSubmitted: (_) => _onSearch(),
                  ),
                ),
                const SizedBox(width: 12),
                ElevatedButton(
                  onPressed: _onSearch,
                  child: const Text('بحث'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (loading)
              const Center(child: CircularProgressIndicator()),
            if (error.isNotEmpty)
              Center(
                  child: Text(error, style: const TextStyle(color: Colors.red))),
            if (!loading && error.isEmpty && bibleData != null)
              Expanded(
                child: ListView.builder(
                  itemCount: (bibleData!['verses'] as List).length,
                  itemBuilder: (context, i) {
                    final v = bibleData!['verses'][i];
                    return Card(
                      color: isDark ? Colors.blueGrey[900] : Colors.blueGrey[50],
                      margin: const EdgeInsets.symmetric(vertical: 6),
                      child: ListTile(
                        title: Text(
                          v['text'] ?? '',
                          style:
                          const TextStyle(fontSize: 20, fontFamily: 'Tajawal'),
                          textAlign: TextAlign.right,
                        ),
                        subtitle:
                        Text('${v['book_name']} ${v['chapter']}:${v['verse']}'),
                        trailing: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            IconButton(
                              icon: const Icon(Icons.copy),
                              onPressed: () => _copyVerse(v['text'] ?? ''),
                              tooltip: 'نسخ',
                            ),
                            IconButton(
                              icon: const Icon(Icons.share),
                              onPressed: () => _shareVerse(v['text'] ?? ''),
                              tooltip: 'مشاركة',
                            ),
                            IconButton(
                              icon: Icon(_isPlaying ? Icons.pause : Icons.play_arrow),
                              onPressed: () {
                                if (_isPlaying) {
                                  _pauseAudio();
                                } else {
                                  // استبدل هذا برابط صوتي حقيقي إذا توفر
                                  _playAudio(
                                      'https://example.com/bible/matthew.mp3');
                                }
                              },
                              tooltip: _isPlaying ? 'إيقاف' : 'تشغيل',
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
          ],
        ),
      ),
    );
  }
}

class HijriCalendarPage extends StatefulWidget {
  const HijriCalendarPage({super.key});

  @override
  _HijriCalendarPageState createState() => _HijriCalendarPageState();
}

class _HijriCalendarPageState extends State<HijriCalendarPage> {
  CalendarFormat _calendarFormat = CalendarFormat.month;
  DateTime _focusedDay = DateTime.now();
  DateTime? _selectedDay;
  String? _hijriDateText;
  bool _loadingHijri = false;
  String? _hijriError;

  final Map<DateTime, List<String>> _holidays = {
    DateTime(2025, 4, 20): ['عيد الفطر'],
    DateTime(2025, 6, 27): ['عيد الأضحى'],
  };

  Future<void> fetchHijriDate(DateTime date) async {
    setState(() {
      _loadingHijri = true;
      _hijriError = null;
    });
    try {
      final url = Uri.parse(
          'http://api.aladhan.com/v1/gToH/${date.day}-${date.month}-${date.year}');
      final response = await http.get(url);
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        setState(() {
          _hijriDateText =
          '${data['data']['hijri']['day']} ${data['data']['hijri']['month']['ar']} ${data['data']['hijri']['year']}';
        });
      } else {
        setState(() {
          _hijriError = 'فشل في جلب التاريخ الهجري';
        });
      }
    } catch (e) {
      setState(() {
        _hijriError = 'خطأ في الاتصال بالخادم';
      });
    } finally {
      setState(() {
        _loadingHijri = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('التقويم الهجري'),
        backgroundColor: Colors.teal[700],
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [Colors.teal[50]!, Colors.white],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
        ),
        child: Column(
          children: [
            TableCalendar(
              firstDay: DateTime.utc(2020, 1, 1),
              lastDay: DateTime.utc(2030, 12, 31),
              focusedDay: _focusedDay,
              calendarFormat: _calendarFormat,
              selectedDayPredicate: (day) => isSameDay(_selectedDay, day),
              onDaySelected: (selectedDay, focusedDay) {
                setState(() {
                  _selectedDay = selectedDay;
                  _focusedDay = focusedDay;
                });
                fetchHijriDate(selectedDay);
              },
              onFormatChanged: (format) {
                setState(() {
                  _calendarFormat = format;
                });
              },
              eventLoader: (day) => _holidays[day] ?? [],
              calendarStyle: CalendarStyle(
                holidayTextStyle: const TextStyle(color: Colors.red),
                holidayDecoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(color: Colors.red),
                ),
              ),
            ),
            if (_selectedDay != null)
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 12.0),
                child: _loadingHijri
                    ? const CircularProgressIndicator()
                    : _hijriError != null
                    ? Text(_hijriError!,
                    style: const TextStyle(color: Colors.red))
                    : _hijriDateText != null
                    ? Text('التاريخ الهجري: $_hijriDateText',
                    style: const TextStyle(
                        fontSize: 18, color: Colors.teal))
                    : const SizedBox.shrink(),
              ),
            if (_selectedDay != null && _holidays[_selectedDay] != null)
              FadeInUp(
                duration: const Duration(milliseconds: 400),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Text(
                    'العيد: ${_holidays[_selectedDay]!.join(", ")}',
                    style: const TextStyle(fontSize: 18, color: Colors.teal),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}

// الصفحات الأخرى (AzkarPage, IslamicPrayerTimesPage, StrangeQuranPage, إلخ) لم تتغير
class AzkarPage extends StatefulWidget {
  const AzkarPage({super.key});

  @override
  _AzkarPageState createState() => _AzkarPageState();
}

class _AzkarPageState extends State<AzkarPage> {
  final AudioPlayer _audioPlayer = AudioPlayer();
  final List<Map<String, String>> azkarSample = const [
    {
      'text': 'سبحان الله',
      'audioUrl': 'https://example.com/azkar/subhanallah.mp3'
    },
    {
      'text': 'الحمد لله',
      'audioUrl': 'https://example.com/azkar/alhamdulillah.mp3'
    },
  ];

  Future<void> _scheduleNotification(
      String title, String body, DateTime scheduledTime) async {
    final flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();
    const androidDetails = AndroidNotificationDetails(
      'azkar_notification',
      'Azkar Playback',
      channelDescription: 'Notifications for Azkar playback',
      importance: Importance.high,
      priority: Priority.high,
    );
    const notificationDetails = NotificationDetails(android: androidDetails);
    await flutterLocalNotificationsPlugin.zonedSchedule(
      0,
      title,
      body,
      tz.TZDateTime.from(scheduledTime, tz.local),
      notificationDetails,
      androidAllowWhileIdle: true,
      uiLocalNotificationDateInterpretation:
      UILocalNotificationDateInterpretation.absoluteTime,
    );
  }

  @override
  void dispose() {
    _audioPlayer.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الأذكار اليومية'),
        backgroundColor: Colors.green[600],
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [Colors.teal[50]!, Colors.white],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
        ),
        child: ListView.builder(
          padding: const EdgeInsets.all(16.0),
          itemCount: azkarSample.length,
          itemBuilder: (context, index) {
            final azkar = azkarSample[index];
            return FadeInUp(
              duration: const Duration(milliseconds: 400),
              child: Card(
                elevation: 4,
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12)),
                child: ListTile(
                  title: Text(
                    azkar['text']!,
                    style: const TextStyle(fontSize: 18, color: Colors.green),
                  ),
                  trailing: IconButton(
                    icon: const Icon(Icons.play_arrow, color: Colors.green),
                    onPressed: () async {
                      try {
                        await _audioPlayer.play(UrlSource(azkar['audioUrl']!));
                        _scheduleNotification(
                          'أذكار اليوم',
                          azkar['text']!,
                          DateTime.now().add(const Duration(minutes: 1)),
                        );
                      } catch (e) {
                        Provider.of<AppState>(context, listen: false)
                            .showSnackBar('خطأ في تشغيل الصوت: $e', Colors.red);
                      }
                    },
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}

class IslamicPrayerTimesPage extends StatefulWidget {
  const IslamicPrayerTimesPage({super.key});

  @override
  _IslamicPrayerTimesPageState createState() => _IslamicPrayerTimesPageState();
}

class _IslamicPrayerTimesPageState extends State<IslamicPrayerTimesPage> {
  Map<String, dynamic>? prayerTimes;
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    fetchPrayerTimes();
  }

  Future<void> fetchPrayerTimes() async {
    try {
      final response = await http.get(
        Uri.parse(
            'http://api.aladhan.com/v1/timingsByCity?city=Cairo&country=Egypt&method=5'),
      );
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        setState(() {
          prayerTimes = data['data']['timings'];
          isLoading = false;
        });
        _scheduleNotification(
          'مواقيت الصلاة',
          'حان وقت صلاة ${prayerTimes!['Fajr']}',
          DateTime.now().add(const Duration(minutes: 5)),
        );
      } else {
        throw Exception('فشل جلب مواقيت الصلاة');
      }
    } catch (e) {
      setState(() {
        isLoading = false;
      });
      Provider.of<AppState>(context, listen: false)
          .showSnackBar('خطأ في جلب مواقيت الصلاة: $e', Colors.red);
    }
  }

  Future<void> _scheduleNotification(
      String title, String body, DateTime scheduledTime) async {
    final flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();
    const androidDetails = AndroidNotificationDetails(
      'prayer_notification',
      'Prayer Times',
      channelDescription: 'Notifications for prayer times',
      importance: Importance.high,
      priority: Priority.high,
    );
    const notificationDetails = NotificationDetails(android: androidDetails);
    await flutterLocalNotificationsPlugin.zonedSchedule(
      0,
      title,
      body,
      tz.TZDateTime.from(scheduledTime, tz.local),
      notificationDetails,
      androidAllowWhileIdle: true,
      uiLocalNotificationDateInterpretation:
      UILocalNotificationDateInterpretation.absoluteTime,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('مواقيت الصلاة الإسلامية'),
        backgroundColor: Colors.teal[700],
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [Colors.teal[50]!, Colors.white],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
        ),
        child: isLoading
            ? const Center(child: CircularProgressIndicator(color: Colors.teal))
            : prayerTimes == null
            ? const Center(
            child: Text('فشل تحميل مواقيت الصلاة',
                style: TextStyle(color: Colors.teal)))
            : Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              const Text(
                'مواقيت الصلاة في القاهرة',
                style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.teal),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 20),
              ...[
                _buildPrayerTimeCard('الفجر', prayerTimes!['Fajr']),
                _buildPrayerTimeCard('الشروق', prayerTimes!['Sunrise']),
                _buildPrayerTimeCard('الظهر', prayerTimes!['Dhuhr']),
                _buildPrayerTimeCard('العصر', prayerTimes!['Asr']),
                _buildPrayerTimeCard('المغرب', prayerTimes!['Maghrib']),
                _buildPrayerTimeCard('العشاء', prayerTimes!['Isha']),
              ]
                  .asMap()
                  .entries
                  .map((entry) => FadeInUp(
                duration:
                Duration(milliseconds: 400 + entry.key * 100),
                child: entry.value,
              ))
                  ,
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPrayerTimeCard(String prayerName, String time) {
    return Card(
      elevation: 4,
      margin: const EdgeInsets.symmetric(vertical: 8),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: ListTile(
        title: Text(
          prayerName,
          style: const TextStyle(fontWeight: FontWeight.bold, color: Colors.teal),
        ),
        trailing: Text(
          time,
          style: const TextStyle(fontSize: 18, color: Colors.grey),
        ),
      ),
    );
  }
}

class StrangeQuranPage extends StatelessWidget {
  const StrangeQuranPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('غريب القرآن'),
        backgroundColor: Colors.green[600],
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [Colors.teal[50]!, Colors.white],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
        ),
        child: const Center(
          child: Text(
            'قيد التطوير: غريب القرآن',
            style: TextStyle(fontSize: 18, color: Colors.teal),
          ),
        ),
      ),
    );
  }
}

class PrayersPage extends StatelessWidget {
  const PrayersPage({super.key});

  final List<String> prayersSample = const [
    'أيها الرب، ارحمنا وباركنا',
    'يا إلهنا، امنحنا السلام',
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('أذكار الصلوات اليومية'),
        backgroundColor: Colors.blue[600],
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [Colors.blue[50]!, Colors.white],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
        ),
        child: ListView.builder(
          padding: const EdgeInsets.all(16.0),
          itemCount: prayersSample.length,
          itemBuilder: (context, index) {
            return FadeInUp(
              duration: const Duration(milliseconds: 400),
              child: Card(
                elevation: 4,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                child: ListTile(
                  title: Text(
                    prayersSample[index],
                    style: const TextStyle(fontSize: 18, color: Colors.blue),
                  ),
                  trailing: IconButton(
                    icon: const Icon(Icons.bookmark_border),
                    onPressed: () {
                      Provider.of<AppState>(context, listen: false)
                          .showSnackBar('تم إضافة الصلاة إلى المفضلة', Colors.blue);
                    },
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}

class ChristianPrayerTimesPage extends StatefulWidget {
  const ChristianPrayerTimesPage({super.key});

  @override
  _ChristianPrayerTimesPageState createState() =>
      _ChristianPrayerTimesPageState();
}

class _ChristianPrayerTimesPageState extends State<ChristianPrayerTimesPage> {
  final Map<String, String> prayerTimes = const {
    'صلاة الفجر': '05:00 صباحًا',
    'صلاة الصباح': '07:00 صباحًا',
    'صلاة الظهيرة': '12:00 ظهرًا',
    'صلاة العصر': '03:00 عصرًا',
    'صلاة المساء': '06:00 مساءً',
    'صلاة الليل': '09:00 مساءً',
  };

  int _prayerCount = 0;

  void _incrementPrayerCount() {
    setState(() {
      _prayerCount++;
    });
    Provider.of<AppState>(context, listen: false).showSnackBar(
        'تم تسجيل صلاة! إجمالي الصلوات: $_prayerCount', Colors.blue);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('مواقيت الصلاة المسيحية'),
        backgroundColor: Colors.blue[600],
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [Colors.blue[50]!, Colors.white],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              const Text(
                'مواقيت الصلاة المسيحية (مثال)',
                style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.blue),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 20),
              Text(
                'عدد الصلوات المسجلة: $_prayerCount',
                style: const TextStyle(fontSize: 18, color: Colors.blue),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 10),
              ...prayerTimes.entries
                  .toList()
                  .asMap()
                  .entries
                  .map((entry) => FadeInUp(
                duration: Duration(milliseconds: 400 + entry.key * 100),
                child: _buildPrayerTimeCard(
                    entry.value.key, entry.value.value),
              ))
                  ,
            ],
          ),
        ),
      ),
      floatingActionButton: ZoomIn(
        duration: const Duration(milliseconds: 400),
        child: FloatingActionButton(
          heroTag: 'christian_prayer_counter_fab',
          onPressed: _incrementPrayerCount,
          backgroundColor: Colors.blue[600],
          child: const Icon(Icons.add),
        ),
      ),
    );
  }

  Widget _buildPrayerTimeCard(String prayerName, String time) {
    return Card(
      elevation: 4,
      margin: const EdgeInsets.symmetric(vertical: 8),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: ListTile(
        title: Text(
          prayerName,
          style: const TextStyle(fontWeight: FontWeight.bold, color: Colors.blue),
        ),
        trailing: Text(
          time,
          style: const TextStyle(fontSize: 18, color: Colors.grey),
        ),
      ),
    );
  }
}

class GregorianCalendarPage extends StatefulWidget {
  const GregorianCalendarPage({super.key});

  @override
  _GregorianCalendarPageState createState() => _GregorianCalendarPageState();
}

class _GregorianCalendarPageState extends State<GregorianCalendarPage> {
  CalendarFormat _calendarFormat = CalendarFormat.month;
  DateTime _focusedDay = DateTime.now();
  DateTime? _selectedDay;

  final Map<DateTime, List<String>> _holidays = {
    DateTime(2025, 1, 7): ['عيد الميلاد (الأرثوذكسي)'],
    DateTime(2025, 4, 20): ['عيد الفصح (تقريبي)'],
  };

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('التقويم الميلادي'),
        backgroundColor: Colors.blue[600],
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [Colors.blue[50]!, Colors.white],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
        ),
        child: Column(
          children: [
            TableCalendar(
              firstDay: DateTime.utc(2020, 1, 1),
              lastDay: DateTime.utc(2030, 12, 31),
              focusedDay: _focusedDay,
              calendarFormat: _calendarFormat,
              selectedDayPredicate: (day) => isSameDay(_selectedDay, day),
              onDaySelected: (selectedDay, focusedDay) {
                setState(() {
                  _selectedDay = selectedDay;
                  _focusedDay = focusedDay;
                });
              },
              onFormatChanged: (format) {
                setState(() {
                  _calendarFormat = format;
                });
              },
              eventLoader: (day) => _holidays[day] ?? [],
              calendarStyle: CalendarStyle(
                holidayTextStyle: const TextStyle(color: Colors.red),
                holidayDecoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(color: Colors.red),
                ),
              ),
            ),
            if (_selectedDay != null && _holidays[_selectedDay] != null)
              FadeInUp(
                duration: const Duration(milliseconds: 400),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Text(
                    'العيد: ${_holidays[_selectedDay]!.join(", ")}',
                    style: const TextStyle(fontSize: 18, color: Colors.blue),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}

class DailyDuaPage extends StatelessWidget {
  const DailyDuaPage({super.key});

  final List<String> duaSample = const [
    'اللهم إني أسألك العفو والعافية في الدنيا والآخرة',
    'اللهم ارزقني رزقًا حلالًا طيبًا',
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('دعاء اليوم'),
        backgroundColor: Colors.green[600],
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [Colors.teal[50]!, Colors.white],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
        ),
        child: ListView.builder(
          padding: const EdgeInsets.all(16.0),
          itemCount: duaSample.length,
          itemBuilder: (context, index) {
            return FadeInUp(
              duration: const Duration(milliseconds: 400),
              child: Card(
                elevation: 4,
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12)),
                child: ListTile(
                  title: Text(
                    duaSample[index],
                    style: const TextStyle(fontSize: 18, color: Colors.green),
                  ),
                  trailing: IconButton(
                    icon: const Icon(Icons.bookmark_border),
                    onPressed: () {
                      Provider.of<AppState>(context, listen: false)
                          .showSnackBar('تم إضافة الدعاء إلى المفضلة', Colors.green);
                    },
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}

class DailyMeditationPage extends StatelessWidget {
  const DailyMeditationPage({super.key});

  final List<Map<String, String>> meditationSample = const [
    {
      'text': 'تأمل في محبة الله للعالم',
      'verse': 'يوحنا 3:16',
    },
    {
      'text': 'ابحث عن السلام الداخلي من خلال الصلاة',
      'verse': 'فيلبي 4:6',
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تأملات يومية'),
        backgroundColor: Colors.blue[600],
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [Colors.blue[50]!, Colors.white],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
        ),
        child: ListView.builder(
          padding: const EdgeInsets.all(16.0),
          itemCount: meditationSample.length,
          itemBuilder: (context, index) {
            return FadeInUp(
              duration: const Duration(milliseconds: 400),
              child: Card(
                elevation: 4,
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12)),
                child: ListTile(
                  title: Text(
                    meditationSample[index]['text']!,
                    style: const TextStyle(fontSize: 18, color: Colors.blue),
                  ),
                  subtitle: Text(
                    'الإصحاح: ${meditationSample[index]['verse']!}',
                    style: const TextStyle(color: Colors.grey),
                  ),
                  trailing: IconButton(
                    icon: const Icon(Icons.bookmark_border),
                    onPressed: () {
                      Provider.of<AppState>(context, listen: false)
                          .showSnackBar('تم إضافة التأمل إلى المفضلة', Colors.blue);
                    },
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}

class SevenSinsPage extends StatelessWidget {
  const SevenSinsPage({super.key});

  final List<Map<String, dynamic>> sins = const [
    {
      'name': 'الكبرياء',
      'description': 'التكبر والاعتزاز بالنفس فوق الآخرين',
      'icon': FontAwesomeIcons.crown,
      'verse': 'الكبرياء تسبق الهلاك، والروح المتعالية تسبق السقوط (أمثال 16:18)',
    },
    {
      'name': 'الطمع',
      'description': 'الرغبة المفرطة في الممتلكات أو الثروة',
      'icon': FontAwesomeIcons.coins,
      'verse': 'لأن محبة المال أصل لكل الشرور (تيموثاوس الأولى 6:10)',
    },
    {
      'name': 'الشهوة',
      'description': 'الرغبة الجنسية غير المنضبطة',
      'icon': FontAwesomeIcons.heartCrack,
      'verse': 'كل من ينظر إلى امرأة ليشتهيها فقد زنى بها في قلبه (متى 5:28)',
    },
    {
      'name': 'الحسد',
      'description': 'الغيرة من نجاح أو ممتلكات الآخرين',
      'icon': FontAwesomeIcons.eye,
      'verse': 'القلب السليم حياة الجسد، أما الحسد فرطوبة العظام (أمثال 14:30)',
    },
    {
      'name': 'الشراهة',
      'description': 'الإفراط في الأكل أو الشرب',
      'icon': FontAwesomeIcons.utensils,
      'verse': 'لا تكن بين شاربي الخمر، ولا بين المتوغلين في اللحم (أمثال 23:20)',
    },
    {
      'name': 'الغضب',
      'description': 'الغضب غير المنضبط الذي يؤدي إلى العنف',
      'icon': FontAwesomeIcons.fire,
      'verse': 'الإنسان البطيء الغضب كثير الفهم، أما السريع الروح فيرفع الحماقة (أمثال 14:29)',
    },
    {
      'name': 'الكسل',
      'description': 'الإهمال والتكاسل عن العمل أو الواجبات',
      'icon': FontAwesomeIcons.couch,
      'verse': 'الكسلان لا يصطاد صيداً، أما ثروة الإنسان المجتهد فهي نفيسة (أمثال 12:27)',
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الخطايا السبع'),
        backgroundColor: Colors.red[600],
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [Colors.red[50]!, Colors.white],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
        ),
        child: ListView.builder(
          padding: const EdgeInsets.all(16.0),
          itemCount: sins.length,
          itemBuilder: (context, index) {
            return FadeInUp(
              duration: const Duration(milliseconds: 400),
              child: Card(
                elevation: 4,
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12)),
                child: ListTile(
                  leading: FaIcon(sins[index]['icon'], color: Colors.red[600]),
                  title: Text(
                    sins[index]['name']!,
                    style: TextStyle(
                        fontWeight: FontWeight.bold, color: Colors.red[600]),
                  ),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        sins[index]['description']!,
                        style: const TextStyle(fontSize: 16, color: Colors.black87),
                      ),
                      Text(
                        'المرجع: ${sins[index]['verse']!}',
                        style: const TextStyle(color: Colors.grey),
                      ),
                    ],
                  ),
                  trailing: IconButton(
                    icon: const Icon(Icons.bookmark_border),
                    onPressed: () {
                      Provider.of<AppState>(context, listen: false)
                          .showSnackBar('تم إضافة الخطيئة إلى المفضلة', Colors.red);
                    },
                  ),
                ),
              ),
            );
          },
        ),
      ),
      floatingActionButton: ZoomIn(
        duration: const Duration(milliseconds: 400),
        child: FloatingActionButton(
          heroTag: 'sins_quiz_fab',
          onPressed: () {
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => const SinsQuizPage()),
            );
          },
          backgroundColor: Colors.red[600],
          tooltip: 'اختبار الخطايا',
          child: const Icon(Icons.quiz),
        ),
      ),
    );
  }
}

class SevenVirtuesPage extends StatelessWidget {
  const SevenVirtuesPage({super.key});

  final List<Map<String, dynamic>> virtues = const [
    {
      'name': 'التواضع',
      'description': 'وضع الآخرين قبل النفس والابتعاد عن التكبر',
      'icon': FontAwesomeIcons.handHoldingHeart,
      'verse': 'تواضعوا تحت يد الله القوية لكي يرفعكم في حينه (بطرس الأولى 5:6)',
    },
    {
      'name': 'الكرم',
      'description': 'العطاء بسخاء دون توقع المقابل',
      'icon': FontAwesomeIcons.gift,
      'verse': 'من يعطي بسخاء يزداد، ومن يمسك أكثر من اللازم يفتقر (أمثال 11:24)',
    },
    {
      'name': 'العفة',
      'description': 'النقاء في الأفكار والأفعال',
      'icon': FontAwesomeIcons.dove,
      'verse': 'طوبى للأنقياء القلب، لأنهم سيرون الله (متى 5:8)',
    },
    {
      'name': 'الرضا',
      'description': 'القناعة والامتنان لما يملكه المرء',
      'icon': FontAwesomeIcons.smile,
      'verse': 'لأننا لم نحمل شيئاً إلى العالم، ومن الواضح أننا لا نستطيع أن نخرج بشيء (تيموثاوس الأولى 6:7)',
    },
    {
      'name': 'الاعتدال',
      'description': 'ضبط النفس في الأكل والشرب والرغبات',
      'icon': FontAwesomeIcons.balanceScale,
      'verse': 'كل شيء حلال لي، لكن ليس كل شيء ينفع (كورنثوس الأولى 6:12)',
    },
    {
      'name': 'الصبر',
      'description': 'تحمل الصعوبات بهدوء وثبات',
      'icon': FontAwesomeIcons.hourglassHalf,
      'verse': 'بالصبر تنتظرون وعد الله، لأن الذي يحرث ويدرّس يجب أن يفعل ذلك بروح الرجاء (عبرانيين 10:36)',
    },
    {
      'name': 'الاجتهاد',
      'description': 'العمل الجاد والمثابرة في الواجبات',
      'icon': FontAwesomeIcons.briefcase,
      'verse': 'كل ما تجد يدك لتفعله، فافعله بقوتك (جامعة 9:10)',
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الفضائل السبع'),
        backgroundColor: Colors.purple[600],
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [Colors.purple[50]!, Colors.white],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
        ),
        child: ListView.builder(
          padding: const EdgeInsets.all(16.0),
          itemCount: virtues.length,
          itemBuilder: (context, index) {
            return FadeInUp(
              duration: const Duration(milliseconds: 400),
              child: Card(
                elevation: 4,
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12)),
                child: ListTile(
                  leading: FaIcon(virtues[index]['icon'], color: Colors.purple[600]),
                  title: Text(
                    virtues[index]['name']!,
                    style: TextStyle(
                        fontWeight: FontWeight.bold, color: Colors.purple[600]),
                  ),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        virtues[index]['description']!,
                        style: const TextStyle(fontSize: 16, color: Colors.black87),
                      ),
                      Text(
                        'المرجع: ${virtues[index]['verse']!}',
                        style: const TextStyle(color: Colors.grey),
                      ),
                    ],
                  ),
                  trailing: IconButton(
                    icon: const Icon(Icons.bookmark_border),
                    onPressed: () {
                      Provider.of<AppState>(context, listen: false)
                          .showSnackBar('تم إضافة الفضيلة إلى المفضلة', Colors.purple);
                    },
                  ),
                ),
              ),
            );
          },
        ),
      ),
      floatingActionButton: ZoomIn(
        duration: const Duration(milliseconds: 400),
        child: FloatingActionButton(
          heroTag: 'virtues_quiz_fab',
          onPressed: () {
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => const VirtuesQuizPage()),
            );
          },
          backgroundColor: Colors.purple[600],
          tooltip: 'اختبار الفضائل',
          child: const Icon(Icons.quiz),
        ),
      ),
    );
  }
}

class QuranTafsirPage extends StatelessWidget {
  final String surahName;
  final String verse;

  const QuranTafsirPage({super.key, required this.surahName, required this.verse});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('تفسير سورة $surahName'),
        backgroundColor: Colors.green[600],
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [Colors.teal[50]!, Colors.white],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Text(
                'الآية: $verse',
                style: const TextStyle(
                    fontSize: 20,
                    color: Colors.green,
                    fontWeight: FontWeight.bold),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              const Text(
                'التفسير: هذا التفسير قيد التطوير. سيتم إضافة تفسير ابن كثير لاحقًا.',
                style: TextStyle(fontSize: 16, color: Colors.black87),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class SinsQuizPage extends StatefulWidget {
  const SinsQuizPage({super.key});

  @override
  _SinsQuizPageState createState() => _SinsQuizPageState();
}

class _SinsQuizPageState extends State<SinsQuizPage> {
  int _currentQuestionIndex = 0;
  int _score = 0;

  final List<Map<String, dynamic>> quizQuestions = [
    {
      'question': 'ما هي الخطيئة التي تتعلق بالتكبر والاعتزاز بالنفس؟',
      'answers': ['الكبرياء', 'الطمع', 'الشهوة', 'الحسد'],
      'correctAnswer': 'الكبرياء',
    },
    {
      'question': 'أي خطيئة تصف الرغبة المفرطة في الممتلكات؟',
      'answers': ['الشراهة', 'الطمع', 'الكسل', 'الغضب'],
      'correctAnswer': 'الطمع',
    },
  ];

  void _answerQuestion(String selectedAnswer) {
    if (selectedAnswer == quizQuestions[_currentQuestionIndex]['correctAnswer']) {
      _score++;
      Provider.of<AppState>(context, listen: false)
          .showSnackBar('إجابة صحيحة!', Colors.green);
    } else {
      Provider.of<AppState>(context, listen: false)
          .showSnackBar('إجابة خاطئة', Colors.red);
    }

    setState(() {
      if (_currentQuestionIndex < quizQuestions.length - 1) {
        _currentQuestionIndex++;
      } else {
        _showQuizResult(context);
      }
    });
  }

  void _showQuizResult(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('نتيجة الاختبار'),
        content: Text('لقد حصلت على $_score من ${quizQuestions.length} درجات!'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              setState(() {
                _currentQuestionIndex = 0;
                _score = 0;
              });
            },
            child: const Text('إعادة الاختبار'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final currentQuestion = quizQuestions[_currentQuestionIndex];

    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار الخطايا السبع'),
        backgroundColor: Colors.red[600],
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [Colors.red[50]!, Colors.white],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Text(
                'سؤال ${_currentQuestionIndex + 1} من ${quizQuestions.length}',
                style: TextStyle(fontSize: 18, color: Colors.red[600]),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 20),
              Text(
                currentQuestion['question'],
                style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.red[600]),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 20),
              ...currentQuestion['answers'].map<Widget>((answer) {
                return FadeInUp(
                  duration: const Duration(milliseconds: 400),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8.0),
                    child: ElevatedButton(
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red[600],
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.all(16),
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12)),
                      ),
                      onPressed: () => _answerQuestion(answer),
                      child: Text(answer, style: const TextStyle(fontSize: 18)),
                    ),
                  ),
                );
              }).toList(),
            ],
          ),
        ),
      ),
    );
  }
}

class VirtuesQuizPage extends StatefulWidget {
  const VirtuesQuizPage({super.key});

  @override
  _VirtuesQuizPageState createState() => _VirtuesQuizPageState();
}

class _VirtuesQuizPageState extends State<VirtuesQuizPage> {
  int _currentQuestionIndex = 0;
  int _score = 0;

  final List<Map<String, dynamic>> quizQuestions = [
    {
      'question': 'ما هي الفضيلة التي تتعلق بوضع الآخرين قبل النفس؟',
      'answers': ['التواضع', 'الكرم', 'العفة', 'الصبر'],
      'correctAnswer': 'التواضع',
    },
    {
      'question': 'أي فضيلة تصف العطاء بسخاء دون توقع المقابل؟',
      'answers': ['الاعتدال', 'الكرم', 'الاجتهاد', 'الرضا'],
      'correctAnswer': 'الكرم',
    },
  ];

  void _answerQuestion(String selectedAnswer) {
    if (selectedAnswer == quizQuestions[_currentQuestionIndex]['correctAnswer']) {
      _score++;
      Provider.of<AppState>(context, listen: false)
          .showSnackBar('إجابة صحيحة!', Colors.green);
    } else {
      Provider.of<AppState>(context, listen: false)
          .showSnackBar('إجابة خاطئة', Colors.red);
    }

    setState(() {
      if (_currentQuestionIndex < quizQuestions.length - 1) {
        _currentQuestionIndex++;
      } else {
        _showQuizResult(context);
      }
    });
  }

  void _showQuizResult(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('نتيجة الاختبار'),
        content: Text('لقد حصلت على $_score من ${quizQuestions.length} درجات!'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              setState(() {
                _currentQuestionIndex = 0;
                _score = 0;
              });
            },
            child: const Text('إعادة الاختبار'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final currentQuestion = quizQuestions[_currentQuestionIndex];

    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار الفضائل السبع'),
        backgroundColor: Colors.purple[600],
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [Colors.purple[50]!, Colors.white],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Text(
                'سؤال ${_currentQuestionIndex + 1} من ${quizQuestions.length}',
                style: TextStyle(fontSize: 18, color: Colors.purple[600]),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 20),
              Text(
                currentQuestion['question'],
                style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.purple[600]),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 20),
              ...currentQuestion['answers'].map<Widget>((answer) {
                return FadeInUp(
                  duration: const Duration(milliseconds: 400),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8.0),
                    child: ElevatedButton(
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.purple[600],
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.all(16),
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12)),
                      ),
                      onPressed: () => _answerQuestion(answer),
                      child: Text(answer, style: const TextStyle(fontSize: 18)),
                    ),
                  ),
                );
              }).toList(),
            ],
          ),
        ),
      ),
    );
  }
}