import 'package:flutter/material.dart';
import 'ManageUsersPage.dart';
import 'ManagePostsPage.dart';
import 'ManageReportsPage.dart';
import 'SendAdminNotificationPage.dart';

class AdminPanel extends StatelessWidget {
  const AdminPanel({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('لوحة تحكم المشرف', style: TextStyle(fontFamily: 'Tajawal'))),
      body: ListView(
        padding: const EdgeInsets.all(24),
        children: [
          ListTile(
            leading: const Icon(Icons.group),
            title: const Text('إدارة المستخدمين', style: TextStyle(fontFamily: 'Tajawal')),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () {
              Navigator.push(context, MaterialPageRoute(builder: (_) => const ManageUsersPage()));
            },
          ),
          ListTile(
            leading: const Icon(Icons.post_add),
            title: const Text('إدارة المنشورات', style: TextStyle(fontFamily: 'Tajawal')),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () {
              Navigator.push(context, MaterialPageRoute(builder: (_) => const ManagePostsPage()));
            },
          ),
          ListTile(
            leading: const Icon(Icons.report),
            title: const Text('مراجعة البلاغات', style: TextStyle(fontFamily: 'Tajawal')),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () {
              Navigator.push(context, MaterialPageRoute(builder: (_) => const ManageReportsPage()));
            },
          ),
          ListTile(
            leading: const Icon(Icons.notifications_active),
            title: const Text('إرسال إشعار إداري', style: TextStyle(fontFamily: 'Tajawal')),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () {
              Navigator.push(context, MaterialPageRoute(builder: (_) => const SendAdminNotificationPage()));
            },
          ),
          const Divider(),
          ListTile(
            leading: const Icon(Icons.message),
            title: const Text('إدارة الرسائل', style: TextStyle(fontFamily: 'Tajawal')),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () {
              Navigator.pushNamed(context, '/messenger');
            },
          ),
          ListTile(
            leading: const Icon(Icons.ondemand_video),
            title: const Text('إدارة الفيديوهات', style: TextStyle(fontFamily: 'Tajawal')),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () {
              Navigator.pushNamed(context, '/videos');
            },
          ),
          ListTile(
            leading: const Icon(Icons.map),
            title: const Text('إدارة الخرائط', style: TextStyle(fontFamily: 'Tajawal')),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () {
              Navigator.pushNamed(context, '/map');
            },
          ),
          ListTile(
            leading: const Icon(Icons.store),
            title: const Text('إدارة المتجر الإلكتروني', style: TextStyle(fontFamily: 'Tajawal')),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () {
              Navigator.pushNamed(context, '/ecommerce');
            },
          ),
          ListTile(
            leading: const Icon(Icons.emoji_events),
            title: const Text('إدارة الجوائز', style: TextStyle(fontFamily: 'Tajawal')),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () {
              Navigator.pushNamed(context, '/rewards');
            },
          ),
          ListTile(
            leading: const Icon(Icons.sports_esports),
            title: const Text('إدارة الألعاب', style: TextStyle(fontFamily: 'Tajawal')),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () {
              Navigator.pushNamed(context, '/games');
            },
          ),
          ListTile(
            leading: const Icon(Icons.poll),
            title: const Text('إدارة الاستطلاعات', style: TextStyle(fontFamily: 'Tajawal')),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () {
              Navigator.pushNamed(context, '/polls');
            },
          ),
          ListTile(
            leading: const Icon(Icons.school),
            title: const Text('إدارة الدورات', style: TextStyle(fontFamily: 'Tajawal')),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () {
              Navigator.pushNamed(context, '/courses');
            },
          ),
          ListTile(
            leading: const Icon(Icons.auto_stories),
            title: const Text('إدارة القصص', style: TextStyle(fontFamily: 'Tajawal')),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () {
              Navigator.pushNamed(context, '/stories');
            },
          ),
          ListTile(
            leading: const Icon(Icons.settings),
            title: const Text('الإعدادات العامة', style: TextStyle(fontFamily: 'Tajawal')),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () {
              Navigator.pushNamed(context, '/settings');
            },
          ),
          ListTile(
            leading: const Icon(Icons.notifications),
            title: const Text('إدارة الإشعارات', style: TextStyle(fontFamily: 'Tajawal')),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () {
              Navigator.pushNamed(context, '/notifications');
            },
          ),
          ListTile(
            leading: const Icon(Icons.person),
            title: const Text('إدارة الملف الشخصي', style: TextStyle(fontFamily: 'Tajawal')),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () {
              Navigator.pushNamed(context, '/profile');
            },
          ),
          ListTile(
            leading: const Icon(Icons.work),
            title: const Text('إدارة المهن', style: TextStyle(fontFamily: 'Tajawal')),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () {
              Navigator.pushNamed(context, '/professions');
            },
          ),
          ListTile(
            leading: const Icon(Icons.volunteer_activism),
            title: const Text('إدارة التبرعات', style: TextStyle(fontFamily: 'Tajawal')),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () {
              Navigator.pushNamed(context, '/donations');
            },
          ),
          ListTile(
            leading: const Icon(Icons.local_shipping),
            title: const Text('إدارة الطلبات', style: TextStyle(fontFamily: 'Tajawal')),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () {
              Navigator.pushNamed(context, '/requests');
            },
          ),
          ListTile(
            leading: const Icon(Icons.health_and_safety),
            title: const Text('إدارة الصحة', style: TextStyle(fontFamily: 'Tajawal')),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () {
              Navigator.pushNamed(context, '/health');
            },
          ),
        ],
      ),
    );
  }
}
