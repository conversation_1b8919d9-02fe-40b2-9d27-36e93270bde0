import 'package:flutter/material.dart';

/// أداة للتحقق من صحة المدخلات في النماذج
/// توفر دوال للتحقق من أنواع مختلفة من البيانات
class InputValidator {
  /// التحقق من أن الحقل غير فارغ
  static String? validateRequired(String? value, {String? fieldName}) {
    if (value == null || value.trim().isEmpty) {
      return '${fieldName ?? 'الحقل'} مطلوب';
    }
    return null;
  }

  /// التحقق من صحة البريد الإلكتروني
  static String? validateEmail(String? value, {bool isRequired = true}) {
    if (isRequired && (value == null || value.trim().isEmpty)) {
      return 'البريد الإلكتروني مطلوب';
    }
    
    if (value != null && value.trim().isNotEmpty) {
      final emailRegExp = RegExp(
        r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
      );
      if (!emailRegExp.hasMatch(value.trim())) {
        return 'يرجى إدخال بريد إلكتروني صالح';
      }
    }
    
    return null;
  }

  /// التحقق من صحة رقم الهاتف
  static String? validatePhone(String? value, {bool isRequired = true}) {
    if (isRequired && (value == null || value.trim().isEmpty)) {
      return 'رقم الهاتف مطلوب';
    }
    
    if (value != null && value.trim().isNotEmpty) {
      // يمكن تعديل هذا التعبير المنتظم حسب تنسيق الهاتف المطلوب
      final phoneRegExp = RegExp(r'^\+?[0-9]{8,15}$');
      if (!phoneRegExp.hasMatch(value.trim())) {
        return 'يرجى إدخال رقم هاتف صالح';
      }
    }
    
    return null;
  }

  /// التحقق من صحة كلمة المرور
  static String? validatePassword(String? value, {bool isRequired = true, int minLength = 8}) {
    if (isRequired && (value == null || value.trim().isEmpty)) {
      return 'كلمة المرور مطلوبة';
    }
    
    if (value != null && value.trim().isNotEmpty) {
      if (value.length < minLength) {
        return 'يجب أن تتكون كلمة المرور من $minLength أحرف على الأقل';
      }
      
      // التحقق من قوة كلمة المرور
      bool hasUppercase = value.contains(RegExp(r'[A-Z]'));
      bool hasLowercase = value.contains(RegExp(r'[a-z]'));
      bool hasDigit = value.contains(RegExp(r'[0-9]'));
      bool hasSpecialChar = value.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'));
      
      if (!(hasUppercase && hasLowercase && hasDigit)) {
        return 'يجب أن تحتوي كلمة المرور على حروف كبيرة وصغيرة وأرقام';
      }
      
      if (!hasSpecialChar) {
        return 'يجب أن تحتوي كلمة المرور على حرف خاص واحد على الأقل';
      }
    }
    
    return null;
  }

  /// التحقق من تطابق كلمتي المرور
  static String? validatePasswordMatch(String? password, String? confirmPassword) {
    if (confirmPassword == null || confirmPassword.trim().isEmpty) {
      return 'تأكيد كلمة المرور مطلوب';
    }
    
    if (password != confirmPassword) {
      return 'كلمات المرور غير متطابقة';
    }
    
    return null;
  }

  /// التحقق من صحة الرقم
  static String? validateNumber(String? value, {
    bool isRequired = true,
    double? min,
    double? max,
    bool allowDecimal = true,
  }) {
    if (isRequired && (value == null || value.trim().isEmpty)) {
      return 'الحقل مطلوب';
    }
    
    if (value != null && value.trim().isNotEmpty) {
      final numericValue = allowDecimal
          ? double.tryParse(value.trim())
          : int.tryParse(value.trim());
      
      if (numericValue == null) {
        return 'يرجى إدخال رقم صالح';
      }
      
      if (min != null && numericValue < min) {
        return 'يجب أن يكون الرقم أكبر من أو يساوي $min';
      }
      
      if (max != null && numericValue > max) {
        return 'يجب أن يكون الرقم أقل من أو يساوي $max';
      }
    }
    
    return null;
  }

  /// التحقق من صحة التاريخ
  static String? validateDate(String? value, {
    bool isRequired = true,
    DateTime? minDate,
    DateTime? maxDate,
  }) {
    if (isRequired && (value == null || value.trim().isEmpty)) {
      return 'التاريخ مطلوب';
    }
    
    if (value != null && value.trim().isNotEmpty) {
      final date = DateTime.tryParse(value.trim());
      
      if (date == null) {
        return 'يرجى إدخال تاريخ صالح';
      }
      
      if (minDate != null && date.isBefore(minDate)) {
        return 'يجب أن يكون التاريخ بعد ${_formatDate(minDate)}';
      }
      
      if (maxDate != null && date.isAfter(maxDate)) {
        return 'يجب أن يكون التاريخ قبل ${_formatDate(maxDate)}';
      }
    }
    
    return null;
  }

  /// التحقق من صحة URL
  static String? validateUrl(String? value, {bool isRequired = true}) {
    if (isRequired && (value == null || value.trim().isEmpty)) {
      return 'الرابط مطلوب';
    }
    
    if (value != null && value.trim().isNotEmpty) {
      final urlRegExp = RegExp(
        r'^(https?:\/\/)?(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$',
      );
      if (!urlRegExp.hasMatch(value.trim())) {
        return 'يرجى إدخال رابط صالح';
      }
    }
    
    return null;
  }

  /// التحقق من صحة الاسم
  static String? validateName(String? value, {bool isRequired = true}) {
    if (isRequired && (value == null || value.trim().isEmpty)) {
      return 'الاسم مطلوب';
    }
    
    if (value != null && value.trim().isNotEmpty) {
      if (value.trim().length < 2) {
        return 'يجب أن يتكون الاسم من حرفين على الأقل';
      }
      
      // التحقق من عدم وجود أرقام أو رموز خاصة في الاسم
      final nameRegExp = RegExp(r'^[\u0600-\u06FFa-zA-Z\s]+$');
      if (!nameRegExp.hasMatch(value.trim())) {
        return 'يجب أن يحتوي الاسم على أحرف فقط';
      }
    }
    
    return null;
  }

  /// التحقق من صحة رقم الهوية الوطنية
  static String? validateNationalId(String? value, {bool isRequired = true}) {
    if (isRequired && (value == null || value.trim().isEmpty)) {
      return 'رقم الهوية مطلوب';
    }
    
    if (value != null && value.trim().isNotEmpty) {
      // يمكن تعديل هذا التعبير المنتظم حسب تنسيق الهوية المطلوب
      final idRegExp = RegExp(r'^[0-9]{10}$');
      if (!idRegExp.hasMatch(value.trim())) {
        return 'يرجى إدخال رقم هوية صالح مكون من 10 أرقام';
      }
    }
    
    return null;
  }

  /// تنسيق التاريخ للعرض
  static String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }
}

/// امتداد لـ Form للتحقق من صحة جميع الحقول
extension FormValidation on GlobalKey<FormState> {
  /// التحقق من صحة جميع الحقول وعرض رسالة خطأ
  bool validateAndShowError(BuildContext context) {
    if (currentState?.validate() ?? false) {
      return true;
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى تصحيح الأخطاء في النموذج'),
          backgroundColor: Colors.red,
        ),
      );
      return false;
    }
  }
}

/// مثال لاستخدام أداة التحقق في نموذج
class ValidationExample extends StatefulWidget {
  const ValidationExample({super.key});

  @override
  State<ValidationExample> createState() => _ValidationExampleState();
}

class _ValidationExampleState extends State<ValidationExample> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _nameController = TextEditingController();
  final _phoneController = TextEditingController();

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _nameController.dispose();
    _phoneController.dispose();
    super.dispose();
  }

  void _submitForm() {
    if (_formKey.validateAndShowError(context)) {
      // تنفيذ إرسال النموذج
      debugPrint('تم التحقق من صحة النموذج بنجاح');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('مثال التحقق من الصحة')),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: ListView(
            children: [
              TextFormField(
                controller: _nameController,
                decoration: const InputDecoration(
                  labelText: 'الاسم',
                  hintText: 'أدخل اسمك الكامل',
                ),
                validator: (value) => InputValidator.validateName(value),
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _emailController,
                decoration: const InputDecoration(
                  labelText: 'البريد الإلكتروني',
                  hintText: 'أدخل بريدك الإلكتروني',
                ),
                keyboardType: TextInputType.emailAddress,
                validator: (value) => InputValidator.validateEmail(value),
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _phoneController,
                decoration: const InputDecoration(
                  labelText: 'رقم الهاتف',
                  hintText: 'أدخل رقم هاتفك',
                ),
                keyboardType: TextInputType.phone,
                validator: (value) => InputValidator.validatePhone(value),
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _passwordController,
                decoration: const InputDecoration(
                  labelText: 'كلمة المرور',
                  hintText: 'أدخل كلمة المرور',
                ),
                obscureText: true,
                validator: (value) => InputValidator.validatePassword(value),
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _confirmPasswordController,
                decoration: const InputDecoration(
                  labelText: 'تأكيد كلمة المرور',
                  hintText: 'أعد إدخال كلمة المرور',
                ),
                obscureText: true,
                validator: (value) => InputValidator.validatePasswordMatch(
                  _passwordController.text,
                  value,
                ),
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: _submitForm,
                child: const Text('إرسال'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
