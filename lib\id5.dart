import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:share_plus/share_plus.dart';
import 'package:intl/intl.dart';
import 'package:http/http.dart' as http;
import 'package:provider/provider.dart';
import 'package:socket_io_client/socket_io_client.dart' as io;
import 'package:shimmer/shimmer.dart';
import 'appstate.dart';

import 'models/user_profile_model.dart';

// خدمة إدارة الملفات الشخصية
class ProfileService {
  final String baseUrl;
  final String authToken;
  io.Socket? socket;

  ProfileService({required this.baseUrl, required this.authToken});

  Future<List<UserProfile>> fetchProfiles({
    String search = '',
    String filter = 'all',
    String sort = 'newest',
    int skip = 0,
    int limit = 10,
  }) async {
    final response = await http.get(
      Uri.parse('$baseUrl/profiles?search=$search&filter=$filter&sort=$sort&skip=$skip&limit=$limit'),
      headers: {'Authorization': 'Bearer $authToken'},
    );
    if (response.statusCode == 200) {
      return (jsonDecode(response.body) as List)
          .map((item) => UserProfile.fromJson(item))
          .toList();
    } else {
      throw Exception('فشل في جلب الملفات الشخصية: ${response.body}');
    }
  }

  Future<List<Map<String, dynamic>>> fetchNotifications() async {
    final response = await http.get(
      Uri.parse('$baseUrl/notifications'),
      headers: {'Authorization': 'Bearer $authToken'},
    );
    if (response.statusCode == 200) {
      return List<Map<String, dynamic>>.from(jsonDecode(response.body));
    } else {
      throw Exception('فشل في جلب الإشعارات: ${response.body}');
    }
  }

  Future<void> createProfile(
      String name,
      String age,
      String nationality,
      String maritalStatus,
      String bio,
      String location,
      String category,
      File file,
      String creatorId) async {
    var request = http.MultipartRequest('POST', Uri.parse('$baseUrl/profiles'));
    request.headers['Authorization'] = 'Bearer $authToken';
    request.fields['name'] = name;
    request.fields['age'] = age;
    request.fields['nationality'] = nationality;
    request.fields['maritalStatus'] = maritalStatus;
    request.fields['bio'] = bio;
    request.fields['location'] = location;
    request.fields['category'] = category;
    request.fields['creatorId'] = creatorId;
    request.files.add(await http.MultipartFile.fromPath('file', file.path));
    final response = await request.send();
    if (response.statusCode != 201) {
      throw Exception('فشل في إنشاء الملف الشخصي: ${response.reasonPhrase}');
    }
  }

  Future<void> deleteProfile(String id) async {
    final response = await http.delete(
      Uri.parse('$baseUrl/profiles/$id'),
      headers: {'Authorization': 'Bearer $authToken'},
    );
    if (response.statusCode != 200) {
      throw Exception('فشل في الحذف: ${response.body}');
    }
  }

  Future<void> requestContact(String profileId, String requesterId) async {
    final response = await http.post(
      Uri.parse('$baseUrl/contact-requests'),
      headers: {
        'Authorization': 'Bearer $authToken',
        'Content-Type': 'application/json'
      },
      body: jsonEncode({'profileId': profileId, 'requesterId': requesterId}),
    );
    if (response.statusCode != 201) {
      throw Exception('فشل في إرسال طلب التواصل: ${response.body}');
    }
  }

  void setupSocketListeners(io.Socket socket, Function(dynamic) onNewProfile) {
    this.socket = socket;
    socket.on('new_profile', onNewProfile);
  }
}

class Rayan5 extends StatelessWidget {
  const Rayan5({super.key});

  @override
  Widget build(BuildContext context) {
    return const ProfilesScreen();
  }
}

class ProfilesScreen extends StatefulWidget {
  const ProfilesScreen({super.key});

  @override
  _ProfilesScreenState createState() => _ProfilesScreenState();
}

class _ProfilesScreenState extends State<ProfilesScreen> with TickerProviderStateMixin {
  final PageController _adController = PageController();
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final ProfileService _profileService =
  ProfileService(baseUrl: AppState.getBackendUrl(), authToken: defaultAuthToken);
  List<UserProfile> _userProfiles = [];
  List<Map<String, dynamic>> _notifications = [];
  int _currentAd = 0;
  int _selectedCategory = 0;
  int _visibleAds = 10;
  String _selectedSort = 'newest';
  String _selectedFilter = 'all';
  bool _isLoading = true;
  bool _isLoadingMore = false;
  bool _showScrollToTop = false;
  bool _showFeaturedOnly = false;
  Timer? _timer;
  final String _currentUserId = 'user1';

  final List<String> adImages = const [
    'https://via.placeholder.com/800x400',
    'https://via.placeholder.com/800x400',
    'https://via.placeholder.com/800x400',
  ];

  final List<Map<String, dynamic>> categories = const [
    {'icon': Icons.male, 'title': 'رجال', 'count': 45, 'filter': 'man'},
    {'icon': Icons.female, 'title': 'نساء', 'count': 32, 'filter': 'woman'},
  ];

  @override
  void initState() {
    super.initState();
    _startAutoScroll();
    _scrollController.addListener(_scrollListener);
    _searchController.addListener(() => setState(() {}));
    final appState = Provider.of<AppState>(context, listen: false);
    if (appState.socket != null) {
      _profileService.setupSocketListeners(appState.socket!, (data) {
        _fetchProfiles();
        setState(() {
          _notifications.add({
            'message': 'ملف شخصي جديد: ${data['name']}',
            'date': DateTime.now().toString(),
            'read': false
          });
        });
      });
    }
    _fetchProfiles();
    _fetchNotifications();
  }

  void _startAutoScroll() {
    _timer = Timer.periodic(const Duration(seconds: 5), (timer) {
      if (!mounted) return;
      setState(() => _currentAd = (_currentAd + 1) % adImages.length);
      _adController.animateToPage(_currentAd,
          duration: const Duration(milliseconds: 500), curve: Curves.easeIn);
    });
  }

  void _scrollListener() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 50 &&
        !_isLoadingMore) {
      _loadMoreProfiles();
    }
    setState(() {
      _showScrollToTop = _scrollController.position.pixels > 200;
    });
  }

  Future<void> _fetchProfiles() async {
    setState(() => _isLoading = true);
    try {
      _userProfiles = await _profileService.fetchProfiles(
        search: _searchController.text,
        filter: _selectedFilter,
        sort: _selectedSort,
      );
      setState(() => _isLoading = false);
    } catch (e) {
      _showSnackBar('خطأ في جلب الملفات الشخصية: $e', Colors.red);
      setState(() => _isLoading = false);
    }
  }

  Future<void> _fetchNotifications() async {
    try {
      _notifications = await _profileService.fetchNotifications();
      setState(() {});
    } catch (e) {
      _showSnackBar('خطأ في جلب الإشعارات: $e', Colors.red);
    }
  }

  Future<void> _loadMoreProfiles() async {
    setState(() => _isLoadingMore = true);
    try {
      final newProfiles = await _profileService.fetchProfiles(
        search: _searchController.text,
        filter: _selectedFilter,
        sort: _selectedSort,
        skip: _visibleAds,
      );
      setState(() {
        _userProfiles.addAll(newProfiles);
        _visibleAds += 10;
        _isLoadingMore = false;
      });
    } catch (e) {
      _showSnackBar('خطأ في تحميل المزيد: $e', Colors.red);
      setState(() => _isLoadingMore = false);
    }
  }

  Future<void> _refreshData() async {
    HapticFeedback.lightImpact();
    setState(() => _visibleAds = 10);
    await _fetchProfiles();
    await _fetchNotifications();
  }

  @override
  void dispose() {
    _timer?.cancel();
    _adController.dispose();
    _scrollController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _showSnackBar(String message, [Color? backgroundColor]) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: backgroundColor ?? Colors.teal,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        elevation: 6,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return Scaffold(
      appBar: AppBar(
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: isDarkMode
                  ? [Colors.teal[900]!, Colors.pink[700]!]
                  : [Colors.teal, Colors.pinkAccent],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
        ),
        title: TextField(
          controller: _searchController,
          decoration: InputDecoration(
            hintText: 'ابحث عن شريك لروحك...',
            border: InputBorder.none,
            hintStyle: const TextStyle(color: Colors.white70),
            filled: true,
            fillColor: Colors.white24,
            prefixIcon: const Icon(Icons.search, color: Colors.white),
            suffixIcon: _searchController.text.isNotEmpty
                ? IconButton(
                icon: const Icon(Icons.clear, color: Colors.white),
                onPressed: () {
                  setState(() => _searchController.clear());
                  HapticFeedback.selectionClick();
                })
                : null,
          ),
          style: const TextStyle(color: Colors.white),
          onSubmitted: (_) => _fetchProfiles(),
        ),
        actions: [
          IconButton(
            icon: Stack(
              children: [
                const Icon(Icons.notifications, color: Colors.white),
                if (_notifications.isNotEmpty)
                  Positioned(
                    right: 0,
                    child: Container(
                      padding: const EdgeInsets.all(2),
                      decoration: const BoxDecoration(
                          color: Colors.red, shape: BoxShape.circle),
                      constraints: const BoxConstraints(minWidth: 12, minHeight: 12),
                      child: Text(
                        '${_notifications.length}',
                        style: const TextStyle(color: Colors.white, fontSize: 8),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
              ],
            ),
            onPressed: () {
              HapticFeedback.selectionClick();
              _showNotifications(context);
            },
          ),
          PopupMenuButton<String>(
            itemBuilder: (context) => [
              const PopupMenuItem(value: 'newest', child: Text('الأحدث')),
              const PopupMenuItem(value: 'age_asc', child: Text('العمر: الأصغر')),
              const PopupMenuItem(value: 'age_desc', child: Text('العمر: الأكبر')),
            ],
            onSelected: (value) {
              HapticFeedback.selectionClick();
              setState(() {
                _selectedSort = value;
                _fetchProfiles();
              });
            },
          ),
        ],
      ),
      body: Stack(
        children: [
          RefreshIndicator(
            onRefresh: _refreshData,
            color: Colors.teal,
            backgroundColor: isDarkMode ? Colors.grey[900] : Colors.white,
            child: SingleChildScrollView(
              controller: _scrollController,
              child: Column(
                children: [
                  _buildFilterChips(),
                  _buildAdsCarousel(),
                  _buildCategories(),
                  _buildFeaturedProfiles(),
                  if (_visibleAds < _userProfiles.length) _buildLoadMoreButton(),
                  const SizedBox(height: 80),
                ],
              ),
            ),
          ),
          if (_isLoading) _buildSkeletonLoader(),
          if (_showScrollToTop)
            Positioned(
              bottom: 100,
              right: 16,
              child: FloatingActionButton(
                mini: true,
                backgroundColor: Colors.teal,
                onPressed: () {
                  HapticFeedback.mediumImpact();
                  _scrollController.animateTo(
                    0,
                    duration: const Duration(milliseconds: 500),
                    curve: Curves.easeInOut,
                  );
                },
                child: const Icon(Icons.arrow_upward, color: Colors.white),
              ),
            ),
        ],
      ),
      floatingActionButton: _buildFloatingActionButtons(),
    );
  }

  Widget _buildSkeletonLoader() {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: Column(
        children: [
          Container(
            height: 200,
            margin: const EdgeInsets.all(8),
            color: Colors.white,
          ),
          Container(
            height: 50,
            margin: const EdgeInsets.symmetric(horizontal: 8),
            color: Colors.white,
          ),
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: 0.65,
              crossAxisSpacing: 8,
              mainAxisSpacing: 8,
            ),
            itemCount: 6,
            itemBuilder: (context, index) => Container(
              margin: const EdgeInsets.all(8),
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChips() {
    return Container(
      height: 50,
      padding: const EdgeInsets.symmetric(horizontal: 8),
      child: Row(
        children: [
          AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: _showFeaturedOnly ? Colors.teal[100] : Colors.grey[200],
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.2),
                  spreadRadius: 1,
                  blurRadius: 3,
                ),
              ],
            ),
            child: GestureDetector(
              onTap: () {
                HapticFeedback.selectionClick();
                setState(() {
                  _showFeaturedOnly = !_showFeaturedOnly;
                  _fetchProfiles();
                });
              },
              child: Row(
                children: [
                  Icon(
                    Icons.star,
                    size: 20,
                    color: _showFeaturedOnly ? Colors.teal[800] : Colors.grey,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    'مميز فقط',
                    style: TextStyle(
                      color: _showFeaturedOnly ? Colors.teal[800] : Colors.grey,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ),
          Expanded(
            child: ListView(
              scrollDirection: Axis.horizontal,
              children: [
                _buildFilterChip('الكل', 'all'),
                ...categories.map((category) =>
                    _buildFilterChip(category['title'] as String, category['filter'] as String)),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 4),
      child: FilterChip(
        label: Text(label),
        selected: _selectedFilter == value,
        onSelected: (v) {
          HapticFeedback.selectionClick();
          setState(() {
            _selectedFilter = value;
            _fetchProfiles();
          });
        },
        selectedColor: Colors.teal[100],
        checkmarkColor: Colors.teal[800],
        elevation: _selectedFilter == value ? 4 : 1,
        pressElevation: 8,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      ),
    );
  }

  Widget _buildAdsCarousel() {
    return Column(
      children: [
        SizedBox(
          height: 200,
          child: PageView.builder(
            controller: _adController,
            itemCount: adImages.length,
            onPageChanged: (index) => setState(() => _currentAd = index),
            itemBuilder: (context, index) => GestureDetector(
              onTap: () {
                HapticFeedback.selectionClick();
                _showSnackBar('تم النقر على الإعلان الترويجي $index');
              },
              child: Transform(
                transform: Matrix4.identity()
                  ..setEntry(3, 2, 0.001)
                  ..rotateY(0.1 * (_currentAd - index)),
                alignment: Alignment.center,
                child: CachedNetworkImage(
                  imageUrl: adImages[index],
                  fit: BoxFit.cover,
                  placeholder: (context, url) => Shimmer.fromColors(
                    baseColor: Colors.grey[300]!,
                    highlightColor: Colors.grey[100]!,
                    child: Container(color: Colors.white),
                  ),
                  errorWidget: (context, url, error) =>
                      const Icon(Icons.error, color: Colors.grey),
                ),
              ),
            ),
          ),
        ),
        const SizedBox(height: 8),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: List.generate(
            adImages.length,
                (index) => AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              margin: const EdgeInsets.symmetric(horizontal: 4),
              width: _currentAd == index ? 20 : 8,
              height: 8,
              decoration: BoxDecoration(
                color: _currentAd == index ? Colors.teal : Colors.grey,
                borderRadius: BorderRadius.circular(4),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.2),
                    spreadRadius: 1,
                    blurRadius: 3,
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildCategories() {
    return Container(
      height: 120,
      padding: const EdgeInsets.symmetric(horizontal: 8),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: categories.length,
        itemBuilder: (context, index) => _buildCategoryItem(index),
      ),
    );
  }

  Widget _buildCategoryItem(int index) {
    return GestureDetector(
      onTap: () {
        HapticFeedback.selectionClick();
        setState(() => _selectedCategory = index);
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        width: 100,
        padding: const EdgeInsets.all(8),
        child: Column(
          children: [
            Container(
              decoration: BoxDecoration(
                color: _selectedCategory == index
                    ? Colors.teal[50]
                    : Colors.grey[200],
                borderRadius: BorderRadius.circular(15),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.2),
                    spreadRadius: 1,
                    blurRadius: 3,
                    offset: const Offset(2, 2),
                  ),
                  BoxShadow(
                    color: Colors.white.withOpacity(0.5),
                    spreadRadius: 1,
                    blurRadius: 3,
                    offset: const Offset(-2, -2),
                  ),
                ],
              ),
              padding: const EdgeInsets.all(12),
              child: Icon(
                categories[index]['icon'],
                color: _selectedCategory == index ? Colors.teal : Colors.grey[700],
                size: 30,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '${categories[index]['title']} (${categories[index]['count']})',
              style: TextStyle(
                color: _selectedCategory == index ? Colors.teal : Colors.black,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeaturedProfiles() {
    final filteredProfiles = _userProfiles.where((profile) {
      final searchQuery = _searchController.text.toLowerCase();
      return (profile.name.toLowerCase().contains(searchQuery) ||
          profile.bio.toLowerCase().contains(searchQuery)) &&
          (_selectedFilter == 'all' || profile.category == _selectedFilter) &&
          (!_showFeaturedOnly || profile.isFeatured);
    }).toList();

    filteredProfiles.sort((a, b) {
      if (_selectedSort == 'age_asc') {
        return (int.tryParse(a.age) ?? 0).compareTo(int.tryParse(b.age) ?? 0);
      } else if (_selectedSort == 'age_desc')
        return (int.tryParse(b.age) ?? 0).compareTo(int.tryParse(a.age) ?? 0);
      return b.date.compareTo(a.date);
    });

    return Padding(
      padding: const EdgeInsets.all(8),
      child: filteredProfiles.isEmpty
          ? Center(
        child: Text(
          "لا توجد ملفات شخصية متاحة",
          style: TextStyle(color: Colors.grey[600]),
        ),
      )
          : GridView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          childAspectRatio: 0.65,
          crossAxisSpacing: 8,
          mainAxisSpacing: 8,
        ),
        itemCount:
        filteredProfiles.length > _visibleAds ? _visibleAds : filteredProfiles.length,
        itemBuilder: (context, index) => FadeInAnimation(
          delay: index * 0.1,
          child: _buildProfileItem(filteredProfiles[index]),
        ),
      ),
    );
  }

  Widget _buildProfileItem(UserProfile profile) {
    return Stack(
      children: [
        GestureDetector(
          onLongPress: () {
            HapticFeedback.selectionClick();
            _showProfilePreview(context, profile);
          },
          child: Card(
            elevation: 0,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            child: InkWell(
              onTap: () {
                HapticFeedback.selectionClick();
                _showProfileDetails(context, profile);
              },
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  color: Theme.of(context).cardColor,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withOpacity(0.2),
                      spreadRadius: 1,
                      blurRadius: 3,
                      offset: const Offset(2, 2),
                    ),
                    BoxShadow(
                      color: Colors.white.withOpacity(0.5),
                      spreadRadius: 1,
                      blurRadius: 3,
                      offset: const Offset(-2, -2),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: ClipRRect(
                        borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
                        child: CachedNetworkImage(
                          imageUrl: '${_profileService.baseUrl}${profile.fileUrl}',
                          fit: BoxFit.cover,
                          width: double.infinity,
                          placeholder: (context, url) => Shimmer.fromColors(
                            baseColor: Colors.grey[300]!,
                            highlightColor: Colors.grey[100]!,
                            child: Container(color: Colors.white),
                          ),
                          errorWidget: (context, url, error) =>
                              const Icon(Icons.error, color: Colors.grey),
                        ),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.all(8),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            profile.name,
                            style: const TextStyle(fontWeight: FontWeight.bold),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'العمر: ${profile.age}',
                            style: const TextStyle(color: Colors.teal, fontSize: 14),
                          ),
                          const SizedBox(height: 4),
                          Row(
                            children: [
                              const Icon(Icons.location_on, size: 16, color: Colors.grey),
                              Flexible(
                                child: Text(
                                  profile.location,
                                  style: const TextStyle(color: Colors.grey),
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'التوافق: ${profile.category == _selectedFilter ? 'عالي' : 'متوسط'}',
                            style: const TextStyle(color: Colors.pinkAccent, fontSize: 12),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
        if (profile.isFeatured)
          Positioned(
            top: 8,
            left: 8,
            child: DottedBorder(
              color: Colors.amber,
              strokeWidth: 2,
              dashPattern: const [4, 4],
              borderType: BorderType.RRect,
              radius: const Radius.circular(8),
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                color: Colors.amber.withOpacity(0.2),
                child: const Text(
                  'مميز',
                  style: TextStyle(color: Colors.amber, fontSize: 12),
                ),
              ),
            ),
          ),
        Positioned(
          top: 8,
          right: 8,
          child: Row(
            children: [
              IconButton(
                icon: Icon(
                  profile.isFavorite ? Icons.favorite : Icons.favorite_border,
                  size: 20,
                  color: profile.isFavorite ? Colors.red : Colors.teal,
                ),
                onPressed: () {
                  HapticFeedback.selectionClick();
                  setState(() {
                    profile.isFavorite = !profile.isFavorite;
                  });
                  _showSnackBar(
                    profile.isFavorite
                        ? 'تمت الإضافة إلى المفضلة'
                        : 'تمت الإزالة من المفضلة',
                    Colors.teal,
                  );
                },
              ),
              IconButton(
                icon: const Icon(Icons.share, size: 20, color: Colors.teal),
                onPressed: () {
                  HapticFeedback.selectionClick();
                  Share.share('${profile.name} - ${profile.age} - ${profile.location}');
                },
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildLoadMoreButton() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: ScaleTransitionButton(
        onPressed: _isLoadingMore ? null : _loadMoreProfiles,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.teal,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
          padding: const EdgeInsets.symmetric(horizontal: 40, vertical: 12),
          elevation: 4,
        ),
        child: _isLoadingMore
            ? const CircularProgressIndicator(color: Colors.white)
            : const Text('تحميل المزيد', style: TextStyle(color: Colors.white)),
      ),
    );
  }

  Widget _buildFloatingActionButtons() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        Tooltip(
          message: 'ملفاتي',
          child: ScaleTransitionButton(
            onPressed: () {
              HapticFeedback.selectionClick();
              _showMyProfiles(context);
            },
            style: ButtonStyle(
              backgroundColor: WidgetStateProperty.all(Colors.teal),
              elevation: WidgetStateProperty.all(4),
              // ملاحظة: تحقق مما إذا كان heroTag مطلوبًا في ScaleTransitionButton
            ),
            child: const Icon(Icons.list_alt, color: Colors.white),
          ),
        ),
        const SizedBox(height: 16),
        Tooltip(
          message: 'إنشاء ملف شخصي',
          child: ScaleTransitionButton(
            onPressed: () {
              HapticFeedback.selectionClick();
              _showCreateProfileDialog(context);
            },
            style: ButtonStyle(
              backgroundColor: WidgetStateProperty.all(Colors.teal),
              elevation: WidgetStateProperty.all(4),
              // ملاحظة: تحقق مما إذا كان heroTag مطلوبًا في ScaleTransitionButton
            ),
            child: const Icon(Icons.post_add, color: Colors.white),
          ),
        ),
      ],
    );
  }

  void _showCreateProfileDialog(BuildContext context) {
    final nameController = TextEditingController();
    final ageController = TextEditingController();
    final nationalityController = TextEditingController();
    final maritalStatusController = TextEditingController();
    final bioController = TextEditingController();
    final locationController = TextEditingController();
    String selectedCategory = categories[0]['title'];
    File? selectedFile;

    showGeneralDialog(
      context: context,
      barrierDismissible: true,
      barrierLabel: MaterialLocalizations.of(context).modalBarrierDismissLabel,
      barrierColor: Colors.black54,
      transitionDuration: const Duration(milliseconds: 300),
      pageBuilder: (context, anim1, anim2) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        title: Container(
          padding: const EdgeInsets.all(10),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [Colors.teal[700]!, Colors.pink[400]!],
            ),
            borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: const Text(
            "إنشاء ملف شخصي",
            style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
            textAlign: TextAlign.center,
          ),
        ),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDialogTextField(
                label: "الاسم:",
                controller: nameController,
                hint: "أدخل الاسم...",
              ),
              _buildDialogTextField(
                label: "العمر:",
                controller: ageController,
                hint: "أدخل العمر...",
                keyboardType: TextInputType.number,
              ),
              _buildDialogTextField(
                label: "الجنسية:",
                controller: nationalityController,
                hint: "أدخل الجنسية...",
              ),
              _buildDialogTextField(
                label: "الحالة الاجتماعية:",
                controller: maritalStatusController,
                hint: "أدخل الحالة الاجتماعية...",
              ),
              _buildDialogTextField(
                label: "نبذة عنك:",
                controller: bioController,
                hint: "أدخل نبذة عنك...",
                maxLines: 3,
              ),
              _buildCategoryDropdown(
                label: "الفئة:",
                selectedCategory: selectedCategory,
                onChanged: (value) => setState(() => selectedCategory = value!),
              ),
              _buildDialogTextField(
                label: "الموقع:",
                controller: locationController,
                hint: "أدخل الموقع...",
              ),
              _buildFilePickerButton(
                onPressed: () async {
                  FilePickerResult? result = await FilePicker.platform.pickFiles(
                    type: FileType.custom,
                    allowedExtensions: ['jpg', 'jpeg', 'png', 'pdf', 'doc', 'docx', 'txt'],
                  );
                  if (result != null) {
                    setState(() => selectedFile = File(result.files.single.path!));
                  }
                },
                selectedFileName: selectedFile?.path.split('/').last,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text("إلغاء"),
          ),
          ScaleTransitionButton(
            onPressed: () => _createProfile(
              context,
              nameController.text,
              ageController.text,
              nationalityController.text,
              maritalStatusController.text,
              bioController.text,
              locationController.text,
              selectedCategory,
              selectedFile,
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.teal,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
            ),
            child: const Text("إنشاء", style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
      transitionBuilder: (context, anim1, anim2, child) => FadeTransition(
        opacity: CurvedAnimation(parent: anim1, curve: Curves.easeInOut),
        child: ScaleTransition(
          scale: CurvedAnimation(parent: anim1, curve: Curves.easeInOut),
          child: child,
        ),
      ),
    );
  }

  Widget _buildDialogTextField({
    required String label,
    required TextEditingController controller,
    required String hint,
    int maxLines = 1,
    TextInputType? keyboardType,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.teal[800],
          ),
        ),
        const SizedBox(height: 8),
        TextField(
          controller: controller,
          maxLines: maxLines,
          keyboardType: keyboardType,
          decoration: InputDecoration(
            hintText: hint,
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(10)),
            filled: true,
            fillColor: Colors.grey[100],
            contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
          ),
        ),
        const SizedBox(height: 10),
      ],
    );
  }

  Widget _buildCategoryDropdown({
    required String label,
    required String selectedCategory,
    required Function(String?) onChanged,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.teal[800],
          ),
        ),
        const SizedBox(height: 8),
        DropdownButton<String>(
          value: selectedCategory,
          items: categories
              .map((category) => DropdownMenuItem<String>(
            value: category['title'] as String,
            child: Text(category['title'] as String),
          ))
              .toList(),
          onChanged: onChanged,
          isExpanded: true,
          borderRadius: BorderRadius.circular(10),
          underline: Container(),
        ),
        const SizedBox(height: 10),
      ],
    );
  }

  Widget _buildFilePickerButton({
    required VoidCallback onPressed,
    required String? selectedFileName,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "اختر صورة شخصية - CV (PDF/DOC/TXT):",
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.teal[800],
          ),
        ),
        const SizedBox(height: 8),
        ScaleTransitionButton(
          onPressed: onPressed,
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.teal,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(Icons.upload_file, color: Colors.white),
              const SizedBox(width: 8),
              Text(
                selectedFileName ?? "اختيار ملف",
                style: const TextStyle(color: Colors.white),
              ),
            ],
          ),
        ),
        const SizedBox(height: 10),
      ],
    );
  }

  Future<void> _createProfile(
      BuildContext context,
      String name,
      String age,
      String nationality,
      String maritalStatus,
      String bio,
      String location,
      String category,
      File? file) async {
    if (name.isEmpty ||
        age.isEmpty ||
        nationality.isEmpty ||
        maritalStatus.isEmpty ||
        bio.isEmpty ||
        location.isEmpty ||
        file == null) {
      _showSnackBar("يرجى ملء جميع الحقول المطلوبة", Colors.orange);
      return;
    }
    try {
      await _profileService.createProfile(
          name, age, nationality, maritalStatus, bio, location, category, file, _currentUserId);
      Navigator.pop(context);
      _fetchProfiles();
      _fetchNotifications();
      _showSnackBar("تم إنشاء الملف الشخصي بنجاح!", Colors.green);
    } catch (e) {
      _showSnackBar("خطأ في إنشاء الملف الشخصي: $e", Colors.red);
    }
  }

  void _showProfilePreview(BuildContext context, UserProfile profile) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CachedNetworkImage(
              imageUrl: '${_profileService.baseUrl}${profile.fileUrl}',
              width: 100,
              height: 100,
              fit: BoxFit.cover,
              placeholder: (context, url) => Shimmer.fromColors(
                baseColor: Colors.grey[300]!,
                highlightColor: Colors.grey[100]!,
                child: Container(color: Colors.white),
              ),
              errorWidget: (context, url, error) => const Icon(Icons.error),
            ),
            const SizedBox(height: 8),
            Text(
              profile.name,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            Text('العمر: ${profile.age}'),
            Text('الموقع: ${profile.location}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  void _showProfileDetails(BuildContext context, UserProfile profile) {
    showGeneralDialog(
      context: context,
      barrierDismissible: true,
      barrierLabel: MaterialLocalizations.of(context).modalBarrierDismissLabel,
      barrierColor: Colors.black54,
      transitionDuration: const Duration(milliseconds: 300),
      pageBuilder: (context, anim1, anim2) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        title: Text(profile.name, style: const TextStyle(fontWeight: FontWeight.bold)),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              CachedNetworkImage(
                imageUrl: '${_profileService.baseUrl}${profile.fileUrl}',
                width: double.infinity,
                height: 200,
                fit: BoxFit.cover,
                placeholder: (context, url) => Shimmer.fromColors(
                  baseColor: Colors.grey[300]!,
                  highlightColor: Colors.grey[100]!,
                  child: Container(color: Colors.white),
                ),
                errorWidget: (context, url, error) => const Icon(Icons.error),
              ),
              const SizedBox(height: 16),
              Text('العمر: ${profile.age}'),
              const SizedBox(height: 8),
              Text('الجنسية: ${profile.nationality}'),
              const SizedBox(height: 8),
              Text('الحالة الاجتماعية: ${profile.maritalStatus}'),
              const SizedBox(height: 8),
              Text('نبذة: ${profile.bio}'),
              const SizedBox(height: 8),
              Text('الموقع: ${profile.location}'),
              const SizedBox(height: 8),
              Text(
                  'التاريخ: ${DateFormat('dd/MM/yyyy HH:mm').format(profile.date)}'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
          ScaleTransitionButton(
            onPressed: () => _requestContact(context, profile.id, profile.name),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.teal,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
            ),
            child: const Text('تواصل الآن', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
      transitionBuilder: (context, anim1, anim2, child) => FadeTransition(
        opacity: CurvedAnimation(parent: anim1, curve: Curves.easeInOut),
        child: ScaleTransition(
          scale: CurvedAnimation(parent: anim1, curve: Curves.easeInOut),
          child: child,
        ),
      ),
    );
  }

  Future<void> _requestContact(BuildContext context, String profileId, String name) async {
    try {
      await _profileService.requestContact(profileId, _currentUserId);
      Navigator.pop(context);
      _showSnackBar('تم إرسال طلب التواصل مع $name', Colors.green);
      await _fetchNotifications();
      // Show contact request animation
      showDialog(
        context: context,
        builder: (context) => const HeartPulseAnimation(),
      );
      await Future.delayed(const Duration(seconds: 1));
      Navigator.pop(context);
    } catch (e) {
      _showSnackBar('خطأ في إرسال طلب التواصل: $e', Colors.red);
    }
  }

  void _showMyProfiles(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => SizedBox(
        height: MediaQuery.of(context).size.height * 0.8,
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.all(16),
              child: Text(
                'ملفاتي الشخصية',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.teal[900],
                ),
              ),
            ),
            Expanded(
              child: _userProfiles.where((profile) => profile.creatorId == _currentUserId).isEmpty
                  ? Center(
                child: Text(
                  'لم تقم بإنشاء ملفات شخصية بعد',
                  style: TextStyle(color: Colors.grey[600]),
                ),
              )
                  : ListView.builder(
                itemCount:
                _userProfiles.where((profile) => profile.creatorId == _currentUserId).length,
                itemBuilder: (context, index) {
                  final profile = _userProfiles
                      .where((profile) => profile.creatorId == _currentUserId)
                      .toList()[index];
                  return FadeInAnimation(
                    delay: index * 0.1,
                    child: ListTile(
                      title: Text(profile.name),
                      subtitle: Text('${profile.age} - ${profile.location}'),
                      trailing: IconButton(
                        icon: const Icon(Icons.delete, color: Colors.red),
                        onPressed: () => _deleteProfile(profile.id),
                      ),
                      onTap: () => _showProfileDetails(context, profile),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _deleteProfile(String id) async {
    try {
      await _profileService.deleteProfile(id);
      _fetchProfiles();
      _fetchNotifications();
      Navigator.pop(context);
      _showSnackBar('تم حذف الملف الشخصي', Colors.green);
    } catch (e) {
      _showSnackBar('خطأ في الحذف: $e', Colors.red);
    }
  }

  void _showNotifications(BuildContext context) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.5,
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Text(
              'الإشعارات',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.teal[900],
              ),
            ),
            const SizedBox(height: 10),
            Expanded(
              child: _notifications.isEmpty
                  ? Center(
                child: Text(
                  'لا توجد إشعارات جديدة حالياً',
                  style: TextStyle(color: Colors.grey[600]),
                ),
              )
                  : ListView.builder(
                itemCount: _notifications.length,
                itemBuilder: (context, index) => FadeInAnimation(
                  delay: index * 0.1,
                  child: ListTile(
                    leading: const Icon(Icons.notifications_active, color: Colors.teal),
                    title: Text(_notifications[index]['message']),
                    subtitle: Text(
                      DateFormat('dd/MM/yyyy HH:mm')
                          .format(DateTime.parse(_notifications[index]['date'])),
                    ),
                    trailing: _notifications[index]['read']
                        ? null
                        : const Icon(Icons.circle, color: Colors.teal, size: 10),
                    onTap: () =>
                        setState(() => _notifications[index]['read'] = true),
                  ),
                ),
              ),
            ),
            ScaleTransitionButton(
              onPressed: () {
                setState(() => _notifications.clear());
                Navigator.pop(context);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
              child: const Text('مسح الكل', style: TextStyle(color: Colors.white)),
            ),
          ],
        ),
      ),
    );
  }
}

// Custom Animation Widgets
class FadeInAnimation extends StatefulWidget {
  final Widget child;
  final double delay;

  const FadeInAnimation({super.key, required this.child, this.delay = 0});

  @override
  _FadeInAnimationState createState() => _FadeInAnimationState();
}

class _FadeInAnimationState extends State<FadeInAnimation>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _animation = CurvedAnimation(parent: _controller, curve: Curves.easeIn);
    Future.delayed(Duration(milliseconds: (widget.delay * 1000).toInt()), () {
      if (mounted) _controller.forward();
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return FadeTransition(
      opacity: _animation,
      child: widget.child,
    );
  }
}

class ScaleTransitionButton extends StatefulWidget {
  final VoidCallback? onPressed;
  final Widget child;
  final ButtonStyle? style;

  const ScaleTransitionButton({super.key, 
    required this.onPressed,
    required this.child,
    this.style,
  });

  @override
  _ScaleTransitionButtonState createState() => _ScaleTransitionButtonState();
}

class _ScaleTransitionButtonState extends State<ScaleTransitionButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 100),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.95).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: (_) => _controller.forward(),
      onTapUp: (_) => _controller.reverse(),
      onTapCancel: () => _controller.reverse(),
      child: ScaleTransition(
        scale: _scaleAnimation,
        child: ElevatedButton(
          onPressed: widget.onPressed,
          style: widget.style,
          child: widget.child,
        ),
      ),
    );
  }
}

class HeartPulseAnimation extends StatefulWidget {
  const HeartPulseAnimation({super.key});

  @override
  _HeartPulseAnimationState createState() => _HeartPulseAnimationState();
}

class _HeartPulseAnimationState extends State<HeartPulseAnimation>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _animation = Tween<double>(begin: 0.8, end: 1.2).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );
    _controller.repeat(reverse: true);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: ScaleTransition(
        scale: _animation,
        child: const Icon(
          Icons.favorite,
          color: Colors.red,
          size: 100,
        ),
      ),
    );
  }
}