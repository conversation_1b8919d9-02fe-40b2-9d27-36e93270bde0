import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'appstate.dart';

class DonationAd {
  final String id;
  final String title;
  final String description;
  final double targetAmount;
  final double currentAmount;
  final DateTime endDate;
  final String imageUrl;
  final String category;
  final String createdBy;
  final DateTime createdAt;
  final bool isActive;

  DonationAd({
    required this.id,
    required this.title,
    required this.description,
    required this.targetAmount,
    required this.currentAmount,
    required this.endDate,
    required this.imageUrl,
    required this.category,
    required this.createdBy,
    required this.createdAt,
    this.isActive = true,
  });

  factory DonationAd.fromJson(Map<String, dynamic> json) {
    return DonationAd(
      id: json['_id'] ?? '',
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      targetAmount: (json['targetAmount'] ?? 0).toDouble(),
      currentAmount: (json['currentAmount'] ?? 0).toDouble(),
      endDate: DateTime.tryParse(json['endDate'] ?? '') ?? DateTime.now(),
      imageUrl: json['imageUrl'] ?? '',
      category: json['category'] ?? '',
      createdBy: json['createdBy'] ?? '',
      createdAt: DateTime.tryParse(json['createdAt'] ?? '') ?? DateTime.now(),
      isActive: json['isActive'] ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'title': title,
      'description': description,
      'targetAmount': targetAmount,
      'currentAmount': currentAmount,
      'endDate': endDate.toIso8601String(),
      'imageUrl': imageUrl,
      'category': category,
      'createdBy': createdBy,
      'createdAt': createdAt.toIso8601String(),
      'isActive': isActive,
    };
  }

  double get progressPercentage {
    if (targetAmount <= 0) return 0;
    return (currentAmount / targetAmount).clamp(0.0, 1.0);
  }
}

class DonationAdsScreen extends StatefulWidget {
  const DonationAdsScreen({Key? key}) : super(key: key);

  @override
  State<DonationAdsScreen> createState() => _DonationAdsScreenState();
}

class _DonationAdsScreenState extends State<DonationAdsScreen> {
  final List<DonationAd> _ads = [];
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadAds();
  }

  Future<void> _loadAds() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // TODO: Replace with actual API call
      await Future.delayed(const Duration(seconds: 1));
      
      // Mock data for now
      final mockAds = [
        DonationAd(
          id: '1',
          title: 'مساعدة الأسرة الفقيرة',
          description: 'تبرع لمساعدة أسرة فقيرة على تلبية احتياجاتها الأساسية',
          targetAmount: 5000,
          currentAmount: 2500,
          endDate: DateTime.now().add(const Duration(days: 30)),
          imageUrl: 'https://via.placeholder.com/300',
          category: 'إغاثة',
          createdBy: 'admin',
          createdAt: DateTime.now(),
        ),
      ];

      setState(() {
        _ads.addAll(mockAds);
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('حملات التبرع'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadAds,
          ),
        ],
      ),
      body: _buildBody(),
      floatingActionButton: _buildAddButton(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, color: Colors.red, size: 60),
            const SizedBox(height: 16),
            Text('حدث خطأ: $_error'),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadAds,
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    if (_ads.isEmpty) {
      return const Center(
        child: Text('لا توجد حملات تبرع متاحة حالياً'),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _ads.length,
      itemBuilder: (context, index) {
        final ad = _ads[index];
        return _buildDonationAdCard(ad);
      },
    );
  }

  Widget _buildDonationAdCard(DonationAd ad) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          if (ad.imageUrl.isNotEmpty)
            Image.network(
              ad.imageUrl,
              height: 200,
              fit: BoxFit.cover,
            ),
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  ad.title,
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                const SizedBox(height: 8),
                Text(ad.description),
                const SizedBox(height: 16),
                LinearProgressIndicator(
                  value: ad.progressPercentage,
                  backgroundColor: Colors.grey[200],
                ),
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      '${ad.currentAmount.toStringAsFixed(0)} / ${ad.targetAmount.toStringAsFixed(0)}',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                    Text(
                      '${(ad.progressPercentage * 100).toStringAsFixed(1)}%',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'ينتهي في: ${_formatDate(ad.endDate)}',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                    ElevatedButton(
                      onPressed: () {
                        // TODO: Implement donation
                      },
                      child: const Text('تبرع الآن'),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget? _buildAddButton() {
    final appState = Provider.of<AppState>(context, listen: false);
    
    // Only show add button for admin users
    if (appState.userRole != 'admin') {
      return null;
    }

    return FloatingActionButton(
      onPressed: () {
        // TODO: Implement add new ad
      },
      child: const Icon(Icons.add),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
