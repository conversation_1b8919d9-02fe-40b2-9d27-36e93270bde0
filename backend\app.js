const express = require('express');
const cors = require('cors');
const path = require('path');
const http = require('http');
const socketio = require('socket.io');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const mongoSanitize = require('express-mongo-sanitize');
const xss = require('xss-clean');
const hpp = require('hpp');
const cookieParser = require('cookie-parser');
const compression = require('compression');
const connectDB = require('./src/config/db');
const errorHandler = require('./src/middleware/errorMiddleware');
const { createActivityTracker } = require('./src/middleware/activity.middleware');
const logger = require('./src/utils/logger'); // إضافة مسجل الأحداث

// الاتصال بقاعدة البيانات
connectDB().then(() => {
  logger.info('✅ تم الاتصال بنجاح بقاعدة البيانات');
}).catch(err => {
  logger.error('❌ فشل الاتصال بقاعدة البيانات:', err);
});

// طباعة معلومات البيئة
logger.info(`🔧 بيئة التشغيل: ${process.env.NODE_ENV || 'development'}`);
logger.info(`🔧 المنفذ: ${process.env.PORT || 3000}`);
logger.info(`🔧 المضيف: ${process.env.HOST || '0.0.0.0'}`);


// تهيئة التطبيق
const app = express();

// إنشاء خادم HTTP
const server = http.createServer(app);

// تكوين Socket.io مع إعدادات متقدمة
const io = socketio(server, {
  cors: {
    origin: process.env.ALLOWED_ORIGINS ? process.env.ALLOWED_ORIGINS.split(',') : '*',
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization'],
    credentials: true
  },
  transports: ['websocket', 'polling'],
  pingTimeout: 60000, // 60 ثانية
  pingInterval: 25000, // 25 ثانية
  cookie: {
    name: 'io',
    path: '/',
    httpOnly: true,
    sameSite: 'lax',
    secure: process.env.NODE_ENV === 'production'
  }
});

// معالجة أخطاء اتصال Socket.io
io.on('connection_error', (err) => {
  console.error('❌  خطأ في اتصال Socket.io:', err);
});

// تسجيل الأحداث الواردة من السوكيت
io.on('connection', (socket) => {
  const clientIp = socket.handshake.headers['x-forwarded-for'] || socket.handshake.address;
  console.log(`🔌  عميل متصل: ${socket.id} من ${clientIp}`);
  
  // معالجة الأحداث المخصصة
  socket.on('joinRoom', (room) => {
    socket.join(room);
    console.log(`👥  العميل ${socket.id} انضم إلى الغرفة: ${room}`);
  });
  
  // تنظيف عند انقطاع الاتصال
  socket.on('disconnect', (reason) => {
    console.log(`👋  العميل ${socket.id} انقطع. السبب: ${reason}`);
    // يمكنك إضافة المزيد من المنطق للتعامل مع انقطاع الاتصال
  });
  
  // معالجة الأخطاء
  socket.on('error', (error) => {
    console.error(`❌  خطأ في السوكيت ${socket.id}:`, error);
  });
});

// تهيئة خدمة Socket.io
require('./src/services/socketservice')(io);

// تهيئة نظام المشاركة والاستعارة
const shareBorrowSystem = require('./server')(app, io);

// 1) وسائط الأمان
// تعيين رؤوس HTTP الآمنة مع تكوين إضافي
app.use(
  helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        scriptSrc: ["'self'", "'unsafe-inline'"],
        styleSrc: ["'self'", "'unsafe-inline'"],
        imgSrc: ["'self'", 'data:', 'https:'],
        connectSrc: ["'self'", 'https://api.example.com'],
        fontSrc: ["'self'", 'https: data:'],
        objectSrc: ["'none'"],
        mediaSrc: ["'self'"],
        frameSrc: ["'none'"],
      },
    },
    frameguard: { action: 'deny' },
    hidePoweredBy: true,
    xssFilter: true,
    noSniff: true,
    ieNoOpen: true,
    hsts: {
      maxAge: 63072000, // سنتان بالثواني
      includeSubDomains: true,
      preload: true,
    },
  })
);

// تمكين CORS مع إعدادات متقدمة
const corsOptions = {
  origin: process.env.ALLOWED_ORIGINS ? process.env.ALLOWED_ORIGINS.split(',') : '*',
  methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'Accept'],
  credentials: true,
  optionsSuccessStatus: 200 // بعض المتصفحات القديمة (IE11, various SmartTVs) تتعطل مع 204
};
app.use(cors(corsOptions));

// معالجة طلبات OPTIONS
app.options('*', cors(corsOptions));

// تكوين معدل الطلبات
const apiLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 دقيقة
  max: 100, // حد 100 طلب لكل نافذة
  standardHeaders: true, // إرجاع معلومات معدل الحد في رؤوس الاستجابة
  legacyHeaders: false, // تعطيل رؤوس X-RateLimit-* القديمة
  message: {
    status: 'error',
    message: 'لقد تجاوزت الحد المسموح به للطلبات. يرجى المحاولة مرة أخرى لاحقًا.'
  },
  handler: (req, res) => {
    res.status(429).json({
      status: 'error',
      message: 'لقد تجاوزت الحد المسموح به للطلبات. يرجى المحاولة مرة أخرى بعد 15 دقيقة.'
    });
  }
});

// تطبيق معدل الطلبات على مسارات API فقط
app.use('/api', apiLimiter);

// معدل طلبات أقل تسجيل الدخول والمصادقة
const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 دقيقة
  max: 10, // 10 محاولات فقط
  message: {
    status: 'error',
    message: 'تم تجاوز عدد المحاولات المسموح بها. يرجى المحاولة مرة أخرى لاحقًا.'
  }
});

// تطبيق معدل الطلبات على مسارات المصادقة
app.use(['/api/auth/login', '/api/auth/register', '/api/auth/forgot-password'], authLimiter);

// حماية ضد هجمات NoSQL injection
app.use(mongoSanitize({
  replaceWith: '_',
  onSanitize: ({ req, key }) => {
    console.warn(`[NoSQL Injection] تمت إزالة محتوى غير آمن من الحقل: ${key}`, {
      ip: req.ip,
      url: req.originalUrl,
      method: req.method,
      user: req.user ? req.user.id : 'غير مسجل الدخول'
    });
  }
}));

// حماية ضد XSS (Cross-Site Scripting)
app.use(xss());

// منع تلوث المعلمات مع قائمة بيضاء بالحقول المسموح بها
app.use(hpp({
  whitelist: ['duration', 'ratingsQuantity', 'ratingsAverage', 'maxGroupSize', 'difficulty', 'price']
}));

// ضغط النصوص المرسلة
app.use(compression());

// 2) مسارات API
// استيراد المسارات
const authRoutes = require('./src/routes/authroutes');
const userRoutes = require('./src/routes/userroutes');
const postRoutes = require('./src/routes/postroutes');
const messageRoutes = require('./src/routes/messageroutes');
const groupRoutes = require('./src/routes/grouproutes');
const storyRoutes = require('./src/routes/storyroutes');
const videoRoutes = require('./src/routes/videoroutes');
const medicalRecordRoutes = require('./src/routes/medicalRecordroutes');
const cardRoutes = require('./src/routes/cardsroutes');
const calculusRoutes = require('./src/routes/calculus.routes');
const professionRoutes = require('./src/routes/professions.routes');
const notificationRoutes = require('./src/routes/notification.routes');
const pointsRoutes = require('./src/routes/points.routes');

// مسارات إضافية
const shareBorrowItemRoutes = require('./src/routes/shareBorrowItem.routes');
const sharingGroupRoutes = require('./src/routes/sharingGroup.routes');
const analyticsRoutes = require('./src/routes/analyticsEvent.routes');
const uploadRoutes = require('./src/routes/upload.routes');
const rideRequestRoutes = require('./src/routes/rideRequest.routes');
const driverRoutes = require('./src/routes/driver.routes');

// تسجيل النشاط
const activityTracker = createActivityTracker();

// مسارات API مع تسجيل النشاط
const apiRoutes = [
  // Activity tracking middleware
  activityTracker,
  
  // Route handlers
  authRoutes,
  userRoutes,
  postRoutes,
  messageRoutes,
  groupRoutes,
  storyRoutes,
  videoRoutes,
  medicalRecordRoutes,
  cardRoutes,
  calculusRoutes,
  professionRoutes,
  notificationRoutes,
  pointsRoutes
];

// تطبيق مسارات API
app.use('/api', apiRoutes);

// مسارات إضافية
app.use('/api/share-borrow-items', shareBorrowItemRoutes);
app.use('/api/sharing-groups', sharingGroupRoutes);
app.use('/api/analytics', analyticsRoutes);
app.use('/api/uploads', uploadRoutes);
app.use('/api/rides', rideRequestRoutes);
app.use('/api/drivers', driverRoutes);

// 3) الملفات الثابتة
app.use(express.static(path.join(__dirname, 'public')));

// 4) معالجة طلبات غير موجودة (404)
app.all('*', (req, res, next) => {
  const err = new Error(`مسار ${req.originalUrl} غير موجود على الخادم`);
  err.statusCode = 404;
  err.isOperational = true;
  next(err);
});

// معالجة الأخطاء الشاملة
app.use((err, req, res, next) => {
  // تعيين القيم الافتراضية في حالة عدم وجودها
  err.statusCode = err.statusCode || 500;
  err.status = err.status || 'error';

  // تسجيل الخطأ بالتفصيل في وضع التطوير
  if (process.env.NODE_ENV === 'development') {
    console.error('حدث خطأ:', {
      status: err.status,
      error: err,
      message: err.message,
      stack: err.stack
    });
  }

  // تسجيل أخطاء التشغيل في الإنتاج
  if (process.env.NODE_ENV === 'production') {
    // يمكنك إضافة خدمة تسجيل الأخطاء هنا مثل Sentry أو LogRocket
    console.error(`[${new Date().toISOString()}] ${err.statusCode} - ${err.message} - ${req.originalUrl} - ${req.method} - ${req.ip}`);
  }

  // إرسال استجابة الخطأ المناسبة
  if (req.originalUrl.startsWith('/api')) {
    // استجابة API
    return res.status(err.statusCode).json({
      status: err.status,
      message: err.isOperational ? err.message : 'حدث خطأ ما. يرجى المحاولة مرة أخرى لاحقًا.'
    });
  }

  // استجابة عادية (عرض صفحة خطأ)
  return res.status(err.statusCode).render('error', {
    title: 'حدث خطأ',
    msg: err.message
  });
});

// تصدير التطبيق والخادم
module.exports = { app, server };