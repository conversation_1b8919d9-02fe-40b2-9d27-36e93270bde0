// أداة إرسال إشعار Push عبر FCM
const axios = require('axios');

/**
 * إرسال إشعار FCM لمستخدم
 * @param {string} fcmToken - توكن الجهاز
 * @param {string} title - عنوان الإشعار
 * @param {string} body - نص الإشعار
 * @param {object} data - بيانات إضافية
 */
async function sendFcmNotification(fcmToken, title, body, data = {}) {
  const serverKey = process.env.FCM_SERVER_KEY; // ضع مفتاح FCM في متغير بيئة
  if (!serverKey) throw new Error('FCM_SERVER_KEY is not set');

  const payload = {
    to: fcmToken,
    notification: {
      title,
      body,
    },
    data
  };

  await axios.post('https://fcm.googleapis.com/fcm/send', payload, {
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `key=${serverKey}`
    }
  });
}

module.exports = sendFcmNotification;
